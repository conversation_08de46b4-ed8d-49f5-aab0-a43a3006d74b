import { createApp } from "vue"
import { createPinia } from "pinia"
import App from "./src/App.vue"
import router from "./src/router"
import "./src/style.css"
import { useSmartMenuStore } from "./src/stores/smartMenu"
import { initMockSystem } from "./src/mock"

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

// 初始化Mock系统
initMockSystem()

// 初始化智能菜单 store
const smartMenuStore = useSmartMenuStore()
smartMenuStore.loadFromStorage()

app.mount("#app")
