<template>
  <div class="markdown-editor" :class="{ 'dark': isDarkMode }">
    <!-- 工具栏 -->
    <div class="markdown-toolbar">
      <div class="toolbar-group">
        <!-- 文本格式 -->
        <button
          v-for="tool in textTools"
          :key="tool.name"
          @click="insertSyntax(tool.syntax)"
          :title="tool.title"
          class="toolbar-btn"
        >
          {{ tool.icon }}
        </button>
      </div>
      
      <div class="toolbar-group">
        <!-- 标题 -->
        <button
          v-for="heading in headingTools"
          :key="heading.name"
          @click="insertSyntax(heading.syntax)"
          :title="heading.title"
          class="toolbar-btn"
        >
          {{ heading.icon }}
        </button>
      </div>
      
      <div class="toolbar-group">
        <!-- 列表和引用 -->
        <button
          v-for="tool in listTools"
          :key="tool.name"
          @click="insertSyntax(tool.syntax)"
          :title="tool.title"
          class="toolbar-btn"
        >
          {{ tool.icon }}
        </button>
      </div>
      
      <div class="toolbar-group">
        <!-- 视图模式切换 -->
        <button
          v-for="mode in viewModes"
          :key="mode.value"
          @click="setViewMode(mode.value)"
          :class="['toolbar-btn', { 'active': viewMode === mode.value }]"
          :title="mode.title"
        >
          {{ mode.icon }}
        </button>
      </div>
      
      <div class="toolbar-group toolbar-group-right">
        <!-- 统计信息 -->
        <div class="stats-info">
          <span class="stat-item">{{ stats.words }} 词</span>
          <span class="stat-item">{{ stats.characters }} 字符</span>
        </div>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="markdown-body" :class="`view-mode-${viewMode}`">
      <!-- 编辑区域 -->
      <div v-show="viewMode !== 'preview'" class="editor-pane">
        <textarea
          ref="textareaRef"
          v-model="content"
          @input="handleInput"
          @scroll="handleEditorScroll"
          @keydown="handleKeydown"
          class="markdown-textarea"
          :placeholder="placeholder"
          spellcheck="false"
        ></textarea>
        
        <!-- 行号 -->
        <div v-if="showLineNumbers" class="line-numbers">
          <div
            v-for="n in lineCount"
            :key="n"
            class="line-number"
          >
            {{ n }}
          </div>
        </div>
      </div>

      <!-- 预览区域 -->
      <div v-show="viewMode !== 'edit'" class="preview-pane">
        <div
          ref="previewRef"
          class="markdown-preview"
          v-html="renderedHtml"
          @scroll="handlePreviewScroll"
        ></div>
      </div>
    </div>

    <!-- 目录侧边栏 -->
    <div v-if="showToc && toc.length > 0" class="toc-sidebar">
      <div class="toc-header">
        <h4>目录</h4>
        <button @click="showToc = false" class="toc-close">×</button>
      </div>
      <div class="toc-content">
        <div
          v-for="item in toc"
          :key="item.id"
          :class="['toc-item', `toc-level-${item.level}`]"
          @click="scrollToHeading(item.id)"
        >
          {{ item.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { markdownRenderer, debounce, insertMarkdownSyntax } from '@/utils/markdown'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入Markdown内容...'
  },
  height: {
    type: String,
    default: '400px'
  },
  showLineNumbers: {
    type: Boolean,
    default: true
  },
  autoSave: {
    type: Boolean,
    default: false
  },
  autoSaveDelay: {
    type: Number,
    default: 2000
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'change'])

// 主题
const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)

// 编辑器状态
const textareaRef = ref(null)
const previewRef = ref(null)
const content = ref(props.modelValue)
const viewMode = ref('split') // edit, preview, split
const showToc = ref(false)
const isScrollSyncing = ref(false)

// 工具栏配置
const textTools = [
  { name: 'bold', icon: '𝐁', title: '粗体 (Ctrl+B)', syntax: 'bold' },
  { name: 'italic', icon: '𝐼', title: '斜体 (Ctrl+I)', syntax: 'italic' },
  { name: 'code', icon: '</>', title: '行内代码 (Ctrl+`)', syntax: 'code' },
  { name: 'link', icon: '🔗', title: '链接 (Ctrl+K)', syntax: 'link' },
  { name: 'image', icon: '🖼️', title: '图片 (Ctrl+Shift+I)', syntax: 'image' }
]

const headingTools = [
  { name: 'h1', icon: 'H1', title: '一级标题', syntax: 'heading1' },
  { name: 'h2', icon: 'H2', title: '二级标题', syntax: 'heading2' },
  { name: 'h3', icon: 'H3', title: '三级标题', syntax: 'heading3' }
]

const listTools = [
  { name: 'quote', icon: '❝', title: '引用', syntax: 'quote' },
  { name: 'list', icon: '•', title: '无序列表', syntax: 'list' },
  { name: 'orderedList', icon: '1.', title: '有序列表', syntax: 'orderedList' },
  { name: 'codeBlock', icon: '{ }', title: '代码块', syntax: 'codeBlock' }
]

const viewModes = [
  { value: 'edit', icon: '✏️', title: '编辑模式' },
  { value: 'split', icon: '⚌', title: '分屏模式' },
  { value: 'preview', icon: '👁️', title: '预览模式' }
]

// 计算属性
const lineCount = computed(() => {
  return content.value.split('\n').length
})

const renderedHtml = computed(() => {
  return markdownRenderer.render(content.value)
})

const stats = computed(() => {
  return markdownRenderer.getStats(content.value)
})

const toc = computed(() => {
  return markdownRenderer.extractToc(content.value)
})

// 防抖渲染
const debouncedRender = debounce(() => {
  emit('change', content.value)
  if (props.autoSave) {
    emit('save', content.value)
  }
}, 300)

// 自动保存
const debouncedAutoSave = debounce(() => {
  if (props.autoSave) {
    emit('save', content.value)
  }
}, props.autoSaveDelay)

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    content.value = newValue
  }
})

watch(content, (newValue) => {
  emit('update:modelValue', newValue)
  debouncedRender()
  debouncedAutoSave()
})

// 方法
const handleInput = (event) => {
  content.value = event.target.value
}

const insertSyntax = (syntax) => {
  if (textareaRef.value) {
    insertMarkdownSyntax(textareaRef.value, syntax)
    content.value = textareaRef.value.value
  }
}

const setViewMode = (mode) => {
  viewMode.value = mode
  nextTick(() => {
    if (mode === 'edit' && textareaRef.value) {
      textareaRef.value.focus()
    }
  })
}

const handleKeydown = (event) => {
  // 快捷键处理
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        insertSyntax('bold')
        break
      case 'i':
        event.preventDefault()
        insertSyntax('italic')
        break
      case 'k':
        event.preventDefault()
        insertSyntax('link')
        break
      case '`':
        event.preventDefault()
        insertSyntax('code')
        break
    }
  }
  
  if (event.ctrlKey && event.shiftKey && event.key === 'I') {
    event.preventDefault()
    insertSyntax('image')
  }
}

const handleEditorScroll = () => {
  if (isScrollSyncing.value || viewMode.value !== 'split') return
  
  isScrollSyncing.value = true
  const editor = textareaRef.value
  const preview = previewRef.value
  
  if (editor && preview) {
    const scrollRatio = editor.scrollTop / (editor.scrollHeight - editor.clientHeight)
    preview.scrollTop = scrollRatio * (preview.scrollHeight - preview.clientHeight)
  }
  
  setTimeout(() => {
    isScrollSyncing.value = false
  }, 100)
}

const handlePreviewScroll = () => {
  if (isScrollSyncing.value || viewMode.value !== 'split') return
  
  isScrollSyncing.value = true
  const editor = textareaRef.value
  const preview = previewRef.value
  
  if (editor && preview) {
    const scrollRatio = preview.scrollTop / (preview.scrollHeight - preview.clientHeight)
    editor.scrollTop = scrollRatio * (editor.scrollHeight - editor.clientHeight)
  }
  
  setTimeout(() => {
    isScrollSyncing.value = false
  }, 100)
}

const scrollToHeading = (id) => {
  const element = previewRef.value?.querySelector(`#${id}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

onMounted(() => {
  // 设置编辑器高度
  if (textareaRef.value) {
    textareaRef.value.style.height = props.height
  }
})

// 暴露方法给父组件
defineExpose({
  insertSyntax,
  setViewMode,
  focus: () => textareaRef.value?.focus(),
  getContent: () => content.value,
  setContent: (newContent) => { content.value = newContent }
})
</script>

<style scoped>
.markdown-editor {
  @apply border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800;
  position: relative;
}

/* 工具栏样式 */
.markdown-toolbar {
  @apply flex items-center gap-2 p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
  flex-wrap: wrap;
}

.toolbar-group {
  @apply flex items-center gap-1;
}

.toolbar-group-right {
  margin-left: auto;
}

.toolbar-btn {
  @apply px-2 py-1 text-sm rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors;
  @apply text-gray-700 dark:text-gray-300 border border-transparent;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toolbar-btn:hover {
  @apply border-gray-300 dark:border-gray-600;
}

.toolbar-btn.active {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-blue-300 dark:border-blue-600;
}

.stats-info {
  @apply flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400;
}

.stat-item {
  @apply px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded;
}

/* 编辑器主体 */
.markdown-body {
  @apply relative;
  height: 400px;
  display: flex;
}

.view-mode-edit .editor-pane {
  width: 100%;
}

.view-mode-preview .preview-pane {
  width: 100%;
}

.view-mode-split .editor-pane,
.view-mode-split .preview-pane {
  width: 50%;
}

/* 编辑区域 */
.editor-pane {
  @apply relative border-r border-gray-200 dark:border-gray-700;
  position: relative;
}

.markdown-textarea {
  @apply w-full h-full p-4 resize-none outline-none bg-transparent;
  @apply text-gray-900 dark:text-gray-100 font-mono text-sm leading-relaxed;
  border: none;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.markdown-textarea::placeholder {
  @apply text-gray-400 dark:text-gray-500;
}

.line-numbers {
  @apply absolute left-0 top-0 w-12 h-full bg-gray-50 dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700;
  @apply text-xs text-gray-400 dark:text-gray-500 text-right;
  padding: 16px 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.5;
  user-select: none;
  pointer-events: none;
}

.line-number {
  height: 21px;
  line-height: 21px;
}

.editor-pane:has(.line-numbers) .markdown-textarea {
  padding-left: 60px;
}

/* 预览区域 */
.preview-pane {
  @apply overflow-auto;
}

.markdown-preview {
  @apply p-4 h-full overflow-auto;
  @apply text-gray-900 dark:text-gray-100;
}

/* Markdown内容样式 */
.markdown-preview :deep(h1),
.markdown-preview :deep(h2),
.markdown-preview :deep(h3),
.markdown-preview :deep(h4),
.markdown-preview :deep(h5),
.markdown-preview :deep(h6),
.markdown-preview :deep(.markdown-heading),
.markdown-preview :deep(.markdown-h1),
.markdown-preview :deep(.markdown-h2),
.markdown-preview :deep(.markdown-h3),
.markdown-preview :deep(.markdown-h4),
.markdown-preview :deep(.markdown-h5),
.markdown-preview :deep(.markdown-h6) {
  @apply font-semibold mb-4 mt-6 text-gray-900 dark:text-gray-100;
  position: relative;
  line-height: 1.3;
}

.markdown-preview :deep(h1),
.markdown-preview :deep(.markdown-h1) {
  @apply text-2xl;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.markdown-preview :deep(h2),
.markdown-preview :deep(.markdown-h2) {
  @apply text-xl;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.25rem;
}

.markdown-preview :deep(h3),
.markdown-preview :deep(.markdown-h3) { @apply text-lg; }

.markdown-preview :deep(h4),
.markdown-preview :deep(.markdown-h4) { @apply text-base; }

.markdown-preview :deep(h5),
.markdown-preview :deep(.markdown-h5) { @apply text-sm; }

.markdown-preview :deep(h6),
.markdown-preview :deep(.markdown-h6) { @apply text-xs; }

/* 暗色主题下的标题边框 */
.dark .markdown-preview :deep(h1),
.dark .markdown-preview :deep(.markdown-h1) {
  border-bottom-color: #374151;
}

.dark .markdown-preview :deep(h2),
.dark .markdown-preview :deep(.markdown-h2) {
  border-bottom-color: #374151;
}

.markdown-preview :deep(.markdown-anchor) {
  @apply absolute -left-6 top-0 text-gray-400 dark:text-gray-500 opacity-0 transition-opacity;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: normal;
}

.markdown-preview :deep(h1:hover .markdown-anchor),
.markdown-preview :deep(h2:hover .markdown-anchor),
.markdown-preview :deep(h3:hover .markdown-anchor),
.markdown-preview :deep(h4:hover .markdown-anchor),
.markdown-preview :deep(h5:hover .markdown-anchor),
.markdown-preview :deep(h6:hover .markdown-anchor),
.markdown-preview :deep(.markdown-heading:hover .markdown-anchor) {
  opacity: 1;
}

/* 确保标题内容正确显示 */
.markdown-preview :deep(.markdown-heading) {
  display: block;
  width: 100%;
}

.markdown-preview :deep(.markdown-heading .markdown-anchor) {
  display: inline-block;
  margin-right: 0.5rem;
}

.markdown-preview :deep(p) {
  @apply mb-4 leading-relaxed;
}

.markdown-preview :deep(blockquote) {
  @apply border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic text-gray-600 dark:text-gray-400 mb-4;
}

.markdown-preview :deep(ul),
.markdown-preview :deep(ol) {
  @apply mb-4 pl-6;
}

.markdown-preview :deep(li) {
  @apply mb-1;
}

.markdown-preview :deep(code) {
  @apply bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm font-mono;
  @apply text-red-600 dark:text-red-400;
}

.markdown-preview :deep(.markdown-code-block) {
  @apply mb-4 rounded-lg overflow-hidden;
}

.markdown-preview :deep(.markdown-code-block code) {
  @apply block p-4 bg-gray-100 dark:bg-gray-900 text-sm font-mono overflow-x-auto;
  @apply text-gray-900 dark:text-gray-100;
}

.markdown-preview :deep(.markdown-table-wrapper) {
  @apply mb-4 overflow-x-auto;
}

.markdown-preview :deep(.markdown-table) {
  @apply w-full border-collapse border border-gray-200 dark:border-gray-700;
}

.markdown-preview :deep(.markdown-table th),
.markdown-preview :deep(.markdown-table td) {
  @apply border border-gray-200 dark:border-gray-700 px-3 py-2 text-left;
}

.markdown-preview :deep(.markdown-table th) {
  @apply bg-gray-50 dark:bg-gray-800 font-semibold;
}

.markdown-preview :deep(.markdown-link) {
  @apply text-blue-600 dark:text-blue-400 hover:underline;
}

.markdown-preview :deep(img) {
  @apply max-w-full h-auto rounded-lg mb-4;
}

.markdown-preview :deep(hr) {
  @apply border-gray-200 dark:border-gray-700 my-6;
}

/* 目录侧边栏 */
.toc-sidebar {
  @apply absolute right-0 top-0 w-64 h-full bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700;
  @apply shadow-lg z-10;
}

.toc-header {
  @apply flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700;
}

.toc-header h4 {
  @apply font-semibold text-gray-900 dark:text-gray-100;
}

.toc-close {
  @apply text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300;
  @apply w-6 h-6 flex items-center justify-center rounded;
}

.toc-content {
  @apply p-3 overflow-auto;
  max-height: calc(100% - 60px);
}

.toc-item {
  @apply py-1 px-2 text-sm cursor-pointer rounded hover:bg-gray-100 dark:hover:bg-gray-700;
  @apply text-gray-700 dark:text-gray-300;
}

.toc-level-1 { @apply font-semibold; }
.toc-level-2 { @apply pl-4; }
.toc-level-3 { @apply pl-8; }
.toc-level-4 { @apply pl-12; }
.toc-level-5 { @apply pl-16; }
.toc-level-6 { @apply pl-20; }

/* 错误样式 */
.markdown-preview :deep(.markdown-error) {
  @apply bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 p-4 rounded-lg;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-toolbar {
    @apply flex-col items-stretch gap-2;
  }

  .toolbar-group {
    @apply justify-center;
  }

  .view-mode-split .editor-pane,
  .view-mode-split .preview-pane {
    width: 100%;
  }

  .view-mode-split .markdown-body {
    flex-direction: column;
  }

  .view-mode-split .editor-pane {
    height: 50%;
    border-right: none;
    border-bottom: 1px solid theme('colors.gray.200');
  }

  .view-mode-split .preview-pane {
    height: 50%;
  }

  .dark .view-mode-split .editor-pane {
    border-bottom-color: theme('colors.gray.700');
  }

  .toc-sidebar {
    width: 100%;
  }
}

/* 滚动条样式 */
.markdown-textarea::-webkit-scrollbar,
.markdown-preview::-webkit-scrollbar,
.toc-content::-webkit-scrollbar {
  width: 6px;
}

.markdown-textarea::-webkit-scrollbar-track,
.markdown-preview::-webkit-scrollbar-track,
.toc-content::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.markdown-textarea::-webkit-scrollbar-thumb,
.markdown-preview::-webkit-scrollbar-thumb,
.toc-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.markdown-textarea::-webkit-scrollbar-thumb:hover,
.markdown-preview::-webkit-scrollbar-thumb:hover,
.toc-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}
</style>
