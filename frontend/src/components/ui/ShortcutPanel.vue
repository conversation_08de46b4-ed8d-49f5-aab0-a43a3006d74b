<template>
  <Teleport to="body">
    <Transition name="shortcut-panel">
      <div v-if="isOpen" class="shortcut-panel-overlay" @click="close">
        <div class="shortcut-panel" @click.stop>
          <div class="panel-header">
            <h2 class="panel-title">⌨️ 键盘快捷键</h2>
            <button @click="close" class="close-button">
              <MenuIcons name="close" :size="20" />
            </button>
          </div>
          
          <div class="panel-content">
            <div class="shortcut-search">
              <div class="search-wrapper">
                <MenuIcons name="search" :size="16" />
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索快捷键..."
                  class="search-input"
                />
              </div>
            </div>
            
            <div class="shortcut-categories">
              <div
                v-for="category in filteredCategories"
                :key="category.name"
                class="category-section"
              >
                <h3 class="category-title">{{ category.name }}</h3>
                <div class="shortcuts-grid">
                  <div
                    v-for="shortcut in category.shortcuts"
                    :key="shortcut.key"
                    class="shortcut-item"
                    :class="{ 'highlighted': isHighlighted(shortcut) }"
                  >
                    <div class="shortcut-keys">
                      <kbd
                        v-for="key in parseShortcutKey(shortcut.key)"
                        :key="key"
                        class="key"
                      >
                        {{ key }}
                      </kbd>
                    </div>
                    <div class="shortcut-description">
                      {{ shortcut.description }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 空状态 -->
            <div v-if="filteredCategories.length === 0" class="empty-state">
              <div class="empty-icon">🔍</div>
              <div class="empty-title">未找到匹配的快捷键</div>
              <div class="empty-subtitle">尝试使用其他关键词搜索</div>
            </div>
          </div>
          
          <div class="panel-footer">
            <div class="footer-tip">
              💡 提示：按 <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>H</kbd> 可快速打开此面板
            </div>
            <div class="footer-actions">
              <button @click="printShortcuts" class="action-button secondary">
                打印快捷键
              </button>
              <button @click="exportShortcuts" class="action-button primary">
                导出列表
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { shortcutCategories } from '@/composables/useKeyboardShortcuts'
import MenuIcons from './icons/MenuIcons.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close'])

const searchQuery = ref('')
const highlightedShortcut = ref(null)

// 过滤快捷键
const filteredCategories = computed(() => {
  if (!searchQuery.value) return shortcutCategories
  
  const query = searchQuery.value.toLowerCase()
  
  return shortcutCategories
    .map(category => ({
      ...category,
      shortcuts: category.shortcuts.filter(shortcut =>
        shortcut.key.toLowerCase().includes(query) ||
        shortcut.description.toLowerCase().includes(query)
      )
    }))
    .filter(category => category.shortcuts.length > 0)
})

// 解析快捷键字符串
const parseShortcutKey = (keyString) => {
  return keyString.split(' + ').map(key => {
    // 替换常见的键名
    const keyMap = {
      'Ctrl': '⌘',
      'Alt': '⌥',
      'Shift': '⇧',
      'Enter': '↵',
      'Escape': 'Esc',
      'Space': '␣'
    }
    return keyMap[key] || key
  })
}

// 检查是否高亮
const isHighlighted = (shortcut) => {
  return highlightedShortcut.value === shortcut.key
}

// 关闭面板
const close = () => {
  emit('close')
}

// 高亮快捷键
const highlightShortcut = (key) => {
  highlightedShortcut.value = key
  setTimeout(() => {
    highlightedShortcut.value = null
  }, 1000)
}

// 打印快捷键
const printShortcuts = () => {
  const printContent = generatePrintContent()
  const printWindow = window.open('', '_blank')
  printWindow.document.write(printContent)
  printWindow.document.close()
  printWindow.print()
}

// 导出快捷键
const exportShortcuts = () => {
  const shortcuts = shortcutCategories.flatMap(category =>
    category.shortcuts.map(shortcut => ({
      category: category.name,
      key: shortcut.key,
      description: shortcut.description
    }))
  )
  
  const csvContent = [
    ['分类', '快捷键', '描述'],
    ...shortcuts.map(s => [s.category, s.key, s.description])
  ].map(row => row.join(',')).join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = 'keyboard-shortcuts.csv'
  link.click()
}

// 生成打印内容
const generatePrintContent = () => {
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>键盘快捷键</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; border-bottom: 2px solid #007acc; }
        h2 { color: #666; margin-top: 30px; }
        .shortcut { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }
        .key { background: #f5f5f5; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
      </style>
    </head>
    <body>
      <h1>键盘快捷键参考</h1>
      ${shortcutCategories.map(category => `
        <h2>${category.name}</h2>
        ${category.shortcuts.map(shortcut => `
          <div class="shortcut">
            <span class="key">${shortcut.key}</span>
            <span>${shortcut.description}</span>
          </div>
        `).join('')}
      `).join('')}
    </body>
    </html>
  `
}

// 键盘事件处理
const handleKeyDown = (event) => {
  if (event.key === 'Escape') {
    close()
  }
}

// 监听搜索变化
watch(searchQuery, (newQuery) => {
  if (newQuery && filteredCategories.value.length > 0) {
    const firstShortcut = filteredCategories.value[0].shortcuts[0]
    if (firstShortcut) {
      highlightShortcut(firstShortcut.key)
    }
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.shortcut-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.shortcut-panel {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  width: 90vw;
  max-width: 800px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dark .shortcut-panel {
  background: rgba(15, 23, 42, 0.98);
  border-color: rgba(51, 65, 85, 0.8);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.dark .panel-header {
  border-bottom-color: rgba(51, 65, 85, 0.6);
}

.panel-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin: 0;
}

.dark .panel-title {
  color: var(--color-neutral-100);
}

.close-button {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  background: var(--color-neutral-100);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  color: var(--color-neutral-600);
}

.close-button:hover {
  background: var(--color-neutral-200);
  transform: scale(1.05);
}

.dark .close-button {
  background: var(--color-neutral-800);
  color: var(--color-neutral-400);
}

.dark .close-button:hover {
  background: var(--color-neutral-700);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem 2rem;
}

.shortcut-search {
  margin-bottom: 2rem;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  padding: 0.75rem 1rem;
  gap: 0.75rem;
}

.dark .search-wrapper {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  font-size: var(--font-size-sm);
  color: var(--color-neutral-700);
}

.dark .search-input {
  color: var(--color-neutral-200);
}

.shortcut-categories {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.category-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.category-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin: 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--color-primary-200);
}

.dark .category-title {
  color: var(--color-neutral-100);
  border-bottom-color: var(--color-primary-700);
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 0.75rem;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  transition: all var(--duration-200) var(--ease-out);
}

.shortcut-item:hover {
  background: var(--color-neutral-100);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.shortcut-item.highlighted {
  background: rgba(14, 165, 233, 0.1);
  border-color: var(--color-primary-300);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.dark .shortcut-item {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.dark .shortcut-item:hover {
  background: var(--color-neutral-700);
}

.dark .shortcut-item.highlighted {
  background: rgba(14, 165, 233, 0.2);
  border-color: var(--color-primary-500);
}

.shortcut-keys {
  display: flex;
  gap: 0.25rem;
  align-items: center;
}

.key {
  background: var(--color-neutral-200);
  color: var(--color-neutral-700);
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 600;
  border: 1px solid var(--color-neutral-300);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 1.5rem;
  text-align: center;
}

.dark .key {
  background: var(--color-neutral-700);
  color: var(--color-neutral-200);
  border-color: var(--color-neutral-600);
}

.shortcut-description {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
  font-weight: 500;
}

.dark .shortcut-description {
  color: var(--color-neutral-400);
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-neutral-700);
  margin-bottom: 0.5rem;
}

.dark .empty-title {
  color: var(--color-neutral-300);
}

.empty-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-500);
}

.dark .empty-subtitle {
  color: var(--color-neutral-400);
}

.panel-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  background: rgba(248, 250, 252, 0.8);
}

.dark .panel-footer {
  border-top-color: rgba(51, 65, 85, 0.6);
  background: rgba(2, 6, 23, 0.8);
}

.footer-tip {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.dark .footer-tip {
  color: var(--color-neutral-400);
}

.footer-tip kbd {
  background: var(--color-neutral-200);
  color: var(--color-neutral-700);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-family: inherit;
  border: 1px solid var(--color-neutral-300);
}

.dark .footer-tip kbd {
  background: var(--color-neutral-700);
  color: var(--color-neutral-200);
  border-color: var(--color-neutral-600);
}

.footer-actions {
  display: flex;
  gap: 1rem;
}

.action-button {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.action-button.primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500));
  color: white;
}

.action-button.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.25);
}

.action-button.secondary {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
  border: 1px solid var(--color-neutral-300);
}

.action-button.secondary:hover {
  background: var(--color-neutral-200);
  transform: translateY(-1px);
}

.dark .action-button.secondary {
  background: var(--color-neutral-800);
  color: var(--color-neutral-200);
  border-color: var(--color-neutral-600);
}

.dark .action-button.secondary:hover {
  background: var(--color-neutral-700);
}

/* 动画 */
.shortcut-panel-enter-active,
.shortcut-panel-leave-active {
  transition: all var(--duration-300) var(--ease-out);
}

.shortcut-panel-enter-from {
  opacity: 0;
  transform: scale(0.95);
}

.shortcut-panel-leave-to {
  opacity: 0;
  transform: scale(0.95);
}
</style>
