<template>
  <Teleport to="body">
    <div class="toast-container">
      <TransitionGroup name="toast" tag="div" class="toast-list">
        <div
          v-for="toast in toasts"
          :key="toast.id"
          :class="[
            'toast',
            `toast--${toast.type}`,
            { 'dark': isDarkMode }
          ]"
          @click="removeToast(toast.id)"
        >
          <div class="toast__icon">
            <span v-if="toast.type === 'success'">✅</span>
            <span v-else-if="toast.type === 'error'">❌</span>
            <span v-else-if="toast.type === 'warning'">⚠️</span>
            <span v-else-if="toast.type === 'info'">ℹ️</span>
          </div>
          
          <div class="toast__content">
            <div v-if="toast.title" class="toast__title">{{ toast.title }}</div>
            <div class="toast__message">{{ toast.message }}</div>
          </div>
          
          <button
            class="toast__close"
            @click.stop="removeToast(toast.id)"
            :aria-label="'关闭通知'"
          >
            ×
          </button>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup>
import { computed } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useToastStore } from '@/stores/toast'

// 主题
const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)

// Toast状态
const toastStore = useToastStore()
const toasts = computed(() => toastStore.toasts)

// 移除Toast
const removeToast = (id) => {
  toastStore.removeToast(id)
}
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  pointer-events: none;
}

.toast-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
}

.toast {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg;
  @apply flex items-start gap-3 p-4 min-w-[300px] max-w-[400px];
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toast:hover {
  @apply shadow-xl transform scale-105;
}

.toast--success {
  @apply border-l-4 border-l-green-500;
}

.toast--error {
  @apply border-l-4 border-l-red-500;
}

.toast--warning {
  @apply border-l-4 border-l-yellow-500;
}

.toast--info {
  @apply border-l-4 border-l-blue-500;
}

.toast__icon {
  @apply flex-shrink-0 text-lg;
}

.toast__content {
  @apply flex-1 min-w-0;
}

.toast__title {
  @apply font-semibold text-gray-900 dark:text-white text-sm mb-1;
}

.toast__message {
  @apply text-gray-700 dark:text-gray-300 text-sm leading-relaxed;
  word-wrap: break-word;
}

.toast__close {
  @apply flex-shrink-0 w-6 h-6 flex items-center justify-center;
  @apply text-gray-400 hover:text-gray-600 dark:hover:text-gray-200;
  @apply rounded-full hover:bg-gray-100 dark:hover:bg-gray-700;
  @apply transition-colors duration-200;
  font-size: 18px;
  line-height: 1;
}

/* Toast动画 */
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.3s ease-in;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.8);
}

.toast-move {
  transition: transform 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .toast-list {
    max-width: none;
  }
  
  .toast {
    min-width: auto;
    max-width: none;
  }
}
</style>
