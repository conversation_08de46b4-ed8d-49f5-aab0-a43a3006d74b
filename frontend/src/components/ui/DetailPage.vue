<template>
  <div class="detail-page" :class="{ 'dark': isDarkMode, 'fullscreen': isFullscreen, 'focus-mode': isFocusMode }">
    <!-- 页面头部 -->
    <div v-if="!isFocusMode" class="page-header">
      <PageHeader :title="title" :description="description">
        <template #actions>
          <div class="header-actions">
            <!-- 模式切换 -->
            <ActionButton
              v-if="showModeToggle"
              variant="secondary"
              size="sm"
              :icon="isFocusMode ? '👁️' : '🎯'"
              @click="toggleFocusMode"
              :title="isFocusMode ? '退出专注模式' : '专注模式'"
            >
              {{ isFocusMode ? '退出专注' : '专注模式' }}
            </ActionButton>
            
            <!-- 全屏切换 -->
            <ActionButton
              v-if="showFullscreen"
              variant="secondary"
              size="sm"
              :icon="isFullscreen ? '⊡' : '⊞'"
              @click="toggleFullscreen"
              :title="isFullscreen ? '退出全屏' : '全屏'"
            >
              {{ isFullscreen ? '退出全屏' : '全屏' }}
            </ActionButton>
            
            <!-- 移除自动保存状态显示 -->
            
            <!-- 自定义操作 -->
            <slot name="actions" :save="handleSave" :cancel="handleCancel" :data="formData" />
            
            <!-- 默认操作按钮 -->
            <template v-if="!$slots.actions">
              <ActionButton
                variant="secondary"
                @click="handleCancel"
                :disabled="loading"
              >
                取消
              </ActionButton>
              
              <ActionButton
                v-if="mode !== 'view'"
                variant="primary"
                @click="handleSave"
                :disabled="loading || !isFormValid"
                :icon="loading ? '⟳' : '💾'"
              >
                {{ loading ? '保存中...' : '保存' }}
              </ActionButton>
            </template>
          </div>
        </template>
      </PageHeader>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <!-- 侧边栏 -->
      <div v-if="showSidebar && !isFocusMode" class="content-sidebar">
        <!-- 目录导航 -->
        <div v-if="toc.length > 0" class="sidebar-section">
          <h4 class="sidebar-title">目录</h4>
          <div class="toc-list">
            <div
              v-for="item in toc"
              :key="item.id"
              :class="['toc-item', `toc-level-${item.level}`]"
              @click="scrollToHeading(item.id)"
            >
              {{ item.text }}
            </div>
          </div>
        </div>
        
        <!-- 自定义侧边栏内容 -->
        <slot name="sidebar" :data="formData" :toc="toc" />
      </div>

      <!-- 主内容 -->
      <div class="main-content">
        <!-- 标签页导航 -->
        <TabNavigation
          v-if="tabs.length > 1"
          v-model="activeTab"
          :tabs="tabs"
          variant="underline"
          :show-actions="false"
          @tab-change="handleTabChange"
        >
          <!-- 基本信息标签页 -->
          <template #basic>
            <div class="tab-content-wrapper">
              <FormValidator
                v-if="validationRules"
                :rules="validationRules"
                :model-value="formData"
                @validate="handleValidation"
                v-slot="{ validate, errors, isValid }"
              >
                <slot
                  name="basic-content"
                  :data="formData"
                  :errors="errors"
                  :isValid="isValid"
                  :validate="validate"
                  :mode="mode"
                />
              </FormValidator>
              
              <slot v-else name="basic-content" :data="formData" :mode="mode" />
            </div>
          </template>

          <!-- 详细内容标签页 -->
          <template #content>
            <div class="tab-content-wrapper" :class="{ 'editor-container': hasMarkdownContent }">
              <!-- 统一使用MarkdownEditorFinal -->
              <MarkdownEditor
                v-if="hasMarkdownContent"
                v-model="formData[markdownField]"
                :height="editorHeight"
                :show-line-numbers="showLineNumbers"
                :auto-save="false"
                :readonly="mode === 'view'"
                :mode="'sv'"
                placeholder="请输入Markdown内容..."
                @save="handleMarkdownSave"
                @change="handleMarkdownChange"
              />

              <slot v-else name="detail-content" :data="formData" :mode="mode" />
            </div>
          </template>

          <!-- 历史版本标签页 -->
          <template #history>
            <div class="tab-content-wrapper">
              <slot name="history-content" :data="formData" :mode="mode" />
            </div>
          </template>

          <!-- 自定义标签页 -->
          <template v-for="tab in customTabs" :key="tab.key" #[tab.key]>
            <div class="tab-content-wrapper">
              <component
                v-if="tab.component"
                :is="tab.component"
                v-model="formData"
                :mode="mode"
                v-bind="tab.props || {}"
              />
              
              <slot v-else :name="`${tab.key}-content`" :data="formData" :mode="mode" />
            </div>
          </template>
        </TabNavigation>

        <!-- 单一内容区域（无标签页） -->
        <div v-else class="single-content">
          <MarkdownEditor
            v-if="hasMarkdownContent"
            v-model="formData[markdownField]"
            :height="editorHeight"
            :show-line-numbers="showLineNumbers"
            :auto-save="enableAutoSave"
            @save="handleMarkdownSave"
            @change="handleMarkdownChange"
          />
          
          <slot v-else name="content" :data="formData" :mode="mode" />
        </div>
      </div>
    </div>

    <!-- 专注模式退出按钮 -->
    <div v-if="isFocusMode" class="focus-mode-controls">
      <button @click="toggleFocusMode" class="focus-exit-btn" title="退出专注模式 (ESC)">
        ×
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { markdownRenderer } from '@/utils/markdown'
import { useDebugHelper } from '@/utils/debugHelper'
import PageHeader from '@/components/PageHeader.vue'
import ActionButton from '@/components/ActionButton.vue'
import TabNavigation from './TabNavigation.vue'
import MarkdownEditor from './MarkdownEditorFinal.vue'
import FormValidator from './FormValidator.vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  mode: {
    type: String,
    default: 'edit',
    validator: (value) => ['view', 'edit', 'create'].includes(value)
  },
  data: {
    type: Object,
    default: () => ({})
  },
  tabs: {
    type: Array,
    default: () => [
      { key: 'basic', label: '基本信息', icon: '📋' },
      { key: 'content', label: '详细内容', icon: '📝' },
      { key: 'history', label: '历史版本', icon: '📚' }
    ]
  },
  validationRules: {
    type: Object,
    default: null
  },
  showSidebar: {
    type: Boolean,
    default: true
  },
  showModeToggle: {
    type: Boolean,
    default: true
  },
  showFullscreen: {
    type: Boolean,
    default: true
  },
  hasMarkdownContent: {
    type: Boolean,
    default: true
  },
  markdownField: {
    type: String,
    default: 'content'
  },
  editorHeight: {
    type: String,
    default: '600px'
  },
  showLineNumbers: {
    type: Boolean,
    default: true
  },
  enableAutoSave: {
    type: Boolean,
    default: true
  },
  autoSaveKey: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['save', 'cancel', 'data-change', 'tab-change'])

// 调试助手
const { log, watchData } = useDebugHelper('DetailPage')

// 主题
const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)

// 状态
const formData = ref({ ...props.data })
const activeTab = ref(props.tabs[0]?.key || 'basic')
const isFormValid = ref(true)
const isFullscreen = ref(false)
const isFocusMode = ref(false)
// 移除自动保存状态

// 计算属性
const customTabs = computed(() => {
  return props.tabs.filter(tab => !['basic', 'content', 'history'].includes(tab.key))
})

const toc = computed(() => {
  if (props.hasMarkdownContent && formData.value[props.markdownField]) {
    return markdownRenderer.extractToc(formData.value[props.markdownField])
  }
  return []
})

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 监听数据变化
watch(() => props.data, (newData) => {
  log('props.data changed', { hasData: !!newData, isUpdating: isUpdatingFromProps.value })
  watchData('props.data', newData, 'props')

  if (!isUpdatingFromProps.value) {
    isUpdatingFromProps.value = true
    formData.value = { ...newData }
    log('formData updated from props')
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { deep: true })

// 监听模式变化
watch(() => props.mode, (newMode) => {
  if (import.meta.env.DEV) {
    console.log('DetailPage: mode变化', newMode, 'readonly:', newMode === 'view')
  }
})

watch(formData, (newData, oldData) => {
  log('formData changed', {
    isUpdatingFromProps: isUpdatingFromProps.value,
    hasChanged: JSON.stringify(newData) !== JSON.stringify(oldData)
  })
  watchData('formData', newData, 'local')

  // 避免在从props更新时触发
  if (isUpdatingFromProps.value) {
    log('skipping formData change - updating from props')
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newData) === JSON.stringify(oldData)) {
    log('skipping formData change - no actual change')
    return
  }

  log('emitting data-change')
  emit('data-change', newData)

  // 移除自动保存逻辑，改为纯手动保存
}, { deep: true })

// 方法
const handleSave = async () => {
  emit('save', formData.value)
}

const handleCancel = () => {
  emit('cancel')
}

const handleValidation = ({ valid }) => {
  isFormValid.value = valid
}

const handleTabChange = ({ activeTab: newTab }) => {
  activeTab.value = newTab
  emit('tab-change', newTab)
}

const handleMarkdownSave = (content) => {
  formData.value[props.markdownField] = content
  handleSave()
}

const handleMarkdownChange = (content) => {
  formData.value[props.markdownField] = content
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

const toggleFocusMode = () => {
  isFocusMode.value = !isFocusMode.value
}

const scrollToHeading = (id) => {
  const element = document.querySelector(`#${id}`)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape') {
    if (isFocusMode.value) {
      toggleFocusMode()
    } else if (isFullscreen.value) {
      toggleFullscreen()
    }
  }
  
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 's':
        event.preventDefault()
        handleSave()
        break
      case 'Enter':
        if (event.shiftKey) {
          event.preventDefault()
          toggleFullscreen()
        }
        break
    }
  }
}

onMounted(() => {
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})

// 暴露方法
defineExpose({
  save: handleSave,
  cancel: handleCancel,
  toggleFullscreen,
  toggleFocusMode,
  scrollToHeading
})
</script>

<style scoped>
.detail-page {
  @apply min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200;
  display: flex;
  flex-direction: column;
}

/* 全屏模式 */
.detail-page.fullscreen {
  @apply fixed inset-0 z-50 bg-white dark:bg-gray-900;
}

/* 专注模式 */
.detail-page.focus-mode {
  @apply fixed inset-0 z-50 bg-white dark:bg-gray-900;
}

.detail-page.focus-mode .page-header,
.detail-page.focus-mode .content-sidebar {
  @apply hidden;
}

.detail-page.focus-mode .main-content {
  @apply w-full max-w-4xl mx-auto p-8;
}

/* 页面头部 */
.page-header {
  @apply bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700;
  @apply sticky top-0 z-40;
}

.header-actions {
  @apply flex items-center gap-3;
}

.save-status {
  @apply flex items-center gap-2 px-3 py-1 bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-lg;
}

.status-icon {
  @apply text-sm animate-pulse;
}

.status-text {
  @apply text-xs font-medium;
}

/* 页面内容 */
.page-content {
  @apply flex flex-1 overflow-hidden;
}

/* 侧边栏 */
.content-sidebar {
  @apply w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700;
  @apply overflow-y-auto flex-shrink-0;
}

.sidebar-section {
  @apply p-4 border-b border-gray-200 dark:border-gray-700;
}

.sidebar-title {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3;
}

.toc-list {
  @apply space-y-1;
}

.toc-item {
  @apply py-1 px-2 text-sm cursor-pointer rounded hover:bg-gray-100 dark:hover:bg-gray-700;
  @apply text-gray-700 dark:text-gray-300 transition-colors;
}

.toc-level-1 { @apply font-semibold; }
.toc-level-2 { @apply pl-4; }
.toc-level-3 { @apply pl-8; }
.toc-level-4 { @apply pl-12; }
.toc-level-5 { @apply pl-16; }
.toc-level-6 { @apply pl-20; }

/* 主内容 */
.main-content {
  @apply flex-1 overflow-hidden bg-white dark:bg-gray-800;
  display: flex;
  flex-direction: column;
}

.tab-content-wrapper {
  @apply p-6 h-full overflow-auto;
}

.single-content {
  @apply p-6 h-full overflow-auto;
}

/* 专注模式控制 */
.focus-mode-controls {
  @apply fixed top-4 right-4 z-50;
}

.focus-exit-btn {
  @apply w-10 h-10 bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-800 rounded-full;
  @apply flex items-center justify-center text-xl font-bold;
  @apply hover:bg-gray-700 dark:hover:bg-gray-300 transition-colors;
  @apply shadow-lg;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-sidebar {
    @apply w-56;
  }
}

@media (max-width: 768px) {
  .page-content {
    @apply flex-col;
  }

  .content-sidebar {
    @apply w-full h-auto max-h-48 border-r-0 border-b border-gray-200 dark:border-gray-700;
  }

  .sidebar-section {
    @apply p-3;
  }

  .toc-list {
    @apply flex flex-wrap gap-2;
  }

  .toc-item {
    @apply px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-xs;
  }

  .toc-level-2,
  .toc-level-3,
  .toc-level-4,
  .toc-level-5,
  .toc-level-6 {
    @apply pl-2;
  }

  .tab-content-wrapper,
  .single-content {
    @apply p-4;
  }

  /* 编辑器容器特殊处理 */
  .tab-content-wrapper.editor-container {
    @apply p-0;
    min-height: 600px;
  }

  /* 确保编辑器在容器中正确显示 */
  .tab-content-wrapper.editor-container .markdown-editor-final {
    height: 100%;
    min-height: 600px;
  }

  .header-actions {
    @apply flex-wrap gap-2;
  }

  .detail-page.focus-mode .main-content {
    @apply p-4;
  }
}

/* 滚动条样式 */
.content-sidebar::-webkit-scrollbar,
.tab-content-wrapper::-webkit-scrollbar,
.single-content::-webkit-scrollbar {
  width: 6px;
}

.content-sidebar::-webkit-scrollbar-track,
.tab-content-wrapper::-webkit-scrollbar-track,
.single-content::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.content-sidebar::-webkit-scrollbar-thumb,
.tab-content-wrapper::-webkit-scrollbar-thumb,
.single-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.content-sidebar::-webkit-scrollbar-thumb:hover,
.tab-content-wrapper::-webkit-scrollbar-thumb:hover,
.single-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* 动画效果 */
.detail-page {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.save-status {
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .content-sidebar,
  .main-content {
    @apply border-2 border-gray-900 dark:border-gray-100;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .detail-page,
  .save-status {
    animation: none;
  }

  .toc-item,
  .focus-exit-btn {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .page-header,
  .content-sidebar,
  .focus-mode-controls {
    @apply hidden;
  }

  .main-content {
    @apply w-full;
  }

  .detail-page {
    @apply bg-white text-black;
  }
}
</style>
