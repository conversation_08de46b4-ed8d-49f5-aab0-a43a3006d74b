<template>
  <div class="tab-navigation" :class="{ 'dark': isDarkMode }">
    <!-- 标签页头部 -->
    <div class="tab-header" :class="headerClass">
      <div class="tab-list" role="tablist">
        <button
          v-for="tab in tabs"
          :key="tab.key"
          :class="[
            'tab-button',
            {
              'active': activeTab === tab.key,
              'disabled': tab.disabled,
              'has-badge': tab.badge
            }
          ]"
          :aria-selected="activeTab === tab.key"
          :aria-controls="`tabpanel-${tab.key}`"
          :disabled="tab.disabled"
          role="tab"
          @click="selectTab(tab.key)"
        >
          <span v-if="tab.icon" class="tab-icon">{{ tab.icon }}</span>
          <span class="tab-label">{{ tab.label }}</span>
          <span v-if="tab.badge" class="tab-badge">{{ tab.badge }}</span>
          <span v-if="tab.closable" class="tab-close" @click.stop="closeTab(tab.key)">×</span>
        </button>
      </div>
      
      <!-- 标签页操作 -->
      <div v-if="showActions" class="tab-actions">
        <slot name="actions" :activeTab="activeTab" :tabs="tabs" />
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="tab-content">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        :id="`tabpanel-${tab.key}`"
        :class="[
          'tab-pane',
          {
            'active': activeTab === tab.key,
            'lazy': tab.lazy && !loadedTabs.has(tab.key)
          }
        ]"
        role="tabpanel"
        :aria-labelledby="`tab-${tab.key}`"
        :aria-hidden="activeTab !== tab.key"
      >
        <!-- 懒加载内容 -->
        <template v-if="!tab.lazy || loadedTabs.has(tab.key)">
          <!-- 动态组件 -->
          <component
            v-if="tab.component"
            :is="tab.component"
            v-bind="tab.props || {}"
            @update="handleTabUpdate(tab.key, $event)"
          />
          
          <!-- 插槽内容 -->
          <slot
            v-else
            :name="tab.key"
            :tab="tab"
            :active="activeTab === tab.key"
          />
        </template>
        
        <!-- 懒加载占位符 -->
        <div v-else class="tab-placeholder">
          <div class="placeholder-content">
            <div class="placeholder-icon">📄</div>
            <p class="placeholder-text">点击加载内容</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'

const props = defineProps({
  tabs: {
    type: Array,
    required: true,
    validator: (tabs) => {
      return tabs.every(tab => 
        tab.key && tab.label && typeof tab.key === 'string'
      )
    }
  },
  modelValue: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'pills', 'underline', 'card'].includes(value)
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  showActions: {
    type: Boolean,
    default: false
  },
  lazy: {
    type: Boolean,
    default: false
  },
  keepAlive: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'tab-change', 'tab-close', 'tab-update'])

// 主题
const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)

// 状态
const activeTab = ref(props.modelValue || props.tabs[0]?.key || '')
const loadedTabs = ref(new Set())

// 计算属性
const headerClass = computed(() => {
  return [
    `variant-${props.variant}`,
    `size-${props.size}`
  ]
})

// 监听外部变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue !== activeTab.value) {
    selectTab(newValue)
  }
})

watch(() => props.tabs, (newTabs) => {
  // 如果当前活动标签页不存在，切换到第一个可用标签页
  if (!newTabs.find(tab => tab.key === activeTab.value)) {
    const firstAvailable = newTabs.find(tab => !tab.disabled)
    if (firstAvailable) {
      selectTab(firstAvailable.key)
    }
  }
}, { deep: true })

// 方法
const selectTab = (tabKey) => {
  const tab = props.tabs.find(t => t.key === tabKey)
  if (!tab || tab.disabled) return

  const oldTab = activeTab.value
  activeTab.value = tabKey

  // 标记为已加载
  if (props.lazy || tab.lazy) {
    loadedTabs.value.add(tabKey)
  }

  emit('update:modelValue', tabKey)
  emit('tab-change', {
    activeTab: tabKey,
    previousTab: oldTab,
    tab
  })
}

const closeTab = (tabKey) => {
  const tab = props.tabs.find(t => t.key === tabKey)
  if (!tab || !tab.closable) return

  emit('tab-close', {
    tab,
    tabKey
  })

  // 如果关闭的是当前活动标签页，切换到下一个可用标签页
  if (activeTab.value === tabKey) {
    const remainingTabs = props.tabs.filter(t => t.key !== tabKey && !t.disabled)
    if (remainingTabs.length > 0) {
      selectTab(remainingTabs[0].key)
    }
  }
}

const handleTabUpdate = (tabKey, data) => {
  emit('tab-update', {
    tabKey,
    data
  })
}

// 键盘导航
const handleKeydown = (event) => {
  const currentIndex = props.tabs.findIndex(tab => tab.key === activeTab.value)
  let nextIndex = currentIndex

  switch (event.key) {
    case 'ArrowLeft':
      event.preventDefault()
      nextIndex = currentIndex > 0 ? currentIndex - 1 : props.tabs.length - 1
      break
    case 'ArrowRight':
      event.preventDefault()
      nextIndex = currentIndex < props.tabs.length - 1 ? currentIndex + 1 : 0
      break
    case 'Home':
      event.preventDefault()
      nextIndex = 0
      break
    case 'End':
      event.preventDefault()
      nextIndex = props.tabs.length - 1
      break
    default:
      return
  }

  // 找到下一个可用的标签页
  while (props.tabs[nextIndex]?.disabled) {
    if (event.key === 'ArrowLeft' || event.key === 'End') {
      nextIndex = nextIndex > 0 ? nextIndex - 1 : props.tabs.length - 1
    } else {
      nextIndex = nextIndex < props.tabs.length - 1 ? nextIndex + 1 : 0
    }
    
    // 防止无限循环
    if (nextIndex === currentIndex) break
  }

  if (props.tabs[nextIndex] && !props.tabs[nextIndex].disabled) {
    selectTab(props.tabs[nextIndex].key)
  }
}

onMounted(() => {
  // 初始化活动标签页
  if (!activeTab.value && props.tabs.length > 0) {
    const firstAvailable = props.tabs.find(tab => !tab.disabled)
    if (firstAvailable) {
      selectTab(firstAvailable.key)
    }
  }

  // 预加载非懒加载标签页
  if (!props.lazy) {
    props.tabs.forEach(tab => {
      if (!tab.lazy) {
        loadedTabs.value.add(tab.key)
      }
    })
  } else {
    // 懒加载模式下，只加载当前活动标签页
    if (activeTab.value) {
      loadedTabs.value.add(activeTab.value)
    }
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 暴露方法
defineExpose({
  selectTab,
  closeTab,
  activeTab: computed(() => activeTab.value),
  loadedTabs: computed(() => Array.from(loadedTabs.value))
})
</script>

<style scoped>
.tab-navigation {
  @apply w-full;
}

/* 标签页头部 */
.tab-header {
  @apply flex items-center justify-between border-b border-gray-200 dark:border-gray-700;
}

.tab-list {
  @apply flex items-center;
}

.tab-actions {
  @apply flex items-center gap-2 px-4;
}

/* 标签页按钮 */
.tab-button {
  @apply relative flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all;
  @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100;
  @apply border-b-2 border-transparent;
  outline: none;
}

.tab-button:focus {
  @apply ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-800;
}

.tab-button.active {
  @apply text-blue-600 dark:text-blue-400 border-blue-600 dark:border-blue-400;
}

.tab-button.disabled {
  @apply opacity-50 cursor-not-allowed;
}

.tab-button:not(.disabled):hover {
  @apply bg-gray-50 dark:bg-gray-800;
}

/* 标签页元素 */
.tab-icon {
  @apply text-base;
}

.tab-label {
  @apply whitespace-nowrap;
}

.tab-badge {
  @apply bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full min-w-[1.25rem] text-center;
}

.tab-close {
  @apply w-4 h-4 flex items-center justify-center rounded text-gray-400 hover:text-gray-600 dark:hover:text-gray-200;
  @apply hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
}

/* 变体样式 */
/* Pills 变体 */
.tab-header.variant-pills .tab-button {
  @apply rounded-lg border-0 mx-1;
}

.tab-header.variant-pills .tab-button.active {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
}

/* Underline 变体 */
.tab-header.variant-underline {
  @apply border-b-0;
}

.tab-header.variant-underline .tab-button {
  @apply border-b-2 border-transparent;
}

.tab-header.variant-underline .tab-button.active {
  @apply border-blue-600 dark:border-blue-400;
}

/* Card 变体 */
.tab-header.variant-card {
  @apply bg-gray-50 dark:bg-gray-900 border-0 rounded-t-lg;
}

.tab-header.variant-card .tab-button {
  @apply border-0 border-b-2 border-transparent;
}

.tab-header.variant-card .tab-button.active {
  @apply bg-white dark:bg-gray-800 border-blue-600 dark:border-blue-400;
}

/* 尺寸变体 */
.tab-header.size-sm .tab-button {
  @apply px-3 py-2 text-xs;
}

.tab-header.size-lg .tab-button {
  @apply px-6 py-4 text-base;
}

/* 标签页内容 */
.tab-content {
  @apply relative;
}

.tab-pane {
  @apply hidden;
}

.tab-pane.active {
  @apply block;
}

.tab-pane.lazy {
  @apply flex items-center justify-center min-h-[200px];
}

/* 占位符 */
.tab-placeholder {
  @apply flex items-center justify-center min-h-[200px] text-gray-500 dark:text-gray-400;
}

.placeholder-content {
  @apply text-center;
}

.placeholder-icon {
  @apply text-4xl mb-2;
}

.placeholder-text {
  @apply text-sm;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-header {
    @apply flex-col items-stretch;
  }

  .tab-list {
    @apply overflow-x-auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .tab-list::-webkit-scrollbar {
    display: none;
  }

  .tab-button {
    @apply flex-shrink-0;
  }

  .tab-actions {
    @apply px-0 pt-2 border-t border-gray-200 dark:border-gray-700;
  }
}

/* 滚动条样式 */
.tab-list::-webkit-scrollbar {
  height: 2px;
}

.tab-list::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.tab-list::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded;
}

/* 动画效果 */
.tab-button {
  transition: all 0.2s ease;
}

.tab-pane {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .tab-button.active {
    @apply border-b-4;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .tab-button,
  .tab-pane {
    transition: none;
    animation: none;
  }
}
</style>
