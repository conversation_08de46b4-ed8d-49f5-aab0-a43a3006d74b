<template>
  <Teleport to="body">
    <Transition name="modal" appear>
      <div
        v-if="visible"
        class="detail-modal-overlay"
        @click="handleOverlayClick"
        @keydown.esc="handleClose"
        tabindex="-1"
      >
        <div
          ref="modalRef"
          class="detail-modal"
          :class="[
            `size-${size}`,
            { 'dark': isDarkMode }
          ]"
          @click.stop
          role="dialog"
          :aria-labelledby="titleId"
          :aria-describedby="descriptionId"
          aria-modal="true"
        >
          <!-- 模态框头部 -->
          <div class="modal-header">
            <div class="header-content">
              <h3 :id="titleId" class="modal-title">
                <span v-if="icon" class="title-icon">{{ icon }}</span>
                {{ title }}
              </h3>
              <p v-if="description" :id="descriptionId" class="modal-description">
                {{ description }}
              </p>
            </div>
            
            <div class="header-actions">
              <button
                v-if="showFullscreen"
                @click="toggleFullscreen"
                class="action-btn"
                :title="isFullscreen ? '退出全屏' : '全屏'"
              >
                {{ isFullscreen ? '⊡' : '⊞' }}
              </button>
              
              <button
                @click="handleClose"
                class="action-btn close-btn"
                title="关闭 (ESC)"
              >
                ×
              </button>
            </div>
          </div>

          <!-- 模态框内容 -->
          <div class="modal-body" :class="{ 'has-footer': hasFooter }">
            <FormValidator
              v-if="validationRules"
              :rules="validationRules"
              :model-value="formData"
              :validate-on-change="validateOnChange"
              @validate="handleValidation"
              v-slot="{ validate, errors, isValid, reset }"
            >
              <!-- 动态组件渲染 -->
              <component
                v-if="component"
                :is="component"
                v-model="formData"
                :mode="mode"
                :errors="errors"
                :loading="loading"
                v-bind="componentProps"
                @update:modelValue="handleDataChange"
                @save="handleSave"
                @cancel="handleClose"
              />
              
              <!-- 默认内容插槽 -->
              <div v-else class="default-content">
                <slot
                  :data="formData"
                  :errors="errors"
                  :isValid="isValid"
                  :validate="validate"
                  :reset="reset"
                  :loading="loading"
                  :save="handleSave"
                  :cancel="handleClose"
                />
              </div>
            </FormValidator>
            
            <!-- 无验证的简单内容 -->
            <div v-else class="simple-content">
              <component
                v-if="component"
                :is="component"
                v-model="formData"
                :mode="mode"
                :loading="loading"
                v-bind="componentProps"
                @update:modelValue="handleDataChange"
                @save="handleSave"
                @cancel="handleClose"
              />
              
              <div v-else>
                <slot
                  :data="formData"
                  :loading="loading"
                  :save="handleSave"
                  :cancel="handleClose"
                />
              </div>
            </div>
          </div>

          <!-- 模态框底部 -->
          <div v-if="hasFooter" class="modal-footer">
            <slot name="footer" :save="handleSave" :cancel="handleClose" :loading="loading" :isValid="isFormValid">
              <div class="footer-actions">
                <ActionButton
                  variant="secondary"
                  @click="handleClose"
                  :disabled="loading"
                >
                  {{ cancelText }}
                </ActionButton>
                
                <ActionButton
                  v-if="mode !== 'view'"
                  variant="primary"
                  @click="handleSave"
                  :disabled="loading || !isFormValid"
                  :icon="loading ? '⟳' : saveIcon"
                >
                  {{ loading ? '保存中...' : saveText }}
                </ActionButton>
              </div>
            </slot>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import FormValidator from './FormValidator.vue'
import ActionButton from '@/components/ActionButton.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl', 'full'].includes(value)
  },
  mode: {
    type: String,
    default: 'edit',
    validator: (value) => ['view', 'edit', 'create'].includes(value)
  },
  component: {
    type: [String, Object],
    default: null
  },
  componentProps: {
    type: Object,
    default: () => ({})
  },
  data: {
    type: Object,
    default: () => ({})
  },
  validationRules: {
    type: Object,
    default: null
  },
  validateOnChange: {
    type: Boolean,
    default: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  showFooter: {
    type: Boolean,
    default: true
  },
  showFullscreen: {
    type: Boolean,
    default: false
  },
  closeOnOverlay: {
    type: Boolean,
    default: true
  },
  saveText: {
    type: String,
    default: '保存'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  saveIcon: {
    type: String,
    default: '💾'
  }
})

const emit = defineEmits(['update:visible', 'save', 'cancel', 'close', 'data-change'])

// 主题
const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)

// 状态
const modalRef = ref(null)
const formData = ref({ ...props.data })
const isFormValid = ref(true)
const isFullscreen = ref(false)

// 生成唯一ID
const titleId = `modal-title-${Math.random().toString(36).substr(2, 9)}`
const descriptionId = `modal-desc-${Math.random().toString(36).substr(2, 9)}`

// 计算属性
const hasFooter = computed(() => {
  return props.showFooter || !!$slots.footer
})

// 监听数据变化
watch(() => props.data, (newData) => {
  formData.value = { ...newData }
}, { deep: true })

watch(() => props.visible, (visible) => {
  if (visible) {
    nextTick(() => {
      // 聚焦到模态框
      modalRef.value?.focus()
      // 锁定body滚动
      document.body.style.overflow = 'hidden'
    })
  } else {
    // 恢复body滚动
    document.body.style.overflow = ''
    isFullscreen.value = false
  }
})

// 方法
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose()
  }
}

const handleSave = async () => {
  emit('save', formData.value)
}

const handleDataChange = (newData) => {
  formData.value = newData
  emit('data-change', newData)
}

const handleValidation = ({ valid, errors }) => {
  isFormValid.value = valid
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 键盘事件处理
const handleKeydown = (event) => {
  if (event.key === 'Escape' && props.visible) {
    handleClose()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  document.body.style.overflow = ''
})

// 暴露方法
defineExpose({
  close: handleClose,
  save: handleSave,
  toggleFullscreen
})
</script>

<style scoped>
/* 模态框遮罩层 */
.detail-modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50;
  backdrop-filter: blur(2px);
}

/* 模态框主体 */
.detail-modal {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-2xl max-h-full overflow-hidden;
  @apply border border-gray-200 dark:border-gray-700;
  display: flex;
  flex-direction: column;
  outline: none;
}

/* 尺寸变体 */
.detail-modal.size-sm {
  @apply w-full max-w-md;
}

.detail-modal.size-md {
  @apply w-full max-w-2xl;
}

.detail-modal.size-lg {
  @apply w-full max-w-4xl;
}

.detail-modal.size-xl {
  @apply w-full max-w-6xl;
}

.detail-modal.size-full {
  @apply w-full h-full max-w-none max-h-none rounded-none;
}

/* 全屏模式 */
.detail-modal-overlay:has(.detail-modal.size-full) {
  @apply p-0;
}

/* 模态框头部 */
.modal-header {
  @apply flex items-start justify-between p-6 border-b border-gray-200 dark:border-gray-700;
  @apply bg-gray-50 dark:bg-gray-900;
}

.header-content {
  @apply flex-1 min-w-0;
}

.modal-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2;
  @apply mb-1;
}

.title-icon {
  @apply text-2xl;
}

.modal-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mt-1;
}

.header-actions {
  @apply flex items-center gap-2 ml-4;
}

.action-btn {
  @apply w-8 h-8 flex items-center justify-center rounded-lg;
  @apply text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200;
  @apply hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors;
}

.close-btn {
  @apply text-lg font-bold;
}

/* 模态框内容 */
.modal-body {
  @apply flex-1 overflow-auto p-6;
  @apply text-gray-900 dark:text-gray-100;
}

.modal-body.has-footer {
  @apply pb-0;
}

.default-content,
.simple-content {
  @apply h-full;
}

/* 模态框底部 */
.modal-footer {
  @apply p-6 border-t border-gray-200 dark:border-gray-700;
  @apply bg-gray-50 dark:bg-gray-900;
}

.footer-actions {
  @apply flex items-center justify-end gap-3;
}

/* 动画效果 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .detail-modal,
.modal-leave-to .detail-modal {
  transform: scale(0.9) translateY(-20px);
}

.modal-enter-to .detail-modal,
.modal-leave-from .detail-modal {
  transform: scale(1) translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-modal-overlay {
    @apply p-2;
  }

  .detail-modal {
    @apply w-full h-full max-h-none rounded-lg;
  }

  .detail-modal.size-sm,
  .detail-modal.size-md,
  .detail-modal.size-lg,
  .detail-modal.size-xl {
    @apply w-full h-full max-w-none;
  }

  .modal-header {
    @apply p-4;
  }

  .modal-body {
    @apply p-4;
  }

  .modal-footer {
    @apply p-4;
  }

  .footer-actions {
    @apply flex-col-reverse gap-2;
  }

  .footer-actions button {
    @apply w-full;
  }
}

/* 滚动条样式 */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

.modal-body::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* 焦点样式 */
.detail-modal:focus {
  outline: 2px solid theme('colors.blue.500');
  outline-offset: -2px;
}

/* 加载状态 */
.action-btn[disabled] {
  @apply opacity-50 cursor-not-allowed;
}

/* 深色模式特殊处理 */
.dark .detail-modal-overlay {
  backdrop-filter: blur(2px);
  background-color: rgba(0, 0, 0, 0.7);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .detail-modal {
    @apply border-2 border-gray-900 dark:border-gray-100;
  }

  .modal-header,
  .modal-footer {
    @apply border-gray-900 dark:border-gray-100;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .modal-enter-active,
  .modal-leave-active {
    transition: opacity 0.2s ease;
  }

  .modal-enter-from .detail-modal,
  .modal-leave-to .detail-modal,
  .modal-enter-to .detail-modal,
  .modal-leave-from .detail-modal {
    transform: none;
  }
}
</style>
