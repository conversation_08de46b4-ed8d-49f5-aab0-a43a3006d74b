<template>
  <Teleport to="body">
    <Transition name="mobile-overlay">
      <div
        v-if="isOpen"
        class="mobile-menu-overlay"
        @click="close"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <div class="overlay-backdrop" />
        <div class="mobile-menu-container" @click.stop>
          <!-- 移动端头部 -->
          <div class="mobile-header">
            <div class="header-content">
              <div class="logo-section">
                <div class="mobile-logo">
                  <MenuIcons name="service" :size="28" />
                </div>
                <div class="logo-text">
                  <h1 class="logo-title">Admin Pro</h1>
                  <p class="logo-subtitle">智能管理控制台</p>
                </div>
              </div>
              <button @click="close" class="close-button" :aria-label="'关闭菜单'">
                <MenuIcons name="close" :size="24" />
              </button>
            </div>
          </div>

          <!-- 移动端菜单内容 -->
          <div class="mobile-menu-content">
            <!-- 快速操作区 -->
            <div class="quick-actions">
              <button @click="handleThemeToggle" class="quick-action-btn">
                <MenuIcons :name="isDarkMode ? 'sun' : 'moon'" :size="20" />
                <span>{{ isDarkMode ? '浅色模式' : '深色模式' }}</span>
              </button>
              <button @click="handleSearch" class="quick-action-btn">
                <MenuIcons name="search" :size="20" />
                <span>搜索菜单</span>
              </button>
            </div>

            <!-- 主菜单 -->
            <div class="main-menu">
              <div class="menu-section">
                <h3 class="section-title">主要功能</h3>
                <div class="menu-items">
                  <button
                    v-for="item in mainMenuItems"
                    :key="item.id"
                    @click="handleMenuClick(item)"
                    class="mobile-menu-item"
                    :class="{ 'active': isActive(item.route) }"
                  >
                    <div class="item-icon">
                      <MenuIcons :name="item.icon" :size="22" />
                    </div>
                    <div class="item-content">
                      <div class="item-name">{{ item.name }}</div>
                      <div class="item-description">{{ item.description }}</div>
                    </div>
                    <div v-if="item.badge" class="item-badge">{{ item.badge }}</div>
                  </button>
                </div>
              </div>

              <div class="menu-section">
                <h3 class="section-title">服务管理</h3>
                <div class="menu-items">
                  <button
                    v-for="item in serviceMenuItems"
                    :key="item.id"
                    @click="handleMenuClick(item)"
                    class="mobile-menu-item"
                    :class="{ 'active': isActive(item.route) }"
                  >
                    <div class="item-icon">
                      <MenuIcons :name="item.icon" :size="22" />
                    </div>
                    <div class="item-content">
                      <div class="item-name">{{ item.name }}</div>
                      <div class="item-description">{{ item.description }}</div>
                    </div>
                    <div v-if="item.badge" class="item-badge">{{ item.badge }}</div>
                  </button>
                </div>
              </div>

              <div class="menu-section">
                <h3 class="section-title">个人中心</h3>
                <div class="menu-items">
                  <button
                    v-for="item in personalMenuItems"
                    :key="item.id"
                    @click="handleMenuClick(item)"
                    class="mobile-menu-item"
                    :class="{ 'active': isActive(item.route) }"
                  >
                    <div class="item-icon">
                      <MenuIcons :name="item.icon" :size="22" />
                    </div>
                    <div class="item-content">
                      <div class="item-name">{{ item.name }}</div>
                      <div class="item-description">{{ item.description }}</div>
                    </div>
                  </button>
                </div>
              </div>
            </div>

            <!-- 底部信息 -->
            <div class="mobile-footer">
              <div class="user-info">
                <div class="user-avatar">
                  <MenuIcons name="users" :size="20" />
                </div>
                <div class="user-details">
                  <div class="user-name">管理员</div>
                  <div class="user-role">系统管理员</div>
                </div>
              </div>
              <div class="footer-actions">
                <button @click="handleLogout" class="logout-btn">
                  <MenuIcons name="logout" :size="18" />
                  <span>退出登录</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import MenuIcons from './icons/MenuIcons.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'search'])

const router = useRouter()
const route = useRoute()
const themeStore = useThemeStore()

// 触摸手势相关
const touchStartX = ref(0)
const touchStartY = ref(0)

// 计算属性
const isDarkMode = computed(() => themeStore.isDarkMode)

// 菜单数据
const mainMenuItems = [
  {
    id: 'dashboard',
    name: '数据看板',
    icon: 'dashboard',
    route: '/dashboard',
    description: '查看系统数据统计'
  },
  {
    id: 'users',
    name: '用户管理',
    icon: 'users',
    route: '/users',
    description: '管理系统用户'
  }
]

const serviceMenuItems = [
  {
    id: 'services',
    name: '服务管理',
    icon: 'service',
    route: '/services',
    description: '管理MCP服务',
    badge: 12
  },
  {
    id: 'categories',
    name: '分类管理',
    icon: 'category',
    route: '/categories',
    description: '管理内容分类'
  },
  {
    id: 'tasks',
    name: '任务管理',
    icon: 'tasks',
    route: '/tasks',
    description: '管理定时任务'
  }
]

const personalMenuItems = [
  {
    id: 'wallet',
    name: '钱包',
    icon: 'wallet',
    route: '/wallet',
    description: '查看账户余额'
  },
  {
    id: 'settings',
    name: '个人设置',
    icon: 'settings',
    route: '/settings',
    description: '配置个人偏好'
  }
]

// 方法
const close = () => {
  emit('close')
}

const isActive = (routePath) => {
  return route.path === routePath
}

const handleMenuClick = (item) => {
  router.push(item.route)
  close()
}

const handleThemeToggle = () => {
  themeStore.toggleTheme()
}

const handleSearch = () => {
  emit('search')
  close()
}

const handleLogout = () => {
  // 实现退出登录逻辑
  console.log('退出登录')
  close()
}

// 触摸手势处理
const handleTouchStart = (e) => {
  touchStartX.value = e.touches[0].clientX
  touchStartY.value = e.touches[0].clientY
}

const handleTouchMove = (e) => {
  // 防止背景滚动
  e.preventDefault()
}

const handleTouchEnd = (e) => {
  const touchEndX = e.changedTouches[0].clientX
  const touchEndY = e.changedTouches[0].clientY
  
  const diffX = touchStartX.value - touchEndX
  const diffY = touchStartY.value - touchEndY
  
  // 检测向左滑动关闭菜单
  if (Math.abs(diffX) > Math.abs(diffY) && diffX > 100) {
    close()
  }
}
</script>

<style scoped>
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
}

.overlay-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.mobile-menu-container {
  position: relative;
  width: 320px;
  height: 100%;
  background: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.98) 0%, 
    rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dark .mobile-menu-container {
  background: linear-gradient(180deg, 
    rgba(15, 23, 42, 0.98) 0%, 
    rgba(2, 6, 23, 0.95) 100%);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3);
}

.mobile-header {
  padding: 1.5rem 1.25rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.9) 0%, 
    rgba(248, 250, 252, 0.8) 100%);
}

.dark .mobile-header {
  border-bottom-color: rgba(51, 65, 85, 0.6);
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.9) 0%, 
    rgba(2, 6, 23, 0.8) 100%);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mobile-logo {
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, 
    var(--color-primary-500) 0%, 
    var(--color-secondary-500) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.25);
}

.logo-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin: 0;
  line-height: 1.2;
  background: linear-gradient(135deg, 
    var(--color-primary-600) 0%, 
    var(--color-secondary-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-subtitle {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-neutral-500);
  margin: 0;
  line-height: 1.2;
  margin-top: 0.125rem;
}

.dark .logo-title {
  background: linear-gradient(135deg, 
    var(--color-primary-400) 0%, 
    var(--color-secondary-400) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .logo-subtitle {
  color: var(--color-neutral-400);
}

.close-button {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  color: var(--color-neutral-600);
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: scale(1.05);
}

.dark .close-button {
  background: rgba(15, 23, 42, 0.8);
  border-color: rgba(51, 65, 85, 0.8);
  color: var(--color-neutral-400);
}

.dark .close-button:hover {
  background: rgba(15, 23, 42, 0.95);
}

.mobile-menu-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.quick-actions {
  display: flex;
  gap: 0.75rem;
}

.quick-action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.875rem;
  background: var(--color-neutral-100);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: 500;
  color: var(--color-neutral-700);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.quick-action-btn:hover {
  background: var(--color-neutral-200);
  transform: translateY(-1px);
}

.dark .quick-action-btn {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
  color: var(--color-neutral-300);
}

.dark .quick-action-btn:hover {
  background: var(--color-neutral-700);
}

.main-menu {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.menu-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.section-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-neutral-600);
  margin: 0;
  padding: 0 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dark .section-title {
  color: var(--color-neutral-400);
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  text-align: left;
  position: relative;
}

.mobile-menu-item:hover {
  background: var(--color-neutral-100);
  transform: translateX(4px);
  box-shadow: var(--shadow-md);
}

.mobile-menu-item.active {
  background: linear-gradient(135deg, 
    var(--color-primary-500) 0%, 
    var(--color-secondary-500) 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

.dark .mobile-menu-item {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.dark .mobile-menu-item:hover {
  background: var(--color-neutral-700);
}

.item-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-secondary-100));
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary-600);
  flex-shrink: 0;
}

.mobile-menu-item.active .item-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.dark .item-icon {
  background: linear-gradient(135deg, var(--color-primary-900), var(--color-secondary-900));
  color: var(--color-primary-400);
}

.item-content {
  flex: 1;
}

.item-name {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: 0.25rem;
  line-height: 1.3;
}

.mobile-menu-item.active .item-name {
  color: white;
}

.dark .item-name {
  color: var(--color-neutral-100);
}

.item-description {
  font-size: var(--font-size-sm);
  color: var(--color-neutral-600);
  line-height: 1.4;
}

.mobile-menu-item.active .item-description {
  color: rgba(255, 255, 255, 0.8);
}

.dark .item-description {
  color: var(--color-neutral-400);
}

.item-badge {
  background: var(--color-error-500);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-full);
  min-width: 1.5rem;
  text-align: center;
}

.mobile-footer {
  margin-top: auto;
  padding: 1.25rem;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  background: rgba(248, 250, 252, 0.8);
}

.dark .mobile-footer {
  border-top-color: rgba(51, 65, 85, 0.6);
  background: rgba(2, 6, 23, 0.8);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-full);
  background: var(--color-primary-500);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: 0.125rem;
}

.dark .user-name {
  color: var(--color-neutral-100);
}

.user-role {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
}

.dark .user-role {
  color: var(--color-neutral-400);
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: var(--color-error-50);
  border: 1px solid var(--color-error-200);
  border-radius: var(--radius-lg);
  color: var(--color-error-700);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  width: 100%;
  justify-content: center;
}

.logout-btn:hover {
  background: var(--color-error-100);
  transform: translateY(-1px);
}

.dark .logout-btn {
  background: var(--color-error-900);
  border-color: var(--color-error-700);
  color: var(--color-error-300);
}

.dark .logout-btn:hover {
  background: var(--color-error-800);
}

/* 动画 */
.mobile-overlay-enter-active,
.mobile-overlay-leave-active {
  transition: all var(--duration-300) var(--ease-out);
}

.mobile-overlay-enter-from {
  opacity: 0;
}

.mobile-overlay-leave-to {
  opacity: 0;
}

.mobile-overlay-enter-from .mobile-menu-container {
  transform: translateX(-100%);
}

.mobile-overlay-leave-to .mobile-menu-container {
  transform: translateX(-100%);
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .mobile-menu-container {
    width: 100vw;
  }
  
  .quick-actions {
    flex-direction: column;
  }
}
</style>
