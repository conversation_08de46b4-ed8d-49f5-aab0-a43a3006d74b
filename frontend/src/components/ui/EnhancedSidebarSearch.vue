<template>
  <div class="enhanced-search-container">
    <!-- 搜索输入框 -->
    <div class="search-input-wrapper" :class="{ 'focused': isFocused, 'has-results': hasResults }">
      <div class="search-icon-wrapper">
        <MenuIcons name="search" :size="16" />
      </div>
      <input
        ref="searchInput"
        v-model="searchQuery"
        type="text"
        :placeholder="placeholder"
        class="search-input"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        @input="handleInput"
        :aria-label="placeholder"
        autocomplete="off"
      />
      <div v-if="searchQuery" class="clear-button" @click="clearSearch">
        <MenuIcons name="close" :size="14" />
      </div>
    </div>

    <!-- 搜索结果下拉 -->
    <Transition name="search-results">
      <div v-if="showResults && hasResults" class="search-results-dropdown">
        <div class="search-results-header">
          <span class="results-count">找到 {{ filteredResults.length }} 个结果</span>
          <button v-if="hasRecentSearches" @click="showRecent = !showRecent" class="toggle-recent">
            {{ showRecent ? '隐藏' : '显示' }}最近搜索
          </button>
        </div>
        
        <!-- 最近搜索 -->
        <div v-if="showRecent && hasRecentSearches" class="recent-searches">
          <div class="section-title">最近搜索</div>
          <div class="recent-items">
            <button
              v-for="recent in recentSearches.slice(0, 3)"
              :key="recent"
              @click="selectRecentSearch(recent)"
              class="recent-item"
            >
              <MenuIcons name="search" :size="14" />
              <span>{{ recent }}</span>
            </button>
          </div>
        </div>

        <!-- 搜索结果 -->
        <div class="search-results-list">
          <button
            v-for="(result, index) in filteredResults"
            :key="result.id"
            :class="['search-result-item', { 'highlighted': index === highlightedIndex }]"
            @click="selectResult(result)"
            @mouseenter="highlightedIndex = index"
          >
            <div class="result-icon">
              <MenuIcons :name="result.icon" :size="16" />
            </div>
            <div class="result-content">
              <div class="result-title" v-html="highlightMatch(result.title)"></div>
              <div class="result-path">{{ result.path }}</div>
            </div>
            <div v-if="result.badge" class="result-badge">
              {{ result.badge }}
            </div>
          </button>
        </div>

        <!-- 快捷键提示 -->
        <div class="search-shortcuts">
          <span class="shortcut"><kbd>↑</kbd><kbd>↓</kbd> 导航</span>
          <span class="shortcut"><kbd>Enter</kbd> 选择</span>
          <span class="shortcut"><kbd>Esc</kbd> 关闭</span>
        </div>
      </div>
    </Transition>

    <!-- 空状态 -->
    <Transition name="search-results">
      <div v-if="showResults && !hasResults && searchQuery" class="search-empty">
        <div class="empty-icon">🔍</div>
        <div class="empty-title">未找到相关结果</div>
        <div class="empty-subtitle">尝试使用其他关键词搜索</div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import MenuIcons from './icons/MenuIcons.vue'

const props = defineProps({
  placeholder: {
    type: String,
    default: '搜索菜单...'
  },
  menuItems: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['search', 'select'])

const router = useRouter()
const searchInput = ref(null)
const searchQuery = ref('')
const isFocused = ref(false)
const showResults = ref(false)
const showRecent = ref(false)
const highlightedIndex = ref(-1)
const recentSearches = ref([])

// 模拟菜单数据 - 实际应用中应该从props或store获取
const menuData = ref([
  { id: 'dashboard', title: '数据看板', path: '首页 / 控制台', icon: 'dashboard', route: '/dashboard' },
  { id: 'users', title: '用户管理', path: '首页 / 控制台', icon: 'users', route: '/users' },
  { id: 'services', title: '服务管理', path: '首页 / MCP服务', icon: 'service', route: '/services', badge: '12' },
  { id: 'categories', title: '分类管理', path: '首页 / MCP服务', icon: 'category', route: '/categories' },
  { id: 'tasks', title: '任务管理', path: '首页 / 定时任务', icon: 'tasks', route: '/tasks' },
  { id: 'wallet', title: '钱包', path: '首页 / 个人中心', icon: 'wallet', route: '/wallet' },
  { id: 'settings', title: '个人设置', path: '首页 / 个人中心', icon: 'settings', route: '/settings' }
])

const filteredResults = computed(() => {
  if (!searchQuery.value) return []
  
  const query = searchQuery.value.toLowerCase()
  return menuData.value.filter(item => 
    item.title.toLowerCase().includes(query) ||
    item.path.toLowerCase().includes(query)
  ).slice(0, 8) // 限制结果数量
})

const hasResults = computed(() => filteredResults.value.length > 0)
const hasRecentSearches = computed(() => recentSearches.value.length > 0)

// 高亮匹配文本
const highlightMatch = (text) => {
  if (!searchQuery.value) return text
  const regex = new RegExp(`(${searchQuery.value})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 事件处理
const handleFocus = () => {
  isFocused.value = true
  showResults.value = true
}

const handleBlur = () => {
  isFocused.value = false
  // 延迟关闭，允许点击结果
  setTimeout(() => {
    showResults.value = false
    highlightedIndex.value = -1
  }, 200)
}

const handleInput = () => {
  highlightedIndex.value = -1
  emit('search', searchQuery.value)
}

const handleKeydown = (event) => {
  if (!showResults.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredResults.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0) {
        selectResult(filteredResults.value[highlightedIndex.value])
      }
      break
    case 'Escape':
      event.preventDefault()
      clearSearch()
      searchInput.value?.blur()
      break
  }
}

const selectResult = (result) => {
  addToRecentSearches(searchQuery.value)
  emit('select', result)
  router.push(result.route)
  clearSearch()
  searchInput.value?.blur()
}

const selectRecentSearch = (query) => {
  searchQuery.value = query
  handleInput()
}

const clearSearch = () => {
  searchQuery.value = ''
  showResults.value = false
  highlightedIndex.value = -1
}

const addToRecentSearches = (query) => {
  if (!query || recentSearches.value.includes(query)) return
  
  recentSearches.value.unshift(query)
  if (recentSearches.value.length > 5) {
    recentSearches.value = recentSearches.value.slice(0, 5)
  }
  
  // 保存到localStorage
  localStorage.setItem('sidebar-recent-searches', JSON.stringify(recentSearches.value))
}

// 生命周期
onMounted(() => {
  // 从localStorage恢复最近搜索
  const saved = localStorage.getItem('sidebar-recent-searches')
  if (saved) {
    try {
      recentSearches.value = JSON.parse(saved)
    } catch (e) {
      console.warn('Failed to parse recent searches:', e)
    }
  }
})

// 全局快捷键支持
const handleGlobalKeydown = (event) => {
  // Ctrl/Cmd + K 聚焦搜索
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    searchInput.value?.focus()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleGlobalKeydown)
})
</script>

<style scoped>
.enhanced-search-container {
  position: relative;
  margin-bottom: 1rem;
}

.search-input-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: var(--radius-xl);
  transition: all var(--duration-200) var(--ease-out);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.search-input-wrapper.focused {
  border-color: var(--color-primary-400);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.search-input-wrapper.has-results {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.dark .search-input-wrapper {
  background: rgba(15, 23, 42, 0.8);
  border-color: rgba(51, 65, 85, 0.8);
}

.dark .search-input-wrapper.focused {
  background: rgba(15, 23, 42, 0.95);
  border-color: var(--color-primary-500);
}

.search-icon-wrapper {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-neutral-400);
  pointer-events: none;
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 2.75rem;
  background: transparent;
  border: none;
  outline: none;
  font-size: var(--font-size-sm);
  color: var(--color-neutral-700);
  placeholder-color: var(--color-neutral-400);
}

.dark .search-input {
  color: var(--color-neutral-200);
}

.clear-button {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: var(--radius-full);
  background: var(--color-neutral-200);
  color: var(--color-neutral-600);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  z-index: 2;
}

.clear-button:hover {
  background: var(--color-neutral-300);
  transform: translateY(-50%) scale(1.1);
}

.dark .clear-button {
  background: var(--color-neutral-600);
  color: var(--color-neutral-300);
}

.dark .clear-button:hover {
  background: var(--color-neutral-500);
}

.search-results-dropdown,
.search-empty {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-top: none;
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 1000;
  max-height: 24rem;
  overflow-y: auto;
}

.dark .search-results-dropdown,
.dark .search-empty {
  background: rgba(15, 23, 42, 0.98);
  border-color: rgba(51, 65, 85, 0.8);
}

.search-results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.dark .search-results-header {
  border-bottom-color: rgba(51, 65, 85, 0.6);
}

.results-count {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
  font-weight: var(--font-weight-medium);
}

.toggle-recent {
  font-size: var(--font-size-xs);
  color: var(--color-primary-600);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-md);
  transition: background-color var(--duration-200) var(--ease-out);
}

.toggle-recent:hover {
  background: rgba(14, 165, 233, 0.1);
}

.dark .toggle-recent {
  color: var(--color-primary-400);
}

.recent-searches {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.dark .recent-searches {
  border-bottom-color: rgba(51, 65, 85, 0.6);
}

.section-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-600);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.dark .section-title {
  color: var(--color-neutral-400);
}

.recent-items {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  background: rgba(14, 165, 233, 0.1);
  color: var(--color-primary-700);
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.recent-item:hover {
  background: rgba(14, 165, 233, 0.2);
  transform: scale(1.05);
}

.dark .recent-item {
  background: rgba(14, 165, 233, 0.2);
  color: var(--color-primary-300);
}

.dark .recent-item:hover {
  background: rgba(14, 165, 233, 0.3);
}

.search-results-list {
  padding: 0.5rem 0;
}

.search-result-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  text-align: left;
}

.search-result-item:hover,
.search-result-item.highlighted {
  background: rgba(14, 165, 233, 0.1);
  transform: translateX(4px);
}

.dark .search-result-item:hover,
.dark .search-result-item.highlighted {
  background: rgba(14, 165, 233, 0.2);
}

.result-icon {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-lg);
  background: rgba(14, 165, 233, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  color: var(--color-primary-600);
  transition: all var(--duration-200) var(--ease-out);
}

.search-result-item:hover .result-icon,
.search-result-item.highlighted .result-icon {
  background: var(--color-primary-500);
  color: white;
  transform: scale(1.1);
}

.dark .result-icon {
  background: rgba(14, 165, 233, 0.2);
  color: var(--color-primary-400);
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-900);
  margin-bottom: 0.125rem;
  line-height: 1.4;
}

.result-title :deep(mark) {
  background: rgba(14, 165, 233, 0.2);
  color: var(--color-primary-700);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-semibold);
}

.dark .result-title {
  color: var(--color-neutral-100);
}

.dark .result-title :deep(mark) {
  background: rgba(14, 165, 233, 0.3);
  color: var(--color-primary-300);
}

.result-path {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
  line-height: 1.3;
}

.dark .result-path {
  color: var(--color-neutral-400);
}

.result-badge {
  background: var(--color-error-500);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: 0.125rem 0.5rem;
  border-radius: var(--radius-full);
  margin-left: 0.5rem;
}

.search-shortcuts {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 0.75rem 1rem;
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  background: rgba(248, 250, 252, 0.8);
}

.dark .search-shortcuts {
  border-top-color: rgba(51, 65, 85, 0.6);
  background: rgba(2, 6, 23, 0.8);
}

.shortcut {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
}

.dark .shortcut {
  color: var(--color-neutral-400);
}

.shortcut kbd {
  background: var(--color-neutral-200);
  color: var(--color-neutral-700);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-family: inherit;
  border: 1px solid var(--color-neutral-300);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .shortcut kbd {
  background: var(--color-neutral-700);
  color: var(--color-neutral-200);
  border-color: var(--color-neutral-600);
}

.search-empty {
  padding: 2rem 1rem;
  text-align: center;
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.6;
}

.empty-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-700);
  margin-bottom: 0.25rem;
}

.dark .empty-title {
  color: var(--color-neutral-300);
}

.empty-subtitle {
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
}

.dark .empty-subtitle {
  color: var(--color-neutral-400);
}

/* 动画 */
.search-results-enter-active,
.search-results-leave-active {
  transition: all var(--duration-200) var(--ease-out);
}

.search-results-enter-from {
  opacity: 0;
  transform: translateY(-8px) scale(0.98);
}

.search-results-leave-to {
  opacity: 0;
  transform: translateY(-4px) scale(0.99);
}
</style>
