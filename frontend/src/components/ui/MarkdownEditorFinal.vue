<template>
  <div class="markdown-editor-final">
    <div ref="vditorContainer" class="vditor-wrapper"></div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useThemeStore } from '@/stores/theme'
import Vditor from 'vditor'
import 'vditor/dist/index.css'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: [String, Number],
    default: '600px'
  },
  readonly: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'sv', // 默认分屏预览模式
    validator: (value) => ['wysiwyg', 'ir', 'sv'].includes(value)
  },
  placeholder: {
    type: String,
    default: '请输入内容...'
  },
  autoSave: {
    type: Boolean,
    default: true
  },
  autoSaveKey: {
    type: String,
    default: ''
  },
  showLineNumbers: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'change', 'input'])

// 主题
const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)

// 编辑器实例
const vditorContainer = ref(null)
let vditor = null
const isInitialized = ref(false)

// 内容管理
const content = ref(props.modelValue)

// 初始化编辑器
const initVditor = () => {
  if (!vditorContainer.value) {
    return
  }

  try {
    const config = {
      height: typeof props.height === 'number' ? props.height : parseInt(props.height.toString()),
      mode: 'sv', // 回到分屏模式
      placeholder: props.placeholder,
      theme: isDarkMode.value ? 'dark' : 'classic',
      value: content.value || '',
      width: '100%',
      
      // 最小化配置
      typewriterMode: false,
      debugger: false,
      
      // 工具栏
      toolbar: [
        'headings', 'bold', 'italic', 'strike', '|',
        'list', 'ordered-list', 'check', '|',
        'quote', 'line', 'code', 'table', '|',
        'undo', 'redo', '|',
        'preview', 'both'
      ],

      // 预览配置
      preview: {
        delay: 300,
        mode: 'editor',
        actions: ['desktop', 'tablet', 'mobile'],
        hljs: {
          lineNumber: props.showLineNumbers,
          style: isDarkMode.value ? 'github-dark' : 'github'
        }
      },

      // 上传配置
      upload: {
        handler: () => Promise.resolve('')
      },

      // 计数器
      counter: {
        enable: true,
        type: 'markdown'
      },

      // 缓存
      cache: {
        enable: false
      },

      // 输入回调
      input: (value) => {
        content.value = value
        emit('update:modelValue', value)
        emit('input', value)
        emit('change', value)
      },

      // 失焦回调
      blur: () => {
        // 不在blur时保存
      },

      // 初始化完成回调
      after: () => {
        isInitialized.value = true

        if (props.readonly) {
          vditor?.disabled()
        }

        // 简单的换行修复方法
        if (vditor && vditor.vditor) {
          setTimeout(() => {
            const container = vditor.vditor.element
            if (container) {
              // 查找分屏模式下的textarea
              const svTextarea = container.querySelector('.vditor-sv__editor textarea')
              if (svTextarea) {
                // 设置基本样式
                svTextarea.style.whiteSpace = 'pre-wrap'
                svTextarea.style.wordWrap = 'break-word'
                svTextarea.style.overflowWrap = 'break-word'
                
                // 监听input事件，在输入后清理重复的markdown标签
                svTextarea.addEventListener('input', () => {
                  const currentValue = svTextarea.value
                  let cleanedValue = currentValue
                  
                  // 查找并清理重复的markdown标题标签
                  const lines = currentValue.split('\n')
                  let modified = false
                  
                  for (let i = 0; i < lines.length; i++) {
                    const line = lines[i]
                    // 检查是否有重复的标题标签（如 "## ## 标题"）
                    const duplicateHeaderMatch = line.match(/^(#+)\s+\1\s+(.*)$/)
                    if (duplicateHeaderMatch) {
                      lines[i] = duplicateHeaderMatch[1] + ' ' + duplicateHeaderMatch[2]
                      modified = true
                    }
                    
                    // 检查连续两行都有相同标题标签的情况
                    if (i > 0 && lines[i-1].match(/^#+\s*$/) && line.match(/^#+\s/)) {
                      lines[i] = line.replace(/^#+\s/, '')
                      modified = true
                    }
                  }
                  
                  if (modified) {
                    const cursorPos = svTextarea.selectionStart
                    svTextarea.value = lines.join('\n')
                    svTextarea.selectionStart = svTextarea.selectionEnd = Math.min(cursorPos, svTextarea.value.length)
                    
                    // 触发input事件通知Vue
                    const inputEvent = new Event('input', { bubbles: true })
                    svTextarea.dispatchEvent(inputEvent)
                  }
                }, { passive: false })
              }

              // 处理其他编辑器元素
              const allEditableElements = container.querySelectorAll('textarea, [contenteditable="true"]')
              allEditableElements.forEach(element => {
                element.style.whiteSpace = 'pre-wrap'
                element.style.wordWrap = 'break-word'
                element.style.overflowWrap = 'break-word'
              })
            }
          }, 100)
        }
      }
    }

    vditor = new Vditor(vditorContainer.value, config)
  } catch (error) {
    console.error('Vditor initialization error:', error)
  }
}

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value && vditor && isInitialized.value) {
    content.value = newValue
    vditor.setValue(newValue || '')
  }
})

// 监听只读状态
watch(() => props.readonly, (readonly) => {
  if (vditor && isInitialized.value) {
    if (readonly) {
      vditor.disabled()
    } else {
      vditor.enable()
    }
  }
})

// 监听主题变化
watch(isDarkMode, (dark) => {
  if (vditor && isInitialized.value) {
    vditor.setTheme(dark ? 'dark' : 'classic')
  }
})

// 公开方法
const getValue = () => vditor?.getValue() || ''
const setValue = (value) => vditor?.setValue(value || '')
const insertValue = (value) => vditor?.insertValue(value)
const focus = () => vditor?.focus()
const blur = () => vditor?.blur()
const disabled = () => vditor?.disabled()
const enable = () => vditor?.enable()

// 暴露方法给父组件
defineExpose({
  getValue,
  setValue,
  insertValue,
  focus,
  blur,
  disabled,
  enable,
  getVditor: () => vditor
})

onMounted(() => {
  nextTick(() => {
    setTimeout(initVditor, 100) // 延迟初始化确保DOM完全准备好
  })
})

onBeforeUnmount(() => {
  if (vditor) {
    try {
      vditor.destroy()
    } catch (error) {
      // 静默处理销毁错误
    }
    vditor = null
  }
})
</script>

<style>
/* 全局样式，不使用scoped */
.markdown-editor-final {
  width: 100%;
  height: 100%;
}

.vditor-wrapper {
  width: 100%;
  height: 100%;
}

/* 确保编辑器正确显示 */
.vditor {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.dark .vditor {
  border-color: #374151;
  background-color: #1f2937;
}

/* 关键修复：确保SV模式编辑器换行功能正常 */
.vditor-sv__editor textarea {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  line-height: 1.6 !important;
}

/* 确保所有编辑器元素都支持换行 */
.vditor textarea,
.vditor [contenteditable="true"] {
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  line-height: 1.6 !important;
}

/* 分屏预览样式 */
.vditor-sv {
  display: flex !important;
}

.vditor-sv .vditor-sv__editor {
  flex: 1;
  min-width: 50%;
}

.vditor-sv .vditor-sv__preview {
  flex: 1;
  min-width: 50%;
  border-left: 1px solid #e5e7eb;
}

.dark .vditor-sv .vditor-sv__preview {
  border-left-color: #4b5563;
}
</style>
