<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="handleClose"></div>

      <!-- 模态框内容 -->
      <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
        <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <!-- 操作图标 -->
            <div :class="[
              'mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10',
              operationIconClass
            ]">
              <div v-html="operationIcon()" class="h-6 w-6"></div>
            </div>
            
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
              <!-- 标题 -->
              <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                {{ computedTitle }}
              </h3>
              
              <!-- 描述信息 -->
              <div class="mt-2">
                <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                  {{ computedMessage }}
                </p>
                
                <!-- 选中项目信息 -->
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 mb-3">
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                      已选择 {{ selectedItems.length }} 项
                    </span>
                    <button
                      v-if="!showDetails"
                      @click="showDetails = true"
                      class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                    >
                      查看详情
                    </button>
                    <button
                      v-else
                      @click="showDetails = false"
                      class="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300"
                    >
                      收起详情
                    </button>
                  </div>
                  
                  <!-- 项目详情列表 -->
                  <div v-if="showDetails" class="max-h-32 overflow-y-auto">
                    <div
                      v-for="(item, index) in selectedItems"
                      :key="item.id || index"
                      class="flex items-center justify-between py-1 text-xs"
                    >
                      <span class="text-gray-600 dark:text-gray-400 truncate">
                        {{ getItemDisplayName(item) }}
                      </span>
                      <span class="text-gray-500 dark:text-gray-500 ml-2">
                        #{{ item.id }}
                      </span>
                    </div>
                  </div>
                </div>
                
                <!-- 操作进度 -->
                <div v-if="isProcessing" class="mb-3">
                  <div class="flex items-center justify-between mb-1">
                    <span class="text-sm text-gray-600 dark:text-gray-400">处理进度</span>
                    <span class="text-sm text-gray-600 dark:text-gray-400">
                      {{ processedCount }}/{{ selectedItems.length }}
                    </span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                    <div
                      class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${progressPercentage}%` }"
                    ></div>
                  </div>
                </div>
                
                <!-- 操作结果 -->
                <div v-if="operationResult" class="mb-3">
                  <div v-if="operationResult.success > 0" class="text-sm text-green-600 dark:text-green-400 mb-1">
                    ✅ 成功处理 {{ operationResult.success }} 项
                  </div>
                  <div v-if="operationResult.failed > 0" class="text-sm text-red-600 dark:text-red-400 mb-1">
                    ❌ 失败 {{ operationResult.failed }} 项
                  </div>
                  <div v-if="operationResult.errors.length > 0" class="text-xs text-gray-500 dark:text-gray-400">
                    <details>
                      <summary class="cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
                        查看错误详情
                      </summary>
                      <div class="mt-1 max-h-20 overflow-y-auto">
                        <div v-for="error in operationResult.errors" :key="error.id" class="py-1">
                          {{ error.name }}: {{ error.message }}
                        </div>
                      </div>
                    </details>
                  </div>
                </div>
                
                <!-- 警告信息 -->
                <div v-if="warningMessage" class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-3 mb-3">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        {{ warningMessage }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            v-if="!operationResult"
            type="button"
            @click="handleConfirm"
            :disabled="isProcessing"
            :class="[
              'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed',
              operationButtonClass
            ]"
          >
            {{ isProcessing ? '处理中...' : confirmText }}
          </button>
          
          <button
            v-if="operationResult"
            type="button"
            @click="handleClose"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
          >
            完成
          </button>
          
          <button
            v-if="!operationResult"
            type="button"
            @click="handleClose"
            :disabled="isProcessing"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {{ cancelText }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  operation: {
    type: String,
    default: 'delete',
    validator: (value) => ['delete', 'edit', 'export', 'archive', 'activate', 'deactivate'].includes(value)
  },
  selectedItems: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: ''
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  warningMessage: {
    type: String,
    default: ''
  },
  itemNameField: {
    type: String,
    default: 'name'
  }
})

const emit = defineEmits(['close', 'confirm', 'progress', 'complete'])

// 状态管理
const showDetails = ref(false)
const isProcessing = ref(false)
const processedCount = ref(0)
const operationResult = ref(null)

// 操作配置
const operationConfig = {
  delete: {
    title: '批量删除',
    message: '确定要删除选中的项目吗？此操作不可撤销。',
    confirmText: '删除',
    icon: 'TrashIcon',
    iconClass: 'bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400',
    buttonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
  },
  edit: {
    title: '批量编辑',
    message: '确定要编辑选中的项目吗？',
    confirmText: '编辑',
    icon: 'PencilIcon',
    iconClass: 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400',
    buttonClass: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
  },
  export: {
    title: '批量导出',
    message: '确定要导出选中的项目吗？',
    confirmText: '导出',
    icon: 'DownloadIcon',
    iconClass: 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400',
    buttonClass: 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
  },
  archive: {
    title: '批量归档',
    message: '确定要归档选中的项目吗？',
    confirmText: '归档',
    icon: 'ArchiveIcon',
    iconClass: 'bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400',
    buttonClass: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
  }
}

// 计算属性
const config = computed(() => operationConfig[props.operation] || operationConfig.delete)

const computedTitle = computed(() => props.title || config.value.title)
const computedMessage = computed(() => props.message || config.value.message)
const confirmText = computed(() => props.confirmText || config.value.confirmText)

const operationIcon = computed(() => {
  const iconMap = {
    TrashIcon: () => `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>`,
    PencilIcon: () => `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path></svg>`,
    DownloadIcon: () => `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>`,
    ArchiveIcon: () => `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8l6 6 6-6"></path></svg>`
  }
  return iconMap[config.value.icon] || iconMap.TrashIcon
})

const operationIconClass = computed(() => config.value.iconClass)
const operationButtonClass = computed(() => config.value.buttonClass)

const progressPercentage = computed(() => {
  if (props.selectedItems.length === 0) return 0
  return Math.round((processedCount.value / props.selectedItems.length) * 100)
})

// 方法
const getItemDisplayName = (item) => {
  return item[props.itemNameField] || item.title || item.label || `项目 ${item.id}`
}

const handleClose = () => {
  if (!isProcessing.value) {
    emit('close')
    resetState()
  }
}

const handleConfirm = () => {
  isProcessing.value = true
  processedCount.value = 0
  operationResult.value = null
  emit('confirm', {
    operation: props.operation,
    items: props.selectedItems,
    onProgress: updateProgress,
    onComplete: handleComplete
  })
}

const updateProgress = (processed, total) => {
  processedCount.value = processed
  emit('progress', { processed, total })
}

const handleComplete = (result) => {
  isProcessing.value = false
  operationResult.value = result
  emit('complete', result)
}

const resetState = () => {
  showDetails.value = false
  isProcessing.value = false
  processedCount.value = 0
  operationResult.value = null
}

// 监听show变化，重置状态
watch(() => props.show, (newShow) => {
  if (newShow) {
    resetState()
  }
})
</script>
