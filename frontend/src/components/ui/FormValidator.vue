<template>
  <div class="form-validator">
    <slot :validate="validate" :errors="errors" :isValid="isValid" :reset="reset" />
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  rules: {
    type: Object,
    default: () => ({})
  },
  modelValue: {
    type: Object,
    default: () => ({})
  },
  validateOnChange: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'validate'])

const errors = ref({})
const touched = ref({})

// 计算是否有效
const isValid = computed(() => {
  return Object.keys(errors.value).length === 0
})

// 验证规则
const validators = {
  required: (value, message = '此字段为必填项') => {
    if (value === null || value === undefined || value === '') {
      return message
    }
    return null
  },
  
  minLength: (value, min, message) => {
    if (value && value.length < min) {
      return message || `最少需要${min}个字符`
    }
    return null
  },
  
  maxLength: (value, max, message) => {
    if (value && value.length > max) {
      return message || `最多允许${max}个字符`
    }
    return null
  },
  
  email: (value, message = '请输入有效的邮箱地址') => {
    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return message
    }
    return null
  },
  
  url: (value, message = '请输入有效的URL地址') => {
    if (value && !/^https?:\/\/.+/.test(value)) {
      return message
    }
    return null
  },
  
  pattern: (value, pattern, message = '格式不正确') => {
    if (value && !pattern.test(value)) {
      return message
    }
    return null
  },
  
  custom: (value, validator, message) => {
    if (typeof validator === 'function') {
      const result = validator(value)
      if (result !== true) {
        return message || result || '验证失败'
      }
    }
    return null
  }
}

// 验证单个字段
const validateField = (fieldName, value) => {
  const fieldRules = props.rules[fieldName]
  if (!fieldRules) return null

  const fieldErrors = []

  // 处理规则数组
  const rules = Array.isArray(fieldRules) ? fieldRules : [fieldRules]

  for (const rule of rules) {
    if (typeof rule === 'string') {
      // 简单规则：'required'
      const error = validators[rule]?.(value)
      if (error) fieldErrors.push(error)
    } else if (typeof rule === 'object') {
      // 复杂规则：{ type: 'required', message: '自定义消息' }
      const { type, message, ...params } = rule
      
      if (validators[type]) {
        let error = null
        
        switch (type) {
          case 'minLength':
            error = validators[type](value, params.min || params.value, message)
            break
          case 'maxLength':
            error = validators[type](value, params.max || params.value, message)
            break
          case 'pattern':
            error = validators[type](value, params.pattern || params.value, message)
            break
          case 'custom':
            error = validators[type](value, params.validator || params.value, message)
            break
          default:
            error = validators[type](value, message)
        }
        
        if (error) fieldErrors.push(error)
      }
    } else if (typeof rule === 'function') {
      // 函数规则
      const result = rule(value, props.modelValue)
      if (result !== true) {
        fieldErrors.push(typeof result === 'string' ? result : '验证失败')
      }
    }
  }

  return fieldErrors.length > 0 ? fieldErrors[0] : null
}

// 验证所有字段
const validate = (data = props.modelValue) => {
  const newErrors = {}
  
  Object.keys(props.rules).forEach(fieldName => {
    const error = validateField(fieldName, data[fieldName])
    if (error) {
      newErrors[fieldName] = error
    }
  })
  
  errors.value = newErrors
  
  const valid = Object.keys(newErrors).length === 0
  emit('validate', { valid, errors: newErrors })
  
  return valid
}

// 重置验证状态
const reset = () => {
  errors.value = {}
  touched.value = {}
}

// 标记字段为已触摸
const touch = (fieldName) => {
  touched.value[fieldName] = true
}

// 监听数据变化
watch(() => props.modelValue, (newValue, oldValue) => {
  if (props.validateOnChange && oldValue) {
    // 只验证已更改的字段
    Object.keys(newValue).forEach(fieldName => {
      if (newValue[fieldName] !== oldValue[fieldName] && touched.value[fieldName]) {
        const error = validateField(fieldName, newValue[fieldName])
        if (error) {
          errors.value[fieldName] = error
        } else {
          delete errors.value[fieldName]
        }
      }
    })
  }
}, { deep: true })

// 暴露方法
defineExpose({
  validate,
  reset,
  touch,
  errors: computed(() => errors.value),
  isValid
})
</script>

<style scoped>
.form-validator {
  width: 100%;
}
</style>
