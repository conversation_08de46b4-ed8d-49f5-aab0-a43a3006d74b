<template>
  <div
    :class="[
      'accessible-menu-item',
      {
        'is-active': isActive,
        'is-disabled': disabled,
        'has-submenu': hasSubmenu,
        'is-expanded': isExpanded
      }
    ]"
    :role="role"
    :aria-expanded="hasSubmenu ? isExpanded : undefined"
    :aria-haspopup="hasSubmenu ? 'menu' : undefined"
    :aria-disabled="disabled"
    :aria-current="isActive ? 'page' : undefined"
  >
    <button
      ref="buttonRef"
      :id="buttonId"
      :class="[
        'menu-item-button',
        {
          'focus-visible': showFocusRing
        }
      ]"
      :tabindex="tabIndex"
      :aria-label="ariaLabel"
      :aria-describedby="ariaDescribedBy"
      :aria-expanded="hasSubmenu ? isExpanded : undefined"
      :aria-controls="hasSubmenu ? submenuId : undefined"
      :disabled="disabled"
      @click="handleClick"
      @keydown="handleKeyDown"
      @focus="handleFocus"
      @blur="handleBlur"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    >
      <!-- 图标 -->
      <span class="menu-item-icon" :aria-hidden="true">
        <MenuIcons :name="icon" :size="iconSize" />
      </span>
      
      <!-- 文本内容 -->
      <span class="menu-item-content">
        <span class="menu-item-label">{{ label }}</span>
        <span v-if="description" class="menu-item-description">{{ description }}</span>
      </span>
      
      <!-- 快捷键 -->
      <span v-if="shortcut" class="menu-item-shortcut" :aria-label="`快捷键 ${shortcut}`">
        <kbd v-for="key in shortcutKeys" :key="key">{{ key }}</kbd>
      </span>
      
      <!-- 徽章 -->
      <span
        v-if="badge"
        class="menu-item-badge"
        :aria-label="`${badge} 个通知`"
        role="status"
      >
        {{ badge }}
      </span>
      
      <!-- 子菜单指示器 -->
      <span
        v-if="hasSubmenu"
        class="submenu-indicator"
        :aria-hidden="true"
      >
        <MenuIcons
          :name="isExpanded ? 'chevronDown' : 'chevronRight'"
          :size="14"
        />
      </span>
    </button>
    
    <!-- 子菜单 -->
    <Transition name="submenu" @enter="onSubmenuEnter" @leave="onSubmenuLeave">
      <div
        v-if="hasSubmenu && isExpanded"
        :id="submenuId"
        class="submenu"
        role="menu"
        :aria-labelledby="buttonId"
        @keydown="handleSubmenuKeyDown"
      >
        <slot name="submenu" />
      </div>
    </Transition>
    
    <!-- 隐藏的描述文本 -->
    <div
      v-if="description"
      :id="descriptionId"
      class="sr-only"
    >
      {{ description }}
    </div>
    
    <!-- 状态公告 -->
    <div
      v-if="statusMessage"
      :id="statusId"
      class="sr-only"
      role="status"
      aria-live="polite"
    >
      {{ statusMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAccessibility } from '@/composables/useAccessibility'
import MenuIcons from './icons/MenuIcons.vue'

const props = defineProps({
  id: {
    type: String,
    default: () => `menu-item-${Date.now()}`
  },
  label: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  icon: {
    type: String,
    required: true
  },
  iconSize: {
    type: Number,
    default: 20
  },
  route: {
    type: String,
    default: ''
  },
  isActive: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  badge: {
    type: [String, Number],
    default: null
  },
  shortcut: {
    type: String,
    default: ''
  },
  hasSubmenu: {
    type: Boolean,
    default: false
  },
  tabIndex: {
    type: Number,
    default: 0
  },
  role: {
    type: String,
    default: 'menuitem'
  }
})

const emit = defineEmits([
  'click',
  'focus',
  'blur',
  'expand',
  'collapse',
  'submenu-focus'
])

const router = useRouter()
const {
  announceToScreenReader,
  createFocusTrap,
  setupKeyboardNavigation,
  focusVisible
} = useAccessibility()

// 响应式状态
const buttonRef = ref(null)
const isExpanded = ref(false)
const isFocused = ref(false)
const isHovered = ref(false)
const statusMessage = ref('')
const showFocusRing = ref(false)

// 计算属性
const buttonId = computed(() => `${props.id}-button`)
const submenuId = computed(() => `${props.id}-submenu`)
const descriptionId = computed(() => `${props.id}-desc`)
const statusId = computed(() => `${props.id}-status`)

const ariaLabel = computed(() => {
  let label = props.label
  if (props.badge) {
    label += `, ${props.badge} 个通知`
  }
  if (props.shortcut) {
    label += `, 快捷键 ${props.shortcut}`
  }
  if (props.hasSubmenu) {
    label += `, 有子菜单`
  }
  if (props.disabled) {
    label += `, 已禁用`
  }
  return label
})

const ariaDescribedBy = computed(() => {
  const ids = []
  if (props.description) ids.push(descriptionId.value)
  if (statusMessage.value) ids.push(statusId.value)
  return ids.length > 0 ? ids.join(' ') : undefined
})

const shortcutKeys = computed(() => {
  if (!props.shortcut) return []
  return props.shortcut.split('+').map(key => key.trim())
})

// 事件处理
const handleClick = (event) => {
  if (props.disabled) return
  
  if (props.hasSubmenu) {
    toggleSubmenu()
  } else {
    if (props.route) {
      router.push(props.route)
      announceToScreenReader(`导航到 ${props.label}`)
    }
    emit('click', event)
  }
}

const handleKeyDown = (event) => {
  if (props.disabled) return
  
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      handleClick(event)
      break
      
    case 'ArrowRight':
      if (props.hasSubmenu && !isExpanded.value) {
        event.preventDefault()
        expandSubmenu()
      }
      break
      
    case 'ArrowLeft':
      if (props.hasSubmenu && isExpanded.value) {
        event.preventDefault()
        collapseSubmenu()
      }
      break
      
    case 'ArrowDown':
      if (props.hasSubmenu) {
        event.preventDefault()
        if (!isExpanded.value) {
          expandSubmenu()
        }
        // 聚焦到第一个子菜单项
        nextTick(() => {
          const firstSubmenuItem = document.querySelector(`#${submenuId.value} [role="menuitem"]`)
          if (firstSubmenuItem) {
            firstSubmenuItem.focus()
          }
        })
      }
      break
      
    case 'Escape':
      if (isExpanded.value) {
        event.preventDefault()
        collapseSubmenu()
        buttonRef.value?.focus()
      }
      break
  }
}

const handleFocus = (event) => {
  isFocused.value = true
  showFocusRing.value = focusVisible.value
  emit('focus', event)
  
  // 公告当前聚焦的项目
  if (props.description) {
    announceToScreenReader(`${props.label}, ${props.description}`)
  }
}

const handleBlur = (event) => {
  isFocused.value = false
  showFocusRing.value = false
  emit('blur', event)
}

const handleMouseEnter = () => {
  isHovered.value = true
  if (props.hasSubmenu) {
    // 鼠标悬停时延迟展开子菜单
    setTimeout(() => {
      if (isHovered.value) {
        expandSubmenu()
      }
    }, 300)
  }
}

const handleMouseLeave = () => {
  isHovered.value = false
  if (props.hasSubmenu) {
    // 鼠标离开时延迟收起子菜单
    setTimeout(() => {
      if (!isHovered.value && !isFocused.value) {
        collapseSubmenu()
      }
    }, 300)
  }
}

const handleSubmenuKeyDown = (event) => {
  if (event.key === 'Escape') {
    event.preventDefault()
    collapseSubmenu()
    buttonRef.value?.focus()
  }
}

// 子菜单管理
const toggleSubmenu = () => {
  if (isExpanded.value) {
    collapseSubmenu()
  } else {
    expandSubmenu()
  }
}

const expandSubmenu = () => {
  if (!props.hasSubmenu || isExpanded.value) return
  
  isExpanded.value = true
  emit('expand')
  announceToScreenReader(`${props.label} 子菜单已展开`)
  
  // 设置焦点陷阱
  nextTick(() => {
    const submenu = document.getElementById(submenuId.value)
    if (submenu) {
      setupKeyboardNavigation(submenu, {
        orientation: 'vertical',
        wrap: true,
        homeEndKeys: true
      })
    }
  })
}

const collapseSubmenu = () => {
  if (!isExpanded.value) return
  
  isExpanded.value = false
  emit('collapse')
  announceToScreenReader(`${props.label} 子菜单已收起`)
}

// 动画回调
const onSubmenuEnter = (el) => {
  el.style.height = '0'
  el.offsetHeight // 强制重排
  el.style.height = el.scrollHeight + 'px'
}

const onSubmenuLeave = (el) => {
  el.style.height = el.scrollHeight + 'px'
  el.offsetHeight // 强制重排
  el.style.height = '0'
}

// 状态更新
const updateStatus = (message) => {
  statusMessage.value = message
  setTimeout(() => {
    statusMessage.value = ''
  }, 3000)
}

// 生命周期
onMounted(() => {
  // 如果是活动项目，公告当前页面
  if (props.isActive) {
    announceToScreenReader(`当前页面: ${props.label}`)
  }
})

// 暴露方法
defineExpose({
  focus: () => buttonRef.value?.focus(),
  blur: () => buttonRef.value?.blur(),
  expandSubmenu,
  collapseSubmenu,
  updateStatus
})
</script>

<style scoped>
.accessible-menu-item {
  position: relative;
}

.menu-item-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  background: transparent;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  color: var(--color-neutral-700);
  font-size: var(--font-size-sm);
  text-align: left;
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
  position: relative;
}

.menu-item-button:hover:not(:disabled) {
  background: var(--color-neutral-100);
  border-color: var(--color-neutral-200);
  transform: translateX(2px);
}

.menu-item-button:focus {
  outline: none;
}

.menu-item-button.focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.menu-item-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.accessible-menu-item.is-active .menu-item-button {
  background: linear-gradient(135deg, 
    var(--color-primary-500) 0%, 
    var(--color-secondary-500) 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

.dark .menu-item-button {
  color: var(--color-neutral-300);
}

.dark .menu-item-button:hover:not(:disabled) {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.menu-item-icon {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-lg);
  background: var(--color-neutral-100);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-neutral-600);
  margin-right: 0.75rem;
  flex-shrink: 0;
  transition: all var(--duration-200) var(--ease-out);
}

.accessible-menu-item.is-active .menu-item-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.dark .menu-item-icon {
  background: var(--color-neutral-800);
  color: var(--color-neutral-400);
}

.menu-item-content {
  flex: 1;
  min-width: 0;
}

.menu-item-label {
  display: block;
  font-weight: 500;
  line-height: 1.3;
}

.menu-item-description {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--color-neutral-500);
  line-height: 1.3;
  margin-top: 0.125rem;
}

.accessible-menu-item.is-active .menu-item-description {
  color: rgba(255, 255, 255, 0.8);
}

.dark .menu-item-description {
  color: var(--color-neutral-400);
}

.menu-item-shortcut {
  display: flex;
  gap: 0.25rem;
  margin-left: 0.5rem;
}

.menu-item-shortcut kbd {
  background: var(--color-neutral-200);
  color: var(--color-neutral-700);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-family: inherit;
  border: 1px solid var(--color-neutral-300);
}

.accessible-menu-item.is-active .menu-item-shortcut kbd {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.dark .menu-item-shortcut kbd {
  background: var(--color-neutral-700);
  color: var(--color-neutral-200);
  border-color: var(--color-neutral-600);
}

.menu-item-badge {
  background: var(--color-error-500);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-full);
  margin-left: 0.5rem;
  min-width: 1.25rem;
  text-align: center;
}

.submenu-indicator {
  margin-left: 0.5rem;
  color: var(--color-neutral-500);
  transition: transform var(--duration-200) var(--ease-out);
}

.accessible-menu-item.is-expanded .submenu-indicator {
  transform: rotate(90deg);
}

.submenu {
  margin-top: 0.5rem;
  margin-left: 2.75rem;
  padding: 0.5rem 0;
  background: var(--color-neutral-50);
  border: 1px solid var(--color-neutral-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: height var(--duration-300) var(--ease-out);
}

.dark .submenu {
  background: var(--color-neutral-800);
  border-color: var(--color-neutral-600);
}

.submenu-enter-active,
.submenu-leave-active {
  transition: all var(--duration-300) var(--ease-out);
}

.submenu-enter-from,
.submenu-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .menu-item-button {
    border: 2px solid var(--color-neutral-400);
  }
  
  .menu-item-button:focus {
    border-color: var(--color-primary-600);
    outline: 3px solid var(--color-primary-600);
  }
  
  .accessible-menu-item.is-active .menu-item-button {
    border: 2px solid white;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .menu-item-button,
  .menu-item-icon,
  .submenu-indicator,
  .submenu {
    transition: none !important;
  }
  
  .submenu-enter-active,
  .submenu-leave-active {
    transition: none !important;
  }
}
</style>
