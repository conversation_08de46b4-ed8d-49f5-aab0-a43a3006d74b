<template>
  <div 
    class="enhanced-menu-item"
    :class="{
      'dragging': isDragging,
      'drag-over': isDragOver,
      'has-context': hasContextMenu
    }"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
    @drop="handleDrop"
    @contextmenu="handleContextMenu"
    :draggable="draggable"
  >
    <SidebarMenuButton
      :is-active="isActive"
      @click="handleClick"
      @mousedown="handleMouseDown"
      @mouseup="handleMouseUp"
      @keydown="handleKeyDown"
      :title="title"
      :class="[
        'sidebar-menu-button-premium enhanced-menu-button',
        {
          'loading': isLoading,
          'success': showSuccess,
          'error': showError,
          'long-pressing': isLongPressing,
          'search-highlighted': isHighlighted,
          'has-context': hasContextMenu
        }
      ]"
      :tabindex="tabIndex"
      :aria-label="ariaLabel"
      :aria-describedby="ariaDescribedBy"
    >
      <MenuIcons :name="icon" class="sidebar-menu-icon" />
      <span class="sidebar-menu-text">{{ label }}</span>
      
      <!-- 徽章 -->
      <div 
        v-if="badge" 
        class="sidebar-menu-badge"
        :class="{ 'new': isNewBadge }"
        :style="{ backgroundColor: badgeColor }"
      >
        {{ badge }}
      </div>
      
      <!-- 快捷键提示 -->
      <div v-if="shortcut && !isCollapsed" class="menu-shortcut">
        <kbd v-for="key in shortcutKeys" :key="key">{{ key }}</kbd>
      </div>
    </SidebarMenuButton>

    <!-- 上下文菜单 -->
    <Transition name="context-menu">
      <div 
        v-if="showContextMenu" 
        class="context-menu"
        :style="contextMenuStyle"
        @click.stop
      >
        <div class="context-menu-item" @click="handleFavorite">
          <MenuIcons :name="isFavorite ? 'star' : 'star'" :size="14" />
          <span>{{ isFavorite ? '取消收藏' : '添加收藏' }}</span>
        </div>
        <div class="context-menu-item" @click="handleOpenInNewTab">
          <MenuIcons name="external" :size="14" />
          <span>新标签页打开</span>
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item" @click="handleCopyLink">
          <MenuIcons name="copy" :size="14" />
          <span>复制链接</span>
        </div>
      </div>
    </Transition>

    <!-- Tooltip -->
    <Transition name="tooltip">
      <div 
        v-if="showTooltip && isCollapsed" 
        class="menu-tooltip"
        :style="tooltipStyle"
      >
        <div class="tooltip-content">
          <div class="tooltip-title">{{ label }}</div>
          <div v-if="description" class="tooltip-description">{{ description }}</div>
          <div v-if="shortcut" class="tooltip-shortcut">
            <span>快捷键:</span>
            <kbd v-for="key in shortcutKeys" :key="key">{{ key }}</kbd>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { SidebarMenuButton } from '@/components/ui/sidebar'
import { useSidebar } from '@/components/ui/sidebar'
import MenuIcons from './icons/MenuIcons.vue'

const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  route: {
    type: String,
    default: ''
  },
  isActive: {
    type: Boolean,
    default: false
  },
  badge: {
    type: [String, Number],
    default: null
  },
  badgeColor: {
    type: String,
    default: '#ef4444'
  },
  shortcut: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  draggable: {
    type: Boolean,
    default: false
  },
  hasContextMenu: {
    type: Boolean,
    default: true
  },
  tabIndex: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits([
  'click', 
  'long-press', 
  'double-click', 
  'drag-start', 
  'drag-end', 
  'context-menu',
  'favorite-toggle',
  'copy-link'
])

const router = useRouter()
const { isCollapsed } = useSidebar()

// 状态管理
const isLoading = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const isLongPressing = ref(false)
const isHighlighted = ref(false)
const isDragging = ref(false)
const isDragOver = ref(false)
const showContextMenu = ref(false)
const showTooltip = ref(false)
const isNewBadge = ref(false)
const isFavorite = ref(false)

// 长按相关
let longPressTimer = null
const longPressDelay = 500

// 上下文菜单位置
const contextMenuStyle = ref({})
const tooltipStyle = ref({})

// 计算属性
const title = computed(() => props.label)
const ariaLabel = computed(() => `${props.label}${props.badge ? ` (${props.badge} 个通知)` : ''}`)
const ariaDescribedBy = computed(() => props.description ? `desc-${props.label}` : null)

const shortcutKeys = computed(() => {
  if (!props.shortcut) return []
  return props.shortcut.split('+').map(key => key.trim())
})

// 事件处理
const handleClick = (event) => {
  if (isLoading.value) return
  
  clearLongPressTimer()
  
  // 显示加载状态
  isLoading.value = true
  
  setTimeout(() => {
    isLoading.value = false
    showSuccess.value = true
    setTimeout(() => {
      showSuccess.value = false
    }, 1000)
  }, 300)
  
  emit('click', event)
  
  if (props.route) {
    router.push(props.route)
  }
}

const handleMouseDown = (event) => {
  if (event.button === 0) { // 左键
    startLongPress()
  }
}

const handleMouseUp = () => {
  clearLongPressTimer()
  isLongPressing.value = false
}

const handleKeyDown = (event) => {
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      handleClick(event)
      break
    case 'F2':
      event.preventDefault()
      // 重命名功能
      break
    case 'Delete':
      event.preventDefault()
      // 删除功能
      break
  }
}

const startLongPress = () => {
  longPressTimer = setTimeout(() => {
    isLongPressing.value = true
    emit('long-press')
    
    // 触觉反馈 (如果支持)
    if (navigator.vibrate) {
      navigator.vibrate(50)
    }
  }, longPressDelay)
}

const clearLongPressTimer = () => {
  if (longPressTimer) {
    clearTimeout(longPressTimer)
    longPressTimer = null
  }
}

// 拖拽处理
const handleDragStart = (event) => {
  isDragging.value = true
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('text/plain', props.label)
  emit('drag-start', event)
}

const handleDragEnd = (event) => {
  isDragging.value = false
  emit('drag-end', event)
}

const handleDragOver = (event) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  const data = event.dataTransfer.getData('text/plain')
  console.log('Dropped:', data)
}

// 上下文菜单
const handleContextMenu = (event) => {
  if (!props.hasContextMenu) return
  
  event.preventDefault()
  
  const rect = event.currentTarget.getBoundingClientRect()
  contextMenuStyle.value = {
    top: `${event.clientY - rect.top}px`,
    left: `${event.clientX - rect.left + 10}px`
  }
  
  showContextMenu.value = true
  emit('context-menu', event)
}

const handleFavorite = () => {
  isFavorite.value = !isFavorite.value
  showContextMenu.value = false
  emit('favorite-toggle', isFavorite.value)
}

const handleOpenInNewTab = () => {
  if (props.route) {
    window.open(props.route, '_blank')
  }
  showContextMenu.value = false
}

const handleCopyLink = () => {
  if (props.route) {
    navigator.clipboard.writeText(window.location.origin + props.route)
    showSuccess.value = true
    setTimeout(() => {
      showSuccess.value = false
    }, 1000)
  }
  showContextMenu.value = false
  emit('copy-link')
}

// Tooltip 处理
const handleMouseEnter = () => {
  if (isCollapsed.value) {
    showTooltip.value = true
  }
}

const handleMouseLeave = () => {
  showTooltip.value = false
  showContextMenu.value = false
}

// 搜索高亮
const highlight = () => {
  isHighlighted.value = true
  setTimeout(() => {
    isHighlighted.value = false
  }, 1000)
}

// 徽章动画
const animateBadge = () => {
  isNewBadge.value = true
  setTimeout(() => {
    isNewBadge.value = false
  }, 600)
}

// 全局点击关闭上下文菜单
const handleGlobalClick = (event) => {
  if (!event.target.closest('.context-menu')) {
    showContextMenu.value = false
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
  
  // 如果有新徽章，播放动画
  if (props.badge) {
    nextTick(() => {
      animateBadge()
    })
  }
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
  clearLongPressTimer()
})

// 暴露方法
defineExpose({
  highlight,
  animateBadge
})
</script>

<style scoped>
.enhanced-menu-item {
  position: relative;
}

.menu-shortcut {
  margin-left: auto;
  display: flex;
  gap: 0.25rem;
}

.menu-shortcut kbd {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
  font-size: 0.625rem;
  font-family: inherit;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.context-menu {
  position: absolute;
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 1000;
  min-width: 160px;
  padding: 0.5rem 0;
}

.dark .context-menu {
  background: rgba(15, 23, 42, 0.98);
  border-color: rgba(51, 65, 85, 0.8);
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: var(--font-size-sm);
  color: var(--color-neutral-700);
  cursor: pointer;
  transition: background-color var(--duration-200) var(--ease-out);
}

.context-menu-item:hover {
  background: rgba(14, 165, 233, 0.1);
}

.dark .context-menu-item {
  color: var(--color-neutral-300);
}

.context-menu-divider {
  height: 1px;
  background: rgba(226, 232, 240, 0.6);
  margin: 0.25rem 0;
}

.dark .context-menu-divider {
  background: rgba(51, 65, 85, 0.6);
}

.menu-tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0.5rem;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.75rem;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  z-index: 1000;
  max-width: 200px;
}

.tooltip-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.tooltip-description {
  font-size: var(--font-size-xs);
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

.tooltip-shortcut {
  font-size: var(--font-size-xs);
  opacity: 0.7;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.tooltip-shortcut kbd {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.125rem 0.25rem;
  border-radius: var(--radius-sm);
}

/* 动画 */
.context-menu-enter-active,
.context-menu-leave-active {
  transition: all var(--duration-200) var(--ease-out);
}

.context-menu-enter-from {
  opacity: 0;
  transform: scale(0.95) translateY(-5px);
}

.context-menu-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-5px);
}

.tooltip-enter-active,
.tooltip-leave-active {
  transition: all var(--duration-200) var(--ease-out);
}

.tooltip-enter-from {
  opacity: 0;
  transform: translateY(-50%) translateX(-5px);
}

.tooltip-leave-to {
  opacity: 0;
  transform: translateY(-50%) translateX(-5px);
}
</style>
