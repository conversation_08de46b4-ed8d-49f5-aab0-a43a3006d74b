<template>
  <header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6" style="height: var(--header-height);">
    <div class="flex items-center justify-between h-full">
      <div class="flex items-center space-x-4">
        <SidebarTrigger />
        <nav class="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 leading-none">
          <span class="leading-none">{{ breadcrumbs.join(' / ') }}</span>
        </nav>
      </div>

      <div class="flex items-center space-x-4">
        <button class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
          🔔
        </button>

        <button @click="toggleTheme" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
          {{ isDarkMode ? '☀️' : '🌙' }}
        </button>

        <button @click="showQuickStart = true" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
          📢
        </button>

        <!-- 用户菜单 -->
        <div class="relative" ref="userMenuRef">
          <button
            @click="showUserMenu = !showUserMenu"
            class="flex items-center space-x-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg px-2 py-1 transition-colors"
          >
            <img
              :src="userStore.userAvatar"
              :alt="userStore.userName"
              class="w-8 h-8 rounded-full object-cover"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">{{ userStore.userName }}</span>
            <svg
              class="w-4 h-4 text-gray-400 transition-transform duration-200"
              :class="{ 'rotate-180': showUserMenu }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          <!-- 用户下拉菜单 -->
          <div
            v-if="showUserMenu"
            class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50"
          >
            <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              <p class="text-sm font-medium text-gray-900 dark:text-white">{{ userStore.userName }}</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">@{{ userStore.user?.username }}</p>
            </div>
            <button
              @click="goToSettings"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <span class="mr-2">⚙️</span>
              个人设置
            </button>
            <button
              @click="handleLogout"
              class="w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <span class="mr-2">🚪</span>
              退出登录
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速开始弹窗 -->
    <div v-if="showQuickStart" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 transition-colors duration-200">
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">系统公告</h3>
          <button @click="showQuickStart = false" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
            ❌
          </button>
        </div>

        <div class="p-6">
          <div class="text-center mb-6">
            <div class="text-2xl mb-2">🚀快速开始</div>
            <p class="text-gray-600 dark:text-gray-300">点击右上角 系统公告 可再次查看 | 完整内容更多参考使用文档</p>
          </div>

          <div class="space-y-6">
            <div>
              <h4 class="font-medium mb-2 text-gray-900 dark:text-white">❓FAQ</h4>
              <p class="text-sm mb-4 text-gray-600 dark:text-gray-300">本站是基于官方 Claude Code 接口，无法体验 Claude Code 的 API 调用</p>
            </div>

            <div>
              <h4 class="font-medium mb-2 text-gray-900 dark:text-white">1️⃣安装 Node.js（已安装可跳过）</h4>
              <div class="rounded-lg p-3 text-sm font-mono bg-gray-100 dark:bg-gray-700">
                <p class="text-gray-900 dark:text-gray-300">确保 Node.js 版本 ≥ 18.0</p>
                <p class="text-gray-900 dark:text-gray-300">npm install -g @anthropic-ai/claude-code</p>
                <p class="text-gray-900 dark:text-gray-300">claude --version</p>
              </div>
            </div>

            <div>
              <h4 class="font-medium mb-2 text-gray-900 dark:text-white">2️⃣开始使用</h4>
              <div class="rounded-lg p-3 text-sm font-mono bg-gray-100 dark:bg-gray-700">
                <p class="text-gray-900 dark:text-gray-300">export ANTHROPIC_AUTH_TOKEN=sk-xxx...</p>
                <p class="text-gray-900 dark:text-gray-300">export ANTHROPIC_BASE_URL=https://anyrouter.top</p>
                <p class="text-gray-900 dark:text-gray-300">claude</p>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button @click="showQuickStart = false" class="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-white transition-colors">今日关闭</button>
          <button @click="showQuickStart = false" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">关闭公告</button>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const themeStore = useThemeStore()
const userStore = useUserStore()
const showQuickStart = ref(false)
const showUserMenu = ref(false)
const userMenuRef = ref(null)

const isDarkMode = computed(() => themeStore.isDarkMode)

const toggleTheme = () => {
  themeStore.toggleTheme()
}

// 用户菜单相关方法
const goToSettings = () => {
  showUserMenu.value = false
  router.push('/settings')
}

const handleLogout = () => {
  showUserMenu.value = false
  userStore.logout()
  router.push('/login')
}

// 点击外部关闭用户菜单
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    showUserMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

const breadcrumbs = computed(() => {
  const routeMap = {
    'dashboard': ['首页', '控制台'],
    'users': ['首页', '控制台', '用户管理'],
    'api-tokens': ['首页', '控制台', 'API令牌'],
    'usage-logs': ['首页', '控制台', '使用日志'],
    'wallet': ['首页', '个人中心', '钱包'],
    'account-bindings': ['首页', '个人中心', '账号绑定'],
    'settings': ['首页', '个人中心', '个人设置'],
    'services': ['首页', 'MCP服务', '服务管理'],
    'categories': ['首页', '成长地图', '分类管理'],
    'tags': ['首页', 'MCP服务', '标签管理'],
    'tasks': ['首页', '定时任务', '任务管理'],
    'logs': ['首页', '定时任务', '执行日志'],
    'dev-tools': ['首页', '成长地图', '研发工具'],
    'prompts': ['首页', '规则中心', 'AI提示词'],
    'content': ['首页', '规范中心', '内容管理'],
    'rss': ['首页', 'AI学习资源', 'RSS管理']
  }
  return routeMap[route.name] || ['首页']
})
</script>
