<template>
  <SidebarProvider>
    <div
      class="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200"
    >
      <!-- 手势区域 -->
      <SidebarGestureArea />

      <!-- 遮罩层 -->
      <SidebarOverlay />

      <div class="flex h-screen">
        <!-- 侧边栏 -->
        <AppSidebar />

        <!-- 主内容区域 -->
        <div class="flex-1 flex flex-col overflow-hidden">
          <!-- 顶部导航栏 -->
          <Header />

          <!-- 页面内容 -->
          <main class="flex-1 overflow-y-auto p-6 bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 transition-colors duration-200">
            <router-view />
          </main>
        </div>
      </div>
    </div>
  </SidebarProvider>
</template>

<script setup>
import { computed } from 'vue'
import { SidebarProvider, SidebarGestureArea, SidebarOverlay } from '@/components/ui/sidebar'
import AppSidebar from './AppSidebar.vue'
import Header from './Header.vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)
</script>
