<template>
  <div class="auth-layout min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 transition-all duration-500">
    <!-- 背景装饰元素 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 动态几何图形 -->
      <div class="absolute top-10 left-10 w-72 h-72 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute bottom-10 right-10 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-indigo-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse delay-500"></div>
      
      <!-- 网格背景 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-5 dark:opacity-10"></div>
    </div>

    <!-- 主内容区域 -->
    <div class="relative z-10 min-h-screen flex items-center justify-center p-4">
      <router-view />
    </div>

    <!-- 底部装饰 -->
    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"></div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

// 添加一些动态效果
onMounted(() => {
  // 可以在这里添加一些页面加载动画
})
</script>

<style scoped>
.auth-layout {
  /* 确保布局占满整个视口 */
  position: relative;
  overflow: hidden;
}

/* 网格背景图案 */
.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}

/* 深色模式下的网格 */
.dark .bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
}

/* 自定义动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .auth-layout .absolute.w-72,
  .auth-layout .absolute.w-96,
  .auth-layout .absolute.w-80 {
    width: 200px;
    height: 200px;
  }
}
</style>
