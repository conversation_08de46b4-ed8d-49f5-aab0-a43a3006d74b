<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- 模态框内容 -->
      <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="handleSubmit">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
                  {{ isEdit ? '编辑用户' : '添加用户' }}
                </h3>
                
                <div class="space-y-4">
                  <!-- 用户名 -->
                  <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      用户名 <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="username"
                      v-model="formData.username"
                      type="text"
                      required
                      :disabled="isEdit"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:bg-gray-100 dark:disabled:bg-gray-600"
                      placeholder="请输入用户名"
                    />
                  </div>

                  <!-- 密码 -->
                  <div v-if="!isEdit">
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      密码 <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="password"
                      v-model="formData.password"
                      type="password"
                      required
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="请输入密码"
                    />
                  </div>

                  <!-- 昵称 -->
                  <div>
                    <label for="nickname" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      昵称
                    </label>
                    <input
                      id="nickname"
                      v-model="formData.nickname"
                      type="text"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="请输入昵称"
                    />
                  </div>

                  <!-- 邮箱 -->
                  <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      邮箱
                    </label>
                    <input
                      id="email"
                      v-model="formData.email"
                      type="email"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="请输入邮箱"
                    />
                  </div>

                  <!-- 手机号 -->
                  <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      手机号
                    </label>
                    <input
                      id="phone"
                      v-model="formData.phone"
                      type="tel"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      placeholder="请输入手机号"
                    />
                  </div>

                  <!-- 状态 -->
                  <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      状态
                    </label>
                    <select
                      id="status"
                      v-model="formData.status"
                      class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option :value="0">正常</option>
                      <option :value="1">停用</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isSubmitting"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ isSubmitting ? '保存中...' : (isEdit ? '更新' : '创建') }}
            </button>
            <button
              type="button"
              @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              取消
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'save'])

// 表单数据
const formData = reactive({
  id: null,
  username: '',
  password: '',
  nickname: '',
  email: '',
  phone: '',
  status: 0
})

const isSubmitting = ref(false)

// 监听用户数据变化，初始化表单
watch(() => props.user, (newUser) => {
  if (newUser) {
    Object.assign(formData, {
      id: newUser.id,
      username: newUser.username || '',
      password: '',
      nickname: newUser.nickname || '',
      email: newUser.email || '',
      phone: newUser.phone || '',
      status: newUser.status ?? 0
    })
  } else {
    // 重置表单
    Object.assign(formData, {
      id: null,
      username: '',
      password: '',
      nickname: '',
      email: '',
      phone: '',
      status: 0
    })
  }
}, { immediate: true })

// 提交表单
const handleSubmit = async () => {
  if (isSubmitting.value) return

  // 基本验证
  if (!formData.username.trim()) {
    alert('请输入用户名')
    return
  }

  if (!props.isEdit && !formData.password.trim()) {
    alert('请输入密码')
    return
  }

  isSubmitting.value = true

  try {
    const submitData = { ...formData }
    
    // 如果是编辑模式且密码为空，则不提交密码字段
    if (props.isEdit && !submitData.password.trim()) {
      delete submitData.password
    }

    emit('save', submitData)
  } finally {
    isSubmitting.value = false
  }
}
</script>
