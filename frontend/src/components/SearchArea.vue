<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
    <div class="flex flex-col lg:flex-row gap-4">
      <!-- 搜索条件区域 -->
      <div v-if="$slots.filters" class="flex flex-col sm:flex-row gap-4 flex-1">
        <slot name="filters" />
      </div>
      
      <!-- 时间选择和按钮区域 -->
      <div class="flex flex-col sm:flex-row gap-4 items-end">
        <!-- 时间选择区域 -->
        <div v-if="$slots.dateFilters" class="flex flex-col sm:flex-row gap-3">
          <slot name="dateFilters" />
        </div>
        
        <!-- 搜索按钮组 -->
        <div class="flex gap-3 mt-4 sm:mt-0">
          <slot name="searchActions" />
        </div>
        
        <!-- 额外操作按钮 -->
        <div v-if="$slots.extraActions" class="flex gap-3">
          <slot name="extraActions" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 这个组件主要用于布局，不需要特殊的 props
</script>
