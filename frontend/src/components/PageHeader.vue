<template>
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
    <!-- 标题区域 -->
    <div class="min-w-0 flex-1">
      <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">{{ title }}</h1>
      <p v-if="description" class="text-gray-600 dark:text-gray-400 mt-1">{{ description }}</p>
    </div>
    
    <!-- 操作按钮区域 -->
    <div v-if="$slots.actions" class="flex flex-col sm:flex-row gap-3 sm:items-center">
      <slot name="actions" />
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  }
})
</script>
