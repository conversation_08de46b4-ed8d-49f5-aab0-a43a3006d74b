<template>
  <div class="flex items-center justify-center space-x-4 py-4">
    <!-- 总数显示 -->
    <div class="text-sm text-gray-600 dark:text-gray-400">
      Total {{ total }}
    </div>

    <!-- 每页条数选择 -->
    <div class="relative">
      <select 
        v-model="currentPageSize" 
        @change="handlePageSizeChange"
        class="appearance-none bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-1 pr-8 text-sm text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
      >
        <option v-for="size in pageSizeOptions" :key="size" :value="size">
          {{ size }}/page
        </option>
      </select>
      <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </div>
    </div>

    <!-- 页码导航 -->
    <div class="flex items-center space-x-1">
      <!-- 上一页 -->
      <button 
        @click="goToPage(currentPage - 1)"
        :disabled="currentPage <= 1"
        class="px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>

      <!-- 页码按钮 -->
      <template v-for="page in visiblePages" :key="page">
        <button
          v-if="page !== '...'"
          @click="goToPage(page)"
          :class="[
            'px-3 py-1 text-sm rounded-md transition-colors',
            page === currentPage
              ? 'bg-blue-500 text-white'
              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white'
          ]"
        >
          {{ page }}
        </button>
        <span v-else class="px-2 py-1 text-sm text-gray-400">...</span>
      </template>

      <!-- 下一页 -->
      <button 
        @click="goToPage(currentPage + 1)"
        :disabled="currentPage >= totalPages"
        class="px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>

    <!-- 跳转到指定页 -->
    <div class="flex items-center space-x-2">
      <span class="text-sm text-gray-600 dark:text-gray-400">Go to</span>
      <input
        v-model="jumpPage"
        @keyup.enter="handleJumpToPage"
        type="number"
        :min="1"
        :max="totalPages"
        class="w-16 px-2 py-1 text-sm text-center border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder="1"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  total: {
    type: Number,
    required: true
  },
  pageSize: {
    type: Number,
    default: 10
  },
  currentPage: {
    type: Number,
    default: 1
  },
  pageSizeOptions: {
    type: Array,
    default: () => [10, 20, 50, 100]
  }
})

const emit = defineEmits(['page-change', 'page-size-change'])

const currentPageSize = ref(props.pageSize)
const jumpPage = ref('')

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(props.total / currentPageSize.value)
})

// 计算可见的页码
const visiblePages = computed(() => {
  const pages = []
  const current = props.currentPage
  const total = totalPages.value

  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总是显示第一页
    pages.push(1)

    if (current <= 4) {
      // 当前页在前面
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    } else if (current >= total - 3) {
      // 当前页在后面
      pages.push('...')
      for (let i = total - 4; i <= total; i++) {
        pages.push(i)
      }
    } else {
      // 当前页在中间
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
      pages.push(total)
    }
  }

  return pages
})

// 跳转到指定页
const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value && page !== props.currentPage) {
    emit('page-change', page)
  }
}

// 处理每页条数变化
const handlePageSizeChange = () => {
  emit('page-size-change', currentPageSize.value)
}

// 处理跳转到指定页
const handleJumpToPage = () => {
  const page = parseInt(jumpPage.value)
  if (page && page >= 1 && page <= totalPages.value) {
    goToPage(page)
    jumpPage.value = ''
  }
}

// 监听 pageSize 属性变化
watch(() => props.pageSize, (newSize) => {
  currentPageSize.value = newSize
})
</script>
