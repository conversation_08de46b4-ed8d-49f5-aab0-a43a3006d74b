<template>
  <div class="space-y-4">
    <!-- Google登录按钮 -->
    <button
      @click="loginWithGoogle"
      :disabled="isLoading"
      class="oauth-button w-full flex items-center justify-center px-4 py-3 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl text-sm font-semibold text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105"
    >
      <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
      </svg>
      <span>{{ isLoading ? '登录中...' : '使用 Google 登录' }}</span>
    </button>

    <!-- GitHub登录按钮 -->
    <button
      @click="loginWithGitHub"
      :disabled="isLoading"
      class="oauth-button w-full flex items-center justify-center px-4 py-3 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl text-sm font-semibold text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500/50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105"
    >
      <svg class="w-5 h-5 mr-3" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
      </svg>
      <span>{{ isLoading ? '登录中...' : '使用 GitHub 登录' }}</span>
    </button>



    <!-- 分割线 -->
    <div class="relative">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span class="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">或使用用户名密码登录</span>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const isLoading = ref(false)

// Google登录
const loginWithGoogle = () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  // 构建OAuth2授权URL
  const authUrl = `http://localhost:8080/oauth2/authorize/google?redirect_uri=${encodeURIComponent(window.location.origin + '/auth/callback')}`
  
  // 打开新窗口进行OAuth2授权
  const popup = window.open(
    authUrl,
    'google-oauth',
    'width=500,height=600,scrollbars=yes,resizable=yes'
  )

  // 监听授权结果
  const checkClosed = setInterval(() => {
    if (popup.closed) {
      clearInterval(checkClosed)
      isLoading.value = false
    }
  }, 1000)

  // 监听来自弹窗的消息
  const messageListener = (event) => {
    if (event.origin !== window.location.origin) return
    
    if (event.data.type === 'OAUTH_SUCCESS') {
      clearInterval(checkClosed)
      popup.close()
      isLoading.value = false

      // 处理登录成功
      handleOAuthSuccess(event.data.token, event.data.refreshToken)

      window.removeEventListener('message', messageListener)
    } else if (event.data.type === 'OAUTH_ERROR') {
      clearInterval(checkClosed)
      popup.close()
      isLoading.value = false
      
      console.error('OAuth登录失败:', event.data.error)
      alert('登录失败: ' + event.data.error)
      
      window.removeEventListener('message', messageListener)
    }
  }

  window.addEventListener('message', messageListener)
}

// GitHub登录
const loginWithGitHub = () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  // 构建OAuth2授权URL
  const authUrl = `http://localhost:8080/oauth2/authorize/github?redirect_uri=${encodeURIComponent(window.location.origin + '/auth/callback')}`
  
  // 打开新窗口进行OAuth2授权
  const popup = window.open(
    authUrl,
    'github-oauth',
    'width=500,height=600,scrollbars=yes,resizable=yes'
  )

  // 监听授权结果
  const checkClosed = setInterval(() => {
    if (popup.closed) {
      clearInterval(checkClosed)
      isLoading.value = false
    }
  }, 1000)

  // 监听来自弹窗的消息
  const messageListener = (event) => {
    if (event.origin !== window.location.origin) return
    
    if (event.data.type === 'OAUTH_SUCCESS') {
      clearInterval(checkClosed)
      popup.close()
      isLoading.value = false

      // 处理登录成功
      handleOAuthSuccess(event.data.token, event.data.refreshToken)

      window.removeEventListener('message', messageListener)
    } else if (event.data.type === 'OAUTH_ERROR') {
      clearInterval(checkClosed)
      popup.close()
      isLoading.value = false
      
      console.error('OAuth登录失败:', event.data.error)
      alert('登录失败: ' + event.data.error)
      
      window.removeEventListener('message', messageListener)
    }
  }

  window.addEventListener('message', messageListener)
}



// 处理OAuth登录成功
const handleOAuthSuccess = async (accessToken, refreshToken) => {
  try {
    // 设置令牌
    userStore.setToken(accessToken)

    // 存储刷新令牌
    if (refreshToken) {
      localStorage.setItem('refreshToken', refreshToken)
    }

    // 获取用户信息
    const fetchSuccess = await userStore.fetchUserInfo()

    if (fetchSuccess) {
      // 跳转到首页
      router.push('/')
    } else {
      throw new Error('获取用户信息失败')
    }
  } catch (error) {
    console.error('OAuth登录后处理失败:', error)
    alert('登录失败: ' + error.message)
  }
}
</script>

<style scoped>
/* OAuth按钮样式 */
.oauth-button {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.oauth-button:hover {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.oauth-button:active {
  transform: scale(0.98);
}

/* 深色模式优化 */
.dark .oauth-button:hover {
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}
</style>
