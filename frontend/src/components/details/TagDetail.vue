<template>
  <div class="tag-detail">
    <div class="form-grid">
      <div class="form-group">
        <label class="form-label">标签名称 *</label>
        <input
          v-model="localData.name"
          type="text"
          class="form-input"
          :class="{ 'error': errors.name }"
          :readonly="mode === 'view'"
          placeholder="请输入标签名称"
        />
        <span v-if="errors.name" class="error-message">{{ errors.name }}</span>
      </div>

      <div class="form-group">
        <label class="form-label">标签颜色</label>
        <div class="color-selector">
          <div
            v-for="color in colorOptions"
            :key="color.value"
            :class="[
              'color-option',
              `bg-${color.value}-500`,
              {
                'selected': localData.color === color.value,
                'pointer-events-none': mode === 'view'
              }
            ]"
            @click="mode !== 'view' && selectColor(color.value)"
            :title="color.label"
          >
            <span v-if="localData.color === color.value" class="color-check">✓</span>
          </div>
        </div>
      </div>

      <div class="form-group full-width">
        <label class="form-label">描述</label>
        <textarea
          v-model="localData.description"
          class="form-textarea"
          :readonly="mode === 'view'"
          rows="3"
          placeholder="请输入标签描述"
        ></textarea>
        <span v-if="errors.description" class="error-message">{{ errors.description }}</span>
      </div>

      <!-- 标签预览 -->
      <div class="form-group full-width">
        <label class="form-label">预览效果</label>
        <div class="tag-preview">
          <span
            :class="[
              'preview-tag',
              `bg-${localData.color || 'gray'}-100`,
              `text-${localData.color || 'gray'}-800`,
              `border-${localData.color || 'gray'}-200`
            ]"
          >
            {{ localData.name || '标签名称' }}
          </span>
          <span
            :class="[
              'preview-tag-solid',
              `bg-${localData.color || 'gray'}-500`,
              'text-white'
            ]"
          >
            {{ localData.name || '标签名称' }}
          </span>
        </div>
      </div>

      <!-- 使用统计（仅查看模式） -->
      <div v-if="mode === 'view'" class="form-group full-width">
        <label class="form-label">使用统计</label>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-icon">📊</span>
            <div class="stat-content">
              <span class="stat-value">{{ localData.usageCount || 0 }}</span>
              <span class="stat-label">使用次数</span>
            </div>
          </div>
          <div class="stat-item">
            <span class="stat-icon">📝</span>
            <div class="stat-content">
              <span class="stat-value">{{ localData.servicesCount || 0 }}</span>
              <span class="stat-label">关联服务</span>
            </div>
          </div>
          <div class="stat-item">
            <span class="stat-icon">📅</span>
            <div class="stat-content">
              <span class="stat-value">{{ formatDate(localData.createdAt) }}</span>
              <span class="stat-label">创建时间</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { useDebugHelper } from '@/utils/debugHelper'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'edit'
  },
  errors: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 调试助手
const { log, watchData } = useDebugHelper('TagDetail')

// 本地数据
const localData = ref({
  name: '',
  color: 'blue',
  description: '',
  usageCount: 0,
  servicesCount: 0,
  createdAt: '',
  ...props.modelValue
})

// 颜色选项
const colorOptions = [
  { value: 'blue', label: '蓝色' },
  { value: 'green', label: '绿色' },
  { value: 'red', label: '红色' },
  { value: 'yellow', label: '黄色' },
  { value: 'purple', label: '紫色' },
  { value: 'pink', label: '粉色' },
  { value: 'indigo', label: '靛蓝' },
  { value: 'gray', label: '灰色' }
]

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 监听数据变化
watch(localData, (newValue, oldValue) => {
  log('localData changed', {
    isUpdatingFromProps: isUpdatingFromProps.value,
    hasChanged: JSON.stringify(newValue) !== JSON.stringify(oldValue)
  })
  watchData('localData', newValue, 'local')

  // 避免在从props更新时触发
  if (isUpdatingFromProps.value) {
    log('skipping localData change - updating from props')
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
    log('skipping localData change - no actual change')
    return
  }

  log('emitting update:modelValue')
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  log('props.modelValue changed', { hasValue: !!newValue, isUpdating: isUpdatingFromProps.value })
  watchData('props.modelValue', newValue, 'props')

  if (!isUpdatingFromProps.value && newValue) {
    isUpdatingFromProps.value = true
    localData.value = { ...localData.value, ...newValue }
    log('localData updated from props')
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { deep: true })

// 方法
const selectColor = (color) => {
  if (props.mode !== 'view') {
    localData.value.color = color
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.tag-detail {
  @apply space-y-6;
}

.form-grid {
  @apply grid grid-cols-1 gap-6;
}

.form-group {
  @apply space-y-2;
}

.form-group.full-width {
  @apply col-span-full;
}

/* 只读字段样式 */
.form-input[readonly],
.form-textarea[readonly],
.form-select[disabled] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 cursor-not-allowed;
  border-color: #e5e7eb;
}

.dark .form-input[readonly],
.dark .form-textarea[readonly],
.dark .form-select[disabled] {
  border-color: #374151;
}

.color-option.pointer-events-none {
  @apply opacity-60 cursor-not-allowed;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  @apply transition-colors;
}

.form-input[readonly],
.form-textarea[readonly] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400;
  @apply cursor-not-allowed;
}

.form-input.error,
.form-textarea.error {
  @apply border-red-500 focus:ring-red-500;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* 颜色选择器 */
.color-selector {
  @apply flex flex-wrap gap-2;
}

.color-option {
  @apply w-8 h-8 rounded-full cursor-pointer border-2 border-transparent;
  @apply flex items-center justify-center transition-all;
  @apply hover:scale-110;
}

.color-option.selected {
  @apply border-gray-400 dark:border-gray-300 scale-110;
}

.color-check {
  @apply text-white font-bold text-sm;
}

/* 标签预览 */
.tag-preview {
  @apply flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.preview-tag {
  @apply px-3 py-1 rounded-full text-sm font-medium border;
}

.preview-tag-solid {
  @apply px-3 py-1 rounded-full text-sm font-medium;
}

/* 统计信息 */
.stats-grid {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4;
}

.stat-item {
  @apply flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.stat-icon {
  @apply text-2xl;
}

.stat-content {
  @apply flex flex-col;
}

.stat-value {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.stat-label {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 深色模式下的颜色调整 */
.dark .color-option {
  @apply border-gray-600;
}

.dark .color-option.selected {
  @apply border-gray-300;
}
</style>
