<template>
  <div class="service-detail">
    <!-- 基本信息内容 -->
    <template v-if="activeTab === 'basic'">
      <div class="form-grid">
        <div class="form-group">
          <label class="form-label">服务名称 *</label>
          <input
            v-model="localData.name"
            type="text"
            class="form-input"
            :class="{ 'error': errors.name }"
            :readonly="mode === 'view'"
            placeholder="请输入服务名称"
          />
          <span v-if="errors.name" class="error-message">{{ errors.name }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">服务分类 *</label>
          <select
            v-model="localData.category"
            class="form-select"
            :class="{ 'error': errors.category }"
            :disabled="mode === 'view'"
          >
            <option value="">请选择分类</option>
            <option value="api">API服务</option>
            <option value="tool">工具服务</option>
            <option value="integration">集成服务</option>
          </select>
          <span v-if="errors.category" class="error-message">{{ errors.category }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">版本号 *</label>
          <input
            v-model="localData.version"
            type="text"
            class="form-input"
            :class="{ 'error': errors.version }"
            :readonly="mode === 'view'"
            placeholder="如: v1.0.0"
          />
          <span v-if="errors.version" class="error-message">{{ errors.version }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">状态 *</label>
          <select
            v-model="localData.status"
            class="form-select"
            :class="{ 'error': errors.status }"
            :disabled="mode === 'view'"
          >
            <option value="">请选择状态</option>
            <option value="active">已发布</option>
            <option value="draft">草稿</option>
            <option value="deprecated">已废弃</option>
          </select>
          <span v-if="errors.status" class="error-message">{{ errors.status }}</span>
        </div>

        <div class="form-group full-width">
          <label class="form-label">标签</label>
          <div class="tags-input">
            <div class="tags-list">
              <span
                v-for="(tag, index) in localData.tags"
                :key="index"
                class="tag-item"
              >
                {{ tag }}
                <button
                  v-if="mode !== 'view'"
                  @click="removeTag(index)"
                  class="tag-remove"
                >×</button>
              </span>
            </div>
            <input
              v-if="mode !== 'view'"
              v-model="newTag"
              @keydown.enter.prevent="addTag"
              @keydown.comma.prevent="addTag"
              type="text"
              class="tag-input"
              placeholder="输入标签后按回车添加"
            />
          </div>
        </div>

        <div class="form-group full-width">
          <label class="form-label">简短描述</label>
          <textarea
            v-model="localData.summary"
            class="form-textarea"
            :readonly="mode === 'view'"
            rows="3"
            placeholder="请输入服务的简短描述"
          ></textarea>
        </div>
      </div>
    </template>

    <!-- 配置参数内容 -->
    <template v-if="activeTab === 'config'">
      <div class="form-grid">
        <div class="form-group">
          <label class="form-label">API端点</label>
          <input
            v-model="localData.endpoint"
            type="url"
            class="form-input"
            :class="{ 'error': errors.endpoint }"
            :readonly="mode === 'view'"
            placeholder="https://api.example.com"
          />
          <span v-if="errors.endpoint" class="error-message">{{ errors.endpoint }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">超时时间(秒)</label>
          <input
            v-model.number="localData.timeout"
            type="number"
            class="form-input"
            :readonly="mode === 'view'"
            min="1"
            max="300"
            placeholder="30"
          />
        </div>

        <div class="form-group">
          <label class="form-label">重试次数</label>
          <input
            v-model.number="localData.retries"
            type="number"
            class="form-input"
            :readonly="mode === 'view'"
            min="0"
            max="10"
            placeholder="3"
          />
        </div>

        <div class="form-group full-width">
          <label class="form-label">配置参数 (JSON)</label>
          <textarea
            v-model="configJson"
            class="form-textarea json-editor"
            :readonly="mode === 'view'"
            rows="10"
            placeholder='{"key": "value"}'
            @blur="validateJson"
          ></textarea>
          <span v-if="jsonError" class="error-message">{{ jsonError }}</span>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useDebugHelper } from '@/utils/debugHelper'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'edit'
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  activeTab: {
    type: String,
    default: 'basic'
  }
})

const emit = defineEmits(['update:modelValue'])

// 调试助手
const { log, watchData } = useDebugHelper('ServiceDetail')

// 本地数据
const localData = ref({
  name: '',
  category: '',
  version: '',
  status: '',
  tags: [],
  summary: '',
  endpoint: '',
  timeout: 30,
  retries: 3,
  config: {},
  ...props.modelValue
})

// 标签相关
const newTag = ref('')

// JSON配置相关
const configJson = ref('')
const jsonError = ref('')

// 初始化JSON配置
if (localData.value.config && typeof localData.value.config === 'object') {
  configJson.value = JSON.stringify(localData.value.config, null, 2)
}

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 监听数据变化
watch(localData, (newValue, oldValue) => {
  log('localData changed', {
    isUpdatingFromProps: isUpdatingFromProps.value,
    hasChanged: JSON.stringify(newValue) !== JSON.stringify(oldValue)
  })
  watchData('localData', newValue, 'local')

  // 避免在从props更新时触发
  if (isUpdatingFromProps.value) {
    log('skipping localData change - updating from props')
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
    log('skipping localData change - no actual change')
    return
  }

  log('emitting update:modelValue')
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  log('props.modelValue changed', { hasValue: !!newValue, isUpdating: isUpdatingFromProps.value })
  watchData('props.modelValue', newValue, 'props')

  if (!isUpdatingFromProps.value && newValue) {
    isUpdatingFromProps.value = true
    localData.value = { ...localData.value, ...newValue }
    if (newValue.config && typeof newValue.config === 'object') {
      configJson.value = JSON.stringify(newValue.config, null, 2)
    }
    log('localData updated from props')
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { deep: true })

// 方法
const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !localData.value.tags.includes(tag)) {
    localData.value.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (index) => {
  localData.value.tags.splice(index, 1)
}

const validateJson = () => {
  jsonError.value = ''
  if (!configJson.value.trim()) {
    localData.value.config = {}
    return
  }

  try {
    const parsed = JSON.parse(configJson.value)
    localData.value.config = parsed
  } catch (error) {
    jsonError.value = 'JSON格式错误: ' + error.message
  }
}
</script>

<style scoped>
.service-detail {
  @apply space-y-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-group {
  @apply space-y-2;
}

.form-group.full-width {
  @apply md:col-span-2;
}

/* 只读字段样式 */
.form-input[readonly],
.form-textarea[readonly],
.form-select[disabled] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 cursor-not-allowed;
  border-color: #e5e7eb;
}

.dark .form-input[readonly],
.dark .form-textarea[readonly],
.dark .form-select[disabled] {
  border-color: #374151;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  @apply transition-colors;
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  @apply border-red-500 focus:ring-red-500;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

.tags-input {
  @apply border border-gray-300 dark:border-gray-600 rounded-md p-2;
  @apply bg-white dark:bg-gray-700;
}

.tags-list {
  @apply flex flex-wrap gap-2 mb-2;
}

.tag-item {
  @apply inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900;
  @apply text-blue-800 dark:text-blue-200 text-sm rounded;
}

.tag-remove {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200;
  @apply font-bold;
}

.tag-input {
  @apply w-full border-0 outline-none bg-transparent;
  @apply text-gray-900 dark:text-white;
}

.json-editor {
  @apply font-mono text-sm;
}
</style>
