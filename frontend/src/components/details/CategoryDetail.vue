<template>
  <div class="category-detail">
    <div class="form-grid">
      <div class="form-group">
        <label class="form-label">分类名称 *</label>
        <input
          v-model="localData.name"
          type="text"
          class="form-input"
          :class="{ 'error': errors.name }"
          :readonly="mode === 'view'"
          placeholder="请输入分类名称"
        />
        <span v-if="errors.name" class="error-message">{{ errors.name }}</span>
      </div>

      <div class="form-group">
        <label class="form-label">父分类</label>
        <select
          v-model="localData.parent"
          class="form-select"
          :disabled="mode === 'view'"
        >
          <option value="">无父分类</option>
          <option
            v-for="category in parentCategories"
            :key="category.id"
            :value="category.id"
          >
            {{ category.name }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label class="form-label">排序</label>
        <input
          v-model.number="localData.sort"
          type="number"
          class="form-input"
          :readonly="mode === 'view'"
          min="0"
          placeholder="0"
        />
      </div>

      <div class="form-group">
        <label class="form-label">状态</label>
        <div class="toggle-switch">
          <input
            v-model="localData.enabled"
            type="checkbox"
            :id="`enabled-${componentId}`"
            class="toggle-input"
            :disabled="mode === 'view'"
          />
          <label :for="`enabled-${componentId}`" class="toggle-label">
            <span class="toggle-slider"></span>
            <span class="toggle-text">{{ localData.enabled ? '启用' : '禁用' }}</span>
          </label>
        </div>
      </div>

      <div class="form-group full-width">
        <label class="form-label">描述</label>
        <textarea
          v-model="localData.description"
          class="form-textarea"
          :readonly="mode === 'view'"
          rows="4"
          placeholder="请输入分类描述"
        ></textarea>
        <span v-if="errors.description" class="error-message">{{ errors.description }}</span>
      </div>

      <!-- 统计信息（仅查看模式） -->
      <div v-if="mode === 'view'" class="form-group full-width">
        <label class="form-label">统计信息</label>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">子分类数量</span>
            <span class="stat-value">{{ localData.childrenCount || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">关联服务数量</span>
            <span class="stat-value">{{ localData.servicesCount || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">创建时间</span>
            <span class="stat-value">{{ formatDate(localData.createdAt) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">更新时间</span>
            <span class="stat-value">{{ formatDate(localData.updatedAt) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useDebugHelper } from '@/utils/debugHelper'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'edit'
  },
  errors: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 调试助手
const { log, watchData } = useDebugHelper('CategoryDetail')

// 生成唯一ID
const componentId = Math.random().toString(36).substr(2, 9)

// 本地数据
const localData = ref({
  name: '',
  parent: '',
  description: '',
  sort: 0,
  enabled: true,
  childrenCount: 0,
  servicesCount: 0,
  createdAt: '',
  updatedAt: '',
  ...props.modelValue
})

// 父分类选项（模拟数据）
const parentCategories = ref([
  { id: 1, name: '编程语言' },
  { id: 2, name: '开发工具' },
  { id: 3, name: '数据库' },
  { id: 4, name: '云服务' }
])

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 监听数据变化
watch(localData, (newValue, oldValue) => {
  log('localData changed', {
    isUpdatingFromProps: isUpdatingFromProps.value,
    hasChanged: JSON.stringify(newValue) !== JSON.stringify(oldValue)
  })
  watchData('localData', newValue, 'local')

  // 避免在从props更新时触发
  if (isUpdatingFromProps.value) {
    log('skipping localData change - updating from props')
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
    log('skipping localData change - no actual change')
    return
  }

  log('emitting update:modelValue')
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  log('props.modelValue changed', { hasValue: !!newValue, isUpdating: isUpdatingFromProps.value })
  watchData('props.modelValue', newValue, 'props')

  if (!isUpdatingFromProps.value && newValue) {
    isUpdatingFromProps.value = true
    localData.value = { ...localData.value, ...newValue }
    log('localData updated from props')
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { deep: true })

// 方法
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  // 加载父分类选项
  // 这里可以调用API获取分类列表
})
</script>

<style scoped>
.category-detail {
  @apply space-y-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-group {
  @apply space-y-2;
}

.form-group.full-width {
  @apply md:col-span-2;
}

/* 只读字段样式 */
.form-input[readonly],
.form-textarea[readonly],
.form-select[disabled] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 cursor-not-allowed;
  border-color: #e5e7eb;
}

.dark .form-input[readonly],
.dark .form-textarea[readonly],
.dark .form-select[disabled] {
  border-color: #374151;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  @apply transition-colors;
}

.form-input[readonly],
.form-select[disabled],
.form-textarea[readonly] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400;
  @apply cursor-not-allowed;
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  @apply border-red-500 focus:ring-red-500;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* 切换开关样式 */
.toggle-switch {
  @apply flex items-center gap-3;
}

.toggle-input {
  @apply sr-only;
}

.toggle-label {
  @apply flex items-center gap-2 cursor-pointer;
}

.toggle-slider {
  @apply relative w-12 h-6 bg-gray-300 dark:bg-gray-600 rounded-full transition-colors;
}

.toggle-slider::before {
  @apply absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform;
  content: '';
}

.toggle-input:checked + .toggle-label .toggle-slider {
  @apply bg-blue-600;
}

.toggle-input:checked + .toggle-label .toggle-slider::before {
  @apply transform translate-x-6;
}

.toggle-input:disabled + .toggle-label {
  @apply opacity-50 cursor-not-allowed;
}

.toggle-text {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

/* 统计信息样式 */
.stats-grid {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.stat-item {
  @apply text-center;
}

.stat-label {
  @apply block text-xs text-gray-500 dark:text-gray-400 mb-1;
}

.stat-value {
  @apply block text-lg font-semibold text-gray-900 dark:text-white;
}
</style>
