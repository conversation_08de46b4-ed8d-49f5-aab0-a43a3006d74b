<template>
  <div class="content-detail">
    <!-- 基本信息内容 -->
    <template v-if="activeTab === 'basic'">
      <div class="form-grid">
        <div class="form-group">
          <label class="form-label">标题 *</label>
          <input
            v-model="localData.title"
            type="text"
            class="form-input"
            :class="{ 'error': errors.title }"
            :readonly="mode === 'view'"
            placeholder="请输入内容标题"
          />
          <span v-if="errors.title" class="error-message">{{ errors.title }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">内容类型 *</label>
          <select
            v-model="localData.type"
            class="form-select"
            :class="{ 'error': errors.type }"
            :disabled="mode === 'view'"
          >
            <option value="">请选择类型</option>
            <option value="document">规范文档</option>
            <option value="manual">操作手册</option>
            <option value="guide">技术文档</option>
            <option value="training">培训材料</option>
          </select>
          <span v-if="errors.type" class="error-message">{{ errors.type }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">状态 *</label>
          <div class="status-selector">
            <label
              v-for="status in statusOptions"
              :key="status.value"
              :class="[
                'status-option',
                status.value,
                { 'selected': localData.status === status.value }
              ]"
            >
              <input
                v-model="localData.status"
                type="radio"
                :value="status.value"
                :disabled="mode === 'view'"
                class="sr-only"
              />
              <span class="status-dot"></span>
              <span class="status-label">{{ status.label }}</span>
            </label>
          </div>
          <span v-if="errors.status" class="error-message">{{ errors.status }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">作者 *</label>
          <input
            v-model="localData.author"
            type="text"
            class="form-input"
            :class="{ 'error': errors.author }"
            :readonly="mode === 'view'"
            placeholder="请输入作者姓名"
          />
          <span v-if="errors.author" class="error-message">{{ errors.author }}</span>
        </div>

        <div class="form-group full-width">
          <label class="form-label">摘要</label>
          <textarea
            v-model="localData.summary"
            class="form-textarea"
            :readonly="mode === 'view'"
            rows="4"
            placeholder="请输入内容摘要..."
          ></textarea>
        </div>
      </div>
    </template>

    <!-- 元数据内容 -->
    <template v-if="activeTab === 'meta'">
      <div class="form-grid">
        <div class="form-group">
          <label class="form-label">发布日期</label>
          <input
            v-model="localData.publishDate"
            type="date"
            class="form-input"
            :readonly="mode === 'view'"
          />
        </div>

        <div class="form-group">
          <label class="form-label">过期日期</label>
          <input
            v-model="localData.expireDate"
            type="date"
            class="form-input"
            :readonly="mode === 'view'"
          />
        </div>

        <div class="form-group">
          <label class="form-label">优先级</label>
          <select
            v-model="localData.priority"
            class="form-select"
            :disabled="mode === 'view'"
          >
            <option value="low">低</option>
            <option value="normal">普通</option>
            <option value="high">高</option>
            <option value="urgent">紧急</option>
          </select>
        </div>

        <div class="form-group">
          <label class="form-label">访问权限</label>
          <select
            v-model="localData.permission"
            class="form-select"
            :disabled="mode === 'view'"
          >
            <option value="public">公开</option>
            <option value="internal">内部</option>
            <option value="private">私有</option>
          </select>
        </div>

        <div class="form-group full-width">
          <label class="form-label">关键词</label>
          <div class="keywords-input">
            <div class="keywords-list">
              <span
                v-for="(keyword, index) in localData.keywords"
                :key="index"
                class="keyword-item"
              >
                {{ keyword }}
                <button
                  v-if="mode !== 'view'"
                  @click="removeKeyword(index)"
                  class="keyword-remove"
                >
                  ×
                </button>
              </span>
            </div>
            <input
              v-if="mode !== 'view'"
              v-model="newKeyword"
              @keydown.enter.prevent="addKeyword"
              @keydown.comma.prevent="addKeyword"
              type="text"
              class="keyword-input"
              placeholder="输入关键词后按回车添加"
            />
          </div>
        </div>

        <div class="form-group full-width">
          <label class="form-label">相关链接</label>
          <div class="links-section">
            <div
              v-for="(link, index) in localData.relatedLinks"
              :key="index"
              class="link-item"
            >
              <input
                v-model="link.title"
                type="text"
                class="link-title"
                :readonly="mode === 'view'"
                placeholder="链接标题"
              />
              <input
                v-model="link.url"
                type="url"
                class="link-url"
                :readonly="mode === 'view'"
                placeholder="链接地址"
              />
              <button
                v-if="mode !== 'view'"
                @click="removeLink(index)"
                class="link-remove"
              >
                ×
              </button>
            </div>
            <button
              v-if="mode !== 'view'"
              @click="addLink"
              class="add-link-btn"
            >
              + 添加链接
            </button>
          </div>
        </div>

        <!-- 统计信息（仅查看模式） -->
        <div v-if="mode === 'view'" class="form-group full-width">
          <label class="form-label">统计信息</label>
          <div class="stats-overview">
            <div class="stat-row">
              <span class="stat-label">浏览次数</span>
              <span class="stat-value">{{ localData.viewCount || 0 }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">下载次数</span>
              <span class="stat-value">{{ localData.downloadCount || 0 }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">收藏次数</span>
              <span class="stat-value">{{ localData.favoriteCount || 0 }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">最后更新</span>
              <span class="stat-value">{{ formatDate(localData.updatedAt) }}</span>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { useDebugHelper } from '@/utils/debugHelper'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'edit'
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  activeTab: {
    type: String,
    default: 'basic'
  }
})

const emit = defineEmits(['update:modelValue'])

// 调试助手
const { log, watchData } = useDebugHelper('ContentDetail')

// 本地数据
const localData = ref({
  title: '',
  type: '',
  status: 'draft',
  author: '',
  summary: '',
  publishDate: '',
  expireDate: '',
  priority: 'normal',
  permission: 'internal',
  keywords: [],
  relatedLinks: [],
  viewCount: 0,
  downloadCount: 0,
  favoriteCount: 0,
  updatedAt: '',
  ...props.modelValue
})

// 关键词和链接相关
const newKeyword = ref('')

// 状态选项
const statusOptions = [
  { value: 'published', label: '已发布' },
  { value: 'draft', label: '草稿' },
  { value: 'review', label: '待审核' },
  { value: 'archived', label: '已归档' }
]

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 监听数据变化
watch(localData, (newValue, oldValue) => {
  log('localData changed', {
    isUpdatingFromProps: isUpdatingFromProps.value,
    hasChanged: JSON.stringify(newValue) !== JSON.stringify(oldValue)
  })
  watchData('localData', newValue, 'local')

  // 避免在从props更新时触发
  if (isUpdatingFromProps.value) {
    log('skipping localData change - updating from props')
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
    log('skipping localData change - no actual change')
    return
  }

  log('emitting update:modelValue')
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  log('props.modelValue changed', { hasValue: !!newValue, isUpdating: isUpdatingFromProps.value })
  watchData('props.modelValue', newValue, 'props')

  if (!isUpdatingFromProps.value && newValue) {
    isUpdatingFromProps.value = true
    localData.value = { ...localData.value, ...newValue }
    log('localData updated from props')
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { deep: true })

// 方法
const addKeyword = () => {
  const keyword = newKeyword.value.trim()
  if (keyword && !localData.value.keywords.includes(keyword)) {
    localData.value.keywords.push(keyword)
    newKeyword.value = ''
  }
}

const removeKeyword = (index) => {
  localData.value.keywords.splice(index, 1)
}

const addLink = () => {
  localData.value.relatedLinks.push({ title: '', url: '' })
}

const removeLink = (index) => {
  localData.value.relatedLinks.splice(index, 1)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
.content-detail {
  @apply space-y-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-group {
  @apply space-y-2;
}

.form-group.full-width {
  @apply md:col-span-2;
}

/* 只读字段样式 */
.form-input[readonly],
.form-textarea[readonly],
.form-select[disabled] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 cursor-not-allowed;
  border-color: #e5e7eb;
}

.dark .form-input[readonly],
.dark .form-textarea[readonly],
.dark .form-select[disabled] {
  border-color: #374151;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  @apply transition-colors;
}

.form-input[readonly],
.form-select[disabled],
.form-textarea[readonly] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400;
  @apply cursor-not-allowed;
}

.form-input.error,
.form-select.error {
  @apply border-red-500 focus:ring-red-500;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* 状态选择器 */
.status-selector {
  @apply grid grid-cols-2 gap-2;
}

.status-option {
  @apply flex items-center gap-2 p-3 border border-gray-200 dark:border-gray-600;
  @apply rounded-lg cursor-pointer transition-all;
}

.status-option.selected {
  @apply border-blue-500 bg-blue-50 dark:bg-blue-900;
}

.status-dot {
  @apply w-3 h-3 rounded-full;
}

.status-option.published .status-dot {
  @apply bg-green-500;
}

.status-option.draft .status-dot {
  @apply bg-gray-500;
}

.status-option.review .status-dot {
  @apply bg-yellow-500;
}

.status-option.archived .status-dot {
  @apply bg-red-500;
}

.status-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

/* 关键词输入 */
.keywords-input {
  @apply border border-gray-300 dark:border-gray-600 rounded-md p-2;
  @apply bg-white dark:bg-gray-700;
}

.keywords-list {
  @apply flex flex-wrap gap-2 mb-2;
}

.keyword-item {
  @apply inline-flex items-center gap-1 px-2 py-1 bg-purple-100 dark:bg-purple-900;
  @apply text-purple-800 dark:text-purple-200 text-sm rounded;
}

.keyword-remove {
  @apply text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200;
  @apply font-bold;
}

.keyword-input {
  @apply w-full border-0 outline-none bg-transparent;
  @apply text-gray-900 dark:text-white;
}

/* 链接部分 */
.links-section {
  @apply space-y-3;
}

.link-item {
  @apply flex gap-2 items-center;
}

.link-title {
  @apply flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.link-url {
  @apply flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
}

.link-remove {
  @apply w-8 h-8 flex items-center justify-center text-red-600 dark:text-red-400;
  @apply hover:bg-red-50 dark:hover:bg-red-900 rounded;
}

.add-link-btn {
  @apply px-4 py-2 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
  @apply border border-blue-200 dark:border-blue-700 rounded-md;
  @apply hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors;
}

/* 统计信息 */
.stats-overview {
  @apply space-y-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.stat-row {
  @apply flex justify-between items-center;
}

.stat-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.stat-value {
  @apply text-sm font-semibold text-gray-900 dark:text-white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-selector {
    @apply grid-cols-1 gap-2;
  }

  .link-item {
    @apply flex-col gap-2;
  }

  .link-title,
  .link-url {
    @apply w-full;
  }
}
</style>
