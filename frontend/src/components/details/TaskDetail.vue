<template>
  <div class="task-detail">
    <!-- 基本信息内容 -->
    <template v-if="activeTab === 'basic'">
      <div class="form-grid">
        <div class="form-group">
          <label class="form-label">任务名称 *</label>
          <input
            v-model="localData.name"
            type="text"
            class="form-input"
            :class="{ 'error': errors.name }"
            :readonly="mode === 'view'"
            placeholder="请输入任务名称"
          />
          <span v-if="errors.name" class="error-message">{{ errors.name }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">任务组 *</label>
          <select
            v-model="localData.group"
            class="form-select"
            :class="{ 'error': errors.group }"
            :disabled="mode === 'view'"
          >
            <option value="">请选择任务组</option>
            <option value="sync">数据同步</option>
            <option value="report">报表生成</option>
            <option value="cleanup">清理任务</option>
            <option value="backup">备份任务</option>
          </select>
          <span v-if="errors.group" class="error-message">{{ errors.group }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">优先级 *</label>
          <div class="priority-selector">
            <input
              v-model.number="localData.priority"
              type="range"
              min="1"
              max="10"
              class="priority-slider"
              :disabled="mode === 'view'"
            />
            <div class="priority-display">
              <span class="priority-value">{{ localData.priority }}</span>
              <span class="priority-label">{{ getPriorityLabel(localData.priority) }}</span>
            </div>
          </div>
          <span v-if="errors.priority" class="error-message">{{ errors.priority }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">状态 *</label>
          <div class="status-toggle">
            <input
              v-model="localData.status"
              type="checkbox"
              :id="`status-${componentId}`"
              class="status-input"
              :disabled="mode === 'view'"
              true-value="enabled"
              false-value="disabled"
            />
            <label :for="`status-${componentId}`" class="status-label">
              <span class="status-slider"></span>
              <span class="status-text">{{ localData.status === 'enabled' ? '启用' : '停用' }}</span>
            </label>
          </div>
        </div>

        <!-- 执行信息（仅查看模式） -->
        <div v-if="mode === 'view'" class="form-group full-width">
          <label class="form-label">执行信息</label>
          <div class="execution-info">
            <div class="info-item">
              <span class="info-label">下次执行</span>
              <span class="info-value">{{ formatDate(localData.nextExecution) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">上次执行</span>
              <span class="info-value">{{ formatDate(localData.lastExecution) }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">执行次数</span>
              <span class="info-value">{{ localData.executionCount || 0 }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">成功率</span>
              <span class="info-value">{{ getSuccessRate() }}%</span>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 调度配置内容 -->
    <template v-if="activeTab === 'schedule'">
      <div class="form-grid">
        <div class="form-group full-width">
          <label class="form-label">Cron表达式 *</label>
          <div class="cron-input-group">
            <input
              v-model="localData.cron"
              type="text"
              class="form-input"
              :class="{ 'error': errors.cron }"
              :readonly="mode === 'view'"
              placeholder="0 0 2 * * ?"
            />
            <button
              v-if="mode !== 'view'"
              type="button"
              class="cron-helper-btn"
              @click="showCronHelper = !showCronHelper"
            >
              ?
            </button>
          </div>
          <span v-if="errors.cron" class="error-message">{{ errors.cron }}</span>
          
          <!-- Cron表达式说明 -->
          <div v-if="showCronHelper" class="cron-helper">
            <h4 class="helper-title">Cron表达式格式说明</h4>
            <div class="helper-content">
              <p class="helper-format">格式: 秒 分 时 日 月 周</p>
              <div class="helper-examples">
                <div class="example-item">
                  <code>0 0 2 * * ?</code>
                  <span>每天凌晨2点执行</span>
                </div>
                <div class="example-item">
                  <code>0 30 8 * * MON-FRI</code>
                  <span>工作日上午8:30执行</span>
                </div>
                <div class="example-item">
                  <code>0 0 */6 * * ?</code>
                  <span>每6小时执行一次</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">超时时间(分钟)</label>
          <input
            v-model.number="localData.timeout"
            type="number"
            class="form-input"
            :readonly="mode === 'view'"
            min="1"
            max="1440"
            placeholder="30"
          />
        </div>

        <div class="form-group">
          <label class="form-label">重试次数</label>
          <input
            v-model.number="localData.retries"
            type="number"
            class="form-input"
            :readonly="mode === 'view'"
            min="0"
            max="5"
            placeholder="3"
          />
        </div>

        <div class="form-group">
          <label class="form-label">失败通知</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input
                v-model="localData.notifyOnFailure"
                type="checkbox"
                :disabled="mode === 'view'"
              />
              <span>失败时发送通知</span>
            </label>
            <label class="checkbox-item">
              <input
                v-model="localData.notifyOnSuccess"
                type="checkbox"
                :disabled="mode === 'view'"
              />
              <span>成功时发送通知</span>
            </label>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { useDebugHelper } from '@/utils/debugHelper'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'edit'
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  activeTab: {
    type: String,
    default: 'basic'
  }
})

const emit = defineEmits(['update:modelValue'])

// 调试助手
const { log, watchData } = useDebugHelper('TaskDetail')

// 生成唯一ID
const componentId = Math.random().toString(36).substr(2, 9)

// 本地数据
const localData = ref({
  name: '',
  group: '',
  priority: 5,
  status: 'enabled',
  cron: '',
  timeout: 30,
  retries: 3,
  notifyOnFailure: true,
  notifyOnSuccess: false,
  nextExecution: '',
  lastExecution: '',
  executionCount: 0,
  successCount: 0,
  ...props.modelValue
})

// 状态
const showCronHelper = ref(false)

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 监听数据变化
watch(localData, (newValue, oldValue) => {
  log('localData changed', {
    isUpdatingFromProps: isUpdatingFromProps.value,
    hasChanged: JSON.stringify(newValue) !== JSON.stringify(oldValue)
  })
  watchData('localData', newValue, 'local')

  // 避免在从props更新时触发
  if (isUpdatingFromProps.value) {
    log('skipping localData change - updating from props')
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
    log('skipping localData change - no actual change')
    return
  }

  log('emitting update:modelValue')
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  log('props.modelValue changed', { hasValue: !!newValue, isUpdating: isUpdatingFromProps.value })
  watchData('props.modelValue', newValue, 'props')

  if (!isUpdatingFromProps.value && newValue) {
    isUpdatingFromProps.value = true
    localData.value = { ...localData.value, ...newValue }
    log('localData updated from props')
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { deep: true })

// 方法
const getPriorityLabel = (priority) => {
  if (priority <= 3) return '低'
  if (priority <= 7) return '中'
  return '高'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const getSuccessRate = () => {
  const total = localData.value.executionCount || 0
  const success = localData.value.successCount || 0
  if (total === 0) return 0
  return Math.round((success / total) * 100)
}
</script>

<style scoped>
.task-detail {
  @apply space-y-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-group {
  @apply space-y-2;
}

.form-group.full-width {
  @apply md:col-span-2;
}

/* 只读字段样式 */
.form-input[readonly],
.form-textarea[readonly],
.form-select[disabled],
.priority-slider[disabled] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 cursor-not-allowed;
  border-color: #e5e7eb;
}

.dark .form-input[readonly],
.dark .form-textarea[readonly],
.dark .form-select[disabled],
.dark .priority-slider[disabled] {
  border-color: #374151;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  @apply transition-colors;
}

.form-input[readonly],
.form-select[disabled] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400;
  @apply cursor-not-allowed;
}

.form-input.error,
.form-select.error {
  @apply border-red-500 focus:ring-red-500;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* 优先级选择器 */
.priority-selector {
  @apply space-y-2;
}

.priority-slider {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer;
}

.priority-slider::-webkit-slider-thumb {
  @apply appearance-none w-4 h-4 bg-blue-600 rounded-full cursor-pointer;
}

.priority-slider::-moz-range-thumb {
  @apply w-4 h-4 bg-blue-600 rounded-full cursor-pointer border-0;
}

.priority-display {
  @apply flex items-center justify-between text-sm;
}

.priority-value {
  @apply font-semibold text-blue-600 dark:text-blue-400;
}

.priority-label {
  @apply text-gray-500 dark:text-gray-400;
}

/* 状态切换 */
.status-toggle {
  @apply flex items-center gap-3;
}

.status-input {
  @apply sr-only;
}

.status-label {
  @apply flex items-center gap-2 cursor-pointer;
}

.status-slider {
  @apply relative w-12 h-6 bg-gray-300 dark:bg-gray-600 rounded-full transition-colors;
}

.status-slider::before {
  @apply absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full transition-transform;
  content: '';
}

.status-input:checked + .status-label .status-slider {
  @apply bg-green-600;
}

.status-input:checked + .status-label .status-slider::before {
  @apply transform translate-x-6;
}

.status-input:disabled + .status-label {
  @apply opacity-50 cursor-not-allowed;
}

.status-text {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

/* 执行信息 */
.execution-info {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.info-item {
  @apply text-center;
}

.info-label {
  @apply block text-xs text-gray-500 dark:text-gray-400 mb-1;
}

.info-value {
  @apply block text-sm font-semibold text-gray-900 dark:text-white;
}

/* Cron输入组 */
.cron-input-group {
  @apply flex gap-2;
}

.cron-helper-btn {
  @apply px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400;
  @apply border border-gray-300 dark:border-gray-600 rounded-md;
  @apply hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
  @apply font-bold;
}

/* Cron帮助器 */
.cron-helper {
  @apply mt-3 p-4 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-lg;
}

.helper-title {
  @apply text-sm font-semibold text-blue-800 dark:text-blue-200 mb-2;
}

.helper-content {
  @apply space-y-2;
}

.helper-format {
  @apply text-sm text-blue-700 dark:text-blue-300 font-mono;
}

.helper-examples {
  @apply space-y-1;
}

.example-item {
  @apply flex items-center gap-3 text-sm;
}

.example-item code {
  @apply bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200 px-2 py-1 rounded font-mono;
}

.example-item span {
  @apply text-blue-700 dark:text-blue-300;
}

/* 复选框组 */
.checkbox-group {
  @apply space-y-2;
}

.checkbox-item {
  @apply flex items-center gap-2 cursor-pointer;
}

.checkbox-item input[type="checkbox"] {
  @apply w-4 h-4 text-blue-600 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded;
  @apply focus:ring-blue-500 focus:ring-2;
}

.checkbox-item input[type="checkbox"]:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.checkbox-item span {
  @apply text-sm text-gray-700 dark:text-gray-300;
}
</style>
