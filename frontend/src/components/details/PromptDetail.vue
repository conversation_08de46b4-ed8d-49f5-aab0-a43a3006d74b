<template>
  <div class="prompt-detail">
    <!-- 基本信息内容 -->
    <template v-if="activeTab === 'basic'">
      <div class="form-grid">
        <div class="form-group">
          <label class="form-label">提示词标题 *</label>
          <input
            v-model="localData.title"
            type="text"
            class="form-input"
            :class="{ 'error': errors.title }"
            :readonly="mode === 'view'"
            placeholder="请输入提示词标题"
          />
          <span v-if="errors.title" class="error-message">{{ errors.title }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">分类 *</label>
          <select
            v-model="localData.category"
            class="form-select"
            :class="{ 'error': errors.category }"
            :disabled="mode === 'view'"
          >
            <option value="">请选择分类</option>
            <option value="code">代码生成</option>
            <option value="writing">文档写作</option>
            <option value="analysis">数据分析</option>
            <option value="design">创意设计</option>
          </select>
          <span v-if="errors.category" class="error-message">{{ errors.category }}</span>
        </div>

        <div class="form-group">
          <label class="form-label">难度等级</label>
          <div class="difficulty-selector">
            <label
              v-for="level in difficultyLevels"
              :key="level.value"
              :class="[
                'difficulty-option',
                { 'selected': localData.difficulty === level.value }
              ]"
            >
              <input
                v-model="localData.difficulty"
                type="radio"
                :value="level.value"
                :disabled="mode === 'view'"
                class="sr-only"
              />
              <span class="difficulty-icon">{{ level.icon }}</span>
              <span class="difficulty-label">{{ level.label }}</span>
            </label>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">使用场景</label>
          <div class="scenario-tags">
            <label
              v-for="scenario in scenarioOptions"
              :key="scenario"
              class="scenario-tag"
            >
              <input
                v-model="localData.scenarios"
                type="checkbox"
                :value="scenario"
                :disabled="mode === 'view'"
                class="sr-only"
              />
              <span class="tag-text">{{ scenario }}</span>
            </label>
          </div>
        </div>

        <div class="form-group full-width">
          <label class="form-label">标签</label>
          <div class="tags-input">
            <div class="tags-list">
              <span
                v-for="(tag, index) in localData.tags"
                :key="index"
                class="tag-item"
              >
                {{ tag }}
                <button
                  v-if="mode !== 'view'"
                  @click="removeTag(index)"
                  class="tag-remove"
                >
                  ×
                </button>
              </span>
            </div>
            <input
              v-if="mode !== 'view'"
              v-model="newTag"
              @keydown.enter.prevent="addTag"
              @keydown.comma.prevent="addTag"
              type="text"
              class="tag-input"
              placeholder="输入标签后按回车添加"
            />
          </div>
        </div>

        <!-- 使用统计（仅查看模式） -->
        <div v-if="mode === 'view'" class="form-group full-width">
          <label class="form-label">使用统计</label>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon">📊</div>
              <div class="stat-content">
                <div class="stat-value">{{ localData.usageCount || 0 }}</div>
                <div class="stat-label">使用次数</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">⭐</div>
              <div class="stat-content">
                <div class="stat-value">{{ localData.rating || 0 }}</div>
                <div class="stat-label">平均评分</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">👥</div>
              <div class="stat-content">
                <div class="stat-value">{{ localData.favoriteCount || 0 }}</div>
                <div class="stat-label">收藏次数</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon">📅</div>
              <div class="stat-content">
                <div class="stat-value">{{ formatDate(localData.lastUsed) }}</div>
                <div class="stat-label">最后使用</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 使用示例内容 -->
    <template v-if="activeTab === 'examples'">
      <div class="examples-section">
        <div class="form-group">
          <label class="form-label">输入示例</label>
          <textarea
            v-model="localData.inputExample"
            class="form-textarea"
            :readonly="mode === 'view'"
            rows="6"
            placeholder="请输入使用此提示词的输入示例..."
          ></textarea>
        </div>

        <div class="form-group">
          <label class="form-label">输出示例</label>
          <textarea
            v-model="localData.outputExample"
            class="form-textarea"
            :readonly="mode === 'view'"
            rows="6"
            placeholder="请输入期望的输出示例..."
          ></textarea>
        </div>

        <div class="form-group">
          <label class="form-label">使用技巧</label>
          <textarea
            v-model="localData.tips"
            class="form-textarea"
            :readonly="mode === 'view'"
            rows="4"
            placeholder="请输入使用技巧和注意事项..."
          ></textarea>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { useDebugHelper } from '@/utils/debugHelper'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  mode: {
    type: String,
    default: 'edit'
  },
  errors: {
    type: Object,
    default: () => ({})
  },
  activeTab: {
    type: String,
    default: 'basic'
  }
})

const emit = defineEmits(['update:modelValue'])

// 调试助手
const { log, watchData } = useDebugHelper('PromptDetail')

// 本地数据
const localData = ref({
  title: '',
  category: '',
  difficulty: 'intermediate',
  scenarios: [],
  tags: [],
  inputExample: '',
  outputExample: '',
  tips: '',
  usageCount: 0,
  rating: 0,
  favoriteCount: 0,
  lastUsed: '',
  ...props.modelValue
})

// 标签相关
const newTag = ref('')

// 难度等级选项
const difficultyLevels = [
  { value: 'beginner', label: '初级', icon: '🟢' },
  { value: 'intermediate', label: '中级', icon: '🟡' },
  { value: 'advanced', label: '高级', icon: '🔴' }
]

// 使用场景选项
const scenarioOptions = [
  '代码重构',
  '文档生成',
  '错误调试',
  '性能优化',
  '测试用例',
  'API设计',
  '数据分析',
  '创意写作'
]

// 防止循环更新的标志
const isUpdatingFromProps = ref(false)

// 监听数据变化
watch(localData, (newValue, oldValue) => {
  log('localData changed', {
    isUpdatingFromProps: isUpdatingFromProps.value,
    hasChanged: JSON.stringify(newValue) !== JSON.stringify(oldValue)
  })
  watchData('localData', newValue, 'local')

  // 避免在从props更新时触发
  if (isUpdatingFromProps.value) {
    log('skipping localData change - updating from props')
    return
  }

  // 检查数据是否真的发生了变化
  if (JSON.stringify(newValue) === JSON.stringify(oldValue)) {
    log('skipping localData change - no actual change')
    return
  }

  log('emitting update:modelValue')
  emit('update:modelValue', newValue)
}, { deep: true })

watch(() => props.modelValue, (newValue) => {
  log('props.modelValue changed', { hasValue: !!newValue, isUpdating: isUpdatingFromProps.value })
  watchData('props.modelValue', newValue, 'props')

  if (!isUpdatingFromProps.value && newValue) {
    isUpdatingFromProps.value = true
    localData.value = { ...localData.value, ...newValue }
    log('localData updated from props')
    nextTick(() => {
      isUpdatingFromProps.value = false
    })
  }
}, { deep: true })

// 方法
const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !localData.value.tags.includes(tag)) {
    localData.value.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (index) => {
  localData.value.tags.splice(index, 1)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}
</script>

<style scoped>
.prompt-detail {
  @apply space-y-6;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-group {
  @apply space-y-2;
}

.form-group.full-width {
  @apply md:col-span-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.form-input,
.form-select,
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  @apply transition-colors;
}

.form-input[readonly],
.form-select[disabled],
.form-textarea[readonly] {
  @apply bg-gray-50 dark:bg-gray-800 text-gray-500 dark:text-gray-400;
  @apply cursor-not-allowed;
}

.form-input.error,
.form-select.error {
  @apply border-red-500 focus:ring-red-500;
}

.error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* 难度选择器 */
.difficulty-selector {
  @apply flex gap-3;
}

.difficulty-option {
  @apply flex flex-col items-center gap-1 p-3 border-2 border-gray-200 dark:border-gray-600;
  @apply rounded-lg cursor-pointer transition-all hover:border-blue-300 dark:hover:border-blue-500;
}

.difficulty-option.selected {
  @apply border-blue-500 bg-blue-50 dark:bg-blue-900;
}

.difficulty-icon {
  @apply text-xl;
}

.difficulty-label {
  @apply text-xs font-medium text-gray-700 dark:text-gray-300;
}

/* 场景标签 */
.scenario-tags {
  @apply flex flex-wrap gap-2;
}

.scenario-tag {
  @apply cursor-pointer;
}

.scenario-tag input:checked + .tag-text {
  @apply bg-blue-500 text-white;
}

.tag-text {
  @apply px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
  @apply rounded-full text-sm transition-colors hover:bg-gray-200 dark:hover:bg-gray-600;
}

/* 标签输入 */
.tags-input {
  @apply border border-gray-300 dark:border-gray-600 rounded-md p-2;
  @apply bg-white dark:bg-gray-700;
}

.tags-list {
  @apply flex flex-wrap gap-2 mb-2;
}

.tag-item {
  @apply inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900;
  @apply text-blue-800 dark:text-blue-200 text-sm rounded;
}

.tag-remove {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200;
  @apply font-bold;
}

.tag-input {
  @apply w-full border-0 outline-none bg-transparent;
  @apply text-gray-900 dark:text-white;
}

/* 统计卡片 */
.stats-grid {
  @apply grid grid-cols-2 md:grid-cols-4 gap-4;
}

.stat-card {
  @apply flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.stat-icon {
  @apply text-2xl;
}

.stat-content {
  @apply flex flex-col;
}

.stat-value {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.stat-label {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 示例部分 */
.examples-section {
  @apply space-y-6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .difficulty-selector {
    @apply grid grid-cols-3 gap-2;
  }

  .scenario-tags {
    @apply grid grid-cols-2 gap-2;
  }

  .stats-grid {
    @apply grid-cols-1 gap-3;
  }

  .stat-card {
    @apply p-3;
  }
}
</style>
