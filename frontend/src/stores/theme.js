import { defineStore } from 'pinia'
import { ref, onMounted } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  const isDarkMode = ref(false)

  // 主题切换功能
  const toggleTheme = () => {
    isDarkMode.value = !isDarkMode.value
    localStorage.setItem('darkMode', isDarkMode.value.toString())
    updateHtmlClass()
  }

  // 设置主题
  const setTheme = (dark) => {
    isDarkMode.value = dark
    localStorage.setItem('darkMode', isDarkMode.value.toString())
    updateHtmlClass()
  }

  // 更新HTML类名
  const updateHtmlClass = () => {
    const html = document.documentElement

    // 使用Tailwind推荐的方式，只需要dark类
    if (isDarkMode.value) {
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')
    } else {
      html.classList.remove('dark')
      html.setAttribute('data-theme', 'light')
    }
  }

  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('darkMode')
    if (savedTheme !== null) {
      isDarkMode.value = savedTheme === 'true'
    } else {
      // 检测系统主题偏好
      isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    updateHtmlClass()
  }

  return {
    isDarkMode,
    toggleTheme,
    setTheme,
    initTheme
  }
})
