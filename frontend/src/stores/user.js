import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useUserStore = defineStore('user', () => {
  // 状态
  const user = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!user.value)
  const userName = computed(() => user.value?.nickname || user.value?.username || '')
  const userAvatar = computed(() => user.value?.avatar || '/placeholder-user.jpg')

  // 设置用户信息
  const setUser = (userData) => {
    user.value = userData
  }

  // 设置token
  const setToken = (tokenValue) => {
    token.value = tokenValue
    if (tokenValue) {
      localStorage.setItem('token', tokenValue)
    } else {
      localStorage.removeItem('token')
    }
  }

  // 登录
  const login = async (credentials) => {
    isLoading.value = true
    try {
      const response = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials)
      })

      const result = await response.json()

      if (result.code === 200 && result.data) {
        // 设置用户信息和令牌
        setUser(result.data.user)
        setToken(result.data.accessToken)

        // 存储刷新令牌
        localStorage.setItem('refreshToken', result.data.refreshToken)

        return { success: true, data: result.data.user }
      } else {
        return { success: false, message: result.message || '登录失败' }
      }
    } catch (error) {
      console.error('登录错误:', error)
      return { success: false, message: '网络错误，请稍后重试' }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = () => {
    setUser(null)
    setToken('')
    // 清除其他可能的本地存储
    localStorage.removeItem('refreshToken')
    localStorage.removeItem('userInfo')
  }

  // 获取用户信息（用于页面刷新后恢复用户状态）
  const fetchUserInfo = async () => {
    if (!token.value) return false

    try {
      const response = await fetch('http://localhost:8080/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token.value}`
        }
      })

      const result = await response.json()

      if (result.code === 200 && result.data) {
        setUser(result.data.user)
        return true
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }

    // 如果获取失败，尝试刷新令牌
    const refreshSuccess = await refreshToken()
    if (!refreshSuccess) {
      logout()
    }
    return refreshSuccess
  }

  // 刷新令牌
  const refreshToken = async () => {
    const refreshTokenValue = localStorage.getItem('refreshToken')
    if (!refreshTokenValue) return false

    try {
      const response = await fetch('http://localhost:8080/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: refreshTokenValue })
      })

      const result = await response.json()

      if (result.code === 200 && result.data) {
        setToken(result.data.accessToken)
        return await fetchUserInfo()
      }
    } catch (error) {
      console.error('刷新令牌失败:', error)
    }

    return false
  }

  // 初始化用户状态
  const initUserState = async () => {
    if (token.value) {
      await fetchUserInfo()
    }
  }

  return {
    // 状态
    user,
    token,
    isLoading,
    
    // 计算属性
    isLoggedIn,
    userName,
    userAvatar,
    
    // 方法
    setUser,
    setToken,
    login,
    logout,
    fetchUserInfo,
    refreshToken,
    initUserState
  }
})
