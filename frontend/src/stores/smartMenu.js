import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useSmartMenuStore = defineStore('smartMenu', () => {
  // 状态
  const recentlyVisited = ref([])
  const favorites = ref([])
  const searchHistory = ref([])
  const menuUsageStats = ref({})
  const personalizedRecommendations = ref([])
  const menuCustomization = ref({
    hiddenItems: [],
    customOrder: [],
    pinnedItems: []
  })
  const isInitialized = ref(false)

  // 菜单数据
  const menuItems = ref([
    {
      id: 'dashboard',
      name: '数据看板',
      icon: 'dashboard',
      route: '/dashboard',
      category: '主要功能',
      description: '查看系统整体数据和统计信息',
      keywords: ['数据', '统计', '图表', '看板', 'dashboard'],
      accessCount: 0,
      lastAccessed: null,
      estimatedTime: '2分钟',
      difficulty: 'easy'
    },
    {
      id: 'users',
      name: '用户管理',
      icon: 'users',
      route: '/users',
      category: '控制台',
      description: '管理系统用户和权限设置',
      keywords: ['用户', '权限', '管理', 'users'],
      accessCount: 0,
      lastAccessed: null,
      estimatedTime: '5分钟',
      difficulty: 'medium'
    },
    {
      id: 'services',
      name: '服务管理',
      icon: 'service',
      route: '/services',
      category: 'MCP服务',
      description: '管理和配置各种服务',
      keywords: ['服务', '配置', 'service', 'mcp'],
      accessCount: 0,
      lastAccessed: null,
      estimatedTime: '10分钟',
      difficulty: 'hard',
      badge: 12
    },
    {
      id: 'categories',
      name: '分类管理',
      icon: 'category',
      route: '/categories',
      category: 'MCP服务',
      description: '管理内容分类和标签',
      keywords: ['分类', '标签', 'category'],
      accessCount: 0,
      lastAccessed: null,
      estimatedTime: '3分钟',
      difficulty: 'easy'
    },
    {
      id: 'tasks',
      name: '任务管理',
      icon: 'tasks',
      route: '/tasks',
      category: '定时任务',
      description: '创建和管理定时任务',
      keywords: ['任务', '定时', 'task', 'cron'],
      accessCount: 0,
      lastAccessed: null,
      estimatedTime: '8分钟',
      difficulty: 'medium'
    },
    {
      id: 'settings',
      name: '个人设置',
      icon: 'settings',
      route: '/settings',
      category: '个人中心',
      description: '配置个人偏好和系统设置',
      keywords: ['设置', '配置', '偏好', 'settings'],
      accessCount: 0,
      lastAccessed: null,
      estimatedTime: '5分钟',
      difficulty: 'easy'
    },
    {
      id: 'wallet',
      name: '钱包',
      icon: 'wallet',
      route: '/wallet',
      category: '个人中心',
      description: '查看账户余额和交易记录',
      keywords: ['钱包', '余额', '交易', 'wallet'],
      accessCount: 0,
      lastAccessed: null,
      estimatedTime: '3分钟',
      difficulty: 'easy'
    }
  ])

  // 计算属性
  const sortedRecentlyVisited = computed(() => {
    return recentlyVisited.value
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, 5)
  })

  const topSearches = computed(() => {
    const searchCounts = {}
    searchHistory.value.forEach(search => {
      searchCounts[search.query] = (searchCounts[search.query] || 0) + 1
    })
    
    return Object.entries(searchCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([query, count]) => ({ query, count }))
  })

  const recommendedItems = computed(() => {
    // 基于使用频率和时间的推荐算法
    const now = new Date()
    const hour = now.getHours()

    const recommendations = menuItems.value
      .filter(item => !menuCustomization.value.hiddenItems.includes(item.id))
      .filter(item => !menuCustomization.value.pinnedItems.includes(item.id)) // 排除已固定的项目
      .map(item => {
        let score = 0

        // 访问频率权重 (30%)
        score += (menuUsageStats.value[item.id]?.count || 0) * 0.3

        // 最近访问权重 (25%)
        if (item.lastAccessed) {
          const daysSinceAccess = (now - new Date(item.lastAccessed)) / (1000 * 60 * 60 * 24)
          score += Math.max(0, (7 - daysSinceAccess) / 7) * 0.25
        }

        // 用户偏好权重 (20%)
        if (favorites.value.includes(item.id)) {
          score += 0.2
        }

        // 基础推荐权重 (15%) - 为常用功能提供基础分数
        const popularItems = ['dashboard', 'users', 'settings', 'profile']
        if (popularItems.includes(item.id)) {
          score += 0.15
        }

        // 时间相关性权重 (10%)
        if (item.category === '数据看板' && (hour >= 9 && hour <= 11)) {
          score += 0.1 // 上午推荐数据看板
        } else if (item.category === '个人中心' && (hour >= 17 && hour <= 19)) {
          score += 0.1 // 下班时间推荐个人中心
        } else if (item.category === '控制台' && (hour >= 14 && hour <= 16)) {
          score += 0.1 // 下午推荐管理功能
        }

        return { ...item, recommendationScore: score }
      })
      .sort((a, b) => b.recommendationScore - a.recommendationScore)
      .slice(0, 3) // 只显示3个推荐

    // 如果没有推荐结果，返回一些默认推荐
    if (recommendations.length === 0) {
      const defaultRecommendations = menuItems.value
        .filter(item => !menuCustomization.value.pinnedItems.includes(item.id))
        .filter(item => ['dashboard', 'users', 'services'].includes(item.id))
        .slice(0, 3)
        .map(item => ({ ...item, recommendationScore: 0.1 }))

      return defaultRecommendations
    }

    return recommendations
  })

  const quickAccessItems = computed(() => {
    // 获取固定项目，最多4个
    const pinnedItems = menuCustomization.value.pinnedItems.map(id =>
      menuItems.value.find(item => item.id === id)
    ).filter(Boolean).slice(0, 4)

    // 获取最近访问项目，排除已固定的项目，并从完整菜单项中获取数据
    const recentItems = sortedRecentlyVisited.value
      .filter(item => !menuCustomization.value.pinnedItems.includes(item.id))
      .map(recentItem => menuItems.value.find(item => item.id === recentItem.id))
      .filter(Boolean)
      .slice(0, 4 - pinnedItems.length) // 确保总数不超过4个

    return [...pinnedItems, ...recentItems].slice(0, 4)
  })

  // 方法
  const recordVisit = (itemId) => {
    const item = menuItems.value.find(m => m.id === itemId)
    if (!item) return

    const now = new Date()
    
    // 更新菜单项访问信息
    item.accessCount++
    item.lastAccessed = now.toISOString()
    
    // 更新使用统计
    if (!menuUsageStats.value[itemId]) {
      menuUsageStats.value[itemId] = { count: 0, totalTime: 0, lastAccess: null }
    }
    menuUsageStats.value[itemId].count++
    menuUsageStats.value[itemId].lastAccess = now.toISOString()
    
    // 添加到最近访问
    const existingIndex = recentlyVisited.value.findIndex(v => v.id === itemId)
    if (existingIndex >= 0) {
      recentlyVisited.value.splice(existingIndex, 1)
    }
    
    recentlyVisited.value.unshift({
      id: itemId,
      name: item.name,
      icon: item.icon,
      route: item.route,
      timestamp: now.toISOString()
    })
    
    // 限制最近访问数量
    if (recentlyVisited.value.length > 20) {
      recentlyVisited.value = recentlyVisited.value.slice(0, 20)
    }
    
    saveToStorage()
  }

  const addToFavorites = (itemId) => {
    if (!favorites.value.includes(itemId)) {
      favorites.value.push(itemId)
      saveToStorage()
    }
  }

  const removeFromFavorites = (itemId) => {
    const index = favorites.value.indexOf(itemId)
    if (index >= 0) {
      favorites.value.splice(index, 1)
      saveToStorage()
    }
  }

  const toggleFavorite = (itemId) => {
    if (favorites.value.includes(itemId)) {
      removeFromFavorites(itemId)
    } else {
      addToFavorites(itemId)
    }
  }

  const recordSearch = (query, results = []) => {
    if (!query.trim()) return
    
    searchHistory.value.unshift({
      query: query.trim(),
      results: results.length,
      timestamp: new Date().toISOString()
    })
    
    // 限制搜索历史数量
    if (searchHistory.value.length > 100) {
      searchHistory.value = searchHistory.value.slice(0, 100)
    }
    
    saveToStorage()
  }

  const searchMenuItems = (query) => {
    if (!query.trim()) return []
    
    const searchTerm = query.toLowerCase()
    const results = menuItems.value.filter(item => {
      return item.name.toLowerCase().includes(searchTerm) ||
             item.description.toLowerCase().includes(searchTerm) ||
             item.category.toLowerCase().includes(searchTerm) ||
             item.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm))
    })
    
    // 记录搜索
    recordSearch(query, results)
    
    // 按相关性排序
    return results.sort((a, b) => {
      let scoreA = 0
      let scoreB = 0
      
      // 名称匹配权重最高
      if (a.name.toLowerCase().includes(searchTerm)) scoreA += 10
      if (b.name.toLowerCase().includes(searchTerm)) scoreB += 10
      
      // 描述匹配
      if (a.description.toLowerCase().includes(searchTerm)) scoreA += 5
      if (b.description.toLowerCase().includes(searchTerm)) scoreB += 5
      
      // 关键词匹配
      scoreA += a.keywords.filter(k => k.toLowerCase().includes(searchTerm)).length * 2
      scoreB += b.keywords.filter(k => k.toLowerCase().includes(searchTerm)).length * 2
      
      // 使用频率加权
      scoreA += (menuUsageStats.value[a.id]?.count || 0) * 0.1
      scoreB += (menuUsageStats.value[b.id]?.count || 0) * 0.1
      
      return scoreB - scoreA
    })
  }

  const pinItem = (itemId) => {
    if (!menuCustomization.value.pinnedItems.includes(itemId)) {
      menuCustomization.value.pinnedItems.push(itemId)
      saveToStorage()
    }
  }

  const unpinItem = (itemId) => {
    const index = menuCustomization.value.pinnedItems.indexOf(itemId)
    if (index >= 0) {
      menuCustomization.value.pinnedItems.splice(index, 1)
      saveToStorage()
    }
  }

  const hideItem = (itemId) => {
    if (!menuCustomization.value.hiddenItems.includes(itemId)) {
      menuCustomization.value.hiddenItems.push(itemId)
      saveToStorage()
    }
  }

  const showItem = (itemId) => {
    const index = menuCustomization.value.hiddenItems.indexOf(itemId)
    if (index >= 0) {
      menuCustomization.value.hiddenItems.splice(index, 1)
      saveToStorage()
    }
  }

  const clearRecentlyVisited = () => {
    recentlyVisited.value = []
    saveToStorage()
  }

  const clearSearchHistory = () => {
    searchHistory.value = []
    saveToStorage()
  }

  const resetCustomization = () => {
    menuCustomization.value = {
      hiddenItems: [],
      customOrder: [],
      pinnedItems: []
    }
    saveToStorage()
  }

  const saveToStorage = () => {
    try {
      localStorage.setItem('smart-menu-data', JSON.stringify({
        recentlyVisited: recentlyVisited.value,
        favorites: favorites.value,
        searchHistory: searchHistory.value,
        menuUsageStats: menuUsageStats.value,
        menuCustomization: menuCustomization.value,
        menuItems: menuItems.value
      }))
    } catch (error) {
      console.warn('Failed to save smart menu data:', error)
    }
  }

  const loadFromStorage = () => {
    if (isInitialized.value) return // 避免重复初始化

    try {
      const saved = localStorage.getItem('smart-menu-data')
      if (saved) {
        const data = JSON.parse(saved)
        recentlyVisited.value = data.recentlyVisited || []
        favorites.value = data.favorites || []
        searchHistory.value = data.searchHistory || []
        menuUsageStats.value = data.menuUsageStats || {}
        menuCustomization.value = data.menuCustomization || {
          hiddenItems: [],
          customOrder: [],
          pinnedItems: []
        }

        // 合并菜单项数据，保留统计信息
        if (data.menuItems) {
          data.menuItems.forEach(savedItem => {
            const currentItem = menuItems.value.find(item => item.id === savedItem.id)
            if (currentItem) {
              currentItem.accessCount = savedItem.accessCount || 0
              currentItem.lastAccessed = savedItem.lastAccessed
            }
          })
        }
      }
      isInitialized.value = true
    } catch (error) {
      console.warn('Failed to load smart menu data:', error)
      isInitialized.value = true
    }
  }

  const getAnalytics = () => {
    const totalAccess = Object.values(menuUsageStats.value)
      .reduce((sum, stat) => sum + (stat.count || 0), 0)

    // 获取最常使用的项目，包含访问次数
    const mostUsedItems = menuItems.value
      .map(item => ({
        ...item,
        accessCount: menuUsageStats.value[item.id]?.count || 0
      }))
      .filter(item => item.accessCount > 0)
      .sort((a, b) => b.accessCount - a.accessCount)
      .slice(0, 3)

    // 如果没有使用数据，提供一些示例数据
    const finalMostUsedItems = mostUsedItems.length > 0 ? mostUsedItems : [
      { ...menuItems.value.find(item => item.id === 'dashboard') || menuItems.value[0], accessCount: 5 },
      { ...menuItems.value.find(item => item.id === 'users') || menuItems.value[1], accessCount: 3 },
      { ...menuItems.value.find(item => item.id === 'settings') || menuItems.value[2], accessCount: 2 }
    ].filter(Boolean)

    const categoryUsage = {}
    menuItems.value.forEach(item => {
      if (!categoryUsage[item.category]) {
        categoryUsage[item.category] = 0
      }
      categoryUsage[item.category] += menuUsageStats.value[item.id]?.count || 0
    })

    return {
      totalAccess: totalAccess || 10, // 提供默认值
      mostUsedItems: finalMostUsedItems,
      categoryUsage,
      searchCount: searchHistory.value.length,
      favoriteCount: favorites.value.length,
      recentCount: recentlyVisited.value.length
    }
  }

  // 生成示例数据
  const generateSampleData = (force = false) => {
    // 如果已经有数据且不是强制生成，就不生成示例数据
    if (!force && (Object.keys(menuUsageStats.value).length > 0 || recentlyVisited.value.length > 0)) {
      return
    }

    // 添加一些示例使用统计
    const sampleStats = {
      'dashboard': { count: 15, lastAccessed: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() },
      'users': { count: 8, lastAccessed: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() },
      'services': { count: 5, lastAccessed: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString() },
      'profile': { count: 3, lastAccessed: new Date(Date.now() - 1000 * 60 * 60 * 72).toISOString() }
    }

    Object.entries(sampleStats).forEach(([itemId, stats]) => {
      menuUsageStats.value[itemId] = stats
      const menuItem = menuItems.value.find(item => item.id === itemId)
      if (menuItem) {
        menuItem.lastAccessed = stats.lastAccessed
        menuItem.accessCount = stats.count
      }
    })

    // 添加一些最近访问记录
    const sampleRecentItems = [
      { id: 'dashboard', name: '数据看板', icon: 'dashboard', timestamp: Date.now() - 1000 * 60 * 60 * 2 },
      { id: 'users', name: '用户管理', icon: 'users', timestamp: Date.now() - 1000 * 60 * 60 * 24 }
    ]

    recentlyVisited.value = sampleRecentItems

    // 添加一些收藏项目
    favorites.value = ['dashboard', 'services']

    // 添加一些搜索历史
    searchHistory.value = [
      { query: '用户', timestamp: Date.now() - 1000 * 60 * 60 * 12 },
      { query: '设置', timestamp: Date.now() - 1000 * 60 * 60 * 24 },
      { query: '数据', timestamp: Date.now() - 1000 * 60 * 60 * 48 }
    ]

    // 保存示例数据
    saveToStorage()
  }

  // 初始化函数
  const initialize = () => {
    loadFromStorage()
    // 如果是首次使用，生成示例数据
    setTimeout(() => {
      generateSampleData()
    }, 100)
  }

  return {
    // 状态
    recentlyVisited,
    favorites,
    searchHistory,
    menuUsageStats,
    personalizedRecommendations,
    menuCustomization,
    menuItems,
    
    // 计算属性
    sortedRecentlyVisited,
    topSearches,
    recommendedItems,
    quickAccessItems,
    
    // 方法
    recordVisit,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    recordSearch,
    searchMenuItems,
    pinItem,
    unpinItem,
    hideItem,
    showItem,
    clearRecentlyVisited,
    clearSearchHistory,
    resetCustomization,
    saveToStorage,
    loadFromStorage,
    initialize,
    generateSampleData,
    getAnalytics
  }
})
