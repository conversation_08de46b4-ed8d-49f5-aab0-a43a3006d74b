import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useToastStore = defineStore('toast', () => {
  // 状态
  const toasts = ref([])
  const maxToasts = ref(5) // 最大同时显示的Toast数量

  // 添加Toast
  const addToast = (toast) => {
    const id = Date.now() + Math.random()
    const newToast = {
      id,
      type: 'info',
      title: '',
      message: '',
      duration: 4000,
      ...toast
    }

    toasts.value.unshift(newToast)

    // 限制最大数量
    if (toasts.value.length > maxToasts.value) {
      toasts.value = toasts.value.slice(0, maxToasts.value)
    }

    // 自动移除
    if (newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, newToast.duration)
    }

    return id
  }

  // 移除Toast
  const removeToast = (id) => {
    const index = toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }

  // 清除所有Toast
  const clearAll = () => {
    toasts.value = []
  }

  // 快捷方法
  const success = (message, options = {}) => {
    return addToast({
      type: 'success',
      message,
      ...options
    })
  }

  const error = (message, options = {}) => {
    return addToast({
      type: 'error',
      message,
      duration: 6000, // 错误消息显示更久
      ...options
    })
  }

  const warning = (message, options = {}) => {
    return addToast({
      type: 'warning',
      message,
      duration: 5000,
      ...options
    })
  }

  const info = (message, options = {}) => {
    return addToast({
      type: 'info',
      message,
      ...options
    })
  }

  return {
    // 状态
    toasts,
    maxToasts,
    
    // 方法
    addToast,
    removeToast,
    clearAll,
    success,
    error,
    warning,
    info
  }
})
