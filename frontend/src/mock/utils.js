// Mock工具函数
import { mockConfig } from './config'

// 生成随机ID
export function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 生成随机数字
export function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 生成随机日期
export function randomDate(start = new Date(2023, 0, 1), end = new Date()) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

// 格式化日期
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 随机选择数组元素
export function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)]
}

// 随机选择多个数组元素
export function randomChoices(array, count = 1) {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}

// 生成随机字符串
export function randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

// 生成随机中文姓名
export function randomChineseName() {
  const surnames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗']
  const names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞']
  
  return randomChoice(surnames) + randomChoice(names) + (Math.random() > 0.5 ? randomChoice(names) : '')
}

// 生成随机邮箱
export function randomEmail() {
  const domains = ['gmail.com', 'qq.com', '163.com', 'sina.com', 'hotmail.com']
  const username = randomString(randomInt(5, 12), 'abcdefghijklmnopqrstuvwxyz0123456789')
  return `${username}@${randomChoice(domains)}`
}

// 生成随机手机号
export function randomPhone() {
  const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139', '150', '151', '152', '153', '155', '156', '157', '158', '159', '180', '181', '182', '183', '184', '185', '186', '187', '188', '189']
  return randomChoice(prefixes) + randomString(8, '0123456789')
}

// 分页处理
export function paginate(data, page = 1, pageSize = mockConfig.pagination.defaultPageSize) {
  const total = data.length
  const totalPages = Math.ceil(total / pageSize)
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const items = data.slice(start, end)
  
  return {
    items,
    total,
    page: parseInt(page),
    pageSize: parseInt(pageSize),
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  }
}

// 搜索过滤
export function filterData(data, filters = {}) {
  return data.filter(item => {
    return Object.entries(filters).every(([key, value]) => {
      if (!value || value === '') return true
      
      const itemValue = item[key]
      if (typeof itemValue === 'string') {
        return itemValue.toLowerCase().includes(value.toLowerCase())
      }
      if (typeof itemValue === 'number') {
        return itemValue.toString().includes(value.toString())
      }
      
      return itemValue === value
    })
  })
}

// 排序
export function sortData(data, sortBy = 'id', sortOrder = 'asc') {
  return [...data].sort((a, b) => {
    const aVal = a[sortBy]
    const bVal = b[sortBy]
    
    if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1
    if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1
    return 0
  })
}

// 创建标准的CRUD操作
export function createCrudOperations(dataArray, options = {}) {
  const {
    idField = 'id',
    searchFields = [],
    defaultSort = 'id'
  } = options
  
  return {
    // 获取列表
    getList: ({ params = {} }) => {
      let result = [...dataArray]
      
      // 搜索过滤
      if (searchFields.length > 0 && params.search) {
        result = result.filter(item => 
          searchFields.some(field => 
            item[field] && item[field].toString().toLowerCase().includes(params.search.toLowerCase())
          )
        )
      }
      
      // 其他过滤
      const filters = { ...params }
      delete filters.page
      delete filters.pageSize
      delete filters.sortBy
      delete filters.sortOrder
      delete filters.search
      
      result = filterData(result, filters)
      
      // 排序
      if (params.sortBy) {
        result = sortData(result, params.sortBy, params.sortOrder || 'asc')
      } else {
        result = sortData(result, defaultSort, 'desc')
      }
      
      // 分页
      return paginate(result, params.page, params.pageSize)
    },
    
    // 根据ID获取
    getById: ({ pathParams }) => {
      const id = pathParams[0]
      return dataArray.find(item => item[idField].toString() === id.toString())
    },
    
    // 创建
    create: ({ data }) => {
      const newItem = {
        ...data,
        [idField]: generateId(),
        createdAt: formatDate(new Date()),
        updatedAt: formatDate(new Date())
      }
      dataArray.unshift(newItem)
      return newItem
    },
    
    // 更新
    update: ({ pathParams, data }) => {
      const id = pathParams[0]
      const index = dataArray.findIndex(item => item[idField].toString() === id.toString())
      
      if (index === -1) return null
      
      dataArray[index] = {
        ...dataArray[index],
        ...data,
        updatedAt: formatDate(new Date())
      }
      
      return dataArray[index]
    },
    
    // 删除
    delete: ({ pathParams }) => {
      const id = pathParams[0]
      const index = dataArray.findIndex(item => item[idField].toString() === id.toString())
      
      if (index === -1) return false
      
      dataArray.splice(index, 1)
      return true
    }
  }
}

export default {
  generateId,
  randomInt,
  randomDate,
  formatDate,
  randomChoice,
  randomChoices,
  randomString,
  randomChineseName,
  randomEmail,
  randomPhone,
  paginate,
  filterData,
  sortData,
  createCrudOperations
}
