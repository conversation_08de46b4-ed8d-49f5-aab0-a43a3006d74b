// Mock数据系统主入口
import { setupMockInterceptor } from './interceptor'
import { mockConfig } from './config'

// 导入所有mock数据
import authData from './data/auth'
import dashboardData from './data/dashboard'
import usersData from './data/users'
import servicesData from './data/services'
import categoriesData from './data/categories'
import tagsData from './data/tags'
import tasksData from './data/tasks'
import logsData from './data/logs'
import apiTokensData from './data/apiTokens'
import usageLogsData from './data/usageLogs'
import walletData from './data/wallet'
import settingsData from './data/settings'
import devToolsData from './data/devTools'
import promptsData from './data/prompts'
import contentData from './data/content'
import rssData from './data/rss'
import accountBindingsData from './data/accountBindings'

// Mock数据存储
export const mockDataStore = {
  auth: authData,
  dashboard: dashboardData,
  users: usersData,
  services: servicesData,
  categories: categoriesData,
  tags: tagsData,
  tasks: tasksData,
  logs: logsData,
  apiTokens: apiTokensData,
  usageLogs: usageLogsData,
  wallet: walletData,
  settings: settingsData,
  devTools: devToolsData,
  prompts: promptsData,
  content: contentData,
  rss: rssData,
  accountBindings: accountBindingsData
}

// 初始化Mock系统
export function initMockSystem() {
  console.log('🎭 Mock系统初始化开始...')
  console.log('Mock配置:', {
    enabled: mockConfig.enabled,
    VITE_USE_MOCK: import.meta.env.VITE_USE_MOCK,
    DEV: import.meta.env.DEV
  })

  if (mockConfig.enabled) {
    console.log('🎭 Mock系统已启用')
    setupMockInterceptor()

    // 在开发环境下显示mock状态
    if (import.meta.env.DEV) {
      console.log('📊 Mock数据模块:', Object.keys(mockDataStore))
    }

    console.log('✅ Mock系统初始化完成')
  } else {
    console.log('🔗 使用真实API')
  }
}

// 获取mock数据
export function getMockData(module, params = {}) {
  const data = mockDataStore[module]
  if (!data) {
    console.warn(`Mock数据模块 "${module}" 不存在`)
    return { items: [], total: 0 }
  }
  
  return data.getList ? data.getList(params) : data
}

// 获取单个mock数据项
export function getMockItem(module, id) {
  const data = mockDataStore[module]
  if (!data || !data.getById) {
    console.warn(`Mock数据模块 "${module}" 不存在或不支持getById`)
    return null
  }
  
  return data.getById(id)
}

// 创建mock数据项
export function createMockItem(module, itemData) {
  const data = mockDataStore[module]
  if (!data || !data.create) {
    console.warn(`Mock数据模块 "${module}" 不存在或不支持create`)
    return null
  }
  
  return data.create(itemData)
}

// 更新mock数据项
export function updateMockItem(module, id, itemData) {
  const data = mockDataStore[module]
  if (!data || !data.update) {
    console.warn(`Mock数据模块 "${module}" 不存在或不支持update`)
    return null
  }
  
  return data.update(id, itemData)
}

// 删除mock数据项
export function deleteMockItem(module, id) {
  const data = mockDataStore[module]
  if (!data || !data.delete) {
    console.warn(`Mock数据模块 "${module}" 不存在或不支持delete`)
    return false
  }
  
  return data.delete(id)
}

export default {
  initMockSystem,
  getMockData,
  getMockItem,
  createMockItem,
  updateMockItem,
  deleteMockItem,
  mockDataStore
}
