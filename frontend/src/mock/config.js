// Mock系统配置
export const mockConfig = {
  // 是否启用Mock系统
  enabled: import.meta.env.VITE_USE_MOCK === 'true' || import.meta.env.DEV,
  
  // Mock API基础URL
  baseURL: '/api',
  
  // 响应延迟（毫秒）
  delay: {
    min: 100,
    max: 800
  },
  
  // 分页配置
  pagination: {
    defaultPageSize: 10,
    maxPageSize: 100
  },
  
  // 错误模拟配置
  errorSimulation: {
    enabled: false, // 是否启用错误模拟
    rate: 0.1, // 错误率 (0-1)
    types: ['network', 'server', 'timeout']
  },
  
  // 日志配置
  logging: {
    enabled: import.meta.env.DEV,
    level: 'info' // 'debug', 'info', 'warn', 'error'
  },
  
  // API路径映射
  apiMapping: {
    // 用户相关
    'GET /api/users': 'users.getList',
    'GET /api/users/:id': 'users.getById',
    'POST /api/users': 'users.create',
    'PUT /api/users/:id': 'users.update',
    'DELETE /api/users/:id': 'users.delete',
    
    // 服务相关
    'GET /api/services': 'services.getList',
    'GET /api/services/:id': 'services.getById',
    'POST /api/services': 'services.create',
    'PUT /api/services/:id': 'services.update',
    'DELETE /api/services/:id': 'services.delete',
    
    // 分类相关
    'GET /api/categories': 'categories.getList',
    'GET /api/categories/:id': 'categories.getById',
    'POST /api/categories': 'categories.create',
    'PUT /api/categories/:id': 'categories.update',
    'DELETE /api/categories/:id': 'categories.delete',
    
    // 标签相关
    'GET /api/tags': 'tags.getList',
    'GET /api/tags/:id': 'tags.getById',
    'POST /api/tags': 'tags.create',
    'PUT /api/tags/:id': 'tags.update',
    'DELETE /api/tags/:id': 'tags.delete',
    
    // 任务相关
    'GET /api/tasks': 'tasks.getList',
    'GET /api/tasks/:id': 'tasks.getById',
    'POST /api/tasks': 'tasks.create',
    'PUT /api/tasks/:id': 'tasks.update',
    'DELETE /api/tasks/:id': 'tasks.delete',
    
    // 日志相关
    'GET /api/logs': 'logs.getList',
    'GET /api/logs/:id': 'logs.getById',
    
    // API令牌相关
    'GET /api/api-tokens': 'apiTokens.getList',
    'GET /api/api-tokens/:id': 'apiTokens.getById',
    'POST /api/api-tokens': 'apiTokens.create',
    'PUT /api/api-tokens/:id': 'apiTokens.update',
    'DELETE /api/api-tokens/:id': 'apiTokens.delete',
    
    // 使用日志相关
    'GET /api/usage-logs': 'usageLogs.getList',
    'GET /api/usage-logs/:id': 'usageLogs.getById',
    
    // 钱包相关
    'GET /api/wallet': 'wallet.getInfo',
    'GET /api/wallet/transactions': 'wallet.getTransactions',
    'POST /api/wallet/recharge': 'wallet.recharge',
    
    // 设置相关
    'GET /api/settings': 'settings.getAll',
    'PUT /api/settings': 'settings.update',
    
    // 开发工具相关
    'GET /api/dev-tools': 'devTools.getList',
    'GET /api/dev-tools/:id': 'devTools.getById',
    
    // AI提示词相关
    'GET /api/prompts': 'prompts.getList',
    'GET /api/prompts/:id': 'prompts.getById',
    'POST /api/prompts': 'prompts.create',
    'PUT /api/prompts/:id': 'prompts.update',
    'DELETE /api/prompts/:id': 'prompts.delete',
    
    // 内容相关
    'GET /api/content': 'content.getList',
    'GET /api/content/:id': 'content.getById',
    'POST /api/content': 'content.create',
    'PUT /api/content/:id': 'content.update',
    'DELETE /api/content/:id': 'content.delete',
    
    // RSS相关
    'GET /api/rss': 'rss.getList',
    'GET /api/rss/:id': 'rss.getById',
    'POST /api/rss': 'rss.create',
    'PUT /api/rss/:id': 'rss.update',
    'DELETE /api/rss/:id': 'rss.delete',
    
    // 账号绑定相关
    'GET /api/account-bindings': 'accountBindings.getList',
    'POST /api/account-bindings': 'accountBindings.create',
    'DELETE /api/account-bindings/:id': 'accountBindings.delete',
    
    // 仪表板相关
    'GET /api/dashboard/stats': 'dashboard.getStats',
    'GET /api/dashboard/charts': 'dashboard.getCharts',
    'GET /api/dashboard/recent': 'dashboard.getRecent',
    
    // 认证相关
    'POST /api/auth/login': 'auth.login',
    'POST /api/auth/logout': 'auth.logout',
    'GET /api/auth/profile': 'auth.getProfile',
    'PUT /api/auth/profile': 'auth.updateProfile'
  }
}

export default mockConfig
