// Users页面Mock数据
import { generateId, randomChineseName, randomEmail, randomPhone, randomDate, formatDate, randomChoice, createCrudOperations } from '../utils'

// 用户角色选项
const roles = ['管理员', '普通用户', '访客', '开发者', '运维人员']

// 用户状态选项
const statuses = ['正常', '禁用', '待激活', '已锁定']

// 部门选项
const departments = ['技术部', '产品部', '运营部', '市场部', '人事部', '财务部']

// 生成用户数据
function generateUser(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const lastLoginAt = randomDate(createdAt, new Date())
  
  return {
    id: id || generateId(),
    username: `user_${Math.random().toString(36).substr(2, 8)}`,
    name: randomChineseName(),
    email: randomEmail(),
    phone: randomPhone(),
    role: randomChoice(roles),
    status: randomChoice(statuses),
    department: randomChoice(departments),
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${Math.random()}`,
    lastLoginAt: formatDate(lastLoginAt),
    loginCount: Math.floor(Math.random() * 1000),
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    description: `这是${randomChineseName()}的个人描述信息`,
    permissions: randomChoice([
      ['read', 'write'],
      ['read'],
      ['read', 'write', 'delete'],
      ['read', 'write', 'delete', 'admin']
    ])
  }
}

// 生成用户列表数据
const usersData = Array.from({ length: 156 }, (_, index) => generateUser(index + 1))

// 用户统计数据
const userStats = {
  total: usersData.length,
  active: usersData.filter(u => u.status === '正常').length,
  disabled: usersData.filter(u => u.status === '禁用').length,
  pending: usersData.filter(u => u.status === '待激活').length,
  locked: usersData.filter(u => u.status === '已锁定').length,
  byRole: roles.reduce((acc, role) => {
    acc[role] = usersData.filter(u => u.role === role).length
    return acc
  }, {}),
  byDepartment: departments.reduce((acc, dept) => {
    acc[dept] = usersData.filter(u => u.department === dept).length
    return acc
  }, {})
}

// 创建CRUD操作
const crudOps = createCrudOperations(usersData, {
  idField: 'id',
  searchFields: ['username', 'name', 'email', 'phone'],
  defaultSort: 'createdAt'
})

// 导出Users mock数据操作
export default {
  ...crudOps,
  
  // 获取用户统计
  getStats: () => userStats,
  
  // 批量操作
  batchUpdate: ({ data }) => {
    const { ids, updates } = data
    let updatedCount = 0
    
    ids.forEach(id => {
      const index = usersData.findIndex(user => user.id.toString() === id.toString())
      if (index !== -1) {
        usersData[index] = {
          ...usersData[index],
          ...updates,
          updatedAt: formatDate(new Date())
        }
        updatedCount++
      }
    })
    
    return { updatedCount }
  },
  
  // 重置密码
  resetPassword: ({ pathParams }) => {
    const id = pathParams[0]
    const user = usersData.find(u => u.id.toString() === id.toString())
    
    if (!user) return null
    
    // 模拟重置密码
    const newPassword = Math.random().toString(36).substr(2, 8)
    
    return {
      success: true,
      newPassword,
      message: '密码重置成功'
    }
  },
  
  // 切换用户状态
  toggleStatus: ({ pathParams }) => {
    const id = pathParams[0]
    const user = usersData.find(u => u.id.toString() === id.toString())
    
    if (!user) return null
    
    user.status = user.status === '正常' ? '禁用' : '正常'
    user.updatedAt = formatDate(new Date())
    
    return user
  },
  
  // 获取用户权限
  getPermissions: ({ pathParams }) => {
    const id = pathParams[0]
    const user = usersData.find(u => u.id.toString() === id.toString())
    
    if (!user) return null
    
    return {
      userId: user.id,
      permissions: user.permissions,
      role: user.role
    }
  },
  
  // 更新用户权限
  updatePermissions: ({ pathParams, data }) => {
    const id = pathParams[0]
    const user = usersData.find(u => u.id.toString() === id.toString())
    
    if (!user) return null
    
    user.permissions = data.permissions
    user.updatedAt = formatDate(new Date())
    
    return user
  }
}
