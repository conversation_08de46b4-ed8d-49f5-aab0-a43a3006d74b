// Prompts页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, randomChineseName, createCrudOperations } from '../utils'

// 提示词分类
const promptCategories = ['代码生成', '文档编写', '数据分析', '创意写作', '问题解答', '翻译', '总结', '优化建议']

// 提示词状态
const promptStatuses = ['已发布', '草稿', '待审核', '已归档']

// 提示词标签
const promptTags = ['GPT-4', 'Claude', '编程', '写作', '分析', '创意', '效率', '专业', '通用', '高级']

// 模型类型
const modelTypes = ['GPT-4', 'GPT-3.5', 'Claude-3', 'Claude-2', 'Gemini', 'LLaMA']

// 生成提示词数据
function generatePrompt(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const category = randomChoice(promptCategories)
  const usageCount = randomInt(0, 1000)
  const rating = parseFloat((Math.random() * 2 + 3).toFixed(1)) // 3.0-5.0
  
  const promptTemplates = {
    '代码生成': [
      '请帮我生成一个{language}的{功能}函数，要求：{要求}',
      '编写一个{framework}组件，实现{功能}，包含{特性}',
      '创建一个{数据库}的查询语句，用于{目的}'
    ],
    '文档编写': [
      '为{项目}编写技术文档，包含{内容}',
      '生成{API}的使用说明，格式为{格式}',
      '创建{功能}的用户手册，面向{用户群体}'
    ],
    '数据分析': [
      '分析以下数据：{数据}，请提供{分析类型}',
      '对{数据集}进行{分析方法}分析，重点关注{关注点}',
      '根据{指标}数据，给出{建议类型}建议'
    ]
  }
  
  const templates = promptTemplates[category] || ['请帮我{任务}，要求{要求}']
  const content = randomChoice(templates)
  
  return {
    id: id || generateId(),
    title: `${category}提示词${id || Math.random().toString(36).substr(2, 4)}`,
    description: `这是一个专门用于${category}的AI提示词模板，可以帮助用户快速生成高质量的内容。`,
    content,
    category,
    status: randomChoice(promptStatuses),
    tags: randomChoice([
      [randomChoice(promptTags), randomChoice(promptTags)],
      [randomChoice(promptTags)],
      [randomChoice(promptTags), randomChoice(promptTags), randomChoice(promptTags)]
    ]),
    author: randomChineseName(),
    modelType: randomChoice(modelTypes),
    language: randomChoice(['中文', '英文', '双语']),
    difficulty: randomChoice(['初级', '中级', '高级', '专家']),
    usageCount,
    rating,
    ratingCount: randomInt(10, 200),
    isPublic: Math.random() > 0.3, // 70%概率是公开的
    isFavorite: Math.random() > 0.8, // 20%概率被收藏
    isTemplate: Math.random() > 0.6, // 40%概率是模板
    version: `v${randomInt(1, 3)}.${randomInt(0, 9)}`,
    parameters: randomChoice([
      [
        { name: 'language', type: 'select', options: ['JavaScript', 'Python', 'Java', 'Go'], required: true },
        { name: '功能', type: 'text', placeholder: '请输入功能描述', required: true }
      ],
      [
        { name: '项目', type: 'text', placeholder: '项目名称', required: true },
        { name: '内容', type: 'textarea', placeholder: '详细内容', required: false }
      ],
      []
    ]),
    examples: [
      {
        input: '生成一个Python的排序函数',
        output: '这是一个示例输出结果...',
        description: '基本排序功能示例'
      }
    ],
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    lastUsedAt: usageCount > 0 ? formatDate(randomDate(createdAt, new Date())) : null,
    settings: {
      temperature: parseFloat((Math.random() * 0.8 + 0.2).toFixed(1)), // 0.2-1.0
      maxTokens: randomChoice([1000, 2000, 4000, 8000]),
      topP: parseFloat((Math.random() * 0.5 + 0.5).toFixed(1)), // 0.5-1.0
      frequencyPenalty: parseFloat((Math.random() * 0.4).toFixed(1)), // 0.0-0.4
      presencePenalty: parseFloat((Math.random() * 0.4).toFixed(1)) // 0.0-0.4
    },
    metrics: {
      successRate: parseFloat((Math.random() * 0.3 + 0.7).toFixed(2)), // 70%-100%
      avgResponseTime: randomInt(500, 3000), // 毫秒
      avgTokenUsage: randomInt(100, 1000),
      userSatisfaction: parseFloat((Math.random() * 1.5 + 3.5).toFixed(1)) // 3.5-5.0
    }
  }
}

// 生成提示词列表数据
const promptsData = Array.from({ length: 89 }, (_, index) => generatePrompt(index + 1))

// 提示词统计数据
const promptStats = {
  total: promptsData.length,
  published: promptsData.filter(p => p.status === '已发布').length,
  draft: promptsData.filter(p => p.status === '草稿').length,
  pending: promptsData.filter(p => p.status === '待审核').length,
  archived: promptsData.filter(p => p.status === '已归档').length,
  public: promptsData.filter(p => p.isPublic).length,
  templates: promptsData.filter(p => p.isTemplate).length,
  totalUsage: promptsData.reduce((sum, p) => sum + p.usageCount, 0),
  avgRating: parseFloat((promptsData.reduce((sum, p) => sum + p.rating, 0) / promptsData.length).toFixed(1)),
  byCategory: promptCategories.reduce((acc, category) => {
    acc[category] = promptsData.filter(p => p.category === category).length
    return acc
  }, {}),
  byModel: modelTypes.reduce((acc, model) => {
    acc[model] = promptsData.filter(p => p.modelType === model).length
    return acc
  }, {})
}

// 创建CRUD操作
const crudOps = createCrudOperations(promptsData, {
  idField: 'id',
  searchFields: ['title', 'description', 'content', 'author'],
  defaultSort: 'usageCount'
})

// 导出Prompts mock数据操作
export default {
  ...crudOps,
  
  // 获取提示词统计
  getStats: () => promptStats,
  
  // 获取热门提示词
  getPopular: ({ params = {} }) => {
    const { limit = 20 } = params
    return promptsData
      .filter(p => p.status === '已发布' && p.isPublic)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, limit)
  },
  
  // 获取推荐提示词
  getRecommended: ({ params = {} }) => {
    const { category, limit = 10 } = params
    let result = promptsData.filter(p => p.status === '已发布' && p.isPublic)
    
    if (category) {
      result = result.filter(p => p.category === category)
    }
    
    return result
      .sort((a, b) => (b.rating * b.usageCount) - (a.rating * a.usageCount))
      .slice(0, limit)
  },
  
  // 使用提示词
  use: ({ pathParams, data }) => {
    const id = pathParams[0]
    const prompt = promptsData.find(p => p.id.toString() === id.toString())
    
    if (!prompt) return null
    
    prompt.usageCount++
    prompt.lastUsedAt = formatDate(new Date())
    prompt.updatedAt = formatDate(new Date())
    
    // 模拟AI响应
    return {
      promptId: prompt.id,
      input: data.input || '',
      output: `这是基于提示词"${prompt.title}"生成的示例输出内容...`,
      tokensUsed: randomInt(100, 1000),
      responseTime: randomInt(500, 3000),
      timestamp: Date.now()
    }
  },
  
  // 收藏/取消收藏
  toggleFavorite: ({ pathParams }) => {
    const id = pathParams[0]
    const prompt = promptsData.find(p => p.id.toString() === id.toString())
    
    if (!prompt) return null
    
    prompt.isFavorite = !prompt.isFavorite
    prompt.updatedAt = formatDate(new Date())
    
    return prompt
  },
  
  // 评分
  rate: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { rating } = data
    const prompt = promptsData.find(p => p.id.toString() === id.toString())
    
    if (!prompt || rating < 1 || rating > 5) return null
    
    // 重新计算平均评分
    const totalRating = prompt.rating * prompt.ratingCount + rating
    prompt.ratingCount++
    prompt.rating = parseFloat((totalRating / prompt.ratingCount).toFixed(1))
    prompt.updatedAt = formatDate(new Date())
    
    return prompt
  },
  
  // 复制提示词
  duplicate: ({ pathParams }) => {
    const id = pathParams[0]
    const original = promptsData.find(p => p.id.toString() === id.toString())
    
    if (!original) return null
    
    const duplicate = {
      ...original,
      id: generateId(),
      title: `${original.title} (副本)`,
      status: '草稿',
      usageCount: 0,
      rating: 0,
      ratingCount: 0,
      isFavorite: false,
      createdAt: formatDate(new Date()),
      updatedAt: formatDate(new Date()),
      lastUsedAt: null
    }
    
    promptsData.unshift(duplicate)
    return duplicate
  },
  
  // 获取分类列表
  getCategories: () => {
    return promptCategories.map(category => ({
      name: category,
      count: promptsData.filter(p => p.category === category).length,
      avgRating: parseFloat((
        promptsData
          .filter(p => p.category === category)
          .reduce((sum, p) => sum + p.rating, 0) / 
        promptsData.filter(p => p.category === category).length
      ).toFixed(1))
    }))
  },
  
  // 批量操作
  batchUpdate: ({ data }) => {
    const { ids, updates } = data
    let updatedCount = 0
    
    ids.forEach(id => {
      const prompt = promptsData.find(p => p.id.toString() === id.toString())
      if (prompt) {
        Object.assign(prompt, updates, { updatedAt: formatDate(new Date()) })
        updatedCount++
      }
    })
    
    return { updatedCount }
  }
}
