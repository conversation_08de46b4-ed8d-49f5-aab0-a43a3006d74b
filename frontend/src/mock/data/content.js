// Content页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, randomChineseName, createCrudOperations } from '../utils'

// 内容类型
const contentTypes = ['规范文档', '操作手册', '技术文档', '培训材料', '产品说明', 'API文档']

// 内容状态
const contentStatuses = ['已发布', '待审核', '草稿', '已归档']

// 内容格式
const contentFormats = ['Markdown', 'HTML', 'PDF', 'Word', 'PPT', '纯文本']

// 生成内容数据
function generateContent(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const type = randomChoice(contentTypes)
  const viewCount = randomInt(0, 5000)
  
  const titles = {
    '规范文档': ['API开发规范', '代码审查标准', '数据库设计规范', '安全开发指南'],
    '操作手册': ['系统部署手册', '用户操作指南', '故障排查手册', '备份恢复流程'],
    '技术文档': ['架构设计文档', '接口文档', '数据库设计', '性能优化指南'],
    '培训材料': ['新员工培训', '技术分享', '产品培训', '安全培训'],
    '产品说明': ['产品介绍', '功能说明', '使用指南', '更新日志'],
    'API文档': ['REST API文档', 'GraphQL文档', 'SDK使用指南', '接口规范']
  }
  
  return {
    id: id || generateId(),
    title: randomChoice(titles[type] || ['文档标题']),
    description: `这是一份${type}，详细介绍了相关的内容和操作流程，为团队提供标准化的指导。`,
    content: `# ${randomChoice(titles[type] || ['文档标题'])}\n\n## 概述\n\n这是文档的主要内容...\n\n## 详细说明\n\n具体的操作步骤和说明...\n\n## 注意事项\n\n需要注意的重要事项...`,
    type,
    status: randomChoice(contentStatuses),
    format: randomChoice(contentFormats),
    author: randomChineseName(),
    editor: Math.random() > 0.5 ? randomChineseName() : null,
    version: `v${randomInt(1, 5)}.${randomInt(0, 9)}`,
    wordCount: randomInt(500, 10000),
    readTime: randomInt(2, 30), // 分钟
    viewCount,
    downloadCount: randomInt(0, Math.floor(viewCount * 0.3)),
    likeCount: randomInt(0, Math.floor(viewCount * 0.1)),
    commentCount: randomInt(0, 50),
    isPublic: Math.random() > 0.3,
    isFeatured: Math.random() > 0.8,
    isPinned: Math.random() > 0.9,
    tags: randomChoice([
      ['开发', '规范'],
      ['操作', '手册'],
      ['技术', '文档'],
      ['培训', '学习'],
      ['产品', '说明']
    ]),
    categories: randomChoice([
      ['技术文档'],
      ['产品文档'],
      ['运营文档'],
      ['管理文档']
    ]),
    attachments: Math.random() > 0.6 ? Array.from({ length: randomInt(1, 3) }, (_, i) => ({
      id: generateId(),
      name: `附件${i + 1}.${randomChoice(['pdf', 'doc', 'xlsx', 'png'])}`,
      size: randomInt(100, 5000) + 'KB',
      downloadCount: randomInt(0, 100)
    })) : [],
    relatedContent: randomChoice([
      [],
      [randomInt(1, 50)],
      [randomInt(1, 50), randomInt(1, 50)]
    ]),
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    publishedAt: randomChoice(contentStatuses) === '已发布' ? formatDate(randomDate(createdAt, new Date())) : null,
    seo: {
      title: `${randomChoice(titles[type] || ['文档'])} - 企业文档中心`,
      description: `详细的${type}，包含完整的操作指南和最佳实践`,
      keywords: randomChoice([
        '文档,规范,指南',
        '操作,手册,流程',
        '技术,开发,标准'
      ])
    },
    permissions: {
      read: randomChoice(['public', 'internal', 'restricted']),
      write: randomChoice(['author', 'editors', 'admins']),
      delete: 'admins'
    },
    workflow: {
      needsApproval: Math.random() > 0.7,
      approver: randomChineseName(),
      approvedAt: null,
      rejectedAt: null,
      rejectionReason: null
    }
  }
}

// 生成内容列表数据
const contentData = Array.from({ length: 156 }, (_, index) => generateContent(index + 1))

// 内容统计数据
const contentStats = {
  total: contentData.length,
  published: contentData.filter(c => c.status === '已发布').length,
  draft: contentData.filter(c => c.status === '草稿').length,
  pending: contentData.filter(c => c.status === '待审核').length,
  archived: contentData.filter(c => c.status === '已归档').length,
  public: contentData.filter(c => c.isPublic).length,
  featured: contentData.filter(c => c.isFeatured).length,
  totalViews: contentData.reduce((sum, c) => sum + c.viewCount, 0),
  totalDownloads: contentData.reduce((sum, c) => sum + c.downloadCount, 0),
  totalWords: contentData.reduce((sum, c) => sum + c.wordCount, 0),
  byType: contentTypes.reduce((acc, type) => {
    acc[type] = contentData.filter(c => c.type === type).length
    return acc
  }, {}),
  byFormat: contentFormats.reduce((acc, format) => {
    acc[format] = contentData.filter(c => c.format === format).length
    return acc
  }, {})
}

// 创建CRUD操作
const crudOps = createCrudOperations(contentData, {
  idField: 'id',
  searchFields: ['title', 'description', 'content', 'author'],
  defaultSort: 'updatedAt'
})

// 导出Content mock数据操作
export default {
  ...crudOps,
  
  // 获取内容统计
  getStats: () => contentStats,
  
  // 获取热门内容
  getPopular: ({ params = {} }) => {
    const { limit = 20 } = params
    return contentData
      .filter(c => c.status === '已发布' && c.isPublic)
      .sort((a, b) => b.viewCount - a.viewCount)
      .slice(0, limit)
  },
  
  // 获取推荐内容
  getFeatured: ({ params = {} }) => {
    const { limit = 10 } = params
    return contentData
      .filter(c => c.status === '已发布' && c.isFeatured)
      .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
      .slice(0, limit)
  },
  
  // 获取最新内容
  getLatest: ({ params = {} }) => {
    const { limit = 10 } = params
    return contentData
      .filter(c => c.status === '已发布' && c.isPublic)
      .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
      .slice(0, limit)
  },
  
  // 查看内容（增加浏览量）
  view: ({ pathParams }) => {
    const id = pathParams[0]
    const content = contentData.find(c => c.id.toString() === id.toString())
    
    if (!content) return null
    
    content.viewCount++
    return content
  },
  
  // 下载内容
  download: ({ pathParams }) => {
    const id = pathParams[0]
    const content = contentData.find(c => c.id.toString() === id.toString())
    
    if (!content) return null
    
    content.downloadCount++
    return {
      contentId: content.id,
      downloadUrl: `/api/content/${id}/download`,
      filename: `${content.title}.${content.format.toLowerCase()}`,
      size: content.wordCount * 2 + 'KB'
    }
  },
  
  // 点赞内容
  like: ({ pathParams }) => {
    const id = pathParams[0]
    const content = contentData.find(c => c.id.toString() === id.toString())
    
    if (!content) return null
    
    content.likeCount++
    return content
  },
  
  // 发布内容
  publish: ({ pathParams }) => {
    const id = pathParams[0]
    const content = contentData.find(c => c.id.toString() === id.toString())
    
    if (!content) return null
    
    content.status = '已发布'
    content.publishedAt = formatDate(new Date())
    content.updatedAt = formatDate(new Date())
    
    return content
  },
  
  // 归档内容
  archive: ({ pathParams }) => {
    const id = pathParams[0]
    const content = contentData.find(c => c.id.toString() === id.toString())
    
    if (!content) return null
    
    content.status = '已归档'
    content.updatedAt = formatDate(new Date())
    
    return content
  },
  
  // 复制内容
  duplicate: ({ pathParams }) => {
    const id = pathParams[0]
    const original = contentData.find(c => c.id.toString() === id.toString())
    
    if (!original) return null
    
    const duplicate = {
      ...original,
      id: generateId(),
      title: `${original.title} (副本)`,
      status: '草稿',
      viewCount: 0,
      downloadCount: 0,
      likeCount: 0,
      commentCount: 0,
      createdAt: formatDate(new Date()),
      updatedAt: formatDate(new Date()),
      publishedAt: null
    }
    
    contentData.unshift(duplicate)
    return duplicate
  },
  
  // 获取内容历史版本
  getVersions: ({ pathParams }) => {
    const id = pathParams[0]
    
    // 模拟版本历史
    return Array.from({ length: randomInt(1, 5) }, (_, i) => ({
      id: generateId(),
      version: `v${randomInt(1, 3)}.${i}`,
      author: randomChineseName(),
      changes: randomChoice([
        '更新了内容结构',
        '修复了错误信息',
        '添加了新的章节',
        '优化了格式',
        '更新了示例代码'
      ]),
      createdAt: formatDate(randomDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()))
    })).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
  }
}
