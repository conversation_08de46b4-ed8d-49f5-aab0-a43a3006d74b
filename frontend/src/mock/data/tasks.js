// Tasks页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, randomChineseName, createCrudOperations } from '../utils'

// 任务状态
const taskStatuses = ['待开始', '进行中', '已完成', '已取消', '已暂停']

// 任务优先级
const taskPriorities = ['低', '中', '高', '紧急']

// 任务类型
const taskTypes = ['开发任务', '测试任务', '部署任务', '维护任务', '文档任务', '培训任务']

// 任务分组
const taskGroups = ['前端开发', '后端开发', '数据库', '运维部署', '测试验证', '产品设计']

// 生成任务数据
function generateTask(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const status = randomChoice(taskStatuses)
  const priority = randomChoice(taskPriorities)
  const estimatedHours = randomInt(1, 40)
  const actualHours = status === '已完成' ? randomInt(1, estimatedHours + 10) : randomInt(0, estimatedHours)
  
  // 根据状态设置日期
  let startDate = null
  let endDate = null
  let dueDate = randomDate(createdAt, new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
  
  if (status === '进行中' || status === '已完成' || status === '已暂停') {
    startDate = randomDate(createdAt, new Date())
  }
  
  if (status === '已完成') {
    endDate = randomDate(startDate || createdAt, new Date())
  }
  
  return {
    id: id || generateId(),
    title: `任务${id || Math.random().toString(36).substr(2, 4)} - ${randomChoice(['开发', '测试', '部署', '优化', '修复'])}${randomChoice(['用户模块', '订单系统', '支付接口', '数据分析', '性能监控'])}`,
    description: `这是一个${randomChoice(taskTypes)}，需要完成相关的开发和测试工作。任务包含多个子项目，需要团队协作完成。`,
    status,
    priority,
    type: randomChoice(taskTypes),
    group: randomChoice(taskGroups),
    assignee: randomChineseName(),
    creator: randomChineseName(),
    estimatedHours,
    actualHours,
    progress: status === '已完成' ? 100 : (status === '进行中' ? randomInt(10, 90) : 0),
    tags: randomChoice([
      ['前端', 'React'],
      ['后端', 'API'],
      ['数据库', 'MySQL'],
      ['部署', 'Docker'],
      ['测试', '自动化'],
      ['文档', 'API文档']
    ]),
    startDate: startDate ? formatDate(startDate) : null,
    endDate: endDate ? formatDate(endDate) : null,
    dueDate: formatDate(dueDate),
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    dependencies: Math.random() > 0.7 ? [randomInt(1, 50)] : [], // 30%概率有依赖
    subtasks: Array.from({ length: randomInt(0, 5) }, (_, i) => ({
      id: generateId(),
      title: `子任务${i + 1}`,
      completed: Math.random() > 0.5,
      assignee: randomChineseName()
    })),
    comments: Array.from({ length: randomInt(0, 8) }, (_, i) => ({
      id: generateId(),
      author: randomChineseName(),
      content: randomChoice([
        '任务进展顺利，预计按时完成',
        '遇到一些技术难题，需要额外时间',
        '已完成初步开发，等待测试',
        '需要产品确认具体需求',
        '代码已提交，请进行代码审查'
      ]),
      createdAt: formatDate(randomDate(createdAt, new Date()))
    })),
    attachments: Math.random() > 0.6 ? Array.from({ length: randomInt(1, 3) }, (_, i) => ({
      id: generateId(),
      name: `附件${i + 1}.${randomChoice(['pdf', 'doc', 'xlsx', 'png', 'jpg'])}`,
      size: randomInt(100, 5000) + 'KB',
      uploadedBy: randomChineseName(),
      uploadedAt: formatDate(randomDate(createdAt, new Date()))
    })) : [],
    workLogs: Array.from({ length: randomInt(0, 10) }, (_, i) => ({
      id: generateId(),
      date: formatDate(randomDate(startDate || createdAt, new Date())),
      hours: randomInt(1, 8),
      description: randomChoice([
        '完成需求分析和设计',
        '编写核心业务逻辑',
        '进行单元测试',
        '修复发现的bug',
        '优化代码性能',
        '编写技术文档'
      ]),
      author: randomChineseName()
    }))
  }
}

// 生成任务列表数据
const tasksData = Array.from({ length: 127 }, (_, index) => generateTask(index + 1))

// 任务统计数据
const taskStats = {
  total: tasksData.length,
  pending: tasksData.filter(t => t.status === '待开始').length,
  inProgress: tasksData.filter(t => t.status === '进行中').length,
  completed: tasksData.filter(t => t.status === '已完成').length,
  cancelled: tasksData.filter(t => t.status === '已取消').length,
  paused: tasksData.filter(t => t.status === '已暂停').length,
  overdue: tasksData.filter(t => new Date(t.dueDate) < new Date() && t.status !== '已完成').length,
  byPriority: taskPriorities.reduce((acc, priority) => {
    acc[priority] = tasksData.filter(t => t.priority === priority).length
    return acc
  }, {}),
  byType: taskTypes.reduce((acc, type) => {
    acc[type] = tasksData.filter(t => t.type === type).length
    return acc
  }, {}),
  byGroup: taskGroups.reduce((acc, group) => {
    acc[group] = tasksData.filter(t => t.group === group).length
    return acc
  }, {}),
  totalEstimatedHours: tasksData.reduce((sum, t) => sum + t.estimatedHours, 0),
  totalActualHours: tasksData.reduce((sum, t) => sum + t.actualHours, 0)
}

// 创建CRUD操作
const crudOps = createCrudOperations(tasksData, {
  idField: 'id',
  searchFields: ['title', 'description', 'assignee', 'creator'],
  defaultSort: 'createdAt'
})

// 导出Tasks mock数据操作
export default {
  ...crudOps,
  
  // 获取任务统计
  getStats: () => taskStats,
  
  // 更新任务状态
  updateStatus: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { status } = data
    const task = tasksData.find(t => t.id.toString() === id.toString())
    
    if (!task) return null
    
    task.status = status
    task.updatedAt = formatDate(new Date())
    
    // 根据状态更新相关字段
    if (status === '进行中' && !task.startDate) {
      task.startDate = formatDate(new Date())
    }
    
    if (status === '已完成') {
      task.endDate = formatDate(new Date())
      task.progress = 100
    }
    
    return task
  },
  
  // 更新任务进度
  updateProgress: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { progress } = data
    const task = tasksData.find(t => t.id.toString() === id.toString())
    
    if (!task) return null
    
    task.progress = Math.max(0, Math.min(100, progress))
    task.updatedAt = formatDate(new Date())
    
    // 自动更新状态
    if (task.progress === 100 && task.status !== '已完成') {
      task.status = '已完成'
      task.endDate = formatDate(new Date())
    } else if (task.progress > 0 && task.status === '待开始') {
      task.status = '进行中'
      task.startDate = formatDate(new Date())
    }
    
    return task
  },
  
  // 分配任务
  assign: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { assignee } = data
    const task = tasksData.find(t => t.id.toString() === id.toString())
    
    if (!task) return null
    
    task.assignee = assignee
    task.updatedAt = formatDate(new Date())
    
    return task
  },
  
  // 添加评论
  addComment: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { content, author } = data
    const task = tasksData.find(t => t.id.toString() === id.toString())
    
    if (!task) return null
    
    const comment = {
      id: generateId(),
      author,
      content,
      createdAt: formatDate(new Date())
    }
    
    task.comments.unshift(comment)
    task.updatedAt = formatDate(new Date())
    
    return comment
  },
  
  // 添加工作日志
  addWorkLog: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { date, hours, description, author } = data
    const task = tasksData.find(t => t.id.toString() === id.toString())
    
    if (!task) return null
    
    const workLog = {
      id: generateId(),
      date,
      hours,
      description,
      author
    }
    
    task.workLogs.unshift(workLog)
    task.actualHours += hours
    task.updatedAt = formatDate(new Date())
    
    return workLog
  },
  
  // 获取甘特图数据
  getGanttData: () => {
    return tasksData
      .filter(t => t.startDate && t.dueDate)
      .map(t => ({
        id: t.id,
        title: t.title,
        start: t.startDate,
        end: t.endDate || t.dueDate,
        progress: t.progress,
        status: t.status,
        assignee: t.assignee,
        dependencies: t.dependencies
      }))
  },
  
  // 获取我的任务
  getMyTasks: ({ params = {} }) => {
    const { assignee, status } = params
    let result = tasksData
    
    if (assignee) {
      result = result.filter(t => t.assignee === assignee)
    }
    
    if (status) {
      result = result.filter(t => t.status === status)
    }
    
    return result.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
  }
}
