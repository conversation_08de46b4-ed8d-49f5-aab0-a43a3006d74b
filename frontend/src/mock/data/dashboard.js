// Dashboard页面Mock数据
import { randomInt, randomDate, formatDate, randomChoice } from '../utils'

// 统计数据
const statsData = {
  users: {
    total: 1247,
    active: 892,
    new: 156,
    growth: '+12.5%'
  },
  services: {
    total: 45,
    running: 38,
    stopped: 7,
    growth: '+8.2%'
  },
  tasks: {
    total: 2341,
    completed: 1876,
    pending: 465,
    growth: '+15.3%'
  },
  revenue: {
    total: 89750,
    thisMonth: 12450,
    lastMonth: 11200,
    growth: '+11.2%'
  }
}

// 图表数据
const chartData = {
  userGrowth: {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      label: '用户增长',
      data: [120, 190, 300, 500, 200, 300],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4
    }]
  },
  serviceStatus: {
    labels: ['运行中', '已停止', '维护中', '错误'],
    datasets: [{
      data: [38, 7, 2, 3],
      backgroundColor: [
        'rgba(34, 197, 94, 0.8)',
        'rgba(239, 68, 68, 0.8)',
        'rgba(245, 158, 11, 0.8)',
        'rgba(156, 163, 175, 0.8)'
      ]
    }]
  },
  taskCompletion: {
    labels: ['已完成', '进行中', '待开始', '已取消'],
    datasets: [{
      data: [1876, 324, 141, 45],
      backgroundColor: [
        'rgba(34, 197, 94, 0.8)',
        'rgba(59, 130, 246, 0.8)',
        'rgba(245, 158, 11, 0.8)',
        'rgba(239, 68, 68, 0.8)'
      ]
    }]
  },
  revenueChart: {
    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
    datasets: [{
      label: '收入',
      data: [8500, 9200, 8800, 10500, 11200, 12450],
      borderColor: 'rgb(34, 197, 94)',
      backgroundColor: 'rgba(34, 197, 94, 0.1)',
      tension: 0.4
    }]
  }
}

// 最近活动数据
const recentActivities = [
  {
    id: 1,
    type: 'user',
    action: '新用户注册',
    user: '张三',
    time: formatDate(randomDate(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date())),
    icon: '👤',
    color: 'blue'
  },
  {
    id: 2,
    type: 'service',
    action: '服务部署完成',
    user: '李四',
    time: formatDate(randomDate(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date())),
    icon: '🚀',
    color: 'green'
  },
  {
    id: 3,
    type: 'task',
    action: '任务执行失败',
    user: '王五',
    time: formatDate(randomDate(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date())),
    icon: '❌',
    color: 'red'
  },
  {
    id: 4,
    type: 'system',
    action: '系统备份完成',
    user: '系统',
    time: formatDate(randomDate(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date())),
    icon: '💾',
    color: 'purple'
  },
  {
    id: 5,
    type: 'user',
    action: '用户登录',
    user: '赵六',
    time: formatDate(randomDate(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date())),
    icon: '🔑',
    color: 'blue'
  }
]

// 快速操作数据
const quickActions = [
  {
    id: 1,
    title: '创建新用户',
    description: '添加新的系统用户',
    icon: '👤',
    color: 'blue',
    route: '/users?action=create'
  },
  {
    id: 2,
    title: '部署服务',
    description: '部署新的微服务',
    icon: '🚀',
    color: 'green',
    route: '/services?action=create'
  },
  {
    id: 3,
    title: '创建任务',
    description: '创建新的执行任务',
    icon: '📋',
    color: 'orange',
    route: '/tasks?action=create'
  },
  {
    id: 4,
    title: '查看日志',
    description: '查看系统运行日志',
    icon: '📊',
    color: 'purple',
    route: '/logs'
  }
]

// 系统状态数据
const systemStatus = {
  cpu: {
    usage: randomInt(20, 80),
    cores: 8,
    temperature: randomInt(45, 75)
  },
  memory: {
    used: randomInt(4, 12),
    total: 16,
    usage: randomInt(25, 75)
  },
  disk: {
    used: randomInt(100, 800),
    total: 1000,
    usage: randomInt(10, 80)
  },
  network: {
    upload: randomInt(10, 100),
    download: randomInt(50, 500),
    latency: randomInt(10, 50)
  }
}

// 导出Dashboard mock数据操作
export default {
  // 获取统计数据
  getStats: () => statsData,
  
  // 获取图表数据
  getCharts: () => chartData,
  
  // 获取最近活动
  getRecent: ({ params = {} }) => {
    const { limit = 10 } = params
    return recentActivities.slice(0, limit)
  },
  
  // 获取快速操作
  getQuickActions: () => quickActions,
  
  // 获取系统状态
  getSystemStatus: () => ({
    ...systemStatus,
    timestamp: Date.now(),
    uptime: randomInt(1000000, 9999999) // 系统运行时间（秒）
  })
}
