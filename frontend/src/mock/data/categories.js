// Categories页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, createCrudOperations } from '../utils'

// 分类图标
const categoryIcons = ['📁', '📂', '🗂️', '📋', '📊', '📈', '📉', '📌', '🏷️', '🔖', '📝', '📄']

// 分类颜色
const categoryColors = ['blue', 'green', 'red', 'yellow', 'purple', 'pink', 'indigo', 'gray']

// 分类状态
const categoryStatuses = ['启用', '禁用', '草稿']

// 生成分类数据
function generateCategory(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const itemCount = randomInt(0, 100)
  
  return {
    id: id || generateId(),
    name: `分类${id || Math.random().toString(36).substr(2, 4)}`,
    slug: `category-${Math.random().toString(36).substr(2, 8)}`,
    description: `这是分类${id || Math.random().toString(36).substr(2, 4)}的描述信息，用于组织和管理相关内容`,
    icon: randomChoice(categoryIcons),
    color: randomChoice(categoryColors),
    status: randomChoice(categoryStatuses),
    parentId: Math.random() > 0.7 ? randomInt(1, 10) : null, // 30%概率有父分类
    sort: randomInt(1, 100),
    itemCount,
    isSystem: Math.random() > 0.8, // 20%概率是系统分类
    seoTitle: `分类${id || Math.random().toString(36).substr(2, 4)} - SEO标题`,
    seoDescription: `分类${id || Math.random().toString(36).substr(2, 4)}的SEO描述信息`,
    seoKeywords: randomChoice([
      '分类,管理,系统',
      '内容,组织,标签',
      '数据,分类,整理'
    ]),
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    createdBy: randomChoice(['张三', '李四', '王五', '赵六']),
    settings: {
      allowSubCategories: randomChoice([true, false]),
      autoSort: randomChoice([true, false]),
      showInMenu: randomChoice([true, false]),
      requireAuth: randomChoice([true, false])
    },
    metadata: {
      template: randomChoice(['default', 'grid', 'list', 'card']),
      pageSize: randomChoice([10, 20, 50]),
      sortBy: randomChoice(['name', 'createdAt', 'sort', 'itemCount'])
    }
  }
}

// 生成分类列表数据
const categoriesData = Array.from({ length: 28 }, (_, index) => generateCategory(index + 1))

// 建立父子关系
categoriesData.forEach(category => {
  if (category.parentId && category.parentId <= categoriesData.length) {
    const parent = categoriesData.find(c => c.id === category.parentId)
    if (parent) {
      category.parentName = parent.name
    }
  }
})

// 分类统计数据
const categoryStats = {
  total: categoriesData.length,
  enabled: categoriesData.filter(c => c.status === '启用').length,
  disabled: categoriesData.filter(c => c.status === '禁用').length,
  draft: categoriesData.filter(c => c.status === '草稿').length,
  system: categoriesData.filter(c => c.isSystem).length,
  withParent: categoriesData.filter(c => c.parentId).length,
  totalItems: categoriesData.reduce((sum, c) => sum + c.itemCount, 0)
}

// 创建CRUD操作
const crudOps = createCrudOperations(categoriesData, {
  idField: 'id',
  searchFields: ['name', 'slug', 'description'],
  defaultSort: 'sort'
})

// 导出Categories mock数据操作
export default {
  ...crudOps,
  
  // 获取分类统计
  getStats: () => categoryStats,
  
  // 获取分类树
  getTree: () => {
    const rootCategories = categoriesData.filter(c => !c.parentId)
    
    function buildTree(categories) {
      return categories.map(category => ({
        ...category,
        children: buildTree(categoriesData.filter(c => c.parentId === category.id))
      }))
    }
    
    return buildTree(rootCategories)
  },
  
  // 移动分类
  move: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { parentId, sort } = data
    const category = categoriesData.find(c => c.id.toString() === id.toString())
    
    if (!category) return null
    
    category.parentId = parentId
    category.sort = sort
    category.updatedAt = formatDate(new Date())
    
    return category
  },
  
  // 批量更新排序
  updateSort: ({ data }) => {
    const { items } = data
    let updatedCount = 0
    
    items.forEach(item => {
      const category = categoriesData.find(c => c.id.toString() === item.id.toString())
      if (category) {
        category.sort = item.sort
        category.updatedAt = formatDate(new Date())
        updatedCount++
      }
    })
    
    return { updatedCount }
  },
  
  // 切换状态
  toggleStatus: ({ pathParams }) => {
    const id = pathParams[0]
    const category = categoriesData.find(c => c.id.toString() === id.toString())
    
    if (!category) return null
    
    category.status = category.status === '启用' ? '禁用' : '启用'
    category.updatedAt = formatDate(new Date())
    
    return category
  },
  
  // 获取子分类
  getChildren: ({ pathParams }) => {
    const id = pathParams[0]
    return categoriesData.filter(c => c.parentId && c.parentId.toString() === id.toString())
  },
  
  // 获取分类路径
  getPath: ({ pathParams }) => {
    const id = pathParams[0]
    const category = categoriesData.find(c => c.id.toString() === id.toString())
    
    if (!category) return []
    
    const path = [category]
    let current = category
    
    while (current.parentId) {
      const parent = categoriesData.find(c => c.id === current.parentId)
      if (parent) {
        path.unshift(parent)
        current = parent
      } else {
        break
      }
    }
    
    return path
  }
}
