// Wallet页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, createCrudOperations } from '../utils'

// 交易类型
const transactionTypes = ['充值', '消费', '退款', '奖励', '提现', '转账']

// 交易状态
const transactionStatuses = ['成功', '处理中', '失败', '已取消']

// 支付方式
const paymentMethods = ['支付宝', '微信支付', '银行卡', '余额', '积分']

// 生成钱包信息
const walletInfo = {
  id: generateId(),
  userId: 1,
  balance: 15678.50,
  frozenAmount: 234.00,
  availableAmount: 15444.50,
  totalIncome: 25890.00,
  totalExpense: 10211.50,
  currency: 'CNY',
  level: 'VIP',
  creditScore: 850,
  lastUpdated: formatDate(new Date()),
  settings: {
    autoRecharge: true,
    rechargeThreshold: 100,
    rechargeAmount: 500,
    notifyOnTransaction: true,
    notifyOnLowBalance: true,
    dailyLimit: 10000,
    monthlyLimit: 50000
  },
  statistics: {
    thisMonthIncome: 3456.78,
    thisMonthExpense: 1234.56,
    avgMonthlyIncome: 2890.45,
    avgMonthlyExpense: 1567.89,
    largestTransaction: 5000.00,
    transactionCount: 156,
    favoritePaymentMethod: '支付宝'
  }
}

// 生成交易记录
function generateTransaction(id) {
  const createdAt = randomDate(new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), new Date()) // 最近90天
  const type = randomChoice(transactionTypes)
  const status = randomChoice(transactionStatuses)
  
  // 根据类型设置金额范围
  let amount
  switch (type) {
    case '充值':
      amount = randomChoice([100, 200, 500, 1000, 2000])
      break
    case '消费':
      amount = parseFloat((Math.random() * 500 + 10).toFixed(2))
      break
    case '退款':
      amount = parseFloat((Math.random() * 200 + 5).toFixed(2))
      break
    case '奖励':
      amount = parseFloat((Math.random() * 100 + 1).toFixed(2))
      break
    case '提现':
      amount = randomChoice([500, 1000, 2000, 5000])
      break
    case '转账':
      amount = parseFloat((Math.random() * 1000 + 50).toFixed(2))
      break
    default:
      amount = parseFloat((Math.random() * 500).toFixed(2))
  }
  
  return {
    id: id || generateId(),
    type,
    amount,
    status,
    description: randomChoice([
      `${type} - 系统操作`,
      `${type} - 用户主动操作`,
      `${type} - 自动处理`,
      `${type} - 管理员操作`
    ]),
    paymentMethod: randomChoice(paymentMethods),
    orderId: `ORD${Date.now()}${randomInt(1000, 9999)}`,
    transactionId: `TXN${Date.now()}${randomInt(1000, 9999)}`,
    balanceBefore: parseFloat((Math.random() * 10000 + 1000).toFixed(2)),
    balanceAfter: 0, // 将在后面计算
    fee: type === '提现' ? parseFloat((amount * 0.01).toFixed(2)) : 0,
    actualAmount: 0, // 将在后面计算
    remark: randomChoice([
      '正常交易',
      '系统自动处理',
      '用户申请',
      '活动奖励',
      '退款处理'
    ]),
    operator: randomChoice(['系统', '用户', '管理员', '客服']),
    ip: `192.168.${randomInt(1, 255)}.${randomInt(1, 255)}`,
    device: randomChoice(['PC', 'Mobile', 'Tablet']),
    location: randomChoice(['北京', '上海', '广州', '深圳', '杭州']),
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    completedAt: status === '成功' ? formatDate(randomDate(createdAt, new Date())) : null,
    failedReason: status === '失败' ? randomChoice([
      '余额不足',
      '银行卡异常',
      '网络超时',
      '系统维护',
      '风控拦截'
    ]) : null,
    metadata: {
      channel: randomChoice(['web', 'mobile', 'api']),
      version: `v${randomInt(1, 3)}.${randomInt(0, 9)}`,
      userAgent: randomChoice([
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0)',
        'Mozilla/5.0 (Android 10; Mobile)'
      ])
    }
  }
}

// 计算交易后余额
const transactionsData = Array.from({ length: 200 }, (_, index) => {
  const transaction = generateTransaction(index + 1)
  
  // 计算实际金额和余额变化
  if (transaction.type === '充值' || transaction.type === '奖励' || transaction.type === '退款') {
    transaction.actualAmount = transaction.amount
    transaction.balanceAfter = transaction.balanceBefore + transaction.amount
  } else {
    transaction.actualAmount = transaction.amount + transaction.fee
    transaction.balanceAfter = transaction.balanceBefore - transaction.actualAmount
  }
  
  return transaction
}).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

// 钱包统计数据
const walletStats = {
  totalTransactions: transactionsData.length,
  successfulTransactions: transactionsData.filter(t => t.status === '成功').length,
  pendingTransactions: transactionsData.filter(t => t.status === '处理中').length,
  failedTransactions: transactionsData.filter(t => t.status === '失败').length,
  totalIncome: transactionsData
    .filter(t => ['充值', '奖励', '退款'].includes(t.type) && t.status === '成功')
    .reduce((sum, t) => sum + t.amount, 0),
  totalExpense: transactionsData
    .filter(t => ['消费', '提现', '转账'].includes(t.type) && t.status === '成功')
    .reduce((sum, t) => sum + t.actualAmount, 0),
  totalFees: transactionsData
    .filter(t => t.status === '成功')
    .reduce((sum, t) => sum + t.fee, 0),
  byType: transactionTypes.reduce((acc, type) => {
    acc[type] = transactionsData.filter(t => t.type === type).length
    return acc
  }, {}),
  byStatus: transactionStatuses.reduce((acc, status) => {
    acc[status] = transactionsData.filter(t => t.status === status).length
    return acc
  }, {}),
  byPaymentMethod: paymentMethods.reduce((acc, method) => {
    acc[method] = transactionsData.filter(t => t.paymentMethod === method).length
    return acc
  }, {}),
  avgTransactionAmount: parseFloat((
    transactionsData.reduce((sum, t) => sum + t.amount, 0) / transactionsData.length
  ).toFixed(2)),
  largestTransaction: Math.max(...transactionsData.map(t => t.amount)),
  smallestTransaction: Math.min(...transactionsData.map(t => t.amount))
}

// 创建CRUD操作（钱包交易通常只读）
const crudOps = createCrudOperations(transactionsData, {
  idField: 'id',
  searchFields: ['description', 'orderId', 'transactionId', 'remark'],
  defaultSort: 'createdAt'
})

// 导出Wallet mock数据操作
export default {
  // 交易记录查询
  getTransactions: crudOps.getList,
  getTransactionById: crudOps.getById,
  
  // 获取钱包信息
  getInfo: () => walletInfo,
  
  // 获取钱包统计
  getStats: () => walletStats,
  
  // 充值
  recharge: ({ data }) => {
    const { amount, paymentMethod } = data
    
    const transaction = {
      id: generateId(),
      type: '充值',
      amount: parseFloat(amount),
      status: '处理中',
      description: '用户充值',
      paymentMethod,
      orderId: `ORD${Date.now()}${randomInt(1000, 9999)}`,
      transactionId: `TXN${Date.now()}${randomInt(1000, 9999)}`,
      balanceBefore: walletInfo.balance,
      balanceAfter: walletInfo.balance + parseFloat(amount),
      fee: 0,
      actualAmount: parseFloat(amount),
      remark: '用户主动充值',
      operator: '用户',
      ip: `192.168.1.${randomInt(1, 255)}`,
      device: 'PC',
      location: '北京',
      createdAt: formatDate(new Date()),
      updatedAt: formatDate(new Date()),
      completedAt: null,
      failedReason: null,
      metadata: {
        channel: 'web',
        version: 'v1.0',
        userAgent: 'Mozilla/5.0'
      }
    }
    
    // 模拟异步处理
    setTimeout(() => {
      transaction.status = '成功'
      transaction.completedAt = formatDate(new Date())
      walletInfo.balance += parseFloat(amount)
      walletInfo.availableAmount += parseFloat(amount)
      walletInfo.totalIncome += parseFloat(amount)
      walletInfo.lastUpdated = formatDate(new Date())
    }, 2000)
    
    transactionsData.unshift(transaction)
    return transaction
  },
  
  // 提现
  withdraw: ({ data }) => {
    const { amount, paymentMethod } = data
    const fee = parseFloat(amount) * 0.01 // 1%手续费
    const actualAmount = parseFloat(amount) + fee
    
    if (walletInfo.availableAmount < actualAmount) {
      return {
        success: false,
        message: '余额不足'
      }
    }
    
    const transaction = {
      id: generateId(),
      type: '提现',
      amount: parseFloat(amount),
      status: '处理中',
      description: '用户提现',
      paymentMethod,
      orderId: `ORD${Date.now()}${randomInt(1000, 9999)}`,
      transactionId: `TXN${Date.now()}${randomInt(1000, 9999)}`,
      balanceBefore: walletInfo.balance,
      balanceAfter: walletInfo.balance - actualAmount,
      fee,
      actualAmount,
      remark: '用户申请提现',
      operator: '用户',
      ip: `192.168.1.${randomInt(1, 255)}`,
      device: 'PC',
      location: '北京',
      createdAt: formatDate(new Date()),
      updatedAt: formatDate(new Date()),
      completedAt: null,
      failedReason: null,
      metadata: {
        channel: 'web',
        version: 'v1.0',
        userAgent: 'Mozilla/5.0'
      }
    }
    
    // 立即冻结金额
    walletInfo.availableAmount -= actualAmount
    walletInfo.frozenAmount += actualAmount
    
    // 模拟异步处理
    setTimeout(() => {
      transaction.status = '成功'
      transaction.completedAt = formatDate(new Date())
      walletInfo.balance -= actualAmount
      walletInfo.frozenAmount -= actualAmount
      walletInfo.totalExpense += actualAmount
      walletInfo.lastUpdated = formatDate(new Date())
    }, 5000)
    
    transactionsData.unshift(transaction)
    return transaction
  },
  
  // 转账
  transfer: ({ data }) => {
    const { amount, targetUserId, remark } = data
    const actualAmount = parseFloat(amount)
    
    if (walletInfo.availableAmount < actualAmount) {
      return {
        success: false,
        message: '余额不足'
      }
    }
    
    const transaction = {
      id: generateId(),
      type: '转账',
      amount: actualAmount,
      status: '成功',
      description: `转账给用户${targetUserId}`,
      paymentMethod: '余额',
      orderId: `ORD${Date.now()}${randomInt(1000, 9999)}`,
      transactionId: `TXN${Date.now()}${randomInt(1000, 9999)}`,
      balanceBefore: walletInfo.balance,
      balanceAfter: walletInfo.balance - actualAmount,
      fee: 0,
      actualAmount,
      remark: remark || '用户转账',
      operator: '用户',
      ip: `192.168.1.${randomInt(1, 255)}`,
      device: 'PC',
      location: '北京',
      createdAt: formatDate(new Date()),
      updatedAt: formatDate(new Date()),
      completedAt: formatDate(new Date()),
      failedReason: null,
      metadata: {
        channel: 'web',
        version: 'v1.0',
        userAgent: 'Mozilla/5.0',
        targetUserId
      }
    }
    
    // 立即更新余额
    walletInfo.balance -= actualAmount
    walletInfo.availableAmount -= actualAmount
    walletInfo.totalExpense += actualAmount
    walletInfo.lastUpdated = formatDate(new Date())
    
    transactionsData.unshift(transaction)
    return transaction
  },
  
  // 获取余额变化趋势
  getBalanceTrend: ({ params = {} }) => {
    const { days = 30 } = params
    const trends = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
      const dayTransactions = transactionsData.filter(t => {
        const transactionDate = new Date(t.createdAt)
        return transactionDate.toDateString() === date.toDateString() && t.status === '成功'
      })
      
      const income = dayTransactions
        .filter(t => ['充值', '奖励', '退款'].includes(t.type))
        .reduce((sum, t) => sum + t.amount, 0)
      
      const expense = dayTransactions
        .filter(t => ['消费', '提现', '转账'].includes(t.type))
        .reduce((sum, t) => sum + t.actualAmount, 0)
      
      trends.push({
        date: formatDate(date, 'YYYY-MM-DD'),
        income: parseFloat(income.toFixed(2)),
        expense: parseFloat(expense.toFixed(2)),
        net: parseFloat((income - expense).toFixed(2)),
        transactionCount: dayTransactions.length
      })
    }
    
    return trends
  },
  
  // 更新钱包设置
  updateSettings: ({ data }) => {
    Object.assign(walletInfo.settings, data)
    walletInfo.lastUpdated = formatDate(new Date())
    return walletInfo.settings
  }
}
