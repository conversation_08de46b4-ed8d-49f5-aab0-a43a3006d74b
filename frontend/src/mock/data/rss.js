// RSS页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, createCrudOperations } from '../utils'

// RSS源分类
const rssCategories = ['技术资讯', '行业动态', '产品更新', '开发博客', '新闻资讯', '学习资源']

// RSS源状态
const rssStatuses = ['正常', '异常', '暂停', '已删除']

// 更新频率
const updateFrequencies = ['实时', '每小时', '每日', '每周', '手动']

// 生成RSS源数据
function generateRssSource(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const category = randomChoice(rssCategories)
  const itemCount = randomInt(10, 1000)
  const lastFetchAt = randomDate(createdAt, new Date())
  
  const sourceNames = {
    '技术资讯': ['TechCrunch', 'Hacker News', 'InfoQ', '36氪', 'CSDN'],
    '行业动态': ['艾瑞网', '亿欧网', '钛媒体', '虎嗅网', '创业邦'],
    '产品更新': ['Product Hunt', 'GitHub Releases', 'Chrome Updates', 'VS Code'],
    '开发博客': ['阮一峰博客', '廖雪峰博客', '美团技术团队', '腾讯技术'],
    '新闻资讯': ['新浪科技', '网易科技', '搜狐科技', 'IT之家'],
    '学习资源': ['慕课网', '极客时间', 'Coursera', 'edX']
  }
  
  return {
    id: id || generateId(),
    name: randomChoice(sourceNames[category] || ['RSS源']),
    description: `${category}相关的RSS订阅源，提供最新的资讯和动态`,
    url: `https://example.com/rss/${Math.random().toString(36).substr(2, 8)}.xml`,
    siteUrl: `https://example.com/${Math.random().toString(36).substr(2, 8)}`,
    category,
    status: randomChoice(rssStatuses),
    updateFrequency: randomChoice(updateFrequencies),
    itemCount,
    newItemCount: randomInt(0, 50),
    lastFetchAt: formatDate(lastFetchAt),
    lastSuccessAt: formatDate(randomDate(createdAt, lastFetchAt)),
    lastErrorAt: Math.random() > 0.7 ? formatDate(randomDate(createdAt, new Date())) : null,
    errorMessage: Math.random() > 0.7 ? randomChoice([
      'Feed URL不可访问',
      'XML格式错误',
      '网络超时',
      '服务器返回404',
      'SSL证书错误'
    ]) : null,
    fetchCount: randomInt(100, 5000),
    errorCount: randomInt(0, 50),
    successRate: parseFloat((Math.random() * 0.3 + 0.7).toFixed(2)), // 70%-100%
    avgFetchTime: randomInt(500, 3000), // 毫秒
    encoding: randomChoice(['UTF-8', 'GBK', 'GB2312']),
    language: randomChoice(['zh-CN', 'en-US', 'zh-TW']),
    tags: randomChoice([
      ['技术', '资讯'],
      ['新闻', '动态'],
      ['博客', '分享'],
      ['产品', '更新'],
      ['学习', '教程']
    ]),
    isActive: Math.random() > 0.2, // 80%概率是活跃的
    isPublic: Math.random() > 0.3, // 70%概率是公开的
    isFavorite: Math.random() > 0.8, // 20%概率被收藏
    priority: randomInt(1, 10),
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    createdBy: randomChoice(['张三', '李四', '王五', '系统管理员']),
    settings: {
      autoFetch: Math.random() > 0.2,
      fetchInterval: randomInt(30, 1440), // 分钟
      maxItems: randomChoice([50, 100, 200, 500]),
      enableNotification: Math.random() > 0.5,
      filterKeywords: randomChoice([
        [],
        ['重要', '紧急'],
        ['技术', '开发'],
        ['产品', '设计']
      ]),
      excludeKeywords: randomChoice([
        [],
        ['广告', '推广'],
        ['娱乐', '八卦']
      ])
    },
    statistics: {
      todayItems: randomInt(0, 20),
      weekItems: randomInt(0, 100),
      monthItems: randomInt(0, 500),
      avgDailyItems: randomInt(1, 30),
      peakFetchTime: randomInt(0, 23),
      mostActiveDay: randomChoice(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'])
    }
  }
}

// 生成RSS文章数据
function generateRssItem(sourceId) {
  const publishedAt = randomDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date())
  
  return {
    id: generateId(),
    sourceId,
    title: randomChoice([
      '人工智能技术的最新发展趋势',
      '云计算在企业数字化转型中的作用',
      '前端开发框架对比分析',
      '数据安全与隐私保护的重要性',
      '移动应用开发的最佳实践',
      '区块链技术在金融领域的应用',
      '物联网设备的安全挑战',
      '机器学习算法的实际应用案例'
    ]),
    description: '这是一篇关于技术发展的深度分析文章，探讨了当前行业的发展趋势和未来展望...',
    content: '文章的完整内容，包含详细的分析和见解...',
    url: `https://example.com/article/${Math.random().toString(36).substr(2, 8)}`,
    author: randomChoice(['张三', '李四', '王五', '技术团队', '编辑部']),
    publishedAt: formatDate(publishedAt),
    fetchedAt: formatDate(randomDate(publishedAt, new Date())),
    isRead: Math.random() > 0.6, // 40%概率已读
    isStarred: Math.random() > 0.9, // 10%概率被收藏
    isArchived: Math.random() > 0.95, // 5%概率被归档
    readCount: randomInt(0, 1000),
    shareCount: randomInt(0, 100),
    commentCount: randomInt(0, 50),
    tags: randomChoice([
      ['技术', 'AI'],
      ['云计算', '企业'],
      ['前端', '开发'],
      ['安全', '隐私'],
      ['移动', '应用']
    ]),
    category: randomChoice(rssCategories),
    wordCount: randomInt(500, 5000),
    readTime: randomInt(2, 20), // 分钟
    sentiment: randomChoice(['positive', 'neutral', 'negative']),
    importance: randomInt(1, 5),
    metadata: {
      images: randomInt(0, 5),
      videos: randomInt(0, 2),
      links: randomInt(0, 10),
      hasCode: Math.random() > 0.7
    }
  }
}

// 生成RSS源列表数据
const rssData = Array.from({ length: 45 }, (_, index) => generateRssSource(index + 1))

// 生成RSS文章数据
const rssItemsData = []
rssData.forEach(source => {
  const itemCount = randomInt(10, 100)
  for (let i = 0; i < itemCount; i++) {
    rssItemsData.push(generateRssItem(source.id))
  }
})

// RSS统计数据
const rssStats = {
  totalSources: rssData.length,
  activeSources: rssData.filter(r => r.isActive).length,
  normalSources: rssData.filter(r => r.status === '正常').length,
  errorSources: rssData.filter(r => r.status === '异常').length,
  totalItems: rssItemsData.length,
  unreadItems: rssItemsData.filter(i => !i.isRead).length,
  starredItems: rssItemsData.filter(i => i.isStarred).length,
  todayItems: rssItemsData.filter(i => {
    const today = new Date()
    const itemDate = new Date(i.publishedAt)
    return itemDate.toDateString() === today.toDateString()
  }).length,
  byCategory: rssCategories.reduce((acc, category) => {
    acc[category] = rssData.filter(r => r.category === category).length
    return acc
  }, {}),
  avgSuccessRate: parseFloat((rssData.reduce((sum, r) => sum + r.successRate, 0) / rssData.length).toFixed(2))
}

// 创建CRUD操作
const crudOps = createCrudOperations(rssData, {
  idField: 'id',
  searchFields: ['name', 'description', 'url', 'siteUrl'],
  defaultSort: 'lastFetchAt'
})

// 导出RSS mock数据操作
export default {
  ...crudOps,
  
  // 获取RSS统计
  getStats: () => rssStats,
  
  // 获取RSS文章列表
  getItems: ({ params = {} }) => {
    const { sourceId, category, isRead, isStarred, page = 1, pageSize = 20 } = params
    let result = [...rssItemsData]
    
    if (sourceId) {
      result = result.filter(i => i.sourceId.toString() === sourceId.toString())
    }
    
    if (category) {
      result = result.filter(i => i.category === category)
    }
    
    if (isRead !== undefined) {
      result = result.filter(i => i.isRead === (isRead === 'true'))
    }
    
    if (isStarred !== undefined) {
      result = result.filter(i => i.isStarred === (isStarred === 'true'))
    }
    
    // 排序
    result = result.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
    
    // 分页
    const total = result.length
    const start = (page - 1) * pageSize
    const items = result.slice(start, start + pageSize)
    
    return {
      items,
      total,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
      totalPages: Math.ceil(total / pageSize)
    }
  },
  
  // 获取单个RSS文章
  getItem: ({ pathParams }) => {
    const id = pathParams[0]
    return rssItemsData.find(i => i.id.toString() === id.toString())
  },
  
  // 手动抓取RSS
  fetch: ({ pathParams }) => {
    const id = pathParams[0]
    const source = rssData.find(r => r.id.toString() === id.toString())
    
    if (!source) return null
    
    // 模拟抓取过程
    return new Promise(resolve => {
      setTimeout(() => {
        const success = Math.random() > 0.2 // 80%成功率
        const newItemCount = success ? randomInt(0, 10) : 0
        
        if (success) {
          source.lastFetchAt = formatDate(new Date())
          source.lastSuccessAt = formatDate(new Date())
          source.newItemCount += newItemCount
          source.itemCount += newItemCount
          source.fetchCount++
          source.errorMessage = null
          
          // 生成新文章
          for (let i = 0; i < newItemCount; i++) {
            rssItemsData.unshift(generateRssItem(source.id))
          }
        } else {
          source.lastFetchAt = formatDate(new Date())
          source.lastErrorAt = formatDate(new Date())
          source.errorCount++
          source.errorMessage = randomChoice([
            'Feed URL不可访问',
            'XML格式错误',
            '网络超时'
          ])
        }
        
        source.successRate = parseFloat((source.fetchCount / (source.fetchCount + source.errorCount)).toFixed(2))
        source.updatedAt = formatDate(new Date())
        
        resolve({
          success,
          newItemCount,
          message: success ? `成功抓取到 ${newItemCount} 篇新文章` : source.errorMessage,
          source
        })
      }, 2000)
    })
  },
  
  // 批量抓取
  batchFetch: ({ data }) => {
    const { sourceIds } = data
    
    return new Promise(resolve => {
      setTimeout(() => {
        let totalNew = 0
        let successCount = 0
        
        sourceIds.forEach(id => {
          const source = rssData.find(r => r.id.toString() === id.toString())
          if (source) {
            const success = Math.random() > 0.2
            if (success) {
              const newItems = randomInt(0, 5)
              totalNew += newItems
              successCount++
              source.newItemCount += newItems
              source.itemCount += newItems
              source.lastFetchAt = formatDate(new Date())
              source.lastSuccessAt = formatDate(new Date())
            }
          }
        })
        
        resolve({
          totalSources: sourceIds.length,
          successCount,
          totalNewItems: totalNew,
          message: `批量抓取完成，${successCount}/${sourceIds.length} 个源成功，共获取 ${totalNew} 篇新文章`
        })
      }, 3000)
    })
  },
  
  // 标记文章为已读
  markAsRead: ({ pathParams }) => {
    const id = pathParams[0]
    const item = rssItemsData.find(i => i.id.toString() === id.toString())
    
    if (!item) return null
    
    item.isRead = true
    return item
  },
  
  // 收藏文章
  starItem: ({ pathParams }) => {
    const id = pathParams[0]
    const item = rssItemsData.find(i => i.id.toString() === id.toString())
    
    if (!item) return null
    
    item.isStarred = !item.isStarred
    return item
  },
  
  // 归档文章
  archiveItem: ({ pathParams }) => {
    const id = pathParams[0]
    const item = rssItemsData.find(i => i.id.toString() === id.toString())
    
    if (!item) return null
    
    item.isArchived = true
    return item
  },
  
  // 获取RSS源分类
  getCategories: () => {
    return rssCategories.map(category => ({
      name: category,
      sourceCount: rssData.filter(r => r.category === category).length,
      itemCount: rssItemsData.filter(i => i.category === category).length,
      unreadCount: rssItemsData.filter(i => i.category === category && !i.isRead).length
    }))
  },
  
  // 搜索文章
  searchItems: ({ params = {} }) => {
    const { q, limit = 50 } = params
    if (!q) return []
    
    return rssItemsData
      .filter(i => 
        i.title.toLowerCase().includes(q.toLowerCase()) ||
        i.description.toLowerCase().includes(q.toLowerCase()) ||
        i.content.toLowerCase().includes(q.toLowerCase())
      )
      .sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))
      .slice(0, limit)
  },
  
  // 导出OPML
  exportOpml: () => {
    const opmlContent = `<?xml version="1.0" encoding="UTF-8"?>
<opml version="1.0">
  <head>
    <title>RSS订阅源</title>
    <dateCreated>${formatDate(new Date())}</dateCreated>
  </head>
  <body>
    ${rssData.map(source => 
      `<outline text="${source.name}" title="${source.name}" type="rss" xmlUrl="${source.url}" htmlUrl="${source.siteUrl}"/>`
    ).join('\n    ')}
  </body>
</opml>`
    
    return {
      filename: `rss_sources_${formatDate(new Date(), 'YYYY-MM-DD')}.opml`,
      content: opmlContent,
      downloadUrl: '/api/rss/export/opml',
      sourceCount: rssData.length
    }
  }
}
