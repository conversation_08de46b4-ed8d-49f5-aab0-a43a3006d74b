// Auth认证Mock数据
import { generateId, randomChineseName, randomEmail, formatDate } from '../utils'

// 模拟用户数据
const mockUsers = [
  {
    id: 1,
    username: 'admin',
    password: 'admin123', // 实际项目中不应该存储明文密码
    name: '系统管理员',
    email: '<EMAIL>',
    role: '管理员',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    status: '正常',
    lastLoginAt: null,
    createdAt: formatDate(new Date(2023, 0, 1))
  },
  {
    id: 2,
    username: 'user',
    password: 'user123',
    name: '普通用户',
    email: '<EMAIL>',
    role: '普通用户',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user',
    status: '正常',
    lastLoginAt: null,
    createdAt: formatDate(new Date(2023, 1, 1))
  },
  {
    id: 3,
    username: 'github_10414',
    password: null, // OAuth用户没有密码
    name: 'GitHub用户',
    email: '<EMAIL>',
    role: '开发者',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=github',
    status: '正常',
    lastLoginAt: null,
    createdAt: formatDate(new Date(2023, 2, 1)),
    oauthProvider: 'github',
    oauthId: 'github_10414'
  }
]

// 当前登录用户
let currentUser = null

// 生成JWT Token（模拟）
function generateToken(user) {
  const payload = {
    userId: user.id,
    username: user.username,
    role: user.role,
    exp: Date.now() + 24 * 60 * 60 * 1000 // 24小时后过期
  }
  
  // 实际项目中应该使用真正的JWT库
  return `mock_token_${btoa(JSON.stringify(payload))}`
}

// 验证Token
function verifyToken(token) {
  if (!token || !token.startsWith('mock_token_')) {
    return null
  }
  
  try {
    const payload = JSON.parse(atob(token.replace('mock_token_', '')))
    
    if (payload.exp < Date.now()) {
      return null // Token已过期
    }
    
    return payload
  } catch (error) {
    return null
  }
}

// 导出Auth mock数据操作
export default {
  // 用户登录
  login: ({ data }) => {
    const { username, password, captcha } = data
    
    // 验证验证码（模拟）
    if (!captcha || captcha.toLowerCase() !== 'abcd') {
      return {
        success: false,
        message: '验证码错误',
        code: 400
      }
    }
    
    // 查找用户
    const user = mockUsers.find(u => 
      u.username === username && 
      u.password === password &&
      u.status === '正常'
    )
    
    if (!user) {
      return {
        success: false,
        message: '用户名或密码错误',
        code: 401
      }
    }
    
    // 更新最后登录时间
    user.lastLoginAt = formatDate(new Date())
    currentUser = user
    
    // 生成Token
    const token = generateToken(user)
    
    return {
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          email: user.email,
          role: user.role,
          avatar: user.avatar,
          lastLoginAt: user.lastLoginAt
        }
      }
    }
  },
  
  // OAuth登录
  oauthLogin: ({ data }) => {
    const { provider, code, state } = data
    
    // 模拟OAuth流程
    return new Promise(resolve => {
      setTimeout(() => {
        // 模拟从第三方获取用户信息
        const oauthUser = {
          id: `${provider}_${Math.random().toString(36).substr(2, 8)}`,
          name: randomChineseName(),
          email: `${provider}<EMAIL>`,
          avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${provider}`
        }
        
        // 查找或创建用户
        let user = mockUsers.find(u => u.oauthProvider === provider && u.oauthId === oauthUser.id)
        
        if (!user) {
          // 创建新用户
          user = {
            id: mockUsers.length + 1,
            username: `${provider}_${oauthUser.id}`,
            password: null,
            name: oauthUser.name,
            email: oauthUser.email,
            role: '普通用户',
            avatar: oauthUser.avatar,
            status: '正常',
            lastLoginAt: formatDate(new Date()),
            createdAt: formatDate(new Date()),
            oauthProvider: provider,
            oauthId: oauthUser.id
          }
          
          mockUsers.push(user)
        } else {
          user.lastLoginAt = formatDate(new Date())
        }
        
        currentUser = user
        const token = generateToken(user)
        
        resolve({
          success: true,
          message: `${provider}登录成功`,
          data: {
            token,
            user: {
              id: user.id,
              username: user.username,
              name: user.name,
              email: user.email,
              role: user.role,
              avatar: user.avatar,
              lastLoginAt: user.lastLoginAt
            }
          }
        })
      }, 1000)
    })
  },
  
  // 用户登出
  logout: () => {
    currentUser = null
    
    return {
      success: true,
      message: '登出成功'
    }
  },
  
  // 获取当前用户信息
  getProfile: ({ headers = {} }) => {
    const token = headers.authorization?.replace('Bearer ', '')
    const payload = verifyToken(token)
    
    if (!payload) {
      return {
        success: false,
        message: '未登录或登录已过期',
        code: 401
      }
    }
    
    const user = mockUsers.find(u => u.id === payload.userId)
    
    if (!user) {
      return {
        success: false,
        message: '用户不存在',
        code: 404
      }
    }
    
    return {
      success: true,
      data: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
        avatar: user.avatar,
        status: user.status,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt,
        oauthProvider: user.oauthProvider
      }
    }
  },
  
  // 更新用户信息
  updateProfile: ({ data, headers = {} }) => {
    const token = headers.authorization?.replace('Bearer ', '')
    const payload = verifyToken(token)
    
    if (!payload) {
      return {
        success: false,
        message: '未登录或登录已过期',
        code: 401
      }
    }
    
    const user = mockUsers.find(u => u.id === payload.userId)
    
    if (!user) {
      return {
        success: false,
        message: '用户不存在',
        code: 404
      }
    }
    
    // 更新允许的字段
    const allowedFields = ['name', 'email', 'avatar']
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        user[field] = data[field]
      }
    })
    
    return {
      success: true,
      message: '用户信息更新成功',
      data: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
        avatar: user.avatar,
        status: user.status,
        lastLoginAt: user.lastLoginAt,
        createdAt: user.createdAt
      }
    }
  },
  
  // 修改密码
  changePassword: ({ data, headers = {} }) => {
    const token = headers.authorization?.replace('Bearer ', '')
    const payload = verifyToken(token)
    
    if (!payload) {
      return {
        success: false,
        message: '未登录或登录已过期',
        code: 401
      }
    }
    
    const { oldPassword, newPassword } = data
    const user = mockUsers.find(u => u.id === payload.userId)
    
    if (!user) {
      return {
        success: false,
        message: '用户不存在',
        code: 404
      }
    }
    
    // OAuth用户不能修改密码
    if (!user.password) {
      return {
        success: false,
        message: 'OAuth用户不支持修改密码',
        code: 400
      }
    }
    
    // 验证旧密码
    if (user.password !== oldPassword) {
      return {
        success: false,
        message: '原密码错误',
        code: 400
      }
    }
    
    // 更新密码
    user.password = newPassword
    
    return {
      success: true,
      message: '密码修改成功'
    }
  },
  
  // 忘记密码
  forgotPassword: ({ data }) => {
    const { email } = data
    const user = mockUsers.find(u => u.email === email)
    
    if (!user) {
      return {
        success: false,
        message: '邮箱地址不存在',
        code: 404
      }
    }
    
    // 模拟发送重置邮件
    const resetToken = generateId()
    
    return {
      success: true,
      message: '密码重置邮件已发送，请查收',
      data: {
        resetToken // 实际项目中不应该返回token
      }
    }
  },
  
  // 重置密码
  resetPassword: ({ data }) => {
    const { token, newPassword } = data
    
    // 模拟验证重置token
    if (!token || token.length < 10) {
      return {
        success: false,
        message: '重置链接无效或已过期',
        code: 400
      }
    }
    
    // 这里应该根据token找到对应的用户，为了演示简化处理
    const user = mockUsers[0] // 假设重置第一个用户的密码
    user.password = newPassword
    
    return {
      success: true,
      message: '密码重置成功，请使用新密码登录'
    }
  },
  
  // 获取验证码
  getCaptcha: () => {
    // 模拟生成验证码
    const captcha = 'ABCD' // 固定验证码，方便测试
    
    return {
      success: true,
      data: {
        captcha: `data:image/svg+xml;base64,${btoa(`
          <svg width="100" height="40" xmlns="http://www.w3.org/2000/svg">
            <rect width="100" height="40" fill="#f0f0f0"/>
            <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial" font-size="18" fill="#333">${captcha}</text>
          </svg>
        `)}`,
        key: generateId()
      }
    }
  },
  
  // 验证Token有效性
  verifyToken: ({ headers = {} }) => {
    const token = headers.authorization?.replace('Bearer ', '')
    const payload = verifyToken(token)
    
    return {
      success: !!payload,
      data: payload
    }
  }
}
