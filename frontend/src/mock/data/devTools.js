// DevTools页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, createCrudOperations } from '../utils'

// 工具分类
const toolCategories = ['代码生成', '数据库工具', 'API工具', '测试工具', '部署工具', '监控工具', '文档工具', '效率工具']

// 工具状态
const toolStatuses = ['可用', '维护中', '已废弃', '开发中']

// 工具类型
const toolTypes = ['在线工具', '命令行工具', '浏览器插件', '桌面应用', 'API服务']

// 生成开发工具数据
function generateDevTool(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const category = randomChoice(toolCategories)
  const usageCount = randomInt(0, 5000)
  const rating = parseFloat((Math.random() * 2 + 3).toFixed(1)) // 3.0-5.0
  
  const toolNames = {
    '代码生成': ['代码模板生成器', 'API代码生成', '数据模型生成器', 'SQL生成器'],
    '数据库工具': ['数据库设计器', 'SQL查询工具', '数据迁移工具', '数据备份工具'],
    'API工具': ['API测试工具', 'Mock服务器', 'API文档生成', '接口监控'],
    '测试工具': ['单元测试生成', '性能测试工具', '自动化测试', '压力测试'],
    '部署工具': ['Docker构建器', 'CI/CD配置', '环境管理', '版本发布'],
    '监控工具': ['性能监控', '日志分析', '错误追踪', '系统监控'],
    '文档工具': ['API文档生成', '代码文档', '项目文档', '用户手册'],
    '效率工具': ['代码格式化', '图片压缩', '文件转换', '密码生成器']
  }
  
  const descriptions = {
    '代码生成': '自动生成标准化的代码模板，提高开发效率',
    '数据库工具': '数据库设计、查询和管理的专业工具',
    'API工具': 'API开发、测试和文档生成的完整解决方案',
    '测试工具': '自动化测试和质量保证工具集',
    '部署工具': '应用部署和环境管理的自动化工具',
    '监控工具': '系统性能和应用监控的专业工具',
    '文档工具': '技术文档生成和管理工具',
    '效率工具': '提高开发效率的实用小工具'
  }
  
  return {
    id: id || generateId(),
    name: randomChoice(toolNames[category] || ['开发工具']),
    description: descriptions[category] || '实用的开发工具',
    category,
    type: randomChoice(toolTypes),
    status: randomChoice(toolStatuses),
    version: `v${randomInt(1, 5)}.${randomInt(0, 9)}.${randomInt(0, 9)}`,
    author: randomChoice(['开发团队', '社区贡献', '第三方', '内部开发']),
    maintainer: randomChoice(['张三', '李四', '王五', '开发团队']),
    usageCount,
    rating,
    ratingCount: randomInt(10, 500),
    downloadCount: randomInt(100, 10000),
    isPublic: Math.random() > 0.2, // 80%概率是公开的
    isFavorite: Math.random() > 0.7, // 30%概率被收藏
    isRecommended: Math.random() > 0.8, // 20%概率被推荐
    url: `https://tools.example.com/${Math.random().toString(36).substr(2, 8)}`,
    sourceUrl: Math.random() > 0.5 ? `https://github.com/example/${Math.random().toString(36).substr(2, 8)}` : null,
    documentUrl: `https://docs.example.com/${Math.random().toString(36).substr(2, 8)}`,
    demoUrl: Math.random() > 0.6 ? `https://demo.example.com/${Math.random().toString(36).substr(2, 8)}` : null,
    tags: randomChoice([
      ['开发', '效率'],
      ['工具', '自动化'],
      ['测试', '质量'],
      ['部署', 'DevOps'],
      ['监控', '运维']
    ]),
    features: randomChoice([
      ['易于使用', '功能强大', '高性能'],
      ['开源免费', '社区支持', '持续更新'],
      ['企业级', '安全可靠', '可扩展'],
      ['轻量级', '快速部署', '零配置']
    ]),
    requirements: {
      os: randomChoice(['Windows', 'macOS', 'Linux', '跨平台']),
      memory: randomChoice(['512MB', '1GB', '2GB', '4GB']),
      disk: randomChoice(['100MB', '500MB', '1GB', '2GB']),
      network: randomChoice(['无需网络', '需要网络', '可选网络'])
    },
    installation: {
      method: randomChoice(['npm install', 'pip install', 'docker pull', '直接下载', '在线使用']),
      command: `install-${Math.random().toString(36).substr(2, 8)}`,
      size: randomChoice(['10MB', '50MB', '100MB', '500MB'])
    },
    pricing: {
      type: randomChoice(['免费', '付费', '免费增值', '企业版']),
      price: randomChoice([0, 9.99, 29.99, 99.99, 299.99]),
      currency: 'USD',
      period: randomChoice(['一次性', '月付', '年付'])
    },
    support: {
      documentation: true,
      community: Math.random() > 0.3,
      email: Math.random() > 0.5,
      phone: Math.random() > 0.8,
      chat: Math.random() > 0.6
    },
    metrics: {
      uptime: parseFloat((Math.random() * 0.05 + 0.95).toFixed(3)), // 95%-100%
      responseTime: randomInt(50, 500), // 毫秒
      errorRate: parseFloat((Math.random() * 0.02).toFixed(3)), // 0%-2%
      lastCheck: formatDate(randomDate(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date()))
    },
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    lastUsedAt: usageCount > 0 ? formatDate(randomDate(createdAt, new Date())) : null,
    changelog: [
      {
        version: `v${randomInt(1, 5)}.${randomInt(0, 9)}.${randomInt(0, 9)}`,
        date: formatDate(randomDate(createdAt, new Date())),
        changes: randomChoice([
          '修复了已知问题',
          '添加了新功能',
          '性能优化',
          '界面改进',
          '安全更新'
        ])
      }
    ]
  }
}

// 生成开发工具列表数据
const devToolsData = Array.from({ length: 67 }, (_, index) => generateDevTool(index + 1))

// 开发工具统计数据
const devToolsStats = {
  total: devToolsData.length,
  available: devToolsData.filter(t => t.status === '可用').length,
  maintenance: devToolsData.filter(t => t.status === '维护中').length,
  deprecated: devToolsData.filter(t => t.status === '已废弃').length,
  development: devToolsData.filter(t => t.status === '开发中').length,
  public: devToolsData.filter(t => t.isPublic).length,
  recommended: devToolsData.filter(t => t.isRecommended).length,
  totalUsage: devToolsData.reduce((sum, t) => sum + t.usageCount, 0),
  totalDownloads: devToolsData.reduce((sum, t) => sum + t.downloadCount, 0),
  avgRating: parseFloat((devToolsData.reduce((sum, t) => sum + t.rating, 0) / devToolsData.length).toFixed(1)),
  byCategory: toolCategories.reduce((acc, category) => {
    acc[category] = devToolsData.filter(t => t.category === category).length
    return acc
  }, {}),
  byType: toolTypes.reduce((acc, type) => {
    acc[type] = devToolsData.filter(t => t.type === type).length
    return acc
  }, {})
}

// 创建CRUD操作
const crudOps = createCrudOperations(devToolsData, {
  idField: 'id',
  searchFields: ['name', 'description', 'author', 'maintainer'],
  defaultSort: 'usageCount'
})

// 导出DevTools mock数据操作
export default {
  ...crudOps,
  
  // 获取开发工具统计
  getStats: () => devToolsStats,
  
  // 获取推荐工具
  getRecommended: ({ params = {} }) => {
    const { limit = 10 } = params
    return devToolsData
      .filter(t => t.isRecommended && t.status === '可用')
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit)
  },
  
  // 获取热门工具
  getPopular: ({ params = {} }) => {
    const { limit = 20 } = params
    return devToolsData
      .filter(t => t.status === '可用' && t.isPublic)
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, limit)
  },
  
  // 获取分类工具
  getByCategory: ({ params = {} }) => {
    const { category, limit = 20 } = params
    let result = devToolsData.filter(t => t.status === '可用' && t.isPublic)
    
    if (category) {
      result = result.filter(t => t.category === category)
    }
    
    return result
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit)
  },
  
  // 使用工具
  use: ({ pathParams }) => {
    const id = pathParams[0]
    const tool = devToolsData.find(t => t.id.toString() === id.toString())
    
    if (!tool) return null
    
    tool.usageCount++
    tool.lastUsedAt = formatDate(new Date())
    tool.updatedAt = formatDate(new Date())
    
    return {
      toolId: tool.id,
      name: tool.name,
      url: tool.url,
      usageCount: tool.usageCount,
      timestamp: Date.now()
    }
  },
  
  // 下载工具
  download: ({ pathParams }) => {
    const id = pathParams[0]
    const tool = devToolsData.find(t => t.id.toString() === id.toString())
    
    if (!tool) return null
    
    tool.downloadCount++
    tool.updatedAt = formatDate(new Date())
    
    return {
      toolId: tool.id,
      name: tool.name,
      downloadUrl: tool.url,
      size: tool.installation.size,
      downloadCount: tool.downloadCount,
      timestamp: Date.now()
    }
  },
  
  // 收藏/取消收藏
  toggleFavorite: ({ pathParams }) => {
    const id = pathParams[0]
    const tool = devToolsData.find(t => t.id.toString() === id.toString())
    
    if (!tool) return null
    
    tool.isFavorite = !tool.isFavorite
    tool.updatedAt = formatDate(new Date())
    
    return tool
  },
  
  // 评分
  rate: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { rating } = data
    const tool = devToolsData.find(t => t.id.toString() === id.toString())
    
    if (!tool || rating < 1 || rating > 5) return null
    
    // 重新计算平均评分
    const totalRating = tool.rating * tool.ratingCount + rating
    tool.ratingCount++
    tool.rating = parseFloat((totalRating / tool.ratingCount).toFixed(1))
    tool.updatedAt = formatDate(new Date())
    
    return tool
  },
  
  // 检查工具状态
  checkStatus: ({ pathParams }) => {
    const id = pathParams[0]
    const tool = devToolsData.find(t => t.id.toString() === id.toString())
    
    if (!tool) return null
    
    // 模拟状态检查
    const isOnline = Math.random() > 0.1 // 90%在线率
    const responseTime = randomInt(50, 500)
    
    tool.metrics.uptime = isOnline ? parseFloat((Math.random() * 0.05 + 0.95).toFixed(3)) : 0
    tool.metrics.responseTime = responseTime
    tool.metrics.errorRate = isOnline ? parseFloat((Math.random() * 0.02).toFixed(3)) : 1
    tool.metrics.lastCheck = formatDate(new Date())
    
    return {
      toolId: tool.id,
      status: isOnline ? '在线' : '离线',
      responseTime,
      uptime: tool.metrics.uptime,
      errorRate: tool.metrics.errorRate,
      lastCheck: tool.metrics.lastCheck
    }
  },
  
  // 获取工具分类
  getCategories: () => {
    return toolCategories.map(category => ({
      name: category,
      count: devToolsData.filter(t => t.category === category).length,
      avgRating: parseFloat((
        devToolsData
          .filter(t => t.category === category)
          .reduce((sum, t) => sum + t.rating, 0) / 
        devToolsData.filter(t => t.category === category).length
      ).toFixed(1))
    }))
  },
  
  // 搜索工具
  search: ({ params = {} }) => {
    const { q, category, type, status, limit = 20 } = params
    let result = [...devToolsData]
    
    if (q) {
      result = result.filter(t => 
        t.name.toLowerCase().includes(q.toLowerCase()) ||
        t.description.toLowerCase().includes(q.toLowerCase()) ||
        t.tags.some(tag => tag.toLowerCase().includes(q.toLowerCase()))
      )
    }
    
    if (category) {
      result = result.filter(t => t.category === category)
    }
    
    if (type) {
      result = result.filter(t => t.type === type)
    }
    
    if (status) {
      result = result.filter(t => t.status === status)
    }
    
    return result
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit)
  }
}
