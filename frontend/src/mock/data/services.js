// Services页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, createCrudOperations } from '../utils'

// 服务类型
const serviceTypes = ['微服务', 'Web服务', 'API服务', '数据服务', '消息服务', '缓存服务']

// 服务状态
const serviceStatuses = ['运行中', '已停止', '维护中', '错误', '部署中']

// 服务环境
const environments = ['开发环境', '测试环境', '预发布环境', '生产环境']

// 技术栈
const techStacks = ['Spring Boot', 'Node.js', 'Python Flask', 'Go Gin', 'Java', 'Docker', 'Kubernetes']

// 生成服务数据
function generateService(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const status = randomChoice(serviceStatuses)
  
  return {
    id: id || generateId(),
    name: `service-${Math.random().toString(36).substr(2, 8)}`,
    displayName: `${randomChoice(['用户', '订单', '支付', '商品', '库存', '通知', '日志', '监控'])}服务`,
    description: `这是一个${randomChoice(serviceTypes)}，负责处理相关业务逻辑`,
    type: randomChoice(serviceTypes),
    status,
    environment: randomChoice(environments),
    version: `v${randomInt(1, 3)}.${randomInt(0, 9)}.${randomInt(0, 9)}`,
    port: randomInt(3000, 9999),
    host: `192.168.1.${randomInt(10, 200)}`,
    url: `http://192.168.1.${randomInt(10, 200)}:${randomInt(3000, 9999)}`,
    healthCheckUrl: `/health`,
    techStack: randomChoice(techStacks),
    cpu: status === '运行中' ? randomInt(10, 80) : 0,
    memory: status === '运行中' ? randomInt(100, 2000) : 0,
    uptime: status === '运行中' ? randomInt(1000, 999999) : 0,
    requestCount: randomInt(1000, 100000),
    errorRate: parseFloat((Math.random() * 5).toFixed(2)),
    responseTime: randomInt(50, 500),
    lastDeployAt: formatDate(randomDate(createdAt, new Date())),
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    owner: randomChoice(['张三', '李四', '王五', '赵六', '钱七']),
    team: randomChoice(['前端团队', '后端团队', '运维团队', '测试团队']),
    dependencies: randomChoice([
      ['redis', 'mysql'],
      ['mongodb', 'rabbitmq'],
      ['elasticsearch', 'kafka'],
      ['postgresql', 'redis']
    ]),
    tags: randomChoice([
      ['核心服务', '高可用'],
      ['业务服务', '可扩展'],
      ['基础服务', '稳定'],
      ['工具服务', '轻量级']
    ]),
    config: {
      replicas: randomInt(1, 5),
      maxMemory: `${randomInt(512, 4096)}MB`,
      maxCpu: `${randomInt(1, 8)}核`,
      autoScale: randomChoice([true, false])
    }
  }
}

// 生成服务列表数据
const servicesData = Array.from({ length: 45 }, (_, index) => generateService(index + 1))

// 服务统计数据
const serviceStats = {
  total: servicesData.length,
  running: servicesData.filter(s => s.status === '运行中').length,
  stopped: servicesData.filter(s => s.status === '已停止').length,
  maintenance: servicesData.filter(s => s.status === '维护中').length,
  error: servicesData.filter(s => s.status === '错误').length,
  deploying: servicesData.filter(s => s.status === '部署中').length,
  byType: serviceTypes.reduce((acc, type) => {
    acc[type] = servicesData.filter(s => s.type === type).length
    return acc
  }, {}),
  byEnvironment: environments.reduce((acc, env) => {
    acc[env] = servicesData.filter(s => s.environment === env).length
    return acc
  }, {})
}

// 创建CRUD操作
const crudOps = createCrudOperations(servicesData, {
  idField: 'id',
  searchFields: ['name', 'displayName', 'description', 'owner'],
  defaultSort: 'createdAt'
})

// 导出Services mock数据操作
export default {
  ...crudOps,
  
  // 获取服务统计
  getStats: () => serviceStats,
  
  // 启动服务
  start: ({ pathParams }) => {
    const id = pathParams[0]
    const service = servicesData.find(s => s.id.toString() === id.toString())
    
    if (!service) return null
    
    service.status = '运行中'
    service.updatedAt = formatDate(new Date())
    service.uptime = 0
    
    return service
  },
  
  // 停止服务
  stop: ({ pathParams }) => {
    const id = pathParams[0]
    const service = servicesData.find(s => s.id.toString() === id.toString())
    
    if (!service) return null
    
    service.status = '已停止'
    service.updatedAt = formatDate(new Date())
    service.cpu = 0
    service.memory = 0
    service.uptime = 0
    
    return service
  },
  
  // 重启服务
  restart: ({ pathParams }) => {
    const id = pathParams[0]
    const service = servicesData.find(s => s.id.toString() === id.toString())
    
    if (!service) return null
    
    service.status = '部署中'
    service.updatedAt = formatDate(new Date())
    
    // 模拟重启过程
    setTimeout(() => {
      service.status = '运行中'
      service.uptime = 0
    }, 2000)
    
    return service
  },
  
  // 获取服务日志
  getLogs: ({ pathParams, params = {} }) => {
    const id = pathParams[0]
    const { limit = 100 } = params
    
    // 生成模拟日志
    const logs = Array.from({ length: limit }, (_, index) => ({
      id: index + 1,
      timestamp: formatDate(randomDate(new Date(Date.now() - 24 * 60 * 60 * 1000), new Date())),
      level: randomChoice(['INFO', 'WARN', 'ERROR', 'DEBUG']),
      message: randomChoice([
        'Service started successfully',
        'Processing request',
        'Database connection established',
        'Cache updated',
        'Request completed',
        'Health check passed'
      ]),
      source: 'application'
    }))
    
    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
  },
  
  // 获取服务监控数据
  getMetrics: ({ pathParams }) => {
    const id = pathParams[0]
    const service = servicesData.find(s => s.id.toString() === id.toString())
    
    if (!service) return null
    
    return {
      serviceId: service.id,
      cpu: service.cpu,
      memory: service.memory,
      uptime: service.uptime,
      requestCount: service.requestCount,
      errorRate: service.errorRate,
      responseTime: service.responseTime,
      timestamp: Date.now()
    }
  },
  
  // 部署服务
  deploy: ({ pathParams, data }) => {
    const id = pathParams[0]
    const service = servicesData.find(s => s.id.toString() === id.toString())
    
    if (!service) return null
    
    service.status = '部署中'
    service.version = data.version || service.version
    service.updatedAt = formatDate(new Date())
    service.lastDeployAt = formatDate(new Date())
    
    // 模拟部署过程
    setTimeout(() => {
      service.status = '运行中'
    }, 3000)
    
    return service
  }
}
