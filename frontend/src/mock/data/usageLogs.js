// Usage Logs页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, randomChineseName, createCrudOperations } from '../utils'

// 使用类型
const usageTypes = ['API调用', '页面访问', '文件下载', '数据导出', '功能使用', '系统操作']

// 资源类型
const resourceTypes = ['用户管理', '订单管理', '商品管理', '报表查询', '系统设置', '文件管理']

// 操作结果
const operationResults = ['成功', '失败', '部分成功', '超时', '取消']

// 生成使用日志数据
function generateUsageLog(id) {
  const createdAt = randomDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()) // 最近30天
  const type = randomChoice(usageTypes)
  const result = randomChoice(operationResults)
  const duration = randomInt(100, 5000) // 毫秒
  
  return {
    id: id || generateId(),
    userId: randomInt(1, 100),
    userName: randomChineseName(),
    userRole: randomChoice(['管理员', '普通用户', '访客', '开发者']),
    sessionId: generateId(),
    type,
    resource: randomChoice(resourceTypes),
    action: randomChoice(['查看', '创建', '编辑', '删除', '导出', '导入', '搜索', '下载']),
    result,
    duration,
    ip: `192.168.${randomInt(1, 255)}.${randomInt(1, 255)}`,
    userAgent: randomChoice([
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ]),
    device: randomChoice(['PC', 'Mobile', 'Tablet']),
    browser: randomChoice(['Chrome', 'Firefox', 'Safari', 'Edge']),
    os: randomChoice(['Windows', 'macOS', 'Linux', 'iOS', 'Android']),
    location: randomChoice(['北京', '上海', '广州', '深圳', '杭州', '成都']),
    requestSize: randomInt(100, 10000), // 字节
    responseSize: randomInt(500, 50000), // 字节
    errorCode: result === '失败' ? randomChoice(['400', '401', '403', '404', '500', '502']) : null,
    errorMessage: result === '失败' ? randomChoice([
      '权限不足',
      '参数错误',
      '资源不存在',
      '服务器错误',
      '网络超时'
    ]) : null,
    apiEndpoint: type === 'API调用' ? randomChoice([
      '/api/users',
      '/api/orders',
      '/api/products',
      '/api/reports',
      '/api/settings'
    ]) : null,
    httpMethod: type === 'API调用' ? randomChoice(['GET', 'POST', 'PUT', 'DELETE']) : null,
    statusCode: result === '成功' ? randomChoice([200, 201, 204]) : (result === '失败' ? randomChoice([400, 401, 403, 404, 500]) : 200),
    referrer: randomChoice([
      'https://admin.example.com/dashboard',
      'https://admin.example.com/users',
      'https://admin.example.com/orders',
      null
    ]),
    tags: randomChoice([
      ['重要操作'],
      ['日常使用'],
      ['批量操作'],
      ['系统管理'],
      ['数据操作']
    ]),
    metadata: {
      feature: randomChoice(['用户管理', '数据分析', '报表生成', '系统配置']),
      module: randomChoice(['前端', '后端', 'API', '数据库']),
      version: `v${randomInt(1, 3)}.${randomInt(0, 9)}.${randomInt(0, 9)}`,
      buildNumber: randomInt(1000, 9999)
    },
    performance: {
      loadTime: randomInt(100, 3000),
      renderTime: randomInt(50, 1000),
      networkTime: randomInt(20, 500),
      serverTime: randomInt(10, 200)
    },
    createdAt: formatDate(createdAt, 'YYYY-MM-DD HH:mm:ss')
  }
}

// 生成使用日志列表数据
const usageLogsData = Array.from({ length: 5000 }, (_, index) => generateUsageLog(index + 1))

// 使用日志统计数据
const usageLogStats = {
  total: usageLogsData.length,
  today: usageLogsData.filter(l => {
    const today = new Date()
    const logDate = new Date(l.createdAt)
    return logDate.toDateString() === today.toDateString()
  }).length,
  thisWeek: usageLogsData.filter(l => {
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    return new Date(l.createdAt) > weekAgo
  }).length,
  thisMonth: usageLogsData.filter(l => {
    const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    return new Date(l.createdAt) > monthAgo
  }).length,
  successRate: parseFloat((usageLogsData.filter(l => l.result === '成功').length / usageLogsData.length * 100).toFixed(2)),
  avgDuration: Math.round(usageLogsData.reduce((sum, l) => sum + l.duration, 0) / usageLogsData.length),
  uniqueUsers: new Set(usageLogsData.map(l => l.userId)).size,
  byType: usageTypes.reduce((acc, type) => {
    acc[type] = usageLogsData.filter(l => l.type === type).length
    return acc
  }, {}),
  byResult: operationResults.reduce((acc, result) => {
    acc[result] = usageLogsData.filter(l => l.result === result).length
    return acc
  }, {}),
  byDevice: ['PC', 'Mobile', 'Tablet'].reduce((acc, device) => {
    acc[device] = usageLogsData.filter(l => l.device === device).length
    return acc
  }, {}),
  topUsers: Object.entries(
    usageLogsData.reduce((acc, l) => {
      acc[l.userName] = (acc[l.userName] || 0) + 1
      return acc
    }, {})
  ).sort(([,a], [,b]) => b - a).slice(0, 10).map(([name, count]) => ({ name, count })),
  topResources: Object.entries(
    usageLogsData.reduce((acc, l) => {
      acc[l.resource] = (acc[l.resource] || 0) + 1
      return acc
    }, {})
  ).sort(([,a], [,b]) => b - a).slice(0, 10).map(([resource, count]) => ({ resource, count }))
}

// 创建CRUD操作（使用日志通常只读）
const crudOps = createCrudOperations(usageLogsData, {
  idField: 'id',
  searchFields: ['userName', 'resource', 'action', 'ip', 'errorMessage'],
  defaultSort: 'createdAt'
})

// 导出Usage Logs mock数据操作
export default {
  // 只保留查询相关的操作
  getList: crudOps.getList,
  getById: crudOps.getById,
  
  // 获取使用日志统计
  getStats: () => usageLogStats,
  
  // 获取用户使用统计
  getUserStats: ({ params = {} }) => {
    const { userId, days = 7 } = params
    let userLogs = usageLogsData
    
    if (userId) {
      userLogs = userLogs.filter(l => l.userId.toString() === userId.toString())
    }
    
    const dayAgo = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    userLogs = userLogs.filter(l => new Date(l.createdAt) > dayAgo)
    
    return {
      totalUsage: userLogs.length,
      successRate: parseFloat((userLogs.filter(l => l.result === '成功').length / userLogs.length * 100).toFixed(2)),
      avgDuration: Math.round(userLogs.reduce((sum, l) => sum + l.duration, 0) / userLogs.length),
      mostUsedResource: Object.entries(
        userLogs.reduce((acc, l) => {
          acc[l.resource] = (acc[l.resource] || 0) + 1
          return acc
        }, {})
      ).sort(([,a], [,b]) => b - a)[0]?.[0] || '无',
      deviceBreakdown: ['PC', 'Mobile', 'Tablet'].reduce((acc, device) => {
        acc[device] = userLogs.filter(l => l.device === device).length
        return acc
      }, {})
    }
  },
  
  // 获取资源使用统计
  getResourceStats: ({ params = {} }) => {
    const { resource, days = 7 } = params
    let resourceLogs = usageLogsData
    
    if (resource) {
      resourceLogs = resourceLogs.filter(l => l.resource === resource)
    }
    
    const dayAgo = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    resourceLogs = resourceLogs.filter(l => new Date(l.createdAt) > dayAgo)
    
    return {
      totalUsage: resourceLogs.length,
      uniqueUsers: new Set(resourceLogs.map(l => l.userId)).size,
      successRate: parseFloat((resourceLogs.filter(l => l.result === '成功').length / resourceLogs.length * 100).toFixed(2)),
      avgDuration: Math.round(resourceLogs.reduce((sum, l) => sum + l.duration, 0) / resourceLogs.length),
      peakHour: Object.entries(
        resourceLogs.reduce((acc, l) => {
          const hour = new Date(l.createdAt).getHours()
          acc[hour] = (acc[hour] || 0) + 1
          return acc
        }, {})
      ).sort(([,a], [,b]) => b - a)[0]?.[0] || '0',
      topActions: Object.entries(
        resourceLogs.reduce((acc, l) => {
          acc[l.action] = (acc[l.action] || 0) + 1
          return acc
        }, {})
      ).sort(([,a], [,b]) => b - a).slice(0, 5).map(([action, count]) => ({ action, count }))
    }
  },
  
  // 获取使用趋势
  getTrends: ({ params = {} }) => {
    const { days = 7, groupBy = 'day' } = params
    const trends = []
    
    if (groupBy === 'day') {
      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
        const dayLogs = usageLogsData.filter(l => {
          const logDate = new Date(l.createdAt)
          return logDate.toDateString() === date.toDateString()
        })
        
        trends.push({
          date: formatDate(date, 'YYYY-MM-DD'),
          total: dayLogs.length,
          success: dayLogs.filter(l => l.result === '成功').length,
          failed: dayLogs.filter(l => l.result === '失败').length,
          uniqueUsers: new Set(dayLogs.map(l => l.userId)).size,
          avgDuration: dayLogs.length > 0 ? Math.round(dayLogs.reduce((sum, l) => sum + l.duration, 0) / dayLogs.length) : 0
        })
      }
    } else if (groupBy === 'hour') {
      for (let i = 23; i >= 0; i--) {
        const hour = new Date().getHours() - i
        const normalizedHour = hour < 0 ? hour + 24 : hour
        
        const hourLogs = usageLogsData.filter(l => {
          const logHour = new Date(l.createdAt).getHours()
          return logHour === normalizedHour
        })
        
        trends.push({
          hour: normalizedHour,
          total: hourLogs.length,
          success: hourLogs.filter(l => l.result === '成功').length,
          failed: hourLogs.filter(l => l.result === '失败').length,
          avgDuration: hourLogs.length > 0 ? Math.round(hourLogs.reduce((sum, l) => sum + l.duration, 0) / hourLogs.length) : 0
        })
      }
    }
    
    return trends
  },
  
  // 导出使用日志
  export: ({ params = {} }) => {
    const { startDate, endDate, userId, resource, type } = params
    let result = [...usageLogsData]
    
    if (startDate) {
      result = result.filter(l => new Date(l.createdAt) >= new Date(startDate))
    }
    
    if (endDate) {
      result = result.filter(l => new Date(l.createdAt) <= new Date(endDate))
    }
    
    if (userId) {
      result = result.filter(l => l.userId.toString() === userId.toString())
    }
    
    if (resource) {
      result = result.filter(l => l.resource === resource)
    }
    
    if (type) {
      result = result.filter(l => l.type === type)
    }
    
    return {
      filename: `usage_logs_${formatDate(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.csv`,
      count: result.length,
      downloadUrl: '/api/usage-logs/export/download',
      estimatedSize: Math.round(result.length * 0.8) + 'KB'
    }
  }
}
