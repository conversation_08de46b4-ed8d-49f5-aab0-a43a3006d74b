// Settings页面Mock数据
import { formatDate } from '../utils'

// 系统设置数据
const settingsData = {
  // 基本设置
  basic: {
    siteName: '企业管理系统',
    siteDescription: '一个功能完整的企业级管理系统',
    siteKeywords: '管理系统,企业应用,后台管理',
    siteLogo: '/logo.png',
    favicon: '/favicon.ico',
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm:ss',
    currency: 'CNY',
    currencySymbol: '¥'
  },
  
  // 安全设置
  security: {
    passwordMinLength: 8,
    passwordRequireUppercase: true,
    passwordRequireLowercase: true,
    passwordRequireNumbers: true,
    passwordRequireSymbols: false,
    passwordExpireDays: 90,
    maxLoginAttempts: 5,
    lockoutDuration: 30, // 分钟
    sessionTimeout: 120, // 分钟
    enableTwoFactor: false,
    enableCaptcha: true,
    enableIpWhitelist: false,
    ipWhitelist: [],
    enableAuditLog: true,
    enableSecurityNotifications: true
  },
  
  // 邮件设置
  email: {
    smtpHost: 'smtp.example.com',
    smtpPort: 587,
    smtpUsername: '<EMAIL>',
    smtpPassword: '********',
    smtpEncryption: 'TLS',
    fromName: '企业管理系统',
    fromEmail: '<EMAIL>',
    enableEmailNotifications: true,
    emailTemplates: {
      welcome: '欢迎使用我们的系统！',
      passwordReset: '您的密码重置链接：{link}',
      notification: '您有新的通知：{message}'
    }
  },
  
  // 短信设置
  sms: {
    provider: 'aliyun',
    accessKeyId: 'LTAI***********',
    accessKeySecret: '********',
    signName: '企业管理系统',
    enableSmsNotifications: true,
    templates: {
      verification: 'SMS_123456789',
      notification: 'SMS_987654321'
    }
  },
  
  // 存储设置
  storage: {
    driver: 'local',
    localPath: '/uploads',
    maxFileSize: 10, // MB
    allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
    enableCompression: true,
    compressionQuality: 80,
    enableWatermark: false,
    watermarkText: '企业管理系统',
    watermarkPosition: 'bottom-right'
  },
  
  // 缓存设置
  cache: {
    driver: 'redis',
    redisHost: '127.0.0.1',
    redisPort: 6379,
    redisPassword: '',
    redisDatabase: 0,
    defaultTtl: 3600, // 秒
    enablePageCache: true,
    pageCacheTtl: 1800,
    enableApiCache: true,
    apiCacheTtl: 300
  },
  
  // 日志设置
  logging: {
    level: 'info',
    enableFileLog: true,
    logPath: '/logs',
    maxFileSize: 100, // MB
    maxFiles: 30,
    enableDatabaseLog: true,
    enableErrorReporting: true,
    errorReportingUrl: 'https://sentry.io/api/...'
  },
  
  // 备份设置
  backup: {
    enableAutoBackup: true,
    backupInterval: 'daily',
    backupTime: '02:00',
    backupPath: '/backups',
    maxBackupFiles: 7,
    enableDatabaseBackup: true,
    enableFileBackup: true,
    enableCloudBackup: false,
    cloudProvider: 'aliyun',
    cloudConfig: {}
  },
  
  // API设置
  api: {
    enableRateLimit: true,
    rateLimit: 1000, // 每小时
    enableApiKey: true,
    enableCors: true,
    corsOrigins: ['*'],
    enableApiDoc: true,
    apiDocPath: '/api/docs',
    enableVersioning: true,
    currentVersion: 'v1'
  },
  
  // 通知设置
  notification: {
    enableInAppNotifications: true,
    enableEmailNotifications: true,
    enableSmsNotifications: false,
    enablePushNotifications: false,
    notificationRetention: 30, // 天
    enableNotificationSound: true,
    enableDesktopNotifications: true
  },
  
  // 主题设置
  theme: {
    primaryColor: '#1890ff',
    secondaryColor: '#52c41a',
    errorColor: '#ff4d4f',
    warningColor: '#faad14',
    successColor: '#52c41a',
    infoColor: '#1890ff',
    borderRadius: 6,
    enableDarkMode: true,
    defaultTheme: 'light',
    enableCustomThemes: true
  },
  
  // 性能设置
  performance: {
    enableGzip: true,
    enableMinification: true,
    enableCdn: false,
    cdnUrl: '',
    enableLazyLoading: true,
    enableImageOptimization: true,
    enableCaching: true,
    cacheStrategy: 'aggressive'
  },
  
  // 集成设置
  integrations: {
    enableOAuth: true,
    oauthProviders: {
      google: {
        enabled: true,
        clientId: 'google_client_id',
        clientSecret: '********'
      },
      github: {
        enabled: true,
        clientId: 'github_client_id',
        clientSecret: '********'
      },
      wechat: {
        enabled: false,
        appId: '',
        appSecret: '********'
      }
    },
    enableWebhooks: true,
    webhookUrl: 'https://api.example.com/webhooks',
    webhookSecret: '********'
  }
}

// 设置分组配置
const settingsGroups = [
  {
    key: 'basic',
    name: '基本设置',
    description: '系统基本信息和显示设置',
    icon: '⚙️'
  },
  {
    key: 'security',
    name: '安全设置',
    description: '密码策略、登录安全等设置',
    icon: '🔒'
  },
  {
    key: 'email',
    name: '邮件设置',
    description: 'SMTP配置和邮件模板设置',
    icon: '📧'
  },
  {
    key: 'sms',
    name: '短信设置',
    description: '短信服务商配置和模板设置',
    icon: '📱'
  },
  {
    key: 'storage',
    name: '存储设置',
    description: '文件上传和存储相关配置',
    icon: '💾'
  },
  {
    key: 'cache',
    name: '缓存设置',
    description: '缓存驱动和策略配置',
    icon: '⚡'
  },
  {
    key: 'logging',
    name: '日志设置',
    description: '日志记录和错误报告配置',
    icon: '📋'
  },
  {
    key: 'backup',
    name: '备份设置',
    description: '自动备份和恢复配置',
    icon: '💿'
  },
  {
    key: 'api',
    name: 'API设置',
    description: 'API访问控制和文档配置',
    icon: '🔌'
  },
  {
    key: 'notification',
    name: '通知设置',
    description: '各种通知方式的配置',
    icon: '🔔'
  },
  {
    key: 'theme',
    name: '主题设置',
    description: '界面主题和样式配置',
    icon: '🎨'
  },
  {
    key: 'performance',
    name: '性能设置',
    description: '性能优化相关配置',
    icon: '🚀'
  },
  {
    key: 'integrations',
    name: '集成设置',
    description: '第三方服务集成配置',
    icon: '🔗'
  }
]

// 导出Settings mock数据操作
export default {
  // 获取所有设置
  getAll: () => ({
    settings: settingsData,
    groups: settingsGroups,
    lastUpdated: formatDate(new Date())
  }),
  
  // 获取设置分组
  getGroups: () => settingsGroups,
  
  // 获取特定分组的设置
  getGroup: ({ pathParams }) => {
    const groupKey = pathParams[0]
    const group = settingsGroups.find(g => g.key === groupKey)
    
    if (!group) return null
    
    return {
      group,
      settings: settingsData[groupKey] || {},
      lastUpdated: formatDate(new Date())
    }
  },
  
  // 更新设置
  update: ({ data }) => {
    const { group, settings } = data
    
    if (settingsData[group]) {
      Object.assign(settingsData[group], settings)
      
      return {
        success: true,
        message: '设置更新成功',
        settings: settingsData[group],
        lastUpdated: formatDate(new Date())
      }
    }
    
    return {
      success: false,
      message: '设置分组不存在'
    }
  },
  
  // 重置设置到默认值
  reset: ({ data }) => {
    const { group } = data
    
    // 这里应该重置到默认值，为了演示，我们只是返回成功
    return {
      success: true,
      message: `${group}设置已重置为默认值`,
      settings: settingsData[group] || {},
      lastUpdated: formatDate(new Date())
    }
  },
  
  // 导出设置
  export: () => {
    return {
      filename: `settings_${formatDate(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.json`,
      data: settingsData,
      downloadUrl: '/api/settings/export/download',
      size: JSON.stringify(settingsData).length + ' bytes'
    }
  },
  
  // 导入设置
  import: ({ data }) => {
    const { settings } = data
    
    try {
      // 验证设置格式
      if (typeof settings !== 'object') {
        throw new Error('设置格式无效')
      }
      
      // 合并设置
      Object.keys(settings).forEach(group => {
        if (settingsData[group]) {
          Object.assign(settingsData[group], settings[group])
        }
      })
      
      return {
        success: true,
        message: '设置导入成功',
        importedGroups: Object.keys(settings),
        lastUpdated: formatDate(new Date())
      }
    } catch (error) {
      return {
        success: false,
        message: `设置导入失败: ${error.message}`
      }
    }
  },
  
  // 测试邮件配置
  testEmail: ({ data }) => {
    const { testEmail } = data
    
    // 模拟邮件发送测试
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          success: Math.random() > 0.2, // 80%成功率
          message: Math.random() > 0.2 ? '测试邮件发送成功' : 'SMTP连接失败',
          testEmail,
          timestamp: formatDate(new Date())
        })
      }, 2000)
    })
  },
  
  // 测试短信配置
  testSms: ({ data }) => {
    const { testPhone } = data
    
    // 模拟短信发送测试
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          success: Math.random() > 0.3, // 70%成功率
          message: Math.random() > 0.3 ? '测试短信发送成功' : '短信服务配置错误',
          testPhone,
          timestamp: formatDate(new Date())
        })
      }, 1500)
    })
  },
  
  // 清理缓存
  clearCache: () => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '缓存清理成功',
          clearedItems: Math.floor(Math.random() * 1000) + 100,
          timestamp: formatDate(new Date())
        })
      }, 1000)
    })
  }
}
