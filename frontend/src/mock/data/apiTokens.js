// API Tokens页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, randomString, createCrudOperations } from '../utils'

// Token状态
const tokenStatuses = ['正常', '已禁用', '已过期', '即将过期']

// Token类型
const tokenTypes = ['只读', '读写', '管理员', '临时', '系统']

// 权限范围
const scopes = ['users:read', 'users:write', 'orders:read', 'orders:write', 'products:read', 'products:write', 'reports:read', 'system:admin']

// 生成API Token数据
function generateApiToken(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const expiresAt = randomDate(new Date(), new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)) // 未来1年内
  const isExpired = expiresAt < new Date()
  const isExpiringSoon = !isExpired && (expiresAt - new Date()) < (30 * 24 * 60 * 60 * 1000) // 30天内过期
  
  let status = '正常'
  if (isExpired) status = '已过期'
  else if (isExpiringSoon) status = '即将过期'
  else if (Math.random() > 0.9) status = '已禁用'
  
  const usageCount = randomInt(0, 10000)
  const lastUsedAt = usageCount > 0 ? randomDate(createdAt, new Date()) : null
  
  return {
    id: id || generateId(),
    name: `API Token ${id || Math.random().toString(36).substr(2, 4)}`,
    description: `用于${randomChoice(['第三方集成', '移动应用', 'Web应用', '数据同步', '自动化脚本'])}的API访问令牌`,
    token: `tk_${randomString(32)}`, // 模拟token格式
    type: randomChoice(tokenTypes),
    status,
    scopes: randomChoice([
      ['users:read'],
      ['users:read', 'users:write'],
      ['orders:read', 'products:read'],
      ['users:read', 'orders:read', 'products:read'],
      ['system:admin']
    ]),
    usageCount,
    usageLimit: randomChoice([null, 1000, 5000, 10000, 50000]), // null表示无限制
    rateLimit: randomChoice([100, 500, 1000, 5000]), // 每小时请求限制
    ipWhitelist: Math.random() > 0.7 ? [
      `192.168.1.${randomInt(1, 255)}`,
      `10.0.0.${randomInt(1, 255)}`
    ] : [], // 30%概率有IP白名单
    lastUsedAt: lastUsedAt ? formatDate(lastUsedAt) : null,
    lastUsedIp: lastUsedAt ? `192.168.1.${randomInt(1, 255)}` : null,
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    expiresAt: formatDate(expiresAt),
    createdBy: randomChoice(['张三', '李四', '王五', '系统管理员']),
    application: randomChoice(['移动应用', 'Web应用', '数据分析工具', '第三方集成', '自动化脚本']),
    environment: randomChoice(['开发环境', '测试环境', '生产环境']),
    metadata: {
      userAgent: randomChoice([
        'MyApp/1.0',
        'DataSync/2.1',
        'WebApp/3.0',
        'AutoScript/1.5'
      ]),
      version: `v${randomInt(1, 3)}.${randomInt(0, 9)}`,
      platform: randomChoice(['iOS', 'Android', 'Web', 'Server', 'Desktop'])
    },
    usage: {
      today: randomInt(0, 100),
      thisWeek: randomInt(0, 700),
      thisMonth: randomInt(0, 3000),
      avgDaily: randomInt(10, 200),
      peakHour: randomInt(0, 23),
      errorRate: parseFloat((Math.random() * 5).toFixed(2))
    }
  }
}

// 生成API Token列表数据
const apiTokensData = Array.from({ length: 34 }, (_, index) => generateApiToken(index + 1))

// API Token统计数据
const apiTokenStats = {
  total: apiTokensData.length,
  active: apiTokensData.filter(t => t.status === '正常').length,
  disabled: apiTokensData.filter(t => t.status === '已禁用').length,
  expired: apiTokensData.filter(t => t.status === '已过期').length,
  expiringSoon: apiTokensData.filter(t => t.status === '即将过期').length,
  totalUsage: apiTokensData.reduce((sum, t) => sum + t.usageCount, 0),
  avgUsage: Math.round(apiTokensData.reduce((sum, t) => sum + t.usageCount, 0) / apiTokensData.length),
  byType: tokenTypes.reduce((acc, type) => {
    acc[type] = apiTokensData.filter(t => t.type === type).length
    return acc
  }, {}),
  byEnvironment: ['开发环境', '测试环境', '生产环境'].reduce((acc, env) => {
    acc[env] = apiTokensData.filter(t => t.environment === env).length
    return acc
  }, {})
}

// 创建CRUD操作
const crudOps = createCrudOperations(apiTokensData, {
  idField: 'id',
  searchFields: ['name', 'description', 'application', 'createdBy'],
  defaultSort: 'createdAt'
})

// 导出API Tokens mock数据操作
export default {
  ...crudOps,
  
  // 获取API Token统计
  getStats: () => apiTokenStats,
  
  // 生成新Token
  generate: ({ data }) => {
    const { name, description, type, scopes, expiresAt, usageLimit, rateLimit, ipWhitelist } = data
    
    const newToken = {
      id: generateId(),
      name,
      description,
      token: `tk_${randomString(32)}`,
      type,
      status: '正常',
      scopes: scopes || ['users:read'],
      usageCount: 0,
      usageLimit,
      rateLimit,
      ipWhitelist: ipWhitelist || [],
      lastUsedAt: null,
      lastUsedIp: null,
      createdAt: formatDate(new Date()),
      updatedAt: formatDate(new Date()),
      expiresAt,
      createdBy: '当前用户',
      application: data.application || '未指定',
      environment: data.environment || '开发环境',
      metadata: {
        userAgent: 'Unknown',
        version: 'v1.0',
        platform: 'Unknown'
      },
      usage: {
        today: 0,
        thisWeek: 0,
        thisMonth: 0,
        avgDaily: 0,
        peakHour: 0,
        errorRate: 0
      }
    }
    
    apiTokensData.unshift(newToken)
    return newToken
  },
  
  // 重新生成Token
  regenerate: ({ pathParams }) => {
    const id = pathParams[0]
    const token = apiTokensData.find(t => t.id.toString() === id.toString())
    
    if (!token) return null
    
    token.token = `tk_${randomString(32)}`
    token.updatedAt = formatDate(new Date())
    token.usageCount = 0
    token.lastUsedAt = null
    token.lastUsedIp = null
    
    return token
  },
  
  // 启用/禁用Token
  toggleStatus: ({ pathParams }) => {
    const id = pathParams[0]
    const token = apiTokensData.find(t => t.id.toString() === id.toString())
    
    if (!token) return null
    
    token.status = token.status === '正常' ? '已禁用' : '正常'
    token.updatedAt = formatDate(new Date())
    
    return token
  },
  
  // 更新Token权限
  updateScopes: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { scopes } = data
    const token = apiTokensData.find(t => t.id.toString() === id.toString())
    
    if (!token) return null
    
    token.scopes = scopes
    token.updatedAt = formatDate(new Date())
    
    return token
  },
  
  // 更新使用限制
  updateLimits: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { usageLimit, rateLimit } = data
    const token = apiTokensData.find(t => t.id.toString() === id.toString())
    
    if (!token) return null
    
    token.usageLimit = usageLimit
    token.rateLimit = rateLimit
    token.updatedAt = formatDate(new Date())
    
    return token
  },
  
  // 更新IP白名单
  updateIpWhitelist: ({ pathParams, data }) => {
    const id = pathParams[0]
    const { ipWhitelist } = data
    const token = apiTokensData.find(t => t.id.toString() === id.toString())
    
    if (!token) return null
    
    token.ipWhitelist = ipWhitelist
    token.updatedAt = formatDate(new Date())
    
    return token
  },
  
  // 获取Token使用统计
  getUsageStats: ({ pathParams, params = {} }) => {
    const id = pathParams[0]
    const { days = 7 } = params
    const token = apiTokensData.find(t => t.id.toString() === id.toString())
    
    if (!token) return null
    
    // 生成使用统计数据
    const stats = []
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
      stats.push({
        date: formatDate(date, 'YYYY-MM-DD'),
        requests: randomInt(0, 200),
        errors: randomInt(0, 10),
        avgResponseTime: randomInt(100, 1000)
      })
    }
    
    return {
      tokenId: token.id,
      period: `${days}天`,
      stats,
      summary: {
        totalRequests: stats.reduce((sum, s) => sum + s.requests, 0),
        totalErrors: stats.reduce((sum, s) => sum + s.errors, 0),
        avgResponseTime: Math.round(stats.reduce((sum, s) => sum + s.avgResponseTime, 0) / stats.length),
        errorRate: parseFloat((stats.reduce((sum, s) => sum + s.errors, 0) / stats.reduce((sum, s) => sum + s.requests, 0) * 100).toFixed(2))
      }
    }
  },
  
  // 获取Token访问日志
  getAccessLogs: ({ pathParams, params = {} }) => {
    const id = pathParams[0]
    const { limit = 50 } = params
    
    // 生成访问日志
    const logs = Array.from({ length: limit }, (_, i) => ({
      id: generateId(),
      timestamp: formatDate(randomDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date())),
      ip: `192.168.1.${randomInt(1, 255)}`,
      method: randomChoice(['GET', 'POST', 'PUT', 'DELETE']),
      endpoint: randomChoice(['/api/users', '/api/orders', '/api/products', '/api/reports']),
      statusCode: randomChoice([200, 201, 400, 401, 403, 404, 500]),
      responseTime: randomInt(50, 2000),
      userAgent: randomChoice(['MyApp/1.0', 'DataSync/2.1', 'WebApp/3.0'])
    }))
    
    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
  },
  
  // 批量操作
  batchUpdate: ({ data }) => {
    const { ids, updates } = data
    let updatedCount = 0
    
    ids.forEach(id => {
      const token = apiTokensData.find(t => t.id.toString() === id.toString())
      if (token) {
        Object.assign(token, updates, { updatedAt: formatDate(new Date()) })
        updatedCount++
      }
    })
    
    return { updatedCount }
  }
}
