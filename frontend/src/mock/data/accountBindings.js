// Account Bindings页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, randomChineseName, createCrudOperations } from '../utils'

// 账号类型
const accountTypes = ['Google', 'GitHub', 'WeChat', 'QQ', 'Weibo', 'DingTalk', 'Feishu', 'Microsoft']

// 绑定状态
const bindingStatuses = ['已绑定', '未绑定', '绑定失败', '已过期']

// 权限范围
const permissionScopes = ['基本信息', '邮箱地址', '头像', '好友列表', '发布动态', '读取动态']

// 生成账号绑定数据
function generateAccountBinding(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const accountType = randomChoice(accountTypes)
  const status = randomChoice(bindingStatuses)
  const lastUsedAt = status === '已绑定' ? randomDate(createdAt, new Date()) : null
  
  // 根据账号类型生成相应的信息
  const accountInfo = {
    'Google': {
      icon: '🔍',
      color: '#4285f4',
      displayName: 'Google账号',
      description: '使用Google账号登录，访问Gmail、Drive等服务'
    },
    'GitHub': {
      icon: '🐙',
      color: '#333',
      displayName: 'GitHub账号',
      description: '使用GitHub账号登录，访问代码仓库和开发者服务'
    },
    'WeChat': {
      icon: '💬',
      color: '#07c160',
      displayName: '微信账号',
      description: '使用微信账号登录，享受便捷的社交体验'
    },
    'QQ': {
      icon: '🐧',
      color: '#12b7f5',
      displayName: 'QQ账号',
      description: '使用QQ账号登录，连接你的QQ好友'
    },
    'Weibo': {
      icon: '📱',
      color: '#e6162d',
      displayName: '微博账号',
      description: '使用微博账号登录，分享你的动态'
    },
    'DingTalk': {
      icon: '💼',
      color: '#0089ff',
      displayName: '钉钉账号',
      description: '使用钉钉账号登录，连接企业办公'
    },
    'Feishu': {
      icon: '🚀',
      color: '#00d4aa',
      displayName: '飞书账号',
      description: '使用飞书账号登录，提升团队协作效率'
    },
    'Microsoft': {
      icon: '🪟',
      color: '#0078d4',
      displayName: 'Microsoft账号',
      description: '使用Microsoft账号登录，访问Office 365服务'
    }
  }
  
  const info = accountInfo[accountType] || accountInfo['Google']
  
  return {
    id: id || generateId(),
    userId: randomInt(1, 100),
    userName: randomChineseName(),
    accountType,
    status,
    displayName: info.displayName,
    description: info.description,
    icon: info.icon,
    color: info.color,
    thirdPartyId: `${accountType.toLowerCase()}_${randomInt(100000, 999999)}`,
    thirdPartyUsername: `user_${Math.random().toString(36).substr(2, 8)}`,
    thirdPartyEmail: `user${randomInt(1000, 9999)}@${accountType.toLowerCase()}.com`,
    thirdPartyAvatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${Math.random()}`,
    accessToken: status === '已绑定' ? `at_${Math.random().toString(36).substr(2, 32)}` : null,
    refreshToken: status === '已绑定' ? `rt_${Math.random().toString(36).substr(2, 32)}` : null,
    tokenExpiresAt: status === '已绑定' ? formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)) : null, // 30天后过期
    scopes: status === '已绑定' ? randomChoice([
      ['基本信息', '邮箱地址'],
      ['基本信息', '邮箱地址', '头像'],
      ['基本信息', '邮箱地址', '头像', '好友列表'],
      permissionScopes
    ]) : [],
    isDefault: Math.random() > 0.8, // 20%概率是默认登录方式
    isEnabled: status === '已绑定',
    loginCount: status === '已绑定' ? randomInt(1, 100) : 0,
    lastLoginAt: lastUsedAt ? formatDate(lastUsedAt) : null,
    lastUsedAt: lastUsedAt ? formatDate(lastUsedAt) : null,
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    bindingIp: `192.168.${randomInt(1, 255)}.${randomInt(1, 255)}`,
    bindingLocation: randomChoice(['北京', '上海', '广州', '深圳', '杭州', '成都']),
    bindingDevice: randomChoice(['PC', 'Mobile', 'Tablet']),
    errorMessage: status === '绑定失败' ? randomChoice([
      '第三方服务暂时不可用',
      '授权已被用户取消',
      '账号信息验证失败',
      '网络连接超时',
      '服务器内部错误'
    ]) : null,
    lastErrorAt: status === '绑定失败' ? formatDate(randomDate(createdAt, new Date())) : null,
    retryCount: status === '绑定失败' ? randomInt(1, 5) : 0,
    settings: {
      autoLogin: Math.random() > 0.5,
      syncProfile: Math.random() > 0.3,
      syncAvatar: Math.random() > 0.4,
      enableNotifications: Math.random() > 0.6,
      shareActivity: Math.random() > 0.7
    },
    metadata: {
      appVersion: `v${randomInt(1, 3)}.${randomInt(0, 9)}.${randomInt(0, 9)}`,
      sdkVersion: `${accountType.toLowerCase()}-sdk-${randomInt(1, 5)}.${randomInt(0, 9)}`,
      userAgent: randomChoice([
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0)',
        'Mozilla/5.0 (Android 10; Mobile)'
      ])
    }
  }
}

// 生成账号绑定列表数据
const accountBindingsData = Array.from({ length: 28 }, (_, index) => generateAccountBinding(index + 1))

// 账号绑定统计数据
const accountBindingStats = {
  total: accountBindingsData.length,
  bound: accountBindingsData.filter(a => a.status === '已绑定').length,
  unbound: accountBindingsData.filter(a => a.status === '未绑定').length,
  failed: accountBindingsData.filter(a => a.status === '绑定失败').length,
  expired: accountBindingsData.filter(a => a.status === '已过期').length,
  enabled: accountBindingsData.filter(a => a.isEnabled).length,
  totalLogins: accountBindingsData.reduce((sum, a) => sum + a.loginCount, 0),
  byType: accountTypes.reduce((acc, type) => {
    acc[type] = accountBindingsData.filter(a => a.accountType === type).length
    return acc
  }, {}),
  mostUsedType: Object.entries(
    accountBindingsData.reduce((acc, a) => {
      acc[a.accountType] = (acc[a.accountType] || 0) + a.loginCount
      return acc
    }, {})
  ).sort(([,a], [,b]) => b - a)[0]?.[0] || '无'
}

// 创建CRUD操作
const crudOps = createCrudOperations(accountBindingsData, {
  idField: 'id',
  searchFields: ['accountType', 'thirdPartyUsername', 'thirdPartyEmail', 'userName'],
  defaultSort: 'lastUsedAt'
})

// 导出Account Bindings mock数据操作
export default {
  ...crudOps,
  
  // 获取账号绑定统计
  getStats: () => accountBindingStats,
  
  // 获取用户的账号绑定
  getUserBindings: ({ params = {} }) => {
    const { userId } = params
    let result = accountBindingsData
    
    if (userId) {
      result = result.filter(a => a.userId.toString() === userId.toString())
    }
    
    return result.sort((a, b) => new Date(b.lastUsedAt || 0) - new Date(a.lastUsedAt || 0))
  },
  
  // 绑定账号
  bind: ({ data }) => {
    const { accountType, authCode } = data
    
    // 模拟绑定过程
    return new Promise(resolve => {
      setTimeout(() => {
        const success = Math.random() > 0.2 // 80%成功率
        
        if (success) {
          const binding = {
            id: generateId(),
            userId: 1, // 当前用户
            userName: '当前用户',
            accountType,
            status: '已绑定',
            displayName: accountBindingsData.find(a => a.accountType === accountType)?.displayName || accountType,
            description: accountBindingsData.find(a => a.accountType === accountType)?.description || '',
            icon: accountBindingsData.find(a => a.accountType === accountType)?.icon || '🔗',
            color: accountBindingsData.find(a => a.accountType === accountType)?.color || '#666',
            thirdPartyId: `${accountType.toLowerCase()}_${randomInt(100000, 999999)}`,
            thirdPartyUsername: `user_${Math.random().toString(36).substr(2, 8)}`,
            thirdPartyEmail: `user${randomInt(1000, 9999)}@${accountType.toLowerCase()}.com`,
            thirdPartyAvatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${Math.random()}`,
            accessToken: `at_${Math.random().toString(36).substr(2, 32)}`,
            refreshToken: `rt_${Math.random().toString(36).substr(2, 32)}`,
            tokenExpiresAt: formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)),
            scopes: ['基本信息', '邮箱地址', '头像'],
            isDefault: false,
            isEnabled: true,
            loginCount: 0,
            lastLoginAt: null,
            lastUsedAt: null,
            createdAt: formatDate(new Date()),
            updatedAt: formatDate(new Date()),
            bindingIp: '*************',
            bindingLocation: '北京',
            bindingDevice: 'PC',
            errorMessage: null,
            lastErrorAt: null,
            retryCount: 0,
            settings: {
              autoLogin: true,
              syncProfile: true,
              syncAvatar: true,
              enableNotifications: true,
              shareActivity: false
            },
            metadata: {
              appVersion: 'v1.0.0',
              sdkVersion: `${accountType.toLowerCase()}-sdk-2.1`,
              userAgent: 'Mozilla/5.0'
            }
          }
          
          accountBindingsData.unshift(binding)
          
          resolve({
            success: true,
            message: `${accountType}账号绑定成功`,
            binding
          })
        } else {
          resolve({
            success: false,
            message: randomChoice([
              '第三方服务暂时不可用',
              '授权已被用户取消',
              '账号信息验证失败'
            ])
          })
        }
      }, 2000)
    })
  },
  
  // 解绑账号
  unbind: ({ pathParams }) => {
    const id = pathParams[0]
    const binding = accountBindingsData.find(a => a.id.toString() === id.toString())
    
    if (!binding) return null
    
    binding.status = '未绑定'
    binding.isEnabled = false
    binding.accessToken = null
    binding.refreshToken = null
    binding.tokenExpiresAt = null
    binding.scopes = []
    binding.updatedAt = formatDate(new Date())
    
    return {
      success: true,
      message: `${binding.accountType}账号解绑成功`,
      binding
    }
  },
  
  // 刷新Token
  refreshToken: ({ pathParams }) => {
    const id = pathParams[0]
    const binding = accountBindingsData.find(a => a.id.toString() === id.toString())
    
    if (!binding || binding.status !== '已绑定') return null
    
    // 模拟刷新Token
    return new Promise(resolve => {
      setTimeout(() => {
        const success = Math.random() > 0.1 // 90%成功率
        
        if (success) {
          binding.accessToken = `at_${Math.random().toString(36).substr(2, 32)}`
          binding.refreshToken = `rt_${Math.random().toString(36).substr(2, 32)}`
          binding.tokenExpiresAt = formatDate(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000))
          binding.updatedAt = formatDate(new Date())
          
          resolve({
            success: true,
            message: 'Token刷新成功',
            binding
          })
        } else {
          binding.status = '已过期'
          binding.errorMessage = 'Token刷新失败，请重新绑定'
          binding.lastErrorAt = formatDate(new Date())
          
          resolve({
            success: false,
            message: 'Token刷新失败，请重新绑定',
            binding
          })
        }
      }, 1000)
    })
  },
  
  // 设置默认登录方式
  setDefault: ({ pathParams }) => {
    const id = pathParams[0]
    const binding = accountBindingsData.find(a => a.id.toString() === id.toString())
    
    if (!binding || binding.status !== '已绑定') return null
    
    // 取消其他默认设置
    accountBindingsData.forEach(a => {
      if (a.userId === binding.userId) {
        a.isDefault = false
      }
    })
    
    // 设置当前为默认
    binding.isDefault = true
    binding.updatedAt = formatDate(new Date())
    
    return binding
  },
  
  // 更新绑定设置
  updateSettings: ({ pathParams, data }) => {
    const id = pathParams[0]
    const binding = accountBindingsData.find(a => a.id.toString() === id.toString())
    
    if (!binding) return null
    
    Object.assign(binding.settings, data)
    binding.updatedAt = formatDate(new Date())
    
    return binding
  },
  
  // 获取可用的账号类型
  getAvailableTypes: () => {
    return accountTypes.map(type => {
      const info = accountBindingsData.find(a => a.accountType === type)
      return {
        type,
        displayName: info?.displayName || type,
        description: info?.description || `使用${type}账号登录`,
        icon: info?.icon || '🔗',
        color: info?.color || '#666',
        isSupported: true,
        isPopular: ['Google', 'GitHub', 'WeChat'].includes(type)
      }
    })
  },
  
  // 获取绑定历史
  getBindingHistory: ({ pathParams, params = {} }) => {
    const id = pathParams[0]
    const { limit = 20 } = params
    
    // 生成绑定历史记录
    const history = Array.from({ length: limit }, (_, i) => ({
      id: generateId(),
      action: randomChoice(['绑定', '解绑', '登录', '刷新Token', '更新设置']),
      timestamp: formatDate(randomDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date())),
      ip: `192.168.${randomInt(1, 255)}.${randomInt(1, 255)}`,
      location: randomChoice(['北京', '上海', '广州', '深圳']),
      device: randomChoice(['PC', 'Mobile', 'Tablet']),
      userAgent: randomChoice([
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0)',
        'Mozilla/5.0 (Android 10; Mobile)'
      ]),
      result: randomChoice(['成功', '失败']),
      details: randomChoice([
        '操作成功完成',
        '网络连接超时',
        '第三方服务不可用',
        '用户取消操作'
      ])
    }))
    
    return history.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
  }
}
