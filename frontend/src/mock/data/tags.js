// Tags页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, createCrudOperations } from '../utils'

// 标签颜色
const tagColors = ['blue', 'green', 'red', 'yellow', 'purple', 'pink', 'indigo', 'gray', 'orange', 'teal']

// 标签类型
const tagTypes = ['系统标签', '用户标签', '自动标签', '临时标签']

// 标签状态
const tagStatuses = ['正常', '禁用', '待审核']

// 预定义标签名称
const tagNames = [
  '重要', '紧急', '待处理', '已完成', '进行中', '暂停', '取消',
  '前端', '后端', '数据库', '运维', '测试', '设计', '产品',
  '新功能', '修复', '优化', '重构', '文档', '培训',
  '高优先级', '中优先级', '低优先级', '临时', '长期',
  'React', 'Vue', 'Angular', 'Node.js', 'Python', 'Java', 'Go'
]

// 生成标签数据
function generateTag(id) {
  const createdAt = randomDate(new Date(2023, 0, 1), new Date())
  const usageCount = randomInt(0, 500)
  
  return {
    id: id || generateId(),
    name: randomChoice(tagNames),
    slug: `tag-${Math.random().toString(36).substr(2, 8)}`,
    description: `这是一个${randomChoice(tagTypes)}，用于标记和分类相关内容`,
    color: randomChoice(tagColors),
    type: randomChoice(tagTypes),
    status: randomChoice(tagStatuses),
    usageCount,
    isSystem: Math.random() > 0.7, // 30%概率是系统标签
    isHot: usageCount > 100, // 使用次数超过100为热门标签
    weight: randomInt(1, 10), // 权重，影响推荐排序
    categoryId: Math.random() > 0.5 ? randomInt(1, 10) : null, // 50%概率关联分类
    createdAt: formatDate(createdAt),
    updatedAt: formatDate(randomDate(createdAt, new Date())),
    createdBy: randomChoice(['张三', '李四', '王五', '赵六', '系统']),
    settings: {
      autoComplete: randomChoice([true, false]),
      caseSensitive: randomChoice([true, false]),
      allowDuplicate: randomChoice([true, false]),
      requireApproval: randomChoice([true, false])
    },
    seo: {
      title: `${randomChoice(tagNames)} - 标签页`,
      description: `关于${randomChoice(tagNames)}标签的相关内容和信息`,
      keywords: randomChoice([
        '标签,分类,管理',
        '内容,标记,组织',
        '系统,标签,搜索'
      ])
    },
    statistics: {
      weeklyUsage: randomInt(0, 50),
      monthlyUsage: randomInt(0, 200),
      peakUsageDate: formatDate(randomDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date())),
      relatedTags: randomInt(0, 10)
    }
  }
}

// 生成标签列表数据
const tagsData = Array.from({ length: 85 }, (_, index) => generateTag(index + 1))

// 确保标签名称唯一性
const usedNames = new Set()
tagsData.forEach(tag => {
  let baseName = tag.name
  let counter = 1
  
  while (usedNames.has(tag.name)) {
    tag.name = `${baseName}${counter}`
    counter++
  }
  
  usedNames.add(tag.name)
})

// 标签统计数据
const tagStats = {
  total: tagsData.length,
  normal: tagsData.filter(t => t.status === '正常').length,
  disabled: tagsData.filter(t => t.status === '禁用').length,
  pending: tagsData.filter(t => t.status === '待审核').length,
  system: tagsData.filter(t => t.isSystem).length,
  hot: tagsData.filter(t => t.isHot).length,
  totalUsage: tagsData.reduce((sum, t) => sum + t.usageCount, 0),
  byType: tagTypes.reduce((acc, type) => {
    acc[type] = tagsData.filter(t => t.type === type).length
    return acc
  }, {}),
  byColor: tagColors.reduce((acc, color) => {
    acc[color] = tagsData.filter(t => t.color === color).length
    return acc
  }, {})
}

// 创建CRUD操作
const crudOps = createCrudOperations(tagsData, {
  idField: 'id',
  searchFields: ['name', 'slug', 'description'],
  defaultSort: 'usageCount'
})

// 导出Tags mock数据操作
export default {
  ...crudOps,
  
  // 获取标签统计
  getStats: () => tagStats,
  
  // 获取热门标签
  getHotTags: ({ params = {} }) => {
    const { limit = 20 } = params
    return tagsData
      .filter(t => t.status === '正常')
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, limit)
  },
  
  // 获取推荐标签
  getRecommended: ({ params = {} }) => {
    const { limit = 10, excludeIds = [] } = params
    return tagsData
      .filter(t => t.status === '正常' && !excludeIds.includes(t.id))
      .sort((a, b) => (b.weight * b.usageCount) - (a.weight * a.usageCount))
      .slice(0, limit)
  },
  
  // 搜索标签
  search: ({ params = {} }) => {
    const { q, limit = 20 } = params
    if (!q) return []
    
    return tagsData
      .filter(t => 
        t.status === '正常' && 
        t.name.toLowerCase().includes(q.toLowerCase())
      )
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, limit)
  },
  
  // 合并标签
  merge: ({ data }) => {
    const { sourceIds, targetId } = data
    const targetTag = tagsData.find(t => t.id.toString() === targetId.toString())
    
    if (!targetTag) return null
    
    let mergedUsage = targetTag.usageCount
    
    // 删除源标签并累加使用次数
    sourceIds.forEach(sourceId => {
      const sourceIndex = tagsData.findIndex(t => t.id.toString() === sourceId.toString())
      if (sourceIndex !== -1) {
        mergedUsage += tagsData[sourceIndex].usageCount
        tagsData.splice(sourceIndex, 1)
      }
    })
    
    targetTag.usageCount = mergedUsage
    targetTag.updatedAt = formatDate(new Date())
    
    return targetTag
  },
  
  // 批量更新标签
  batchUpdate: ({ data }) => {
    const { ids, updates } = data
    let updatedCount = 0
    
    ids.forEach(id => {
      const tag = tagsData.find(t => t.id.toString() === id.toString())
      if (tag) {
        Object.assign(tag, updates, { updatedAt: formatDate(new Date()) })
        updatedCount++
      }
    })
    
    return { updatedCount }
  },
  
  // 获取相关标签
  getRelated: ({ pathParams, params = {} }) => {
    const id = pathParams[0]
    const { limit = 10 } = params
    const currentTag = tagsData.find(t => t.id.toString() === id.toString())
    
    if (!currentTag) return []
    
    // 简单的相关性算法：相同类型、相似颜色、相近使用次数
    return tagsData
      .filter(t => t.id !== currentTag.id && t.status === '正常')
      .map(t => ({
        ...t,
        relevance: (
          (t.type === currentTag.type ? 3 : 0) +
          (t.color === currentTag.color ? 2 : 0) +
          (Math.abs(t.usageCount - currentTag.usageCount) < 50 ? 1 : 0)
        )
      }))
      .filter(t => t.relevance > 0)
      .sort((a, b) => b.relevance - a.relevance)
      .slice(0, limit)
  },
  
  // 切换标签状态
  toggleStatus: ({ pathParams }) => {
    const id = pathParams[0]
    const tag = tagsData.find(t => t.id.toString() === id.toString())
    
    if (!tag) return null
    
    tag.status = tag.status === '正常' ? '禁用' : '正常'
    tag.updatedAt = formatDate(new Date())
    
    return tag
  }
}
