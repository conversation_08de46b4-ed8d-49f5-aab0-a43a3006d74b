// Logs页面Mock数据
import { generateId, randomChoice, randomDate, formatDate, randomInt, randomChineseName, createCrudOperations } from '../utils'

// 日志级别
const logLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL']

// 日志来源
const logSources = ['用户服务', '订单服务', '支付服务', '商品服务', '库存服务', '通知服务', '网关服务', '认证服务']

// 日志类型
const logTypes = ['系统日志', '操作日志', '错误日志', '访问日志', '安全日志', '性能日志']

// 操作类型
const operationTypes = ['登录', '登出', '创建', '更新', '删除', '查询', '导出', '导入', '审核', '发布']

// 生成日志数据
function generateLog(id) {
  const createdAt = randomDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), new Date()) // 最近7天
  const level = randomChoice(logLevels)
  const source = randomChoice(logSources)
  const type = randomChoice(logTypes)
  
  const messages = {
    'DEBUG': ['调试信息：变量值检查', '方法调用跟踪', '数据库连接状态'],
    'INFO': ['用户登录成功', '数据同步完成', '服务启动成功', '任务执行完成'],
    'WARN': ['数据库连接缓慢', '内存使用率较高', '请求响应时间超时'],
    'ERROR': ['数据库连接失败', '文件读取错误', '网络请求超时', '参数验证失败'],
    'FATAL': ['系统崩溃', '数据库不可用', '内存溢出', '磁盘空间不足']
  }
  
  return {
    id: id || generateId(),
    timestamp: formatDate(createdAt, 'YYYY-MM-DD HH:mm:ss.SSS'),
    level,
    source,
    type,
    message: randomChoice(messages[level]),
    details: `详细的日志信息，包含具体的错误堆栈或操作详情...`,
    userId: Math.random() > 0.3 ? randomInt(1, 100) : null,
    userName: Math.random() > 0.3 ? randomChineseName() : null,
    ip: `192.168.${randomInt(1, 255)}.${randomInt(1, 255)}`,
    userAgent: randomChoice([
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ]),
    operation: randomChoice(operationTypes),
    resource: randomChoice(['用户管理', '订单管理', '商品管理', '系统设置', '报表查询']),
    requestId: generateId(),
    sessionId: generateId(),
    responseTime: randomInt(10, 2000), // 毫秒
    statusCode: level === 'ERROR' || level === 'FATAL' ? randomChoice([400, 401, 403, 404, 500, 502, 503]) : randomChoice([200, 201, 204]),
    method: randomChoice(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
    url: randomChoice([
      '/api/users',
      '/api/orders',
      '/api/products',
      '/api/auth/login',
      '/api/reports'
    ]),
    tags: randomChoice([
      ['系统', '重要'],
      ['用户操作'],
      ['错误', '需关注'],
      ['性能'],
      ['安全']
    ]),
    metadata: {
      serverName: randomChoice(['web-01', 'web-02', 'api-01', 'api-02']),
      processId: randomInt(1000, 9999),
      threadId: randomInt(100, 999),
      memoryUsage: randomInt(100, 2000) + 'MB',
      cpuUsage: randomInt(10, 90) + '%'
    },
    createdAt: formatDate(createdAt)
  }
}

// 生成日志列表数据
const logsData = Array.from({ length: 2500 }, (_, index) => generateLog(index + 1))

// 日志统计数据
const logStats = {
  total: logsData.length,
  today: logsData.filter(l => {
    const today = new Date()
    const logDate = new Date(l.createdAt)
    return logDate.toDateString() === today.toDateString()
  }).length,
  thisWeek: logsData.filter(l => {
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    return new Date(l.createdAt) > weekAgo
  }).length,
  byLevel: logLevels.reduce((acc, level) => {
    acc[level] = logsData.filter(l => l.level === level).length
    return acc
  }, {}),
  bySource: logSources.reduce((acc, source) => {
    acc[source] = logsData.filter(l => l.source === source).length
    return acc
  }, {}),
  byType: logTypes.reduce((acc, type) => {
    acc[type] = logsData.filter(l => l.type === type).length
    return acc
  }, {}),
  errorRate: parseFloat((logsData.filter(l => l.level === 'ERROR' || l.level === 'FATAL').length / logsData.length * 100).toFixed(2)),
  avgResponseTime: Math.round(logsData.reduce((sum, l) => sum + l.responseTime, 0) / logsData.length)
}

// 创建CRUD操作（日志通常只读）
const crudOps = createCrudOperations(logsData, {
  idField: 'id',
  searchFields: ['message', 'details', 'userName', 'source', 'operation'],
  defaultSort: 'timestamp'
})

// 导出Logs mock数据操作
export default {
  // 只保留查询相关的操作，日志通常不允许修改
  getList: crudOps.getList,
  getById: crudOps.getById,
  
  // 获取日志统计
  getStats: () => logStats,
  
  // 获取实时日志
  getRealtime: ({ params = {} }) => {
    const { limit = 50 } = params
    return logsData
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit)
  },
  
  // 获取错误日志
  getErrors: ({ params = {} }) => {
    const { limit = 100 } = params
    return logsData
      .filter(l => l.level === 'ERROR' || l.level === 'FATAL')
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit)
  },
  
  // 获取用户操作日志
  getUserLogs: ({ params = {} }) => {
    const { userId, limit = 100 } = params
    let result = logsData.filter(l => l.type === '操作日志')
    
    if (userId) {
      result = result.filter(l => l.userId && l.userId.toString() === userId.toString())
    }
    
    return result
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit)
  },
  
  // 获取系统性能日志
  getPerformanceLogs: ({ params = {} }) => {
    const { limit = 100 } = params
    return logsData
      .filter(l => l.type === '性能日志' || l.responseTime > 1000)
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit)
  },
  
  // 获取安全日志
  getSecurityLogs: ({ params = {} }) => {
    const { limit = 100 } = params
    return logsData
      .filter(l => l.type === '安全日志' || l.operation === '登录' || l.operation === '登出')
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, limit)
  },
  
  // 导出日志
  export: ({ params = {} }) => {
    const { startDate, endDate, level, source } = params
    let result = [...logsData]
    
    if (startDate) {
      result = result.filter(l => new Date(l.timestamp) >= new Date(startDate))
    }
    
    if (endDate) {
      result = result.filter(l => new Date(l.timestamp) <= new Date(endDate))
    }
    
    if (level) {
      result = result.filter(l => l.level === level)
    }
    
    if (source) {
      result = result.filter(l => l.source === source)
    }
    
    return {
      filename: `logs_${formatDate(new Date(), 'YYYY-MM-DD_HH-mm-ss')}.csv`,
      count: result.length,
      downloadUrl: '/api/logs/export/download',
      estimatedSize: Math.round(result.length * 0.5) + 'KB'
    }
  },
  
  // 获取日志趋势
  getTrends: ({ params = {} }) => {
    const { days = 7 } = params
    const trends = []
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
      const dayLogs = logsData.filter(l => {
        const logDate = new Date(l.timestamp)
        return logDate.toDateString() === date.toDateString()
      })
      
      trends.push({
        date: formatDate(date, 'YYYY-MM-DD'),
        total: dayLogs.length,
        error: dayLogs.filter(l => l.level === 'ERROR' || l.level === 'FATAL').length,
        warn: dayLogs.filter(l => l.level === 'WARN').length,
        info: dayLogs.filter(l => l.level === 'INFO').length,
        debug: dayLogs.filter(l => l.level === 'DEBUG').length
      })
    }
    
    return trends
  },
  
  // 清理旧日志
  cleanup: ({ data }) => {
    const { beforeDate } = data
    const beforeTimestamp = new Date(beforeDate)
    
    const originalLength = logsData.length
    const remainingLogs = logsData.filter(l => new Date(l.timestamp) >= beforeTimestamp)
    
    // 模拟清理操作
    const deletedCount = originalLength - remainingLogs.length
    
    return {
      deletedCount,
      remainingCount: remainingLogs.length,
      message: `成功清理了 ${deletedCount} 条日志记录`
    }
  }
}
