// Mock API拦截器
import { mockConfig } from './config'

// 导入所有mock数据
import authData from './data/auth'
import dashboardData from './data/dashboard'
import usersData from './data/users'
import servicesData from './data/services'
import categoriesData from './data/categories'
import tagsData from './data/tags'
import tasksData from './data/tasks'
import logsData from './data/logs'
import apiTokensData from './data/apiTokens'
import usageLogsData from './data/usageLogs'
import walletData from './data/wallet'
import settingsData from './data/settings'
import devToolsData from './data/devTools'
import promptsData from './data/prompts'
import contentData from './data/content'
import rssData from './data/rss'
import accountBindingsData from './data/accountBindings'

// Mock数据存储
const mockDataStore = {
  auth: authData,
  dashboard: dashboardData,
  users: usersData,
  services: servicesData,
  categories: categoriesData,
  tags: tagsData,
  tasks: tasksData,
  logs: logsData,
  apiTokens: apiTokensData,
  usageLogs: usageLogsData,
  wallet: walletData,
  settings: settingsData,
  devTools: devToolsData,
  prompts: promptsData,
  content: contentData,
  rss: rssData,
  accountBindings: accountBindingsData
}

// 模拟网络延迟
function simulateDelay() {
  const { min, max } = mockConfig.delay
  const delay = Math.random() * (max - min) + min
  return new Promise(resolve => setTimeout(resolve, delay))
}

// 模拟错误
function simulateError() {
  if (!mockConfig.errorSimulation.enabled) return false
  
  const shouldError = Math.random() < mockConfig.errorSimulation.rate
  if (shouldError) {
    const errorTypes = mockConfig.errorSimulation.types
    const errorType = errorTypes[Math.floor(Math.random() * errorTypes.length)]
    
    switch (errorType) {
      case 'network':
        throw new Error('Network Error')
      case 'server':
        throw new Error('Internal Server Error')
      case 'timeout':
        throw new Error('Request Timeout')
      default:
        throw new Error('Unknown Error')
    }
  }
  
  return false
}

// 解析URL参数
function parseUrlParams(url) {
  const urlObj = new URL(url, 'http://localhost')
  const params = {}
  
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value
  })
  
  return params
}

// 匹配API路径
function matchApiPath(method, url) {
  const cleanUrl = url.replace(/\?.*$/, '') // 移除查询参数
  const key = `${method} ${cleanUrl}`
  
  // 精确匹配
  if (mockConfig.apiMapping[key]) {
    return mockConfig.apiMapping[key]
  }
  
  // 参数匹配
  for (const [pattern, handler] of Object.entries(mockConfig.apiMapping)) {
    const [patternMethod, patternPath] = pattern.split(' ')
    if (patternMethod !== method) continue
    
    const regex = patternPath.replace(/:([^/]+)/g, '([^/]+)')
    const match = cleanUrl.match(new RegExp(`^${regex}$`))
    
    if (match) {
      return { handler, params: match.slice(1) }
    }
  }
  
  return null
}

// 执行Mock处理器
async function executeMockHandler(handlerInfo, url, options) {
  let handler, params = []
  
  if (typeof handlerInfo === 'string') {
    handler = handlerInfo
  } else {
    handler = handlerInfo.handler
    params = handlerInfo.params || []
  }
  
  const [module, method] = handler.split('.')
  const mockModule = mockDataStore[module]
  
  if (!mockModule || !mockModule[method]) {
    throw new Error(`Mock handler not found: ${handler}`)
  }
  
  // 解析请求参数
  const urlParams = parseUrlParams(url)
  let requestData = {}
  
  if (options.body) {
    try {
      requestData = JSON.parse(options.body)
    } catch (e) {
      requestData = options.body
    }
  }
  
  // 调用mock方法
  const result = await mockModule[method]({
    params: urlParams,
    data: requestData,
    pathParams: params
  })
  
  return result
}

// 创建Mock响应
function createMockResponse(data, status = 200) {
  return {
    ok: status >= 200 && status < 300,
    status,
    statusText: status === 200 ? 'OK' : 'Error',
    headers: new Headers({
      'Content-Type': 'application/json'
    }),
    json: async () => ({
      code: status,
      data,
      message: status === 200 ? 'success' : 'error',
      timestamp: Date.now()
    })
  }
}

// 设置Mock拦截器
export function setupMockInterceptor() {
  console.log('🎭 设置Mock拦截器...')
  console.log('Mock baseURL:', mockConfig.baseURL)

  // 保存原始fetch
  const originalFetch = window.fetch

  // 重写fetch
  window.fetch = async function(url, options = {}) {
    const method = (options.method || 'GET').toUpperCase()

    console.log(`🔍 拦截请求: ${method} ${url}`)

    // 检查是否需要mock
    if (!url.includes(mockConfig.baseURL)) {
      console.log(`⏭️ 跳过Mock: ${url} (不包含 ${mockConfig.baseURL})`)
      return originalFetch(url, options)
    }

    console.log(`🎭 使用Mock处理: ${method} ${url}`)
    
    try {
      // 模拟网络延迟
      await simulateDelay()
      
      // 模拟错误
      simulateError()
      
      // 匹配API路径
      const handlerInfo = matchApiPath(method, url)
      
      if (!handlerInfo) {
        console.warn(`No mock handler found for: ${method} ${url}`)
        return originalFetch(url, options)
      }
      
      // 执行mock处理器
      const result = await executeMockHandler(handlerInfo, url, options)
      
      // 记录日志
      if (mockConfig.logging.enabled) {
        console.log(`🎭 Mock API: ${method} ${url}`, result)
      }
      
      // 返回mock响应
      return createMockResponse(result)
      
    } catch (error) {
      console.error('Mock interceptor error:', error)
      
      // 返回错误响应
      return createMockResponse(
        { error: error.message },
        error.message.includes('Network') ? 0 : 500
      )
    }
  }
  
  console.log('🎭 Mock拦截器已设置')
}

export default {
  setupMockInterceptor,
  simulateDelay,
  simulateError
}
