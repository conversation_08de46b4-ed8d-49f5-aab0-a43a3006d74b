/**
 * 自动保存工具类
 */
export class AutoSave {
  constructor(options = {}) {
    this.options = {
      delay: 5000, // 延迟时间（毫秒）- 增加到5秒避免频繁保存
      maxRetries: 3, // 最大重试次数
      storageKey: 'autosave', // 本地存储键名
      enableLocalStorage: true, // 是否启用本地存储
      ...options
    }
    
    this.timers = new Map() // 存储定时器
    this.retryCount = new Map() // 重试计数
    this.callbacks = new Map() // 保存回调函数
    this.lastSaved = new Map() // 最后保存的数据
  }

  /**
   * 注册自动保存
   * @param {string} key - 唯一标识
   * @param {Function} saveCallback - 保存回调函数
   * @param {Object} options - 选项
   */
  register(key, saveCallback, options = {}) {
    const config = { ...this.options, ...options }
    
    this.callbacks.set(key, {
      save: saveCallback,
      config
    })
    
    // 从本地存储恢复数据
    if (config.enableLocalStorage) {
      const saved = this.getFromStorage(key)
      if (saved) {
        return saved
      }
    }
    
    return null
  }

  /**
   * 触发自动保存
   * @param {string} key - 唯一标识
   * @param {*} data - 要保存的数据
   */
  save(key, data) {
    const callback = this.callbacks.get(key)
    if (!callback) {
      console.warn(`AutoSave: 未找到键 "${key}" 的回调函数`)
      return
    }

    // 检查数据是否有变化
    const lastData = this.lastSaved.get(key)
    if (this.isDataEqual(data, lastData)) {
      return // 数据未变化，跳过保存
    }

    // 清除之前的定时器
    const existingTimer = this.timers.get(key)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 设置新的定时器
    const timer = setTimeout(() => {
      this.executeSave(key, data)
    }, callback.config.delay)

    this.timers.set(key, timer)

    // 保存到本地存储
    if (callback.config.enableLocalStorage) {
      this.saveToStorage(key, data)
    }
  }

  /**
   * 立即保存
   * @param {string} key - 唯一标识
   * @param {*} data - 要保存的数据
   */
  saveNow(key, data) {
    // 清除定时器
    const timer = this.timers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(key)
    }

    return this.executeSave(key, data)
  }

  /**
   * 执行保存操作
   * @param {string} key - 唯一标识
   * @param {*} data - 要保存的数据
   */
  async executeSave(key, data) {
    const callback = this.callbacks.get(key)
    if (!callback) return

    try {
      await callback.save(data)
      this.lastSaved.set(key, this.cloneData(data))
      this.retryCount.delete(key) // 重置重试计数
      this.clearStorage(key) // 清除本地存储的临时数据
      
      console.log(`AutoSave: 成功保存 "${key}"`)
      return { success: true }
    } catch (error) {
      console.error(`AutoSave: 保存失败 "${key}":`, error)
      
      // 重试机制
      const retries = this.retryCount.get(key) || 0
      if (retries < callback.config.maxRetries) {
        this.retryCount.set(key, retries + 1)
        
        // 延迟重试
        setTimeout(() => {
          this.executeSave(key, data)
        }, 1000 * (retries + 1)) // 递增延迟
        
        console.log(`AutoSave: 将在 ${retries + 1} 秒后重试保存 "${key}"`)
      }
      
      return { success: false, error }
    }
  }

  /**
   * 取消自动保存
   * @param {string} key - 唯一标识
   */
  cancel(key) {
    const timer = this.timers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.timers.delete(key)
    }
    
    this.retryCount.delete(key)
    console.log(`AutoSave: 已取消 "${key}" 的自动保存`)
  }

  /**
   * 注销自动保存
   * @param {string} key - 唯一标识
   */
  unregister(key) {
    this.cancel(key)
    this.callbacks.delete(key)
    this.lastSaved.delete(key)
    this.clearStorage(key)
  }

  /**
   * 获取保存状态
   * @param {string} key - 唯一标识
   */
  getStatus(key) {
    return {
      hasPendingSave: this.timers.has(key),
      retryCount: this.retryCount.get(key) || 0,
      hasLocalData: this.hasStorageData(key)
    }
  }

  /**
   * 保存到本地存储
   * @param {string} key - 唯一标识
   * @param {*} data - 数据
   */
  saveToStorage(key, data) {
    try {
      const storageKey = `${this.options.storageKey}_${key}`
      const saveData = {
        data,
        timestamp: Date.now(),
        version: '1.0'
      }
      localStorage.setItem(storageKey, JSON.stringify(saveData))
    } catch (error) {
      console.warn('AutoSave: 本地存储失败:', error)
    }
  }

  /**
   * 从本地存储获取数据
   * @param {string} key - 唯一标识
   */
  getFromStorage(key) {
    try {
      const storageKey = `${this.options.storageKey}_${key}`
      const saved = localStorage.getItem(storageKey)
      if (saved) {
        const parsed = JSON.parse(saved)
        return parsed.data
      }
    } catch (error) {
      console.warn('AutoSave: 读取本地存储失败:', error)
    }
    return null
  }

  /**
   * 检查是否有本地存储数据
   * @param {string} key - 唯一标识
   */
  hasStorageData(key) {
    const storageKey = `${this.options.storageKey}_${key}`
    return localStorage.getItem(storageKey) !== null
  }

  /**
   * 清除本地存储
   * @param {string} key - 唯一标识
   */
  clearStorage(key) {
    const storageKey = `${this.options.storageKey}_${key}`
    localStorage.removeItem(storageKey)
  }

  /**
   * 比较数据是否相等
   * @param {*} data1 - 数据1
   * @param {*} data2 - 数据2
   */
  isDataEqual(data1, data2) {
    // 如果其中一个为空，直接比较
    if (!data1 || !data2) {
      return data1 === data2
    }

    // 深度比较对象
    try {
      // 先比较类型
      if (typeof data1 !== typeof data2) {
        return false
      }

      // 对于对象，排序后比较JSON字符串
      if (typeof data1 === 'object') {
        const sortedData1 = this.sortObjectKeys(data1)
        const sortedData2 = this.sortObjectKeys(data2)
        return JSON.stringify(sortedData1) === JSON.stringify(sortedData2)
      }

      // 其他类型直接比较
      return data1 === data2
    } catch (error) {
      console.warn('AutoSave: 数据比较失败，使用简单比较:', error)
      return JSON.stringify(data1) === JSON.stringify(data2)
    }
  }

  /**
   * 递归排序对象键
   * @param {*} obj - 要排序的对象
   */
  sortObjectKeys(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sortObjectKeys(item))
    }

    const sorted = {}
    Object.keys(obj).sort().forEach(key => {
      sorted[key] = this.sortObjectKeys(obj[key])
    })

    return sorted
  }

  /**
   * 克隆数据
   * @param {*} data - 原始数据
   */
  cloneData(data) {
    return JSON.parse(JSON.stringify(data))
  }

  /**
   * 清理所有定时器
   */
  cleanup() {
    this.timers.forEach((timer) => {
      clearTimeout(timer)
    })
    this.timers.clear()
    this.retryCount.clear()
    this.callbacks.clear()
    this.lastSaved.clear()
  }
}

// 创建全局实例
export const autoSave = new AutoSave()

/**
 * Vue组合式函数：使用自动保存
 * @param {string} key - 唯一标识
 * @param {Function} saveCallback - 保存回调函数
 * @param {Object} options - 选项
 */
export function useAutoSave(key, saveCallback, options = {}) {
  const { onMounted, onUnmounted, ref, watch } = require('vue')
  
  const status = ref({
    hasPendingSave: false,
    retryCount: 0,
    hasLocalData: false
  })

  onMounted(() => {
    // 注册自动保存
    const restored = autoSave.register(key, saveCallback, options)
    
    // 更新状态
    status.value = autoSave.getStatus(key)
    
    return restored
  })

  onUnmounted(() => {
    // 注销自动保存
    autoSave.unregister(key)
  })

  const save = (data) => {
    autoSave.save(key, data)
    status.value = autoSave.getStatus(key)
  }

  const saveNow = (data) => {
    return autoSave.saveNow(key, data)
  }

  const cancel = () => {
    autoSave.cancel(key)
    status.value = autoSave.getStatus(key)
  }

  return {
    save,
    saveNow,
    cancel,
    status
  }
}

export default autoSave
