/* ===== 性能监控和优化工具 ===== */

/**
 * 性能监控类
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = new Map()
    this.isSupported = this.checkSupport()
  }

  /**
   * 检查浏览器支持
   */
  checkSupport() {
    return !!(
      window.performance &&
      window.performance.mark &&
      window.performance.measure &&
      window.PerformanceObserver
    )
  }

  /**
   * 开始性能测量
   */
  startMeasure(name) {
    if (!this.isSupported) return

    const markName = `${name}-start`
    performance.mark(markName)
    
    return {
      end: () => this.endMeasure(name)
    }
  }

  /**
   * 结束性能测量
   */
  endMeasure(name) {
    if (!this.isSupported) return

    const startMark = `${name}-start`
    const endMark = `${name}-end`
    const measureName = `${name}-duration`

    performance.mark(endMark)
    performance.measure(measureName, startMark, endMark)

    const measure = performance.getEntriesByName(measureName)[0]
    this.metrics.set(name, measure.duration)

    // 清理标记
    performance.clearMarks(startMark)
    performance.clearMarks(endMark)
    performance.clearMeasures(measureName)

    return measure.duration
  }

  /**
   * 监控 Core Web Vitals
   */
  observeCoreWebVitals() {
    if (!this.isSupported) return

    // LCP (Largest Contentful Paint)
    this.observeMetric('largest-contentful-paint', (entry) => {
      this.metrics.set('LCP', entry.startTime)
    })

    // FID (First Input Delay)
    this.observeMetric('first-input', (entry) => {
      this.metrics.set('FID', entry.processingStart - entry.startTime)
    })

    // CLS (Cumulative Layout Shift)
    this.observeMetric('layout-shift', (entry) => {
      if (!entry.hadRecentInput) {
        const currentCLS = this.metrics.get('CLS') || 0
        this.metrics.set('CLS', currentCLS + entry.value)
      }
    })
  }

  /**
   * 监控特定性能指标
   */
  observeMetric(type, callback) {
    if (!this.isSupported) return

    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(callback)
      })
      
      observer.observe({ type, buffered: true })
      this.observers.set(type, observer)
    } catch (error) {
      console.warn(`Failed to observe ${type}:`, error)
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics() {
    return Object.fromEntries(this.metrics)
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage() {
    if (!performance.memory) return null

    return {
      usedJSHeapSize: performance.memory.usedJSHeapSize,
      totalJSHeapSize: performance.memory.totalJSHeapSize,
      jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
    }
  }

  /**
   * 清理观察器
   */
  cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
    this.metrics.clear()
  }
}

/**
 * 动画性能优化工具
 */
export class AnimationOptimizer {
  constructor() {
    this.activeAnimations = new Set()
    this.rafId = null
  }

  /**
   * 优化的 requestAnimationFrame
   */
  requestFrame(callback) {
    return requestAnimationFrame((timestamp) => {
      callback(timestamp)
    })
  }

  /**
   * 批量 DOM 更新
   */
  batchDOMUpdates(updates) {
    return new Promise(resolve => {
      this.requestFrame(() => {
        updates.forEach(update => update())
        resolve()
      })
    })
  }

  /**
   * 防抖动画
   */
  debounceAnimation(fn, delay = 16) {
    let timeoutId
    return (...args) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => fn.apply(this, args), delay)
    }
  }

  /**
   * 节流动画
   */
  throttleAnimation(fn, delay = 16) {
    let lastCall = 0
    return (...args) => {
      const now = Date.now()
      if (now - lastCall >= delay) {
        lastCall = now
        return fn.apply(this, args)
      }
    }
  }
}

/**
 * 内存优化工具
 */
export class MemoryOptimizer {
  constructor() {
    this.cache = new Map()
    this.weakCache = new WeakMap()
  }

  /**
   * 带过期时间的缓存
   */
  setCache(key, value, ttl = 300000) { // 5分钟默认
    const expiry = Date.now() + ttl
    this.cache.set(key, { value, expiry })
  }

  /**
   * 获取缓存
   */
  getCache(key) {
    const item = this.cache.get(key)
    if (!item) return null

    if (Date.now() > item.expiry) {
      this.cache.delete(key)
      return null
    }

    return item.value
  }

  /**
   * 清理过期缓存
   */
  cleanupCache() {
    const now = Date.now()
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
      }
    }
  }

  /**
   * 弱引用缓存
   */
  setWeakCache(obj, key, value) {
    let objCache = this.weakCache.get(obj)
    if (!objCache) {
      objCache = new Map()
      this.weakCache.set(obj, objCache)
    }
    objCache.set(key, value)
  }

  /**
   * 获取弱引用缓存
   */
  getWeakCache(obj, key) {
    const objCache = this.weakCache.get(obj)
    return objCache ? objCache.get(key) : undefined
  }
}

/**
 * 全局性能监控实例
 */
export const performanceMonitor = new PerformanceMonitor()
export const animationOptimizer = new AnimationOptimizer()
export const memoryOptimizer = new MemoryOptimizer()

/**
 * 初始化性能监控
 */
export function initPerformanceMonitoring() {
  // 监控 Core Web Vitals
  performanceMonitor.observeCoreWebVitals()

  // 定期清理缓存
  setInterval(() => {
    memoryOptimizer.cleanupCache()
  }, 60000) // 每分钟清理一次

  // 页面卸载时清理
  window.addEventListener('beforeunload', () => {
    performanceMonitor.cleanup()
  })

  // Performance monitoring initialized
}

/**
 * 性能报告
 */
export function generatePerformanceReport() {
  const metrics = performanceMonitor.getMetrics()
  const memory = performanceMonitor.getMemoryUsage()

  return {
    timestamp: new Date().toISOString(),
    metrics,
    memory,
    recommendations: generateRecommendations(metrics)
  }
}

/**
 * 生成性能建议
 */
function generateRecommendations(metrics) {
  const recommendations = []

  if (metrics.LCP > 2500) {
    recommendations.push('LCP 超过 2.5s，建议优化图片加载和关键资源')
  }

  if (metrics.FID > 100) {
    recommendations.push('FID 超过 100ms，建议优化 JavaScript 执行时间')
  }

  if (metrics.CLS > 0.1) {
    recommendations.push('CLS 超过 0.1，建议优化布局稳定性')
  }

  return recommendations
}
