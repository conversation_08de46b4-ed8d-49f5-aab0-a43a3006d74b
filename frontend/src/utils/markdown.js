import { marked } from 'marked'
import hljs from 'highlight.js'

/**
 * Markdown解析和渲染工具
 */
class MarkdownRenderer {
  constructor() {
    this.setupMarked()
    this.loadThemeStyles()
  }

  /**
   * 根据当前主题加载样式
   */
  loadThemeStyles() {
    // 动态加载主题样式，避免冲突
    const isDarkMode = document.documentElement.classList.contains('dark')
    
    // 移除已存在的高亮样式
    const existingStyle = document.querySelector('#highlight-theme')
    if (existingStyle) {
      existingStyle.remove()
    }

    // 动态创建样式链接
    const link = document.createElement('link')
    link.id = 'highlight-theme'
    link.rel = 'stylesheet'
    link.href = isDarkMode 
      ? 'https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github-dark.css'
      : 'https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.css'
    
    document.head.appendChild(link)
  }

  /**
   * 配置marked解析器
   */
  setupMarked() {
    // 使用简单的配置，避免复杂的renderer问题
    marked.setOptions({
      breaks: true,
      gfm: true,
      sanitize: false,
      highlight: (code, lang) => {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(code, { language: lang }).value
          } catch (err) {
            console.warn('代码高亮失败:', err)
          }
        }
        return hljs.highlightAuto(code).value
      },
      langPrefix: 'hljs language-'
    })
  }

  /**
   * 渲染Markdown为HTML
   * @param {string} markdown - Markdown文本
   * @returns {string} HTML字符串
   */
  render(markdown) {
    if (!markdown || typeof markdown !== 'string') {
      return ''
    }

    try {
      // 直接使用marked渲染
      const html = marked(markdown)
      return html
    } catch (error) {
      console.error('Markdown渲染失败:', error)
      return `<div class="markdown-error">渲染失败: ${error.message}</div>`
    }
  }

  /**
   * 更新主题样式
   */
  updateTheme() {
    this.loadThemeStyles()
  }

  /**
   * 从标题文本生成ID
   * @param {string} text - 标题文本
   * @returns {string} ID字符串
   */
  generateId(text) {
    return text.replace(/<[^>]*>/g, '')
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fa5]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }

  /**
   * 提取目录结构
   * @param {string} markdown - Markdown文本
   * @returns {Array} 目录数组
   */
  extractToc(markdown) {
    if (!markdown || typeof markdown !== 'string') return []

    const headings = []
    const lines = String(markdown).split('\n')

    lines.forEach((line, index) => {
      const match = String(line).match(/^(#{1,6})\s+(.+)$/)
      if (match) {
        const level = match[1].length
        const text = String(match[2] || '').trim()
        const id = text.toLowerCase().replace(/[^\w]+/g, '-')
        
        headings.push({
          level,
          text,
          id,
          line: index + 1
        })
      }
    })

    return headings
  }

  /**
   * 获取Markdown统计信息
   * @param {string} markdown - Markdown文本
   * @returns {Object} 统计信息
   */
  getStats(markdown) {
    if (!markdown || typeof markdown !== 'string') {
      return {
        characters: 0,
        words: 0,
        lines: 0,
        paragraphs: 0,
        headings: 0,
        codeBlocks: 0,
        links: 0,
        images: 0
      }
    }

    const markdownStr = String(markdown)
    const lines = markdownStr.split('\n')
    const words = markdownStr.match(/\b\w+\b/g) || []
    const headings = markdownStr.match(/^#{1,6}\s+.+$/gm) || []
    const codeBlocks = markdownStr.match(/```[\s\S]*?```/g) || []
    const links = markdownStr.match(/\[([^\]]+)\]\(([^)]+)\)/g) || []
    const images = markdownStr.match(/!\[([^\]]*)\]\(([^)]+)\)/g) || []
    const paragraphs = markdownStr.split(/\n\s*\n/).filter(p => p.trim()).length

    return {
      characters: markdownStr.length,
      words: words.length,
      lines: lines.length,
      paragraphs,
      headings: headings.length,
      codeBlocks: codeBlocks.length,
      links: links.length,
      images: images.length
    }
  }
}

// 创建全局实例
export const markdownRenderer = new MarkdownRenderer()

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay) {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 插入Markdown语法
 * @param {HTMLTextAreaElement} textarea - 文本区域元素
 * @param {string} syntax - 要插入的语法
 */
export function insertMarkdownSyntax(textarea, syntax) {
  if (!textarea) return

  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const selectedText = textarea.value.substring(start, end)
  
  let newText = ''
  let newCursorPos = start

  switch (syntax) {
    case 'bold':
      newText = `**${selectedText || '粗体文本'}**`
      newCursorPos = selectedText ? end + 4 : start + 2
      break
    case 'italic':
      newText = `*${selectedText || '斜体文本'}*`
      newCursorPos = selectedText ? end + 2 : start + 1
      break
    case 'code':
      newText = `\`${selectedText || '代码'}\``
      newCursorPos = selectedText ? end + 2 : start + 1
      break
    case 'link':
      newText = `[${selectedText || '链接文本'}](url)`
      newCursorPos = selectedText ? start + selectedText.length + 3 : start + 4
      break
    case 'image':
      newText = `![${selectedText || '图片描述'}](url)`
      newCursorPos = selectedText ? start + selectedText.length + 4 : start + 5
      break
    case 'heading1':
      newText = `# ${selectedText || '一级标题'}`
      newCursorPos = selectedText ? end + 2 : start + 2
      break
    case 'heading2':
      newText = `## ${selectedText || '二级标题'}`
      newCursorPos = selectedText ? end + 3 : start + 3
      break
    case 'heading3':
      newText = `### ${selectedText || '三级标题'}`
      newCursorPos = selectedText ? end + 4 : start + 4
      break
    case 'quote':
      newText = `> ${selectedText || '引用文本'}`
      newCursorPos = selectedText ? end + 2 : start + 2
      break
    case 'list':
      newText = `- ${selectedText || '列表项'}`
      newCursorPos = selectedText ? end + 2 : start + 2
      break
    case 'orderedList':
      newText = `1. ${selectedText || '有序列表项'}`
      newCursorPos = selectedText ? end + 3 : start + 3
      break
    case 'codeBlock':
      newText = `\`\`\`\n${selectedText || '代码块'}\n\`\`\``
      newCursorPos = selectedText ? end + 7 : start + 4
      break
    default:
      return
  }

  // 替换选中文本
  textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end)
  
  // 设置光标位置
  textarea.focus()
  textarea.setSelectionRange(newCursorPos, newCursorPos)
  
  // 触发input事件
  textarea.dispatchEvent(new Event('input', { bubbles: true }))
}

export default markdownRenderer
