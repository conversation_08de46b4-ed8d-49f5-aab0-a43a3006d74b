// API基础配置
const API_BASE_URL = import.meta.env.VITE_USE_MOCK === 'true'
  ? '/api'  // Mock模式使用相对路径
  : (import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api')

// 获取token
const getToken = () => {
  return localStorage.getItem('token') || ''
}

// 通用请求函数
const request = async (url, options = {}) => {
  const token = getToken()
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  }

  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  }

  try {
    const response = await fetch(`${API_BASE_URL}${url}`, finalOptions)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    return result
  } catch (error) {
    console.error('API请求错误:', error)
    throw error
  }
}

// GET请求
export const get = (url, params = {}) => {
  const queryString = new URLSearchParams(params).toString()
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  return request(fullUrl, {
    method: 'GET'
  })
}

// POST请求
export const post = (url, data = {}) => {
  return request(url, {
    method: 'POST',
    body: JSON.stringify(data)
  })
}

// PUT请求
export const put = (url, data = {}) => {
  return request(url, {
    method: 'PUT',
    body: JSON.stringify(data)
  })
}

// DELETE请求
export const del = (url) => {
  return request(url, {
    method: 'DELETE'
  })
}

// 认证相关API
export const authApi = {
  // 用户登录
  login: (credentials) => post('/auth/login', credentials),

  // 刷新令牌
  refreshToken: (refreshToken) => post('/auth/refresh', { refreshToken }),

  // 获取当前用户信息
  getCurrentUser: () => get('/auth/me'),

  // 绑定OAuth2账号
  bindOAuth2Account: (provider) => post(`/auth/bind/${provider}`),

  // 解绑OAuth2账号
  unbindOAuth2Account: (provider) => del(`/auth/unbind/${provider}`),

  // 设置主要登录方式
  setPrimaryProvider: (provider) => post(`/auth/primary/${provider}`)
}

// 用户相关API
export const userApi = {
  // 获取用户列表
  getUserList: () => get('/user/list'),

  // 根据ID获取用户
  getUserById: (id) => get(`/user/${id}`),

  // 创建用户
  createUser: (userData) => post('/user/create', userData),

  // 更新用户
  updateUser: (userData) => put('/user/update', userData),

  // 删除用户
  deleteUser: (id) => del(`/user/${id}`),

  // 获取用户数量
  getUserCount: () => get('/user/count')
}

// 系统相关API
export const systemApi = {
  // 健康检查
  health: () => get('/health'),
  
  // 欢迎信息
  welcome: () => get('/welcome')
}

// 错误处理工具
export const handleApiError = (error) => {
  if (error.response) {
    // 服务器返回错误状态码
    const { status, data } = error.response
    switch (status) {
      case 401:
        return '未授权，请重新登录'
      case 403:
        return '权限不足'
      case 404:
        return '请求的资源不存在'
      case 500:
        return '服务器内部错误'
      default:
        return data?.message || '请求失败'
    }
  } else if (error.request) {
    // 网络错误
    return '网络连接失败，请检查网络设置'
  } else {
    // 其他错误
    return error.message || '未知错误'
  }
}

// 响应拦截器 - 统一处理响应
export const handleApiResponse = (response) => {
  if (response.code === 200) {
    return {
      success: true,
      data: response.data,
      message: response.message
    }
  } else {
    return {
      success: false,
      message: response.message || '操作失败'
    }
  }
}

export default {
  get,
  post,
  put,
  del,
  authApi,
  userApi,
  systemApi,
  handleApiError,
  handleApiResponse
}
