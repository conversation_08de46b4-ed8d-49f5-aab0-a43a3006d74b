/* ===== 无障碍访问优化工具 ===== */

/**
 * 无障碍访问管理器
 */
export class AccessibilityManager {
  constructor() {
    this.focusableElements = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])'
    ].join(',')
    
    this.trapStack = []
    this.announcements = []
  }

  /**
   * 设置焦点陷阱
   */
  trapFocus(container) {
    const focusable = container.querySelectorAll(this.focusableElements)
    if (focusable.length === 0) return

    const firstFocusable = focusable[0]
    const lastFocusable = focusable[focusable.length - 1]

    const handleKeyDown = (e) => {
      if (e.key !== 'Tab') return

      if (e.shiftKey) {
        if (document.activeElement === firstFocusable) {
          e.preventDefault()
          lastFocusable.focus()
        }
      } else {
        if (document.activeElement === lastFocusable) {
          e.preventDefault()
          firstFocusable.focus()
        }
      }
    }

    container.addEventListener('keydown', handleKeyDown)
    firstFocusable.focus()

    const trap = {
      container,
      handleKeyDown,
      release: () => {
        container.removeEventListener('keydown', handleKeyDown)
        this.trapStack = this.trapStack.filter(t => t !== trap)
      }
    }

    this.trapStack.push(trap)
    return trap
  }

  /**
   * 释放焦点陷阱
   */
  releaseFocusTrap() {
    const trap = this.trapStack.pop()
    if (trap) {
      trap.release()
    }
  }

  /**
   * 管理 ARIA 实时区域
   */
  announce(message, priority = 'polite') {
    const announcement = {
      id: Date.now(),
      message,
      priority,
      timestamp: new Date()
    }

    this.announcements.push(announcement)

    // 创建或更新 ARIA live region
    let liveRegion = document.getElementById(`aria-live-${priority}`)
    if (!liveRegion) {
      liveRegion = document.createElement('div')
      liveRegion.id = `aria-live-${priority}`
      liveRegion.setAttribute('aria-live', priority)
      liveRegion.setAttribute('aria-atomic', 'true')
      liveRegion.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `
      document.body.appendChild(liveRegion)
    }

    liveRegion.textContent = message

    // 清理旧的公告
    setTimeout(() => {
      this.announcements = this.announcements.filter(a => a.id !== announcement.id)
    }, 5000)
  }

  /**
   * 键盘导航支持
   */
  enableKeyboardNavigation(container) {
    const handleKeyDown = (e) => {
      switch (e.key) {
        case 'Escape':
          this.handleEscape(e)
          break
        case 'Enter':
        case ' ':
          this.handleActivation(e)
          break
        case 'ArrowUp':
        case 'ArrowDown':
        case 'ArrowLeft':
        case 'ArrowRight':
          this.handleArrowNavigation(e)
          break
        case 'Home':
        case 'End':
          this.handleHomeEnd(e)
          break
      }
    }

    container.addEventListener('keydown', handleKeyDown)
    return () => container.removeEventListener('keydown', handleKeyDown)
  }

  /**
   * 处理 Escape 键
   */
  handleEscape(e) {
    // 关闭模态框、下拉菜单等
    const modal = e.target.closest('[role="dialog"]')
    if (modal) {
      const closeButton = modal.querySelector('[data-close]')
      if (closeButton) {
        closeButton.click()
      }
    }
  }

  /**
   * 处理激活键（Enter/Space）
   */
  handleActivation(e) {
    const target = e.target
    if (target.matches('button, [role="button"]') && !target.disabled) {
      e.preventDefault()
      target.click()
    }
  }

  /**
   * 处理箭头键导航
   */
  handleArrowNavigation(e) {
    const target = e.target
    const container = target.closest('[role="menu"], [role="menubar"], [role="tablist"]')
    if (!container) return

    const items = Array.from(container.querySelectorAll('[role="menuitem"], [role="tab"]'))
    const currentIndex = items.indexOf(target)
    
    if (currentIndex === -1) return

    let nextIndex
    switch (e.key) {
      case 'ArrowUp':
      case 'ArrowLeft':
        nextIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1
        break
      case 'ArrowDown':
      case 'ArrowRight':
        nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0
        break
    }

    if (nextIndex !== undefined) {
      e.preventDefault()
      items[nextIndex].focus()
    }
  }

  /**
   * 处理 Home/End 键
   */
  handleHomeEnd(e) {
    const target = e.target
    const container = target.closest('[role="menu"], [role="menubar"], [role="tablist"]')
    if (!container) return

    const items = Array.from(container.querySelectorAll('[role="menuitem"], [role="tab"]'))
    
    if (items.length === 0) return

    e.preventDefault()
    if (e.key === 'Home') {
      items[0].focus()
    } else {
      items[items.length - 1].focus()
    }
  }

  /**
   * 检查颜色对比度
   */
  checkColorContrast(foreground, background) {
    const getLuminance = (color) => {
      const rgb = this.hexToRgb(color)
      const [r, g, b] = rgb.map(c => {
        c = c / 255
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
      })
      return 0.2126 * r + 0.7152 * g + 0.0722 * b
    }

    const l1 = getLuminance(foreground)
    const l2 = getLuminance(background)
    const ratio = (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05)

    return {
      ratio,
      AA: ratio >= 4.5,
      AAA: ratio >= 7,
      level: ratio >= 7 ? 'AAA' : ratio >= 4.5 ? 'AA' : 'Fail'
    }
  }

  /**
   * 十六进制颜色转 RGB
   */
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? [
      parseInt(result[1], 16),
      parseInt(result[2], 16),
      parseInt(result[3], 16)
    ] : null
  }

  /**
   * 设置 ARIA 属性
   */
  setAriaAttributes(element, attributes) {
    Object.entries(attributes).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        element.setAttribute(`aria-${key}`, value)
      } else {
        element.removeAttribute(`aria-${key}`)
      }
    })
  }

  /**
   * 生成唯一 ID
   */
  generateId(prefix = 'a11y') {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}

/**
 * 减少动画偏好检测
 */
export function prefersReducedMotion() {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

/**
 * 高对比度模式检测
 */
export function prefersHighContrast() {
  return window.matchMedia('(prefers-contrast: high)').matches
}

/**
 * 暗色主题偏好检测
 */
export function prefersDarkTheme() {
  return window.matchMedia('(prefers-color-scheme: dark)').matches
}

/**
 * 屏幕阅读器检测
 */
export function isScreenReaderActive() {
  // 检测常见的屏幕阅读器
  const userAgent = navigator.userAgent.toLowerCase()
  const screenReaders = ['nvda', 'jaws', 'voiceover', 'narrator', 'orca']
  
  return screenReaders.some(sr => userAgent.includes(sr)) ||
         // 检测是否有辅助技术
         navigator.userAgent.includes('aural') ||
         // 检测 Windows 高对比度模式
         window.matchMedia('(-ms-high-contrast: active)').matches
}

/**
 * 全局无障碍管理器实例
 */
export const accessibilityManager = new AccessibilityManager()

/**
 * 初始化无障碍功能
 */
export function initAccessibility() {
  // 监听偏好设置变化
  const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
  const highContrastQuery = window.matchMedia('(prefers-contrast: high)')
  
  reducedMotionQuery.addEventListener('change', (e) => {
    document.documentElement.classList.toggle('reduce-motion', e.matches)
    accessibilityManager.announce(
      e.matches ? '已启用减少动画模式' : '已禁用减少动画模式'
    )
  })

  highContrastQuery.addEventListener('change', (e) => {
    document.documentElement.classList.toggle('high-contrast', e.matches)
    accessibilityManager.announce(
      e.matches ? '已启用高对比度模式' : '已禁用高对比度模式'
    )
  })

  // 初始设置
  document.documentElement.classList.toggle('reduce-motion', reducedMotionQuery.matches)
  document.documentElement.classList.toggle('high-contrast', highContrastQuery.matches)

  console.log('Accessibility features initialized')
}

/**
 * 无障碍审计
 */
export function auditAccessibility(container = document) {
  const issues = []

  // 检查缺失的 alt 属性
  const images = container.querySelectorAll('img:not([alt])')
  if (images.length > 0) {
    issues.push(`Found ${images.length} images without alt attributes`)
  }

  // 检查缺失的标签
  const inputs = container.querySelectorAll('input:not([aria-label]):not([aria-labelledby])')
  inputs.forEach(input => {
    const label = container.querySelector(`label[for="${input.id}"]`)
    if (!label && !input.closest('label')) {
      issues.push(`Input element missing label: ${input.outerHTML.slice(0, 50)}...`)
    }
  })

  // 检查按钮文本
  const buttons = container.querySelectorAll('button:not([aria-label]):not([aria-labelledby])')
  buttons.forEach(button => {
    if (!button.textContent.trim()) {
      issues.push(`Button element missing text content: ${button.outerHTML.slice(0, 50)}...`)
    }
  })

  return {
    issues,
    score: Math.max(0, 100 - issues.length * 10),
    level: issues.length === 0 ? 'AAA' : issues.length <= 2 ? 'AA' : 'A'
  }
}
