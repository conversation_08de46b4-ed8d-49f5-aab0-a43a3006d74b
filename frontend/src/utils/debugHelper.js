/**
 * 调试助手工具
 * 用于帮助诊断页面刷新和数据更新问题
 */

export class DebugHelper {
  constructor() {
    this.logs = []
    this.watchers = new Map()
    this.isEnabled = process.env.NODE_ENV === 'development'
  }

  /**
   * 记录调试信息
   * @param {string} component - 组件名称
   * @param {string} event - 事件类型
   * @param {*} data - 相关数据
   */
  log(component, event, data = null) {
    if (!this.isEnabled) return

    const logEntry = {
      timestamp: new Date().toISOString(),
      component,
      event,
      data: data ? JSON.stringify(data) : null
    }

    this.logs.push(logEntry)
    console.log(`[DEBUG] ${component}: ${event}`, data)

    // 保持最近100条日志
    if (this.logs.length > 100) {
      this.logs.shift()
    }
  }

  /**
   * 监听数据变化
   * @param {string} key - 监听键
   * @param {*} data - 数据
   * @param {string} source - 数据源
   */
  watchData(key, data, source = 'unknown') {
    if (!this.isEnabled) return

    const dataHash = this.hashData(data)
    const lastHash = this.watchers.get(key)

    if (lastHash !== dataHash) {
      this.log('DataWatcher', `${key} changed from ${source}`, {
        key,
        source,
        oldHash: lastHash,
        newHash: dataHash,
        data
      })
      this.watchers.set(key, dataHash)
    }
  }

  /**
   * 生成数据哈希
   * @param {*} data - 数据
   */
  hashData(data) {
    try {
      return btoa(JSON.stringify(data)).slice(0, 8)
    } catch (error) {
      return 'hash-error'
    }
  }

  /**
   * 获取调试日志
   * @param {string} component - 组件名称过滤
   * @param {number} limit - 限制条数
   */
  getLogs(component = null, limit = 50) {
    let filteredLogs = this.logs

    if (component) {
      filteredLogs = this.logs.filter(log => log.component === component)
    }

    return filteredLogs.slice(-limit)
  }

  /**
   * 清除日志
   */
  clearLogs() {
    this.logs = []
    this.watchers.clear()
  }

  /**
   * 导出日志
   */
  exportLogs() {
    const data = {
      logs: this.logs,
      watchers: Object.fromEntries(this.watchers),
      timestamp: new Date().toISOString()
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    })

    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `debug-logs-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }
}

// 全局实例
export const debugHelper = new DebugHelper()

/**
 * Vue组合式函数：使用调试助手
 * @param {string} componentName - 组件名称
 */
export function useDebugHelper(componentName) {
  const log = (event, data) => {
    debugHelper.log(componentName, event, data)
  }

  const watchData = (key, data, source) => {
    debugHelper.watchData(`${componentName}.${key}`, data, source)
  }

  return {
    log,
    watchData,
    debugHelper
  }
}
