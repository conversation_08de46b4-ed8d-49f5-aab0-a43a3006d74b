/**
 * 详情页面模板配置
 */

// 模板类型定义
export const TEMPLATE_MODES = {
  MODAL: 'modal',      // 弹窗模式
  PAGE: 'page'         // 独立页面模式
}

// 字段类型定义
export const FIELD_TYPES = {
  TEXT: 'text',
  TEXTAREA: 'textarea',
  MARKDOWN: 'markdown',
  SELECT: 'select',
  MULTISELECT: 'multiselect',
  DATE: 'date',
  DATETIME: 'datetime',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  EMAIL: 'email',
  URL: 'url',
  JSON: 'json',
  TAGS: 'tags'
}

// 验证规则定义
export const VALIDATION_RULES = {
  required: { type: 'required', message: '此字段为必填项' },
  email: { type: 'email', message: '请输入有效的邮箱地址' },
  url: { type: 'url', message: '请输入有效的URL地址' },
  minLength: (min) => ({ type: 'minLength', min, message: `最少需要${min}个字符` }),
  maxLength: (max) => ({ type: 'maxLength', max, message: `最多允许${max}个字符` }),
  pattern: (pattern, message) => ({ type: 'pattern', pattern, message })
}

// 详情模板配置
export const DETAIL_TEMPLATES = {
  // 服务管理
  services: {
    mode: TEMPLATE_MODES.PAGE,
    title: '服务详情',
    icon: '⚙️',
    hasMarkdown: true,
    markdownField: 'description',
    tabs: [
      { key: 'basic', label: '基本信息', icon: '📋' },
      { key: 'content', label: '服务文档', icon: '📝' },
      { key: 'config', label: '配置参数', icon: '⚙️' },
      { key: 'history', label: '版本历史', icon: '📚' }
    ],
    fields: {
      basic: [
        {
          key: 'name',
          label: '服务名称',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(100)]
        },
        {
          key: 'category',
          label: '服务分类',
          type: FIELD_TYPES.SELECT,
          required: true,
          options: [
            { value: 'api', label: 'API服务' },
            { value: 'tool', label: '工具服务' },
            { value: 'integration', label: '集成服务' }
          ],
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'version',
          label: '版本号',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'status',
          label: '状态',
          type: FIELD_TYPES.SELECT,
          required: true,
          options: [
            { value: 'active', label: '已发布' },
            { value: 'draft', label: '草稿' },
            { value: 'deprecated', label: '已废弃' }
          ],
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'tags',
          label: '标签',
          type: FIELD_TYPES.TAGS,
          required: false
        }
      ],
      config: [
        {
          key: 'endpoint',
          label: 'API端点',
          type: FIELD_TYPES.URL,
          required: false,
          validation: [VALIDATION_RULES.url]
        },
        {
          key: 'timeout',
          label: '超时时间(秒)',
          type: FIELD_TYPES.NUMBER,
          required: false,
          min: 1,
          max: 300
        },
        {
          key: 'retries',
          label: '重试次数',
          type: FIELD_TYPES.NUMBER,
          required: false,
          min: 0,
          max: 10
        },
        {
          key: 'config',
          label: '配置参数',
          type: FIELD_TYPES.JSON,
          required: false
        }
      ]
    }
  },

  // 分类管理
  categories: {
    mode: TEMPLATE_MODES.MODAL,
    title: '分类详情',
    icon: '📁',
    hasMarkdown: false,
    size: 'md',
    fields: {
      basic: [
        {
          key: 'name',
          label: '分类名称',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(50)]
        },
        {
          key: 'parent',
          label: '父分类',
          type: FIELD_TYPES.SELECT,
          required: false,
          options: [] // 动态加载
        },
        {
          key: 'description',
          label: '描述',
          type: FIELD_TYPES.TEXTAREA,
          required: false,
          validation: [VALIDATION_RULES.maxLength(500)]
        },
        {
          key: 'sort',
          label: '排序',
          type: FIELD_TYPES.NUMBER,
          required: false,
          min: 0
        },
        {
          key: 'enabled',
          label: '启用状态',
          type: FIELD_TYPES.BOOLEAN,
          required: false,
          default: true
        }
      ]
    }
  },

  // 标签管理
  tags: {
    mode: TEMPLATE_MODES.MODAL,
    title: '标签详情',
    icon: '🏷️',
    hasMarkdown: false,
    size: 'md',
    fields: {
      basic: [
        {
          key: 'name',
          label: '标签名称',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(30)]
        },
        {
          key: 'color',
          label: '标签颜色',
          type: FIELD_TYPES.SELECT,
          required: false,
          options: [
            { value: 'blue', label: '蓝色' },
            { value: 'green', label: '绿色' },
            { value: 'red', label: '红色' },
            { value: 'yellow', label: '黄色' },
            { value: 'purple', label: '紫色' },
            { value: 'gray', label: '灰色' }
          ]
        },
        {
          key: 'description',
          label: '描述',
          type: FIELD_TYPES.TEXTAREA,
          required: false,
          validation: [VALIDATION_RULES.maxLength(200)]
        }
      ]
    }
  },

  // 任务管理
  tasks: {
    mode: TEMPLATE_MODES.MODAL,
    title: '任务详情',
    icon: '⏰',
    hasMarkdown: true,
    markdownField: 'description',
    size: 'lg',
    tabs: [
      { key: 'basic', label: '基本信息', icon: '📋' },
      { key: 'content', label: '任务描述', icon: '📝' },
      { key: 'schedule', label: '调度配置', icon: '⏰' }
    ],
    fields: {
      basic: [
        {
          key: 'name',
          label: '任务名称',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(100)]
        },
        {
          key: 'group',
          label: '任务组',
          type: FIELD_TYPES.SELECT,
          required: true,
          options: [
            { value: 'sync', label: '数据同步' },
            { value: 'report', label: '报表生成' },
            { value: 'cleanup', label: '清理任务' },
            { value: 'backup', label: '备份任务' }
          ],
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'priority',
          label: '优先级',
          type: FIELD_TYPES.NUMBER,
          required: true,
          min: 1,
          max: 10,
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'status',
          label: '状态',
          type: FIELD_TYPES.SELECT,
          required: true,
          options: [
            { value: 'enabled', label: '启用' },
            { value: 'disabled', label: '停用' }
          ],
          validation: [VALIDATION_RULES.required]
        }
      ],
      schedule: [
        {
          key: 'cron',
          label: 'Cron表达式',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'timeout',
          label: '超时时间(分钟)',
          type: FIELD_TYPES.NUMBER,
          required: false,
          min: 1,
          max: 1440
        },
        {
          key: 'retries',
          label: '重试次数',
          type: FIELD_TYPES.NUMBER,
          required: false,
          min: 0,
          max: 5
        }
      ]
    }
  },

  // AI提示词
  prompts: {
    mode: TEMPLATE_MODES.PAGE,
    title: '提示词详情',
    icon: '🤖',
    hasMarkdown: true,
    markdownField: 'content',
    tabs: [
      { key: 'basic', label: '基本信息', icon: '📋' },
      { key: 'content', label: '提示词内容', icon: '📝' },
      { key: 'examples', label: '使用示例', icon: '💡' },
      { key: 'history', label: '版本历史', icon: '📚' }
    ],
    fields: {
      basic: [
        {
          key: 'title',
          label: '提示词标题',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(100)]
        },
        {
          key: 'category',
          label: '分类',
          type: FIELD_TYPES.SELECT,
          required: true,
          options: [
            { value: 'code', label: '代码生成' },
            { value: 'writing', label: '文档写作' },
            { value: 'analysis', label: '数据分析' },
            { value: 'design', label: '创意设计' }
          ],
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'tags',
          label: '标签',
          type: FIELD_TYPES.TAGS,
          required: false
        },
        {
          key: 'difficulty',
          label: '难度等级',
          type: FIELD_TYPES.SELECT,
          required: false,
          options: [
            { value: 'beginner', label: '初级' },
            { value: 'intermediate', label: '中级' },
            { value: 'advanced', label: '高级' }
          ]
        }
      ],
      examples: [
        {
          key: 'examples',
          label: '使用示例',
          type: FIELD_TYPES.MARKDOWN,
          required: false
        }
      ]
    }
  },

  // 内容管理
  content: {
    mode: TEMPLATE_MODES.PAGE,
    title: '内容详情',
    icon: '📄',
    hasMarkdown: true,
    markdownField: 'content',
    tabs: [
      { key: 'basic', label: '基本信息', icon: '📋' },
      { key: 'content', label: '内容编辑', icon: '📝' },
      { key: 'meta', label: '元数据', icon: '🏷️' },
      { key: 'history', label: '版本历史', icon: '📚' }
    ],
    fields: {
      basic: [
        {
          key: 'title',
          label: '标题',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required, VALIDATION_RULES.maxLength(200)]
        },
        {
          key: 'type',
          label: '内容类型',
          type: FIELD_TYPES.SELECT,
          required: true,
          options: [
            { value: 'document', label: '规范文档' },
            { value: 'manual', label: '操作手册' },
            { value: 'guide', label: '技术文档' },
            { value: 'training', label: '培训材料' }
          ],
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'status',
          label: '状态',
          type: FIELD_TYPES.SELECT,
          required: true,
          options: [
            { value: 'published', label: '已发布' },
            { value: 'draft', label: '草稿' },
            { value: 'review', label: '待审核' },
            { value: 'archived', label: '已归档' }
          ],
          validation: [VALIDATION_RULES.required]
        },
        {
          key: 'author',
          label: '作者',
          type: FIELD_TYPES.TEXT,
          required: true,
          validation: [VALIDATION_RULES.required]
        }
      ],
      meta: [
        {
          key: 'keywords',
          label: '关键词',
          type: FIELD_TYPES.TAGS,
          required: false
        },
        {
          key: 'summary',
          label: '摘要',
          type: FIELD_TYPES.TEXTAREA,
          required: false,
          validation: [VALIDATION_RULES.maxLength(500)]
        },
        {
          key: 'publishDate',
          label: '发布日期',
          type: FIELD_TYPES.DATE,
          required: false
        }
      ]
    }
  }
}

// 获取模板配置
export function getTemplateConfig(moduleName) {
  return DETAIL_TEMPLATES[moduleName] || null
}

// 获取字段配置
export function getFieldConfig(moduleName, tabKey = 'basic') {
  const template = getTemplateConfig(moduleName)
  return template?.fields?.[tabKey] || []
}

// 获取验证规则
export function getValidationRules(moduleName, tabKey = 'basic') {
  const fields = getFieldConfig(moduleName, tabKey)
  const rules = {}
  
  fields.forEach(field => {
    if (field.validation) {
      rules[field.key] = field.validation
    }
  })
  
  return rules
}

export default DETAIL_TEMPLATES
