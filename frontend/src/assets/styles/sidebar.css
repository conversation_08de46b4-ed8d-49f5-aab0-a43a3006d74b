/* ===== 世界级侧边栏样式系统 ===== */

/* ===== 侧边栏主容器 ===== */
.sidebar-premium {
  background: linear-gradient(180deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%);
  border-right: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  color: var(--color-neutral-700);
  transition: all var(--duration-300) var(--ease-in-out);
  contain: layout style paint;
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

.dark .sidebar-premium {
  background: linear-gradient(180deg,
    rgba(15, 23, 42, 0.98) 0%,
    rgba(2, 6, 23, 0.95) 100%);
  border-right: 1px solid rgba(51, 65, 85, 0.8);
  color: var(--color-neutral-200);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.3),
    0 2px 4px -1px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.02);
}

.sidebar-premium.collapsed {
  width: 5rem; /* 80px，为图标和动效提供更好的空间 */
}

.sidebar-premium.expanded {
  width: 17rem; /* 272px，提供更宽敞的空间 */
}

/* ===== 侧边栏内容区域 - 支持滚动 ===== */
.sidebar-content-premium {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--color-neutral-400) transparent;
  max-height: calc(100vh - 120px);
}

.sidebar-content-premium::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content-premium::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content-premium::-webkit-scrollbar-thumb {
  background: var(--color-neutral-400);
  border-radius: var(--radius-full);
}

.sidebar-content-premium::-webkit-scrollbar-thumb:hover {
  background: var(--color-neutral-500);
}

/* ===== 侧边栏头部和Logo ===== */
.sidebar-header-premium {
  padding: 1.25rem 1rem;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  position: relative;
  z-index: 10;
  height: var(--header-height);
  display: flex;
  align-items: center;
}

.dark .sidebar-header-premium {
  border-bottom: 1px solid rgba(51, 65, 85, 0.6);
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.9) 0%,
    rgba(2, 6, 23, 0.8) 100%);
}

/* Logo容器样式 */
.logo-container {
  position: relative;
  transition: all var(--duration-300) var(--ease-out);
}

/* Logo图标样式 */
.logo-icon {
  width: 2.75rem;
  height: 2.75rem;
  background: linear-gradient(135deg,
    var(--color-primary-500) 0%,
    var(--color-secondary-500) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow:
    0 4px 12px rgba(14, 165, 233, 0.25),
    0 2px 6px rgba(168, 85, 247, 0.15);
  transition: all var(--duration-300) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%);
  opacity: 0;
  transition: opacity var(--duration-200) var(--ease-out);
}

.logo-icon:hover {
  transform: scale(1.05) rotate(5deg);
  box-shadow:
    0 8px 25px rgba(14, 165, 233, 0.35),
    0 4px 12px rgba(168, 85, 247, 0.25);
}

.logo-icon:hover::before {
  opacity: 1;
}

.logo-icon svg {
  width: 1.5rem;
  height: 1.5rem;
  transition: transform var(--duration-200) var(--ease-out);
}

.logo-icon:hover svg {
  transform: scale(1.1);
}

/* Logo文字样式 */
.logo-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  line-height: 1;
}

.logo-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin: 0;
  line-height: 1.2;
  background: linear-gradient(135deg,
    var(--color-primary-600) 0%,
    var(--color-secondary-600) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-subtitle {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-neutral-500);
  margin: 0;
  line-height: 1.2;
  margin-top: 0.125rem;
}

.dark .logo-title {
  background: linear-gradient(135deg,
    var(--color-primary-400) 0%,
    var(--color-secondary-400) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .logo-subtitle {
  color: var(--color-neutral-400);
}

/* 收起/展开按钮样式 */
.sidebar-toggle-premium {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-200) var(--ease-out);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.sidebar-toggle-premium:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: var(--color-primary-300);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.15);
  transform: scale(1.05);
}

.dark .sidebar-toggle-premium {
  background: rgba(15, 23, 42, 0.8);
  border-color: rgba(51, 65, 85, 0.8);
}

.dark .sidebar-toggle-premium:hover {
  background: rgba(15, 23, 42, 0.95);
  border-color: var(--color-primary-400);
}

.toggle-icon {
  width: 1rem;
  height: 1rem;
  color: var(--color-neutral-600);
  transition: all var(--duration-200) var(--ease-out);
}

.sidebar-toggle-premium:hover .toggle-icon {
  color: var(--color-primary-600);
}

.dark .toggle-icon {
  color: var(--color-neutral-400);
}

.dark .sidebar-toggle-premium:hover .toggle-icon {
  color: var(--color-primary-400);
}

/* ===== 菜单按钮样式 ===== */
.sidebar-menu-button-premium {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-xl);
  color: var(--color-neutral-600);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  position: relative;
  transition: all var(--duration-200) var(--ease-out);
  border: 1px solid transparent;
  background: transparent;
  cursor: pointer;
  overflow: hidden;
}

.sidebar-menu-button-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(14, 165, 233, 0.05) 0%,
    rgba(168, 85, 247, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--duration-200) var(--ease-out);
  z-index: 0;
}

.sidebar-menu-button-premium:hover {
  background: rgba(255, 255, 255, 0.8);
  color: var(--color-neutral-900);
  border-color: rgba(14, 165, 233, 0.2);
  box-shadow:
    0 4px 12px rgba(14, 165, 233, 0.1),
    0 2px 6px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px) scale(1.02);
}

.sidebar-menu-button-premium:hover::before {
  opacity: 1;
}

.sidebar-menu-button-premium.active {
  background: linear-gradient(135deg,
    var(--color-primary-500) 0%,
    var(--color-secondary-500) 100%);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 25px rgba(14, 165, 233, 0.25),
    0 4px 12px rgba(168, 85, 247, 0.15);
  transform: translateX(2px);
}

.sidebar-menu-button-premium.active::before {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%);
  opacity: 1;
}

.dark .sidebar-menu-button-premium {
  color: var(--color-neutral-300);
}

.dark .sidebar-menu-button-premium:hover {
  background: rgba(15, 23, 42, 0.8);
  color: var(--color-neutral-100);
  border-color: rgba(14, 165, 233, 0.3);
}

.dark .sidebar-menu-button-premium.active {
  background: linear-gradient(135deg,
    var(--color-primary-600) 0%,
    var(--color-secondary-600) 100%);
  box-shadow:
    0 8px 25px rgba(14, 165, 233, 0.4),
    0 4px 12px rgba(168, 85, 247, 0.25);
}

.dark .sidebar-menu-button-premium:hover {
  background: var(--color-neutral-700);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px) scale(1.02);
  border-left: 4px solid var(--color-primary-400);
}

.dark .sidebar-menu-button-premium.active {
  background: var(--gradient-primary);
  color: white;
  box-shadow: 0 6px 20px rgba(14, 165, 233, 0.4), 0 3px 8px rgba(14, 165, 233, 0.25);
  border-left: 4px solid rgba(255, 255, 255, 0.95);
  transform: translateX(2px);
}

/* 深色模式下的收起状态优化 */
.dark .sidebar-premium.collapsed .sidebar-menu-button-premium:hover {
  transform: scale(1.1);
  border-left: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
}

.dark .sidebar-premium.collapsed .sidebar-menu-button-premium.active {
  transform: scale(1.05);
  border-left: none;
  box-shadow: 0 6px 24px rgba(14, 165, 233, 0.5);
}

/* ===== 菜单图标和文字 ===== */
.sidebar-menu-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
  transition: all var(--duration-200) var(--ease-out);
  position: relative;
  z-index: 1;
}

.sidebar-menu-button-premium:hover .sidebar-menu-icon {
  transform: scale(1.15) rotate(5deg);
  color: var(--color-primary-600);
}

.sidebar-menu-button-premium.active .sidebar-menu-icon {
  transform: scale(1.1);
  color: white;
}

.sidebar-menu-text {
  flex: 1;
  font-weight: var(--font-weight-medium);
  line-height: 1.4;
  transition: all var(--duration-200) var(--ease-out);
  position: relative;
  z-index: 1;
}

.sidebar-premium.collapsed .sidebar-menu-text {
  display: none;
}

.sidebar-premium.collapsed .sidebar-menu-icon {
  margin-right: 0;
}

/* 菜单项徽章样式 */
.sidebar-menu-badge {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  background: var(--color-error-500);
  color: white;
  font-size: 0.625rem;
  font-weight: var(--font-weight-bold);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-full);
  min-width: 1.25rem;
  height: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* ===== 菜单分组样式 ===== */
.menu-group-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  margin: 1rem 0 0.5rem 0;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--color-neutral-500);
  background: linear-gradient(135deg,
    rgba(14, 165, 233, 0.05) 0%,
    rgba(168, 85, 247, 0.05) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(14, 165, 233, 0.1);
  position: relative;
  overflow: hidden;
}

.menu-group-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg,
    var(--color-primary-500) 0%,
    var(--color-secondary-500) 100%);
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.dark .menu-group-label {
  color: var(--color-neutral-400);
  background: linear-gradient(135deg,
    rgba(14, 165, 233, 0.1) 0%,
    rgba(168, 85, 247, 0.1) 100%);
  border-color: rgba(14, 165, 233, 0.2);
}

.sidebar-premium.collapsed .menu-group-label {
  padding: 0.5rem;
  margin: 0.5rem 0;
  justify-content: center;
}

.sidebar-premium.collapsed .menu-group-label span {
  display: none;
}

/* ===== 菜单项间距和布局 ===== */
.sidebar-menu {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0 0.5rem;
}

.sidebar-menu-item {
  position: relative;
}

/* ===== 收起状态优化 ===== */
.sidebar-premium.collapsed .sidebar-menu {
  padding: 0 0.25rem;
}

.sidebar-premium.collapsed .sidebar-menu-button-premium {
  padding: 0.75rem;
  justify-content: center;
  min-height: 3rem;
}

.sidebar-premium.collapsed .sidebar-menu-button-premium:hover {
  transform: scale(1.05);
}

/* ===== 分割线样式 ===== */
.sidebar-divider {
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(226, 232, 240, 0.8) 20%,
    rgba(226, 232, 240, 0.8) 80%,
    transparent 100%);
  margin: 1rem 0;
  position: relative;
}

.dark .sidebar-divider {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(51, 65, 85, 0.8) 20%,
    rgba(51, 65, 85, 0.8) 80%,
    transparent 100%);
}

.sidebar-divider::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 3px;
  background: var(--color-primary-500);
  border-radius: var(--radius-full);
  opacity: 0.6;
}

/* ===== 高级交互效果 ===== */

/* 菜单项加载动画 */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-menu-item {
  animation: slideInFromLeft 0.3s ease-out;
  animation-fill-mode: both;
}

.sidebar-menu-item:nth-child(1) { animation-delay: 0.1s; }
.sidebar-menu-item:nth-child(2) { animation-delay: 0.15s; }
.sidebar-menu-item:nth-child(3) { animation-delay: 0.2s; }
.sidebar-menu-item:nth-child(4) { animation-delay: 0.25s; }
.sidebar-menu-item:nth-child(5) { animation-delay: 0.3s; }

/* 菜单按钮点击效果 */
.sidebar-menu-button-premium:active {
  transform: scale(0.98);
  transition: transform 0.1s ease-out;
}

/* 菜单项长按效果 */
.sidebar-menu-button-premium.long-pressing {
  background: linear-gradient(135deg,
    rgba(14, 165, 233, 0.2) 0%,
    rgba(168, 85, 247, 0.2) 100%);
  transform: scale(1.02);
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
}

/* 拖拽状态 */
.sidebar-menu-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
  pointer-events: none;
}

.sidebar-menu-item.drag-over {
  border-top: 2px solid var(--color-primary-500);
  margin-top: 2px;
}

/* 菜单组展开/收起动画 */
.menu-group-content {
  overflow: hidden;
  transition: all var(--duration-300) var(--ease-out);
}

.menu-group-content.collapsed {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.menu-group-content.expanded {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
}

/* 搜索高亮动画 */
@keyframes searchHighlight {
  0% {
    background: transparent;
  }
  50% {
    background: rgba(14, 165, 233, 0.2);
  }
  100% {
    background: transparent;
  }
}

.sidebar-menu-button-premium.search-highlighted {
  animation: searchHighlight 1s ease-in-out;
}

/* 通知徽章动画 */
@keyframes badgeBounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-3px) scale(1.1);
  }
  60% {
    transform: translateY(-1px) scale(1.05);
  }
}

.sidebar-menu-badge.new {
  animation: badgeBounce 0.6s ease-out;
}

/* 键盘导航焦点样式 */
.sidebar-menu-button-premium:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-lg);
}

/* 收起状态特殊动画 */
.sidebar-premium.collapsed .sidebar-menu-button-premium:hover {
  transform: scale(1.1) translateX(4px);
  box-shadow:
    0 8px 25px rgba(14, 165, 233, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 菜单项右键上下文菜单指示 */
.sidebar-menu-button-premium.has-context::after {
  content: '⋯';
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity var(--duration-200) var(--ease-out);
  font-weight: bold;
  color: var(--color-neutral-400);
}

.sidebar-menu-button-premium.has-context:hover::after {
  opacity: 1;
}

/* 加载状态 */
.sidebar-menu-button-premium.loading {
  position: relative;
  pointer-events: none;
}

.sidebar-menu-button-premium.loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid transparent;
  border-top: 2px solid var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 成功/错误状态指示 */
.sidebar-menu-button-premium.success {
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.1) 0%,
    rgba(34, 197, 94, 0.05) 100%);
  border-color: rgba(34, 197, 94, 0.3);
}

.sidebar-menu-button-premium.error {
  background: linear-gradient(135deg,
    rgba(239, 68, 68, 0.1) 0%,
    rgba(239, 68, 68, 0.05) 100%);
  border-color: rgba(239, 68, 68, 0.3);
}

/* ===== 响应式布局优化 ===== */

/* 平板设备 (768px - 1024px) */
@media (max-width: 1024px) and (min-width: 768px) {
  .sidebar-premium.expanded {
    width: 15rem; /* 稍微缩小宽度 */
  }

  .sidebar-header-premium {
    padding: 1rem 0.75rem;
  }

  .logo-title {
    font-size: 1rem;
  }

  .logo-subtitle {
    font-size: 0.6875rem;
  }

  .sidebar-menu-button-premium {
    padding: 0.625rem 0.75rem;
    font-size: var(--font-size-xs);
  }

  .sidebar-menu-icon {
    width: 1.125rem;
    height: 1.125rem;
  }

  .menu-group-label {
    padding: 0.625rem 0.75rem;
    font-size: 0.625rem;
  }
}

/* 移动设备 (< 768px) */
@media (max-width: 767px) {
  .sidebar-premium {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform var(--duration-300) var(--ease-out);
    width: 280px !important; /* 固定宽度，不受collapsed状态影响 */
  }

  .sidebar-premium.mobile-open {
    transform: translateX(0);
  }

  .sidebar-premium.collapsed {
    width: 280px !important; /* 移动端不使用collapsed状态 */
  }

  /* 移动端始终显示完整菜单 */
  .sidebar-premium.collapsed .sidebar-menu-text,
  .sidebar-premium.collapsed .logo-text {
    display: block !important;
  }

  .sidebar-premium.collapsed .sidebar-menu-icon {
    margin-right: 0.75rem !important;
  }

  .sidebar-premium.collapsed .menu-group-label span {
    display: inline !important;
  }

  /* 移动端头部调整 */
  .sidebar-header-premium {
    padding: 1rem;
    border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  }

  .logo-container {
    flex: 1;
  }

  .sidebar-toggle-premium {
    display: none; /* 移动端隐藏收起按钮 */
  }

  /* 移动端菜单项优化 */
  .sidebar-menu-button-premium {
    padding: 1rem;
    font-size: var(--font-size-sm);
    min-height: 3.5rem;
  }

  .sidebar-menu-icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .menu-group-label {
    padding: 1rem;
    font-size: var(--font-size-xs);
    margin: 0.5rem 0;
  }

  /* 移动端搜索优化 */
  .search-input-wrapper {
    margin-bottom: 1rem;
  }

  .search-input {
    padding: 1rem 3rem 1rem 3rem;
    font-size: var(--font-size-base);
  }

  /* 移动端智能面板优化 */
  .smart-menu-panel {
    padding: 1rem;
  }

  .quick-access-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .quick-access-item {
    padding: 1.25rem 0.75rem;
  }

  .item-icon {
    width: 3rem;
    height: 3rem;
  }

  .item-label {
    font-size: var(--font-size-sm);
  }
}

/* 超小屏幕设备 (< 480px) */
@media (max-width: 479px) {
  .sidebar-premium {
    width: 100vw !important; /* 全屏宽度 */
  }

  .quick-access-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .shortcuts-grid {
    grid-template-columns: 1fr;
  }

  .footer-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-button {
    width: 100%;
  }
}

/* 大屏幕优化 (> 1440px) */
@media (min-width: 1440px) {
  .sidebar-premium.expanded {
    width: 18rem; /* 更宽的侧边栏 */
  }

  .sidebar-header-premium {
    padding: 1.5rem 1.25rem;
  }

  .logo-icon {
    width: 3rem;
    height: 3rem;
  }

  .logo-title {
    font-size: 1.25rem;
  }

  .sidebar-menu-button-premium {
    padding: 1rem 1.25rem;
    font-size: var(--font-size-base);
  }

  .sidebar-menu-icon {
    width: 1.375rem;
    height: 1.375rem;
  }

  .quick-access-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .sidebar-menu-button-premium {
    min-height: 3rem; /* 更大的触摸目标 */
  }

  .quick-access-item {
    min-height: 4rem;
  }

  .action-button {
    min-width: 2.5rem;
    min-height: 2.5rem;
  }

  .search-input {
    padding: 1rem 3rem;
  }

  /* 移除hover效果，避免在触摸设备上的粘滞 */
  .sidebar-menu-button-premium:hover {
    transform: none;
    background: transparent;
  }

  .quick-access-item:hover {
    transform: none;
  }

  .recommendation-item:hover {
    transform: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .sidebar-premium {
    border-right: 2px solid var(--color-neutral-900);
  }

  .dark .sidebar-premium {
    border-right: 2px solid var(--color-neutral-100);
  }

  .sidebar-menu-button-premium {
    border: 1px solid var(--color-neutral-400);
  }

  .sidebar-menu-button-premium.active {
    border: 2px solid var(--color-primary-600);
  }

  .menu-group-label {
    border: 1px solid var(--color-neutral-400);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .sidebar-premium,
  .sidebar-menu-button-premium,
  .quick-access-item,
  .recommendation-item,
  .logo-icon,
  .sidebar-toggle-premium {
    transition: none !important;
    animation: none !important;
  }

  .sidebar-menu-item {
    animation: none !important;
  }

  .sidebar-menu-badge.new {
    animation: none !important;
  }
}

/* 打印样式 */
@media print {
  .sidebar-premium {
    display: none !important;
  }
}

/* 收起状态下的菜单按钮优化 */
.sidebar-premium.collapsed .sidebar-menu-button-premium {
  justify-content: center;
  padding: 0.75rem 0.5rem; /* 增加垂直内边距，让图标更突出 */
  margin: 0.25rem 0.5rem; /* 添加外边距，创造更好的视觉分离 */
  border-radius: var(--radius-xl); /* 使用更大的圆角 */
}

/* 收起状态下的图标优化 */
.sidebar-premium.collapsed .sidebar-menu-icon {
  margin-right: 0; /* 收起时图标不需要右边距 */
  font-size: 1.5rem; /* 增大图标尺寸让它更明显 */
  transition: all var(--duration-200) var(--ease-out);
}

/* 收起状态下的悬浮效果增强 */
.sidebar-premium.collapsed .sidebar-menu-button-premium:hover {
  transform: scale(1.1); /* 收起时使用缩放而不是上移 */
  border-left: none; /* 移除左边框，避免布局问题 */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12); /* 增强阴影 */
}

.sidebar-premium.collapsed .sidebar-menu-button-premium.active {
  transform: scale(1.05); /* 活跃状态稍微小一点的缩放 */
  border-left: none;
  box-shadow: 0 6px 24px rgba(14, 165, 233, 0.3);
}
/* ===== 工具提示 ===== */
.sidebar-tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%) translateX(-8px);
  margin-left: 0.75rem; /* 增加与侧边栏的距离 */
  padding: 0.5rem 0.75rem; /* 增加内边距让提示更舒适 */
  background: var(--color-neutral-900);
  color: white;
  border-radius: var(--radius-lg); /* 使用更大的圆角 */
  font-size: var(--font-size-sm); /* 稍微增大字体 */
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--duration-200) var(--ease-in-out),
              transform var(--duration-200) var(--ease-out);
  z-index: var(--z-tooltip);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* 添加阴影增强层次感 */
}

/* 工具提示箭头 */
.sidebar-tooltip::before {
  content: '';
  position: absolute;
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  border: 4px solid transparent;
  border-right-color: var(--color-neutral-900);
}

/* 只在收起状态下显示工具提示 */
.sidebar-premium.collapsed .sidebar-menu-button-premium:hover .sidebar-tooltip {
  opacity: 1;
  transform: translateY(-50%) translateX(0);
}

.dark .sidebar-tooltip {
  background: var(--color-neutral-100);
  color: var(--color-neutral-900);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

.dark .sidebar-tooltip::before {
  border-right-color: var(--color-neutral-100);
}

/* ===== 菜单组样式 ===== */
.sidebar-group-premium {
  margin-bottom: var(--spacing-4);
}

.sidebar-group-label-premium {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-2) var(--spacing-3);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--color-neutral-500);
  transition: color var(--duration-200) var(--ease-in-out);
}

.dark .sidebar-group-label-premium {
  color: var(--color-neutral-400);
}

/* 菜单组箭头 */
.sidebar-group-arrow {
  font-size: var(--font-size-xs);
  transition: transform var(--duration-200) var(--ease-in-out);
  color: var(--color-neutral-400);
}

.sidebar-group-arrow.expanded {
  transform: rotate(90deg);
}

/* 菜单组内容 */
.sidebar-group-content-premium {
  overflow: hidden;
  transition: max-height var(--duration-300) var(--ease-in-out),
              opacity var(--duration-200) var(--ease-in-out);
}

.sidebar-group-content-premium.collapsed {
  max-height: 0;
  opacity: 0;
}

.sidebar-group-content-premium.expanded {
  max-height: 500px;
  opacity: 1;
}

/* ===== 徽章样式 ===== */
.sidebar-badge {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  min-width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  transition: transform 0.2s ease-out;
  z-index: 10;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  padding: 0 0.25rem;
}

.sidebar-menu-button-premium:hover .sidebar-badge {
  transform: scale(1.1);
}

.sidebar-badge.primary {
  background: #3b82f6;
}

.sidebar-badge.success {
  background: #10b981;
}

.sidebar-badge.warning {
  background: #f59e0b;
}

.sidebar-badge.danger {
  background: #ef4444;
}

.sidebar-badge.dot {
  width: 0.5rem;
  height: 0.5rem;
  min-width: 0.5rem;
  padding: 0;
}

/* ===== 搜索框样式 ===== */
.sidebar-search-input {
  background: var(--color-neutral-100);
  border: 1px solid var(--color-neutral-300);
  color: var(--color-neutral-900);
}

.sidebar-search-input::placeholder {
  color: var(--color-neutral-500);
}

.sidebar-search-input:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: var(--color-neutral-50);
}

.sidebar-search-input-focused {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.sidebar-search-icon {
  color: var(--color-neutral-500);
}

/* 深色模式下的搜索框 */
.dark .sidebar-search-input {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.dark .sidebar-search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.dark .sidebar-search-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.dark .sidebar-search-input-focused {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.dark .sidebar-search-icon {
  color: rgba(255, 255, 255, 0.4);
}

/* ===== 分割条样式 ===== */
.sidebar-divider {
  border-top: 1px solid var(--color-neutral-300);
  margin: 1rem 0;
}

.dark .sidebar-divider {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== 切换按钮样式 ===== */
.sidebar-toggle-premium {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  background: transparent;
  border: none;
  color: var(--color-neutral-500);
  transition: background-color var(--duration-200) var(--ease-in-out),
              color var(--duration-200) var(--ease-in-out);
  cursor: pointer;
}

.sidebar-toggle-premium:hover {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
}

.dark .sidebar-toggle-premium {
  color: var(--color-neutral-400);
}

.dark .sidebar-toggle-premium:hover {
  background: var(--color-neutral-700);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar-expanded {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 50;
  }

  .sidebar-collapsed {
    transform: translateX(-100%);
  }
}
