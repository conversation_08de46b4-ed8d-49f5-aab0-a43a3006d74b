/* ===== 世界级动画系统 ===== */

/* ===== 关键帧动画定义 ===== */

/* 淡入淡出动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.9);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* 滑动动画 */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

/* 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

/* 涟漪效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* 骨架屏动画 */
@keyframes skeleton {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 呼吸效果 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.8;
  }
}

/* 弹性进入 */
@keyframes elasticIn {
  0% {
    transform: scale(0);
  }
  55% {
    transform: scale(1.1);
  }
  75% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* ===== 动画工具类 ===== */

/* 基础动画类 */
.animate-fade-in {
  animation: fadeIn var(--duration-300) var(--ease-out) forwards;
}

.animate-fade-out {
  animation: fadeOut var(--duration-300) var(--ease-in) forwards;
}

.animate-fade-in-up {
  animation: fadeInUp var(--duration-300) var(--ease-out) forwards;
}

.animate-fade-in-down {
  animation: fadeInDown var(--duration-300) var(--ease-out) forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft var(--duration-300) var(--ease-out) forwards;
}

.animate-fade-in-right {
  animation: fadeInRight var(--duration-300) var(--ease-out) forwards;
}

.animate-scale-in {
  animation: scaleIn var(--duration-200) var(--ease-out) forwards;
}

.animate-scale-out {
  animation: scaleOut var(--duration-200) var(--ease-in) forwards;
}

.animate-pulse {
  animation: pulse var(--duration-1000) var(--ease-in-out) infinite;
}

.animate-bounce {
  animation: bounce var(--duration-1000) var(--ease-bounce) infinite;
}

.animate-spin {
  animation: spin var(--duration-1000) linear infinite;
}

.animate-wiggle {
  animation: wiggle var(--duration-1000) var(--ease-in-out);
}

.animate-breathe {
  animation: breathe 2s var(--ease-in-out) infinite;
}

.animate-elastic-in {
  animation: elasticIn var(--duration-500) var(--ease-elastic) forwards;
}

/* 延迟动画类 */
.animate-delay-75 {
  animation-delay: var(--delay-75);
}

.animate-delay-100 {
  animation-delay: var(--delay-100);
}

.animate-delay-150 {
  animation-delay: var(--delay-150);
}

.animate-delay-200 {
  animation-delay: var(--delay-200);
}

.animate-delay-300 {
  animation-delay: var(--delay-300);
}

.animate-delay-500 {
  animation-delay: var(--delay-500);
}

/* 过渡效果类 */
.transition-all {
  transition: all var(--duration-200) var(--ease-in-out);
}

.transition-colors {
  transition: color var(--duration-200) var(--ease-in-out),
              background-color var(--duration-200) var(--ease-in-out),
              border-color var(--duration-200) var(--ease-in-out);
}

.transition-transform {
  transition: transform var(--duration-200) var(--ease-in-out);
}

.transition-opacity {
  transition: opacity var(--duration-200) var(--ease-in-out);
}

.transition-shadow {
  transition: box-shadow var(--duration-200) var(--ease-in-out);
}

.transition-smooth {
  transition: all var(--duration-300) var(--ease-out);
}

.transition-bounce {
  transition: all var(--duration-300) var(--ease-bounce);
}

.transition-elastic {
  transition: all var(--duration-500) var(--ease-elastic);
}

/* 悬浮效果类 */
.hover-lift {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--duration-200) var(--ease-out);
}

.hover-glow:hover {
  box-shadow: var(--shadow-glow);
}

/* 涟漪效果 */
.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width var(--duration-300) var(--ease-out),
              height var(--duration-300) var(--ease-out);
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

/* 骨架屏效果 */
.skeleton {
  background: linear-gradient(90deg, 
    var(--color-neutral-200) 25%, 
    var(--color-neutral-100) 50%, 
    var(--color-neutral-200) 75%);
  background-size: 200px 100%;
  animation: skeleton var(--duration-1000) ease-in-out infinite;
}

.dark .skeleton {
  background: linear-gradient(90deg, 
    var(--color-neutral-800) 25%, 
    var(--color-neutral-700) 50%, 
    var(--color-neutral-800) 75%);
  background-size: 200px 100%;
}

/* 性能优化 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-auto {
  will-change: auto;
}

/* ===== 高级物理感动画 ===== */

/* 弹性进入动画 */
@keyframes elasticInOut {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  75% {
    transform: scale(0.95);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 弹跳悬浮效果 */
@keyframes bounceHover {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-2px) scale(1.02);
  }
}

/* 波浪式展开 */
@keyframes waveExpand {
  0% {
    transform: scaleX(0);
    opacity: 0;
  }
  50% {
    transform: scaleX(1.05);
    opacity: 0.7;
  }
  100% {
    transform: scaleX(1);
    opacity: 1;
  }
}

/* 流体动画 */
@keyframes fluidMotion {
  0% {
    transform: translateX(-20px) scale(0.9);
    opacity: 0;
  }
  60% {
    transform: translateX(5px) scale(1.02);
    opacity: 0.8;
  }
  100% {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

/* 磁性吸附效果 */
@keyframes magneticSnap {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scale(0.95);
  }
  60% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 呼吸光晕 */
@keyframes breatheGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(14, 165, 233, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.6);
  }
}

/* 粒子扩散 */
@keyframes particleExpand {
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: scale(3) rotate(180deg);
    opacity: 0;
  }
}

/* 液体波动 */
@keyframes liquidWave {
  0%, 100% {
    border-radius: 50% 50% 50% 50%;
  }
  25% {
    border-radius: 60% 40% 60% 40%;
  }
  50% {
    border-radius: 40% 60% 40% 60%;
  }
  75% {
    border-radius: 50% 50% 40% 60%;
  }
}

/* ===== 高级动画工具类 ===== */

/* 弹性动画类 */
.animate-elastic-in-out {
  animation: elasticInOut var(--duration-500) var(--ease-elastic) forwards;
}

.animate-bounce-hover {
  animation: bounceHover var(--duration-300) var(--ease-out) infinite;
}

.animate-wave-expand {
  animation: waveExpand var(--duration-300) var(--ease-out) forwards;
}

.animate-fluid-motion {
  animation: fluidMotion var(--duration-300) var(--ease-out) forwards;
}

.animate-magnetic-snap {
  animation: magneticSnap var(--duration-200) var(--ease-bounce);
}

.animate-breathe-glow {
  animation: breatheGlow 2s var(--ease-in-out) infinite;
}

.animate-particle-expand {
  animation: particleExpand var(--duration-500) var(--ease-out) forwards;
}

.animate-liquid-wave {
  animation: liquidWave 3s var(--ease-in-out) infinite;
}

/* ===== 高级过渡效果 ===== */

/* 物理感过渡 */
.transition-physics {
  transition: all var(--duration-300) cubic-bezier(0.34, 1.56, 0.64, 1);
}

.transition-spring {
  transition: all var(--duration-500) cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.transition-bounce-in {
  transition: all var(--duration-300) cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.transition-smooth-out {
  transition: all var(--duration-200) cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 错开动画时序 */
.stagger-1 { animation-delay: 0ms; }
.stagger-2 { animation-delay: 50ms; }
.stagger-3 { animation-delay: 100ms; }
.stagger-4 { animation-delay: 150ms; }
.stagger-5 { animation-delay: 200ms; }
.stagger-6 { animation-delay: 250ms; }
.stagger-7 { animation-delay: 300ms; }
.stagger-8 { animation-delay: 350ms; }

/* 悬浮增强效果 */
.hover-physics {
  transition: transform var(--duration-200) cubic-bezier(0.34, 1.56, 0.64, 1);
}

.hover-physics:hover {
  transform: translateY(-3px) scale(1.02);
}

.hover-magnetic {
  transition: all var(--duration-200) var(--ease-out);
}

.hover-magnetic:hover {
  transform: scale(1.05);
  filter: brightness(1.1);
}

.hover-float {
  transition: transform var(--duration-300) var(--ease-out);
}

.hover-float:hover {
  transform: translateY(-4px);
  animation: bounceHover var(--duration-1000) var(--ease-in-out) infinite;
}

/* ===== 组合动画效果 ===== */

/* 菜单项组合动画 */
.menu-item-enter {
  animation:
    fadeInLeft var(--duration-300) var(--ease-out) forwards,
    elasticIn var(--duration-500) var(--ease-elastic) forwards;
}

.menu-item-hover {
  transition: all var(--duration-200) cubic-bezier(0.34, 1.56, 0.64, 1);
}

.menu-item-hover:hover {
  transform: translateY(-1px) scale(1.02);
  animation: magneticSnap var(--duration-200) var(--ease-bounce);
}

/* 侧边栏展开/收起动画 */
.sidebar-expand {
  animation:
    slideInLeft var(--duration-300) var(--ease-out),
    elasticIn var(--duration-500) var(--ease-elastic);
}

.sidebar-collapse {
  animation: slideOutLeft var(--duration-300) var(--ease-in);
}

/* 菜单组波浪展开 */
.group-wave-expand .sidebar-menu-item:nth-child(1) {
  animation: waveExpand var(--duration-300) var(--ease-out) 0ms forwards;
}

.group-wave-expand .sidebar-menu-item:nth-child(2) {
  animation: waveExpand var(--duration-300) var(--ease-out) 50ms forwards;
}

.group-wave-expand .sidebar-menu-item:nth-child(3) {
  animation: waveExpand var(--duration-300) var(--ease-out) 100ms forwards;
}

.group-wave-expand .sidebar-menu-item:nth-child(4) {
  animation: waveExpand var(--duration-300) var(--ease-out) 150ms forwards;
}

.group-wave-expand .sidebar-menu-item:nth-child(5) {
  animation: waveExpand var(--duration-300) var(--ease-out) 200ms forwards;
}

/* ===== 性能优化增强 ===== */

/* GPU 加速 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 高性能动画 */
.performance-optimized {
  will-change: transform, opacity;
  contain: layout style paint;
  transform: translateZ(0);
}

/* 减少重绘 */
.no-repaint {
  contain: strict;
  will-change: transform;
}

/* 动画状态管理 */
.animating {
  will-change: transform, opacity, box-shadow;
}

.animating:not(.hover) {
  will-change: auto;
}
