/* ===== 世界级设计系统 - 设计令牌 ===== */

:root {
  /* ===== 色彩系统 ===== */
  
  /* 主色调 - 现代蓝紫渐变 */
  --color-primary-50: #f0f9ff;
  --color-primary-100: #e0f2fe;
  --color-primary-200: #bae6fd;
  --color-primary-300: #7dd3fc;
  --color-primary-400: #38bdf8;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  --color-primary-800: #075985;
  --color-primary-900: #0c4a6e;
  --color-primary-950: #082f49;

  /* 辅助色调 - 紫色系 */
  --color-secondary-50: #faf5ff;
  --color-secondary-100: #f3e8ff;
  --color-secondary-200: #e9d5ff;
  --color-secondary-300: #d8b4fe;
  --color-secondary-400: #c084fc;
  --color-secondary-500: #a855f7;
  --color-secondary-600: #9333ea;
  --color-secondary-700: #7c3aed;
  --color-secondary-800: #6b21a8;
  --color-secondary-900: #581c87;

  /* 中性色系 - 现代灰色 */
  --color-neutral-0: #ffffff;
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-neutral-950: #0a0a0a;

  /* 语义色彩 */
  --color-success-50: #f0fdf4;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-warning-50: #fffbeb;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-error-50: #fef2f2;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-info-50: #eff6ff;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;

  /* 渐变色系 */
  --gradient-primary: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-secondary-500) 100%);
  --gradient-primary-soft: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-secondary-100) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  --gradient-glass-dark: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);

  /* ===== 间距系统 (8px 网格) ===== */
  --spacing-0: 0;
  --spacing-1: 0.25rem;  /* 4px */
  --spacing-2: 0.5rem;   /* 8px */
  --spacing-3: 0.75rem;  /* 12px */
  --spacing-4: 1rem;     /* 16px */
  --spacing-5: 1.25rem;  /* 20px */
  --spacing-6: 1.5rem;   /* 24px */
  --spacing-8: 2rem;     /* 32px */
  --spacing-10: 2.5rem;  /* 40px */
  --spacing-12: 3rem;    /* 48px */
  --spacing-16: 4rem;    /* 64px */
  --spacing-20: 5rem;    /* 80px */
  --spacing-24: 6rem;    /* 96px */

  /* ===== 布局高度系统 ===== */
  --header-height: 56px;  /* 统一的header高度，确保侧边栏和主header区域一致性 */

  /* ===== 圆角系统 ===== */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */
  --radius-full: 9999px;

  /* ===== 阴影系统 ===== */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
  
  /* 彩色阴影 */
  --shadow-primary: 0 10px 15px -3px rgba(14, 165, 233, 0.1), 0 4px 6px -4px rgba(14, 165, 233, 0.1);
  --shadow-secondary: 0 10px 15px -3px rgba(168, 85, 247, 0.1), 0 4px 6px -4px rgba(168, 85, 247, 0.1);

  /* ===== 字体系统 ===== */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* ===== 动画系统 ===== */
  
  /* 缓动函数 */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --ease-back: cubic-bezier(0.68, -0.6, 0.32, 1.6);

  /* 动画时长 */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  /* 动画延迟 */
  --delay-75: 75ms;
  --delay-100: 100ms;
  --delay-150: 150ms;
  --delay-200: 200ms;
  --delay-300: 300ms;
  --delay-500: 500ms;

  /* ===== Z-Index 层级 ===== */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* ===== 断点系统 ===== */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ===== 深色主题 ===== */
.dark {
  /* 暂时注释掉深色模式下的色彩覆盖，让Tailwind使用标准颜色 */
  /*
  --color-neutral-0: #000000;
  --color-neutral-50: #0a0a0a;
  --color-neutral-100: #171717;
  --color-neutral-200: #262626;
  --color-neutral-300: #404040;
  --color-neutral-400: #525252;
  --color-neutral-500: #737373;
  --color-neutral-600: #a3a3a3;
  --color-neutral-700: #d4d4d4;
  --color-neutral-800: #e5e5e5;
  --color-neutral-900: #f5f5f5;
  --color-neutral-950: #fafafa;
  */

  /* 深色模式渐变 */
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
  --gradient-glass-dark: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);

  /* 深色模式阴影 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);

  /* 深色主题卡片样式 */
  --card-bg-primary: #1f2937;          /* 主要内容卡片 */
  --card-bg-secondary: #374151;        /* 次要内容卡片 */
  --card-bg-accent: #4b5563;           /* 强调卡片 */
  --card-bg-interactive: #374151;      /* 交互式卡片 */
  --card-bg-stats: #374151;            /* 统计卡片 */

  /* 深色主题卡片边框 */
  --card-border-primary: #4b5563;      /* 主边框 */
  --card-border-secondary: #6b7280;    /* 次要边框 */
  --card-border-accent: #9ca3af;       /* 强调边框 */

  /* 深色主题卡片阴影 */
  --card-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --card-shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --card-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);


}

/* ===== 实用工具类 ===== */
.glass-effect {
  background: var(--gradient-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .glass-effect {
  background: var(--gradient-glass-dark);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* ===== 卡片样式类 ===== */

/* 基础卡片样式 */
.card-base {
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

/* 主要内容卡片 */
.card-primary {
  background-color: var(--card-bg-primary);
  border: 1px solid var(--card-border-primary);
  box-shadow: var(--card-shadow-base);
}

.card-primary:hover {
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-1px);
}

/* 次要内容卡片 - 带底色 */
.card-secondary {
  background-color: var(--card-bg-secondary);
  border: 1px solid var(--card-border-secondary);
  box-shadow: var(--card-shadow-sm);
}

.card-secondary:hover {
  box-shadow: var(--card-shadow-md);
}

/* 强调卡片 */
.card-accent {
  background-color: var(--card-bg-accent);
  border: 1px solid var(--card-border-accent);
  box-shadow: var(--card-shadow-base);
}

/* 交互式卡片 */
.card-interactive {
  background-color: var(--card-bg-interactive);
  border: 1px solid var(--card-border-primary);
  box-shadow: var(--card-shadow-sm);
  cursor: pointer;
}

.card-interactive:hover {
  background-color: var(--card-bg-accent);
  box-shadow: var(--card-shadow-hover);
  transform: translateY(-2px);
}

/* 统计卡片 */
.card-stats {
  background-color: var(--card-bg-stats);
  border: 1px solid var(--card-border-primary);
  box-shadow: var(--card-shadow-base);
}

.card-stats:hover {
  box-shadow: var(--card-shadow-md);
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.3);
}

.dark .shadow-glow {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.2);
}
