<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="分类管理"
    description="管理MCP服务分类和层级结构"
  >
    <template #actions>
      <ActionButton variant="primary" icon="➕" @click="handleAddCategory">
        新增分类
      </ActionButton>
      <ActionButton variant="secondary" icon="📤">
        导出分类
      </ActionButton>
    </template>
  </PageHeader>

  <!-- 搜索区域 -->
  <SearchArea>
    <template #filters>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类名称</label>
        <input
          v-model="searchForm.name"
          type="text"
          placeholder="请输入分类名称"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
    </template>

    <template #searchActions>
      <ActionButton variant="search" icon="🔍" @click="handleSearch">
        搜索
      </ActionButton>
      <ActionButton variant="reset" icon="🔄" @click="handleReset">
        重置
      </ActionButton>
    </template>
  </SearchArea>

  <!-- 分类列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <TableHeader title="分类列表" subtitle="层级分类结构管理">
      <template #batchActions>
        <ActionButton variant="batch" size="sm" icon="📁">
          展开全部
        </ActionButton>
        <ActionButton variant="batch" size="sm" icon="📂">
          收起全部
        </ActionButton>
      </template>
    </TableHeader>

    <div class="p-6">
      <div class="space-y-4">
        <!-- 编程语言 -->
        <div class="border border-gray-200 dark:border-gray-600 rounded-lg">
          <div class="p-4 bg-gray-50 dark:bg-gray-700 flex items-center justify-between cursor-pointer" @click="toggleCategory('programming')">
            <div class="flex items-center space-x-3">
              <span class="text-lg">{{ expandedCategories.programming ? '📂' : '📁' }}</span>
              <span class="font-medium text-gray-900 dark:text-white">编程语言</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">({{ programmingLanguages.length }})</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">2025-06-24T11:53:00</span>
              <button
                @click.stop="handleViewCategory({ id: 'programming', name: '编程语言', type: 'parent' })"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                title="查看详情"
              >
                👁️
              </button>
              <button
                @click.stop="handleEditCategory({ id: 'programming', name: '编程语言', type: 'parent' })"
                class="text-green-600 hover:text-green-800 dark:text-green-400"
                title="编辑"
              >
                ✏️
              </button>
              <button
                @click.stop="handleDeleteCategory({ id: 'programming', name: '编程语言', type: 'parent' })"
                class="text-red-600 hover:text-red-800 dark:text-red-400"
                title="删除"
              >
                🗑️
              </button>
            </div>
          </div>
          <div v-if="expandedCategories.programming" class="border-t border-gray-200 dark:border-gray-600">
            <div v-for="lang in programmingLanguages" :key="lang.id" class="p-4 border-b border-gray-100 dark:border-gray-700 last:border-b-0 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div class="flex items-center space-x-3">
                <span class="text-gray-400">└─</span>
                <span class="text-gray-900 dark:text-white">{{ lang.name }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">ID: {{ lang.id }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ lang.createdAt }}</span>
                <button
                  @click="handleViewCategory(lang)"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                  title="查看详情"
                >
                  👁️
                </button>
                <button
                  @click="handleEditCategory(lang)"
                  class="text-green-600 hover:text-green-800 dark:text-green-400"
                  title="编辑"
                >
                  ✏️
                </button>
                <button
                  @click="handleDeleteCategory(lang)"
                  class="text-red-600 hover:text-red-800 dark:text-red-400"
                  title="删除"
                >
                  🗑️
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 开发工具 -->
        <div class="border border-gray-200 dark:border-gray-600 rounded-lg">
          <div class="p-4 bg-gray-50 dark:bg-gray-700 flex items-center justify-between cursor-pointer" @click="toggleCategory('tools')">
            <div class="flex items-center space-x-3">
              <span class="text-lg">{{ expandedCategories.tools ? '📂' : '📁' }}</span>
              <span class="font-medium text-gray-900 dark:text-white">开发工具</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">({{ developmentTools.length }})</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">2025-06-24T11:53:00</span>
              <button
                @click.stop="handleViewCategory({ id: 'tools', name: '开发工具', type: 'parent' })"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                title="查看详情"
              >
                👁️
              </button>
              <button
                @click.stop="handleEditCategory({ id: 'tools', name: '开发工具', type: 'parent' })"
                class="text-green-600 hover:text-green-800 dark:text-green-400"
                title="编辑"
              >
                ✏️
              </button>
              <button
                @click.stop="handleDeleteCategory({ id: 'tools', name: '开发工具', type: 'parent' })"
                class="text-red-600 hover:text-red-800 dark:text-red-400"
                title="删除"
              >
                🗑️
              </button>
            </div>
          </div>
          <div v-if="expandedCategories.tools" class="border-t border-gray-200 dark:border-gray-600">
            <div v-for="tool in developmentTools" :key="tool.id" class="p-4 border-b border-gray-100 dark:border-gray-700 last:border-b-0 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div class="flex items-center space-x-3">
                <span class="text-gray-400">└─</span>
                <span class="text-gray-900 dark:text-white">{{ tool.name }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">ID: {{ tool.id }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ tool.createdAt }}</span>
                <button
                  @click="handleViewCategory(tool)"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                  title="查看详情"
                >
                  👁️
                </button>
                <button
                  @click="handleEditCategory(tool)"
                  class="text-green-600 hover:text-green-800 dark:text-green-400"
                  title="编辑"
                >
                  ✏️
                </button>
                <button
                  @click="handleDeleteCategory(tool)"
                  class="text-red-600 hover:text-red-800 dark:text-red-400"
                  title="删除"
                >
                  🗑️
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 测试类别 -->
        <div class="border border-gray-200 dark:border-gray-600 rounded-lg">
          <div class="p-4 bg-gray-50 dark:bg-gray-700 flex items-center justify-between cursor-pointer" @click="toggleCategory('testing')">
            <div class="flex items-center space-x-3">
              <span class="text-lg">{{ expandedCategories.testing ? '📂' : '📁' }}</span>
              <span class="font-medium text-gray-900 dark:text-white">测试类别</span>
              <span class="text-sm text-gray-500 dark:text-gray-400">({{ testingTools.length }})</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">2025-06-24T11:53:00</span>
              <button
                @click.stop="handleViewCategory({ id: 'testing', name: '测试类别', type: 'parent' })"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                title="查看详情"
              >
                👁️
              </button>
              <button
                @click.stop="handleEditCategory({ id: 'testing', name: '测试类别', type: 'parent' })"
                class="text-green-600 hover:text-green-800 dark:text-green-400"
                title="编辑"
              >
                ✏️
              </button>
              <button
                @click.stop="handleDeleteCategory({ id: 'testing', name: '测试类别', type: 'parent' })"
                class="text-red-600 hover:text-red-800 dark:text-red-400"
                title="删除"
              >
                🗑️
              </button>
            </div>
          </div>
          <div v-if="expandedCategories.testing" class="border-t border-gray-200 dark:border-gray-600">
            <div v-for="test in testingTools" :key="test.id" class="p-4 border-b border-gray-100 dark:border-gray-700 last:border-b-0 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <div class="flex items-center space-x-3">
                <span class="text-gray-400">└─</span>
                <span class="text-gray-900 dark:text-white">{{ test.name }}</span>
                <span class="text-sm text-gray-500 dark:text-gray-400">ID: {{ test.id }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500 dark:text-gray-400">{{ test.createdAt }}</span>
                <button
                  @click="handleViewCategory(test)"
                  class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                  title="查看详情"
                >
                  👁️
                </button>
                <button
                  @click="handleEditCategory(test)"
                  class="text-green-600 hover:text-green-800 dark:text-green-400"
                  title="编辑"
                >
                  ✏️
                </button>
                <button
                  @click="handleDeleteCategory(test)"
                  class="text-red-600 hover:text-red-800 dark:text-red-400"
                  title="删除"
                >
                  🗑️
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 详情弹窗 -->
  <DetailModal
    v-model:visible="modalVisible"
    :title="modalTitle"
    :mode="modalMode"
    :data="modalData"
    :validation-rules="validationRules"
    size="md"
    @save="handleSaveCategory"
    @cancel="handleCloseModal"
  >
    <CategoryDetail
      v-model="modalData"
      :mode="modalMode"
      :errors="modalErrors"
    />
  </DetailModal>

  <!-- 确认删除弹窗 -->
  <ConfirmModal
    :show="showConfirmModal"
    :title="confirmModalData.title"
    :message="confirmModalData.message"
    :confirm-text="confirmModalData.confirmText"
    :cancel-text="confirmModalData.cancelText"
    @confirm="confirmDelete"
    @close="closeConfirmModal"
  />
</div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'
import TableHeader from '@/components/TableHeader.vue'
import DetailModal from '@/components/ui/DetailModal.vue'
import CategoryDetail from '@/components/details/CategoryDetail.vue'
import ConfirmModal from '@/components/ConfirmModal.vue'
import { getValidationRules } from '@/config/detailTemplates'
import { useCrudOperations } from '@/composables/useCrudOperations'
import { useToast } from '@/composables/useToast'
import { getTemplateConfig } from '@/config/detailTemplates'

// 初始化CRUD操作和Toast
const toast = useToast()
const crud = useCrudOperations('categories')

// 获取分类详情模板配置
const categoryTemplate = getTemplateConfig('categories')

const searchForm = reactive({
  name: ''
})

const expandedCategories = reactive({
  programming: true,
  tools: false,
  testing: false
})

const programmingLanguages = ref([
  { id: 1, name: '编程语言', createdAt: '2025-06-24T11:53:00' },
  { id: 23, name: 'Vue.js', createdAt: '2025-06-24T11:53:01' },
  { id: 22, name: 'React', createdAt: '2025-06-24T11:53:01' },
  { id: 21, name: 'Spring Boot', createdAt: '2025-06-24T11:53:01' },
  { id: 24, name: 'Next.js', createdAt: '2025-06-24T11:53:01' },
  { id: 25, name: 'Django', createdAt: '2025-06-24T11:53:01' },
  { id: 26, name: 'Flask', createdAt: '2025-06-24T11:53:01' },
  { id: 27, name: 'FastAPI', createdAt: '2025-06-24T11:53:01' },
  { id: 28, name: 'Express.js', createdAt: '2025-06-24T11:53:01' },
  { id: 29, name: 'Flutter', createdAt: '2025-06-24T11:53:01' },
  { id: 30, name: 'SwiftUI', createdAt: '2025-06-24T11:53:01' }
])

const developmentTools = ref([
  { id: 31, name: 'Git', createdAt: '2025-06-24T11:53:01' },
  { id: 32, name: 'Docker', createdAt: '2025-06-24T11:53:01' },
  { id: 33, name: 'Cursor', createdAt: '2025-06-24T11:53:01' },
  { id: 34, name: 'VS Code', createdAt: '2025-06-24T11:53:01' },
  { id: 35, name: 'IntelliJ IDEA', createdAt: '2025-06-24T11:53:01' }
])

const testingTools = ref([
  { id: 41, name: '代码检查', createdAt: '2025-06-24T11:53:01' },
  { id: 42, name: '测试检查', createdAt: '2025-06-24T11:53:01' },
  { id: 43, name: '性能优化', createdAt: '2025-06-24T11:53:01' },
  { id: 44, name: '安全检查', createdAt: '2025-06-24T11:53:01' },
  { id: 45, name: '文档检查', createdAt: '2025-06-24T11:53:01' }
])

const toggleCategory = (category) => {
  expandedCategories[category] = !expandedCategories[category]
}

// 加载分类数据
const loadCategories = async () => {
  try {
    await crud.fetchList({
      name: searchForm.name
    })

    // 更新本地分类数据（这里可以根据实际API结构调整）
    const categories = crud.data.value || []
    updateLocalCategories(categories)
  } catch (error) {
    console.error('加载分类列表失败:', error)
  }
}

// 更新本地分类数据结构
const updateLocalCategories = (categories) => {
  // 这里可以根据API返回的数据结构更新本地的分类树
  // 暂时保持现有的静态数据结构，实际项目中应该根据API数据动态构建
  console.log('更新分类数据:', categories)
}

const handleSearch = async () => {
  await loadCategories()
}

const handleReset = async () => {
  searchForm.name = ''
  await loadCategories()
}

// 弹窗相关状态
const modalVisible = ref(false)
const modalMode = ref('create')
const modalData = ref({})
const modalErrors = ref({})

// 确认弹窗状态
const showConfirmModal = ref(false)
const confirmModalData = ref({})

// 初始化数据
onMounted(async () => {
  await loadCategories()
})

// 获取验证规则
const validationRules = getValidationRules('categories')

// 计算弹窗标题
const modalTitle = computed(() => {
  switch (modalMode.value) {
    case 'create': return '新建分类'
    case 'edit': return '编辑分类'
    case 'view': return '分类详情'
    default: return '分类'
  }
})

const handleAddCategory = () => {
  modalMode.value = 'create'
  modalData.value = {
    name: '',
    parent: '',
    description: '',
    sort: 0,
    enabled: true
  }
  modalVisible.value = true
}

const handleViewCategory = async (category) => {
  modalMode.value = 'view'
  modalVisible.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(category.id)
    modalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取分类详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    modalData.value = { ...category }
  }
}

const handleEditCategory = async (category) => {
  modalMode.value = 'edit'
  modalVisible.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(category.id)
    modalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取分类详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    modalData.value = { ...category }
  }
}

const handleDeleteCategory = (category) => {
  // 使用ConfirmModal确认删除
  const warningMessage = category.type === 'parent'
    ? '注意：删除父分类将同时删除所有子分类。此操作不可撤销，请谨慎操作。'
    : '此操作不可撤销，请谨慎操作。'

  confirmModalData.value = {
    title: '确认删除分类',
    message: `确定要删除分类 "${category.name}" 吗？\n\n${warningMessage}`,
    confirmText: '删除',
    cancelText: '取消',
    category: category
  }
  showConfirmModal.value = true
}

// 确认删除分类
const confirmDelete = async () => {
  try {
    const category = confirmModalData.value.category
    await crud.remove(category.id)
    showConfirmModal.value = false

    // 刷新分类列表
    await loadCategories()

    toast.success(`分类 "${category.name}" 删除成功`)
  } catch (error) {
    console.error('删除分类失败:', error)
    toast.error('删除分类失败：' + (error.message || '未知错误'))
  }
}

// 关闭确认弹窗
const closeConfirmModal = () => {
  showConfirmModal.value = false
  confirmModalData.value = {}
}

const handleSaveCategory = async (data) => {
  try {
    if (modalMode.value === 'create') {
      await crud.create(data)
      toast.success('分类创建成功')
    } else if (modalMode.value === 'edit') {
      await crud.update(modalData.value.id, data)
      toast.success('分类更新成功')
    }

    modalVisible.value = false

    // 刷新分类列表
    await loadCategories()
  } catch (error) {
    console.error('保存分类失败:', error)
    toast.error('保存分类失败：' + (error.message || '未知错误'))
  }
}

const handleCloseModal = () => {
  modalVisible.value = false
}
</script>
