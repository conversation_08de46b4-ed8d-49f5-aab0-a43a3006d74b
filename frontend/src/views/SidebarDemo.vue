<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <PageHeader
      title="侧边栏功能演示"
      description="展示新的侧边栏收起和展开功能"
    />

    <!-- 功能说明 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">功能特性</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="space-y-4">
          <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <span class="text-blue-600 dark:text-blue-400">📱</span>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">侧边栏收起/展开</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">点击侧边栏头部的收起按钮，可以收起或展开整个侧边栏</p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <span class="text-green-600 dark:text-green-400">📂</span>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">二级菜单展开/收起</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">每个菜单组都支持独立的展开和收起操作</p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <span class="text-purple-600 dark:text-purple-400">💡</span>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">智能工具提示</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">侧边栏收起时，鼠标悬停显示菜单项名称</p>
            </div>
          </div>
        </div>

        <div class="space-y-4">
          <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
              <span class="text-yellow-600 dark:text-yellow-400">🎨</span>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">主题适配</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">完美支持明暗主题切换，保持一致的视觉体验</p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
              <span class="text-red-600 dark:text-red-400">📱</span>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">响应式设计</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">在移动设备上自动适配，提供最佳用户体验</p>
            </div>
          </div>

          <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
              <span class="text-indigo-600 dark:text-indigo-400">⚡</span>
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">平滑动画</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">所有交互都有平滑的过渡动画效果</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作指南 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">操作指南</h2>
      
      <div class="space-y-4">
        <div class="flex items-center space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <span class="text-blue-600 dark:text-blue-400 text-lg">1️⃣</span>
          <span class="text-gray-900 dark:text-white">点击侧边栏头部的 "←" 或 "→" 按钮来收起或展开侧边栏</span>
        </div>
        
        <div class="flex items-center space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <span class="text-green-600 dark:text-green-400 text-lg">2️⃣</span>
          <span class="text-gray-900 dark:text-white">点击菜单组标题（如"主要功能"、"MCP服务"等）来展开或收起该组</span>
        </div>
        
        <div class="flex items-center space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <span class="text-purple-600 dark:text-purple-400 text-lg">3️⃣</span>
          <span class="text-gray-900 dark:text-white">在侧边栏收起状态下，将鼠标悬停在菜单项上查看工具提示</span>
        </div>
        
        <div class="flex items-center space-x-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <span class="text-yellow-600 dark:text-yellow-400 text-lg">4️⃣</span>
          <span class="text-gray-900 dark:text-white">使用右上角的主题切换按钮来体验明暗主题下的侧边栏效果</span>
        </div>
      </div>
    </div>

    <!-- 技术信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <h2 class="text-lg font-semibold mb-4 text-gray-900 dark:text-white">技术实现</h2>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <h3 class="font-medium mb-2 text-gray-900 dark:text-white">核心技术</h3>
          <ul class="space-y-1 text-sm text-gray-600 dark:text-gray-400">
            <li>• Vue 3 Composition API</li>
            <li>• Provide/Inject 状态管理</li>
            <li>• Tailwind CSS 样式系统</li>
            <li>• CSS 过渡动画</li>
            <li>• 响应式设计</li>
          </ul>
        </div>
        
        <div>
          <h3 class="font-medium mb-2 text-gray-900 dark:text-white">组件架构</h3>
          <ul class="space-y-1 text-sm text-gray-600 dark:text-gray-400">
            <li>• SidebarProvider (状态提供者)</li>
            <li>• Sidebar (主容器)</li>
            <li>• SidebarGroup (菜单组)</li>
            <li>• SidebarMenu (菜单)</li>
            <li>• SidebarMenuButton (菜单按钮)</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import PageHeader from '@/components/PageHeader.vue'
</script>
