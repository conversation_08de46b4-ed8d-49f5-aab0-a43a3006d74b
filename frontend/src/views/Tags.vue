<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="标签管理"
    description="管理MCP服务标签和分类标识"
  >
    <template #actions>
      <ActionButton variant="primary" icon="➕" @click="handleAddTag">
        新建标签
      </ActionButton>
      <ActionButton variant="secondary" icon="📤">
        导出标签
      </ActionButton>
    </template>
  </PageHeader>

  <!-- 搜索区域 -->
  <SearchArea>
    <template #filters>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标签名称</label>
        <input
          v-model="searchForm.name"
          type="text"
          placeholder="请输入标签名称"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
    </template>

    <template #searchActions>
      <ActionButton variant="search" icon="🔍" @click="handleSearch">
        搜索
      </ActionButton>
      <ActionButton variant="reset" icon="🔄" @click="handleReset">
        重置
      </ActionButton>
    </template>
  </SearchArea>

  <!-- 标签列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <TableHeader title="MCP标签列表" subtitle="服务标签和分类标识">
      <template #batchActions>
        <ActionButton variant="danger" size="sm" icon="🗑️" @click="handleBatchDelete">
          批量删除
        </ActionButton>
        <ActionButton variant="batch" size="sm" icon="🏷️" @click="handleBatchEdit">
          批量编辑
        </ActionButton>
      </template>
    </TableHeader>

    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              <input
                type="checkbox"
                v-model="selectAll"
                @change="toggleSelectAll"
                class="rounded border-gray-300 dark:border-gray-600"
              />
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">标签名称</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">创建时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr v-for="tag in tags" :key="tag.id" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <td class="px-6 py-4">
              <input
                type="checkbox"
                :checked="selectedTags.includes(tag.id)"
                @change="toggleTagSelection(tag.id)"
                class="rounded border-gray-300 dark:border-gray-600"
              />
            </td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ tag.id }}</td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ tag.name }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ tag.createdAt }}</td>
            <td class="px-6 py-4 text-sm space-x-2">
              <button
                @click="handleViewTag(tag)"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                title="查看详情"
              >
                👁️
              </button>
              <button
                @click="handleEditTag(tag)"
                class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                title="编辑"
              >
                ✏️
              </button>
              <button
                @click="handleDeleteTag(tag)"
                class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                title="删除"
              >
                🗑️
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
      <div class="text-sm text-gray-500 dark:text-gray-400">
        Total {{ totalCount }} / {{ pagination.pageSize }}/page
        <span v-if="selectedTags.length > 0" class="ml-4 text-blue-600 dark:text-blue-400">
          已选择 {{ selectedTags.length }} 项
        </span>
      </div>
      <div class="flex items-center space-x-2">
        <button class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          &lt;
        </button>
        <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">1</button>
        <button class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">2</button>
        <button class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">3</button>
        <span class="text-gray-500">...</span>
        <button class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">8</button>
        <button class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          &gt;
        </button>
        <span class="text-sm text-gray-500 dark:text-gray-400 ml-4">Go to</span>
        <input type="number" class="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700" />
      </div>
    </div>
  </div>

  <!-- 标签详情弹窗 -->
  <DetailModal
    v-if="tagTemplate"
    :visible="showDetailModal"
    :title="detailModalMode === 'create' ? '新建标签' : detailModalMode === 'edit' ? '编辑标签' : '标签详情'"
    :icon="tagTemplate.icon"
    :mode="detailModalMode"
    :data="detailModalData"
    :template="tagTemplate"
    @update:visible="showDetailModal = $event"
    @save="saveTagDetail"
    @close="closeDetailModal"
  >
    <TagDetail
      v-model="detailModalData"
      :mode="detailModalMode"
      :template="tagTemplate"
    />
  </DetailModal>

  <!-- 确认删除弹窗 -->
  <ConfirmModal
    :show="showConfirmModal"
    :title="confirmModalData.title"
    :message="confirmModalData.message"
    :confirm-text="confirmModalData.confirmText"
    :cancel-text="confirmModalData.cancelText"
    @confirm="confirmDelete"
    @close="closeConfirmModal"
  />

  <!-- 批量操作弹窗 -->
  <BatchOperationModal
    :show="batchOp.showBatchModal.value"
    :operation="batchOp.batchOperation.value"
    :selected-items="batchOp.selectedItems.value"
    v-bind="batchOp.batchModalProps.value"
    @close="batchOp.closeBatchModal"
    @confirm="batchOp.batchModalProps.value.onConfirm"
  />
</div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'
import TableHeader from '@/components/TableHeader.vue'
import DetailModal from '@/components/ui/DetailModal.vue'
import TagDetail from '@/components/details/TagDetail.vue'
import ConfirmModal from '@/components/ConfirmModal.vue'
import BatchOperationModal from '@/components/ui/BatchOperationModal.vue'
import { useCrudOperations } from '@/composables/useCrudOperations'
import { useToast } from '@/composables/useToast'
import { useBatchOperation } from '@/composables/useBatchOperation'
import { getTemplateConfig } from '@/config/detailTemplates'

// 初始化路由和CRUD操作
const route = useRoute()
const router = useRouter()
const toast = useToast()
const crud = useCrudOperations('tags')
const batchOp = useBatchOperation()

// 获取标签详情模板配置
const tagTemplate = getTemplateConfig('tags')

const searchForm = reactive({
  name: ''
})

// 标签选择状态
const selectedTags = ref([])
const selectAll = ref(false)

// 弹窗状态
const showDetailModal = ref(false)
const showConfirmModal = ref(false)
const detailModalMode = ref('view')
const detailModalData = ref({})
const confirmModalData = ref({})

// 分页状态
const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 初始化数据
onMounted(async () => {
  await loadTags()

  // 处理URL参数，如果有openDetail参数，打开对应的详情弹窗
  if (route.query.openDetail) {
    const tagId = route.query.openDetail
    const mode = route.query.mode || 'view'

    // 等待数据加载完成后再打开弹窗
    setTimeout(() => {
      const tag = tags.value.find(t => t.id == tagId)
      if (tag) {
        if (mode === 'edit') {
          handleEditTag(tag)
        } else {
          handleViewTag(tag)
        }

        // 清除URL参数，避免刷新页面时重复打开
        router.replace({ query: { ...route.query, openDetail: undefined, mode: undefined } })
      }
    }, 100)
  }
})

// 监听路由变化，处理新的openDetail参数
watch(() => route.query.openDetail, (newId) => {
  if (newId && tags.value.length > 0) {
    const tag = tags.value.find(t => t.id == newId)
    if (tag) {
      const mode = route.query.mode || 'view'
      if (mode === 'edit') {
        handleEditTag(tag)
      } else {
        handleViewTag(tag)
      }
    }
  }
})

// 计算属性
const tags = computed(() => crud.data.value || [])
const totalCount = computed(() => crud.pagination.total || 0)

// 加载标签数据
const loadTags = async () => {
  try {
    await crud.fetchList({
      name: searchForm.name,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    // 更新分页信息
    pagination.currentPage = crud.pagination.currentPage
  } catch (error) {
    console.error('加载标签列表失败:', error)
  }
}

// 搜索和重置
const handleSearch = async () => {
  pagination.currentPage = 1
  await loadTags()
}

const handleReset = async () => {
  searchForm.name = ''
  pagination.currentPage = 1
  await loadTags()
}

// 标签CRUD操作
const handleAddTag = () => {
  detailModalData.value = {
    name: '',
    description: '',
    color: '#3B82F6',
    enabled: true
  }
  detailModalMode.value = 'create'
  showDetailModal.value = true
}

const handleViewTag = async (tag) => {
  detailModalMode.value = 'view'
  showDetailModal.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(tag.id)
    detailModalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取标签详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    detailModalData.value = { ...tag }
  }
}

const handleEditTag = async (tag) => {
  detailModalMode.value = 'edit'
  showDetailModal.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(tag.id)
    detailModalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取标签详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    detailModalData.value = { ...tag }
  }
}

const handleDeleteTag = (tag) => {
  confirmModalData.value = {
    title: '确认删除标签',
    message: `确定要删除标签 "${tag.name}" 吗？\n\n此操作不可撤销，请谨慎操作。`,
    confirmText: '删除',
    cancelText: '取消',
    tag: tag
  }
  showConfirmModal.value = true
}

// 确认删除标签
const confirmDelete = async () => {
  try {
    const tag = confirmModalData.value.tag
    await crud.remove(tag.id)
    showConfirmModal.value = false

    // 刷新列表
    await loadTags()

    // 清除选择状态
    selectedTags.value = selectedTags.value.filter(id => id !== tag.id)

    toast.success(`标签 "${tag.name}" 删除成功`)
  } catch (error) {
    console.error('删除标签失败:', error)
    toast.error('删除标签失败：' + (error.message || '未知错误'))
  }
}

// 关闭确认弹窗
const closeConfirmModal = () => {
  showConfirmModal.value = false
  confirmModalData.value = {}
}

// 保存标签详情
const saveTagDetail = async (data) => {
  try {
    if (detailModalMode.value === 'create') {
      await crud.create(data)
      toast.success('标签创建成功')
    } else if (detailModalMode.value === 'edit') {
      await crud.update(detailModalData.value.id, data)
      toast.success('标签更新成功')
    }

    showDetailModal.value = false

    // 刷新列表
    await loadTags()
  } catch (error) {
    console.error('保存标签失败:', error)
    toast.error('保存标签失败：' + (error.message || '未知错误'))
  }
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  detailModalData.value = {}
}

// 批量选择功能
const toggleSelectAll = () => {
  if (selectAll.value) {
    selectedTags.value = tags.value.map(tag => tag.id)
  } else {
    selectedTags.value = []
  }
}

const toggleTagSelection = (tagId) => {
  const index = selectedTags.value.indexOf(tagId)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tagId)
  }

  // 更新全选状态
  selectAll.value = selectedTags.value.length === tags.value.length
}

// 批量操作
const handleBatchDelete = async () => {
  if (selectedTags.value.length === 0) {
    toast.warning('请先选择要删除的标签')
    return
  }

  try {
    const selectedTagItems = tags.value.filter(tag => selectedTags.value.includes(tag.id))

    await batchOp.confirmBatchDelete(
      selectedTagItems,
      (tagId) => crud.remove(tagId),
      {
        itemName: '标签',
        warningMessage: '此操作不可撤销，请谨慎操作。'
      }
    )

    // 清除选择状态
    selectedTags.value = []
    selectAll.value = false

    // 刷新列表
    await loadTags()

  } catch (error) {
    console.error('批量删除失败:', error)
  }
}

const handleBatchEdit = async () => {
  if (selectedTags.value.length === 0) {
    toast.warning('请先选择要编辑的标签')
    return
  }

  try {
    const selectedTagItems = tags.value.filter(tag => selectedTags.value.includes(tag.id))

    // 这里可以打开批量编辑弹窗，暂时使用简单的状态切换示例
    const editData = { enabled: true } // 示例：批量启用

    await batchOp.confirmBatchEdit(
      selectedTagItems,
      (tagId, data) => crud.update(tagId, data),
      {
        itemName: '标签',
        editData: editData
      }
    )

    // 清除选择状态
    selectedTags.value = []
    selectAll.value = false

    // 刷新列表
    await loadTags()

  } catch (error) {
    console.error('批量编辑失败:', error)
  }
}
</script>
