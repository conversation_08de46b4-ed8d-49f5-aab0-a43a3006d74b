<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <PageHeader
      title="内容管理"
      description="管理系统内容和规范文档"
    >
      <template #actions>
        <ActionButton variant="primary" icon="➕" @click="handleAddContent">
          新建内容
        </ActionButton>
        <ActionButton variant="secondary" icon="📤">
          导出文档
        </ActionButton>
      </template>
    </PageHeader>

    <!-- 内容统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
        <div class="flex items-center">
          <span class="text-2xl mr-3">📄</span>
          <div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">总文档数</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">156</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
        <div class="flex items-center">
          <span class="text-2xl mr-3">📝</span>
          <div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">规范文档</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">42</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
        <div class="flex items-center">
          <span class="text-2xl mr-3">🔄</span>
          <div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">待审核</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">8</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
        <div class="flex items-center">
          <span class="text-2xl mr-3">✅</span>
          <div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">已发布</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">106</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <SearchArea>
      <template #filters>
        <div class="flex-1 min-w-0">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">搜索内容</label>
          <input
            v-model="searchForm.keyword"
            type="text"
            placeholder="搜索内容..."
            class="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500"
          >
        </div>
        <div class="flex-1 min-w-0">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">内容类型</label>
          <select v-model="searchForm.type" class="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500">
            <option value="">所有类型</option>
            <option value="规范文档">规范文档</option>
            <option value="操作手册">操作手册</option>
            <option value="技术文档">技术文档</option>
            <option value="培训材料">培训材料</option>
          </select>
        </div>
        <div class="flex-1 min-w-0">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态</label>
          <select v-model="searchForm.status" class="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500">
            <option value="">所有状态</option>
            <option value="草稿">草稿</option>
            <option value="待审核">待审核</option>
            <option value="已发布">已发布</option>
            <option value="已归档">已归档</option>
          </select>
        </div>
      </template>

      <template #searchActions>
        <ActionButton variant="search" icon="🔍" @click="handleSearch">
          搜索
        </ActionButton>
        <ActionButton variant="reset" icon="🔄" @click="handleReset">
          重置
        </ActionButton>
      </template>
    </SearchArea>

    <!-- 内容列表 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="text-left p-4 text-gray-700 dark:text-gray-300">标题</th>
              <th class="text-left p-4 text-gray-700 dark:text-gray-300">类型</th>
              <th class="text-left p-4 text-gray-700 dark:text-gray-300">状态</th>
              <th class="text-left p-4 text-gray-700 dark:text-gray-300">作者</th>
              <th class="text-left p-4 text-gray-700 dark:text-gray-300">更新时间</th>
              <th class="text-left p-4 text-gray-700 dark:text-gray-300">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="item in currentContent" :key="item.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="p-4">
                <div>
                  <p class="text-gray-900 dark:text-white font-medium">{{ item.title }}</p>
                  <p class="text-gray-600 dark:text-gray-400 text-sm">{{ item.description }}</p>
                </div>
              </td>
              <td class="p-4">
                <span :class="getTypeColor(item.type)" class="text-white px-2 py-1 rounded text-sm">{{ item.type }}</span>
              </td>
              <td class="p-4">
                <div class="relative">
                  <select
                    :value="item.status"
                    @change="handleChangeStatus(item, $event.target.value)"
                    :class="[
                      'text-white px-2 py-1 rounded text-sm border-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500',
                      getStatusColor(item.status)
                    ]"
                  >
                    <option value="草稿">草稿</option>
                    <option value="待审核">待审核</option>
                    <option value="已发布">已发布</option>
                    <option value="已归档">已归档</option>
                  </select>
                </div>
              </td>
              <td class="p-4 text-gray-600 dark:text-gray-300">{{ item.author }}</td>
              <td class="p-4 text-gray-600 dark:text-gray-300">{{ item.updatedAt }}</td>
              <td class="p-4">
                <div class="flex space-x-2">
                  <button
                    @click="handleViewContent(item)"
                    class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
                  >
                    查看
                  </button>
                  <button
                    @click="handleEditContent(item)"
                    class="text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300"
                  >
                    编辑
                  </button>
                  <button
                    @click="handleDeleteContent(item)"
                    class="text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"
                  >
                    删除
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页组件 -->
      <Pagination
        :total="crud.pagination.total"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- 内容详情弹窗 -->
    <DetailModal
      v-if="contentTemplate"
      :visible="showDetailModal"
      :title="'内容详情'"
      :icon="contentTemplate.icon"
      :mode="'view'"
      :data="detailModalData"
      @update:visible="showDetailModal = $event"
      @close="closeDetailModal"
    >
      <ContentDetail
        v-model="detailModalData"
        :mode="'view'"
        :template="contentTemplate"
      />
    </DetailModal>

    <!-- 确认删除弹窗 -->
    <ConfirmModal
      :show="showConfirmModal"
      :title="confirmModalData.title"
      :message="confirmModalData.message"
      :confirm-text="confirmModalData.confirmText"
      :cancel-text="confirmModalData.cancelText"
      @confirm="confirmDelete"
      @close="closeConfirmModal"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Pagination from '@/components/Pagination.vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'
import DetailModal from '@/components/ui/DetailModal.vue'
import ContentDetail from '@/components/details/ContentDetail.vue'
import ConfirmModal from '@/components/ConfirmModal.vue'
import { useCrudOperations } from '@/composables/useCrudOperations'
import { useToast } from '@/composables/useToast'
import { getTemplateConfig } from '@/config/detailTemplates'

const router = useRouter()
const toast = useToast()
const crud = useCrudOperations('content')

// 获取内容详情模板配置
const contentTemplate = getTemplateConfig('content')

const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

const searchForm = reactive({
  keyword: '',
  type: '',
  status: ''
})

// 弹窗状态
const showDetailModal = ref(false)
const showConfirmModal = ref(false)
const detailModalData = ref({})
const confirmModalData = ref({})

// 初始化数据
onMounted(async () => {
  await loadContent()
})

// 使用CRUD操作的数据
const currentContent = computed(() => crud.data.value || [])

// 加载内容数据
const loadContent = async () => {
  try {
    await crud.fetchList({
      keyword: searchForm.keyword,
      type: searchForm.type,
      status: searchForm.status,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    // 更新分页信息
    pagination.currentPage = crud.pagination.currentPage
  } catch (error) {
    console.error('加载内容列表失败:', error)
  }
}

const handlePageChange = async (page) => {
  pagination.currentPage = page
  await loadContent()
}

const handlePageSizeChange = async (pageSize) => {
  pagination.pageSize = pageSize
  pagination.currentPage = 1
  await loadContent()
}

// 获取类型颜色
const getTypeColor = (type) => {
  const colors = {
    '规范文档': 'bg-blue-600',
    '操作手册': 'bg-purple-600',
    '技术文档': 'bg-green-600',
    '培训材料': 'bg-orange-600'
  }
  return colors[type] || 'bg-gray-600'
}

// 获取状态颜色
const getStatusColor = (status) => {
  const colors = {
    '已发布': 'bg-green-600',
    '待审核': 'bg-yellow-600',
    '草稿': 'bg-gray-600',
    '已归档': 'bg-red-600'
  }
  return colors[status] || 'bg-gray-600'
}

const handleSearch = async () => {
  pagination.currentPage = 1
  await loadContent()
}

const handleReset = async () => {
  searchForm.keyword = ''
  searchForm.type = ''
  searchForm.status = ''
  pagination.currentPage = 1
  await loadContent()
}

const handleAddContent = () => {
  router.push({ name: 'content-create' })
}

const handleViewContent = async (content) => {
  // 使用DetailModal快速查看内容信息
  showDetailModal.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(content.id)
    detailModalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取内容详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    detailModalData.value = { ...content }
  }
}

const handleEditContent = (content) => {
  router.push({
    name: 'content-detail',
    params: { id: content.id },
    query: { mode: 'edit' }
  })
}

const handleDeleteContent = (content) => {
  // 使用ConfirmModal确认删除
  confirmModalData.value = {
    title: '确认删除内容',
    message: `确定要删除内容 "${content.title}" 吗？\n\n删除后将无法恢复，请谨慎操作。`,
    confirmText: '删除',
    cancelText: '取消',
    content: content
  }
  showConfirmModal.value = true
}

// 确认删除内容
const confirmDelete = async () => {
  try {
    const content = confirmModalData.value.content
    await crud.remove(content.id)
    showConfirmModal.value = false

    // 刷新列表
    await loadContent()

    toast.success(`内容 "${content.title}" 删除成功`)
  } catch (error) {
    console.error('删除内容失败:', error)
    toast.error('删除内容失败：' + (error.message || '未知错误'))
  }
}

// 关闭确认弹窗
const closeConfirmModal = () => {
  showConfirmModal.value = false
  confirmModalData.value = {}
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  detailModalData.value = {}
}

// 内容状态切换功能
const handleChangeStatus = async (content, newStatus) => {
  try {
    await crud.update(content.id, { ...content, status: newStatus })

    // 刷新列表
    await loadContent()

    toast.success(`内容状态已更新为 "${newStatus}"`)
  } catch (error) {
    console.error('更新内容状态失败:', error)
    toast.error('更新内容状态失败：' + (error.message || '未知错误'))
  }
}
</script>
