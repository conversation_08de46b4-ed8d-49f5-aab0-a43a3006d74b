<template>
  <div class="auth-callback-container w-full max-w-md mx-auto">
    <!-- 认证回调卡片 -->
    <div class="callback-card backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/30 p-8 text-center">

      <!-- 加载状态 -->
      <div v-if="isProcessing" class="space-y-6">
        <!-- 动态加载图标 -->
        <div class="loading-container mx-auto relative">
          <div class="loading-bg absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full blur-lg opacity-75 animate-pulse"></div>
          <div class="loading-main relative bg-gradient-to-r from-blue-500 to-purple-600 w-16 h-16 rounded-full flex items-center justify-center shadow-lg">
            <svg class="animate-spin h-8 w-8 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </div>

        <div class="space-y-3">
          <h2 class="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
            正在处理登录
          </h2>
          <p class="text-gray-600 dark:text-gray-400">
            请稍候，我们正在验证您的身份...
          </p>
        </div>

        <!-- 进度条 -->
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 overflow-hidden">
          <div class="progress-bar h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
        </div>
      </div>

      <!-- 成功状态 -->
      <div v-else-if="isSuccess" class="space-y-6">
        <!-- 成功图标 -->
        <div class="success-container mx-auto relative">
          <div class="success-bg absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full blur-lg opacity-75 animate-pulse"></div>
          <div class="success-main relative bg-gradient-to-r from-green-500 to-emerald-600 w-16 h-16 rounded-full flex items-center justify-center shadow-lg animate-bounce">
            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
            </svg>
          </div>
        </div>

        <div class="space-y-3">
          <h2 class="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
            登录成功！
          </h2>
          <p class="text-gray-600 dark:text-gray-400">
            正在跳转到主页...
          </p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="errorMessage" class="space-y-6">
        <!-- 错误图标 -->
        <div class="error-container mx-auto relative">
          <div class="error-bg absolute inset-0 bg-gradient-to-r from-red-500 to-pink-600 rounded-full blur-lg opacity-75 animate-pulse"></div>
          <div class="error-main relative bg-gradient-to-r from-red-500 to-pink-600 w-16 h-16 rounded-full flex items-center justify-center shadow-lg">
            <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        </div>

        <div class="space-y-3">
          <h2 class="text-2xl font-bold bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">
            登录失败
          </h2>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            {{ errorMessage }}
          </p>
        </div>

        <!-- 返回按钮 -->
        <button
          @click="goToLogin"
          class="return-button inline-flex items-center px-6 py-3 border border-transparent text-sm font-semibold rounded-xl text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回登录页面
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

const isProcessing = ref(true)
const isSuccess = ref(false)
const errorMessage = ref('')

onMounted(() => {
  processAuthCallback()
})

const processAuthCallback = async () => {
  try {
    const urlParams = new URLSearchParams(window.location.search)
    const success = urlParams.get('success')
    const token = urlParams.get('token')
    const refreshToken = urlParams.get('refreshToken')
    const error = urlParams.get('error')

    console.log('OAuth2回调参数:', { success, token: token ? '***' : null, refreshToken: refreshToken ? '***' : null, error })

    if (success === 'true' && token) {
      // 登录成功
      isProcessing.value = false
      isSuccess.value = true

      // 设置令牌
      const decodedToken = decodeURIComponent(token)
      console.log('设置令牌:', decodedToken.substring(0, 50) + '...')
      userStore.setToken(decodedToken)

      if (refreshToken) {
        const decodedRefreshToken = decodeURIComponent(refreshToken)
        localStorage.setItem('refreshToken', decodedRefreshToken)
        console.log('设置刷新令牌')
      }

      // 获取用户信息
      console.log('开始获取用户信息...')
      const fetchSuccess = await userStore.fetchUserInfo()
      console.log('获取用户信息结果:', fetchSuccess)

      // 如果是在弹窗中，向父窗口发送成功消息
      if (window.opener) {
        window.opener.postMessage({
          type: 'OAUTH_SUCCESS',
          token: decodeURIComponent(token),
          refreshToken: refreshToken ? decodeURIComponent(refreshToken) : null
        }, window.location.origin)
        window.close()
        return
      }

      // 延迟跳转到首页
      setTimeout(() => {
        router.push('/')
      }, 2000)

    } else if (success === 'false' || error) {
      // 登录失败
      isProcessing.value = false
      errorMessage.value = error ? decodeURIComponent(error) : '登录过程中发生未知错误'

      // 如果是在弹窗中，向父窗口发送错误消息
      if (window.opener) {
        window.opener.postMessage({
          type: 'OAUTH_ERROR',
          error: errorMessage.value
        }, window.location.origin)
        window.close()
        return
      }

    } else {
      // 无效的回调参数
      isProcessing.value = false
      errorMessage.value = '无效的认证回调参数'

      if (window.opener) {
        window.opener.postMessage({
          type: 'OAUTH_ERROR',
          error: errorMessage.value
        }, window.location.origin)
        window.close()
        return
      }
    }

  } catch (error) {
    console.error('处理OAuth回调失败:', error)
    isProcessing.value = false
    errorMessage.value = `处理登录回调时发生错误: ${error.message || error}`

    if (window.opener) {
      window.opener.postMessage({
        type: 'OAUTH_ERROR',
        error: errorMessage.value
      }, window.location.origin)
      window.close()
      return
    }
  }
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
/* 认证回调容器样式 */
.auth-callback-container {
  animation: fadeInUp 0.8s ease-out;
}

/* 回调卡片样式 */
.callback-card {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: slideInScale 0.6s ease-out;
}

/* 加载状态样式 */
.loading-container {
  animation: float 2s ease-in-out infinite;
}

.loading-bg {
  animation: pulse 1.5s ease-in-out infinite;
}

.progress-bar {
  width: 0%;
  animation: progressFill 2s ease-in-out infinite;
}

/* 成功状态样式 */
.success-container {
  animation: successPop 0.6s ease-out;
}

.success-main {
  animation: bounce 1s ease-in-out infinite;
}

/* 错误状态样式 */
.error-container {
  animation: errorShake 0.5s ease-in-out;
}

/* 返回按钮样式 */
.return-button {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

.return-button:hover {
  background-position: right center;
  box-shadow:
    0 20px 40px -12px rgba(59, 130, 246, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 动画定义 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

@keyframes progressFill {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

@keyframes successPop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .callback-card {
    margin: 1rem;
    padding: 1.5rem;
  }

  .loading-main,
  .success-main,
  .error-main {
    width: 3rem;
    height: 3rem;
  }

  .return-button {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }
}

/* 深色模式优化 */
.dark .callback-card {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.dark .return-button:hover {
  box-shadow:
    0 20px 40px -12px rgba(59, 130, 246, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}
</style>
