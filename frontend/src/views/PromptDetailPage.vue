<template>
  <DetailPage
    :title="pageTitle"
    :description="pageDescription"
    :mode="mode"
    :data="promptData"
    :tabs="tabs"
    :validation-rules="validationRules"
    :has-markdown-content="true"
    markdown-field="content"
    :enable-auto-save="false"
    :use-simple-editor="true"
    @save="handleSave"
    @cancel="handleCancel"
    @data-change="handleDataChange"
  >
    <!-- 基本信息内容 -->
    <template #basic-content="{ data, errors, mode }">
      <PromptDetail
        v-model="promptData"
        :mode="mode"
        :errors="errors"
        active-tab="basic"
      />
    </template>

    <!-- 使用示例内容 -->
    <template #examples-content="{ data, errors, mode }">
      <PromptDetail
        v-model="promptData"
        :mode="mode"
        :errors="errors"
        active-tab="examples"
      />
    </template>

    <!-- 历史版本内容 -->
    <template #history-content="{ data, mode }">
      <div class="history-section">
        <div v-if="versionHistory.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3 class="empty-title">暂无版本历史</h3>
          <p class="empty-description">当前提示词还没有版本历史记录</p>
        </div>
        
        <div v-else class="version-list">
          <div
            v-for="version in versionHistory"
            :key="version.id"
            class="version-item"
          >
            <div class="version-header">
              <div class="version-info">
                <span class="version-number">v{{ version.version }}</span>
                <span class="version-date">{{ formatDate(version.createdAt) }}</span>
              </div>
              <div class="version-actions">
                <button class="action-btn view-btn" @click="viewVersion(version)">
                  查看
                </button>
                <button class="action-btn restore-btn" @click="restoreVersion(version)">
                  恢复
                </button>
              </div>
            </div>
            <div class="version-changes">
              <p class="change-summary">{{ version.changeLog }}</p>
              <div class="change-author">
                <span>修改人：{{ version.author }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 侧边栏内容 -->
    <template #sidebar="{ data, toc }">
      <div class="sidebar-section">
        <h4 class="sidebar-title">使用统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-value">{{ data.usageCount || 0 }}</span>
            <span class="stat-label">使用次数</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ data.rating || 0 }}</span>
            <span class="stat-label">平均评分</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ data.favoriteCount || 0 }}</span>
            <span class="stat-label">收藏次数</span>
          </div>
        </div>
      </div>
    </template>
  </DetailPage>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import DetailPage from '@/components/ui/DetailPage.vue'
import PromptDetail from '@/components/details/PromptDetail.vue'
import { getTemplateConfig, getValidationRules } from '@/config/detailTemplates'

const route = useRoute()
const router = useRouter()

// 状态
const promptData = ref({
  title: '',
  category: '',
  difficulty: 'intermediate',
  scenarios: [],
  tags: [],
  content: '',
  inputExample: '',
  outputExample: '',
  tips: '',
  usageCount: 0,
  rating: 0,
  favoriteCount: 0,
  lastUsed: ''
})

const versionHistory = ref([])
const loading = ref(false)

// 计算属性
const promptId = computed(() => route.params.id)
const mode = computed(() => {
  if (route.name === 'prompt-create') return 'create'
  if (route.query.mode === 'edit') return 'edit'
  return 'view'
})

const pageTitle = computed(() => {
  if (mode.value === 'create') return '新建提示词'
  if (mode.value === 'edit') return `编辑提示词 - ${promptData.value.title}`
  return `提示词详情 - ${promptData.value.title}`
})

const pageDescription = computed(() => {
  if (mode.value === 'create') return '创建新的AI提示词'
  return '查看和管理AI提示词的详细信息'
})

// 获取模板配置
const templateConfig = getTemplateConfig('prompts')
const tabs = templateConfig.tabs
const validationRules = getValidationRules('prompts', 'basic')

// 方法
const loadPromptData = async () => {
  if (mode.value === 'create') {
    // 新建模式，使用默认数据
    promptData.value = {
      title: '',
      category: '',
      difficulty: 'intermediate',
      scenarios: [],
      tags: [],
      content: '',
      inputExample: '',
      outputExample: '',
      tips: '',
      usageCount: 0,
      rating: 0,
      favoriteCount: 0,
      lastUsed: ''
    }
    return
  }

  // 模拟加载数据
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))

    promptData.value = {
      id: promptId.value,
      title: '代码重构助手',
      category: 'development',
      difficulty: 'intermediate',
      scenarios: ['代码重构', '性能优化', '代码审查'],
      tags: ['JavaScript', 'React', '重构', '优化'],
      content: `# 代码重构助手

你是一个专业的代码重构专家。请帮助我重构以下代码，使其更加清晰、高效和可维护。

## 重构要求：
1. 提高代码可读性
2. 优化性能
3. 遵循最佳实践
4. 保持功能不变

## 代码：
{code}

## 请提供：
1. 重构后的代码
2. 重构说明
3. 性能改进点
4. 最佳实践建议`,
      inputExample: `function processUserData(users) {
  var result = [];
  for (var i = 0; i < users.length; i++) {
    if (users[i].age >= 18) {
      result.push({
        name: users[i].name,
        email: users[i].email,
        isAdult: true
      });
    }
  }
  return result;
}`,
      outputExample: `// 重构后的代码
const processUserData = (users) => {
  return users
    .filter(user => user.age >= 18)
    .map(user => ({
      name: user.name,
      email: user.email,
      isAdult: true
    }));
};

// 重构说明：
// 1. 使用箭头函数和const声明
// 2. 使用filter和map方法替代for循环
// 3. 使用函数式编程风格，更简洁易读
// 4. 避免了手动管理数组索引`,
      tips: `1. 使用现代JavaScript语法（ES6+）
2. 优先使用函数式编程方法
3. 保持函数简洁，单一职责
4. 添加适当的类型注释（TypeScript）
5. 考虑性能影响，避免不必要的计算`,
      usageCount: 156,
      rating: 4.8,
      favoriteCount: 23,
      lastUsed: new Date().toISOString()
    }

    // 加载版本历史
    versionHistory.value = []
  } catch (error) {
    console.error('加载提示词数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSave = async (data) => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))

    console.log('保存提示词数据:', data)

    if (mode.value === 'create') {
      // 创建模式：保存成功后显示提示，但保持在当前页面继续编辑
      console.log('提示词保存成功！')
      // 可以在这里显示成功提示消息
      alert('提示词保存成功！')
    } else {
      // 编辑模式：保存后跳转到查看模式
      router.push({ name: 'prompt-detail', params: { id: promptId.value } })
    }
  } catch (error) {
    console.error('保存失败:', error)
    alert('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  router.push({ name: 'prompts' })
}

const handleDataChange = (data) => {
  promptData.value = data
}

const viewVersion = (version) => {
  console.log('查看版本:', version)
}

const restoreVersion = (version) => {
  console.log('恢复版本:', version)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 监听路由变化，只在必要时重新加载数据
watch([() => route.params.id, () => route.query.mode], ([newId, newMode], [oldId, oldMode]) => {
  // 只有当ID变化或从创建模式切换到其他模式时才重新加载
  if (newId !== oldId || (oldMode === undefined && newMode !== undefined)) {
    loadPromptData()
  }
}, { immediate: true })

onMounted(() => {
  // 初始加载已经通过watch处理，这里不需要重复调用
  // loadPromptData()
})
</script>

<style scoped>
/* 历史版本部分 */
.history-section {
  @apply space-y-4;
}

.empty-state {
  @apply text-center py-12;
}

.empty-icon {
  @apply text-6xl mb-4;
}

.empty-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.empty-description {
  @apply text-gray-600 dark:text-gray-400;
}

.version-list {
  @apply space-y-4;
}

.version-item {
  @apply bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700;
}

.version-header {
  @apply flex items-center justify-between mb-3;
}

.version-info {
  @apply flex items-center gap-3;
}

.version-number {
  @apply font-semibold text-blue-600 dark:text-blue-400;
}

.version-date {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.version-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply px-3 py-1 text-sm rounded transition-colors;
}

.view-btn {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
  @apply hover:bg-blue-200 dark:hover:bg-blue-800;
}

.restore-btn {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
  @apply hover:bg-green-200 dark:hover:bg-green-800;
}

.version-changes {
  @apply space-y-2;
}

.change-summary {
  @apply text-gray-900 dark:text-white;
}

.change-author {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 侧边栏统计 */
.sidebar-section {
  @apply p-4 border-b border-gray-200 dark:border-gray-700;
}

.sidebar-title {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3;
}

.stats-grid {
  @apply grid grid-cols-1 gap-3;
}

.stat-item {
  @apply text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.stat-value {
  @apply block text-lg font-semibold text-gray-900 dark:text-white;
}

.stat-label {
  @apply block text-xs text-gray-500 dark:text-gray-400 mt-1;
}
</style>
