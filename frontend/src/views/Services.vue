<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="服务管理"
    description="管理MCP服务和配置"
  >
    <template #actions>
      <ActionButton variant="primary" icon="➕" @click="handleAddService">
        新增服务
      </ActionButton>
      <ActionButton variant="secondary" icon="📤">
        导出配置
      </ActionButton>
      <ActionButton variant="secondary" icon="📥">
        导入配置
      </ActionButton>
    </template>
  </PageHeader>

  <!-- 搜索筛选区域 -->
  <SearchArea>
    <template #filters>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">服务名称</label>
        <input
          v-model="searchForm.name"
          type="text"
          placeholder="按名称搜索"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
        <select
          v-model="searchForm.category"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="">请选择分类</option>
          <option value="image">图像处理</option>
          <option value="text">文本处理</option>
          <option value="translation">翻译</option>
        </select>
      </div>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标签</label>
        <input
          v-model="searchForm.tags"
          type="text"
          placeholder="请选择标签"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
    </template>

    <template #searchActions>
      <ActionButton variant="search" @click="handleSearch">搜索</ActionButton>
      <ActionButton variant="reset" @click="handleReset">重置</ActionButton>
    </template>
  </SearchArea>

  <!-- 服务列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">服务名称</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">分类</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">标签</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">版本</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">状态</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">简短描述</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">更新时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr v-for="service in services" :key="service.id" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ service.id }}</td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ service.name }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ service.category }}</td>
            <td class="px-6 py-4">
              <span v-for="tag in service.tags" :key="tag" class="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-2 py-1 rounded mr-1">
                {{ tag }}
              </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ service.version }}</td>
            <td class="px-6 py-4">
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                ✅ {{ service.status }}
              </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">{{ service.description }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ service.updatedAt }}</td>
            <td class="px-6 py-4 text-sm space-x-2">
              <button
                @click="handleViewService(service)"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
              >
                查看
              </button>
              <button
                @click="handleEditService(service)"
                class="text-green-600 hover:text-green-800 dark:text-green-400"
              >
                编辑
              </button>
              <button
                @click="handleDeleteService(service)"
                class="text-red-600 hover:text-red-800 dark:text-red-400"
              >
                删除
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页组件 -->
    <Pagination
      :total="crud.pagination.total"
      :current-page="pagination.currentPage"
      :page-size="pagination.pageSize"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />
  </div>

  <!-- 确认删除弹窗 -->
  <ConfirmModal
    :show="showConfirmModal"
    :title="confirmModalData.title"
    :message="confirmModalData.message"
    :confirm-text="confirmModalData.confirmText"
    :cancel-text="confirmModalData.cancelText"
    @confirm="confirmDelete"
    @close="closeConfirmModal"
  />


</div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import Pagination from '@/components/Pagination.vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'
import ConfirmModal from '@/components/ConfirmModal.vue'

import { useCrudOperations } from '@/composables/useCrudOperations'
import { useToast } from '@/composables/useToast'


const router = useRouter()
const toast = useToast()
const crud = useCrudOperations('services')



const searchForm = reactive({
  name: '',
  category: '',
  tags: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 弹窗状态
const showConfirmModal = ref(false)
const confirmModalData = ref({})

const allServices = ref([
  {
    id: 47,
    name: '图像超分辨率服务',
    category: '图像处理 > 图像...',
    tags: ['API'],
    version: 'v2.6.0',
    status: '已发布',
    description: '图像超分辨率',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 48,
    name: '图像去噪服务',
    category: '图像处理 > 图像...',
    tags: ['API'],
    version: 'v1.7.0',
    status: '已发布',
    description: '图像去噪处理',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 49,
    name: '智能图像压缩服务',
    category: '图像处理 > 图像...',
    tags: ['API'],
    version: 'v2.3.0',
    status: '已发布',
    description: '智能图像压缩',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 50,
    name: '批量图像处理服务',
    category: '图像处理 > 图像...',
    tags: ['API'],
    version: 'v1.9.0',
    status: '已发布',
    description: '批量图像处理',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 51,
    name: '文本关键词提取服务',
    category: '文本处理 > 文本...',
    tags: ['API', 'GraphQL'],
    version: 'v2.1.0',
    status: '已发布',
    description: '文本关键词提取',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 52,
    name: '文本相似度计算',
    category: '文本处理 > 文本...',
    tags: ['API', 'GraphQL'],
    version: 'v1.8.0',
    status: '已发布',
    description: '文本相似度分析',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 53,
    name: '智能文本生成服务',
    category: '文本处理 > 文本...',
    tags: ['API', 'GraphQL'],
    version: 'v3.0.0',
    status: '已发布',
    description: '智能文本内容生成',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 54,
    name: '代码生成服务',
    category: '文本处理 > 文本...',
    tags: ['API', 'GraphQL'],
    version: 'v2.4.0',
    status: '已发布',
    description: '智能代码生成',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 55,
    name: '多语言翻译服务',
    category: '文本处理 > 翻译...',
    tags: ['API', 'GraphQL'],
    version: 'v2.8.0',
    status: '已发布',
    description: '多语言智能翻译',
    updatedAt: '2025-07-02T12:07:24'
  },
  {
    id: 56,
    name: '专业术语翻译',
    category: '文本处理 > 翻译...',
    tags: ['API', 'GraphQL'],
    version: 'v1.9.0',
    status: '已发布',
    description: '专业术语翻译',
    updatedAt: '2025-07-02T12:07:24'
  }
])

// 过滤后的服务列表
const filteredServices = computed(() => {
  return allServices.value.filter(service => {
    const nameMatch = !searchForm.name || service.name.toLowerCase().includes(searchForm.name.toLowerCase())
    const categoryMatch = !searchForm.category || service.category.includes(searchForm.category)
    const tagsMatch = !searchForm.tags || service.tags.some(tag => tag.toLowerCase().includes(searchForm.tags.toLowerCase()))
    return nameMatch && categoryMatch && tagsMatch
  })
})

// 当前页显示的服务列表（使用CRUD操作的数据）
const services = computed(() => {
  return crud.data.value || []
})

const handleSearch = async () => {
  pagination.currentPage = 1 // 搜索时重置到第一页
  await loadServices()
}

const handleReset = async () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.currentPage = 1 // 重置时回到第一页
  await loadServices()
}

const handlePageChange = async (page) => {
  pagination.currentPage = page
  await loadServices()
}

const handlePageSizeChange = async (pageSize) => {
  pagination.pageSize = pageSize
  pagination.currentPage = 1
  await loadServices()
}

// 初始化数据
onMounted(async () => {
  await loadServices()
})

// 加载服务列表
const loadServices = async () => {
  try {
    await crud.fetchList({
      name: searchForm.name,
      category: searchForm.category,
      tags: searchForm.tags,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    // 更新分页信息
    pagination.currentPage = crud.pagination.currentPage
    pagination.total = crud.pagination.total
  } catch (error) {
    console.error('加载服务列表失败:', error)
  }
}

const handleAddService = () => {
  router.push({ name: 'service-create' })
}

const handleViewService = (service) => {
  // 跳转到独立详情页面
  router.push({
    name: 'service-detail',
    params: { id: service.id }
  })
}

const handleEditService = (service) => {
  router.push({
    name: 'service-detail',
    params: { id: service.id },
    query: { mode: 'edit' }
  })
}

const handleDeleteService = (service) => {
  // 使用ConfirmModal确认删除
  confirmModalData.value = {
    title: '确认删除服务',
    message: `确定要删除服务 "${service.name}" 吗？\n\n删除后将无法恢复，请谨慎操作。`,
    confirmText: '删除',
    cancelText: '取消',
    service: service
  }
  showConfirmModal.value = true
}

// 确认删除服务
const confirmDelete = async () => {
  try {
    const service = confirmModalData.value.service
    await crud.remove(service.id)
    showConfirmModal.value = false

    // 刷新列表
    await loadServices()

    toast.success(`服务 "${service.name}" 删除成功`)
  } catch (error) {
    console.error('删除服务失败:', error)
    toast.error('删除服务失败：' + (error.message || '未知错误'))
  }
}

// 关闭确认弹窗
const closeConfirmModal = () => {
  showConfirmModal.value = false
  confirmModalData.value = {}
}


</script>
