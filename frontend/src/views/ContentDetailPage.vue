<template>
  <DetailPage
    :title="pageTitle"
    :description="pageDescription"
    :mode="mode"
    :data="contentData"
    :tabs="tabs"
    :validation-rules="validationRules"
    :has-markdown-content="true"
    markdown-field="content"
    :enable-auto-save="false"
    :use-simple-editor="true"
    @save="handleSave"
    @cancel="handleCancel"
    @data-change="handleDataChange"
  >
    <!-- 基本信息内容 -->
    <template #basic-content="{ data, errors, mode }">
      <ContentDetail
        v-model="contentData"
        :mode="mode"
        :errors="errors"
        active-tab="basic"
      />
    </template>

    <!-- 元数据内容 -->
    <template #meta-content="{ data, errors, mode }">
      <ContentDetail
        v-model="contentData"
        :mode="mode"
        :errors="errors"
        active-tab="meta"
      />
    </template>

    <!-- 历史版本内容 -->
    <template #history-content="{ data, mode }">
      <div class="history-section">
        <div v-if="versionHistory.length === 0" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3 class="empty-title">暂无版本历史</h3>
          <p class="empty-description">当前内容还没有版本历史记录</p>
        </div>
        
        <div v-else class="version-list">
          <div
            v-for="version in versionHistory"
            :key="version.id"
            class="version-item"
          >
            <div class="version-header">
              <div class="version-info">
                <span class="version-number">v{{ version.version }}</span>
                <span class="version-date">{{ formatDate(version.createdAt) }}</span>
              </div>
              <div class="version-actions">
                <button class="action-btn view-btn" @click="viewVersion(version)">
                  查看
                </button>
                <button class="action-btn restore-btn" @click="restoreVersion(version)">
                  恢复
                </button>
              </div>
            </div>
            <div class="version-changes">
              <p class="change-summary">{{ version.changeLog }}</p>
              <div class="change-author">
                <span>修改人：{{ version.author }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 侧边栏内容 -->
    <template #sidebar="{ data, toc }">
      <div class="sidebar-section">
        <h4 class="sidebar-title">内容统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-value">{{ data.viewCount || 0 }}</span>
            <span class="stat-label">浏览次数</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ data.downloadCount || 0 }}</span>
            <span class="stat-label">下载次数</span>
          </div>
          <div class="stat-item">
            <span class="stat-value">{{ data.favoriteCount || 0 }}</span>
            <span class="stat-label">收藏次数</span>
          </div>
        </div>
      </div>
      
      <div class="sidebar-section">
        <h4 class="sidebar-title">快速信息</h4>
        <div class="quick-info">
          <div class="info-item">
            <span class="info-label">内容类型</span>
            <span class="info-value">{{ getTypeLabel(data.type) }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">状态</span>
            <span :class="['status-badge', data.status]">
              {{ getStatusLabel(data.status) }}
            </span>
          </div>
          <div class="info-item">
            <span class="info-label">作者</span>
            <span class="info-value">{{ data.author }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">创建时间</span>
            <span class="info-value">{{ formatDate(data.createdAt) }}</span>
          </div>
        </div>
      </div>
    </template>
  </DetailPage>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import DetailPage from '@/components/ui/DetailPage.vue'
import ContentDetail from '@/components/details/ContentDetail.vue'
import { getTemplateConfig, getValidationRules } from '@/config/detailTemplates'

const route = useRoute()
const router = useRouter()

// 状态
const contentData = ref({
  title: '',
  type: '',
  status: 'draft',
  author: '',
  summary: '',
  content: '',
  publishDate: '',
  expireDate: '',
  priority: 'normal',
  permission: 'internal',
  keywords: [],
  relatedLinks: [],
  viewCount: 0,
  downloadCount: 0,
  favoriteCount: 0,
  createdAt: '',
  updatedAt: ''
})

const versionHistory = ref([])
const loading = ref(false)

// 计算属性
const contentId = computed(() => route.params.id)
const mode = computed(() => {
  if (route.name === 'content-create') return 'create'
  if (route.query.mode === 'edit') return 'edit'
  return 'view'
})

const pageTitle = computed(() => {
  if (mode.value === 'create') return '新建内容'
  if (mode.value === 'edit') return `编辑内容 - ${contentData.value.title}`
  return `内容详情 - ${contentData.value.title}`
})

const pageDescription = computed(() => {
  if (mode.value === 'create') return '创建新的内容文档'
  return '查看和管理内容的详细信息'
})

// 获取模板配置
const templateConfig = getTemplateConfig('content')
const tabs = templateConfig.tabs
const validationRules = getValidationRules('content', 'basic')

// 方法
const loadContentData = async () => {
  if (mode.value === 'create') {
    // 新建模式，使用默认数据
    contentData.value = {
      title: '',
      type: '',
      status: 'draft',
      author: '',
      summary: '',
      content: '',
      publishDate: '',
      expireDate: '',
      priority: 'normal',
      permission: 'internal',
      keywords: [],
      relatedLinks: [],
      viewCount: 0,
      downloadCount: 0,
      favoriteCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    return
  }

  // 模拟加载数据
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    contentData.value = {
      id: contentId.value,
      title: 'Vue 3 开发规范文档',
      type: 'document',
      status: 'published',
      author: '张三',
      summary: '本文档详细介绍了Vue 3项目的开发规范和最佳实践',
      content: `# Vue 3 开发规范文档

## 概述

本文档旨在为Vue 3项目开发提供统一的规范和最佳实践指导。

## 项目结构

\`\`\`
src/
├── components/     # 组件目录
├── views/         # 页面目录
├── stores/        # 状态管理
├── utils/         # 工具函数
└── assets/        # 静态资源
\`\`\`

## 编码规范

### 组件命名

- 使用PascalCase命名组件
- 组件名应该具有描述性

### 样式规范

- 使用Tailwind CSS
- 支持深色主题
- 保持响应式设计

## 最佳实践

1. 使用Composition API
2. 合理使用响应式数据
3. 组件解耦和复用
4. 性能优化考虑

## 代码示例

\`\`\`vue
<template>
  <div class="component-wrapper">
    <h1>{{ title }}</h1>
  </div>
</template>

&lt;script setup&gt;
import { ref } from 'vue'

const title = ref('Hello Vue 3')
&lt;/script&gt;
\`\`\`
`,
      publishDate: '2024-01-01',
      expireDate: '',
      priority: 'high',
      permission: 'internal',
      keywords: ['Vue3', '开发规范', '最佳实践', '前端'],
      relatedLinks: [
        { title: 'Vue 3 官方文档', url: 'https://vuejs.org/' },
        { title: 'Tailwind CSS', url: 'https://tailwindcss.com/' }
      ],
      viewCount: 1250,
      downloadCount: 89,
      favoriteCount: 45,
      createdAt: '2024-01-01T10:00:00Z',
      updatedAt: '2024-01-15T14:30:00Z'
    }

    // 加载版本历史
    versionHistory.value = [
      {
        id: 1,
        version: '2.1',
        changeLog: '更新了组件命名规范和样式指南',
        author: '张三',
        createdAt: '2024-01-15T14:30:00Z'
      },
      {
        id: 2,
        version: '2.0',
        changeLog: '重构了项目结构说明，增加了代码示例',
        author: '李四',
        createdAt: '2024-01-10T09:15:00Z'
      }
    ]
  } catch (error) {
    console.error('加载内容数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSave = async (data) => {
  loading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('保存内容数据:', data)
    
    if (mode.value === 'create') {
      router.push({ name: 'content-detail', params: { id: 'new-content-id' } })
    } else {
      router.push({ name: 'content-detail', params: { id: contentId.value } })
    }
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  router.push({ name: 'content' })
}

const handleDataChange = (data) => {
  contentData.value = data
}

const viewVersion = (version) => {
  console.log('查看版本:', version)
}

const restoreVersion = (version) => {
  console.log('恢复版本:', version)
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const getTypeLabel = (type) => {
  const typeMap = {
    document: '规范文档',
    manual: '操作手册',
    guide: '技术文档',
    training: '培训材料'
  }
  return typeMap[type] || type
}

const getStatusLabel = (status) => {
  const statusMap = {
    published: '已发布',
    draft: '草稿',
    review: '待审核',
    archived: '已归档'
  }
  return statusMap[status] || status
}

// 监听路由变化，只在必要时重新加载数据
watch([() => route.params.id, () => route.query.mode], ([newId, newMode], [oldId, oldMode]) => {
  // 只有当ID变化或从创建模式切换到其他模式时才重新加载
  if (newId !== oldId || (oldMode === undefined && newMode !== undefined)) {
    loadContentData()
  }
}, { immediate: true })

onMounted(() => {
  // 初始加载已经通过watch处理，这里不需要重复调用
  // loadContentData()
})
</script>

<style scoped>
/* 历史版本部分 */
.history-section {
  @apply space-y-4;
}

.empty-state {
  @apply text-center py-12;
}

.empty-icon {
  @apply text-6xl mb-4;
}

.empty-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.empty-description {
  @apply text-gray-600 dark:text-gray-400;
}

.version-list {
  @apply space-y-4;
}

.version-item {
  @apply bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700;
}

.version-header {
  @apply flex items-center justify-between mb-3;
}

.version-info {
  @apply flex items-center gap-3;
}

.version-number {
  @apply font-semibold text-blue-600 dark:text-blue-400;
}

.version-date {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.version-actions {
  @apply flex items-center gap-2;
}

.action-btn {
  @apply px-3 py-1 text-sm rounded transition-colors;
}

.view-btn {
  @apply bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300;
  @apply hover:bg-blue-200 dark:hover:bg-blue-800;
}

.restore-btn {
  @apply bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300;
  @apply hover:bg-green-200 dark:hover:bg-green-800;
}

.version-changes {
  @apply space-y-2;
}

.change-summary {
  @apply text-gray-900 dark:text-white;
}

.change-author {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 侧边栏部分 */
.sidebar-section {
  @apply p-4 border-b border-gray-200 dark:border-gray-700;
}

.sidebar-title {
  @apply text-sm font-semibold text-gray-900 dark:text-gray-100 mb-3;
}

.stats-grid {
  @apply grid grid-cols-1 gap-3;
}

.stat-item {
  @apply text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg;
}

.stat-value {
  @apply block text-lg font-semibold text-gray-900 dark:text-white;
}

.stat-label {
  @apply block text-xs text-gray-500 dark:text-gray-400 mt-1;
}

.quick-info {
  @apply space-y-3;
}

.info-item {
  @apply flex flex-col gap-1;
}

.info-label {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.info-value {
  @apply text-sm font-medium text-gray-900 dark:text-white;
}

.status-badge {
  @apply px-2 py-1 text-xs rounded-full font-medium;
}

.status-badge.published {
  @apply bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200;
}

.status-badge.draft {
  @apply bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200;
}

.status-badge.review {
  @apply bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200;
}

.status-badge.archived {
  @apply bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200;
}
</style>
