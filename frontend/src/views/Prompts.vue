<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <PageHeader
      title="AI提示词管理"
      description="管理和优化 AI 提示词模板"
    >
      <template #actions>
        <ActionButton variant="primary" icon="➕" @click="handleAddPrompt">
          新建提示词
        </ActionButton>
        <ActionButton variant="secondary" icon="📤">
          导出模板
        </ActionButton>
        <ActionButton variant="secondary" icon="📥">
          导入模板
        </ActionButton>
      </template>
    </PageHeader>

    <!-- 搜索和筛选 -->
    <SearchArea>
      <template #filters>
        <div class="flex-1 min-w-0">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">搜索提示词</label>
          <input
            v-model="searchForm.keyword"
            type="text"
            placeholder="搜索提示词..."
            class="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:border-blue-500"
          >
        </div>
        <div class="flex-1 min-w-0">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">分类</label>
          <select v-model="searchForm.category" class="w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500">
            <option value="">所有分类</option>
            <option value="code">代码生成</option>
            <option value="writing">文档写作</option>
            <option value="analysis">数据分析</option>
            <option value="design">创意设计</option>
          </select>
        </div>
      </template>

      <template #searchActions>
        <ActionButton variant="search" icon="🔍" @click="handleSearch">
          搜索
        </ActionButton>
        <ActionButton variant="reset" icon="🔄" @click="handleReset">
          重置
        </ActionButton>
      </template>
    </SearchArea>

    <!-- 提示词列表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 动态提示词卡片 -->
      <div
        v-for="prompt in prompts"
        :key="prompt.id"
        class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all"
      >
        <div class="flex items-start justify-between mb-4">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">{{ prompt.title }}</h3>
            <span :class="getCategoryColor(prompt.category)" class="text-white px-2 py-1 rounded text-sm">
              {{ getCategoryName(prompt.category) }}
            </span>
          </div>
          <div class="flex space-x-2">
            <button
              @click="handleViewPrompt(prompt)"
              class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
              title="查看详情"
            >
              👁️
            </button>
            <button
              @click="handleEditPrompt(prompt)"
              class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
              title="编辑"
            >
              ✏️
            </button>
            <button
              @click="handleCopyPrompt(prompt)"
              class="text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200"
              title="复制"
            >
              📋
            </button>
            <button
              @click="handleDeletePrompt(prompt)"
              class="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
              title="删除"
            >
              🗑️
            </button>
          </div>
        </div>
        <p class="text-gray-600 dark:text-gray-400 mb-4">{{ prompt.description || '暂无描述' }}</p>
        <div class="flex items-center justify-between text-sm text-gray-500 dark:text-gray-500">
          <span>使用次数: {{ prompt.usageCount || 0 }}</span>
          <span>最后更新: {{ prompt.updatedAt || prompt.createdAt }}</span>
        </div>
      </div>

      <!-- 空状态提示 -->
      <div v-if="prompts.length === 0" class="col-span-full text-center py-12">
        <div class="text-gray-400 dark:text-gray-500 text-6xl mb-4">🤖</div>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无提示词</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-4">开始创建您的第一个AI提示词模板</p>
        <ActionButton variant="primary" icon="➕" @click="handleAddPrompt">
          新建提示词
        </ActionButton>
      </div>
    </div>



    <!-- 确认删除弹窗 -->
    <ConfirmModal
      :show="showConfirmModal"
      :title="confirmModalData.title"
      :message="confirmModalData.message"
      :confirm-text="confirmModalData.confirmText"
      :cancel-text="confirmModalData.cancelText"
      @confirm="confirmDelete"
      @close="closeConfirmModal"
    />
  </div>
</template>

<script setup>
import { reactive, ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'

import ConfirmModal from '@/components/ConfirmModal.vue'
import { useCrudOperations } from '@/composables/useCrudOperations'
import { useToast } from '@/composables/useToast'


const router = useRouter()
const toast = useToast()
const crud = useCrudOperations('prompts')



const searchForm = reactive({
  keyword: '',
  category: ''
})

// 弹窗状态
const showConfirmModal = ref(false)
const confirmModalData = ref({})

// 分页状态
const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

// 初始化数据
onMounted(async () => {
  await loadPrompts()
})

// 计算属性
const prompts = computed(() => crud.data.value || [])

// 加载提示词数据
const loadPrompts = async () => {
  try {
    await crud.fetchList({
      keyword: searchForm.keyword,
      category: searchForm.category,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    // 更新分页信息
    pagination.currentPage = crud.pagination.currentPage
  } catch (error) {
    console.error('加载提示词列表失败:', error)
  }
}

const handleSearch = async () => {
  pagination.currentPage = 1
  await loadPrompts()
}

const handleReset = async () => {
  searchForm.keyword = ''
  searchForm.category = ''
  pagination.currentPage = 1
  await loadPrompts()
}

const handleAddPrompt = () => {
  router.push({ name: 'prompt-create' })
}

const handleViewPrompt = (prompt) => {
  // 跳转到独立详情页面
  router.push({
    name: 'prompt-detail',
    params: { id: prompt.id }
  })
}

const handleEditPrompt = (prompt) => {
  router.push({
    name: 'prompt-detail',
    params: { id: prompt.id },
    query: { mode: 'edit' }
  })
}

const handleDeletePrompt = (prompt) => {
  // 使用ConfirmModal确认删除
  confirmModalData.value = {
    title: '确认删除提示词',
    message: `确定要删除提示词 "${prompt.title}" 吗？\n\n删除后将无法恢复，请谨慎操作。`,
    confirmText: '删除',
    cancelText: '取消',
    prompt: prompt
  }
  showConfirmModal.value = true
}

// 确认删除提示词
const confirmDelete = async () => {
  try {
    const prompt = confirmModalData.value.prompt
    await crud.remove(prompt.id)
    showConfirmModal.value = false

    // 刷新列表
    await loadPrompts()

    toast.success(`提示词 "${prompt.title}" 删除成功`)
  } catch (error) {
    console.error('删除提示词失败:', error)
    toast.error('删除提示词失败：' + (error.message || '未知错误'))
  }
}

// 关闭确认弹窗
const closeConfirmModal = () => {
  showConfirmModal.value = false
  confirmModalData.value = {}
}



// 提示词复制功能
const handleCopyPrompt = async (prompt) => {
  try {
    const newPrompt = {
      ...prompt,
      title: `${prompt.title} (副本)`,
      id: undefined
    }

    // 跳转到创建页面，并传递模板数据
    router.push({
      name: 'prompt-create',
      query: { template: JSON.stringify(newPrompt) }
    })

    toast.info('已为您创建提示词副本，请编辑后保存')
  } catch (error) {
    console.error('复制提示词失败:', error)
    toast.error('复制提示词失败：' + (error.message || '未知错误'))
  }
}

// 获取分类颜色
const getCategoryColor = (category) => {
  const colors = {
    'code': 'bg-blue-600',
    'writing': 'bg-green-600',
    'analysis': 'bg-purple-600',
    'design': 'bg-orange-600'
  }
  return colors[category] || 'bg-gray-600'
}

// 获取分类名称
const getCategoryName = (category) => {
  const names = {
    'code': '代码生成',
    'writing': '文档写作',
    'analysis': '数据分析',
    'design': '创意设计'
  }
  return names[category] || category
}
</script>
