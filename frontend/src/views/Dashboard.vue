<template>
<div class="space-y-6">


  <!-- 欢迎信息 -->
  <div class="mb-6">
    <h1 class="text-2xl font-semibold mb-2 text-gray-900 dark:text-white">👋早上好，github_10414</h1>
    <div class="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
      <button class="p-1 hover:text-gray-800 dark:hover:text-gray-300 transition-colors">🔍</button>
      <button class="p-1 hover:text-gray-800 dark:hover:text-gray-300 transition-colors">🔄</button>
    </div>
  </div>

  <!-- 快速导航 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">🚀 快速导航</h3>
      <span class="text-sm text-gray-600 dark:text-gray-400">点击快速访问各功能模块</span>
    </div>

    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
      <!-- 主要功能 -->
      <div class="text-center">
        <button @click="$router.push('/dashboard')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📊</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">数据看板</div>
        </button>
      </div>

      <!-- 成长地图 -->
      <div class="text-center">
        <button @click="$router.push('/dev-tools')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📚</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">研发工具</div>
        </button>
      </div>

      <!-- 规则中心 -->
      <div class="text-center">
        <button @click="$router.push('/prompts')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">⚙️</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">AI提示词</div>
        </button>
      </div>

      <!-- MCP服务 -->
      <div class="text-center">
        <button @click="$router.push('/services')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">🔧</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">服务管理</div>
        </button>
      </div>

      <div class="text-center">
        <button @click="$router.push('/categories')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📂</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">分类管理</div>
        </button>
      </div>

      <div class="text-center">
        <button @click="$router.push('/tags')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">🏷️</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">标签管理</div>
        </button>
      </div>

      <!-- 规范中心 -->
      <div class="text-center">
        <button @click="$router.push('/content')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📋</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">内容管理</div>
        </button>
      </div>

      <!-- 定时任务 -->
      <div class="text-center">
        <button @click="$router.push('/tasks')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">⏰</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">任务管理</div>
        </button>
      </div>

      <div class="text-center">
        <button @click="$router.push('/logs')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📊</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">执行日志</div>
        </button>
      </div>

      <!-- AI学习资源 -->
      <div class="text-center">
        <button @click="$router.push('/rss')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📖</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">RSS管理</div>
        </button>
      </div>

      <!-- 控制台 -->
      <div class="text-center">
        <button @click="$router.push('/api-tokens')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">🔗</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">API令牌</div>
        </button>
      </div>

      <div class="text-center">
        <button @click="$router.push('/usage-logs')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">📋</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">使用日志</div>
        </button>
      </div>

      <!-- 个人中心 -->
      <div class="text-center">
        <button @click="$router.push('/wallet')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">💰</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">钱包</div>
        </button>
      </div>

      <div class="text-center">
        <button @click="$router.push('/settings')" class="w-full p-4 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors group">
          <div class="text-2xl mb-2 group-hover:scale-110 transition-transform">👤</div>
          <div class="text-sm text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-white">个人设置</div>
        </button>
      </div>
    </div>
  </div>

  <!-- 功能模块导航 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- MCP服务模块 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center mb-4">
        <span class="text-2xl mr-3">🔧</span>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">MCP服务</h3>
      </div>
      <div class="space-y-3">
        <button @click="$router.push('/services')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">🔧</span>
          <span class="text-gray-700 dark:text-gray-300">服务管理</span>
        </button>
        <button @click="$router.push('/categories')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">📂</span>
          <span class="text-gray-700 dark:text-gray-300">分类管理</span>
        </button>
        <button @click="$router.push('/tags')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">🏷️</span>
          <span class="text-gray-700 dark:text-gray-300">标签管理</span>
        </button>
      </div>
    </div>

    <!-- 任务管理模块 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center mb-4">
        <span class="text-2xl mr-3">⏰</span>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">定时任务</h3>
      </div>
      <div class="space-y-3">
        <button @click="$router.push('/tasks')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">⏰</span>
          <span class="text-gray-700 dark:text-gray-300">任务管理</span>
        </button>
        <button @click="$router.push('/logs')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">📊</span>
          <span class="text-gray-700 dark:text-gray-300">执行日志</span>
        </button>
      </div>
    </div>

    <!-- 控制台模块 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center mb-4">
        <span class="text-2xl mr-3">🎛️</span>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">控制台</h3>
      </div>
      <div class="space-y-3">
        <button @click="$router.push('/api-tokens')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">🔗</span>
          <span class="text-gray-700 dark:text-gray-300">API令牌</span>
        </button>
        <button @click="$router.push('/usage-logs')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">📋</span>
          <span class="text-gray-700 dark:text-gray-300">使用日志</span>
        </button>
      </div>
    </div>

    <!-- 内容管理模块 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center mb-4">
        <span class="text-2xl mr-3">📝</span>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">内容管理</h3>
      </div>
      <div class="space-y-3">
        <button @click="$router.push('/prompts')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">⚙️</span>
          <span class="text-gray-700 dark:text-gray-300">AI提示词</span>
        </button>
        <button @click="$router.push('/content')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">📋</span>
          <span class="text-gray-700 dark:text-gray-300">内容管理</span>
        </button>
      </div>
    </div>

    <!-- 学习资源模块 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center mb-4">
        <span class="text-2xl mr-3">📚</span>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">学习资源</h3>
      </div>
      <div class="space-y-3">
        <button @click="$router.push('/dev-tools')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">📚</span>
          <span class="text-gray-700 dark:text-gray-300">研发工具</span>
        </button>
        <button @click="$router.push('/rss')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">📖</span>
          <span class="text-gray-700 dark:text-gray-300">RSS管理</span>
        </button>
      </div>
    </div>

    <!-- 个人中心模块 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="flex items-center mb-4">
        <span class="text-2xl mr-3">👤</span>
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">个人中心</h3>
      </div>
      <div class="space-y-3">
        <button @click="$router.push('/wallet')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">💰</span>
          <span class="text-gray-700 dark:text-gray-300">钱包</span>
        </button>
        <button @click="$router.push('/settings')" class="w-full text-left p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors flex items-center">
          <span class="mr-3">👤</span>
          <span class="text-gray-700 dark:text-gray-300">个人设置</span>
        </button>
      </div>
    </div>
  </div>

  <!-- 统计卡片 -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- 账户数据 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">账户数据</h3>
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
          <span class="text-blue-600 dark:text-blue-400">👤</span>
        </div>
      </div>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span class="text-xs text-gray-600 dark:text-gray-400">当前余额</span>
          <span class="text-lg font-semibold text-gray-900 dark:text-white">${{ accountData.balance }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-xs text-gray-600 dark:text-gray-400">历史消耗</span>
          <span class="text-sm text-purple-600 dark:text-purple-400">${{ accountData.consumed }}</span>
        </div>
      </div>
    </div>

    <!-- 使用统计 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">使用统计</h3>
        <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
          <span class="text-green-600 dark:text-green-400">📊</span>
        </div>
      </div>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span class="text-xs text-gray-600 dark:text-gray-400">请求次数</span>
          <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ usageStats.requests }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-xs text-gray-600 dark:text-gray-400">统计次数</span>
          <span class="text-sm text-teal-600 dark:text-teal-400">{{ usageStats.stats }}</span>
        </div>
      </div>
    </div>

    <!-- 资源消耗 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">资源消耗</h3>
        <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
          <span class="text-yellow-600 dark:text-yellow-400">⚡</span>
        </div>
      </div>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span class="text-xs text-gray-600 dark:text-gray-400">输入消耗</span>
          <span class="text-lg font-semibold text-gray-900 dark:text-white">${{ resourceUsage.input }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-xs text-gray-600 dark:text-gray-400">输出Tokens</span>
          <span class="text-sm text-red-600 dark:text-red-400">{{ resourceUsage.tokens }}</span>
        </div>
      </div>
    </div>

    <!-- 性能指标 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">性能指标</h3>
        <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
          <span class="text-purple-600 dark:text-purple-400">📈</span>
        </div>
      </div>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span class="text-xs text-gray-600 dark:text-gray-400">平均RPM</span>
          <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ performance.rpm }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-xs text-gray-600 dark:text-gray-400">平均TPM</span>
          <span class="text-sm text-orange-600 dark:text-orange-400">{{ performance.tpm }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 图表区域 -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- 模型消耗分析 -->
    <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">📊 模型数据分析</h3>
        <div class="flex space-x-2">
          <button class="px-3 py-1 text-sm text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 rounded-md transition-colors hover:bg-blue-200 dark:hover:bg-blue-800">📊 详情分布</button>
          <button class="px-3 py-1 text-sm text-gray-700 dark:text-gray-300 rounded-md transition-colors hover:bg-gray-100 dark:hover:bg-gray-700">📞 调用次数分布</button>
        </div>
      </div>

      <div class="mb-4">
        <div class="text-sm mb-2 text-gray-700 dark:text-gray-300">模型消耗分析</div>
        <div class="text-xs text-gray-600 dark:text-gray-400">总计：$0.03</div>
      </div>

      <div class="h-64 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-end justify-center p-4">
        <div class="flex items-end space-x-8">
          <div class="text-center">
            <div class="w-16 h-32 bg-blue-200 dark:bg-blue-600 rounded mb-2 transition-all hover:bg-blue-300 dark:hover:bg-blue-500"></div>
            <div class="text-xs text-gray-600 dark:text-gray-400">07-07 05:00</div>
          </div>
          <div class="text-center">
            <div class="w-16 h-24 bg-blue-300 dark:bg-blue-500 rounded mb-2 transition-all hover:bg-blue-400 dark:hover:bg-blue-400"></div>
            <div class="text-xs text-gray-600 dark:text-gray-400">07-07 06:00</div>
          </div>
          <div class="text-center">
            <div class="w-16 h-40 bg-blue-400 dark:bg-blue-400 rounded mb-2 transition-all hover:bg-blue-500 dark:hover:bg-blue-300"></div>
            <div class="text-xs text-gray-600 dark:text-gray-400">07-07 07:00</div>
          </div>
          <div class="text-center">
            <div class="w-16 h-48 bg-blue-500 dark:bg-blue-300 rounded mb-2 transition-all hover:bg-blue-600 dark:hover:bg-blue-200"></div>
            <div class="text-xs text-gray-600 dark:text-gray-400">07-07 11:00</div>
          </div>
        </div>
      </div>

      <div class="mt-4 flex items-center space-x-4 text-xs">
        <div class="flex items-center space-x-1">
          <div class="w-3 h-3 bg-blue-300 dark:bg-blue-500 rounded"></div>
          <span class="text-gray-700 dark:text-gray-300">claude-sonnet-4-20250514</span>
        </div>
        <div class="flex items-center space-x-1">
          <div class="w-3 h-3 bg-blue-500 dark:bg-blue-400 rounded"></div>
          <span class="text-gray-700 dark:text-gray-300">claude-3-5-haiku-20241022</span>
        </div>
      </div>
    </div>

    <!-- API信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">🔧 API信息</h3>
        <button class="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 transition-colors">🔄</button>
      </div>
      <div class="text-center py-8">
        <div class="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center">
          <span class="text-2xl">🔧</span>
        </div>
        <h4 class="font-medium mb-2 text-gray-900 dark:text-white">暂无API信息</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400">请联系管理员在系统设置中配置API信息</p>
      </div>
    </div>
  </div>

  <!-- 底部信息区域 -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- 系统公告 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">📢 系统公告</h3>
        <span class="text-sm text-gray-600 dark:text-gray-400">显示最新20条</span>
      </div>
      <div class="space-y-3">
        <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
          <div class="flex items-center space-x-2 mb-2">
            <span class="text-xs bg-blue-600 text-white px-2 py-1 rounded">重要</span>
            <span class="text-xs text-gray-600 dark:text-gray-400">进行中</span>
            <span class="text-xs text-orange-600 dark:text-orange-400">置顶</span>
          </div>
          <p class="text-sm text-gray-700 dark:text-gray-300">短时间内出现多条 403 错误，目前已修复</p>
          <span class="text-xs text-gray-500 dark:text-gray-500">20小时前</span>
        </div>
      </div>
    </div>

    <!-- 常见问答 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">❓ 常见问答</h3>
        <button class="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 transition-colors">📖</button>
      </div>
      <div class="text-center py-8">
        <div class="text-6xl mb-4">❓</div>
        <p class="text-gray-600 dark:text-gray-400">暂无常见问题</p>
      </div>
    </div>

    <!-- 服务可用性 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">📊 服务可用性</h3>
        <button class="p-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 transition-colors">📊</button>
      </div>
      <div class="text-center py-8">
        <div class="text-4xl mb-4">📊</div>
        <p class="text-gray-600 dark:text-gray-400">服务运行正常</p>
      </div>
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'

// 主题相关
const themeStore = useThemeStore()
const isDarkMode = computed(() => themeStore.isDarkMode)

// 模拟数据
const accountData = ref({
  balance: '549.97',
  consumed: '0.03'
})

const usageStats = ref({
  requests: 5,
  stats: 5
})

const resourceUsage = ref({
  input: '0.03',
  tokens: 455
})

const performance = ref({
  rpm: '0.003',
  tpm: '0.303'
})
</script>
