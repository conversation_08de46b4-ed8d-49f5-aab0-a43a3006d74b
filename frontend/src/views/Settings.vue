<template>
<div class="space-y-6">

  <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
    <!-- 用户信息 -->
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <div class="flex items-center space-x-4">
        <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center text-white text-xl font-medium">
          G
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">github_10414</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">普通用户 · ID: 10414</p>
        </div>
      </div>

      <div class="mt-6 grid grid-cols-3 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-white">${{ userStats.balance }}</div>
          <div class="text-sm text-gray-400">当前余额</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-white">${{ userStats.consumed }}</div>
          <div class="text-sm text-gray-400">历史消耗</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-white">{{ userStats.requests }}</div>
          <div class="text-sm text-gray-400">请求次数</div>
        </div>
      </div>
    </div>

    <!-- 设置选项卡 -->
    <div class="border-b border-gray-200 dark:border-gray-700">
      <nav class="flex space-x-8 px-6">
        <button @click="activeTab = 'account'"
          :class="[
            'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
            activeTab === 'account'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300'
          ]">
          👤 账户绑定
        </button>
        <button @click="activeTab = 'notifications'"
          :class="[
            'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
            activeTab === 'notifications'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300'
          ]">
          🔔 通知设置
        </button>
        <button @click="activeTab = 'security'"
          :class="[
            'py-4 px-1 border-b-2 font-medium text-sm transition-colors',
            activeTab === 'security'
              ? 'border-blue-500 text-blue-600 dark:text-blue-400'
              : 'border-transparent text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300'
          ]">
          🔒 安全设置
        </button>
      </nav>
    </div>

    <!-- 设置内容 -->
    <div class="p-6">
      <!-- 账户绑定 -->
      <div v-if="activeTab === 'account'" class="space-y-4">
        <div v-for="(platform, index) in platforms" :key="index" class="flex items-center justify-between p-4 border border-gray-300 dark:border-gray-600 rounded-lg transition-colors hover:bg-gray-50 dark:hover:bg-gray-700">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded-lg flex items-center justify-center" :class="platform.bgClass">
              <span :class="platform.iconClass">{{ platform.icon }}</span>
            </div>
            <div>
              <div class="font-medium text-gray-900 dark:text-white">{{ platform.name }}</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">{{ platform.status }}</div>
            </div>
          </div>
          <button v-if="platform.canBind" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm transition-colors">
            {{ platform.status === '未绑定' ? '绑定' : '解绑' }}
          </button>
          <span v-else class="text-sm text-gray-500 dark:text-gray-500">未启用</span>
        </div>
      </div>

      <!-- 通知设置 -->
      <div v-if="activeTab === 'notifications'" class="space-y-6">
        <div>
          <h4 class="text-lg font-medium mb-4 text-gray-900 dark:text-white">通知设置</h4>
          <p class="text-sm mb-6 text-gray-600 dark:text-gray-400">配置您的通知偏好</p>
        </div>

        <div class="space-y-4">
          <div class="flex items-center justify-between p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
            <div>
              <div class="font-medium text-gray-900 dark:text-white">邮件通知</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">接收重要更新和提醒</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="notificationSettings.email" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
            <div>
              <div class="font-medium text-gray-900 dark:text-white">推送通知</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">浏览器推送通知</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="notificationSettings.push" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
            <div>
              <div class="font-medium text-gray-900 dark:text-white">短信通知</div>
              <div class="text-sm text-gray-600 dark:text-gray-400">重要安全提醒</div>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" v-model="notificationSettings.sms" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-300 dark:bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>

        <div class="flex justify-end pt-4">
          <button @click="saveNotificationSettings" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
            💾 保存设置
          </button>
        </div>
      </div>

      <!-- 安全设置 -->
      <div v-if="activeTab === 'security'" class="space-y-6">
        <div>
          <h4 class="text-lg font-medium mb-4 text-gray-900 dark:text-white">安全设置</h4>
          <p class="text-sm mb-6 text-gray-600 dark:text-gray-400">管理您的账户安全</p>
        </div>

        <div class="space-y-4">
          <div class="p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900 dark:text-white">修改密码</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">定期更新密码以保护账户安全</div>
              </div>
              <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                修改
              </button>
            </div>
          </div>

          <div class="p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900 dark:text-white">两步验证</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">增强账户安全性</div>
              </div>
              <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                启用
              </button>
            </div>
          </div>

          <div class="p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
            <div class="flex items-center justify-between">
              <div>
                <div class="font-medium text-gray-900 dark:text-white">登录历史</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">查看最近的登录记录</div>
              </div>
              <button class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                查看
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const activeTab = ref('account')

const userStats = ref({
  balance: '549.97',
  consumed: '0.03',
  requests: 5
})

const notificationSettings = reactive({
  email: true,
  push: false,
  sms: false
})

const platforms = ref([
  {
    name: '邮箱',
    icon: '📧',
    status: '未绑定',
    bgClass: 'bg-gray-700',
    iconClass: 'text-gray-300',
    canBind: true
  },
  {
    name: 'GitHub',
    icon: '🐙',
    status: 'github_10414',
    bgClass: 'bg-gray-900',
    iconClass: 'text-white',
    canBind: true
  },
  {
    name: '微信',
    icon: '💬',
    status: '未绑定',
    bgClass: 'bg-green-100',
    iconClass: 'text-green-600',
    canBind: false
  },
  {
    name: 'Telegram',
    icon: '✈️',
    status: '未绑定',
    bgClass: 'bg-blue-500',
    iconClass: 'text-white',
    canBind: false
  }
])

const saveNotificationSettings = () => {
  console.log('保存通知设置:', notificationSettings)
  // 这里处理保存逻辑
}
</script>
