<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="max-w-md w-full space-y-8">
      <div class="text-center">
        <h2 class="text-3xl font-bold text-gray-900 dark:text-white">OAuth2 测试</h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          测试OAuth2登录功能
        </p>
      </div>

      <div class="space-y-4">
        <!-- 直接跳转测试 -->
        <button
          @click="testDirectRedirect"
          class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          直接跳转到Google OAuth2
        </button>

        <!-- 弹窗测试 -->
        <button
          @click="testPopup"
          class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-blue-600 text-sm font-medium text-white hover:bg-blue-700"
        >
          弹窗方式测试Google OAuth2
        </button>

        <!-- 测试API -->
        <button
          @click="testAPI"
          class="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-green-600 text-sm font-medium text-white hover:bg-green-700"
        >
          测试API连接
        </button>

        <!-- 日志显示 -->
        <div class="mt-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">测试日志：</h3>
          <div class="text-xs text-gray-600 dark:text-gray-400 space-y-1 max-h-40 overflow-y-auto">
            <div v-for="(log, index) in logs" :key="index">
              {{ log }}
            </div>
          </div>
          <button
            @click="clearLogs"
            class="mt-2 text-xs text-blue-600 hover:text-blue-800"
          >
            清除日志
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const logs = ref([])

const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.push(`[${timestamp}] ${message}`)
}

const clearLogs = () => {
  logs.value = []
}

const testDirectRedirect = () => {
  addLog('开始直接跳转测试...')
  const authUrl = `http://localhost:8080/oauth2/authorize/google?redirect_uri=${encodeURIComponent(window.location.origin + '/auth/callback')}`
  addLog(`跳转URL: ${authUrl}`)
  window.location.href = authUrl
}

const testPopup = () => {
  addLog('开始弹窗测试...')
  const authUrl = `http://localhost:8080/oauth2/authorize/google?redirect_uri=${encodeURIComponent(window.location.origin + '/auth/callback')}`
  addLog(`弹窗URL: ${authUrl}`)
  
  const popup = window.open(
    authUrl,
    'google-oauth-test',
    'width=500,height=600,scrollbars=yes,resizable=yes'
  )

  // 监听弹窗关闭
  const checkClosed = setInterval(() => {
    if (popup.closed) {
      clearInterval(checkClosed)
      addLog('弹窗已关闭')
    }
  }, 1000)

  // 监听消息
  const messageListener = (event) => {
    if (event.origin !== window.location.origin) return
    
    addLog(`收到消息: ${JSON.stringify(event.data)}`)
    
    if (event.data.type === 'OAUTH_SUCCESS') {
      addLog('OAuth2登录成功!')
      addLog(`收到令牌: ${event.data.token ? '是' : '否'}`)
      addLog(`收到刷新令牌: ${event.data.refreshToken ? '是' : '否'}`)
      clearInterval(checkClosed)
      popup.close()
      window.removeEventListener('message', messageListener)
    } else if (event.data.type === 'OAUTH_ERROR') {
      addLog(`OAuth2登录失败: ${event.data.error}`)
      clearInterval(checkClosed)
      popup.close()
      window.removeEventListener('message', messageListener)
    }
  }

  window.addEventListener('message', messageListener)
}

const testAPI = async () => {
  addLog('测试API连接...')
  
  try {
    const response = await fetch('http://localhost:8080/api/health')
    const result = await response.json()
    addLog(`API响应: ${JSON.stringify(result)}`)
  } catch (error) {
    addLog(`API错误: ${error.message}`)
  }
}

// 初始化日志
addLog('OAuth2测试页面已加载')
</script>
