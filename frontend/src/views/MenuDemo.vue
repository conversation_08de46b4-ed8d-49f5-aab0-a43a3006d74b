<template>
  <div class="menu-demo-container">
    <div class="demo-header">
      <h1 class="demo-title">🎨 菜单系统优化演示</h1>
      <p class="demo-subtitle">世界级PC端菜单UI/UX设计展示</p>
    </div>

    <div class="demo-grid">
      <!-- 设计特性展示 -->
      <div class="demo-card">
        <h2 class="card-title">✨ 设计特性</h2>
        <div class="feature-list">
          <div class="feature-item">
            <div class="feature-icon">🎯</div>
            <div class="feature-content">
              <h3>现代化视觉设计</h3>
              <p>采用渐变背景、毛玻璃效果和精致阴影，营造专业的视觉体验</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔍</div>
            <div class="feature-content">
              <h3>智能搜索系统</h3>
              <p>支持实时搜索、最近访问记录、键盘快捷键(Ctrl+K)等高级功能</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🎨</div>
            <div class="feature-content">
              <h3>统一图标系统</h3>
              <p>使用SVG图标替代emoji，提供一致的视觉语言和更好的可扩展性</p>
            </div>
          </div>
          <div class="feature-item">
            <div class="feature-icon">⚡</div>
            <div class="feature-content">
              <h3>流畅交互动效</h3>
              <p>精心设计的hover效果、过渡动画和微交互，提升用户体验</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互演示 -->
      <div class="demo-card">
        <h2 class="card-title">🎮 交互演示</h2>
        <div class="interaction-demo">
          <div class="demo-section">
            <h3>菜单状态切换</h3>
            <div class="demo-buttons">
              <button @click="toggleSidebar" class="demo-button primary">
                {{ sidebarCollapsed ? '展开' : '收起' }}侧边栏
              </button>
              <button @click="toggleTheme" class="demo-button secondary">
                切换到{{ isDarkMode ? '浅色' : '深色' }}主题
              </button>
            </div>
          </div>

          <div class="demo-section">
            <h3>搜索功能测试</h3>
            <p class="demo-tip">
              💡 尝试按 <kbd>Ctrl</kbd> + <kbd>K</kbd> 快速打开搜索，或在侧边栏搜索框中输入关键词
            </p>
          </div>

          <div class="demo-section">
            <h3>菜单项交互</h3>
            <p class="demo-tip">
              🖱️ 将鼠标悬停在侧边栏菜单项上，观察精美的hover效果和动画
            </p>
          </div>
        </div>
      </div>

      <!-- 技术实现 -->
      <div class="demo-card">
        <h2 class="card-title">⚙️ 技术实现</h2>
        <div class="tech-list">
          <div class="tech-item">
            <span class="tech-label">框架</span>
            <span class="tech-value">Vue 3 + Composition API</span>
          </div>
          <div class="tech-item">
            <span class="tech-label">样式</span>
            <span class="tech-value">CSS Variables + Tailwind CSS</span>
          </div>
          <div class="tech-item">
            <span class="tech-label">图标</span>
            <span class="tech-value">自定义SVG图标系统</span>
          </div>
          <div class="tech-item">
            <span class="tech-label">动画</span>
            <span class="tech-value">CSS Transitions + Transform</span>
          </div>
          <div class="tech-item">
            <span class="tech-label">主题</span>
            <span class="tech-value">深色/浅色模式切换</span>
          </div>
          <div class="tech-item">
            <span class="tech-label">响应式</span>
            <span class="tech-value">移动端适配 + 手势支持</span>
          </div>
        </div>
      </div>

      <!-- 性能指标 -->
      <div class="demo-card">
        <h2 class="card-title">📊 性能指标</h2>
        <div class="metrics-grid">
          <div class="metric-item">
            <div class="metric-value">< 100ms</div>
            <div class="metric-label">动画响应时间</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">60fps</div>
            <div class="metric-label">动画帧率</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">< 5KB</div>
            <div class="metric-label">图标资源大小</div>
          </div>
          <div class="metric-item">
            <div class="metric-value">AAA</div>
            <div class="metric-label">无障碍等级</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用指南 -->
    <div class="demo-card usage-guide">
      <h2 class="card-title">📖 使用指南</h2>
      <div class="guide-content">
        <div class="guide-section">
          <h3>🔍 搜索功能</h3>
          <ul>
            <li>使用 <kbd>Ctrl</kbd> + <kbd>K</kbd> 快速打开搜索</li>
            <li>支持菜单名称和路径搜索</li>
            <li>自动保存最近搜索记录</li>
            <li>使用方向键导航，回车选择</li>
          </ul>
        </div>
        <div class="guide-section">
          <h3>🎯 菜单导航</h3>
          <ul>
            <li>点击左上角按钮收起/展开侧边栏</li>
            <li>收起状态下仍可查看图标和tooltip</li>
            <li>支持键盘Tab导航</li>
            <li>移动端支持手势滑动</li>
          </ul>
        </div>
        <div class="guide-section">
          <h3>🎨 主题切换</h3>
          <ul>
            <li>点击右上角月亮/太阳图标切换主题</li>
            <li>自动保存用户偏好设置</li>
            <li>所有组件支持深色/浅色模式</li>
            <li>平滑的主题切换动画</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useSidebar } from '@/components/ui/sidebar'
import { useThemeStore } from '@/stores/theme'

const { isCollapsed, toggleCollapse } = useSidebar()
const themeStore = useThemeStore()

const sidebarCollapsed = computed(() => isCollapsed.value)
const isDarkMode = computed(() => themeStore.isDarkMode)

const toggleSidebar = () => {
  toggleCollapse()
}

const toggleTheme = () => {
  themeStore.toggleTheme()
}
</script>

<style scoped>
.menu-demo-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;
}

.demo-title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 0.5rem;
}

.demo-subtitle {
  font-size: 1.125rem;
  color: var(--color-neutral-600);
  font-weight: 500;
}

.dark .demo-subtitle {
  color: var(--color-neutral-400);
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.demo-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: var(--radius-2xl);
  padding: 2rem;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  transition: all var(--duration-300) var(--ease-out);
}

.demo-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.dark .demo-card {
  background: rgba(15, 23, 42, 0.8);
  border-color: rgba(51, 65, 85, 0.8);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-neutral-900);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dark .card-title {
  color: var(--color-neutral-100);
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.feature-item {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.feature-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-secondary-100));
  border-radius: var(--radius-xl);
}

.dark .feature-icon {
  background: linear-gradient(135deg, var(--color-primary-900), var(--color-secondary-900));
}

.feature-content h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: 0.5rem;
}

.dark .feature-content h3 {
  color: var(--color-neutral-100);
}

.feature-content p {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  line-height: 1.6;
}

.dark .feature-content p {
  color: var(--color-neutral-400);
}

.demo-section {
  margin-bottom: 2rem;
}

.demo-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: 1rem;
}

.dark .demo-section h3 {
  color: var(--color-neutral-100);
}

.demo-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.demo-button {
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 500;
  transition: all var(--duration-200) var(--ease-out);
  border: none;
  cursor: pointer;
}

.demo-button.primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-secondary-500));
  color: white;
}

.demo-button.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.25);
}

.demo-button.secondary {
  background: var(--color-neutral-100);
  color: var(--color-neutral-700);
  border: 1px solid var(--color-neutral-300);
}

.demo-button.secondary:hover {
  background: var(--color-neutral-200);
  transform: translateY(-2px);
}

.dark .demo-button.secondary {
  background: var(--color-neutral-800);
  color: var(--color-neutral-200);
  border-color: var(--color-neutral-600);
}

.dark .demo-button.secondary:hover {
  background: var(--color-neutral-700);
}

.demo-tip {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  line-height: 1.6;
  padding: 1rem;
  background: var(--color-neutral-50);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--color-primary-500);
}

.dark .demo-tip {
  color: var(--color-neutral-400);
  background: var(--color-neutral-800);
}

.demo-tip kbd {
  background: var(--color-neutral-200);
  color: var(--color-neutral-700);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-family: inherit;
  border: 1px solid var(--color-neutral-300);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .demo-tip kbd {
  background: var(--color-neutral-700);
  color: var(--color-neutral-200);
  border-color: var(--color-neutral-600);
}

.tech-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tech-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: var(--color-neutral-50);
  border-radius: var(--radius-lg);
}

.dark .tech-item {
  background: var(--color-neutral-800);
}

.tech-label {
  font-weight: 500;
  color: var(--color-neutral-600);
}

.dark .tech-label {
  color: var(--color-neutral-400);
}

.tech-value {
  font-weight: 600;
  color: var(--color-neutral-900);
}

.dark .tech-value {
  color: var(--color-neutral-100);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.metric-item {
  text-align: center;
  padding: 1.5rem 1rem;
  background: linear-gradient(135deg, var(--color-primary-50), var(--color-secondary-50));
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-primary-200);
}

.dark .metric-item {
  background: linear-gradient(135deg, var(--color-primary-900), var(--color-secondary-900));
  border-color: var(--color-primary-700);
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 800;
  color: var(--color-primary-600);
  margin-bottom: 0.5rem;
}

.dark .metric-value {
  color: var(--color-primary-400);
}

.metric-label {
  font-size: 0.875rem;
  color: var(--color-neutral-600);
  font-weight: 500;
}

.dark .metric-label {
  color: var(--color-neutral-400);
}

.usage-guide {
  grid-column: 1 / -1;
}

.guide-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.guide-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-neutral-900);
  margin-bottom: 1rem;
}

.dark .guide-section h3 {
  color: var(--color-neutral-100);
}

.guide-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.guide-section li {
  padding: 0.5rem 0;
  color: var(--color-neutral-600);
  font-size: 0.875rem;
  line-height: 1.6;
  position: relative;
  padding-left: 1.5rem;
}

.dark .guide-section li {
  color: var(--color-neutral-400);
}

.guide-section li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--color-primary-500);
  font-weight: bold;
}

.guide-section li kbd {
  background: var(--color-neutral-200);
  color: var(--color-neutral-700);
  padding: 0.125rem 0.375rem;
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-family: inherit;
  border: 1px solid var(--color-neutral-300);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark .guide-section li kbd {
  background: var(--color-neutral-700);
  color: var(--color-neutral-200);
  border-color: var(--color-neutral-600);
}
</style>
