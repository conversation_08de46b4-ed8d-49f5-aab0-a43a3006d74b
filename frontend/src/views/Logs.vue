<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="执行日志"
    description="查看定时任务执行记录和状态"
  >
    <template #actions>
      <ActionButton variant="secondary" icon="📤">
        导出日志
      </ActionButton>
      <ActionButton variant="secondary" icon="🔄">
        刷新
      </ActionButton>
    </template>
  </PageHeader>

  <!-- 搜索区域 -->
  <SearchArea>
    <template #filters>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">任务名称</label>
        <select
          v-model="searchForm.taskName"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="">请选择任务</option>
          <option value="数据同步任务">数据同步任务</option>
          <option value="报表生成任务">报表生成任务</option>
          <option value="日志清理任务">日志清理任务</option>
          <option value="系统监控检查">系统监控检查</option>
        </select>
      </div>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">执行状态</label>
        <select
          v-model="searchForm.status"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="">请选择状态</option>
          <option value="成功">成功</option>
          <option value="失败">失败</option>
        </select>
      </div>
    </template>

    <template #dateFilters>
      <div class="min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">开始时间</label>
        <input
          v-model="searchForm.startDate"
          type="date"
          class="w-full sm:w-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
      <div class="min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">结束时间</label>
        <input
          v-model="searchForm.endDate"
          type="date"
          class="w-full sm:w-40 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
    </template>

    <template #searchActions>
      <ActionButton variant="search" icon="🔍" @click="handleSearch">
        搜索
      </ActionButton>
      <ActionButton variant="reset" icon="🔄" @click="handleReset">
        重置
      </ActionButton>
    </template>
  </SearchArea>

  <!-- 统计卡片 -->
  <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 border-l-4 border-l-green-500 hover:shadow-md transition-all">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ stats.success }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">执行成功</div>
        </div>
        <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
          <span class="text-2xl">✅</span>
        </div>
      </div>
    </div>

    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 border-l-4 border-l-red-500 hover:shadow-md transition-all">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ stats.failed }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">执行失败</div>
        </div>
        <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center">
          <span class="text-2xl">❌</span>
        </div>
      </div>
    </div>

    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 border-l-4 border-l-yellow-500 hover:shadow-md transition-all">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ stats.avgTime }}ms</div>
          <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">平均耗时</div>
        </div>
        <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center">
          <span class="text-2xl">⏱️</span>
        </div>
      </div>
    </div>

    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 border-l-4 border-l-blue-500 hover:shadow-md transition-all">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-3xl font-bold text-gray-900 dark:text-white">{{ stats.successRate }}%</div>
          <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">成功率</div>
        </div>
        <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
          <span class="text-2xl">📊</span>
        </div>
      </div>
    </div>
  </div>

  <!-- 日志列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">任务名称</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">执行状态</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">开始时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">结束时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">执行耗时</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">执行结果</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr v-for="log in logs" :key="log.id" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <td class="px-6 py-4">
              <div class="flex items-center space-x-3">
                <div :class="log.status === '成功' ? 'w-2 h-2 bg-green-500 rounded-full' : 'w-2 h-2 bg-red-500 rounded-full'"></div>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ log.taskName }}</span>
              </div>
            </td>
            <td class="px-6 py-4">
              <span :class="log.status === '成功' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'" 
                    class="text-sm font-medium">
                {{ log.status }}
              </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ log.startTime }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ log.endTime }}</td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ log.duration }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">{{ log.result }}</td>
            <td class="px-6 py-4 text-sm">
              <button class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">详情</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
      <div class="text-sm text-gray-500 dark:text-gray-400">
        Total {{ totalCount }} / {{ pageSize }}/page
      </div>
      <div class="flex items-center space-x-2">
        <button class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
          &lt;
        </button>
        <button class="px-3 py-1 bg-blue-600 text-white rounded text-sm">1</button>
        <span class="text-gray-500">...</span>
        <span class="text-sm text-gray-500 dark:text-gray-400 ml-4">Go to</span>
        <input type="number" class="w-16 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700" />
      </div>
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'

const searchForm = reactive({
  taskName: '',
  status: '',
  startDate: '',
  endDate: ''
})

const stats = reactive({
  success: 7,
  failed: 1,
  avgTime: 296250,
  successRate: 88
})

const totalCount = ref(8)
const pageSize = ref(20)

const logs = ref([
  {
    id: 1,
    taskName: '数据同步任务',
    status: '成功',
    startTime: '2024-06-24 02:00:00',
    endTime: '2024-06-24 02:05:00',
    duration: '300000ms',
    result: '成功同步1000条数据'
  },
  {
    id: 2,
    taskName: '报表生成任务',
    status: '成功',
    startTime: '2024-06-24 08:30:00',
    endTime: '2024-06-24 08:38:00',
    duration: '480000ms',
    result: '成功生成日报，发送给10个用户'
  },
  {
    id: 3,
    taskName: '数据同步任务',
    status: '失败',
    startTime: '2024-06-24 14:00:00',
    endTime: '2024-06-24 14:02:00',
    duration: '120000ms',
    result: '数据源连接超时'
  },
  {
    id: 4,
    taskName: '系统监控检查',
    status: '成功',
    startTime: '2024-06-24 17:45:00',
    endTime: '2024-06-24 17:45:30',
    duration: '30000ms',
    result: '系统运行正常，CPU使用率45%，内存使用率62%'
  },
  {
    id: 5,
    taskName: '报表生成任务',
    status: '成功',
    startTime: '2024-06-23 08:30:00',
    endTime: '2024-06-23 08:37:00',
    duration: '420000ms',
    result: '成功生成日报，发送给15个用户'
  },
  {
    id: 6,
    taskName: '数据同步任务',
    status: '成功',
    startTime: '2024-06-23 02:00:00',
    endTime: '2024-06-23 02:04:30',
    duration: '270000ms',
    result: '成功同步800条数据'
  },
  {
    id: 7,
    taskName: '系统监控检查',
    status: '成功',
    startTime: '2024-06-24 17:30:00',
    endTime: '2024-06-24 17:30:25',
    duration: '25000ms',
    result: '系统运行正常，CPU使用率38%，内存使用率58%'
  },
  {
    id: 8,
    taskName: '日志清理任务',
    status: '成功',
    startTime: '2024-06-23 03:00:00',
    endTime: '2024-06-23 03:12:00',
    duration: '720000ms',
    result: '清理完成，删除了500MB日志文件'
  }
])

const handleSearch = () => {
  console.log('搜索:', searchForm)
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
}
</script>
