<template>
<div class="space-y-6">
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- 在线充值 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all">
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
          <span class="text-blue-600 dark:text-blue-400">💳</span>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">在线充值</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">快速方便的充值方式</p>
        </div>
      </div>

      <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900 dark:text-white">${{ walletData.balance }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">当前余额</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900 dark:text-white">${{ walletData.consumed }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">历史消耗</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900 dark:text-white">${{ walletData.pending }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">待使用收益</div>
        </div>
      </div>

      <div class="mb-6">
        <h4 class="text-sm font-medium mb-3 text-gray-900 dark:text-white">兑换码充值</h4>
        <div class="flex items-center space-x-3">
          <input
            v-model="redeemCode"
            type="text"
            placeholder="请输入兑换码"
            class="flex-1 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
          <button @click="handleRedeem" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">兑换</button>
        </div>
      </div>
    </div>

    <!-- 邀请奖励 -->
    <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all">
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
          <span class="text-green-600 dark:text-green-400">👥</span>
        </div>
        <div>
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">邀请奖励</h3>
          <p class="text-sm text-gray-600 dark:text-gray-400">邀请好友获得额外奖励</p>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4 mb-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900 dark:text-white">${{ inviteData.totalReward }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">总收益</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ inviteData.inviteCount }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-400">邀请人数</div>
        </div>
      </div>

      <div class="mb-6">
        <h4 class="text-sm font-medium mb-3 text-gray-900 dark:text-white">邀请链接</h4>
        <div class="flex items-center space-x-3">
          <input
            :value="inviteLink"
            readonly
            class="flex-1 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-600 dark:text-gray-300 text-sm"
          />
          <button @click="copyInviteLink" class="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <span>📋</span>
            <span>复制</span>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 交易记录 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">交易记录</h3>
    </div>
    <div class="p-6">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="border-b border-gray-200 dark:border-gray-600">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">时间</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">类型</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">金额</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">余额</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">说明</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="record in transactionRecords" :key="record.id" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">{{ record.timestamp }}</td>
              <td class="px-4 py-4 text-sm">
                <span :class="record.type === 'recharge' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                  {{ record.type === 'recharge' ? '充值' : '消费' }}
                </span>
              </td>
              <td class="px-4 py-4 text-sm" :class="record.type === 'recharge' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'">
                {{ record.type === 'recharge' ? '+' : '-' }}${{ record.amount }}
              </td>
              <td class="px-4 py-4 text-sm text-gray-900 dark:text-white">${{ record.balance }}</td>
              <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">{{ record.description }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页组件 -->
      <Pagination
        :total="filteredRecords.length"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import Pagination from '@/components/Pagination.vue'

const redeemCode = ref('')
const inviteLink = ref('https://anyrouter.top/register?aff=eUfZ')

const walletData = ref({
  balance: '549.97',
  consumed: '0.03',
  pending: '0.00'
})

const inviteData = ref({
  totalReward: '450.00',
  inviteCount: 9
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

const allTransactionRecords = ref([
  {
    id: 1,
    timestamp: '2025-01-07 10:00:00',
    type: 'recharge',
    amount: '100.00',
    balance: '549.97',
    description: '兑换码充值'
  },
  {
    id: 2,
    timestamp: '2025-01-07 11:30:00',
    type: 'consume',
    amount: '0.03',
    balance: '549.94',
    description: 'API调用消费'
  },
  {
    id: 3,
    timestamp: '2025-01-06 15:20:00',
    type: 'recharge',
    amount: '450.00',
    balance: '549.97',
    description: '邀请奖励'
  },
  // 添加更多测试数据
  ...Array.from({ length: 20 }, (_, i) => ({
    id: i + 4,
    timestamp: `2025-01-${String(Math.floor(Math.random() * 30) + 1).padStart(2, '0')} ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`,
    type: Math.random() > 0.7 ? 'recharge' : 'consume',
    amount: (Math.random() * 100).toFixed(2),
    balance: (Math.random() * 1000).toFixed(2),
    description: Math.random() > 0.7 ? '充值' : 'API调用消费'
  }))
])

// 过滤后的交易记录
const filteredRecords = computed(() => {
  // 这里可以添加筛选逻辑
  return allTransactionRecords.value
})

// 当前页显示的交易记录
const transactionRecords = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredRecords.value.slice(start, end)
})

const handleRedeem = () => {
  if (redeemCode.value.trim()) {
    console.log('兑换码:', redeemCode.value)
    // 这里处理兑换逻辑
    redeemCode.value = ''
  }
}

const copyInviteLink = () => {
  navigator.clipboard.writeText(inviteLink.value)
  console.log('邀请链接已复制')
}

const handlePageChange = (page) => {
  pagination.currentPage = page
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.currentPage = 1
}
</script>
