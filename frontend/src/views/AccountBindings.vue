<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <PageHeader title="账号绑定" description="管理您的第三方账号绑定" />

    <!-- 当前绑定状态 -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">第三方账号绑定</h3>
      
      <div class="space-y-4">
        <!-- Google账号绑定 -->
        <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center space-x-3">
            <svg class="w-8 h-8" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">Google</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ googleBinding ? googleBinding.providerEmail : '未绑定' }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span v-if="googleBinding?.isPrimary" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              主要方式
            </span>
            <button
              v-if="googleBinding"
              @click="unbindAccount('google')"
              :disabled="isLoading"
              class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm disabled:opacity-50"
            >
              解绑
            </button>
            <button
              v-else
              @click="bindAccount('google')"
              :disabled="isLoading"
              class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm disabled:opacity-50"
            >
              绑定
            </button>
          </div>
        </div>

        <!-- GitHub账号绑定 -->
        <div class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center space-x-3">
            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            <div>
              <p class="text-sm font-medium text-gray-900 dark:text-white">GitHub</p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ githubBinding ? githubBinding.providerEmail : '未绑定' }}
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span v-if="githubBinding?.isPrimary" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              主要方式
            </span>
            <button
              v-if="githubBinding"
              @click="unbindAccount('github')"
              :disabled="isLoading"
              class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm disabled:opacity-50"
            >
              解绑
            </button>
            <button
              v-else
              @click="bindAccount('github')"
              :disabled="isLoading"
              class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm disabled:opacity-50"
            >
              绑定
            </button>
          </div>
        </div>
      </div>

      <!-- 设置主要登录方式 -->
      <div v-if="oauthBindings.length > 0" class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">主要登录方式</h4>
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          选择您的主要登录方式，这将影响登录时的默认选项。
        </p>
        <div class="space-y-2">
          <label class="flex items-center">
            <input
              type="radio"
              name="primaryProvider"
              value="local"
              :checked="!hasPrimaryOAuth"
              @change="setPrimaryProvider('local')"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">用户名密码登录</span>
          </label>
          <label v-for="binding in oauthBindings" :key="binding.provider" class="flex items-center">
            <input
              type="radio"
              name="primaryProvider"
              :value="binding.provider"
              :checked="binding.isPrimary === 1"
              @change="setPrimaryProvider(binding.provider)"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300 capitalize">{{ binding.provider }} 登录</span>
          </label>
        </div>
      </div>
    </div>

    <!-- 安全提示 -->
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            安全提示
          </h3>
          <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
            <ul class="list-disc list-inside space-y-1">
              <li>绑定第三方账号可以让您使用多种方式登录</li>
              <li>解绑账号前请确保您还有其他登录方式</li>
              <li>建议至少保留一种登录方式以防止账号无法访问</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { authApi, handleApiResponse } from '@/utils/api'
import { useUserStore } from '@/stores/user'
import PageHeader from '@/components/PageHeader.vue'

const userStore = useUserStore()
const isLoading = ref(false)
const oauthBindings = ref([])

// 计算属性
const googleBinding = computed(() => 
  oauthBindings.value.find(binding => binding.provider === 'google')
)

const githubBinding = computed(() => 
  oauthBindings.value.find(binding => binding.provider === 'github')
)

const hasPrimaryOAuth = computed(() => 
  oauthBindings.value.some(binding => binding.isPrimary === 1)
)

// 加载OAuth绑定信息
const loadOAuthBindings = async () => {
  try {
    const response = await authApi.getCurrentUser()
    const result = handleApiResponse(response)
    
    if (result.success && result.data.oauthBindings) {
      oauthBindings.value = result.data.oauthBindings
    }
  } catch (error) {
    console.error('加载OAuth绑定信息失败:', error)
  }
}

// 绑定账号
const bindAccount = (provider) => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  // 构建OAuth2授权URL
  const authUrl = `http://localhost:8080/oauth2/authorize/${provider}?redirect_uri=${encodeURIComponent(window.location.origin + '/auth/callback')}`
  
  // 打开新窗口进行OAuth2授权
  const popup = window.open(
    authUrl,
    `${provider}-oauth-bind`,
    'width=500,height=600,scrollbars=yes,resizable=yes'
  )

  // 监听授权结果
  const checkClosed = setInterval(() => {
    if (popup.closed) {
      clearInterval(checkClosed)
      isLoading.value = false
    }
  }, 1000)

  // 监听来自弹窗的消息
  const messageListener = (event) => {
    if (event.origin !== window.location.origin) return
    
    if (event.data.type === 'OAUTH_SUCCESS') {
      clearInterval(checkClosed)
      popup.close()
      isLoading.value = false
      
      // 重新加载绑定信息
      loadOAuthBindings()
      
      window.removeEventListener('message', messageListener)
    } else if (event.data.type === 'OAUTH_ERROR') {
      clearInterval(checkClosed)
      popup.close()
      isLoading.value = false
      
      console.error('OAuth绑定失败:', event.data.error)
      alert('绑定失败: ' + event.data.error)
      
      window.removeEventListener('message', messageListener)
    }
  }

  window.addEventListener('message', messageListener)
}

// 解绑账号
const unbindAccount = async (provider) => {
  if (isLoading.value) return
  
  if (!confirm(`确定要解绑 ${provider} 账号吗？`)) {
    return
  }
  
  isLoading.value = true
  
  try {
    const response = await authApi.unbindOAuth2Account(provider)
    const result = handleApiResponse(response)
    
    if (result.success) {
      await loadOAuthBindings()
      alert('解绑成功')
    } else {
      alert('解绑失败: ' + result.message)
    }
  } catch (error) {
    console.error('解绑账号失败:', error)
    alert('解绑失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 设置主要登录方式
const setPrimaryProvider = async (provider) => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    const response = await authApi.setPrimaryProvider(provider)
    const result = handleApiResponse(response)
    
    if (result.success) {
      await loadOAuthBindings()
    } else {
      alert('设置失败: ' + result.message)
    }
  } catch (error) {
    console.error('设置主要登录方式失败:', error)
    alert('设置失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadOAuthBindings()
})
</script>
