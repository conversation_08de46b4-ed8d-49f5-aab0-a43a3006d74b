<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="任务管理"
    description="管理定时任务和调度配置"
  >
    <template #actions>
      <ActionButton variant="primary" icon="➕" @click="handleAddTask">
        新建任务
      </ActionButton>
      <ActionButton variant="secondary" icon="▶️">
        批量启动
      </ActionButton>
      <ActionButton variant="secondary" icon="⏸️">
        批量停止
      </ActionButton>
    </template>
  </PageHeader>

  <!-- 搜索筛选区域 -->
  <SearchArea>
    <template #filters>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">任务名称</label>
        <input
          v-model="searchForm.name"
          type="text"
          placeholder="请输入任务名称"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        />
      </div>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">任务分组</label>
        <select
          v-model="searchForm.group"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="">请选择任务分组</option>
          <option value="sync">sync</option>
          <option value="report">report</option>
          <option value="cleanup">cleanup</option>
          <option value="monitor">monitor</option>
        </select>
      </div>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">任务状态</label>
        <select
          v-model="searchForm.status"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="">请选择状态</option>
          <option value="enabled">启用</option>
          <option value="disabled">停用</option>
        </select>
      </div>
    </template>

    <template #searchActions>
      <ActionButton variant="search" @click="handleSearch">搜索</ActionButton>
      <ActionButton variant="reset" @click="handleReset">重置</ActionButton>
    </template>
  </SearchArea>

  <!-- 任务列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">定时任务列表</h3>
      <div class="flex space-x-3">
        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">➕ 新增任务</button>
        <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">📊 批量启用</button>
        <button class="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors">⏸️ 批量停止</button>
        <button class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors">🗑️ 批量删除</button>
      </div>
    </div>

    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              <input type="checkbox" class="rounded border-gray-300 dark:border-gray-600" />
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">任务ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">任务名称</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">任务分组</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">描述</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Cron表达式</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">状态</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">优先级</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">下次执行时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">上次执行时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">创建时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <tr v-for="task in tasks" :key="task.id" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
            <td class="px-6 py-4">
              <input type="checkbox" class="rounded border-gray-300 dark:border-gray-600" />
            </td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ task.id }}</td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ task.name }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ task.group }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">{{ task.description }}</td>
            <td class="px-6 py-4 text-sm font-mono text-gray-900 dark:text-white">{{ task.cron }}</td>
            <td class="px-6 py-4">
              <span :class="task.status === '启用' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'" 
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                {{ task.status }}
              </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">{{ task.priority }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ task.nextExecution }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ task.lastExecution }}</td>
            <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">{{ task.createdAt }}</td>
            <td class="px-6 py-4 text-sm space-x-2">
              <button
                @click="handleViewTask(task)"
                class="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                title="查看详情"
              >
                👁️
              </button>
              <button
                @click="handleEditTask(task)"
                class="text-green-600 hover:text-green-800 dark:text-green-400"
                title="编辑"
              >
                ✏️
              </button>
              <button
                @click="handleToggleStatus(task)"
                :class="task.status === 'enabled' ? 'text-yellow-600 hover:text-yellow-800' : 'text-green-600 hover:text-green-800'"
                :title="task.status === 'enabled' ? '停用任务' : '启用任务'"
              >
                {{ task.status === 'enabled' ? '⏸️' : '▶️' }}
              </button>
              <button
                @click="handleDeleteTask(task)"
                class="text-red-600 hover:text-red-800 dark:text-red-400"
                title="删除"
              >
                🗑️
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页组件 -->
    <Pagination
      :total="crud.pagination.total"
      :current-page="pagination.currentPage"
      :page-size="pagination.pageSize"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
    />
  </div>

  <!-- 任务详情弹窗 -->
  <DetailModal
    v-if="taskTemplate"
    :visible="showDetailModal"
    :title="detailModalMode === 'create' ? '新建任务' : detailModalMode === 'edit' ? '编辑任务' : '任务详情'"
    :icon="taskTemplate.icon"
    :mode="detailModalMode"
    :data="detailModalData"
    :template="taskTemplate"
    @update:visible="showDetailModal = $event"
    @save="saveTask"
    @close="closeDetailModal"
  >
    <TaskDetail
      v-model="detailModalData"
      :mode="detailModalMode"
      :template="taskTemplate"
    />
  </DetailModal>

  <!-- 确认删除弹窗 -->
  <ConfirmModal
    :show="showConfirmModal"
    :title="confirmModalData.title"
    :message="confirmModalData.message"
    :confirm-text="confirmModalData.confirmText"
    :cancel-text="confirmModalData.cancelText"
    @confirm="confirmDelete"
    @close="closeConfirmModal"
  />
</div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import Pagination from '@/components/Pagination.vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'
import DetailModal from '@/components/ui/DetailModal.vue'
import TaskDetail from '@/components/details/TaskDetail.vue'
import ConfirmModal from '@/components/ConfirmModal.vue'
import { useCrudOperations } from '@/composables/useCrudOperations'
import { useToast } from '@/composables/useToast'
import { getTemplateConfig } from '@/config/detailTemplates'

const searchForm = reactive({
  name: '',
  group: '',
  status: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

const allTasks = ref([
  {
    id: 1,
    name: '数据同步任务',
    group: 'sync',
    description: '从外部系统同步用户数据...',
    cron: '0 0 2 * * ?',
    status: '启用',
    priority: 1,
    nextExecution: '2024-06-25 02:00:00',
    lastExecution: '2024-06-24 02:00:00',
    createdAt: '2024-06-24 10:00:00'
  },
  {
    id: 2,
    name: '报表生成任务',
    group: 'report',
    description: '生成日常业务报表，包括...',
    cron: '0 30 8 * * MON...',
    status: '启用',
    priority: 2,
    nextExecution: '2024-06-25 08:30:00',
    lastExecution: '2024-06-24 08:30:00',
    createdAt: '2024-06-24 09:00:00'
  },
  {
    id: 3,
    name: '日志清理任务',
    group: 'cleanup',
    description: '清理30天前的系统日志...',
    cron: '0 0 3 * * ?',
    status: '停用',
    priority: 3,
    nextExecution: '',
    lastExecution: '2024-06-23 03:00:00',
    createdAt: '2024-06-20 16:00:00'
  },
  {
    id: 4,
    name: '系统监控检查',
    group: 'monitor',
    description: '检查系统资源使用情况和...',
    cron: '0 */15 * * * ?',
    status: '启用',
    priority: 1,
    nextExecution: '2024-06-24 18:00:00',
    lastExecution: '2024-06-24 17:45:00',
    createdAt: '2024-06-22 14:00:00'
  }
])

// 过滤后的任务列表
const filteredTasks = computed(() => {
  return allTasks.value.filter(task => {
    const nameMatch = !searchForm.name || task.name.toLowerCase().includes(searchForm.name.toLowerCase())
    const groupMatch = !searchForm.group || task.group === searchForm.group
    const statusMatch = !searchForm.status || task.status === searchForm.status
    return nameMatch && groupMatch && statusMatch
  })
})

// 使用CRUD操作的数据
const tasks = computed(() => crud.data.value || [])

const handleSearch = async () => {
  pagination.currentPage = 1
  await loadTasks()
}

const handleReset = async () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.currentPage = 1
  await loadTasks()
}

const handlePageChange = async (page) => {
  pagination.currentPage = page
  await loadTasks()
}

const handlePageSizeChange = async (pageSize) => {
  pagination.pageSize = pageSize
  pagination.currentPage = 1
  await loadTasks()
}

// 初始化CRUD操作
const toast = useToast()
const crud = useCrudOperations('tasks')
const taskTemplate = getTemplateConfig('tasks')

// 弹窗状态
const showDetailModal = ref(false)
const showConfirmModal = ref(false)
const detailModalMode = ref('view')
const detailModalData = ref({})
const confirmModalData = ref({})

// 初始化数据
onMounted(async () => {
  await loadTasks()
})

// 加载任务数据
const loadTasks = async () => {
  try {
    await crud.fetchList({
      name: searchForm.name,
      group: searchForm.group,
      status: searchForm.status,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })

    pagination.currentPage = crud.pagination.currentPage
  } catch (error) {
    console.error('加载任务列表失败:', error)
  }
}

const handleAddTask = () => {
  detailModalData.value = {
    name: '',
    group: '',
    description: '',
    cronExpression: '',
    status: 'enabled',
    priority: 1
  }
  detailModalMode.value = 'create'
  showDetailModal.value = true
}

const handleViewTask = async (task) => {
  detailModalMode.value = 'view'
  showDetailModal.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(task.id)
    detailModalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    detailModalData.value = { ...task }
  }
}

const handleEditTask = async (task) => {
  detailModalMode.value = 'edit'
  showDetailModal.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(task.id)
    detailModalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    detailModalData.value = { ...task }
  }
}

const handleDeleteTask = (task) => {
  confirmModalData.value = {
    title: '确认删除任务',
    message: `确定要删除任务 "${task.name}" 吗？\n\n删除后将无法恢复，请谨慎操作。`,
    confirmText: '删除',
    cancelText: '取消',
    task: task
  }
  showConfirmModal.value = true
}

// 任务状态切换
const handleToggleStatus = async (task) => {
  try {
    const newStatus = task.status === 'enabled' ? 'disabled' : 'enabled'
    await crud.update(task.id, { ...task, status: newStatus })
    await loadTasks()
    toast.success(`任务已${newStatus === 'enabled' ? '启用' : '停用'}`)
  } catch (error) {
    console.error('切换任务状态失败:', error)
    toast.error('切换任务状态失败：' + (error.message || '未知错误'))
  }
}

// 确认删除
const confirmDelete = async () => {
  try {
    const task = confirmModalData.value.task
    await crud.remove(task.id)
    showConfirmModal.value = false
    await loadTasks()
    toast.success(`任务 "${task.name}" 删除成功`)
  } catch (error) {
    console.error('删除任务失败:', error)
    toast.error('删除任务失败：' + (error.message || '未知错误'))
  }
}

// 保存任务
const saveTask = async (data) => {
  try {
    if (detailModalMode.value === 'create') {
      await crud.create(data)
      toast.success('任务创建成功')
    } else if (detailModalMode.value === 'edit') {
      await crud.update(detailModalData.value.id, data)
      toast.success('任务更新成功')
    }

    showDetailModal.value = false
    await loadTasks()
  } catch (error) {
    console.error('保存任务失败:', error)
    toast.error('保存任务失败：' + (error.message || '未知错误'))
  }
}

// 关闭弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  detailModalData.value = {}
}

const closeConfirmModal = () => {
  showConfirmModal.value = false
  confirmModalData.value = {}
}
</script>
