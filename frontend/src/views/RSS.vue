<template>
  <div class="space-y-6">
    <!-- 页面头部 -->
    <PageHeader
      title="RSS管理"
      description="管理 AI 学习资源和技术资讯订阅"
    >
      <template #actions>
        <ActionButton variant="primary" icon="➕" @click="handleAddSubscription">
          添加订阅
        </ActionButton>
        <ActionButton variant="secondary" icon="🔄">
          刷新全部
        </ActionButton>
      </template>
    </PageHeader>

    <!-- RSS 统计 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
        <div class="flex items-center">
          <span class="text-2xl mr-3">📡</span>
          <div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">订阅源</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">24</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
        <div class="flex items-center">
          <span class="text-2xl mr-3">📰</span>
          <div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">今日更新</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">156</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
        <div class="flex items-center">
          <span class="text-2xl mr-3">⭐</span>
          <div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">收藏文章</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">89</p>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all">
        <div class="flex items-center">
          <span class="text-2xl mr-3">🔄</span>
          <div>
            <p class="text-gray-600 dark:text-gray-400 text-sm">活跃订阅</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">18</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 订阅源管理 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700 shadow-sm">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">订阅源管理</h2>
      <div class="space-y-4">
        <!-- 订阅源项目 -->
        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold">AI</span>
            </div>
            <div>
              <h3 class="text-gray-900 dark:text-white font-medium">AI Research Papers</h3>
              <p class="text-gray-600 dark:text-gray-400 text-sm">最新的 AI 研究论文和技术进展</p>
              <p class="text-gray-500 dark:text-gray-500 text-xs">最后更新: 2小时前 • 12篇新文章</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">活跃</span>
            <button class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">⚙️</button>
            <button class="text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400">🗑️</button>
          </div>
        </div>

        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold">ML</span>
            </div>
            <div>
              <h3 class="text-gray-900 dark:text-white font-medium">Machine Learning News</h3>
              <p class="text-gray-600 dark:text-gray-400 text-sm">机器学习领域的最新资讯和教程</p>
              <p class="text-gray-500 dark:text-gray-500 text-xs">最后更新: 1天前 • 8篇新文章</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">活跃</span>
            <button class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">⚙️</button>
            <button class="text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400">🗑️</button>
          </div>
        </div>

        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold">DL</span>
            </div>
            <div>
              <h3 class="text-gray-900 dark:text-white font-medium">Deep Learning Blog</h3>
              <p class="text-gray-600 dark:text-gray-400 text-sm">深度学习技术博客和实践案例</p>
              <p class="text-gray-500 dark:text-gray-500 text-xs">最后更新: 3天前 • 5篇新文章</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span class="bg-yellow-600 text-white px-2 py-1 rounded text-xs">暂停</span>
            <button class="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white">⚙️</button>
            <button class="text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400">🗑️</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新文章 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
      <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">最新文章</h2>
      <div class="space-y-4">
        <div class="border-l-4 border-blue-500 pl-4">
          <h3 class="text-gray-900 dark:text-white font-medium mb-1">Transformer 架构的最新改进</h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">探讨了 Transformer 模型在效率和性能方面的最新优化方法...</p>
          <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-500">
            <span>AI Research Papers</span>
            <span>2小时前</span>
            <button class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">阅读全文</button>
            <button class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300">⭐ 收藏</button>
          </div>
        </div>

        <div class="border-l-4 border-purple-500 pl-4">
          <h3 class="text-gray-900 dark:text-white font-medium mb-1">大语言模型的实际应用案例</h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">分析了 GPT 系列模型在不同行业中的成功应用实例...</p>
          <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-500">
            <span>Machine Learning News</span>
            <span>5小时前</span>
            <button class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">阅读全文</button>
            <button class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300">⭐ 收藏</button>
          </div>
        </div>

        <div class="border-l-4 border-green-500 pl-4">
          <h3 class="text-gray-900 dark:text-white font-medium mb-1">计算机视觉的新突破</h3>
          <p class="text-gray-600 dark:text-gray-400 text-sm mb-2">介绍了最新的图像识别和处理技术的重大进展...</p>
          <div class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-500">
            <span>Deep Learning Blog</span>
            <span>1天前</span>
            <button class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">阅读全文</button>
            <button class="text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300">⭐ 收藏</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import PageHeader from '@/components/PageHeader.vue'
import ActionButton from '@/components/ActionButton.vue'

const handleAddSubscription = () => {
  console.log('添加订阅')
}
</script>
