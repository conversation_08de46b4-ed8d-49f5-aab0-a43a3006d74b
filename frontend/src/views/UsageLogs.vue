<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="使用日志"
    description="查看API调用记录和使用统计"
  >
    <template #actions>
      <ActionButton variant="secondary" icon="📤">
        导出日志
      </ActionButton>
      <ActionButton variant="secondary" icon="🔄">
        刷新
      </ActionButton>
    </template>
  </PageHeader>

  <!-- 统计信息 -->
  <div class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
    <span class="text-sm text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900 px-3 py-1 rounded">消耗额度: $0.03</span>
    <span class="text-sm text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900 px-3 py-1 rounded">RPM: 0</span>
    <span class="text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded">TPM: 0</span>
  </div>

  <!-- 搜索筛选区域 -->
  <SearchArea>
    <template #filters>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API名称</label>
        <select
          v-model="searchForm.api"
          class="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white"
        >
          <option value="">全部API</option>
          <option value="claude">Claude</option>
          <option value="openai">OpenAI</option>
        </select>
      </div>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态</label>
        <select
          v-model="searchForm.status"
          class="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white"
        >
          <option value="">全部状态</option>
          <option value="success">成功</option>
          <option value="error">失败</option>
        </select>
      </div>
    </template>

    <template #dateFilters>
      <div class="min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">开始时间</label>
        <input
          v-model="searchForm.startDate"
          type="date"
          class="w-full sm:w-40 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white"
        />
      </div>
      <div class="min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">结束时间</label>
        <input
          v-model="searchForm.endDate"
          type="date"
          class="w-full sm:w-40 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white"
        />
      </div>
    </template>

    <template #searchActions>
      <ActionButton variant="search" icon="🔍" @click="handleSearch">
        搜索
      </ActionButton>
      <ActionButton variant="reset" icon="🔄" @click="handleReset">
        重置
      </ActionButton>
    </template>
  </SearchArea>

  <!-- 日志列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <div class="p-6">
      <div class="space-y-4">
        <div v-for="log in logs" :key="log.id" class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 transition-colors hover:bg-gray-100 dark:hover:bg-gray-600">
          <div class="flex items-center space-x-4">
            <div :class="log.status === 'success' ? 'w-2 h-2 bg-green-500 rounded-full' : 'w-2 h-2 bg-red-500 rounded-full'"></div>
            <div>
              <div class="text-sm font-medium text-gray-900 dark:text-white">
                {{ log.title }}
              </div>
              <div class="text-xs text-gray-500 dark:text-gray-400">
                {{ log.timestamp }}
              </div>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span :class="log.status === 'success' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'" class="text-sm">
              {{ log.status === 'success' ? '成功' : '失败' }}
            </span>
            <span class="text-sm text-gray-600 dark:text-gray-300">${{ log.cost }}</span>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <Pagination
        :total="filteredLogs.length"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import Pagination from '@/components/Pagination.vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'

const searchForm = reactive({
  api: '',
  status: '',
  startDate: '',
  endDate: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

const allLogs = ref([
  {
    id: 1,
    title: 'API调用 #101',
    timestamp: '2025-07-07 11:25:33',
    status: 'success',
    cost: '0.001'
  },
  {
    id: 2,
    title: 'API调用 #102',
    timestamp: '2025-07-07 12:30:45',
    status: 'success',
    cost: '0.002'
  },
  {
    id: 3,
    title: 'API调用 #103',
    timestamp: '2025-07-07 13:15:22',
    status: 'error',
    cost: '0.000'
  },
  {
    id: 4,
    title: 'API调用 #104',
    timestamp: '2025-07-07 14:45:18',
    status: 'success',
    cost: '0.003'
  },
  {
    id: 5,
    title: 'API调用 #105',
    timestamp: '2025-07-07 15:20:55',
    status: 'success',
    cost: '0.001'
  },
  // 添加更多测试数据
  ...Array.from({ length: 30 }, (_, i) => ({
    id: i + 6,
    title: `API调用 #${i + 106}`,
    timestamp: `2025-07-07 ${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
    status: Math.random() > 0.8 ? 'error' : 'success',
    cost: (Math.random() * 0.01).toFixed(3)
  }))
])

// 过滤后的日志列表
const filteredLogs = computed(() => {
  return allLogs.value.filter(log => {
    const apiMatch = !searchForm.api || log.title.toLowerCase().includes(searchForm.api.toLowerCase())
    const statusMatch = !searchForm.status || log.status === searchForm.status
    return apiMatch && statusMatch
  })
})

// 当前页显示的日志列表
const logs = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredLogs.value.slice(start, end)
})

const handleSearch = () => {
  pagination.currentPage = 1 // 搜索时重置到第一页
}

const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.currentPage = 1 // 重置时回到第一页
}

const handlePageChange = (page) => {
  pagination.currentPage = page
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.currentPage = 1
}
</script>
