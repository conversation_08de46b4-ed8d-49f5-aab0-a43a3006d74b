<template>
<div class="space-y-6">
  <!-- 页面头部 -->
  <PageHeader
    title="API令牌管理"
    description="管理和配置API访问令牌"
  >
    <template #actions>
      <ActionButton variant="primary" icon="➕" @click="handleAddToken">
        添加令牌
      </ActionButton>
      <ActionButton variant="secondary" icon="📤">
        导出
      </ActionButton>
    </template>
  </PageHeader>

  <!-- 搜索筛选区域 -->
  <SearchArea>
    <template #filters>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">令牌名称</label>
        <input
          v-model="searchForm.name"
          type="text"
          placeholder="请输入令牌名称"
          class="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
        />
      </div>
      <div class="flex-1 min-w-0">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态</label>
        <select
          v-model="searchForm.status"
          class="w-full px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 dark:text-white"
        >
          <option value="">全部状态</option>
          <option value="enabled">已启用</option>
          <option value="disabled">已禁用</option>
        </select>
      </div>
    </template>

    <template #searchActions>
      <ActionButton variant="search" icon="🔍" @click="handleSearch">
        搜索
      </ActionButton>
      <ActionButton variant="reset" icon="🔄" @click="handleReset">
        重置
      </ActionButton>
    </template>
  </SearchArea>

  <!-- 令牌列表 -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <div class="p-6">
      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="border-b border-gray-200 dark:border-gray-600">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">名称</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">状态</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">已用额度</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">剩余额度</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">创建时间</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="token in tokens" :key="token.id" class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <td class="px-4 py-4 text-sm font-medium text-gray-900 dark:text-white">{{ token.name }}</td>
              <td class="px-4 py-4">
                <span :class="token.status === 'enabled' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ token.status === 'enabled' ? '✅ 已启用' : '❌ 已禁用' }}
                </span>
              </td>
              <td class="px-4 py-4 text-sm text-gray-600 dark:text-gray-300">💰 ${{ token.used }}</td>
              <td class="px-4 py-4 text-sm text-gray-600 dark:text-gray-300">💰 ${{ token.remaining }}</td>
              <td class="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">{{ token.createdAt }}</td>
              <td class="px-4 py-4 text-sm">
                <div class="flex items-center space-x-2">
                  <button class="px-3 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700 transition-colors">复制</button>
                  <button class="px-3 py-1 bg-green-600 text-white rounded text-xs hover:bg-green-700 transition-colors">编辑</button>
                  <button class="px-3 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors">删除</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页组件 -->
      <Pagination
        :total="filteredTokens.length"
        :current-page="pagination.currentPage"
        :page-size="pagination.pageSize"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import Pagination from '@/components/Pagination.vue'
import PageHeader from '@/components/PageHeader.vue'
import SearchArea from '@/components/SearchArea.vue'
import ActionButton from '@/components/ActionButton.vue'
import DetailModal from '@/components/ui/DetailModal.vue'
import ConfirmModal from '@/components/ConfirmModal.vue'
import { useCrudOperations } from '@/composables/useCrudOperations'
import { useToast } from '@/composables/useToast'

const searchForm = reactive({
  name: '',
  status: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 10
})

const allTokens = ref([
  {
    id: 1,
    name: 'claude',
    status: 'enabled',
    used: '0.03',
    remaining: '0.97',
    createdAt: '2025-01-01 10:00:00'
  },
  {
    id: 2,
    name: 'openai',
    status: 'enabled',
    used: '0.15',
    remaining: '4.85',
    createdAt: '2025-01-02 14:30:00'
  },
  {
    id: 3,
    name: 'test-token',
    status: 'disabled',
    used: '0.00',
    remaining: '1.00',
    createdAt: '2025-01-03 09:15:00'
  },
  // 添加更多测试数据
  ...Array.from({ length: 20 }, (_, i) => ({
    id: i + 4,
    name: `token-${i + 4}`,
    status: i % 2 === 0 ? 'enabled' : 'disabled',
    used: (Math.random() * 10).toFixed(2),
    remaining: (Math.random() * 100).toFixed(2),
    createdAt: `2025-01-${String(i + 4).padStart(2, '0')} 10:00:00`
  }))
])

// 过滤后的令牌列表
const filteredTokens = computed(() => {
  return allTokens.value.filter(token => {
    const nameMatch = !searchForm.name || token.name.toLowerCase().includes(searchForm.name.toLowerCase())
    const statusMatch = !searchForm.status || token.status === searchForm.status
    return nameMatch && statusMatch
  })
})

// 当前页显示的令牌列表
const tokens = computed(() => {
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return filteredTokens.value.slice(start, end)
})

const handleSearch = () => {
  pagination.currentPage = 1 // 搜索时重置到第一页
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.status = ''
  pagination.currentPage = 1 // 重置时回到第一页
}

const handlePageChange = (page) => {
  pagination.currentPage = page
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.currentPage = 1 // 改变页面大小时重置到第一页
}

// 初始化CRUD操作
const toast = useToast()
const crud = useCrudOperations('api-tokens')

// 弹窗状态
const showDetailModal = ref(false)
const showConfirmModal = ref(false)
const detailModalMode = ref('view')
const detailModalData = ref({})
const confirmModalData = ref({})

// 初始化数据
onMounted(async () => {
  await loadTokens()
})

// 加载令牌数据
const loadTokens = async () => {
  try {
    await crud.fetchList({
      name: searchForm.name,
      status: searchForm.status,
      page: pagination.currentPage,
      pageSize: pagination.pageSize
    })
    pagination.currentPage = crud.pagination.currentPage
  } catch (error) {
    console.error('加载令牌列表失败:', error)
  }
}

const handleAddToken = () => {
  detailModalData.value = {
    name: '',
    description: '',
    permissions: [],
    expiresAt: '',
    status: 'enabled'
  }
  detailModalMode.value = 'create'
  showDetailModal.value = true
}

const handleViewToken = async (token) => {
  detailModalMode.value = 'view'
  showDetailModal.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(token.id)
    detailModalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取令牌详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    detailModalData.value = { ...token }
  }
}

const handleEditToken = async (token) => {
  detailModalMode.value = 'edit'
  showDetailModal.value = true

  try {
    // 调用API获取完整的详情数据
    await crud.fetchById(token.id)
    detailModalData.value = { ...crud.currentItem.value }
  } catch (error) {
    console.error('获取令牌详情失败:', error)
    // 如果API调用失败，使用列表中的基本数据作为降级处理
    detailModalData.value = { ...token }
  }
}

const handleDeleteToken = (token) => {
  confirmModalData.value = {
    title: '确认删除令牌',
    message: `确定要删除令牌 "${token.name}" 吗？\n\n删除后将无法恢复，请谨慎操作。`,
    confirmText: '删除',
    cancelText: '取消',
    token: token
  }
  showConfirmModal.value = true
}

const handleCopyToken = async (token) => {
  try {
    await navigator.clipboard.writeText(token.token)
    toast.success('令牌已复制到剪贴板')
  } catch (error) {
    toast.error('复制失败，请手动复制')
  }
}
</script>
