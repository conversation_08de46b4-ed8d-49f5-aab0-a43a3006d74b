import { ref } from 'vue'

/**
 * 批量操作Composable
 * 提供批量操作的状态管理和方法
 */
export function useBatchOperation() {
  // 状态
  const showBatchModal = ref(false)
  const batchOperation = ref('delete')
  const selectedItems = ref([])
  const batchModalProps = ref({})

  /**
   * 显示批量操作确认弹窗
   * @param {Object} options - 配置选项
   * @param {string} options.operation - 操作类型
   * @param {Array} options.items - 选中的项目
   * @param {string} [options.title] - 自定义标题
   * @param {string} [options.message] - 自定义消息
   * @param {string} [options.warningMessage] - 警告消息
   * @param {Function} options.onConfirm - 确认回调
   * @returns {Promise} 操作结果
   */
  const showBatchConfirm = (options) => {
    return new Promise((resolve, reject) => {
      const {
        operation = 'delete',
        items = [],
        title,
        message,
        warningMessage,
        onConfirm
      } = options

      if (!items || items.length === 0) {
        reject(new Error('没有选中的项目'))
        return
      }

      batchOperation.value = operation
      selectedItems.value = items
      batchModalProps.value = {
        title,
        message,
        warningMessage,
        onConfirm: async (confirmData) => {
          try {
            let result
            if (onConfirm) {
              result = await onConfirm(confirmData)
            }
            resolve(result)
          } catch (error) {
            reject(error)
          }
        }
      }
      
      showBatchModal.value = true
    })
  }

  /**
   * 关闭批量操作弹窗
   */
  const closeBatchModal = () => {
    showBatchModal.value = false
    selectedItems.value = []
    batchModalProps.value = {}
  }

  /**
   * 批量删除确认
   * @param {Array} items - 要删除的项目
   * @param {Function} deleteFunction - 删除函数
   * @param {Object} options - 额外选项
   */
  const confirmBatchDelete = async (items, deleteFunction, options = {}) => {
    const {
      itemName = '项目',
      warningMessage = '此操作不可撤销，请谨慎操作。'
    } = options

    return showBatchConfirm({
      operation: 'delete',
      items,
      title: `批量删除${itemName}`,
      message: `确定要删除选中的 ${items.length} 个${itemName}吗？`,
      warningMessage,
      onConfirm: async ({ items: selectedItems, onProgress, onComplete }) => {
        const results = {
          success: 0,
          failed: 0,
          errors: []
        }

        for (let i = 0; i < selectedItems.length; i++) {
          const item = selectedItems[i]
          try {
            await deleteFunction(item.id)
            results.success++
          } catch (error) {
            results.failed++
            results.errors.push({
              id: item.id,
              name: item.name || item.title || `项目 ${item.id}`,
              message: error.message || '删除失败'
            })
          }
          
          // 更新进度
          onProgress(i + 1, selectedItems.length)
        }

        // 完成回调
        onComplete(results)
        
        return results
      }
    })
  }

  /**
   * 批量编辑确认
   * @param {Array} items - 要编辑的项目
   * @param {Function} editFunction - 编辑函数
   * @param {Object} options - 额外选项
   */
  const confirmBatchEdit = async (items, editFunction, options = {}) => {
    const {
      itemName = '项目',
      editData = {}
    } = options

    return showBatchConfirm({
      operation: 'edit',
      items,
      title: `批量编辑${itemName}`,
      message: `确定要编辑选中的 ${items.length} 个${itemName}吗？`,
      onConfirm: async ({ items: selectedItems, onProgress, onComplete }) => {
        const results = {
          success: 0,
          failed: 0,
          errors: []
        }

        for (let i = 0; i < selectedItems.length; i++) {
          const item = selectedItems[i]
          try {
            await editFunction(item.id, editData)
            results.success++
          } catch (error) {
            results.failed++
            results.errors.push({
              id: item.id,
              name: item.name || item.title || `项目 ${item.id}`,
              message: error.message || '编辑失败'
            })
          }
          
          // 更新进度
          onProgress(i + 1, selectedItems.length)
        }

        // 完成回调
        onComplete(results)
        
        return results
      }
    })
  }

  /**
   * 批量导出确认
   * @param {Array} items - 要导出的项目
   * @param {Function} exportFunction - 导出函数
   * @param {Object} options - 额外选项
   */
  const confirmBatchExport = async (items, exportFunction, options = {}) => {
    const {
      itemName = '项目',
      format = 'Excel'
    } = options

    return showBatchConfirm({
      operation: 'export',
      items,
      title: `批量导出${itemName}`,
      message: `确定要导出选中的 ${items.length} 个${itemName}为${format}文件吗？`,
      onConfirm: async ({ items: selectedItems, onProgress, onComplete }) => {
        try {
          const result = await exportFunction(selectedItems.map(item => item.id))
          
          onProgress(selectedItems.length, selectedItems.length)
          onComplete({
            success: selectedItems.length,
            failed: 0,
            errors: []
          })
          
          return result
        } catch (error) {
          onComplete({
            success: 0,
            failed: selectedItems.length,
            errors: [{
              id: 'export',
              name: '导出操作',
              message: error.message || '导出失败'
            }]
          })
          throw error
        }
      }
    })
  }

  return {
    // 状态
    showBatchModal,
    batchOperation,
    selectedItems,
    batchModalProps,
    
    // 方法
    showBatchConfirm,
    closeBatchModal,
    confirmBatchDelete,
    confirmBatchEdit,
    confirmBatchExport
  }
}
