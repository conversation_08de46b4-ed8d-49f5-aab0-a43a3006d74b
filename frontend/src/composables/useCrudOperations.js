import { ref, reactive } from 'vue'
import { get, post, put, del, handleApiError } from '@/utils/api'
import { useToast } from '@/composables/useToast'

/**
 * 统一的CRUD操作Composable
 * @param {string} moduleName - 模块名称，用于构建API端点
 * @param {Object} options - 配置选项
 * @param {string} [options.baseUrl] - 自定义基础URL
 * @param {Object} [options.messages] - 自定义消息文本
 * @param {boolean} [options.showToast] - 是否显示Toast通知
 * @returns {Object} CRUD操作方法和状态
 */
export function useCrudOperations(moduleName, options = {}) {
  const toast = useToast()
  
  // 默认配置
  const config = {
    baseUrl: `/${moduleName}`,
    showToast: true,
    messages: {
      createSuccess: '创建成功',
      createError: '创建失败',
      updateSuccess: '更新成功',
      updateError: '更新失败',
      deleteSuccess: '删除成功',
      deleteError: '删除失败',
      batchDeleteSuccess: '批量删除成功',
      batchDeleteError: '批量删除失败',
      fetchError: '获取数据失败'
    },
    ...options
  }

  // 状态管理
  const loading = ref(false)
  const creating = ref(false)
  const updating = ref(false)
  const deleting = ref(false)
  const fetching = ref(false)
  
  // 数据状态
  const data = ref([])
  const currentItem = ref(null)
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
  })

  /**
   * 处理API错误
   * @param {Error} error - 错误对象
   * @param {string} defaultMessage - 默认错误消息
   */
  const handleError = (error, defaultMessage) => {
    const errorMessage = handleApiError(error)
    if (config.showToast) {
      toast.error(errorMessage || defaultMessage)
    }
    console.error(`${moduleName} API Error:`, error)
    throw error
  }

  /**
   * 创建新项目
   * @param {Object} itemData - 项目数据
   * @returns {Promise<Object>} 创建结果
   */
  const create = async (itemData) => {
    creating.value = true
    loading.value = true
    
    try {
      const result = await post(config.baseUrl, itemData)
      
      if (config.showToast) {
        toast.success(config.messages.createSuccess)
      }
      
      // 更新本地数据
      if (result.data) {
        data.value.unshift(result.data)
        pagination.total++
      }
      
      return result
    } catch (error) {
      handleError(error, config.messages.createError)
    } finally {
      creating.value = false
      loading.value = false
    }
  }

  /**
   * 更新项目
   * @param {string|number} id - 项目ID
   * @param {Object} itemData - 更新数据
   * @returns {Promise<Object>} 更新结果
   */
  const update = async (id, itemData) => {
    updating.value = true
    loading.value = true
    
    try {
      const result = await put(`${config.baseUrl}/${id}`, itemData)
      
      if (config.showToast) {
        toast.success(config.messages.updateSuccess)
      }
      
      // 更新本地数据
      if (result.data) {
        const index = data.value.findIndex(item => item.id === id)
        if (index !== -1) {
          data.value[index] = result.data
        }
        
        if (currentItem.value && currentItem.value.id === id) {
          currentItem.value = result.data
        }
      }
      
      return result
    } catch (error) {
      handleError(error, config.messages.updateError)
    } finally {
      updating.value = false
      loading.value = false
    }
  }

  /**
   * 删除项目
   * @param {string|number} id - 项目ID
   * @returns {Promise<Object>} 删除结果
   */
  const remove = async (id) => {
    deleting.value = true
    loading.value = true
    
    try {
      const result = await del(`${config.baseUrl}/${id}`)
      
      if (config.showToast) {
        toast.success(config.messages.deleteSuccess)
      }
      
      // 更新本地数据
      const index = data.value.findIndex(item => item.id === id)
      if (index !== -1) {
        data.value.splice(index, 1)
        pagination.total--
      }
      
      if (currentItem.value && currentItem.value.id === id) {
        currentItem.value = null
      }
      
      return result
    } catch (error) {
      handleError(error, config.messages.deleteError)
    } finally {
      deleting.value = false
      loading.value = false
    }
  }

  /**
   * 批量删除项目
   * @param {Array<string|number>} ids - 项目ID数组
   * @returns {Promise<Object>} 删除结果
   */
  const batchRemove = async (ids) => {
    if (!ids || ids.length === 0) {
      if (config.showToast) {
        toast.warning('请选择要删除的项目')
      }
      return
    }
    
    deleting.value = true
    loading.value = true
    
    try {
      const result = await post(`${config.baseUrl}/batch-delete`, { ids })
      
      if (config.showToast) {
        toast.success(`${config.messages.batchDeleteSuccess}，共删除 ${ids.length} 项`)
      }
      
      // 更新本地数据
      data.value = data.value.filter(item => !ids.includes(item.id))
      pagination.total -= ids.length
      
      return result
    } catch (error) {
      handleError(error, config.messages.batchDeleteError)
    } finally {
      deleting.value = false
      loading.value = false
    }
  }

  /**
   * 获取项目列表
   * @param {Object} params - 查询参数
   * @returns {Promise<Object>} 查询结果
   */
  const fetchList = async (params = {}) => {
    fetching.value = true
    loading.value = true
    
    try {
      const queryParams = {
        page: pagination.currentPage,
        pageSize: pagination.pageSize,
        ...params
      }
      
      const result = await get(config.baseUrl, queryParams)
      
      if (result.data) {
        data.value = result.data.items || result.data
        pagination.total = result.data.total || data.value.length
        pagination.currentPage = result.data.currentPage || pagination.currentPage
      }
      
      return result
    } catch (error) {
      handleError(error, config.messages.fetchError)
    } finally {
      fetching.value = false
      loading.value = false
    }
  }

  /**
   * 获取单个项目详情
   * @param {string|number} id - 项目ID
   * @returns {Promise<Object>} 项目详情
   */
  const fetchById = async (id) => {
    fetching.value = true
    loading.value = true
    
    try {
      const result = await get(`${config.baseUrl}/${id}`)
      
      if (result.data) {
        currentItem.value = result.data
      }
      
      return result
    } catch (error) {
      handleError(error, '获取详情失败')
    } finally {
      fetching.value = false
      loading.value = false
    }
  }

  /**
   * 刷新列表数据
   * @param {Object} params - 查询参数
   */
  const refresh = async (params = {}) => {
    await fetchList(params)
  }

  /**
   * 重置状态
   */
  const reset = () => {
    data.value = []
    currentItem.value = null
    pagination.currentPage = 1
    pagination.total = 0
    loading.value = false
    creating.value = false
    updating.value = false
    deleting.value = false
    fetching.value = false
  }

  return {
    // 状态
    loading,
    creating,
    updating,
    deleting,
    fetching,
    data,
    currentItem,
    pagination,
    
    // 方法
    create,
    update,
    remove,
    batchRemove,
    fetchList,
    fetchById,
    refresh,
    reset
  }
}
