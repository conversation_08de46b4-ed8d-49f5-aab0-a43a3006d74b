import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

export function useKeyboardShortcuts() {
  const router = useRouter()
  const isShortcutPanelOpen = ref(false)
  
  // 快捷键映射
  const shortcuts = ref({
    // 导航快捷键
    'ctrl+1': { action: () => router.push('/dashboard'), description: '数据看板' },
    'ctrl+2': { action: () => router.push('/users'), description: '用户管理' },
    'ctrl+3': { action: () => router.push('/services'), description: '服务管理' },
    'ctrl+4': { action: () => router.push('/tasks'), description: '任务管理' },
    'ctrl+5': { action: () => router.push('/settings'), description: '个人设置' },
    
    // 功能快捷键
    'ctrl+k': { action: () => focusSearch(), description: '搜索菜单' },
    'ctrl+b': { action: () => toggleSidebar(), description: '切换侧边栏' },
    'ctrl+shift+d': { action: () => toggleTheme(), description: '切换主题' },
    'ctrl+shift+h': { action: () => toggleShortcutPanel(), description: '显示快捷键' },
    
    // 特殊功能
    'alt+n': { action: () => createNew(), description: '新建项目' },
    'alt+s': { action: () => saveAll(), description: '保存所有' },
    'escape': { action: () => closeModals(), description: '关闭弹窗' },
    
    // 开发者快捷键
    'ctrl+shift+i': { action: () => toggleDevTools(), description: '开发者工具' },
    'ctrl+shift+r': { action: () => window.location.reload(), description: '刷新页面' }
  })
  
  // 当前按下的键
  const pressedKeys = ref(new Set())
  
  // 快捷键处理
  const handleKeyDown = (event) => {
    const key = event.key.toLowerCase()
    pressedKeys.value.add(key)
    
    // 构建快捷键字符串
    const shortcutKey = buildShortcutKey(event)
    
    // 查找匹配的快捷键
    const shortcut = shortcuts.value[shortcutKey]
    if (shortcut) {
      event.preventDefault()
      event.stopPropagation()
      
      try {
        shortcut.action()
        showShortcutFeedback(shortcutKey, shortcut.description)
      } catch (error) {
        console.error('快捷键执行失败:', error)
      }
    }
  }
  
  const handleKeyUp = (event) => {
    const key = event.key.toLowerCase()
    pressedKeys.value.delete(key)
  }
  
  // 构建快捷键字符串
  const buildShortcutKey = (event) => {
    const parts = []
    
    if (event.ctrlKey || event.metaKey) parts.push('ctrl')
    if (event.altKey) parts.push('alt')
    if (event.shiftKey) parts.push('shift')
    
    const key = event.key.toLowerCase()
    if (key !== 'control' && key !== 'alt' && key !== 'shift' && key !== 'meta') {
      parts.push(key)
    }
    
    return parts.join('+')
  }
  
  // 显示快捷键反馈
  const showShortcutFeedback = (key, description) => {
    // 创建临时提示元素
    const feedback = document.createElement('div')
    feedback.className = 'shortcut-feedback'
    feedback.innerHTML = `
      <div class="shortcut-key">${key.toUpperCase()}</div>
      <div class="shortcut-desc">${description}</div>
    `
    
    // 添加样式
    Object.assign(feedback.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: 'rgba(0, 0, 0, 0.9)',
      color: 'white',
      padding: '12px 16px',
      borderRadius: '8px',
      fontSize: '14px',
      zIndex: '10000',
      animation: 'shortcutFadeIn 0.3s ease-out',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)'
    })
    
    document.body.appendChild(feedback)
    
    // 自动移除
    setTimeout(() => {
      feedback.style.animation = 'shortcutFadeOut 0.3s ease-out'
      setTimeout(() => {
        if (feedback.parentNode) {
          feedback.parentNode.removeChild(feedback)
        }
      }, 300)
    }, 2000)
  }
  
  // 功能函数
  const focusSearch = () => {
    const searchInput = document.querySelector('.search-input')
    if (searchInput) {
      searchInput.focus()
    }
  }
  
  const toggleSidebar = () => {
    // 触发侧边栏切换事件
    window.dispatchEvent(new CustomEvent('toggle-sidebar'))
  }
  
  const toggleTheme = () => {
    // 触发主题切换事件
    window.dispatchEvent(new CustomEvent('toggle-theme'))
  }
  
  const toggleShortcutPanel = () => {
    isShortcutPanelOpen.value = !isShortcutPanelOpen.value
  }
  
  const createNew = () => {
    // 触发新建事件
    window.dispatchEvent(new CustomEvent('create-new'))
  }
  
  const saveAll = () => {
    // 触发保存事件
    window.dispatchEvent(new CustomEvent('save-all'))
  }
  
  const closeModals = () => {
    // 触发关闭弹窗事件
    window.dispatchEvent(new CustomEvent('close-modals'))
  }
  
  const toggleDevTools = () => {
    // 开发者工具相关
    console.log('开发者工具快捷键触发')
  }
  
  // 注册快捷键
  const registerShortcut = (key, action, description) => {
    shortcuts.value[key] = { action, description }
  }
  
  // 注销快捷键
  const unregisterShortcut = (key) => {
    delete shortcuts.value[key]
  }
  
  // 获取所有快捷键
  const getAllShortcuts = () => {
    return Object.entries(shortcuts.value).map(([key, { description }]) => ({
      key: key.toUpperCase(),
      description
    }))
  }
  
  // 检查快捷键冲突
  const hasConflict = (key) => {
    return shortcuts.value.hasOwnProperty(key)
  }
  
  // 生命周期
  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown, true)
    document.addEventListener('keyup', handleKeyUp, true)
    
    // 添加CSS动画
    if (!document.querySelector('#shortcut-styles')) {
      const style = document.createElement('style')
      style.id = 'shortcut-styles'
      style.textContent = `
        @keyframes shortcutFadeIn {
          from {
            opacity: 0;
            transform: translateX(20px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }
        
        @keyframes shortcutFadeOut {
          from {
            opacity: 1;
            transform: translateX(0);
          }
          to {
            opacity: 0;
            transform: translateX(20px);
          }
        }
        
        .shortcut-feedback {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .shortcut-key {
          font-weight: 600;
          margin-bottom: 4px;
          font-size: 12px;
          opacity: 0.8;
        }
        
        .shortcut-desc {
          font-weight: 500;
        }
      `
      document.head.appendChild(style)
    }
  })
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown, true)
    document.removeEventListener('keyup', handleKeyUp, true)
  })
  
  return {
    shortcuts,
    pressedKeys,
    isShortcutPanelOpen,
    registerShortcut,
    unregisterShortcut,
    getAllShortcuts,
    hasConflict,
    toggleShortcutPanel,
    focusSearch,
    toggleSidebar,
    toggleTheme
  }
}

// 快捷键面板组件数据
export const shortcutCategories = [
  {
    name: '导航',
    shortcuts: [
      { key: 'Ctrl + 1', description: '数据看板' },
      { key: 'Ctrl + 2', description: '用户管理' },
      { key: 'Ctrl + 3', description: '服务管理' },
      { key: 'Ctrl + 4', description: '任务管理' },
      { key: 'Ctrl + 5', description: '个人设置' }
    ]
  },
  {
    name: '功能',
    shortcuts: [
      { key: 'Ctrl + K', description: '搜索菜单' },
      { key: 'Ctrl + B', description: '切换侧边栏' },
      { key: 'Ctrl + Shift + D', description: '切换主题' },
      { key: 'Ctrl + Shift + H', description: '显示快捷键' }
    ]
  },
  {
    name: '操作',
    shortcuts: [
      { key: 'Alt + N', description: '新建项目' },
      { key: 'Alt + S', description: '保存所有' },
      { key: 'Escape', description: '关闭弹窗' },
      { key: 'F2', description: '重命名' }
    ]
  },
  {
    name: '开发',
    shortcuts: [
      { key: 'Ctrl + Shift + I', description: '开发者工具' },
      { key: 'Ctrl + Shift + R', description: '刷新页面' },
      { key: 'Ctrl + Shift + C', description: '检查元素' }
    ]
  }
]
