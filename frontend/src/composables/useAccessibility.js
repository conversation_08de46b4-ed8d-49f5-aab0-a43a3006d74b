import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

export function useAccessibility() {
  // 状态管理
  const focusedElement = ref(null)
  const isHighContrastMode = ref(false)
  const isReducedMotionMode = ref(false)
  const screenReaderAnnouncements = ref([])
  const keyboardNavigationEnabled = ref(true)
  const focusVisible = ref(false)
  
  // 焦点管理
  const focusableElements = computed(() => {
    return [
      'button',
      'input',
      'select',
      'textarea',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ')
  })
  
  // 焦点陷阱管理
  const createFocusTrap = (container) => {
    if (!container) return null
    
    const focusableEls = container.querySelectorAll(focusableElements.value)
    const firstFocusableEl = focusableEls[0]
    const lastFocusableEl = focusableEls[focusableEls.length - 1]
    
    const handleKeyDown = (e) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstFocusableEl) {
            e.preventDefault()
            lastFocusableEl.focus()
          }
        } else {
          // Tab
          if (document.activeElement === lastFocusableEl) {
            e.preventDefault()
            firstFocusableEl.focus()
          }
        }
      }
      
      if (e.key === 'Escape') {
        // 触发关闭事件
        container.dispatchEvent(new CustomEvent('escape-pressed'))
      }
    }
    
    container.addEventListener('keydown', handleKeyDown)
    
    // 自动聚焦到第一个可聚焦元素
    if (firstFocusableEl) {
      nextTick(() => {
        firstFocusableEl.focus()
      })
    }
    
    return () => {
      container.removeEventListener('keydown', handleKeyDown)
    }
  }
  
  // 键盘导航
  const setupKeyboardNavigation = (container, options = {}) => {
    const {
      orientation = 'vertical', // 'vertical' | 'horizontal' | 'grid'
      wrap = true,
      homeEndKeys = true,
      typeahead = true
    } = options
    
    let typeaheadString = ''
    let typeaheadTimeout = null
    
    const handleKeyDown = (e) => {
      const focusableEls = Array.from(container.querySelectorAll(focusableElements.value))
      const currentIndex = focusableEls.indexOf(document.activeElement)
      
      let nextIndex = currentIndex
      
      switch (e.key) {
        case 'ArrowDown':
          if (orientation === 'vertical' || orientation === 'grid') {
            e.preventDefault()
            nextIndex = wrap ? (currentIndex + 1) % focusableEls.length : Math.min(currentIndex + 1, focusableEls.length - 1)
          }
          break
          
        case 'ArrowUp':
          if (orientation === 'vertical' || orientation === 'grid') {
            e.preventDefault()
            nextIndex = wrap ? (currentIndex - 1 + focusableEls.length) % focusableEls.length : Math.max(currentIndex - 1, 0)
          }
          break
          
        case 'ArrowRight':
          if (orientation === 'horizontal' || orientation === 'grid') {
            e.preventDefault()
            nextIndex = wrap ? (currentIndex + 1) % focusableEls.length : Math.min(currentIndex + 1, focusableEls.length - 1)
          }
          break
          
        case 'ArrowLeft':
          if (orientation === 'horizontal' || orientation === 'grid') {
            e.preventDefault()
            nextIndex = wrap ? (currentIndex - 1 + focusableEls.length) % focusableEls.length : Math.max(currentIndex - 1, 0)
          }
          break
          
        case 'Home':
          if (homeEndKeys) {
            e.preventDefault()
            nextIndex = 0
          }
          break
          
        case 'End':
          if (homeEndKeys) {
            e.preventDefault()
            nextIndex = focusableEls.length - 1
          }
          break
          
        default:
          // 类型提前搜索
          if (typeahead && e.key.length === 1 && !e.ctrlKey && !e.altKey && !e.metaKey) {
            clearTimeout(typeaheadTimeout)
            typeaheadString += e.key.toLowerCase()
            
            const matchingEl = focusableEls.find((el, index) => {
              if (index <= currentIndex) return false
              const text = el.textContent?.toLowerCase() || el.getAttribute('aria-label')?.toLowerCase() || ''
              return text.startsWith(typeaheadString)
            }) || focusableEls.find(el => {
              const text = el.textContent?.toLowerCase() || el.getAttribute('aria-label')?.toLowerCase() || ''
              return text.startsWith(typeaheadString)
            })
            
            if (matchingEl) {
              matchingEl.focus()
            }
            
            typeaheadTimeout = setTimeout(() => {
              typeaheadString = ''
            }, 1000)
          }
          return
      }
      
      if (nextIndex !== currentIndex && focusableEls[nextIndex]) {
        focusableEls[nextIndex].focus()
      }
    }
    
    container.addEventListener('keydown', handleKeyDown)
    
    return () => {
      container.removeEventListener('keydown', handleKeyDown)
      clearTimeout(typeaheadTimeout)
    }
  }
  
  // 屏幕阅读器公告
  const announceToScreenReader = (message, priority = 'polite') => {
    const announcement = {
      id: Date.now(),
      message,
      priority,
      timestamp: new Date()
    }
    
    screenReaderAnnouncements.value.push(announcement)
    
    // 创建临时的aria-live区域
    const liveRegion = document.createElement('div')
    liveRegion.setAttribute('aria-live', priority)
    liveRegion.setAttribute('aria-atomic', 'true')
    liveRegion.className = 'sr-only'
    liveRegion.textContent = message
    
    document.body.appendChild(liveRegion)
    
    // 清理
    setTimeout(() => {
      if (liveRegion.parentNode) {
        liveRegion.parentNode.removeChild(liveRegion)
      }
      
      // 清理旧公告
      screenReaderAnnouncements.value = screenReaderAnnouncements.value.filter(
        a => Date.now() - a.timestamp.getTime() < 10000
      )
    }, 1000)
  }
  
  // 焦点可见性管理
  const handleFocusVisible = (e) => {
    // 只有通过键盘导航时才显示焦点样式
    focusVisible.value = e.detail === 0 || e.type === 'keydown'
  }
  
  // 高对比度模式检测
  const checkHighContrastMode = () => {
    const testEl = document.createElement('div')
    testEl.style.cssText = 'border: 1px solid; border-color: red green'
    document.body.appendChild(testEl)
    
    const computedStyle = window.getComputedStyle(testEl)
    const borderTopColor = computedStyle.borderTopColor
    const borderRightColor = computedStyle.borderRightColor
    
    isHighContrastMode.value = borderTopColor === borderRightColor
    
    document.body.removeChild(testEl)
  }
  
  // 减少动画偏好检测
  const checkReducedMotionPreference = () => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
    isReducedMotionMode.value = mediaQuery.matches
    
    mediaQuery.addEventListener('change', (e) => {
      isReducedMotionMode.value = e.matches
    })
  }
  
  // ARIA属性管理
  const setAriaAttributes = (element, attributes) => {
    Object.entries(attributes).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        element.setAttribute(`aria-${key}`, String(value))
      } else {
        element.removeAttribute(`aria-${key}`)
      }
    })
  }
  
  // 角色和状态管理
  const updateElementRole = (element, role, states = {}) => {
    element.setAttribute('role', role)
    
    Object.entries(states).forEach(([state, value]) => {
      element.setAttribute(`aria-${state}`, String(value))
    })
  }
  
  // 描述性文本管理
  const createDescription = (element, description, id = null) => {
    const descId = id || `desc-${Date.now()}`
    
    let descEl = document.getElementById(descId)
    if (!descEl) {
      descEl = document.createElement('div')
      descEl.id = descId
      descEl.className = 'sr-only'
      document.body.appendChild(descEl)
    }
    
    descEl.textContent = description
    element.setAttribute('aria-describedby', descId)
    
    return descId
  }
  
  // 标签关联
  const associateLabel = (input, label) => {
    const labelId = label.id || `label-${Date.now()}`
    label.id = labelId
    input.setAttribute('aria-labelledby', labelId)
  }
  
  // 错误消息管理
  const setErrorMessage = (input, message) => {
    if (message) {
      const errorId = `error-${Date.now()}`
      const errorEl = document.createElement('div')
      errorEl.id = errorId
      errorEl.className = 'sr-only'
      errorEl.textContent = message
      errorEl.setAttribute('role', 'alert')
      
      input.parentNode.appendChild(errorEl)
      input.setAttribute('aria-describedby', errorId)
      input.setAttribute('aria-invalid', 'true')
      
      return errorId
    } else {
      input.removeAttribute('aria-describedby')
      input.removeAttribute('aria-invalid')
    }
  }
  
  // 跳过链接
  const createSkipLink = (target, text = '跳到主内容') => {
    const skipLink = document.createElement('a')
    skipLink.href = `#${target}`
    skipLink.textContent = text
    skipLink.className = 'skip-link'
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: #fff;
      padding: 8px;
      text-decoration: none;
      z-index: 10000;
      border-radius: 4px;
    `
    
    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px'
    })
    
    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px'
    })
    
    document.body.insertBefore(skipLink, document.body.firstChild)
    
    return skipLink
  }
  
  // 生命周期
  onMounted(() => {
    checkHighContrastMode()
    checkReducedMotionPreference()
    
    // 添加全局样式
    const style = document.createElement('style')
    style.textContent = `
      .sr-only {
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
      }
      
      .focus-visible {
        outline: 2px solid #007acc !important;
        outline-offset: 2px !important;
      }
      
      @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
      
      @media (prefers-contrast: high) {
        * {
          border-color: currentColor !important;
        }
      }
    `
    document.head.appendChild(style)
    
    // 监听焦点事件
    document.addEventListener('focusin', handleFocusVisible)
    document.addEventListener('keydown', handleFocusVisible)
  })
  
  onUnmounted(() => {
    document.removeEventListener('focusin', handleFocusVisible)
    document.removeEventListener('keydown', handleFocusVisible)
  })
  
  return {
    // 状态
    focusedElement,
    isHighContrastMode,
    isReducedMotionMode,
    screenReaderAnnouncements,
    keyboardNavigationEnabled,
    focusVisible,
    
    // 方法
    createFocusTrap,
    setupKeyboardNavigation,
    announceToScreenReader,
    setAriaAttributes,
    updateElementRole,
    createDescription,
    associateLabel,
    setErrorMessage,
    createSkipLink,
    checkHighContrastMode,
    checkReducedMotionPreference
  }
}
