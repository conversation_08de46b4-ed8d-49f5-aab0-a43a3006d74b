import { useToastStore } from '@/stores/toast'

/**
 * Toast通知Composable
 * 提供统一的消息通知功能
 */
export function useToast() {
  const toastStore = useToastStore()

  /**
   * 显示Toast通知
   * @param {Object} options - Toast配置选项
   * @param {string} options.type - 类型：success、error、warning、info
   * @param {string} options.message - 消息内容
   * @param {string} [options.title] - 标题（可选）
   * @param {number} [options.duration] - 显示时长，0表示不自动消失
   * @returns {number} Toast ID
   */
  const show = (options) => {
    return toastStore.addToast(options)
  }

  /**
   * 显示成功消息
   * @param {string} message - 消息内容
   * @param {Object} [options] - 额外选项
   * @returns {number} Toast ID
   */
  const success = (message, options = {}) => {
    return toastStore.success(message, options)
  }

  /**
   * 显示错误消息
   * @param {string} message - 消息内容
   * @param {Object} [options] - 额外选项
   * @returns {number} Toast ID
   */
  const error = (message, options = {}) => {
    return toastStore.error(message, options)
  }

  /**
   * 显示警告消息
   * @param {string} message - 消息内容
   * @param {Object} [options] - 额外选项
   * @returns {number} Toast ID
   */
  const warning = (message, options = {}) => {
    return toastStore.warning(message, options)
  }

  /**
   * 显示信息消息
   * @param {string} message - 消息内容
   * @param {Object} [options] - 额外选项
   * @returns {number} Toast ID
   */
  const info = (message, options = {}) => {
    return toastStore.info(message, options)
  }

  /**
   * 移除指定Toast
   * @param {number} id - Toast ID
   */
  const remove = (id) => {
    toastStore.removeToast(id)
  }

  /**
   * 清除所有Toast
   */
  const clear = () => {
    toastStore.clearAll()
  }

  /**
   * 显示API错误消息
   * @param {Error|string} error - 错误对象或错误消息
   * @param {string} [defaultMessage] - 默认错误消息
   */
  const apiError = (error, defaultMessage = '操作失败，请稍后重试') => {
    let message = defaultMessage
    
    if (typeof error === 'string') {
      message = error
    } else if (error?.message) {
      message = error.message
    } else if (error?.response?.data?.message) {
      message = error.response.data.message
    }
    
    return toastStore.error(message)
  }

  /**
   * 显示操作成功消息
   * @param {string} [action] - 操作名称
   */
  const operationSuccess = (action = '操作') => {
    return toastStore.success(`${action}成功`)
  }

  return {
    show,
    success,
    error,
    warning,
    info,
    remove,
    clear,
    apiError,
    operationSuccess
  }
}
