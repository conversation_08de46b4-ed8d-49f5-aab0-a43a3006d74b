import { ref, computed, onMounted, onUnmounted } from 'vue'

export function useResponsiveLayout() {
  // 响应式状态
  const windowWidth = ref(window.innerWidth)
  const windowHeight = ref(window.innerHeight)
  const isMobileMenuOpen = ref(false)
  
  // 断点定义
  const breakpoints = {
    xs: 480,
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536
  }
  
  // 计算属性
  const isMobile = computed(() => windowWidth.value < breakpoints.md)
  const isTablet = computed(() => windowWidth.value >= breakpoints.md && windowWidth.value < breakpoints.lg)
  const isDesktop = computed(() => windowWidth.value >= breakpoints.lg)
  const isLargeScreen = computed(() => windowWidth.value >= breakpoints.xl)
  const isExtraLargeScreen = computed(() => windowWidth.value >= breakpoints['2xl'])
  
  // 设备类型
  const deviceType = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    if (isLargeScreen.value) return 'large-desktop'
    return 'desktop'
  })
  
  // 屏幕方向
  const isLandscape = computed(() => windowWidth.value > windowHeight.value)
  const isPortrait = computed(() => windowHeight.value > windowWidth.value)
  
  // 侧边栏相关
  const shouldCollapseSidebar = computed(() => {
    // 在平板设备上默认收起侧边栏
    return isTablet.value
  })
  
  const sidebarWidth = computed(() => {
    if (isMobile.value) return '280px'
    if (isTablet.value) return '240px'
    if (isLargeScreen.value) return '288px'
    return '272px'
  })
  
  const sidebarCollapsedWidth = computed(() => {
    if (isMobile.value) return '0px' // 移动端完全隐藏
    return '80px'
  })
  
  // 内容区域适配
  const contentPadding = computed(() => {
    if (isMobile.value) return '1rem'
    if (isTablet.value) return '1.5rem'
    return '2rem'
  })
  
  const maxContentWidth = computed(() => {
    if (isExtraLargeScreen.value) return '1400px'
    if (isLargeScreen.value) return '1200px'
    return '100%'
  })
  
  // 网格布局
  const gridColumns = computed(() => {
    if (isMobile.value) return 1
    if (isTablet.value) return 2
    if (isLargeScreen.value) return 4
    return 3
  })
  
  const cardMinWidth = computed(() => {
    if (isMobile.value) return '100%'
    if (isTablet.value) return '300px'
    return '350px'
  })
  
  // 字体大小适配
  const baseFontSize = computed(() => {
    if (isMobile.value) return '16px'
    if (isTablet.value) return '15px'
    return '14px'
  })
  
  // 触摸设备检测
  const isTouchDevice = computed(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  })
  
  // 事件处理
  const handleResize = () => {
    windowWidth.value = window.innerWidth
    windowHeight.value = window.innerHeight
    
    // 移动端自动关闭菜单
    if (!isMobile.value && isMobileMenuOpen.value) {
      isMobileMenuOpen.value = false
    }
  }
  
  const toggleMobileMenu = () => {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
    
    // 防止背景滚动
    if (isMobileMenuOpen.value) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }
  }
  
  const closeMobileMenu = () => {
    isMobileMenuOpen.value = false
    document.body.style.overflow = ''
  }
  
  // 手势支持
  const setupGestureHandlers = (element) => {
    if (!element || !isTouchDevice.value) return
    
    let startX = 0
    let startY = 0
    let isSwipeGesture = false
    
    const handleTouchStart = (e) => {
      startX = e.touches[0].clientX
      startY = e.touches[0].clientY
      isSwipeGesture = false
    }
    
    const handleTouchMove = (e) => {
      if (!startX || !startY) return
      
      const currentX = e.touches[0].clientX
      const currentY = e.touches[0].clientY
      
      const diffX = startX - currentX
      const diffY = startY - currentY
      
      // 检测水平滑动
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        isSwipeGesture = true
        
        // 从左边缘向右滑动打开菜单
        if (startX < 50 && diffX < -100 && !isMobileMenuOpen.value) {
          toggleMobileMenu()
        }
        
        // 向左滑动关闭菜单
        if (diffX > 100 && isMobileMenuOpen.value) {
          closeMobileMenu()
        }
      }
    }
    
    const handleTouchEnd = () => {
      startX = 0
      startY = 0
      isSwipeGesture = false
    }
    
    element.addEventListener('touchstart', handleTouchStart, { passive: true })
    element.addEventListener('touchmove', handleTouchMove, { passive: true })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })
    
    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
    }
  }
  
  // 键盘导航支持
  const setupKeyboardNavigation = () => {
    const handleKeyDown = (e) => {
      // ESC 键关闭移动菜单
      if (e.key === 'Escape' && isMobileMenuOpen.value) {
        closeMobileMenu()
      }
      
      // Alt + M 切换移动菜单
      if (e.altKey && e.key === 'm' && isMobile.value) {
        e.preventDefault()
        toggleMobileMenu()
      }
    }
    
    document.addEventListener('keydown', handleKeyDown)
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }
  
  // 媒体查询监听
  const setupMediaQueryListeners = () => {
    const mediaQueries = {
      mobile: window.matchMedia(`(max-width: ${breakpoints.md - 1}px)`),
      tablet: window.matchMedia(`(min-width: ${breakpoints.md}px) and (max-width: ${breakpoints.lg - 1}px)`),
      desktop: window.matchMedia(`(min-width: ${breakpoints.lg}px)`),
      largeScreen: window.matchMedia(`(min-width: ${breakpoints.xl}px)`),
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)'),
      highContrast: window.matchMedia('(prefers-contrast: high)'),
      darkMode: window.matchMedia('(prefers-color-scheme: dark)')
    }
    
    const handleMediaChange = (query, type) => {
      // 触发自定义事件
      window.dispatchEvent(new CustomEvent('media-query-change', {
        detail: { type, matches: query.matches }
      }))
    }
    
    // 添加监听器
    Object.entries(mediaQueries).forEach(([type, query]) => {
      const handler = () => handleMediaChange(query, type)
      query.addEventListener('change', handler)
    })
    
    return () => {
      Object.values(mediaQueries).forEach(query => {
        query.removeEventListener('change', handleMediaChange)
      })
    }
  }
  
  // 性能优化：防抖resize事件
  let resizeTimer = null
  const debouncedResize = () => {
    clearTimeout(resizeTimer)
    resizeTimer = setTimeout(handleResize, 150)
  }
  
  // 生命周期
  onMounted(() => {
    window.addEventListener('resize', debouncedResize)
    
    // 设置手势处理
    const gestureCleanup = setupGestureHandlers(document.body)
    
    // 设置键盘导航
    const keyboardCleanup = setupKeyboardNavigation()
    
    // 设置媒体查询监听
    const mediaCleanup = setupMediaQueryListeners()
    
    // 初始化时检查设备类型
    handleResize()
    
    // 清理函数
    onUnmounted(() => {
      window.removeEventListener('resize', debouncedResize)
      clearTimeout(resizeTimer)
      document.body.style.overflow = ''
      
      if (gestureCleanup) gestureCleanup()
      if (keyboardCleanup) keyboardCleanup()
      if (mediaCleanup) mediaCleanup()
    })
  })
  
  return {
    // 响应式状态
    windowWidth,
    windowHeight,
    isMobileMenuOpen,
    
    // 设备检测
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    isExtraLargeScreen,
    deviceType,
    isTouchDevice,
    
    // 屏幕方向
    isLandscape,
    isPortrait,
    
    // 布局计算
    shouldCollapseSidebar,
    sidebarWidth,
    sidebarCollapsedWidth,
    contentPadding,
    maxContentWidth,
    gridColumns,
    cardMinWidth,
    baseFontSize,
    
    // 方法
    toggleMobileMenu,
    closeMobileMenu,
    setupGestureHandlers,
    
    // 断点
    breakpoints
  }
}
