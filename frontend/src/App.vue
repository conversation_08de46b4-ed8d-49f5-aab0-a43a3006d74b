<template>
  <!-- 根据路由和登录状态动态选择布局 -->
  <component :is="currentLayout" />

  <!-- 全局Toast通知组件 -->
  <ToastNotification />
</template>

<script setup>
import AppLayout from '@/components/layout/AppLayout.vue'
import AuthLayout from '@/components/layout/AuthLayout.vue'
import ToastNotification from '@/components/ui/ToastNotification.vue'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import { onMounted, watch, computed } from 'vue'
import { useRoute } from 'vue-router'
import { initPerformanceMonitoring } from '@/utils/performance'
import { initAccessibility } from '@/utils/accessibility'

// 导入统一的设计系统
import '@/assets/styles/animations.css'
import '@/assets/styles/sidebar.css'

const themeStore = useThemeStore()
const userStore = useUserStore()
const route = useRoute()

// 定义需要使用认证布局的路由
const authRoutes = ['login', 'auth-callback']

// 动态计算当前应该使用的布局
const currentLayout = computed(() => {
  // 如果是认证相关路由，使用认证布局
  if (authRoutes.includes(route.name)) {
    return AuthLayout
  }

  // 其他情况使用主布局
  return AppLayout
})

// 监听主题变化
watch(() => themeStore.isDarkMode, (isDark) => {
  // 主题变化处理
}, { immediate: true })

// 初始化应用
onMounted(async () => {
  // 初始化主题
  themeStore.initTheme()

  // 初始化用户状态
  await userStore.initUserState()

  // 初始化性能监控
  initPerformanceMonitoring()

  // 初始化无障碍功能
  initAccessibility()
})
</script>


