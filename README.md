# AIC Core Service - AI社区核心服务系统

<div align="center">

![Java](https://img.shields.io/badge/Java-1.8-orange.svg)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-green.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0-blue.svg)
![MyBatis](https://img.shields.io/badge/MyBatis-3.5.13-red.svg)
![Redis](https://img.shields.io/badge/Redis-6.0-red.svg)
![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)

AI社区系统的核心服务层，负责统一管理所有共享业务数据和核心业务逻辑

[后端服务](./backend/README.md) | [前端界面](./frontend/README.md) | [技术架构](../AI%20Community/02-技术架构/模块详细设计/Core%20Service%20模块完整方案说明.md)

</div>

## 📋 目录

- [模块概述](#模块概述)
- [核心特性](#核心特性)
- [技术架构](#技术架构)
- [功能模块](#功能模块)
- [快速开始](#快速开始)
- [API文档](#api文档)
- [部署指南](#部署指南)
- [开发指南](#开发指南)
- [监控运维](#监控运维)
- [常见问题](#常见问题)

## 🎯 模块概述

Core Service 模块是整个AI社区系统的**核心服务层**，作为系统的数据和业务逻辑中枢，负责统一管理所有共享业务数据和核心业务逻辑。

### 核心职责

- **数据中心化管理** - 独家管理共享业务MySQL数据库，提供统一的数据访问接口
- **业务逻辑统一** - 集中处理所有核心业务逻辑，确保业务规则的一致性
- **服务编排协调** - 协调AI服务、数据采集服务等外围服务的调用
- **API契约提供** - 为Admin和Portal服务提供稳定、统一的API接口
- **数据一致性保障** - 确保跨服务的数据一致性和事务完整性
- **缓存策略管理** - 统一管理数据缓存策略，提升系统性能
- **权限控制中心** - 提供统一的资源权限验证服务

### 系统定位

```mermaid
graph TB
    A[Admin管理端] --> C[Core Service]
    B[Portal用户端] --> C
    C --> D[AI Service]
    C --> E[Support Service]
    C --> F[共享业务数据库]
    C --> G[Redis缓存]
    C --> H[Elasticsearch]
```

## ✨ 核心特性

### 🏗️ 架构特性
- **微服务架构** - 基于Spring Boot的模块化设计
- **分层架构** - Controller、Service、DAO三层架构
- **统一数据访问** - 独家管理共享业务数据库
- **服务编排** - 协调多个外围服务的调用

### 🔐 安全特性
- **JWT认证** - 无状态令牌认证机制
- **OAuth2集成** - 支持Google、GitHub社交登录
- **权限控制** - 基于角色的访问控制(RBAC)
- **数据安全** - SQL注入防护、XSS防护

### 🚀 性能特性
- **Redis缓存** - 分布式缓存提升性能
- **连接池管理** - Druid连接池优化数据库访问
- **异步处理** - 支持异步任务处理
- **搜索优化** - Elasticsearch全文搜索

### 📊 监控特性
- **健康检查** - 系统状态实时监控
- **性能监控** - Micrometer + Prometheus
- **日志管理** - Logback + ELK Stack
- **API文档** - Swagger 3.0 + Knife4j

## 🏛️ 技术架构

### 技术栈

| 分类 | 技术 | 版本 | 说明 |
|------|------|------|------|
| 核心框架 | Spring Boot | 2.7.18 | 微服务基础框架 |
| 数据访问 | MyBatis | 3.5.13 | ORM框架 |
| 数据访问 | MyBatis-Plus | 3.5.3.1 | MyBatis增强工具 |
| 数据库 | MySQL | 8.0 | 关系型数据库 |
| 连接池 | Druid | 1.2.16 | 数据库连接池 |
| 缓存 | Redis | 6.0 | 分布式缓存 |
| 消息队列 | RabbitMQ | 3.11 | 异步消息处理 |
| 搜索引擎 | Elasticsearch | 8.0 | 全文搜索 |
| 服务发现 | Nacos | 2.2.0 | 服务注册发现 |
| 配置管理 | Nacos Config | - | 动态配置管理 |
| 安全框架 | Spring Security | - | 安全认证授权 |
| 监控 | Micrometer | - | 性能监控 |
| 限流熔断 | Sentinel | - | 流量控制 |

### 分层架构

```
┌─────────────────────────────────────────┐
│              Web Layer                   │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ Controller  │  │   Exception     │   │
│  │             │  │   Handler       │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│            Service Layer                 │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   Business  │  │   Integration   │   │
│  │   Service   │  │   Service       │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│             DAO Layer                    │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │   Mapper    │  │     Entity      │   │
│  │             │  │                 │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
┌─────────────────────────────────────────┐
│           Infrastructure                 │
│  ┌─────────┐ ┌─────────┐ ┌─────────────┐ │
│  │  MySQL  │ │  Redis  │ │Elasticsearch│ │
│  └─────────┘ └─────────┘ └─────────────┘ │
└─────────────────────────────────────────┘
```

## 🧩 功能模块

### 用户管理模块 (User Module)
- **用户认证** - 登录、注册、密码管理
- **OAuth2集成** - Google、GitHub社交登录
- **用户信息** - 个人资料、偏好设置
- **权限管理** - 角色分配、权限控制

### 知识管理模块 (Knowledge Module)
- **知识创建** - 支持富文本、Markdown编辑
- **知识分类** - 多级分类、标签管理
- **知识搜索** - 全文搜索、智能推荐
- **版本控制** - 知识版本管理、历史记录

### 内容管理模块 (Content Module)
- **资讯管理** - 新闻资讯、技术动态
- **解决方案** - 技术方案、最佳实践
- **内容来源** - RSS源管理、自动采集
- **内容审核** - 质量评估、审核流程

### 分类标签模块 (Category & Tag Module)
- **分类管理** - 层级分类、分类树
- **标签管理** - 标签创建、热门标签
- **智能分类** - AI辅助分类推荐
- **关联管理** - 内容分类关联

### 互动社交模块 (Interaction Module)
- **评论系统** - 多级评论、回复功能
- **点赞收藏** - 内容互动、个人收藏
- **关注系统** - 用户关注、动态推送
- **消息通知** - 系统消息、互动通知

### 搜索服务模块 (Search Module)
- **全文搜索** - Elasticsearch全文检索
- **智能推荐** - 基于用户行为的推荐
- **搜索分析** - 搜索统计、热词分析
- **索引管理** - 搜索索引维护、更新

## 🚀 快速开始

### 环境要求

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| JDK | 1.8+ | Java开发环境 |
| Maven | 3.6+ | 项目构建工具 |
| MySQL | 8.0+ | 关系型数据库 |
| Redis | 6.0+ | 缓存数据库 |
| Elasticsearch | 8.0+ | 搜索引擎 |

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd aic_core_service
```

#### 2. 数据库初始化（多数据源）
```bash
# 创建多个数据库
mysql -u root -p
CREATE DATABASE ai_community_shared CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE ai_community_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE ai_community_portal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入初始化脚本
mysql -u root -p ai_community_shared < backend/sql/init.sql
# 根据需要为admin和portal数据库导入相应的初始化脚本
```

#### 3. 配置文件修改（多数据源）
```yaml
# backend/web-api/src/main/resources/application.yml
spring:
  datasource:
    # 主数据源
    primary:
      url: ***********************************************
      username: your_username
      password: your_password
    # 管理端数据源
    admin:
      url: **********************************************
      username: your_username
      password: your_password
    # 门户端数据源
    portal:
      url: ***********************************************
      username: your_username
      password: your_password
```

> **多数据源说明**：系统现已支持多数据源配置，详细配置说明请参考 [多数据源配置文档](backend/docs/multi-datasource-config.md)

#### 4. 启动Redis
```bash
redis-server
```

#### 5. 启动Elasticsearch
```bash
# 使用Docker启动
docker run -d --name elasticsearch \
  -p 9200:9200 -p 9300:9300 \
  -e "discovery.type=single-node" \
  elasticsearch:8.0.0
```

#### 6. 编译运行
```bash
# 后端服务
cd backend
mvn clean install
mvn spring-boot:run

# 前端服务
cd frontend
npm install
npm run dev
```

### 验证安装

访问以下地址验证服务是否正常启动：

- **后端API**: http://localhost:8080/api/health
- **前端界面**: http://localhost:3000
- **API文档**: http://localhost:8080/swagger-ui.html
- **数据库监控**: http://localhost:8080/druid

## 📚 API文档

### 核心API接口

#### 用户管理API
```http
POST /api/auth/login          # 用户登录
POST /api/auth/register       # 用户注册
GET  /api/auth/me            # 获取当前用户信息
POST /api/auth/logout        # 用户登出
POST /api/auth/refresh       # 刷新令牌
```

#### 知识管理API
```http
GET    /api/knowledge        # 获取知识列表
POST   /api/knowledge        # 创建知识
GET    /api/knowledge/{id}   # 获取知识详情
PUT    /api/knowledge/{id}   # 更新知识
DELETE /api/knowledge/{id}   # 删除知识
```

#### 内容管理API
```http
GET    /api/content/news     # 获取资讯列表
POST   /api/content/news     # 创建资讯
GET    /api/content/solutions # 获取解决方案
POST   /api/content/solutions # 创建解决方案
```

#### 搜索API
```http
GET /api/search              # 全文搜索
GET /api/search/suggestions  # 搜索建议
GET /api/search/analytics    # 搜索分析
```

### API响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "timestamp": 1640995200000
}
```

### 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | - |
| 400 | 请求参数错误 | 检查请求参数 |
| 401 | 未授权 | 检查认证信息 |
| 403 | 权限不足 | 检查用户权限 |
| 404 | 资源不存在 | 检查资源ID |
| 500 | 服务器内部错误 | 联系技术支持 |

## 🚢 部署指南

### Docker部署

#### 1. 构建镜像
```bash
# 后端镜像
cd backend
docker build -t aic-core-service-backend .

# 前端镜像
cd frontend
docker build -t aic-core-service-frontend .
```

#### 2. Docker Compose部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: ai_community_shared
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:6.0
    ports:
      - "6379:6379"

  elasticsearch:
    image: elasticsearch:8.0.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  backend:
    image: aic-core-service-backend
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
      - elasticsearch

  frontend:
    image: aic-core-service-frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  mysql_data:
```

#### 3. 启动服务
```bash
docker-compose up -d
```

### 生产环境部署

#### 1. 环境配置
```yaml
# application-prod.yml
spring:
  profiles:
    active: prod
  datasource:
    url: ************************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  redis:
    host: prod-redis
    port: 6379
    password: ${REDIS_PASSWORD}
```

#### 2. 健康检查
```bash
# 检查服务状态
curl http://localhost:8080/api/health

# 检查数据库连接
curl http://localhost:8080/actuator/health/db

# 检查Redis连接
curl http://localhost:8080/actuator/health/redis
```

## 👨‍💻 开发指南

### 项目结构

```
aic_core_service/
├── backend/                 # 后端服务
│   ├── common/             # 公共模块
│   │   ├── src/main/java/
│   │   │   └── com/jdl/aic/core/service/common/
│   │   │       ├── config/     # 配置类
│   │   │       ├── constant/   # 常量定义
│   │   │       ├── exception/  # 异常处理
│   │   │       ├── result/     # 响应结果
│   │   │       └── util/       # 工具类
│   ├── dao/                # 数据访问层
│   │   ├── src/main/java/
│   │   │   └── com/jdl/aic/core/service/dao/
│   │   │       ├── entity/     # 实体类
│   │   │       └── mapper/     # Mapper接口
│   │   └── src/main/resources/
│   │       └── mapper/         # MyBatis映射文件
│   ├── service/            # 业务逻辑层
│   │   └── src/main/java/
│   │       └── com/jdl/aic/core/service/service/
│   │           ├── impl/       # 服务实现
│   │           └── integration/ # 外部服务集成
│   └── web-api/            # Web接口层
│       ├── src/main/java/
│       │   └── com/jdl/aic/core/service/
│       │       ├── controller/ # 控制器
│       │       ├── config/     # 配置类
│       │       └── Application.java
│       └── src/main/resources/
│           ├── application.yml # 配置文件
│           └── static/         # 静态资源
└── frontend/               # 前端界面
    ├── src/
    │   ├── components/     # Vue组件
    │   ├── views/          # 页面视图
    │   ├── router/         # 路由配置
    │   ├── store/          # 状态管理
    │   └── utils/          # 工具函数
    ├── public/             # 公共资源
    └── package.json        # 依赖配置
```

### 开发规范

#### 代码规范
- **Java编码规范** - 遵循阿里巴巴Java开发手册
- **命名规范** - 类名使用PascalCase，方法名使用camelCase
- **注释规范** - 类和方法必须添加JavaDoc注释
- **异常处理** - 统一使用自定义异常和全局异常处理

#### 数据库规范
- **表名规范** - 使用下划线分隔的小写字母
- **字段规范** - 必须包含create_time、update_time字段
- **索引规范** - 合理创建索引，避免过度索引
- **事务规范** - 合理使用事务，避免长事务

#### API设计规范
- **RESTful设计** - 遵循REST API设计原则
- **版本控制** - API版本通过URL路径管理
- **参数验证** - 使用Hibernate Validator进行参数校验
- **响应格式** - 统一使用Result包装响应数据

### 开发流程

#### 1. 功能开发
```bash
# 1. 创建功能分支
git checkout -b feature/user-management

# 2. 编写代码
# 3. 单元测试
mvn test

# 4. 代码检查
mvn checkstyle:check

# 5. 提交代码
git add .
git commit -m "feat: 添加用户管理功能"
git push origin feature/user-management
```

#### 2. 代码审查
- **Pull Request** - 创建PR进行代码审查
- **代码质量** - 使用SonarQube进行代码质量检查
- **测试覆盖率** - 确保测试覆盖率达到80%以上

#### 3. 集成测试
```bash
# 运行集成测试
mvn verify

# 性能测试
mvn gatling:test
```

## 📊 监控运维

### 系统监控

#### 应用监控
- **健康检查** - Spring Boot Actuator
- **性能指标** - Micrometer + Prometheus
- **JVM监控** - 内存、GC、线程监控
- **业务指标** - 自定义业务监控指标

#### 基础设施监控
- **数据库监控** - MySQL性能监控
- **缓存监控** - Redis性能监控
- **搜索引擎监控** - Elasticsearch集群监控
- **服务器监控** - CPU、内存、磁盘、网络

### 日志管理

#### 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/aic-core-service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/aic-core-service.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
```

#### 日志收集
- **ELK Stack** - Elasticsearch + Logstash + Kibana
- **日志聚合** - 多实例日志统一收集
- **日志分析** - 错误日志分析、性能日志分析

### 告警配置

#### 告警规则
```yaml
# prometheus-rules.yml
groups:
  - name: aic-core-service
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"

      - alert: HighMemoryUsage
        expr: jvm_memory_used_bytes / jvm_memory_max_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage detected"
```

#### 通知渠道
- **邮件通知** - 关键告警邮件通知
- **短信通知** - 紧急告警短信通知
- **钉钉/企微** - 团队协作工具通知
- **PagerDuty** - 值班人员通知

## ❓ 常见问题

### 启动问题

#### Q: 启动时提示数据库连接失败
**A:** 检查以下配置：
1. 数据库服务是否启动
2. 数据库连接配置是否正确
3. 数据库用户权限是否足够
4. 防火墙是否阻止连接

#### Q: Redis连接失败
**A:** 检查以下配置：
1. Redis服务是否启动
2. Redis连接配置是否正确
3. Redis密码配置是否正确
4. 网络连接是否正常

### 性能问题

#### Q: 接口响应慢
**A:** 排查步骤：
1. 检查数据库查询性能
2. 检查缓存命中率
3. 检查网络延迟
4. 分析JVM性能指标

#### Q: 内存使用过高
**A:** 优化建议：
1. 调整JVM堆内存大小
2. 优化数据库查询
3. 检查内存泄漏
4. 优化缓存策略

### 功能问题

#### Q: OAuth2登录失败
**A:** 检查配置：
1. OAuth2客户端ID和密钥
2. 回调URL配置
3. 权限范围配置
4. 网络连接状态

#### Q: 搜索功能异常
**A:** 排查步骤：
1. 检查Elasticsearch服务状态
2. 检查索引是否正常
3. 检查搜索查询语法
4. 查看错误日志

## 🤝 贡献指南

### 参与贡献

我们欢迎所有形式的贡献，包括但不限于：

- 🐛 **Bug报告** - 发现并报告系统缺陷
- 💡 **功能建议** - 提出新功能需求
- 📝 **文档改进** - 完善项目文档
- 🔧 **代码贡献** - 提交代码修复或新功能
- 🧪 **测试用例** - 编写和完善测试用例

### 开发流程

1. **Fork项目** - 从主仓库Fork到个人仓库
2. **创建分支** - 基于develop分支创建功能分支
3. **开发功能** - 按照开发规范编写代码
4. **测试验证** - 运行单元测试和集成测试
5. **提交PR** - 创建Pull Request进行代码审查
6. **合并代码** - 审查通过后合并到主分支

### 提交规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

**Type类型：**
- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具变动

## 📄 许可证

本项目采用 [MIT License](../LICENSE) 开源协议。

## 💬 技术支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 **邮箱支持**: <EMAIL>
- 💬 **技术交流群**: 加入我们的技术交流群
- 🐛 **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- 📖 **技术文档**: [在线文档](https://docs.aic-community.com)

---

<div align="center">

**Made with ❤️ by AIC Core Service Team**

[⬆ 回到顶部](#aic-core-service---ai社区核心服务系统)

</div>
