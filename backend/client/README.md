# AI Community Core Service Client SDK

## 概述

AI Community Core Service Client SDK 是 AI 社区核心服务的客户端软件开发工具包，为 Admin 和 Portal 模块提供统一的服务接口封装。该 SDK 采用轻量级设计，仅包含接口定义和数据传输对象，不包含具体实现，确保最小依赖和最大灵活性。

## 架构设计理念

### 设计原则

1. **统一接口封装**：为所有核心服务提供统一的接口定义，确保调用方式的一致性
2. **轻量级设计**：仅包含接口定义和 DTO，不包含实现逻辑，保持最小依赖
3. **清晰职责分离**：每个服务接口职责明确，避免功能重叠和冗余
4. **易于集成**：提供简单的 Maven 依赖配置，支持快速集成
5. **向前兼容**：接口设计考虑扩展性，支持功能演进

### 架构特点

- **接口优先**：基于接口的设计，支持多种实现方式（Dubbo、HTTP、本地调用等）
- **数据驱动**：统一的数据传输对象设计，确保数据结构的一致性
- **异常统一**：统一的异常处理机制，简化错误处理逻辑
- **分页标准化**：统一的分页请求和响应格式
- **结果包装**：统一的 Result 包装器，标准化返回值格式

## 服务分类和职责

基于最新的接口重构，SDK 提供 17 个核心服务接口，按功能分为四大类：

### 1. 基础服务（Core Services）

#### UserService - 用户管理服务
- **职责**：用户账户管理、团队管理、用户关系维护
- **核心功能**：
  - 用户 CRUD 操作
  - 团队创建和成员管理
  - 用户权限和状态管理
  - 用户关系（关注、好友）管理

#### KnowledgeService - 知识管理服务
- **职责**：知识类型配置、知识内容全生命周期管理
- **核心功能**：
  - 知识类型的配置和管理
  - 知识内容的创建、编辑、发布
  - 知识版本控制
  - 知识搜索和查询

#### CategoryService - 分类管理服务
- **职责**：内容分类体系管理
- **核心功能**：
  - 分类层次结构管理
  - 分类与内容类型绑定
  - 分类状态和权限控制

#### TagService - 标签管理服务
- **职责**：标签体系管理和标签关联
- **核心功能**：
  - 标签创建和管理
  - 标签与内容类型绑定
  - 标签热度和使用统计

### 2. 业务服务（Business Services）

#### SolutionService - 解决方案服务
- **职责**：AI 解决方案的管理和展示
- **核心功能**：
  - 解决方案发布和管理
  - 解决方案分类和标签
  - 解决方案评价和推荐

#### LearningResourceService - 学习资源服务
- **职责**：AI 学习资源的管理和推荐
- **核心功能**：
  - 学习资源收集和管理
  - 资源分类和标签
  - 学习路径推荐

#### CommunityService - 社区服务
- **职责**：社区互动功能管理
- **核心功能**：
  - 社区内容发布和管理
  - 用户互动（点赞、评论、分享）
  - 社区活动和事件管理

#### ContentAssociationService - 内容关联服务
- **职责**：内容间关联关系管理
- **核心功能**：
  - 内容关联关系建立
  - 关联推荐算法
  - 关联关系分析

### 3. 支撑服务（Support Services）

#### FileStorageService - 文件存储服务
- **职责**：文件上传、存储和管理
- **核心功能**：
  - 文件上传和下载
  - 文件类型验证
  - 存储空间管理

#### SearchService - 搜索服务
- **职责**：全文搜索和智能推荐
- **核心功能**：
  - 全文搜索
  - 搜索结果排序
  - 搜索历史和推荐

#### NotificationService - 通知服务
- **职责**：系统通知和消息推送
- **核心功能**：
  - 通知创建和发送
  - 通知模板管理
  - 通知状态跟踪

#### ContentSourceService - 内容源服务
- **职责**：外部内容源管理和同步
- **核心功能**：
  - 内容源配置
  - 内容抓取和同步
  - 内容质量评估

#### AnalyticsService - 数据分析服务
- **职责**：统一的数据分析和统计
- **核心功能**：
  - 多维度数据统计
  - 趋势分析
  - 排行榜生成
  - 智能推荐

### 4. 专用服务（Specialized Services）

#### AdminService - 管理员服务
- **职责**：管理员专用功能
- **核心功能**：
  - 管理员权限管理
  - 系统配置管理
  - 审核流程管理

#### AdminSystemService - 系统管理服务
- **职责**：系统级配置和监控
- **核心功能**：
  - 系统参数配置
  - 系统监控和日志
  - 系统维护工具

#### PortalUserService - 门户用户服务
- **职责**：门户用户专用功能
- **核心功能**：
  - 用户个性化设置
  - 用户行为跟踪
  - 用户体验优化

#### PortalRecommendationService - 门户推荐服务
- **职责**：门户个性化推荐
- **核心功能**：
  - 个性化内容推荐
  - 推荐算法优化
  - 推荐效果分析

## 接口设计模式和规范

### 统一设计模式

所有服务接口遵循统一的设计模式，确保一致的开发体验：

#### 1. 命名规范
```java
// 服务接口命名：{功能域}Service
public interface UserService { }
public interface KnowledgeService { }

// 方法命名：{动作}{对象}{条件}
Result<UserDTO> getUserById(Long id);
Result<PageResult<UserDTO>> getUserList(PageRequest pageRequest, Map<String, Object> filters);
Result<Long> createUser(UserCreateRequest request);
Result<Boolean> updateUser(Long id, UserUpdateRequest request);
Result<Boolean> deleteUser(Long id);
```

#### 2. 参数设计
```java
// 简单查询：直接参数
Result<UserDTO> getUserById(Long id);

// 复杂查询：Map 参数
Result<PageResult<UserDTO>> getUserList(PageRequest pageRequest, Map<String, Object> filters);

// 创建操作：专用 Request 对象
Result<Long> createUser(UserCreateRequest request);

// 更新操作：ID + Request 对象
Result<Boolean> updateUser(Long id, UserUpdateRequest request);
```

#### 3. 返回值设计
```java
// 统一使用 Result<T> 包装返回值
public class Result<T> {
    private boolean success;
    private String code;
    private String message;
    private T data;
    // ... getter/setter
}

// 分页查询使用 PageResult<T>
public class PageResult<T> {
    private List<T> records;
    private long total;
    private long current;
    private long size;
    // ... getter/setter
}
```

#### 4. 异常处理
```java
// 统一异常定义
public class AicServiceException extends RuntimeException {
    private String code;
    private String message;
    // ... constructor and methods
}

// 常用异常类型
public class AicValidationException extends AicServiceException { }
public class AicBusinessException extends AicServiceException { }
public class AicSystemException extends AicServiceException { }
```

### 接口扩展性设计

#### 1. 参数扩展
```java
// 使用 Map 支持动态参数扩展
Result<PageResult<UserDTO>> getUserList(
    PageRequest pageRequest,
    Map<String, Object> filters  // 支持任意过滤条件扩展
);

// 使用 params 支持功能参数扩展
Result<Object> getStatistics(
    String domain,
    String metricType,
    Map<String, Object> params  // 支持统计参数扩展
);
```

#### 2. 返回值扩展
```java
// 使用泛型支持返回值类型扩展
Result<Object> getStatistics(String domain, String metricType, Map<String, Object> filters);

// 使用继承支持 DTO 扩展
public class UserDTO extends BaseDTO {
    // 基础用户信息
}

public class UserDetailDTO extends UserDTO {
    // 扩展的详细信息
}
```

## 快速开始指南

### 1. 添加 Maven 依赖

在您的项目 `pom.xml` 中添加以下依赖：

```xml
<dependency>
    <groupId>com.jdl.aic.core.service</groupId>
    <artifactId>client</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>
```

### 2. 基本使用示例

```java
// 1. 创建客户端实例
AicCoreServiceClient client = new AicCoreServiceClient();

// 2. 获取服务实例
UserService userService = client.getUserService();
KnowledgeService knowledgeService = client.getKnowledgeService();
AnalyticsService analyticsService = client.getAnalyticsService();

// 3. 调用服务方法
// 用户管理
Result<UserDTO> user = userService.getUserById(1L);
Result<PageResult<UserDTO>> userList = userService.getUserList(
    new PageRequest(1, 10),
    Map.of("status", "active")
);

// 知识管理
Result<KnowledgeTypeDTO> knowledgeType = knowledgeService.getKnowledgeTypeById(1L);
Result<PageResult<KnowledgeDTO>> knowledgeList = knowledgeService.getKnowledgeList(
    new PageRequest(1, 10),
    Map.of("typeId", 1L, "status", "published")
);

// 数据分析
Result<Object> statistics = analyticsService.getStatistics(
    "knowledge",
    "count",
    Map.of("period", "month")
);
```

### 3. 错误处理

```java
try {
    Result<UserDTO> result = userService.getUserById(1L);
    if (result.isSuccess()) {
        UserDTO user = result.getData();
        // 处理成功结果
    } else {
        // 处理业务错误
        System.err.println("Error: " + result.getMessage());
    }
} catch (AicServiceException e) {
    // 处理系统异常
    System.err.println("System error: " + e.getMessage());
}
```

## 依赖管理和集成说明

### Maven 配置

#### 1. 最小依赖配置

Client SDK 采用最小依赖设计，仅包含必要的依赖：

```xml
<!-- 参数校验注解 (仅API定义需要) -->
<dependency>
    <groupId>jakarta.validation</groupId>
    <artifactId>jakarta.validation-api</artifactId>
    <scope>provided</scope>
</dependency>

<!-- JSON注解 (仅用于DTO定义) -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-annotations</artifactId>
    <scope>provided</scope>
</dependency>
```

#### 2. 与 Spring Boot 集成

```xml
<!-- Spring Boot 项目中的集成 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter</artifactId>
</dependency>

<dependency>
    <groupId>com.jdl.aic.core.service</groupId>
    <artifactId>client</artifactId>
    <version>1.0.0</version>
</dependency>
```

#### 3. 实现依赖选择

根据您的实现方式选择相应的依赖：

```xml
<!-- Dubbo 实现 -->
<dependency>
    <groupId>org.apache.dubbo</groupId>
    <artifactId>dubbo-spring-boot-starter</artifactId>
</dependency>

<!-- HTTP 实现 -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
</dependency>

<!-- 本地实现 -->
<dependency>
    <groupId>com.jdl.aic.core.service</groupId>
    <artifactId>service-impl</artifactId>
</dependency>
```

### 配置示例

#### Spring Boot 配置

```yaml
# application.yml
aic:
  core-service:
    # 服务发现配置
    discovery:
      enabled: true
      registry: nacos://localhost:8848

    # 超时配置
    timeout:
      connect: 3000
      read: 5000

    # 重试配置
    retry:
      enabled: true
      max-attempts: 3
```

## 设计决策和权衡说明

### 1. 轻量级设计决策

**决策**：Client SDK 仅包含接口定义和 DTO，不包含实现逻辑

**理由**：
- **最小依赖**：避免引入不必要的依赖，减少版本冲突
- **实现灵活性**：支持多种实现方式（Dubbo、HTTP、本地调用）
- **部署简化**：客户端包体积小，部署和分发更便捷
- **版本独立**：接口定义和实现可以独立演进

**权衡**：
- 需要额外的实现包来提供具体功能
- 调试时无法直接查看实现代码

### 2. 服务整合决策

**决策**：将 23 个服务整合为 17 个服务

**理由**：
- **消除冗余**：统计分析服务存在 80% 的功能重叠
- **简化使用**：减少学习成本和集成复杂度
- **职责清晰**：明确各服务的边界和职责
- **维护效率**：减少接口数量，降低维护成本

**权衡**：
- 单个服务的功能范围增大
- 需要重新学习新的服务结构

### 3. 统一返回值设计

**决策**：所有接口统一使用 `Result<T>` 包装返回值

**理由**：
- **错误处理统一**：标准化的错误信息格式
- **状态明确**：明确区分成功和失败状态
- **扩展性好**：支持添加更多元数据信息
- **调试友好**：提供详细的错误码和消息

**权衡**：
- 增加了返回值的复杂度
- 需要额外的解包操作

### 4. Map 参数设计

**决策**：复杂查询使用 `Map<String, Object>` 作为参数

**理由**：
- **扩展性强**：支持动态添加查询条件
- **向后兼容**：新增参数不影响现有调用
- **实现简化**：避免创建大量的 Request 对象
- **灵活性高**：支持不同业务场景的参数组合

**权衡**：
- 类型安全性降低
- 参数验证需要在运行时进行

## 最佳实践建议

### 1. 服务选择指南

#### 基础数据操作
```java
// 用户相关操作 -> UserService
UserService userService = client.getUserService();

// 知识内容操作 -> KnowledgeService
KnowledgeService knowledgeService = client.getKnowledgeService();

// 分类标签操作 -> CategoryService, TagService
CategoryService categoryService = client.getCategoryService();
TagService tagService = client.getTagService();
```

#### 业务功能实现
```java
// 解决方案管理 -> SolutionService
SolutionService solutionService = client.getSolutionService();

// 学习资源管理 -> LearningResourceService
LearningResourceService learningService = client.getLearningResourceService();

// 社区互动 -> CommunityService
CommunityService communityService = client.getCommunityService();
```

#### 数据分析需求
```java
// 所有统计分析需求 -> AnalyticsService
AnalyticsService analyticsService = client.getAnalyticsService();

// 用户行为统计
Result<Object> userStats = analyticsService.getStatistics(
    "user", "behavior", Map.of("userId", 1L, "period", "week")
);

// 内容热度排行
Result<List<Object>> hotContent = analyticsService.getRanking(
    "popular_content", 10, Map.of("contentType", "knowledge")
);
```

### 2. 错误处理最佳实践

```java
// 1. 统一错误处理
public class ServiceHelper {
    public static <T> T handleResult(Result<T> result) {
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new BusinessException(result.getCode(), result.getMessage());
    }
}

// 2. 使用示例
try {
    UserDTO user = ServiceHelper.handleResult(
        userService.getUserById(1L)
    );
    // 处理成功结果
} catch (BusinessException e) {
    // 统一错误处理
    log.error("Service call failed: {}", e.getMessage());
}
```

### 3. 分页查询最佳实践

```java
// 1. 构建分页请求
PageRequest pageRequest = new PageRequest(1, 20); // 页码从1开始

// 2. 构建查询条件
Map<String, Object> filters = new HashMap<>();
filters.put("status", "active");
filters.put("createTime", Map.of("gte", "2024-01-01"));

// 3. 执行查询
Result<PageResult<UserDTO>> result = userService.getUserList(pageRequest, filters);

// 4. 处理结果
if (result.isSuccess()) {
    PageResult<UserDTO> pageResult = result.getData();
    List<UserDTO> users = pageResult.getRecords();
    long total = pageResult.getTotal();
    // 处理分页数据
}
```

### 4. 性能优化建议

#### 批量操作
```java
// 优先使用批量操作接口
Result<List<Object>> batchResult = userService.batchCreateUsers(userRequests);

// 避免循环调用单个操作
// ❌ 不推荐
for (UserCreateRequest request : requests) {
    userService.createUser(request);
}
```

#### 缓存策略
```java
// 对于频繁查询的数据，建议在客户端实现缓存
@Cacheable(value = "knowledgeTypes", key = "#id")
public KnowledgeTypeDTO getKnowledgeType(Long id) {
    return ServiceHelper.handleResult(
        knowledgeService.getKnowledgeTypeById(id)
    );
}
```

## 版本兼容性

### 当前版本：1.0.0

- **接口稳定性**：所有接口均为稳定版本，保证向后兼容
- **DTO 扩展**：新增字段采用可选方式，不影响现有代码
- **方法演进**：新增方法不会影响现有方法的签名和行为

### 升级指南

当有新版本发布时：

1. **查看变更日志**：了解新增功能和变更内容
2. **测试兼容性**：在测试环境验证现有代码的兼容性
3. **渐进式升级**：建议先升级测试环境，再升级生产环境

## 技术支持

### 文档资源

- **接口文档**：详见 `docs/` 目录下的各服务接口文档
- **示例代码**：参考 `src/test/` 目录下的测试用例
- **最佳实践**：查看项目 Wiki 中的最佳实践指南

### 问题反馈

如遇到问题，请通过以下方式反馈：

1. **GitHub Issues**：提交 Bug 报告或功能请求
2. **技术论坛**：在内部技术论坛讨论使用问题
3. **邮件支持**：发送邮件至 <EMAIL>

---

**AI Community Core Service Client SDK** - 为 AI 社区提供统一、高效、易用的服务接口封装。
```