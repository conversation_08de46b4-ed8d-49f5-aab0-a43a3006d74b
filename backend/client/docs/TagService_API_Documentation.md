# TagService API 文档

## 接口概述

**接口名称**: TagService  
**包路径**: com.jdl.aic.core.service.client.service.TagService  
**功能描述**: 标签管理服务接口，提供标签管理功能，包括标签的CRUD操作和使用统计、标签热门排行和推荐、标签批量操作和状态管理、知识类型专属标签查询等。  
**版本**: 2.0.0  
**作者**: AI Community Development Team  

## 数据模型

### TagDTO
标签数据传输对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 标签ID |
| name | String | 标签名称 |
| description | String | 标签描述 |
| contentCategory | String | 内容类别（knowledge, solution, learning_resource, general） |
| subTypeId | Long | 细分类型ID（用于知识类型专属标签） |
| tagCategory | String | 标签分组（技术、业务、工具等） |
| color | String | 标签颜色（用于前端显示） |
| usageCount | Integer | 使用次数 |
| isActive | Boolean | 是否启用 |
| isHot | Boolean | 是否热门标签 |
| sortOrder | Integer | 排序权重 |
| createTime | Date | 创建时间 |
| updateTime | Date | 更新时间 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |

## API 接口列表

### 1. 标签查询

#### 1.1 获取标签列表（分页）

**方法名**: `getTagList`  
**描述**: 获取标签列表，支持分页和多种过滤条件  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求参数
- `contentCategory` (String, 可选): 内容类别过滤
- `tagCategory` (String, 可选): 标签分组过滤
- `isActive` (Boolean, 可选): 启用状态过滤
- `search` (String, 可选): 搜索关键词

**返回值**: `Result<PageResult<TagDTO>>`

**调用示例**:
```java
// 基本分页查询
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

Result<PageResult<TagDTO>> result = tagService.getTagList(
    pageRequest, 
    null, 
    null, 
    true,  // 只查询启用的标签
    null
);

if (result.isSuccess()) {
    PageResult<TagDTO> pageResult = result.getData();
    System.out.println("总数: " + pageResult.getTotal());
    
    for (TagDTO tag : pageResult.getList()) {
        System.out.println("标签: " + tag.getName());
        System.out.println("使用次数: " + tag.getUsageCount());
        System.out.println("分组: " + tag.getTagCategory());
    }
}

// 查询技术类标签
Result<PageResult<TagDTO>> techResult = tagService.getTagList(
    pageRequest,
    "knowledge",  // 知识类别
    "技术",       // 技术分组
    true,
    null
);

// 搜索标签
Result<PageResult<TagDTO>> searchResult = tagService.getTagList(
    pageRequest,
    null,
    null,
    null,
    "Java"  // 搜索关键词
);
```

#### 1.2 获取标签列表（支持细分类型过滤）

**方法名**: `getTagList`  
**描述**: 获取标签列表，支持知识类型专属标签过滤  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求参数
- `contentCategory` (String, 可选): 内容类别过滤
- `subTypeId` (Long, 可选): 细分类型ID过滤（用于知识类型专属标签）
- `tagCategory` (String, 可选): 标签分组过滤
- `isActive` (Boolean, 可选): 启用状态过滤
- `search` (String, 可选): 搜索关键词

**返回值**: `Result<PageResult<TagDTO>>`

**调用示例**:
```java
// 查询知识类型专属标签
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

Result<PageResult<TagDTO>> result = tagService.getTagList(
    pageRequest,
    "knowledge",  // 知识类别
    1001L,        // 特定知识类型ID
    null,
    true,
    null
);

if (result.isSuccess()) {
    PageResult<TagDTO> pageResult = result.getData();
    System.out.println("知识类型专属标签数量: " + pageResult.getTotal());
    
    for (TagDTO tag : pageResult.getList()) {
        System.out.println("专属标签: " + tag.getName());
    }
}
```

#### 1.3 获取热门标签

**方法名**: `getPopularTags`  
**描述**: 获取热门标签列表，按使用次数排序  

**请求参数**:
- `contentCategory` (String, 可选): 内容类别过滤
- `limit` (Integer, 可选): 返回数量限制

**返回值**: `Result<List<TagDTO>>`

**调用示例**:
```java
// 获取热门标签（前10个）
Result<List<TagDTO>> result = tagService.getPopularTags(null, 10);

if (result.isSuccess()) {
    List<TagDTO> popularTags = result.getData();
    System.out.println("热门标签:");
    
    for (int i = 0; i < popularTags.size(); i++) {
        TagDTO tag = popularTags.get(i);
        System.out.println((i + 1) + ". " + tag.getName() + 
            " (使用次数: " + tag.getUsageCount() + ")");
    }
}

// 获取知识类别的热门标签
Result<List<TagDTO>> knowledgeHotTags = tagService.getPopularTags("knowledge", 5);
```

#### 1.4 获取热门标签（支持细分类型过滤）

**方法名**: `getPopularTags`  
**描述**: 获取热门标签列表，支持知识类型专属标签过滤  

**请求参数**:
- `contentCategory` (String, 可选): 内容类别过滤
- `subTypeId` (Long, 可选): 细分类型ID过滤
- `limit` (Integer, 可选): 返回数量限制

**返回值**: `Result<List<TagDTO>>`

**调用示例**:
```java
// 获取特定知识类型的热门标签
Result<List<TagDTO>> result = tagService.getPopularTags(
    "knowledge", 
    1001L,  // 知识类型ID
    8       // 返回前8个
);

if (result.isSuccess()) {
    List<TagDTO> hotTags = result.getData();
    System.out.println("知识类型热门标签:");
    
    for (TagDTO tag : hotTags) {
        System.out.println("- " + tag.getName() + 
            " (使用: " + tag.getUsageCount() + "次)");
    }
}
```

#### 1.5 根据ID获取标签详情

**方法名**: `getTagById`  
**描述**: 根据标签ID获取详细信息  

**请求参数**:
- `id` (Long): 标签ID

**返回值**: `Result<TagDTO>`

**调用示例**:
```java
Result<TagDTO> result = tagService.getTagById(1001L);

if (result.isSuccess()) {
    TagDTO tag = result.getData();
    System.out.println("标签名称: " + tag.getName());
    System.out.println("标签描述: " + tag.getDescription());
    System.out.println("内容类别: " + tag.getContentCategory());
    System.out.println("标签分组: " + tag.getTagCategory());
    System.out.println("使用次数: " + tag.getUsageCount());
    System.out.println("是否热门: " + tag.getIsHot());
}
```

#### 1.6 根据名称获取标签

**方法名**: `getTagByName`  
**描述**: 根据标签名称和内容类别获取标签信息  

**请求参数**:
- `name` (String): 标签名称
- `contentCategory` (String): 内容类别

**返回值**: `Result<TagDTO>`

**调用示例**:
```java
Result<TagDTO> result = tagService.getTagByName("Java", "knowledge");

if (result.isSuccess()) {
    TagDTO tag = result.getData();
    System.out.println("找到标签: " + tag.getName());
    System.out.println("标签ID: " + tag.getId());
    System.out.println("使用次数: " + tag.getUsageCount());
} else {
    System.out.println("标签不存在，可能需要创建");
}
```

#### 1.7 获取知识类型可用的标签列表

**方法名**: `getAvailableTagsForKnowledgeType`  
**描述**: 获取指定知识类型可以使用的标签，包括专属标签、通用知识标签和全局通用标签  

**请求参数**:
- `knowledgeTypeId` (Long): 知识类型ID

**返回值**: `Result<List<TagDTO>>`

**调用示例**:
```java
Result<List<TagDTO>> result = tagService.getAvailableTagsForKnowledgeType(1001L);

if (result.isSuccess()) {
    List<TagDTO> availableTags = result.getData();
    System.out.println("知识类型可用标签:");
    
    for (TagDTO tag : availableTags) {
        String type = "";
        if (tag.getSubTypeId() != null) {
            type = " (专属)";
        } else if ("knowledge".equals(tag.getContentCategory())) {
            type = " (通用知识)";
        } else if ("general".equals(tag.getContentCategory())) {
            type = " (全局通用)";
        }
        System.out.println("- " + tag.getName() + type + 
            " [使用: " + tag.getUsageCount() + "次]");
    }
}
```

#### 1.8 根据细分类型获取标签列表

**方法名**: `getTagsBySubType`  
**描述**: 根据内容类别和细分类型ID获取标签列表  

**请求参数**:
- `contentCategory` (String): 内容类别
- `subTypeId` (Long): 细分类型ID

**返回值**: `Result<List<TagDTO>>`

**调用示例**:
```java
Result<List<TagDTO>> result = tagService.getTagsBySubType("knowledge", 1001L);

if (result.isSuccess()) {
    List<TagDTO> tags = result.getData();
    System.out.println("细分类型专属标签:");
    
    for (TagDTO tag : tags) {
        System.out.println("- " + tag.getName() + 
            " (分组: " + tag.getTagCategory() + ")");
    }
}
```

### 2. 标签管理

#### 2.1 创建标签

**方法名**: `createTag`  
**描述**: 创建新的标签  

**请求参数**:
- `tag` (TagDTO): 标签信息

**返回值**: `Result<TagDTO>`

**调用示例**:
```java
TagDTO newTag = new TagDTO();
newTag.setName("Spring Boot");
newTag.setDescription("Spring Boot框架相关标签");
newTag.setContentCategory("knowledge");
newTag.setSubTypeId(1001L);  // 知识类型专属标签
newTag.setTagCategory("技术");
newTag.setColor("#007bff");
newTag.setIsActive(true);
newTag.setSortOrder(100);

Result<TagDTO> result = tagService.createTag(newTag);

if (result.isSuccess()) {
    TagDTO createdTag = result.getData();
    System.out.println("标签创建成功，ID: " + createdTag.getId());
    System.out.println("标签名称: " + createdTag.getName());
}
```

#### 2.2 更新标签信息

**方法名**: `updateTag`  
**描述**: 更新标签信息  

**请求参数**:
- `id` (Long): 标签ID
- `tag` (TagDTO): 更新的标签信息

**返回值**: `Result<TagDTO>`

**调用示例**:
```java
TagDTO updateTag = new TagDTO();
updateTag.setName("Spring Boot 3.x");
updateTag.setDescription("Spring Boot 3.x版本相关标签");
updateTag.setColor("#28a745");
updateTag.setTagCategory("框架");

Result<TagDTO> result = tagService.updateTag(1001L, updateTag);

if (result.isSuccess()) {
    TagDTO updatedTag = result.getData();
    System.out.println("标签更新成功: " + updatedTag.getName());
}
```

#### 2.3 删除标签

**方法名**: `deleteTag`  
**描述**: 删除指定标签  

**请求参数**:
- `id` (Long): 标签ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = tagService.deleteTag(1001L);

if (result.isSuccess()) {
    System.out.println("标签删除成功");
} else {
    System.err.println("删除失败: " + result.getMessage());
    // 可能的原因：标签正在被使用中
}
```

#### 2.4 启用/禁用标签

**方法名**: `toggleTagStatus`  
**描述**: 切换标签的启用状态  

**请求参数**:
- `id` (Long): 标签ID
- `isActive` (Boolean): 是否启用

**返回值**: `Result<Void>`

**调用示例**:
```java
// 禁用标签
Result<Void> result = tagService.toggleTagStatus(1001L, false);

if (result.isSuccess()) {
    System.out.println("标签已禁用");
}

// 启用标签
Result<Void> enableResult = tagService.toggleTagStatus(1001L, true);

if (enableResult.isSuccess()) {
    System.out.println("标签已启用");
}
```

### 3. 标签批量操作

#### 3.1 批量创建标签

**方法名**: `batchCreateTags`  
**描述**: 批量创建多个标签  

**请求参数**:
- `tagNames` (List<String>): 标签名称列表
- `contentCategory` (String): 内容类别

**返回值**: `Result<List<TagDTO>>`

**调用示例**:
```java
List<String> tagNames = Arrays.asList(
    "Spring Boot", "Spring Cloud", "MyBatis", "Redis", "MySQL"
);

Result<List<TagDTO>> result = tagService.batchCreateTags(tagNames, "knowledge");

if (result.isSuccess()) {
    List<TagDTO> createdTags = result.getData();
    System.out.println("批量创建标签成功，共创建: " + createdTags.size() + "个标签");
    
    for (TagDTO tag : createdTags) {
        System.out.println("- " + tag.getName() + " (ID: " + tag.getId() + ")");
    }
} else {
    System.err.println("批量创建失败: " + result.getMessage());
}

// 批量创建业务标签
List<String> businessTags = Arrays.asList("订单管理", "库存管理", "用户管理");
Result<List<TagDTO>> businessResult = tagService.batchCreateTags(businessTags, "solution");
```

### 4. 标签使用统计

#### 4.1 增加标签使用次数

**方法名**: `incrementTagUsage`  
**描述**: 增加标签的使用次数，通常在内容关联标签时调用  

**请求参数**:
- `id` (Long): 标签ID

**返回值**: `Result<Void>`

**调用示例**:
```java
// 当内容关联标签时，增加使用次数
Result<Void> result = tagService.incrementTagUsage(1001L);

if (result.isSuccess()) {
    System.out.println("标签使用次数已增加");
}

// 批量增加多个标签的使用次数
List<Long> tagIds = Arrays.asList(1001L, 1002L, 1003L);
for (Long tagId : tagIds) {
    tagService.incrementTagUsage(tagId);
}
```

#### 4.2 减少标签使用次数

**方法名**: `decrementTagUsage`  
**描述**: 减少标签的使用次数，通常在内容取消关联标签时调用  

**请求参数**:
- `id` (Long): 标签ID

**返回值**: `Result<Void>`

**调用示例**:
```java
// 当内容取消关联标签时，减少使用次数
Result<Void> result = tagService.decrementTagUsage(1001L);

if (result.isSuccess()) {
    System.out.println("标签使用次数已减少");
}

// 批量减少多个标签的使用次数
List<Long> tagIds = Arrays.asList(1001L, 1002L, 1003L);
for (Long tagId : tagIds) {
    tagService.decrementTagUsage(tagId);
}
```

## 错误处理

所有接口都返回统一的 `Result<T>` 结果对象，包含以下信息：

- `success`: 操作是否成功
- `code`: 错误码
- `message`: 错误信息或成功信息
- `data`: 返回的数据

**常见错误码**:
- `200`: 操作成功
- `400`: 请求参数错误
- `404`: 标签不存在
- `409`: 标签名称冲突
- `422`: 业务逻辑错误（如删除正在使用的标签）
- `500`: 服务内部错误

**错误处理示例**:
```java
Result<TagDTO> result = tagService.createTag(tag);

if (result.isSuccess()) {
    // 处理成功情况
    TagDTO createdTag = result.getData();
    // 业务逻辑处理
} else {
    // 处理错误情况
    System.err.println("操作失败: " + result.getMessage());
    System.err.println("错误码: " + result.getCode());
    
    // 根据错误码进行不同处理
    switch (result.getCode()) {
        case 400:
            System.out.println("请求参数错误，请检查标签信息");
            break;
        case 404:
            System.out.println("标签不存在");
            break;
        case 409:
            System.out.println("标签名称已存在");
            break;
        case 422:
            System.out.println("业务逻辑错误，如删除正在使用的标签");
            break;
        default:
            System.out.println("其他系统错误");
    }
}
```

## 最佳实践

### 1. 标签命名规范
- 标签名称应简洁明了，避免过长
- 使用统一的命名风格，如技术标签使用英文，业务标签使用中文
- 避免创建重复或相似的标签

### 2. 标签分组管理
- 合理使用`tagCategory`字段对标签进行分组
- 常见分组：技术、业务、工具、框架、语言等
- 保持分组的一致性和层次性

### 3. 热门标签优化
- 定期更新热门标签缓存
- 根据使用频率动态调整标签推荐
- 考虑时间衰减因子，避免过时标签长期占据热门位置

### 4. 性能优化建议
- 使用标签缓存提高查询性能
- 批量操作时使用批量接口
- 合理设置分页大小，避免一次性加载过多数据

### 5. 调用示例综合场景

```java
// 综合示例：完整的标签管理功能实现
public class TagServiceExample {
    
    private TagService tagService;
    
    public void comprehensiveTagManagementExample() {
        try {
            // 1. 批量创建技术标签
            List<String> techTags = Arrays.asList(
                "Java", "Spring Boot", "MyBatis", "Redis", "Docker"
            );
            
            Result<List<TagDTO>> batchResult = tagService.batchCreateTags(
                techTags, "knowledge");
            
            if (batchResult.isSuccess()) {
                System.out.println("批量创建技术标签成功");
            }
            
            // 2. 创建知识类型专属标签
            TagDTO specificTag = new TagDTO();
            specificTag.setName("微服务架构");
            specificTag.setDescription("微服务架构设计相关标签");
            specificTag.setContentCategory("knowledge");
            specificTag.setSubTypeId(1001L);  // 架构设计知识类型
            specificTag.setTagCategory("架构");
            specificTag.setColor("#17a2b8");
            specificTag.setIsActive(true);
            
            Result<TagDTO> createResult = tagService.createTag(specificTag);
            
            // 3. 获取热门标签
            Result<List<TagDTO>> hotTagsResult = tagService.getPopularTags(
                "knowledge", 10);
            
            if (hotTagsResult.isSuccess()) {
                System.out.println("当前热门技术标签:");
                for (TagDTO tag : hotTagsResult.getData()) {
                    System.out.println("- " + tag.getName() + 
                        " (使用: " + tag.getUsageCount() + "次)");
                }
            }
            
            // 4. 查询知识类型可用标签
            Result<List<TagDTO>> availableResult = 
                tagService.getAvailableTagsForKnowledgeType(1001L);
            
            if (availableResult.isSuccess()) {
                System.out.println("知识类型可用标签数量: " + 
                    availableResult.getData().size());
            }
            
            // 5. 分页查询标签
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPageNum(1);
            pageRequest.setPageSize(20);
            
            Result<PageResult<TagDTO>> pageResult = tagService.getTagList(
                pageRequest, "knowledge", "技术", true, null);
            
            if (pageResult.isSuccess()) {
                System.out.println("技术标签总数: " + 
                    pageResult.getData().getTotal());
            }
            
        } catch (Exception e) {
            System.err.println("标签管理操作失败: " + e.getMessage());
        }
    }
    
    // 标签使用统计管理示例
    public void tagUsageManagementExample() {
        try {
            // 1. 模拟内容关联标签，增加使用次数
            List<Long> contentTagIds = Arrays.asList(1001L, 1002L, 1003L);
            
            for (Long tagId : contentTagIds) {
                Result<Void> result = tagService.incrementTagUsage(tagId);
                if (result.isSuccess()) {
                    System.out.println("标签 " + tagId + " 使用次数已增加");
                }
            }
            
            // 2. 查看标签使用情况
            for (Long tagId : contentTagIds) {
                Result<TagDTO> tagResult = tagService.getTagById(tagId);
                if (tagResult.isSuccess()) {
                    TagDTO tag = tagResult.getData();
                    System.out.println("标签: " + tag.getName() + 
                        ", 使用次数: " + tag.getUsageCount());
                }
            }
            
            // 3. 获取更新后的热门标签
            Result<List<TagDTO>> updatedHotTags = tagService.getPopularTags(
                "knowledge", 5);
            
            if (updatedHotTags.isSuccess()) {
                System.out.println("更新后的热门标签:");
                for (int i = 0; i < updatedHotTags.getData().size(); i++) {
                    TagDTO tag = updatedHotTags.getData().get(i);
                    System.out.println((i + 1) + ". " + tag.getName() + 
                        " (使用: " + tag.getUsageCount() + "次)");
                }
            }
            
        } catch (Exception e) {
            System.err.println("标签使用统计管理失败: " + e.getMessage());
        }
    }
    
    // 标签搜索和过滤示例
    public void tagSearchAndFilterExample() {
        try {
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPageNum(1);
            pageRequest.setPageSize(10);
            
            // 1. 搜索Java相关标签
            Result<PageResult<TagDTO>> javaTagsResult = tagService.getTagList(
                pageRequest, null, null, true, "Java");
            
            if (javaTagsResult.isSuccess()) {
                System.out.println("Java相关标签:");
                for (TagDTO tag : javaTagsResult.getData().getList()) {
                    System.out.println("- " + tag.getName() + 
                        " (分组: " + tag.getTagCategory() + ")");
                }
            }
            
            // 2. 按分组查询标签
            Result<PageResult<TagDTO>> frameworkTagsResult = tagService.getTagList(
                pageRequest, "knowledge", "框架", true, null);
            
            if (frameworkTagsResult.isSuccess()) {
                System.out.println("框架类标签:");
                for (TagDTO tag : frameworkTagsResult.getData().getList()) {
                    System.out.println("- " + tag.getName());
                }
            }
            
            // 3. 查询特定知识类型的专属标签
            Result<PageResult<TagDTO>> specificTagsResult = tagService.getTagList(
                pageRequest, "knowledge", 1001L, null, true, null);
            
            if (specificTagsResult.isSuccess()) {
                System.out.println("知识类型专属标签:");
                for (TagDTO tag : specificTagsResult.getData().getList()) {
                    System.out.println("- " + tag.getName() + " (专属)");
                }
            }
            
        } catch (Exception e) {
            System.err.println("标签搜索和过滤失败: " + e.getMessage());
        }
    }
}
```

## 业务场景说明

### 1. 知识类型专属标签
系统支持为不同的知识类型创建专属标签，通过`subTypeId`字段关联：
- 当`subTypeId`不为null时，该标签仅对指定知识类型可用
- 当`subTypeId`为null且`contentCategory`为"knowledge"时，为通用知识标签
- 当`contentCategory`为"general"时，为全局通用标签

### 2. 标签使用统计
- 系统自动维护标签的使用次数统计
- 支持增加和减少使用次数的操作
- 基于使用次数生成热门标签排行

### 3. 标签分组管理
- 通过`tagCategory`字段对标签进行分组管理
- 支持按分组查询和过滤标签
- 便于标签的组织和展示

### 4. 批量操作优化
- 提供批量创建标签接口，提高操作效率
- 支持批量状态管理和使用统计更新
- 减少网络请求次数，提升性能

---

**文档版本**: 2.0.0  
**最后更新**: 2025-07-17  
**维护团队**: AI Community Development Team
