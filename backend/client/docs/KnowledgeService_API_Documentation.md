# KnowledgeService API 文档

## 接口概述

**接口名称**: KnowledgeService  
**包路径**: com.jdl.aic.core.service.client.service.KnowledgeService  
**功能描述**: 知识管理服务接口，整合知识类型和知识内容管理功能，提供完整的知识管理能力，包括知识类型配置、知识内容全生命周期管理、知识搜索查询、版本控制和批量操作支持。  
**版本**: 2.0.0  
**作者**: AI Community Development Team  

## 数据模型

### KnowledgeTypeDTO
知识类型数据传输对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 知识类型ID |
| code | String | 知识类型编码 |
| name | String | 知识类型名称 |
| description | String | 描述 |
| icon | String | 图标 |
| color | String | 颜色 |
| sortOrder | Integer | 排序 |
| isActive | Boolean | 是否启用 |
| createTime | LocalDateTime | 创建时间 |
| updateTime | LocalDateTime | 更新时间 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |

### KnowledgeDTO
知识内容数据传输对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 知识ID |
| title | String | 标题 |
| content | String | 内容 |
| summary | String | 摘要 |
| knowledgeTypeCode | String | 知识类型编码 |
| status | Integer | 状态（0:草稿, 1:待审核, 2:已发布, 3:已下线, 4:已拒绝） |
| visibility | Integer | 可见性（0:私有, 1:团队, 2:公开） |
| authorId | Long | 作者ID |
| authorName | String | 作者姓名 |
| teamId | Long | 团队ID |
| teamName | String | 团队名称 |
| tags | String | 标签（逗号分隔） |
| readCount | Integer | 阅读次数 |
| likeCount | Integer | 点赞次数 |
| collectCount | Integer | 收藏次数 |
| version | String | 版本号 |
| publishTime | LocalDateTime | 发布时间 |
| createTime | LocalDateTime | 创建时间 |
| updateTime | LocalDateTime | 更新时间 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |

## API 接口列表

## 一、知识类型管理

### 1.1 获取知识类型列表（分页）

**方法名**: `getKnowledgeTypeList`  
**描述**: 获取知识类型列表，支持分页和过滤条件  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求参数
- `isActive` (Boolean, 可选): 启用状态过滤
- `search` (String, 可选): 搜索关键词

**返回值**: `Result<PageResult<KnowledgeTypeDTO>>`

**调用示例**:
```java
// 基本分页查询
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

Result<PageResult<KnowledgeTypeDTO>> result = knowledgeService.getKnowledgeTypeList(
    pageRequest, true, null);

if (result.isSuccess()) {
    PageResult<KnowledgeTypeDTO> pageResult = result.getData();
    System.out.println("总数: " + pageResult.getTotal());
    
    for (KnowledgeTypeDTO type : pageResult.getList()) {
        System.out.println("类型: " + type.getName());
        System.out.println("编码: " + type.getCode());
        System.out.println("状态: " + (type.getIsActive() ? "启用" : "禁用"));
    }
}

// 搜索知识类型
Result<PageResult<KnowledgeTypeDTO>> searchResult = knowledgeService.getKnowledgeTypeList(
    pageRequest, null, "技术");
```

### 1.2 根据ID获取知识类型详情

**方法名**: `getKnowledgeTypeById`  
**描述**: 根据知识类型ID获取详细信息  

**请求参数**:
- `id` (Long): 知识类型ID

**返回值**: `Result<KnowledgeTypeDTO>`

**调用示例**:
```java
Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeById(1001L);

if (result.isSuccess()) {
    KnowledgeTypeDTO type = result.getData();
    System.out.println("类型名称: " + type.getName());
    System.out.println("类型编码: " + type.getCode());
    System.out.println("描述: " + type.getDescription());
    System.out.println("图标: " + type.getIcon());
    System.out.println("颜色: " + type.getColor());
}
```

### 1.3 根据编码获取知识类型详情

**方法名**: `getKnowledgeTypeByCode`  
**描述**: 根据知识类型编码获取详细信息  

**请求参数**:
- `code` (String): 知识类型编码

**返回值**: `Result<KnowledgeTypeDTO>`

**调用示例**:
```java
Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeByCode("TECH_DOC");

if (result.isSuccess()) {
    KnowledgeTypeDTO type = result.getData();
    System.out.println("类型名称: " + type.getName());
    System.out.println("是否启用: " + type.getIsActive());
}
```

### 1.4 创建知识类型

**方法名**: `createKnowledgeType`  
**描述**: 创建新的知识类型  

**请求参数**:
- `knowledgeType` (KnowledgeTypeDTO): 知识类型信息

**返回值**: `Result<KnowledgeTypeDTO>`

**调用示例**:
```java
KnowledgeTypeDTO newType = new KnowledgeTypeDTO();
newType.setCode("BEST_PRACTICE");
newType.setName("最佳实践");
newType.setDescription("技术最佳实践和经验分享");
newType.setIcon("icon-practice");
newType.setColor("#4CAF50");
newType.setSortOrder(10);
newType.setIsActive(true);

Result<KnowledgeTypeDTO> result = knowledgeService.createKnowledgeType(newType);

if (result.isSuccess()) {
    KnowledgeTypeDTO createdType = result.getData();
    System.out.println("知识类型创建成功，ID: " + createdType.getId());
}
```

### 1.5 更新知识类型

**方法名**: `updateKnowledgeType`  
**描述**: 更新知识类型信息  

**请求参数**:
- `id` (Long): 知识类型ID
- `knowledgeType` (KnowledgeTypeDTO): 更新的知识类型信息

**返回值**: `Result<KnowledgeTypeDTO>`

**调用示例**:
```java
KnowledgeTypeDTO updateType = new KnowledgeTypeDTO();
updateType.setName("最佳实践（更新版）");
updateType.setDescription("技术最佳实践和经验分享，包含案例分析");
updateType.setColor("#2196F3");

Result<KnowledgeTypeDTO> result = knowledgeService.updateKnowledgeType(1001L, updateType);

if (result.isSuccess()) {
    KnowledgeTypeDTO updatedType = result.getData();
    System.out.println("知识类型更新成功: " + updatedType.getName());
}
```

### 1.6 删除知识类型

**方法名**: `deleteKnowledgeType`  
**描述**: 删除指定的知识类型  

**请求参数**:
- `id` (Long): 知识类型ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = knowledgeService.deleteKnowledgeType(1001L);

if (result.isSuccess()) {
    System.out.println("知识类型删除成功");
} else {
    System.err.println("删除失败: " + result.getMessage());
}
```

### 1.7 启用/禁用知识类型

**方法名**: `toggleKnowledgeTypeStatus`  
**描述**: 启用或禁用知识类型  

**请求参数**:
- `id` (Long): 知识类型ID
- `isActive` (Boolean): 是否启用

**返回值**: `Result<Void>`

**调用示例**:
```java
// 启用知识类型
Result<Void> result = knowledgeService.toggleKnowledgeTypeStatus(1001L, true);

if (result.isSuccess()) {
    System.out.println("知识类型已启用");
}

// 禁用知识类型
Result<Void> disableResult = knowledgeService.toggleKnowledgeTypeStatus(1002L, false);
```

## 二、知识内容管理

### 2.1 获取知识内容列表（分页）

**方法名**: `getKnowledgeList`  
**描述**: 获取知识内容列表，支持多种过滤条件  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求参数
- `knowledgeTypeCode` (String, 可选): 知识类型编码过滤
- `status` (Integer, 可选): 状态过滤
- `authorId` (Long, 可选): 作者ID过滤
- `teamId` (Long, 可选): 团队ID过滤
- `search` (String, 可选): 搜索关键词

**返回值**: `Result<PageResult<KnowledgeDTO>>`

**调用示例**:
```java
// 基本分页查询
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

Result<PageResult<KnowledgeDTO>> result = knowledgeService.getKnowledgeList(
    pageRequest, null, 2, null, null, null);

if (result.isSuccess()) {
    PageResult<KnowledgeDTO> pageResult = result.getData();
    System.out.println("总数: " + pageResult.getTotal());
    
    for (KnowledgeDTO knowledge : pageResult.getList()) {
        System.out.println("标题: " + knowledge.getTitle());
        System.out.println("作者: " + knowledge.getAuthorName());
        System.out.println("阅读次数: " + knowledge.getReadCount());
    }
}

// 按知识类型查询
Result<PageResult<KnowledgeDTO>> typeResult = knowledgeService.getKnowledgeList(
    pageRequest, "TECH_DOC", 2, null, null, null);

// 按作者查询
Result<PageResult<KnowledgeDTO>> authorResult = knowledgeService.getKnowledgeList(
    pageRequest, null, null, 1001L, null, null);

// 搜索知识
Result<PageResult<KnowledgeDTO>> searchResult = knowledgeService.getKnowledgeList(
    pageRequest, null, null, null, null, "Spring Boot");
```

### 2.2 根据ID获取知识内容详情

**方法名**: `getKnowledgeById`  
**描述**: 根据知识ID获取详细信息  

**请求参数**:
- `id` (Long): 知识ID

**返回值**: `Result<KnowledgeDTO>`

**调用示例**:
```java
Result<KnowledgeDTO> result = knowledgeService.getKnowledgeById(1001L);

if (result.isSuccess()) {
    KnowledgeDTO knowledge = result.getData();
    System.out.println("标题: " + knowledge.getTitle());
    System.out.println("内容: " + knowledge.getContent());
    System.out.println("摘要: " + knowledge.getSummary());
    System.out.println("知识类型: " + knowledge.getKnowledgeTypeCode());
    System.out.println("状态: " + getStatusName(knowledge.getStatus()));
    System.out.println("可见性: " + getVisibilityName(knowledge.getVisibility()));
    System.out.println("标签: " + knowledge.getTags());
}

private String getStatusName(Integer status) {
    switch (status) {
        case 0: return "草稿";
        case 1: return "待审核";
        case 2: return "已发布";
        case 3: return "已下线";
        case 4: return "已拒绝";
        default: return "未知状态";
    }
}

private String getVisibilityName(Integer visibility) {
    switch (visibility) {
        case 0: return "私有";
        case 1: return "团队";
        case 2: return "公开";
        default: return "未知";
    }
}
```

### 2.3 创建知识内容

**方法名**: `createKnowledge`  
**描述**: 创建新的知识内容  

**请求参数**:
- `knowledge` (KnowledgeDTO): 知识内容信息

**返回值**: `Result<KnowledgeDTO>`

**调用示例**:
```java
KnowledgeDTO newKnowledge = new KnowledgeDTO();
newKnowledge.setTitle("Spring Boot 微服务最佳实践");
newKnowledge.setContent("本文详细介绍了Spring Boot微服务开发的最佳实践...");
newKnowledge.setSummary("Spring Boot微服务开发指南，包含架构设计、配置管理、监控等内容");
newKnowledge.setKnowledgeTypeCode("BEST_PRACTICE");
newKnowledge.setStatus(0); // 草稿状态
newKnowledge.setVisibility(1); // 团队可见
newKnowledge.setAuthorId(1001L);
newKnowledge.setTeamId(2001L);
newKnowledge.setTags("Spring Boot,微服务,最佳实践");

Result<KnowledgeDTO> result = knowledgeService.createKnowledge(newKnowledge);

if (result.isSuccess()) {
    KnowledgeDTO createdKnowledge = result.getData();
    System.out.println("知识内容创建成功，ID: " + createdKnowledge.getId());
}
```

### 2.4 更新知识内容

**方法名**: `updateKnowledge`  
**描述**: 更新知识内容信息  

**请求参数**:
- `id` (Long): 知识ID
- `knowledge` (KnowledgeDTO): 更新的知识内容信息

**返回值**: `Result<KnowledgeDTO>`

**调用示例**:
```java
KnowledgeDTO updateKnowledge = new KnowledgeDTO();
updateKnowledge.setTitle("Spring Boot 微服务最佳实践（更新版）");
updateKnowledge.setContent("本文详细介绍了Spring Boot微服务开发的最佳实践，新增了监控和部署章节...");
updateKnowledge.setSummary("更新版本，新增监控和部署内容");
updateKnowledge.setTags("Spring Boot,微服务,最佳实践,监控,部署");

Result<KnowledgeDTO> result = knowledgeService.updateKnowledge(1001L, updateKnowledge);

if (result.isSuccess()) {
    KnowledgeDTO updatedKnowledge = result.getData();
    System.out.println("知识内容更新成功: " + updatedKnowledge.getTitle());
}
```

### 2.5 删除知识内容

**方法名**: `deleteKnowledge`  
**描述**: 删除指定的知识内容  

**请求参数**:
- `id` (Long): 知识ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = knowledgeService.deleteKnowledge(1001L);

if (result.isSuccess()) {
    System.out.println("知识内容删除成功");
} else {
    System.err.println("删除失败: " + result.getMessage());
}
```

### 2.6 更新知识内容状态

**方法名**: `updateKnowledgeStatus`  
**描述**: 更新知识内容的状态  

**请求参数**:
- `id` (Long): 知识ID
- `status` (Integer): 新状态（0:草稿, 1:待审核, 2:已发布, 3:已下线, 4:已拒绝）

**返回值**: `Result<Void>`

**调用示例**:
```java
// 提交审核
Result<Void> result = knowledgeService.updateKnowledgeStatus(1001L, 1);

if (result.isSuccess()) {
    System.out.println("知识已提交审核");
}

// 发布知识
Result<Void> publishResult = knowledgeService.updateKnowledgeStatus(1001L, 2);

// 下线知识
Result<Void> offlineResult = knowledgeService.updateKnowledgeStatus(1001L, 3);
```

### 2.7 搜索知识内容

**方法名**: `searchKnowledge`  
**描述**: 根据关键词搜索知识内容  

**请求参数**:
- `keyword` (String): 搜索关键词
- `pageRequest` (PageRequest): 分页请求参数
- `knowledgeTypeCode` (String, 可选): 知识类型编码过滤
- `status` (Integer, 可选): 状态过滤
- `visibility` (Integer, 可选): 可见性过滤

**返回值**: `Result<PageResult<KnowledgeDTO>>`

**调用示例**:
```java
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

// 搜索Spring Boot相关知识
Result<PageResult<KnowledgeDTO>> result = knowledgeService.searchKnowledge(
    "Spring Boot", pageRequest, null, 2, null);

if (result.isSuccess()) {
    PageResult<KnowledgeDTO> pageResult = result.getData();
    System.out.println("搜索到 " + pageResult.getTotal() + " 条相关知识");
    
    for (KnowledgeDTO knowledge : pageResult.getList()) {
        System.out.println("- " + knowledge.getTitle());
        System.out.println("  作者: " + knowledge.getAuthorName());
        System.out.println("  阅读: " + knowledge.getReadCount());
    }
}

// 在特定类型中搜索
Result<PageResult<KnowledgeDTO>> typeSearchResult = knowledgeService.searchKnowledge(
    "微服务", pageRequest, "BEST_PRACTICE", 2, 2);
```

### 2.8 增加知识内容阅读次数

**方法名**: `incrementReadCount`  
**描述**: 增加知识内容的阅读次数  

**请求参数**:
- `id` (Long): 知识ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = knowledgeService.incrementReadCount(1001L);

if (result.isSuccess()) {
    System.out.println("阅读次数已更新");
}
```

## 三、批量操作

### 3.1 批量更新知识内容状态

**方法名**: `batchUpdateKnowledgeStatus`  
**描述**: 批量更新多个知识内容的状态  

**请求参数**:
- `ids` (List<Long>): 知识ID列表
- `status` (Integer): 新状态

**返回值**: `Result<Void>`

**调用示例**:
```java
List<Long> knowledgeIds = Arrays.asList(1001L, 1002L, 1003L, 1004L);

// 批量发布知识
Result<Void> result = knowledgeService.batchUpdateKnowledgeStatus(knowledgeIds, 2);

if (result.isSuccess()) {
    System.out.println("批量发布成功，共发布: " + knowledgeIds.size() + "条知识");
} else {
    System.err.println("批量发布失败: " + result.getMessage());
}

// 批量下线知识
List<Long> offlineIds = Arrays.asList(1005L, 1006L, 1007L);
Result<Void> offlineResult = knowledgeService.batchUpdateKnowledgeStatus(offlineIds, 3);
```

### 3.2 批量删除知识内容

**方法名**: `batchDeleteKnowledge`  
**描述**: 批量删除多个知识内容  

**请求参数**:
- `ids` (List<Long>): 知识ID列表

**返回值**: `Result<Void>`

**调用示例**:
```java
List<Long> knowledgeIds = Arrays.asList(1001L, 1002L, 1003L);

Result<Void> result = knowledgeService.batchDeleteKnowledge(knowledgeIds);

if (result.isSuccess()) {
    System.out.println("批量删除成功，共删除: " + knowledgeIds.size() + "条知识");
} else {
    System.err.println("批量删除失败: " + result.getMessage());
}
```

## 四、版本管理

### 4.1 获取知识版本历史

**方法名**: `getKnowledgeVersionHistory`  
**描述**: 获取知识的版本历史记录  

**请求参数**:
- `knowledgeId` (Long): 知识ID
- `pageRequest` (PageRequest): 分页请求参数

**返回值**: `Result<PageResult<Object>>`

**调用示例**:
```java
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(10);

Result<PageResult<Object>> result = knowledgeService.getKnowledgeVersionHistory(1001L, pageRequest);

if (result.isSuccess()) {
    PageResult<Object> pageResult = result.getData();
    System.out.println("版本历史总数: " + pageResult.getTotal());
    
    for (Object version : pageResult.getList()) {
        // 处理版本信息
        System.out.println("版本信息: " + version.toString());
    }
}
```

### 4.2 创建知识版本

**方法名**: `createKnowledgeVersion`  
**描述**: 为知识创建新版本  

**请求参数**:
- `knowledgeId` (Long): 知识ID
- `versionComment` (String): 版本说明

**返回值**: `Result<Object>`

**调用示例**:
```java
String versionComment = "修复了代码示例中的错误，新增了性能优化章节";

Result<Object> result = knowledgeService.createKnowledgeVersion(1001L, versionComment);

if (result.isSuccess()) {
    Object version = result.getData();
    System.out.println("版本创建成功: " + version.toString());
}
```

## 错误处理

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 403 | 权限不足 | 确认用户是否有相应操作权限 |
| 404 | 知识不存在 | 确认知识ID是否正确 |
| 409 | 知识类型编码重复 | 使用不同的编码 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误处理示例

```java
Result<KnowledgeDTO> result = knowledgeService.getKnowledgeById(1001L);

if (!result.isSuccess()) {
    switch (result.getCode()) {
        case 403:
            System.err.println("权限不足: " + result.getMessage());
            break;
        case 404:
            System.err.println("知识不存在: " + result.getMessage());
            break;
        case 500:
            System.err.println("服务器错误: " + result.getMessage());
            break;
        default:
            System.err.println("未知错误: " + result.getMessage());
    }
}
```

## 最佳实践

### 1. 知识状态管理
建议按照以下流程管理知识状态：
1. 创建时设置为草稿(0)
2. 完成编写后提交审核(1)
3. 审核通过后发布(2)
4. 如需下线则设置为已下线(3)
5. 审核不通过则设置为已拒绝(4)

```java
// 创建草稿
KnowledgeDTO draft = new KnowledgeDTO();
draft.setStatus(0);
knowledgeService.createKnowledge(draft);

// 提交审核
knowledgeService.updateKnowledgeStatus(knowledgeId, 1);

// 发布
knowledgeService.updateKnowledgeStatus(knowledgeId, 2);
```

### 2. 可见性控制
根据知识的敏感程度设置合适的可见性：
- 私有(0)：个人知识，仅作者可见
- 团队(1)：团队知识，团队成员可见
- 公开(2)：公开知识，所有人可见

### 3. 版本管理
重要知识建议启用版本管理：

```java
// 在重要更新前创建版本
knowledgeService.createKnowledgeVersion(knowledgeId, "重大更新前的备份");

// 然后进行更新
knowledgeService.updateKnowledge(knowledgeId, updatedKnowledge);
```

### 4. 搜索优化
使用合适的搜索策略提高查找效率：

```java
// 先按类型筛选，再搜索关键词
Result<PageResult<KnowledgeDTO>> result = knowledgeService.searchKnowledge(
    "关键词", pageRequest, "TECH_DOC", 2, 2);

// 利用标签进行精确搜索
Result<PageResult<KnowledgeDTO>> tagResult = knowledgeService.getKnowledgeList(
    pageRequest, null, null, null, null, "Spring Boot");
```

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 2.0.0 | 2025-07-17 | 整合知识类型和知识内容管理，新增版本控制功能 |
| 1.0.0 | 2025-06-01 | 初始版本，基础知识管理功能 |

## 联系方式

如有问题或建议，请联系：
- 技术支持：AI Community Development Team
- 邮箱：<EMAIL>
- 文档更新：定期更新，请关注最新版本
