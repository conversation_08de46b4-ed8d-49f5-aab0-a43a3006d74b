# CategoryService API 文档

## 接口概述

**接口名称**: CategoryService  
**包路径**: com.jdl.aic.core.service.client.service.CategoryService  
**功能描述**: 分类管理服务接口，提供分类管理功能，包括分类的CRUD操作和层级管理、分类树形结构管理、分类排序和移动操作、知识类型专属分类查询等。  
**版本**: 2.0.0  
**作者**: AI Community Development Team  

## 数据模型

### CategoryDTO
分类数据传输对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 分类ID |
| name | String | 分类名称 |
| description | String | 分类描述 |
| parentId | Long | 父分类ID |
| contentCategory | String | 内容类别（knowledge, solution, learning_resource, general） |
| subTypeId | Long | 细分类型ID（用于知识类型专属分类） |
| sortOrder | Integer | 排序权重 |
| isActive | Boolean | 是否启用 |
| level | Integer | 分类层级 |
| path | String | 分类路径 |
| children | List<CategoryDTO> | 子分类列表 |
| createTime | Date | 创建时间 |
| updateTime | Date | 更新时间 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |

## API 接口列表

### 1. 分类查询

#### 1.1 获取分类列表（分页）

**方法名**: `getCategoryList`  
**描述**: 获取分类列表，支持分页和多种过滤条件  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求参数
- `contentCategory` (String, 可选): 内容类别过滤
- `subTypeId` (Long, 可选): 细分类型ID过滤（用于知识类型专属分类）
- `parentId` (Long, 可选): 父分类ID过滤
- `isActive` (Boolean, 可选): 启用状态过滤
- `search` (String, 可选): 搜索关键词

**返回值**: `Result<PageResult<CategoryDTO>>`

**调用示例**:
```java
// 基本分页查询
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

Result<PageResult<CategoryDTO>> result = categoryService.getCategoryList(
    pageRequest, 
    null, 
    null, 
    null, 
    true,  // 只查询启用的分类
    null
);

if (result.isSuccess()) {
    PageResult<CategoryDTO> pageResult = result.getData();
    System.out.println("总数: " + pageResult.getTotal());
    
    for (CategoryDTO category : pageResult.getList()) {
        System.out.println("分类: " + category.getName());
        System.out.println("描述: " + category.getDescription());
        System.out.println("层级: " + category.getLevel());
    }
}

// 查询知识类型专属分类
Result<PageResult<CategoryDTO>> knowledgeResult = categoryService.getCategoryList(
    pageRequest,
    "knowledge",  // 知识类别
    1001L,        // 特定知识类型ID
    null,
    true,
    null
);

// 搜索分类
Result<PageResult<CategoryDTO>> searchResult = categoryService.getCategoryList(
    pageRequest,
    null,
    null,
    null,
    null,
    "技术"  // 搜索关键词
);
```

#### 1.2 获取分类树形结构

**方法名**: `getCategoryTree`  
**描述**: 获取分类的树形结构，支持过滤条件  

**请求参数**:
- `contentCategory` (String, 可选): 内容类别过滤
- `subTypeId` (Long, 可选): 细分类型ID过滤
- `isActive` (Boolean, 可选): 启用状态过滤

**返回值**: `Result<List<CategoryDTO>>`

**调用示例**:
```java
// 获取所有启用分类的树形结构
Result<List<CategoryDTO>> result = categoryService.getCategoryTree(null, null, true);

if (result.isSuccess()) {
    List<CategoryDTO> categoryTree = result.getData();
    
    // 递归打印分类树
    printCategoryTree(categoryTree, 0);
}

// 获取知识类别的分类树
Result<List<CategoryDTO>> knowledgeTree = categoryService.getCategoryTree(
    "knowledge", 
    null, 
    true
);

// 辅助方法：递归打印分类树
private void printCategoryTree(List<CategoryDTO> categories, int level) {
    for (CategoryDTO category : categories) {
        String indent = "  ".repeat(level);
        System.out.println(indent + "- " + category.getName());
        
        if (category.getChildren() != null && !category.getChildren().isEmpty()) {
            printCategoryTree(category.getChildren(), level + 1);
        }
    }
}
```

#### 1.3 根据ID获取分类详情

**方法名**: `getCategoryById`  
**描述**: 根据分类ID获取详细信息  

**请求参数**:
- `id` (Long): 分类ID

**返回值**: `Result<CategoryDTO>`

**调用示例**:
```java
Result<CategoryDTO> result = categoryService.getCategoryById(1001L);

if (result.isSuccess()) {
    CategoryDTO category = result.getData();
    System.out.println("分类名称: " + category.getName());
    System.out.println("分类描述: " + category.getDescription());
    System.out.println("父分类ID: " + category.getParentId());
    System.out.println("内容类别: " + category.getContentCategory());
    System.out.println("排序权重: " + category.getSortOrder());
    System.out.println("是否启用: " + category.getIsActive());
}
```

#### 1.4 获取知识类型可用的分类列表

**方法名**: `getAvailableCategoriesForKnowledgeType`  
**描述**: 获取指定知识类型可以使用的分类，包括专属分类、通用知识分类和全局通用分类  

**请求参数**:
- `knowledgeTypeId` (Long): 知识类型ID

**返回值**: `Result<List<CategoryDTO>>`

**调用示例**:
```java
Result<List<CategoryDTO>> result = categoryService.getAvailableCategoriesForKnowledgeType(1001L);

if (result.isSuccess()) {
    List<CategoryDTO> availableCategories = result.getData();
    System.out.println("知识类型可用分类:");
    
    for (CategoryDTO category : availableCategories) {
        System.out.println("- " + category.getName());
        if (category.getSubTypeId() != null) {
            System.out.println("  (专属分类)");
        } else if ("knowledge".equals(category.getContentCategory())) {
            System.out.println("  (通用知识分类)");
        } else if ("general".equals(category.getContentCategory())) {
            System.out.println("  (全局通用分类)");
        }
    }
}
```

#### 1.5 根据细分类型获取分类列表

**方法名**: `getCategoriesBySubType`  
**描述**: 根据内容类别和细分类型ID获取分类列表  

**请求参数**:
- `contentCategory` (String): 内容类别
- `subTypeId` (Long): 细分类型ID

**返回值**: `Result<List<CategoryDTO>>`

**调用示例**:
```java
Result<List<CategoryDTO>> result = categoryService.getCategoriesBySubType(
    "knowledge", 
    1001L
);

if (result.isSuccess()) {
    List<CategoryDTO> categories = result.getData();
    System.out.println("细分类型专属分类:");
    
    for (CategoryDTO category : categories) {
        System.out.println("- " + category.getName());
    }
}
```

### 2. 分类管理

#### 2.1 创建分类

**方法名**: `createCategory`  
**描述**: 创建新的分类  

**请求参数**:
- `category` (CategoryDTO): 分类信息

**返回值**: `Result<CategoryDTO>`

**调用示例**:
```java
CategoryDTO newCategory = new CategoryDTO();
newCategory.setName("Java技术");
newCategory.setDescription("Java相关技术知识分类");
newCategory.setParentId(1000L);  // 父分类ID
newCategory.setContentCategory("knowledge");
newCategory.setSubTypeId(1001L);  // 知识类型ID
newCategory.setSortOrder(100);
newCategory.setIsActive(true);

Result<CategoryDTO> result = categoryService.createCategory(newCategory);

if (result.isSuccess()) {
    CategoryDTO createdCategory = result.getData();
    System.out.println("分类创建成功，ID: " + createdCategory.getId());
    System.out.println("分类路径: " + createdCategory.getPath());
}
```

#### 2.2 更新分类信息

**方法名**: `updateCategory`  
**描述**: 更新分类信息  

**请求参数**:
- `id` (Long): 分类ID
- `category` (CategoryDTO): 更新的分类信息

**返回值**: `Result<CategoryDTO>`

**调用示例**:
```java
CategoryDTO updateCategory = new CategoryDTO();
updateCategory.setName("Java高级技术");
updateCategory.setDescription("Java高级技术知识分类，包括JVM、并发等");
updateCategory.setSortOrder(200);

Result<CategoryDTO> result = categoryService.updateCategory(1001L, updateCategory);

if (result.isSuccess()) {
    CategoryDTO updatedCategory = result.getData();
    System.out.println("分类更新成功: " + updatedCategory.getName());
}
```

#### 2.3 删除分类

**方法名**: `deleteCategory`  
**描述**: 删除指定分类  

**请求参数**:
- `id` (Long): 分类ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = categoryService.deleteCategory(1001L);

if (result.isSuccess()) {
    System.out.println("分类删除成功");
} else {
    System.err.println("删除失败: " + result.getMessage());
    // 可能的原因：分类下还有子分类或关联内容
}
```

#### 2.4 启用/禁用分类

**方法名**: `toggleCategoryStatus`  
**描述**: 切换分类的启用状态  

**请求参数**:
- `id` (Long): 分类ID
- `isActive` (Boolean): 是否启用

**返回值**: `Result<Void>`

**调用示例**:
```java
// 禁用分类
Result<Void> result = categoryService.toggleCategoryStatus(1001L, false);

if (result.isSuccess()) {
    System.out.println("分类已禁用");
}

// 启用分类
Result<Void> enableResult = categoryService.toggleCategoryStatus(1001L, true);

if (enableResult.isSuccess()) {
    System.out.println("分类已启用");
}
```

### 3. 分类排序和移动

#### 3.1 更新分类排序权重

**方法名**: `updateCategorySortOrder`  
**描述**: 更新分类的排序权重  

**请求参数**:
- `id` (Long): 分类ID
- `sortOrder` (Integer): 排序权重

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = categoryService.updateCategorySortOrder(1001L, 500);

if (result.isSuccess()) {
    System.out.println("分类排序权重更新成功");
}

// 批量更新排序
List<Long> categoryIds = Arrays.asList(1001L, 1002L, 1003L);
List<Integer> sortOrders = Arrays.asList(100, 200, 300);

for (int i = 0; i < categoryIds.size(); i++) {
    categoryService.updateCategorySortOrder(categoryIds.get(i), sortOrders.get(i));
}
```

#### 3.2 移动分类到新的父分类下

**方法名**: `moveCategoryToParent`  
**描述**: 将分类移动到新的父分类下  

**请求参数**:
- `id` (Long): 分类ID
- `newParentId` (Long): 新父分类ID

**返回值**: `Result<Void>`

**调用示例**:
```java
// 将分类移动到新的父分类下
Result<Void> result = categoryService.moveCategoryToParent(1001L, 2000L);

if (result.isSuccess()) {
    System.out.println("分类移动成功");
}

// 移动到根级别（设置父分类为null）
Result<Void> moveToRootResult = categoryService.moveCategoryToParent(1001L, null);

if (moveToRootResult.isSuccess()) {
    System.out.println("分类已移动到根级别");
}
```

## 错误处理

所有接口都返回统一的 `Result<T>` 结果对象，包含以下信息：

- `success`: 操作是否成功
- `code`: 错误码
- `message`: 错误信息或成功信息
- `data`: 返回的数据

**常见错误码**:
- `200`: 操作成功
- `400`: 请求参数错误
- `404`: 分类不存在
- `409`: 分类名称冲突或存在循环引用
- `422`: 业务逻辑错误（如删除有子分类的分类）
- `500`: 服务内部错误

**错误处理示例**:
```java
Result<CategoryDTO> result = categoryService.createCategory(category);

if (result.isSuccess()) {
    // 处理成功情况
    CategoryDTO createdCategory = result.getData();
    // 业务逻辑处理
} else {
    // 处理错误情况
    System.err.println("操作失败: " + result.getMessage());
    System.err.println("错误码: " + result.getCode());
    
    // 根据错误码进行不同处理
    switch (result.getCode()) {
        case 400:
            System.out.println("请求参数错误，请检查分类信息");
            break;
        case 404:
            System.out.println("分类不存在");
            break;
        case 409:
            System.out.println("分类名称冲突或存在循环引用");
            break;
        case 422:
            System.out.println("业务逻辑错误，如删除有子分类的分类");
            break;
        default:
            System.out.println("其他系统错误");
    }
}
```

## 最佳实践

### 1. 分类层级管理
- 合理设计分类层级，避免过深的嵌套结构
- 使用排序权重控制同级分类的显示顺序
- 移动分类时注意避免循环引用

### 2. 分类命名规范
- 分类名称应简洁明了，便于用户理解
- 避免使用过长的分类名称
- 保持同级分类命名风格的一致性

### 3. 性能优化
- 使用分类树结构时考虑缓存策略
- 大量分类数据时使用分页查询
- 合理使用过滤条件减少数据传输

### 4. 调用示例综合场景

```java
// 综合示例：完整的分类管理功能实现
public class CategoryServiceExample {
    
    private CategoryService categoryService;
    
    public void comprehensiveCategoryManagementExample() {
        try {
            // 1. 创建根分类
            CategoryDTO rootCategory = new CategoryDTO();
            rootCategory.setName("技术分类");
            rootCategory.setDescription("技术相关的根分类");
            rootCategory.setContentCategory("knowledge");
            rootCategory.setSortOrder(100);
            rootCategory.setIsActive(true);
            
            Result<CategoryDTO> rootResult = categoryService.createCategory(rootCategory);
            Long rootCategoryId = rootResult.getData().getId();
            
            // 2. 创建子分类
            CategoryDTO subCategory = new CategoryDTO();
            subCategory.setName("Java技术");
            subCategory.setDescription("Java相关技术知识");
            subCategory.setParentId(rootCategoryId);
            subCategory.setContentCategory("knowledge");
            subCategory.setSubTypeId(1001L);  // 知识类型专属分类
            subCategory.setSortOrder(200);
            subCategory.setIsActive(true);
            
            Result<CategoryDTO> subResult = categoryService.createCategory(subCategory);
            Long subCategoryId = subResult.getData().getId();
            
            // 3. 获取分类树形结构
            Result<List<CategoryDTO>> treeResult = categoryService.getCategoryTree(
                "knowledge", null, true);
            
            if (treeResult.isSuccess()) {
                System.out.println("分类树结构:");
                printCategoryTree(treeResult.getData(), 0);
            }
            
            // 4. 查询知识类型可用分类
            Result<List<CategoryDTO>> availableResult = 
                categoryService.getAvailableCategoriesForKnowledgeType(1001L);
            
            if (availableResult.isSuccess()) {
                System.out.println("知识类型可用分类数量: " + 
                    availableResult.getData().size());
            }
            
            // 5. 更新分类排序
            categoryService.updateCategorySortOrder(subCategoryId, 150);
            
            // 6. 分页查询分类
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPageNum(1);
            pageRequest.setPageSize(10);
            
            Result<PageResult<CategoryDTO>> pageResult = categoryService.getCategoryList(
                pageRequest, "knowledge", null, null, true, null);
            
            if (pageResult.isSuccess()) {
                System.out.println("分页查询结果数量: " + 
                    pageResult.getData().getTotal());
            }
            
        } catch (Exception e) {
            System.err.println("分类管理操作失败: " + e.getMessage());
        }
    }
    
    // 分类移动和重组示例
    public void categoryReorganizationExample() {
        try {
            // 1. 获取当前分类结构
            Result<List<CategoryDTO>> currentTree = categoryService.getCategoryTree(
                "knowledge", null, true);
            
            if (currentTree.isSuccess()) {
                System.out.println("当前分类结构:");
                printCategoryTree(currentTree.getData(), 0);
            }
            
            // 2. 移动分类到新的父分类下
            Long categoryToMove = 1001L;
            Long newParentId = 2000L;
            
            Result<Void> moveResult = categoryService.moveCategoryToParent(
                categoryToMove, newParentId);
            
            if (moveResult.isSuccess()) {
                System.out.println("分类移动成功");
                
                // 3. 查看移动后的结构
                Result<List<CategoryDTO>> newTree = categoryService.getCategoryTree(
                    "knowledge", null, true);
                
                if (newTree.isSuccess()) {
                    System.out.println("移动后的分类结构:");
                    printCategoryTree(newTree.getData(), 0);
                }
            }
            
            // 4. 批量更新排序
            List<Long> categoryIds = Arrays.asList(1001L, 1002L, 1003L);
            List<Integer> newSortOrders = Arrays.asList(100, 200, 300);
            
            for (int i = 0; i < categoryIds.size(); i++) {
                categoryService.updateCategorySortOrder(
                    categoryIds.get(i), newSortOrders.get(i));
            }
            
            System.out.println("批量排序更新完成");
            
        } catch (Exception e) {
            System.err.println("分类重组操作失败: " + e.getMessage());
        }
    }
    
    // 知识类型专属分类管理示例
    public void knowledgeTypeSpecificCategoryExample() {
        try {
            Long knowledgeTypeId = 1001L;
            
            // 1. 创建知识类型专属分类
            CategoryDTO specificCategory = new CategoryDTO();
            specificCategory.setName("Spring Boot专题");
            specificCategory.setDescription("Spring Boot框架专属分类");
            specificCategory.setContentCategory("knowledge");
            specificCategory.setSubTypeId(knowledgeTypeId);
            specificCategory.setSortOrder(100);
            specificCategory.setIsActive(true);
            
            Result<CategoryDTO> createResult = categoryService.createCategory(specificCategory);
            
            if (createResult.isSuccess()) {
                System.out.println("专属分类创建成功: " + 
                    createResult.getData().getName());
            }
            
            // 2. 查询该知识类型的专属分类
            Result<List<CategoryDTO>> specificResult = 
                categoryService.getCategoriesBySubType("knowledge", knowledgeTypeId);
            
            if (specificResult.isSuccess()) {
                System.out.println("知识类型专属分类:");
                for (CategoryDTO category : specificResult.getData()) {
                    System.out.println("- " + category.getName());
                }
            }
            
            // 3. 查询该知识类型可用的所有分类
            Result<List<CategoryDTO>> availableResult = 
                categoryService.getAvailableCategoriesForKnowledgeType(knowledgeTypeId);
            
            if (availableResult.isSuccess()) {
                System.out.println("知识类型可用的所有分类:");
                for (CategoryDTO category : availableResult.getData()) {
                    String type = "";
                    if (category.getSubTypeId() != null) {
                        type = " (专属)";
                    } else if ("knowledge".equals(category.getContentCategory())) {
                        type = " (通用知识)";
                    } else if ("general".equals(category.getContentCategory())) {
                        type = " (全局通用)";
                    }
                    System.out.println("- " + category.getName() + type);
                }
            }
            
        } catch (Exception e) {
            System.err.println("知识类型专属分类管理失败: " + e.getMessage());
        }
    }
    
    // 辅助方法：递归打印分类树
    private void printCategoryTree(List<CategoryDTO> categories, int level) {
        for (CategoryDTO category : categories) {
            String indent = "  ".repeat(level);
            String status = category.getIsActive() ? "启用" : "禁用";
            System.out.println(String.format("%s- %s (ID:%d, 排序:%d, %s)", 
                indent, category.getName(), category.getId(), 
                category.getSortOrder(), status));
            
            if (category.getChildren() != null && !category.getChildren().isEmpty()) {
                printCategoryTree(category.getChildren(), level + 1);
            }
        }
    }
}
```

## 业务场景说明

### 1. 知识类型专属分类
系统支持为不同的知识类型创建专属分类，通过`subTypeId`字段关联：
- 当`subTypeId`不为null时，该分类仅对指定知识类型可用
- 当`subTypeId`为null且`contentCategory`为"knowledge"时，为通用知识分类
- 当`contentCategory`为"general"时，为全局通用分类

### 2. 分类层级管理
- 支持多级分类嵌套，通过`parentId`建立父子关系
- 系统自动维护分类的`level`和`path`字段
- 移动分类时会自动检查循环引用

### 3. 分类排序
- 通过`sortOrder`字段控制同级分类的显示顺序
- 数值越小排序越靠前
- 支持批量更新排序权重

---

**文档版本**: 2.0.0  
**最后更新**: 2025-07-17  
**维护团队**: AI Community Development Team
