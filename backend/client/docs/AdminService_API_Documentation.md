# AdminService API 文档

## 接口概述

**接口名称**: AdminService  
**包路径**: com.jdl.aic.core.service.client.service.AdminService  
**功能描述**: 管理员服务接口，提供管理员权限管理功能，包括管理员用户的CRUD操作、角色和权限管理、用户角色分配、权限验证等。  
**版本**: 1.0.0  
**作者**: AI Community Development Team  

## 数据模型

### AdminUserDTO
管理员用户数据传输对象

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | Long | 否 | 管理员用户ID |
| ssoId | String | 是 | SSO/LDAP唯一标识 |
| username | String | 是 | 用户名（如工号） |
| displayName | String | 是 | 显示名称 |
| email | String | 否 | 邮箱地址 |
| isActive | Boolean | 否 | 是否活跃 |
| roles | List<AdminRoleDTO> | 否 | 角色列表 |
| permissions | List<String> | 否 | 权限列表 |
| lastLoginAt | LocalDateTime | 否 | 最后登录时间 |
| createdAt | LocalDateTime | 否 | 创建时间 |
| updatedAt | LocalDateTime | 否 | 更新时间 |

## API 接口列表

### 1. 管理员用户管理

#### 1.1 获取管理员用户列表（分页）

**方法名**: `getAdminUsers`  
**描述**: 分页获取管理员用户列表，支持活跃状态过滤和关键词搜索  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求参数
- `isActive` (Boolean, 可选): 活跃状态过滤
- `search` (String, 可选): 搜索关键词

**返回值**: `Result<PageResult<AdminUserDTO>>`

**调用示例**:
```java
// 创建分页请求
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(10);

// 调用接口
Result<PageResult<AdminUserDTO>> result = adminService.getAdminUsers(
    pageRequest, 
    true,  // 只查询活跃用户
    "张三"  // 搜索关键词
);

if (result.isSuccess()) {
    PageResult<AdminUserDTO> pageResult = result.getData();
    List<AdminUserDTO> users = pageResult.getList();
    long total = pageResult.getTotal();
    System.out.println("总数: " + total);
    for (AdminUserDTO user : users) {
        System.out.println("用户: " + user.getDisplayName());
    }
}
```

#### 1.2 根据ID获取管理员用户详情

**方法名**: `getAdminUserById`  
**描述**: 根据用户ID获取管理员用户详细信息  

**请求参数**:
- `id` (Long): 管理员用户ID

**返回值**: `Result<AdminUserDTO>`

**调用示例**:
```java
Long userId = 1001L;
Result<AdminUserDTO> result = adminService.getAdminUserById(userId);

if (result.isSuccess()) {
    AdminUserDTO user = result.getData();
    System.out.println("用户名: " + user.getUsername());
    System.out.println("显示名: " + user.getDisplayName());
    System.out.println("邮箱: " + user.getEmail());
    System.out.println("状态: " + (user.getIsActive() ? "活跃" : "非活跃"));
}
```

#### 1.3 根据SSO ID获取管理员用户详情

**方法名**: `getAdminUserBySsoId`  
**描述**: 根据SSO唯一标识获取管理员用户详细信息  

**请求参数**:
- `ssoId` (String): SSO唯一标识

**返回值**: `Result<AdminUserDTO>`

**调用示例**:
```java
String ssoId = "emp001";
Result<AdminUserDTO> result = adminService.getAdminUserBySsoId(ssoId);

if (result.isSuccess()) {
    AdminUserDTO user = result.getData();
    System.out.println("找到用户: " + user.getDisplayName());
} else {
    System.out.println("未找到SSO ID为 " + ssoId + " 的用户");
}
```

#### 1.4 创建管理员用户

**方法名**: `createAdminUser`  
**描述**: 创建新的管理员用户  

**请求参数**:
- `adminUser` (AdminUserDTO): 管理员用户信息

**返回值**: `Result<AdminUserDTO>`

**调用示例**:
```java
// 创建新用户对象
AdminUserDTO newUser = new AdminUserDTO();
newUser.setSsoId("emp002");
newUser.setUsername("zhangsan");
newUser.setDisplayName("张三");
newUser.setEmail("<EMAIL>");
newUser.setIsActive(true);

// 调用创建接口
Result<AdminUserDTO> result = adminService.createAdminUser(newUser);

if (result.isSuccess()) {
    AdminUserDTO createdUser = result.getData();
    System.out.println("用户创建成功，ID: " + createdUser.getId());
} else {
    System.out.println("用户创建失败: " + result.getMessage());
}
```

#### 1.5 更新管理员用户信息

**方法名**: `updateAdminUser`  
**描述**: 更新指定管理员用户的信息  

**请求参数**:
- `id` (Long): 管理员用户ID
- `adminUser` (AdminUserDTO): 更新的用户信息

**返回值**: `Result<AdminUserDTO>`

**调用示例**:
```java
Long userId = 1001L;
AdminUserDTO updateUser = new AdminUserDTO();
updateUser.setDisplayName("张三（更新）");
updateUser.setEmail("<EMAIL>");

Result<AdminUserDTO> result = adminService.updateAdminUser(userId, updateUser);

if (result.isSuccess()) {
    AdminUserDTO updatedUser = result.getData();
    System.out.println("用户更新成功: " + updatedUser.getDisplayName());
}
```

#### 1.6 删除管理员用户

**方法名**: `deleteAdminUser`  
**描述**: 删除指定的管理员用户  

**请求参数**:
- `id` (Long): 管理员用户ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Long userId = 1001L;
Result<Void> result = adminService.deleteAdminUser(userId);

if (result.isSuccess()) {
    System.out.println("用户删除成功");
} else {
    System.out.println("用户删除失败: " + result.getMessage());
}
```

#### 1.7 启用/禁用管理员用户

**方法名**: `toggleAdminUserStatus`  
**描述**: 启用或禁用指定的管理员用户  

**请求参数**:
- `id` (Long): 管理员用户ID
- `isActive` (Boolean): 是否启用

**返回值**: `Result<Void>`

**调用示例**:
```java
Long userId = 1001L;
boolean enable = false; // 禁用用户

Result<Void> result = adminService.toggleAdminUserStatus(userId, enable);

if (result.isSuccess()) {
    System.out.println("用户状态更新成功");
}
```

### 2. 角色管理

#### 2.1 获取所有角色列表

**方法名**: `getAllRoles`  
**描述**: 获取系统中所有角色的列表  

**请求参数**: 无

**返回值**: `Result<List<AdminRoleDTO>>`

**调用示例**:
```java
Result<List<AdminRoleDTO>> result = adminService.getAllRoles();

if (result.isSuccess()) {
    List<AdminRoleDTO> roles = result.getData();
    System.out.println("系统角色数量: " + roles.size());
    for (AdminRoleDTO role : roles) {
        System.out.println("角色: " + role.getName());
    }
}
```

#### 2.2 根据ID获取角色详情

**方法名**: `getRoleById`  
**描述**: 根据角色ID获取角色详细信息  

**请求参数**:
- `id` (Long): 角色ID

**返回值**: `Result<AdminRoleDTO>`

**调用示例**:
```java
Long roleId = 1L;
Result<AdminRoleDTO> result = adminService.getRoleById(roleId);

if (result.isSuccess()) {
    AdminRoleDTO role = result.getData();
    System.out.println("角色名称: " + role.getName());
    System.out.println("角色描述: " + role.getDescription());
}
```

#### 2.3 创建角色

**方法名**: `createRole`  
**描述**: 创建新的角色  

**请求参数**:
- `role` (AdminRoleDTO): 角色信息

**返回值**: `Result<AdminRoleDTO>`

**调用示例**:
```java
AdminRoleDTO newRole = new AdminRoleDTO();
newRole.setName("内容管理员");
newRole.setDescription("负责内容的审核和管理");
newRole.setIsActive(true);

Result<AdminRoleDTO> result = adminService.createRole(newRole);

if (result.isSuccess()) {
    AdminRoleDTO createdRole = result.getData();
    System.out.println("角色创建成功，ID: " + createdRole.getId());
}
```

#### 2.4 更新角色信息

**方法名**: `updateRole`  
**描述**: 更新指定角色的信息  

**请求参数**:
- `id` (Long): 角色ID
- `role` (AdminRoleDTO): 更新的角色信息

**返回值**: `Result<AdminRoleDTO>`

**调用示例**:
```java
Long roleId = 1L;
AdminRoleDTO updateRole = new AdminRoleDTO();
updateRole.setDescription("负责内容的审核、管理和发布");

Result<AdminRoleDTO> result = adminService.updateRole(roleId, updateRole);

if (result.isSuccess()) {
    System.out.println("角色更新成功");
}
```

#### 2.5 删除角色

**方法名**: `deleteRole`  
**描述**: 删除指定的角色  

**请求参数**:
- `id` (Long): 角色ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Long roleId = 1L;
Result<Void> result = adminService.deleteRole(roleId);

if (result.isSuccess()) {
    System.out.println("角色删除成功");
}
```

### 3. 权限管理

#### 3.1 获取所有权限列表

**方法名**: `getAllPermissions`  
**描述**: 获取系统中所有权限的列表  

**请求参数**: 无

**返回值**: `Result<List<AdminPermissionDTO>>`

**调用示例**:
```java
Result<List<AdminPermissionDTO>> result = adminService.getAllPermissions();

if (result.isSuccess()) {
    List<AdminPermissionDTO> permissions = result.getData();
    System.out.println("系统权限数量: " + permissions.size());
    for (AdminPermissionDTO permission : permissions) {
        System.out.println("权限: " + permission.getName());
    }
}
```

#### 3.2 根据ID获取权限详情

**方法名**: `getPermissionById`  
**描述**: 根据权限ID获取权限详细信息  

**请求参数**:
- `id` (Long): 权限ID

**返回值**: `Result<AdminPermissionDTO>`

**调用示例**:
```java
Long permissionId = 1L;
Result<AdminPermissionDTO> result = adminService.getPermissionById(permissionId);

if (result.isSuccess()) {
    AdminPermissionDTO permission = result.getData();
    System.out.println("权限名称: " + permission.getName());
    System.out.println("权限描述: " + permission.getDescription());
}
```

#### 3.3 创建权限

**方法名**: `createPermission`  
**描述**: 创建新的权限  

**请求参数**:
- `permission` (AdminPermissionDTO): 权限信息

**返回值**: `Result<AdminPermissionDTO>`

**调用示例**:
```java
AdminPermissionDTO newPermission = new AdminPermissionDTO();
newPermission.setName("content:read");
newPermission.setDescription("内容查看权限");
newPermission.setResource("content");
newPermission.setAction("read");

Result<AdminPermissionDTO> result = adminService.createPermission(newPermission);

if (result.isSuccess()) {
    System.out.println("权限创建成功");
}
```

#### 3.4 更新权限信息

**方法名**: `updatePermission`  
**描述**: 更新指定权限的信息  

**请求参数**:
- `id` (Long): 权限ID
- `permission` (AdminPermissionDTO): 更新的权限信息

**返回值**: `Result<AdminPermissionDTO>`

**调用示例**:
```java
Long permissionId = 1L;
AdminPermissionDTO updatePermission = new AdminPermissionDTO();
updatePermission.setDescription("内容查看和搜索权限");

Result<AdminPermissionDTO> result = adminService.updatePermission(permissionId, updatePermission);

if (result.isSuccess()) {
    System.out.println("权限更新成功");
}
```

#### 3.5 删除权限

**方法名**: `deletePermission`  
**描述**: 删除指定的权限  

**请求参数**:
- `id` (Long): 权限ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Long permissionId = 1L;
Result<Void> result = adminService.deletePermission(permissionId);

if (result.isSuccess()) {
    System.out.println("权限删除成功");
}
```

### 4. 用户角色分配

#### 4.1 为用户分配角色

**方法名**: `assignRolesToUser`  
**描述**: 为指定用户分配一个或多个角色  

**请求参数**:
- `userId` (Long): 用户ID
- `roleIds` (List<Long>): 角色ID列表

**返回值**: `Result<Void>`

**调用示例**:
```java
Long userId = 1001L;
List<Long> roleIds = Arrays.asList(1L, 2L, 3L); // 分配多个角色

Result<Void> result = adminService.assignRolesToUser(userId, roleIds);

if (result.isSuccess()) {
    System.out.println("角色分配成功");
} else {
    System.out.println("角色分配失败: " + result.getMessage());
}
```

#### 4.2 移除用户的角色

**方法名**: `removeRolesFromUser`  
**描述**: 移除指定用户的一个或多个角色  

**请求参数**:
- `userId` (Long): 用户ID
- `roleIds` (List<Long>): 要移除的角色ID列表

**返回值**: `Result<Void>`

**调用示例**:
```java
Long userId = 1001L;
List<Long> roleIds = Arrays.asList(2L); // 移除指定角色

Result<Void> result = adminService.removeRolesFromUser(userId, roleIds);

if (result.isSuccess()) {
    System.out.println("角色移除成功");
}
```

#### 4.3 获取用户的所有角色

**方法名**: `getUserRoles`  
**描述**: 获取指定用户拥有的所有角色  

**请求参数**:
- `userId` (Long): 用户ID

**返回值**: `Result<List<AdminRoleDTO>>`

**调用示例**:
```java
Long userId = 1001L;
Result<List<AdminRoleDTO>> result = adminService.getUserRoles(userId);

if (result.isSuccess()) {
    List<AdminRoleDTO> roles = result.getData();
    System.out.println("用户拥有 " + roles.size() + " 个角色:");
    for (AdminRoleDTO role : roles) {
        System.out.println("- " + role.getName());
    }
}
```

#### 4.4 获取用户的所有权限

**方法名**: `getUserPermissions`  
**描述**: 获取指定用户拥有的所有权限（通过角色继承）  

**请求参数**:
- `userId` (Long): 用户ID

**返回值**: `Result<List<String>>`

**调用示例**:
```java
Long userId = 1001L;
Result<List<String>> result = adminService.getUserPermissions(userId);

if (result.isSuccess()) {
    List<String> permissions = result.getData();
    System.out.println("用户拥有 " + permissions.size() + " 个权限:");
    for (String permission : permissions) {
        System.out.println("- " + permission);
    }
}
```

### 5. 角色权限分配

#### 5.1 为角色分配权限

**方法名**: `assignPermissionsToRole`  
**描述**: 为指定角色分配一个或多个权限  

**请求参数**:
- `roleId` (Long): 角色ID
- `permissionIds` (List<Long>): 权限ID列表

**返回值**: `Result<Void>`

**调用示例**:
```java
Long roleId = 1L;
List<Long> permissionIds = Arrays.asList(1L, 2L, 3L, 4L); // 分配多个权限

Result<Void> result = adminService.assignPermissionsToRole(roleId, permissionIds);

if (result.isSuccess()) {
    System.out.println("权限分配成功");
}
```

#### 5.2 移除角色的权限

**方法名**: `removePermissionsFromRole`  
**描述**: 移除指定角色的一个或多个权限  

**请求参数**:
- `roleId` (Long): 角色ID
- `permissionIds` (List<Long>): 要移除的权限ID列表

**返回值**: `Result<Void>`

**调用示例**:
```java
Long roleId = 1L;
List<Long> permissionIds = Arrays.asList(3L); // 移除指定权限

Result<Void> result = adminService.removePermissionsFromRole(roleId, permissionIds);

if (result.isSuccess()) {
    System.out.println("权限移除成功");
}
```

#### 5.3 获取角色的所有权限

**方法名**: `getRolePermissions`  
**描述**: 获取指定角色拥有的所有权限  

**请求参数**:
- `roleId` (Long): 角色ID

**返回值**: `Result<List<AdminPermissionDTO>>`

**调用示例**:
```java
Long roleId = 1L;
Result<List<AdminPermissionDTO>> result = adminService.getRolePermissions(roleId);

if (result.isSuccess()) {
    List<AdminPermissionDTO> permissions = result.getData();
    System.out.println("角色拥有 " + permissions.size() + " 个权限:");
    for (AdminPermissionDTO permission : permissions) {
        System.out.println("- " + permission.getName() + ": " + permission.getDescription());
    }
}
```

### 6. 权限验证

#### 6.1 验证用户权限

**方法名**: `hasPermission`  
**描述**: 验证指定用户是否具有某个权限  

**请求参数**:
- `userId` (Long): 用户ID
- `permission` (String): 权限名称

**返回值**: `Result<Boolean>`

**调用示例**:
```java
Long userId = 1001L;
String permission = "content:read";

Result<Boolean> result = adminService.hasPermission(userId, permission);

if (result.isSuccess()) {
    boolean hasPermission = result.getData();
    if (hasPermission) {
        System.out.println("用户具有 " + permission + " 权限");
        // 允许执行相关操作
    } else {
        System.out.println("用户没有 " + permission + " 权限");
        // 拒绝访问
    }
}
```

#### 6.2 验证用户角色

**方法名**: `hasRole`  
**描述**: 验证指定用户是否具有某个角色  

**请求参数**:
- `userId` (Long): 用户ID
- `roleName` (String): 角色名称

**返回值**: `Result<Boolean>`

**调用示例**:
```java
Long userId = 1001L;
String roleName = "内容管理员";

Result<Boolean> result = adminService.hasRole(userId, roleName);

if (result.isSuccess()) {
    boolean hasRole = result.getData();
    if (hasRole) {
        System.out.println("用户具有 " + roleName + " 角色");
    } else {
        System.out.println("用户没有 " + roleName + " 角色");
    }
}
```

## 错误处理

所有接口都返回统一的 `Result<T>` 结果对象，包含以下信息：

- `success`: 操作是否成功
- `code`: 错误码
- `message`: 错误信息或成功信息
- `data`: 返回的数据

**常见错误码**:
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

**错误处理示例**:
```java
Result<AdminUserDTO> result = adminService.getAdminUserById(userId);

if (result.isSuccess()) {
    // 处理成功情况
    AdminUserDTO user = result.getData();
    // 业务逻辑处理
} else {
    // 处理错误情况
    System.err.println("操作失败: " + result.getMessage());
    System.err.println("错误码: " + result.getCode());
    
    // 根据错误码进行不同处理
    switch (result.getCode()) {
        case 404:
            System.out.println("用户不存在");
            break;
        case 403:
            System.out.println("权限不足");
            break;
        default:
            System.out.println("其他错误");
    }
}
```

## 最佳实践

### 1. 权限设计原则
- 遵循最小权限原则，只分配必要的权限
- 使用角色来组织权限，避免直接给用户分配权限
- 定期审查和清理不必要的权限

### 2. 安全建议
- 在执行敏感操作前，始终验证用户权限
- 记录重要的权限变更操作日志
- 定期检查用户权限的合理性

### 3. 性能优化
- 缓存用户权限信息，减少数据库查询
- 批量操作时使用批量接口，避免循环调用
- 合理使用分页，避免一次性加载大量数据

### 4. 调用示例综合场景

```java
// 综合示例：完整的用户权限管理流程
public class AdminServiceExample {
    
    private AdminService adminService;
    
    public void completeUserManagementExample() {
        try {
            // 1. 创建新用户
            AdminUserDTO newUser = new AdminUserDTO();
            newUser.setSsoId("emp003");
            newUser.setUsername("lisi");
            newUser.setDisplayName("李四");
            newUser.setEmail("<EMAIL>");
            newUser.setIsActive(true);
            
            Result<AdminUserDTO> createResult = adminService.createAdminUser(newUser);
            if (!createResult.isSuccess()) {
                throw new RuntimeException("用户创建失败: " + createResult.getMessage());
            }
            
            Long userId = createResult.getData().getId();
            System.out.println("用户创建成功，ID: " + userId);
            
            // 2. 为用户分配角色
            List<Long> roleIds = Arrays.asList(1L, 2L); // 内容管理员、审核员
            Result<Void> assignResult = adminService.assignRolesToUser(userId, roleIds);
            if (assignResult.isSuccess()) {
                System.out.println("角色分配成功");
            }
            
            // 3. 验证用户权限
            Result<Boolean> permissionResult = adminService.hasPermission(userId, "content:write");
            if (permissionResult.isSuccess() && permissionResult.getData()) {
                System.out.println("用户具有内容编辑权限，可以执行相关操作");
                // 执行需要权限的业务逻辑
            }
            
            // 4. 查询用户详情和权限
            Result<AdminUserDTO> userResult = adminService.getAdminUserById(userId);
            if (userResult.isSuccess()) {
                AdminUserDTO user = userResult.getData();
                System.out.println("用户信息: " + user.getDisplayName());
                
                // 获取用户所有权限
                Result<List<String>> permissionsResult = adminService.getUserPermissions(userId);
                if (permissionsResult.isSuccess()) {
                    System.out.println("用户权限: " + permissionsResult.getData());
                }
            }
            
        } catch (Exception e) {
            System.err.println("操作失败: " + e.getMessage());
        }
    }
}
```

---

**文档版本**: 1.0.0  
**最后更新**: 2025-07-17  
**维护团队**: AI Community Development Team
