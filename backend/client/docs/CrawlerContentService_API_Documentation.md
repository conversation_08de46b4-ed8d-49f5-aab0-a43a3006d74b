# CrawlerContentService API 文档

## 接口概述

**接口名称**: CrawlerContentService  
**包路径**: com.jdl.aic.core.service.client.service.CrawlerContentService  
**功能描述**: 爬虫内容管理服务接口，提供爬虫内容管理功能，包括爬虫内容的CRUD操作、内容去重和校验、内容状态和质量管理、内容搜索和分析等。  
**版本**: 1.0.0  
**作者**: AI Community Development Team  

## 数据模型

### CrawlerContentDTO
爬虫内容数据传输对象

**author 字段说明**：
author 字段存储 JSON 格式的作者信息，示例格式：
```json
{
  "name": "张三",
  "url": "https://example.com/author/zhangsan",
  "email": "<EMAIL>",
  "avatar": "https://example.com/avatar.jpg"
}
```

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 内容ID |
| title | String | 内容标题 |
| content | String | 内容正文 |
| contentMd5 | String | 内容MD5值（用于去重） |
| link | String | 原始链接 |
| contentType | String | 内容类型（article, video, document等） |
| type | String | 内容类型，区分文章、视频、音频、图片 |
| language | String | 内容语言（zh-CN, en-US等） |
| author | String | 作者信息（JSON格式，包含姓名、链接等） |
| attachments | List<Object> | 附件信息，JSON格式 |
| media | List<Object> | 媒体资源信息，JSON格式 |
| taskId | String | 任务ID |
| taskName | String | 任务名称 |
| publishDate | LocalDateTime | 发布时间 |
| crawlDate | LocalDateTime | 爬取时间 |
| status | Integer | 内容状态（0:待处理, 1:已处理, 2:已发布, -1:已删除） |
| qualityScore | BigDecimal | 质量评分（0-100） |
| isFeatured | Boolean | 是否精品内容 |
| aiSummary | String | AI生成的摘要 |
| tags | String | 标签（逗号分隔） |
| viewCount | Integer | 浏览次数 |
| likeCount | Integer | 点赞次数 |
| source | String | 内容来源 |
| createTime | LocalDateTime | 创建时间 |
| updateTime | LocalDateTime | 更新时间 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |

## API 接口列表

### 1. 爬虫内容查询

#### 1.1 获取爬虫内容列表（分页）

**方法名**: `getCrawlerContentList`  
**描述**: 获取爬虫内容列表，支持分页和多种过滤条件  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求参数
- `contentType` (String, 可选): 内容类型过滤
- `language` (String, 可选): 语言过滤
- `status` (Integer, 可选): 状态过滤
- `isFeatured` (Boolean, 可选): 是否精品内容过滤
- `search` (String, 可选): 搜索关键词

**返回值**: `Result<PageResult<CrawlerContentDTO>>`

**调用示例**:
```java
// 基本分页查询
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

Result<PageResult<CrawlerContentDTO>> result = crawlerContentService.getCrawlerContentList(
    pageRequest, null, null, 1, null, null);

if (result.isSuccess()) {
    PageResult<CrawlerContentDTO> pageResult = result.getData();
    System.out.println("总数: " + pageResult.getTotal());
    
    for (CrawlerContentDTO content : pageResult.getList()) {
        System.out.println("标题: " + content.getTitle());
        System.out.println("类型: " + content.getContentType());
        System.out.println("质量评分: " + content.getQualityScore());
    }
}
```

#### 1.2 根据ID获取爬虫内容详情

**方法名**: `getCrawlerContentById`  
**描述**: 根据内容ID获取详细信息  

**请求参数**:
- `id` (Long): 内容ID

**返回值**: `Result<CrawlerContentDTO>`

**调用示例**:
```java
Result<CrawlerContentDTO> result = crawlerContentService.getCrawlerContentById(1001L);

if (result.isSuccess()) {
    CrawlerContentDTO content = result.getData();
    System.out.println("标题: " + content.getTitle());
    System.out.println("作者: " + content.getAuthor());
    System.out.println("内容: " + content.getContent());
    System.out.println("原始链接: " + content.getLink());
    System.out.println("AI摘要: " + content.getAiSummary());
}
```

#### 1.3 根据MD5获取爬虫内容

**方法名**: `getCrawlerContentByMd5`  
**描述**: 根据内容MD5值获取内容信息，用于去重检查  

**请求参数**:
- `contentMd5` (String): 内容MD5值

**返回值**: `Result<CrawlerContentDTO>`

**调用示例**:
```java
String contentMd5 = "d41d8cd98f00b204e9800998ecf8427e";
Result<CrawlerContentDTO> result = crawlerContentService.getCrawlerContentByMd5(contentMd5);

if (result.isSuccess()) {
    CrawlerContentDTO content = result.getData();
    System.out.println("找到重复内容: " + content.getTitle());
} else {
    System.out.println("内容不存在，可以添加");
}
```

### 2. 爬虫内容管理

#### 2.1 创建爬虫内容

**方法名**: `createCrawlerContent`  
**描述**: 创建新的爬虫内容  

**请求参数**:
- `crawlerContent` (CrawlerContentDTO): 爬虫内容信息

**返回值**: `Result<CrawlerContentDTO>`

**调用示例**:
```java
CrawlerContentDTO newContent = new CrawlerContentDTO();
newContent.setTitle("Spring Boot 最佳实践");
newContent.setContent("这是一篇关于Spring Boot最佳实践的文章...");
newContent.setContentMd5("abc123def456");
newContent.setLink("https://example.com/spring-boot-best-practices");
newContent.setContentType("article");
newContent.setLanguage("zh-CN");
newContent.setAuthor("技术专家");
newContent.setStatus(0);
newContent.setSource("技术博客");
newContent.setTags("Spring Boot,Java,最佳实践");

Result<CrawlerContentDTO> result = crawlerContentService.createCrawlerContent(newContent);

if (result.isSuccess()) {
    CrawlerContentDTO createdContent = result.getData();
    System.out.println("内容创建成功，ID: " + createdContent.getId());
}
```

#### 2.2 更新爬虫内容

**方法名**: `updateCrawlerContent`  
**描述**: 更新爬虫内容信息  

**请求参数**:
- `id` (Long): 内容ID
- `crawlerContent` (CrawlerContentDTO): 更新的内容信息

**返回值**: `Result<CrawlerContentDTO>`

**调用示例**:
```java
CrawlerContentDTO updateContent = new CrawlerContentDTO();
updateContent.setTitle("Spring Boot 最佳实践（更新版）");
updateContent.setStatus(1);
updateContent.setQualityScore(new BigDecimal("85.5"));
updateContent.setIsFeatured(true);

Result<CrawlerContentDTO> result = crawlerContentService.updateCrawlerContent(1001L, updateContent);

if (result.isSuccess()) {
    CrawlerContentDTO updatedContent = result.getData();
    System.out.println("内容更新成功: " + updatedContent.getTitle());
}
```

#### 2.3 删除爬虫内容

**方法名**: `deleteCrawlerContent`  
**描述**: 删除指定的爬虫内容  

**请求参数**:
- `id` (Long): 内容ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = crawlerContentService.deleteCrawlerContent(1001L);

if (result.isSuccess()) {
    System.out.println("内容删除成功");
} else {
    System.err.println("删除失败: " + result.getMessage());
}
```

### 3. 内容状态管理

#### 3.1 根据状态获取爬虫内容列表

**方法名**: `getCrawlerContentsByStatus`  
**描述**: 根据内容状态获取内容列表  

**请求参数**:
- `status` (Integer): 内容状态
- `limit` (Integer, 可选): 限制数量

**返回值**: `Result<List<CrawlerContentDTO>>`

**调用示例**:
```java
// 获取待处理的内容
Result<List<CrawlerContentDTO>> result = crawlerContentService.getCrawlerContentsByStatus(0, 50);

if (result.isSuccess()) {
    List<CrawlerContentDTO> pendingContents = result.getData();
    System.out.println("待处理内容数量: " + pendingContents.size());
    
    for (CrawlerContentDTO content : pendingContents) {
        System.out.println("- " + content.getTitle());
    }
}
```

#### 3.2 更新内容状态

**方法名**: `updateCrawlerContentStatus`  
**描述**: 更新单个内容的状态  

**请求参数**:
- `id` (Long): 内容ID
- `status` (Integer): 内容状态

**返回值**: `Result<Void>`

**调用示例**:
```java
// 将内容状态更新为已处理
Result<Void> result = crawlerContentService.updateCrawlerContentStatus(1001L, 1);

if (result.isSuccess()) {
    System.out.println("内容状态更新成功");
}
```

#### 3.3 批量更新内容状态

**方法名**: `batchUpdateCrawlerContentStatus`  
**描述**: 批量更新多个内容的状态  

**请求参数**:
- `ids` (List<Long>): 内容ID列表
- `status` (Integer): 内容状态

**返回值**: `Result<Void>`

**调用示例**:
```java
List<Long> contentIds = Arrays.asList(1001L, 1002L, 1003L, 1004L);

Result<Void> result = crawlerContentService.batchUpdateCrawlerContentStatus(contentIds, 1);

if (result.isSuccess()) {
    System.out.println("批量状态更新成功，共更新: " + contentIds.size() + "条内容");
}
```

### 4. 内容质量管理

#### 4.1 更新AI总结

**方法名**: `updateAiSummary`  
**描述**: 更新内容的AI生成摘要  

**请求参数**:
- `id` (Long): 内容ID
- `aiSummary` (String): AI总结内容

**返回值**: `Result<Void>`

**调用示例**:
```java
String aiSummary = "本文介绍了Spring Boot的核心概念和最佳实践，" +
    "包括自动配置、起步依赖、生产就绪特性等关键内容。";

Result<Void> result = crawlerContentService.updateAiSummary(1001L, aiSummary);

if (result.isSuccess()) {
    System.out.println("AI摘要更新成功");
}
```

#### 4.2 更新质量评分

**方法名**: `updateQualityScore`  
**描述**: 更新内容的质量评分  

**请求参数**:
- `id` (Long): 内容ID
- `qualityScore` (BigDecimal): 质量评分（0-100）

**返回值**: `Result<Void>`

**调用示例**:
```java
BigDecimal qualityScore = new BigDecimal("88.5");
Result<Void> result = crawlerContentService.updateQualityScore(1001L, qualityScore);

if (result.isSuccess()) {
    System.out.println("质量评分更新成功");
}
```

### 5. 搜索和统计

#### 5.1 搜索爬虫内容

**方法名**: `searchCrawlerContents`  
**描述**: 根据关键词搜索爬虫内容  

**请求参数**:
- `keyword` (String): 搜索关键词
- `contentType` (String, 可选): 内容类型过滤
- `language` (String, 可选): 语言过滤
- `status` (Integer, 可选): 状态过滤
- `limit` (Integer, 可选): 限制数量

**返回值**: `Result<List<CrawlerContentDTO>>`

**调用示例**:
```java
// 搜索Java相关内容
Result<List<CrawlerContentDTO>> result = crawlerContentService.searchCrawlerContents(
    "Java", "article", "zh-CN", 2, 20);

if (result.isSuccess()) {
    List<CrawlerContentDTO> searchResults = result.getData();
    System.out.println("搜索到Java相关内容: " + searchResults.size() + "条");
    
    for (CrawlerContentDTO content : searchResults) {
        System.out.println("- " + content.getTitle());
    }
}
```

#### 5.2 统计各状态的内容数量

**方法名**: `getContentStatusStatistics`  
**描述**: 统计各状态的内容数量  

**返回值**: `Result<List<Object>>`

**调用示例**:
```java
Result<List<Object>> result = crawlerContentService.getContentStatusStatistics();

if (result.isSuccess()) {
    List<Object> statistics = result.getData();
    System.out.println("内容状态统计:");
    
    for (Object stat : statistics) {
        if (stat instanceof Map) {
            Map<String, Object> statMap = (Map<String, Object>) stat;
            Integer status = (Integer) statMap.get("status");
            Long count = (Long) statMap.get("count");
            
            String statusName = getStatusName(status);
            System.out.println("- " + statusName + ": " + count + "条");
        }
    }
}

private String getStatusName(Integer status) {
    switch (status) {
        case 0: return "待处理";
        case 1: return "已处理";
        case 2: return "已发布";
        case -1: return "已删除";
        default: return "未知状态";
    }
}
```

## 错误处理

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 404 | 内容不存在 | 确认内容ID是否正确 |
| 409 | 内容重复 | 检查MD5或链接是否已存在 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误处理示例

```java
Result<CrawlerContentDTO> result = crawlerContentService.getCrawlerContentById(1001L);

if (!result.isSuccess()) {
    switch (result.getCode()) {
        case 404:
            System.err.println("内容不存在: " + result.getMessage());
            break;
        case 500:
            System.err.println("服务器错误: " + result.getMessage());
            break;
        default:
            System.err.println("未知错误: " + result.getMessage());
    }
}
```

## 最佳实践

### 1. 内容去重
在创建内容前，建议先检查MD5和链接是否已存在：

```java
// 检查MD5去重
Result<CrawlerContentDTO> md5Result = crawlerContentService.getCrawlerContentByMd5(contentMd5);
if (md5Result.isSuccess()) {
    System.out.println("内容已存在，跳过创建");
    return;
}

// 检查链接去重
Result<CrawlerContentDTO> linkResult = crawlerContentService.getCrawlerContentByLink(link);
if (linkResult.isSuccess()) {
    System.out.println("链接已存在，跳过创建");
    return;
}
```

### 2. 批量操作
对于大量数据操作，建议使用批量接口：

```java
// 批量创建而不是单个创建
List<CrawlerContentDTO> contentList = new ArrayList<>();
// ... 添加内容到列表
Result<List<CrawlerContentDTO>> result = crawlerContentService.batchCreateCrawlerContents(contentList);
```

### 3. 状态管理
建议按照以下流程管理内容状态：
1. 创建时设置为待处理(0)
2. 处理完成后更新为已处理(1)
3. 审核通过后发布(2)
4. 如需删除则标记为已删除(-1)

### 4. 质量评分
建议在内容处理完成后及时更新质量评分和AI摘要：

```java
// 更新质量评分
crawlerContentService.updateQualityScore(contentId, new BigDecimal("85.0"));

// 更新AI摘要
crawlerContentService.updateAiSummary(contentId, "AI生成的内容摘要");
```

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2025-07-17 | 初始版本，包含基础CRUD和管理功能 |

## 联系方式

如有问题或建议，请联系：
- 技术支持：AI Community Development Team
- 邮箱：<EMAIL>
- 文档更新：定期更新，请关注最新版本
