# LearningCourseService API 文档

## 概述

LearningCourseService 是学习课程管理服务接口，提供完整的学习课程管理功能，包括课程的CRUD操作、状态管理、搜索过滤、统计分析和推荐功能。

**版本**: 1.0.0  
**作者**: AI Community Development Team

## 功能特性

- 课程的CRUD操作
- 课程状态管理
- 课程搜索和过滤
- 课程统计和分析
- 课程推荐功能
- 批量操作支持

## 数据模型

### LearningCourseDTO

学习课程数据传输对象，包含课程的完整信息。

```json
{
  "id": 1,
  "name": "Java基础编程",
  "description": "Java编程语言基础知识学习课程",
  "category": "编程语言",
  "difficultyLevel": "BEGINNER",
  "totalHours": 40.5,
  "resourceCount": 15,
  "enrolledCount": 120,
  "completionCount": 85,
  "completionRate": 70.83,
  "prerequisites": "计算机基础知识",
  "learningGoals": "掌握Java基础语法和面向对象编程",
  "creatorId": "user123",
  "creatorName": "张老师",
  "status": "PUBLISHED",
  "isOfficial": true,
  "coverImageUrl": "https://example.com/cover.jpg",
  "tags": "Java,编程,基础",
  "tagList": ["Java", "编程", "基础"],
  "createdAt": "2024-01-15T10:30:00",
  "updatedAt": "2024-01-20T14:20:00",
  "createdBy": "admin",
  "updatedBy": "admin"
}
```

**字段说明**:
- `difficultyLevel`: 难度级别 (BEGINNER:初级, INTERMEDIATE:中级, ADVANCED:高级, EXPERT:专家级)
- `status`: 课程状态 (DRAFT:草稿, PUBLISHED:已发布, ARCHIVED:已归档)
- `totalHours`: 预估总学习时长（小时）
- `completionRate`: 平均完成率（百分比）

### 通用响应格式

#### Result<T>
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-20T10:30:00",
  "requestId": "req-123456",
  "details": null
}
```

#### PageResult<T>
```json
{
  "records": [],
  "pagination": {
    "totalElements": 100,
    "totalPages": 10,
    "currentPage": 0,
    "pageSize": 10,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

## API 接口

### 1. 课程管理

#### 1.1 获取学习课程列表（分页）

**方法**: `getLearningCourseList(GetLearningCourseListRequest request)`

**描述**: 获取学习课程列表，支持分页和多种过滤条件。

**请求参数**:
```json
{
  "pageRequest": {
    "page": 0,
    "size": 10
  },
  "category": "编程语言",
  "difficultyLevel": "BEGINNER",
  "status": "PUBLISHED",
  "isOfficial": true,
  "creatorId": "user123",
  "search": "Java",
  "sortBy": "createdAt",
  "sortDirection": "DESC"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "Java基础编程",
        "description": "Java编程语言基础知识学习课程",
        "category": "编程语言",
        "difficultyLevel": "BEGINNER",
        "totalHours": 40.5,
        "resourceCount": 15,
        "enrolledCount": 120,
        "completionCount": 85,
        "completionRate": 70.83,
        "creatorId": "user123",
        "creatorName": "张老师",
        "status": "PUBLISHED",
        "isOfficial": true,
        "createdAt": "2024-01-15T10:30:00",
        "updatedAt": "2024-01-20T14:20:00"
      }
    ],
    "pagination": {
      "totalElements": 50,
      "totalPages": 5,
      "currentPage": 0,
      "pageSize": 10,
      "hasNext": true,
      "hasPrevious": false
    }
  },
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 1.2 根据ID获取学习课程详情

**方法**: `getLearningCourseById(Long id)`

**描述**: 根据课程ID获取课程详细信息。

**请求参数**:
- `id` (Long): 课程ID

**请求示例**:
```java
Long courseId = 1L;
Result<LearningCourseDTO> result = learningCourseService.getLearningCourseById(courseId);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "Java基础编程",
    "description": "Java编程语言基础知识学习课程，涵盖语法基础、面向对象编程、集合框架等核心内容",
    "category": "编程语言",
    "difficultyLevel": "BEGINNER",
    "totalHours": 40.5,
    "resourceCount": 15,
    "enrolledCount": 120,
    "completionCount": 85,
    "completionRate": 70.83,
    "prerequisites": "计算机基础知识",
    "learningGoals": "掌握Java基础语法和面向对象编程",
    "creatorId": "user123",
    "creatorName": "张老师",
    "status": "PUBLISHED",
    "isOfficial": true,
    "coverImageUrl": "https://example.com/cover.jpg",
    "tags": "Java,编程,基础",
    "tagList": ["Java", "编程", "基础"],
    "createdAt": "2024-01-15T10:30:00",
    "updatedAt": "2024-01-20T14:20:00",
    "createdBy": "admin",
    "updatedBy": "admin"
  },
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 1.3 创建学习课程

**方法**: `createLearningCourse(LearningCourseDTO course)`

**描述**: 创建新的学习课程。

**请求参数**:
```json
{
  "name": "Python数据分析",
  "description": "使用Python进行数据分析的完整课程",
  "category": "数据科学",
  "difficultyLevel": "INTERMEDIATE",
  "totalHours": 60.0,
  "prerequisites": "Python基础知识",
  "learningGoals": "掌握Python数据分析库的使用",
  "creatorId": "user456",
  "status": "DRAFT",
  "isOfficial": false,
  "coverImageUrl": "https://example.com/python-cover.jpg",
  "tags": "Python,数据分析,pandas"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "课程创建成功",
  "data": {
    "id": 2,
    "name": "Python数据分析",
    "description": "使用Python进行数据分析的完整课程",
    "category": "数据科学",
    "difficultyLevel": "INTERMEDIATE",
    "totalHours": 60.0,
    "resourceCount": 0,
    "enrolledCount": 0,
    "completionCount": 0,
    "completionRate": 0.0,
    "prerequisites": "Python基础知识",
    "learningGoals": "掌握Python数据分析库的使用",
    "creatorId": "user456",
    "status": "DRAFT",
    "isOfficial": false,
    "coverImageUrl": "https://example.com/python-cover.jpg",
    "tags": "Python,数据分析,pandas",
    "createdAt": "2024-01-20T10:30:00",
    "updatedAt": "2024-01-20T10:30:00",
    "createdBy": "user456",
    "updatedBy": "user456"
  },
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 1.4 更新学习课程信息

**方法**: `updateLearningCourse(Long id, LearningCourseDTO course)`

**描述**: 更新指定课程的信息。

**请求参数**:
- `id` (Long): 课程ID
- `course` (LearningCourseDTO): 更新的课程信息

**请求示例**:
```json
{
  "name": "Java高级编程",
  "description": "Java高级特性和企业级开发实践",
  "difficultyLevel": "ADVANCED",
  "totalHours": 80.0,
  "prerequisites": "Java基础编程",
  "learningGoals": "掌握Java高级特性和企业级开发技能"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "课程更新成功",
  "data": {
    "id": 1,
    "name": "Java高级编程",
    "description": "Java高级特性和企业级开发实践",
    "category": "编程语言",
    "difficultyLevel": "ADVANCED",
    "totalHours": 80.0,
    "resourceCount": 15,
    "enrolledCount": 120,
    "completionCount": 85,
    "completionRate": 70.83,
    "prerequisites": "Java基础编程",
    "learningGoals": "掌握Java高级特性和企业级开发技能",
    "creatorId": "user123",
    "creatorName": "张老师",
    "status": "PUBLISHED",
    "isOfficial": true,
    "updatedAt": "2024-01-20T15:30:00",
    "updatedBy": "admin"
  },
  "timestamp": "2024-01-20T15:30:00"
}
```

#### 1.5 删除学习课程

**方法**: `deleteLearningCourse(Long id)`

**描述**: 删除指定的学习课程。

**请求参数**:
- `id` (Long): 课程ID

**请求示例**:
```java
Long courseId = 1L;
Result<Void> result = learningCourseService.deleteLearningCourse(courseId);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "课程删除成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 1.6 更新课程状态

**方法**: `updateLearningCourseStatus(UpdateLearningCourseStatusRequest request)`

**描述**: 更新课程的发布状态。

**请求参数**:
```json
{
  "id": 1,
  "status": "PUBLISHED",
  "updatedBy": "admin"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "课程状态更新成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

### 2. 课程搜索和过滤

#### 2.1 搜索学习课程

**方法**: `searchLearningCourses(SearchLearningCoursesRequest request)`

**描述**: 根据关键词和多种条件搜索课程。

**请求参数**:
```json
{
  "pageRequest": {
    "page": 0,
    "size": 20
  },
  "keyword": "Java编程",
  "categories": ["编程语言", "软件开发"],
  "difficultyLevels": ["BEGINNER", "INTERMEDIATE"],
  "tags": ["Java", "编程"],
  "isOfficial": null,
  "minHours": 10,
  "maxHours": 100,
  "sortBy": "enrolledCount",
  "sortDirection": "DESC"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "搜索成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "Java基础编程",
        "description": "Java编程语言基础知识学习课程",
        "category": "编程语言",
        "difficultyLevel": "BEGINNER",
        "totalHours": 40.5,
        "enrolledCount": 120,
        "completionRate": 70.83,
        "creatorName": "张老师",
        "status": "PUBLISHED",
        "isOfficial": true
      }
    ],
    "pagination": {
      "totalElements": 15,
      "totalPages": 1,
      "currentPage": 0,
      "pageSize": 20,
      "hasNext": false,
      "hasPrevious": false
    }
  },
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 2.2 根据分类获取课程列表

**方法**: `getLearningCoursesByCategory(String category, Integer limit)`

**描述**: 获取指定分类下的课程列表。

**请求参数**:
- `category` (String): 课程分类
- `limit` (Integer): 限制数量

**请求示例**:
```java
String category = "编程语言";
Integer limit = 10;
Result<List<LearningCourseDTO>> result = learningCourseService.getLearningCoursesByCategory(category, limit);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "Java基础编程",
      "description": "Java编程语言基础知识学习课程",
      "category": "编程语言",
      "difficultyLevel": "BEGINNER",
      "totalHours": 40.5,
      "enrolledCount": 120,
      "status": "PUBLISHED",
      "isOfficial": true
    },
    {
      "id": 3,
      "name": "JavaScript前端开发",
      "description": "现代JavaScript前端开发技术",
      "category": "编程语言",
      "difficultyLevel": "INTERMEDIATE",
      "totalHours": 50.0,
      "enrolledCount": 95,
      "status": "PUBLISHED",
      "isOfficial": false
    }
  ],
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 2.3 根据难度级别获取课程列表

**方法**: `getLearningCoursesByDifficulty(String difficultyLevel, Integer limit)`

**描述**: 获取指定难度级别的课程列表。

**请求参数**:
- `difficultyLevel` (String): 难度级别 (BEGINNER, INTERMEDIATE, ADVANCED, EXPERT)
- `limit` (Integer): 限制数量

**请求示例**:
```java
String difficultyLevel = "BEGINNER";
Integer limit = 5;
Result<List<LearningCourseDTO>> result = learningCourseService.getLearningCoursesByDifficulty(difficultyLevel, limit);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "Java基础编程",
      "category": "编程语言",
      "difficultyLevel": "BEGINNER",
      "totalHours": 40.5,
      "enrolledCount": 120,
      "creatorName": "张老师",
      "status": "PUBLISHED"
    }
  ],
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 2.4 根据创建者获取课程列表

**方法**: `getLearningCoursesByCreator(String creatorId, Integer limit)`

**描述**: 获取指定创建者的课程列表。

**请求参数**:
- `creatorId` (String): 创建者ID
- `limit` (Integer): 限制数量

**请求示例**:
```java
String creatorId = "user123";
Integer limit = 10;
Result<List<LearningCourseDTO>> result = learningCourseService.getLearningCoursesByCreator(creatorId, limit);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "Java基础编程",
      "category": "编程语言",
      "difficultyLevel": "BEGINNER",
      "totalHours": 40.5,
      "creatorId": "user123",
      "creatorName": "张老师",
      "status": "PUBLISHED"
    }
  ],
  "timestamp": "2024-01-20T10:30:00"
}
```

### 3. 课程统计和推荐

#### 3.1 获取热门课程列表

**方法**: `getPopularLearningCourses(Integer limit)`

**描述**: 获取热门课程列表，按报名人数排序。

**请求参数**:
- `limit` (Integer): 限制数量

**请求示例**:
```java
Integer limit = 10;
Result<List<LearningCourseDTO>> result = learningCourseService.getPopularLearningCourses(limit);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "Java基础编程",
      "category": "编程语言",
      "difficultyLevel": "BEGINNER",
      "totalHours": 40.5,
      "enrolledCount": 120,
      "completionRate": 70.83,
      "creatorName": "张老师",
      "status": "PUBLISHED",
      "isOfficial": true
    },
    {
      "id": 3,
      "name": "JavaScript前端开发",
      "category": "编程语言",
      "difficultyLevel": "INTERMEDIATE",
      "totalHours": 50.0,
      "enrolledCount": 95,
      "completionRate": 65.26,
      "creatorName": "李老师",
      "status": "PUBLISHED",
      "isOfficial": false
    }
  ],
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 3.2 获取推荐课程列表

**方法**: `getRecommendedLearningCourses(String userId, Integer limit)`

**描述**: 根据用户学习历史获取推荐课程列表。

**请求参数**:
- `userId` (String): 用户ID
- `limit` (Integer): 限制数量

**请求示例**:
```java
String userId = "user456";
Integer limit = 5;
Result<List<LearningCourseDTO>> result = learningCourseService.getRecommendedLearningCourses(userId, limit);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 4,
      "name": "Spring Boot实战",
      "category": "框架技术",
      "difficultyLevel": "INTERMEDIATE",
      "totalHours": 60.0,
      "enrolledCount": 80,
      "completionRate": 75.0,
      "creatorName": "王老师",
      "status": "PUBLISHED",
      "isOfficial": true
    }
  ],
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 3.3 获取最新课程列表

**方法**: `getLatestLearningCourses(Integer limit)`

**描述**: 获取最新发布的课程列表。

**请求参数**:
- `limit` (Integer): 限制数量

**请求示例**:
```java
Integer limit = 8;
Result<List<LearningCourseDTO>> result = learningCourseService.getLatestLearningCourses(limit);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 5,
      "name": "Vue.js 3.0 实战",
      "category": "前端框架",
      "difficultyLevel": "INTERMEDIATE",
      "totalHours": 45.0,
      "enrolledCount": 30,
      "creatorName": "赵老师",
      "status": "PUBLISHED",
      "createdAt": "2024-01-19T16:00:00"
    }
  ],
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 3.4 获取官方课程列表

**方法**: `getOfficialLearningCourses(Integer limit)`

**描述**: 获取官方认证的课程列表。

**请求参数**:
- `limit` (Integer): 限制数量

**请求示例**:
```java
Integer limit = 10;
Result<List<LearningCourseDTO>> result = learningCourseService.getOfficialLearningCourses(limit);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "Java基础编程",
      "category": "编程语言",
      "difficultyLevel": "BEGINNER",
      "totalHours": 40.5,
      "enrolledCount": 120,
      "completionRate": 70.83,
      "creatorName": "张老师",
      "status": "PUBLISHED",
      "isOfficial": true
    }
  ],
  "timestamp": "2024-01-20T10:30:00"
}
```

### 4. 课程统计

#### 4.1 增加课程报名人数

**方法**: `incrementEnrolledCount(Long courseId, String userId)`

**描述**: 用户报名课程时增加报名人数统计。

**请求参数**:
- `courseId` (Long): 课程ID
- `userId` (String): 用户ID

**请求示例**:
```java
Long courseId = 1L;
String userId = "user789";
Result<Void> result = learningCourseService.incrementEnrolledCount(courseId, userId);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "报名统计更新成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 4.2 增加课程完成人数

**方法**: `incrementCompletionCount(Long courseId, String userId)`

**描述**: 用户完成课程时增加完成人数统计。

**请求参数**:
- `courseId` (Long): 课程ID
- `userId` (String): 用户ID

**请求示例**:
```java
Long courseId = 1L;
String userId = "user789";
Result<Void> result = learningCourseService.incrementCompletionCount(courseId, userId);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "完成统计更新成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 4.3 更新课程资源数量

**方法**: `updateResourceCount(Long courseId, Integer resourceCount)`

**描述**: 更新课程包含的资源数量。

**请求参数**:
- `courseId` (Long): 课程ID
- `resourceCount` (Integer): 资源数量

**请求示例**:
```java
Long courseId = 1L;
Integer resourceCount = 20;
Result<Void> result = learningCourseService.updateResourceCount(courseId, resourceCount);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "资源数量更新成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 4.4 计算并更新课程完成率

**方法**: `updateCompletionRate(Long courseId)`

**描述**: 重新计算并更新课程的完成率。

**请求参数**:
- `courseId` (Long): 课程ID

**请求示例**:
```java
Long courseId = 1L;
Result<Void> result = learningCourseService.updateCompletionRate(courseId);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "完成率更新成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

### 5. 批量操作

#### 5.1 批量更新课程状态

**方法**: `batchUpdateStatus(List<Long> courseIds, String status, String updatedBy)`

**描述**: 批量更新多个课程的状态。

**请求参数**:
- `courseIds` (List<Long>): 课程ID列表
- `status` (String): 新状态
- `updatedBy` (String): 更新用户

**请求示例**:
```java
List<Long> courseIds = Arrays.asList(1L, 2L, 3L);
String status = "PUBLISHED";
String updatedBy = "admin";
Result<Void> result = learningCourseService.batchUpdateStatus(courseIds, status, updatedBy);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "批量状态更新成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 5.2 批量删除课程

**方法**: `batchDeleteLearningCourses(List<Long> courseIds)`

**描述**: 批量删除多个学习课程。

**请求参数**:
- `courseIds` (List<Long>): 课程ID列表

**请求示例**:
```java
List<Long> courseIds = Arrays.asList(10L, 11L, 12L);
Result<Void> result = learningCourseService.batchDeleteLearningCourses(courseIds);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "批量删除成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| SUCCESS | 操作成功 |
| INVALID_PARAM | 参数无效 |
| COURSE_NOT_FOUND | 课程不存在 |
| COURSE_ALREADY_EXISTS | 课程已存在 |
| PERMISSION_DENIED | 权限不足 |
| SYSTEM_ERROR | 系统错误 |

## 使用示例

### Java 调用示例

```java
@Service
public class CourseManagementService {
    
    @Autowired
    private LearningCourseService learningCourseService;
    
    /**
     * 创建课程示例
     */
    public void createCourseExample() {
        LearningCourseDTO course = new LearningCourseDTO();
        course.setName("Spring Boot微服务实战");
        course.setDescription("基于Spring Boot构建微服务架构的实战课程");
        course.setCategory("微服务架构");
        course.setDifficultyLevel("ADVANCED");
        course.setTotalHours(new BigDecimal("80.0"));
        course.setCreatorId("instructor001");
        course.setStatus("DRAFT");
        course.setIsOfficial(false);
        course.setPrerequisites("Spring Boot基础知识");
        course.setLearningGoals("掌握微服务架构设计和实现");
        
        Result<LearningCourseDTO> result = learningCourseService.createLearningCourse(course);
        
        if (result.isSuccess()) {
            System.out.println("课程创建成功，ID: " + result.getData().getId());
        } else {
            System.out.println("课程创建失败: " + result.getMessage());
        }
    }
    
    /**
     * 分页查询课程示例
     */
    public void getCourseListExample() {
        GetLearningCourseListRequest request = new GetLearningCourseListRequest();
        
        // 设置分页参数
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);
        request.setPageRequest(pageRequest);
        
        // 设置过滤条件
        request.setCategory("编程语言");
        request.setDifficultyLevel("BEGINNER");
        request.setStatus("PUBLISHED");
        request.setSearch("Java");
        request.setSortBy("enrolledCount");
        request.setSortDirection("DESC");
        
        Result<PageResult<LearningCourseDTO>> result = learningCourseService.getLearningCourseList(request);
        
        if (result.isSuccess()) {
            PageResult<LearningCourseDTO> pageResult = result.getData();
            System.out.println("总记录数: " + pageResult.getPagination().getTotalElements());
            System.out.println("课程列表: " + pageResult.getRecords().size() + " 条");
        }
    }
    
    /**
     * 搜索课程示例
     */
    public void searchCoursesExample() {
        SearchLearningCoursesRequest request = new SearchLearningCoursesRequest();
        
        // 设置分页参数
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(20);
        request.setPageRequest(pageRequest);
        
        // 设置搜索条件
        request.setKeyword("Java编程");
        request.setCategories(Arrays.asList("编程语言", "软件开发"));
        request.setDifficultyLevels(Arrays.asList("BEGINNER", "INTERMEDIATE"));
        request.setTags(Arrays.asList("Java", "编程"));
        request.setMinHours(10);
        request.setMaxHours(100);
        request.setSortBy("enrolledCount");
        request.setSortDirection("DESC");
        
        Result<PageResult<LearningCourseDTO>> result = learningCourseService.searchLearningCourses(request);
        
        if (result.isSuccess()) {
            System.out.println("搜索到 " + result.getData().getRecords().size() + " 门课程");
        }
    }
    
    /**
     * 批量操作示例
     */
    public void batchOperationExample() {
        // 批量更新状态
        List<Long> courseIds = Arrays.asList(1L, 2L, 3L);
        String status = "PUBLISHED";
        String updatedBy = "admin";
        
        Result<Void> updateResult = learningCourseService.batchUpdateStatus(courseIds, status, updatedBy);
        
        if (updateResult.isSuccess()) {
            System.out.println("批量状态更新成功");
        }
        
        // 批量删除
        List<Long> deleteIds = Arrays.asList(10L, 11L, 12L);
        Result<Void> deleteResult = learningCourseService.batchDeleteLearningCourses(deleteIds);
        
        if (deleteResult.isSuccess()) {
            System.out.println("批量删除成功");
        }
    }
}
```

### Spring Boot Controller 示例

```java
@RestController
@RequestMapping("/api/courses")
@Api(tags = "学习课程管理")
public class LearningCourseController {
    
    @Autowired
    private LearningCourseService learningCourseService;
    
    @PostMapping
    @ApiOperation("创建课程")
    public Result<LearningCourseDTO> createCourse(@RequestBody @Valid LearningCourseDTO course) {
        return learningCourseService.createLearningCourse(course);
    }
    
    @GetMapping("/{id}")
    @ApiOperation("获取课程详情")
    public Result<LearningCourseDTO> getCourse(@PathVariable Long id) {
        return learningCourseService.getLearningCourseById(id);
    }
    
    @PutMapping("/{id}")
    @ApiOperation("更新课程")
    public Result<LearningCourseDTO> updateCourse(@PathVariable Long id, 
                                                  @RequestBody @Valid LearningCourseDTO course) {
        return learningCourseService.updateLearningCourse(id, course);
    }
    
    @DeleteMapping("/{id}")
    @ApiOperation("删除课程")
    public Result<Void> deleteCourse(@PathVariable Long id) {
        return learningCourseService.deleteLearningCourse(id);
    }
    
    @PostMapping("/list")
    @ApiOperation("获取课程列表")
    public Result<PageResult<LearningCourseDTO>> getCourseList(@RequestBody GetLearningCourseListRequest request) {
        return learningCourseService.getLearningCourseList(request);
    }
    
    @PostMapping("/search")
    @ApiOperation("搜索课程")
    public Result<PageResult<LearningCourseDTO>> searchCourses(@RequestBody SearchLearningCoursesRequest request) {
        return learningCourseService.searchLearningCourses(request);
    }
    
    @GetMapping("/popular")
    @ApiOperation("获取热门课程")
    public Result<List<LearningCourseDTO>> getPopularCourses(@RequestParam(defaultValue = "10") Integer limit) {
        return learningCourseService.getPopularLearningCourses(limit);
    }
    
    @GetMapping("/recommended")
    @ApiOperation("获取推荐课程")
    public Result<List<LearningCourseDTO>> getRecommendedCourses(@RequestParam String userId,
                                                                 @RequestParam(defaultValue = "5") Integer limit) {
        return learningCourseService.getRecommendedLearningCourses(userId, limit);
    }
    
    @PutMapping("/status")
    @ApiOperation("更新课程状态")
    public Result<Void> updateCourseStatus(@RequestBody @Valid UpdateLearningCourseStatusRequest request) {
        return learningCourseService.updateLearningCourseStatus(request);
    }
    
    @PostMapping("/batch/status")
    @ApiOperation("批量更新状态")
    public Result<Void> batchUpdateStatus(@RequestParam List<Long> courseIds,
                                          @RequestParam String status,
                                          @RequestParam String updatedBy) {
        return learningCourseService.batchUpdateStatus(courseIds, status, updatedBy);
    }
}
```

## 注意事项

1. **参数验证**: 所有请求参数都应进行有效性验证，特别是必填字段和数据格式。

2. **权限控制**: 课程的创建、更新、删除操作需要进行权限验证，确保只有授权用户才能执行相应操作。

3. **状态管理**: 课程状态变更需要遵循业务规则，例如只有草稿状态的课程才能删除。

4. **数据一致性**: 在进行批量操作时，需要保证数据的一致性，建议使用事务处理。

5. **性能优化**: 对于大量数据的查询操作，建议使用分页查询和适当的索引优化。

6. **缓存策略**: 对于热门课程、推荐课程等频繁查询的数据，可以考虑使用缓存提高性能。

7. **异常处理**: 所有接口都应该有完善的异常处理机制，返回明确的错误信息。

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0.0 | 2024-01-20 | 初始版本，包含完整的课程管理功能 |

---

**文档生成时间**: 2024-01-20  
**最后更新**: 2024-01-20
# LearningCourseService API 文档

## 概述

LearningCourseService 是学习课程管理服务接口，提供完整的学习课程管理功能，包括课程的CRUD操作、状态管理、搜索过滤、统计分析和推荐功能。

**版本**: 1.0.0  
**作者**: AI Community Development Team

## 功能特性

- 课程的CRUD操作
- 课程状态管理
- 课程搜索和过滤
- 课程统计和分析
- 课程推荐功能
- 批量操作支持

## 数据模型

### LearningCourseDTO

学习课程数据传输对象，包含课程的完整信息。

```json
{
  "id": 1,
  "name": "Java基础编程",
  "description": "Java编程语言基础知识学习课程",
  "category": "编程语言",
  "difficultyLevel": "BEGINNER",
  "totalHours": 40.5,
  "resourceCount": 15,
  "enrolledCount": 120,
  "completionCount": 85,
  "completionRate": 70.83,
  "prerequisites": "计算机基础知识",
  "learningGoals": "掌握Java基础语法和面向对象编程",
  "creatorId": "user123",
  "creatorName": "张老师",
  "status": "PUBLISHED",
  "isOfficial": true,
  "coverImageUrl": "https://example.com/cover.jpg",
  "tags": "Java,编程,基础",
  "tagList": ["Java", "编程", "基础"],
  "createdAt": "2024-01-15T10:30:00",
  "updatedAt": "2024-01-20T14:20:00",
  "createdBy": "admin",
  "updatedBy": "admin"
}
```

**字段说明**:
- `difficultyLevel`: 难度级别 (BEGINNER:初级, INTERMEDIATE:中级, ADVANCED:高级, EXPERT:专家级)
- `status`: 课程状态 (DRAFT:草稿, PUBLISHED:已发布, ARCHIVED:已归档)
- `totalHours`: 预估总学习时长（小时）
- `completionRate`: 平均完成率（百分比）

### 通用响应格式

#### Result<T>
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-20T10:30:00",
  "requestId": "req-123456",
  "details": null
}
```

#### PageResult<T>
```json
{
  "records": [],
  "pagination": {
    "totalElements": 100,
    "totalPages": 10,
    "currentPage": 0,
    "pageSize": 10,
    "hasNext": true,
    "hasPrevious": false
  }
}
```

## API 接口

### 1. 课程管理

#### 1.1 获取学习课程列表（分页）

**方法**: `getLearningCourseList(GetLearningCourseListRequest request)`

**描述**: 获取学习课程列表，支持分页和多种过滤条件。

**请求参数**:
```json
{
  "pageRequest": {
    "page": 0,
    "size": 10
  },
  "category": "编程语言",
  "difficultyLevel": "BEGINNER",
  "status": "PUBLISHED",
  "isOfficial": true,
  "creatorId": "user123",
  "search": "Java",
  "sortBy": "createdAt",
  "sortDirection": "DESC"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "Java基础编程",
        "description": "Java编程语言基础知识学习课程",
        "category": "编程语言",
        "difficultyLevel": "BEGINNER",
        "totalHours": 40.5,
        "resourceCount": 15,
        "enrolledCount": 120,
        "completionCount": 85,
        "completionRate": 70.83,
        "creatorId": "user123",
        "creatorName": "张老师",
        "status": "PUBLISHED",
        "isOfficial": true,
        "createdAt": "2024-01-15T10:30:00",
        "updatedAt": "2024-01-20T14:20:00"
      }
    ],
    "pagination": {
      "totalElements": 50,
      "totalPages": 5,
      "currentPage": 0,
      "pageSize": 10,
      "hasNext": true,
      "hasPrevious": false
    }
  },
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 1.2 根据ID获取学习课程详情

**方法**: `getLearningCourseById(Long id)`

**描述**: 根据课程ID获取课程详细信息。

**请求参数**:
- `id` (Long): 课程ID

**请求示例**:
```java
Long courseId = 1L;
Result<LearningCourseDTO> result = learningCourseService.getLearningCourseById(courseId);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": {
    "id": 1,
    "name": "Java基础编程",
    "description": "Java编程语言基础知识学习课程，涵盖语法基础、面向对象编程、集合框架等核心内容",
    "category": "编程语言",
    "difficultyLevel": "BEGINNER",
    "totalHours": 40.5,
    "resourceCount": 15,
    "enrolledCount": 120,
    "completionCount": 85,
    "completionRate": 70.83,
    "prerequisites": "计算机基础知识",
    "learningGoals": "掌握Java基础语法和面向对象编程",
    "creatorId": "user123",
    "creatorName": "张老师",
    "status": "PUBLISHED",
    "isOfficial": true,
    "coverImageUrl": "https://example.com/cover.jpg",
    "tags": "Java,编程,基础",
    "tagList": ["Java", "编程", "基础"],
    "createdAt": "2024-01-15T10:30:00",
    "updatedAt": "2024-01-20T14:20:00",
    "createdBy": "admin",
    "updatedBy": "admin"
  },
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 1.3 创建学习课程

**方法**: `createLearningCourse(LearningCourseDTO course)`

**描述**: 创建新的学习课程。

**请求参数**:
```json
{
  "name": "Python数据分析",
  "description": "使用Python进行数据分析的完整课程",
  "category": "数据科学",
  "difficultyLevel": "INTERMEDIATE",
  "totalHours": 60.0,
  "prerequisites": "Python基础知识",
  "learningGoals": "掌握Python数据分析库的使用",
  "creatorId": "user456",
  "status": "DRAFT",
  "isOfficial": false,
  "coverImageUrl": "https://example.com/python-cover.jpg",
  "tags": "Python,数据分析,pandas"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "课程创建成功",
  "data": {
    "id": 2,
    "name": "Python数据分析",
    "description": "使用Python进行数据分析的完整课程",
    "category": "数据科学",
    "difficultyLevel": "INTERMEDIATE",
    "totalHours": 60.0,
    "resourceCount": 0,
    "enrolledCount": 0,
    "completionCount": 0,
    "completionRate": 0.0,
    "prerequisites": "Python基础知识",
    "learningGoals": "掌握Python数据分析库的使用",
    "creatorId": "user456",
    "status": "DRAFT",
    "isOfficial": false,
    "coverImageUrl": "https://example.com/python-cover.jpg",
    "tags": "Python,数据分析,pandas",
    "createdAt": "2024-01-20T10:30:00",
    "updatedAt": "2024-01-20T10:30:00",
    "createdBy": "user456",
    "updatedBy": "user456"
  },
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 1.4 更新学习课程信息

**方法**: `updateLearningCourse(Long id, LearningCourseDTO course)`

**描述**: 更新指定课程的信息。

**请求参数**:
- `id` (Long): 课程ID
- `course` (LearningCourseDTO): 更新的课程信息

**请求示例**:
```json
{
  "name": "Java高级编程",
  "description": "Java高级特性和企业级开发实践",
  "difficultyLevel": "ADVANCED",
  "totalHours": 80.0,
  "prerequisites": "Java基础编程",
  "learningGoals": "掌握Java高级特性和企业级开发技能"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "课程更新成功",
  "data": {
    "id": 1,
    "name": "Java高级编程",
    "description": "Java高级特性和企业级开发实践",
    "category": "编程语言",
    "difficultyLevel": "ADVANCED",
    "totalHours": 80.0,
    "resourceCount": 15,
    "enrolledCount": 120,
    "completionCount": 85,
    "completionRate": 70.83,
    "prerequisites": "Java基础编程",
    "learningGoals": "掌握Java高级特性和企业级开发技能",
    "creatorId": "user123",
    "creatorName": "张老师",
    "status": "PUBLISHED",
    "isOfficial": true,
    "updatedAt": "2024-01-20T15:30:00",
    "updatedBy": "admin"
  },
  "timestamp": "2024-01-20T15:30:00"
}
```

#### 1.5 删除学习课程

**方法**: `deleteLearningCourse(Long id)`

**描述**: 删除指定的学习课程。

**请求参数**:
- `id` (Long): 课程ID

**请求示例**:
```java
Long courseId = 1L;
Result<Void> result = learningCourseService.deleteLearningCourse(courseId);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "课程删除成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 1.6 更新课程状态

**方法**: `updateLearningCourseStatus(UpdateLearningCourseStatusRequest request)`

**描述**: 更新课程的发布状态。

**请求参数**:
```json
{
  "id": 1,
  "status": "PUBLISHED",
  "updatedBy": "admin"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "课程状态更新成功",
  "data": null,
  "timestamp": "2024-01-20T10:30:00"
}
```

### 2. 课程搜索和过滤

#### 2.1 搜索学习课程

**方法**: `searchLearningCourses(SearchLearningCoursesRequest request)`

**描述**: 根据关键词和多种条件搜索课程。

**请求参数**:
```json
{
  "pageRequest": {
    "page": 0,
    "size": 20
  },
  "keyword": "Java编程",
  "categories": ["编程语言", "软件开发"],
  "difficultyLevels": ["BEGINNER", "INTERMEDIATE"],
  "tags": ["Java", "编程"],
  "isOfficial": null,
  "minHours": 10,
  "maxHours": 100,
  "sortBy": "enrolledCount",
  "sortDirection": "DESC"
}
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "搜索成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "Java基础编程",
        "description": "Java编程语言基础知识学习课程",
        "category": "编程语言",
        "difficultyLevel": "BEGINNER",
        "totalHours": 40.5,
        "enrolledCount": 120,
        "completionRate": 70.83,
        "creatorName": "张老师",
        "status": "PUBLISHED",
        "isOfficial": true
      }
    ],
    "pagination": {
      "totalElements": 15,
      "totalPages": 1,
      "currentPage": 0,
      "pageSize": 20,
      "hasNext": false,
      "hasPrevious": false
    }
  },
  "timestamp": "2024-01-20T10:30:00"
}
```

#### 2.2 根据分类获取课程列表

**方法**: `getLearningCoursesByCategory(String category, Integer limit)`

**描述**: 获取指定分类下的课程列表。

**请求参数**:
- `category` (String): 课程分类
- `limit` (Integer): 限制数量

**请求示例**:
```java
String category = "编程语言";
Integer limit = 10;
Result<List<LearningCourseDTO>> result = learningCourseService.getLearningCoursesByCategory(category, limit);
```

**响应示例**:
```json
{
  "code": "SUCCESS",
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "name": "Java基础编
