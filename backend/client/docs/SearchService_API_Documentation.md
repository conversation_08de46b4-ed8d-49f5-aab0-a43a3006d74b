# SearchService API 文档

## 接口概述

**接口名称**: SearchService  
**包路径**: com.jdl.aic.core.service.client.service.SearchService  
**功能描述**: 搜索服务接口，提供全文搜索功能，包括全文搜索和高级搜索、搜索建议和自动补全、搜索索引管理、搜索统计和分析、个性化搜索推荐等。  
**版本**: 1.0.0  
**作者**: AI Community Development Team  

## 数据模型

### SearchResultDTO
搜索结果数据传输对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| items | List<SearchItemDTO> | 搜索结果项列表 |
| total | Long | 总结果数量 |
| pageNum | Integer | 当前页码 |
| pageSize | Integer | 每页大小 |
| searchTime | Long | 搜索耗时（毫秒） |
| suggestions | List<String> | 搜索建议 |
| facets | Map<String, Object> | 分面搜索结果 |

### SearchFilterDTO
搜索过滤条件数据传输对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| contentTypes | List<String> | 内容类型过滤 |
| dateRange | Map<String, String> | 日期范围过滤 |
| tags | List<String> | 标签过滤 |
| categories | List<Long> | 分类过滤 |
| authors | List<String> | 作者过滤 |
| customFilters | Map<String, Object> | 自定义过滤条件 |

## API 接口列表

### 1. 全文搜索

#### 1.1 全文搜索

**方法名**: `search`  
**描述**: 执行全文搜索，支持分页和过滤条件，可进行个性化搜索  

**请求参数**:
- `keyword` (String): 搜索关键词
- `pageRequest` (PageRequest): 分页请求参数
- `filters` (SearchFilterDTO, 可选): 搜索过滤条件
- `userId` (Long, 可选): 搜索用户ID（用于个性化）

**返回值**: `Result<SearchResultDTO>`

**调用示例**:
```java
// 基本搜索
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

SearchFilterDTO filters = new SearchFilterDTO();
filters.setContentTypes(Arrays.asList("knowledge", "solution"));
filters.setTags(Arrays.asList("Java", "Spring"));

Result<SearchResultDTO> result = searchService.search(
    "Spring Boot教程", 
    pageRequest, 
    filters, 
    1001L  // 用户ID
);

if (result.isSuccess()) {
    SearchResultDTO searchResult = result.getData();
    System.out.println("找到 " + searchResult.getTotal() + " 条结果");
    System.out.println("搜索耗时: " + searchResult.getSearchTime() + "ms");
    
    for (SearchItemDTO item : searchResult.getItems()) {
        System.out.println("标题: " + item.getTitle());
        System.out.println("摘要: " + item.getSummary());
        System.out.println("评分: " + item.getScore());
    }
}
```

#### 1.2 高级搜索

**方法名**: `advancedSearch`  
**描述**: 执行高级搜索，支持复杂的搜索查询条件  

**请求参数**:
- `searchQuery` (Object): 复杂搜索查询对象
- `pageRequest` (PageRequest): 分页请求参数
- `userId` (Long, 可选): 搜索用户ID

**返回值**: `Result<SearchResultDTO>`

**调用示例**:
```java
// 构建高级搜索查询
Map<String, Object> searchQuery = new HashMap<>();
searchQuery.put("must", Arrays.asList(
    Map.of("field", "title", "value", "Spring"),
    Map.of("field", "content", "value", "微服务")
));
searchQuery.put("should", Arrays.asList(
    Map.of("field", "tags", "value", "架构")
));
searchQuery.put("filter", Map.of(
    "dateRange", Map.of("from", "2024-01-01", "to", "2024-12-31")
));

PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(10);

Result<SearchResultDTO> result = searchService.advancedSearch(
    searchQuery, 
    pageRequest, 
    1001L
);

if (result.isSuccess()) {
    SearchResultDTO searchResult = result.getData();
    System.out.println("高级搜索找到 " + searchResult.getTotal() + " 条结果");
}
```

#### 1.3 按内容类型搜索

**方法名**: `searchByContentType`  
**描述**: 在指定内容类型中进行搜索  

**请求参数**:
- `keyword` (String): 搜索关键词
- `contentType` (String): 内容类型（knowledge, solution, learning_resource, user）
- `pageRequest` (PageRequest): 分页请求参数
- `filters` (SearchFilterDTO, 可选): 过滤条件

**返回值**: `Result<SearchResultDTO>`

**调用示例**:
```java
// 只在知识库中搜索
Result<SearchResultDTO> result = searchService.searchByContentType(
    "机器学习算法",
    "knowledge",
    pageRequest,
    null
);

if (result.isSuccess()) {
    System.out.println("在知识库中找到 " + result.getData().getTotal() + " 条结果");
}
```

#### 1.4 相似内容搜索

**方法名**: `findSimilarContent`  
**描述**: 根据指定内容查找相似的内容  

**请求参数**:
- `contentType` (String): 内容类型
- `contentId` (Long): 内容ID
- `pageRequest` (PageRequest): 分页请求参数

**返回值**: `Result<SearchResultDTO>`

**调用示例**:
```java
// 查找相似的知识内容
Result<SearchResultDTO> result = searchService.findSimilarContent(
    "knowledge",
    12345L,
    pageRequest
);

if (result.isSuccess()) {
    System.out.println("找到 " + result.getData().getTotal() + " 条相似内容");
}
```

#### 1.5 模糊搜索

**方法名**: `fuzzySearch`  
**描述**: 执行模糊搜索，容忍拼写错误  

**请求参数**:
- `keyword` (String): 搜索关键词
- `pageRequest` (PageRequest): 分页请求参数
- `fuzzyLevel` (Integer): 模糊程度（0-2）

**返回值**: `Result<SearchResultDTO>`

**调用示例**:
```java
// 模糊搜索，容忍2个字符的差异
Result<SearchResultDTO> result = searchService.fuzzySearch(
    "Sprng Boot",  // 故意拼错
    pageRequest,
    2
);

if (result.isSuccess()) {
    System.out.println("模糊搜索找到 " + result.getData().getTotal() + " 条结果");
}
```

### 2. 搜索建议和自动补全

#### 2.1 获取搜索建议

**方法名**: `getSearchSuggestions`  
**描述**: 根据输入关键词获取搜索建议  

**请求参数**:
- `keyword` (String): 输入关键词
- `maxSuggestions` (Integer): 最大建议数量
- `contentType` (String, 可选): 内容类型过滤

**返回值**: `Result<List<String>>`

**调用示例**:
```java
Result<List<String>> result = searchService.getSearchSuggestions(
    "Spring",
    10,
    "knowledge"
);

if (result.isSuccess()) {
    List<String> suggestions = result.getData();
    System.out.println("搜索建议:");
    for (String suggestion : suggestions) {
        System.out.println("- " + suggestion);
    }
}
```

#### 2.2 获取自动补全建议

**方法名**: `getAutoCompletions`  
**描述**: 根据输入前缀获取自动补全建议  

**请求参数**:
- `prefix` (String): 输入前缀
- `maxCompletions` (Integer): 最大补全数量

**返回值**: `Result<List<String>>`

**调用示例**:
```java
Result<List<String>> result = searchService.getAutoCompletions("Spr", 5);

if (result.isSuccess()) {
    List<String> completions = result.getData();
    System.out.println("自动补全建议:");
    for (String completion : completions) {
        System.out.println("- " + completion);
    }
}
```

#### 2.3 获取热门搜索关键词

**方法名**: `getPopularKeywords`  
**描述**: 获取热门搜索关键词列表  

**请求参数**:
- `maxKeywords` (Integer): 最大关键词数量
- `days` (Integer): 统计天数
- `contentType` (String, 可选): 内容类型过滤

**返回值**: `Result<List<Object>>`

**调用示例**:
```java
Result<List<Object>> result = searchService.getPopularKeywords(20, 7, null);

if (result.isSuccess()) {
    List<Object> keywords = result.getData();
    System.out.println("过去7天热门搜索关键词:");
    for (Object keyword : keywords) {
        System.out.println("- " + keyword);
    }
}
```

#### 2.4 获取用户搜索历史

**方法名**: `getUserSearchHistory`  
**描述**: 获取指定用户的搜索历史  

**请求参数**:
- `userId` (Long): 用户ID
- `maxHistory` (Integer): 最大历史数量

**返回值**: `Result<List<String>>`

**调用示例**:
```java
Result<List<String>> result = searchService.getUserSearchHistory(1001L, 10);

if (result.isSuccess()) {
    List<String> history = result.getData();
    System.out.println("用户搜索历史:");
    for (String keyword : history) {
        System.out.println("- " + keyword);
    }
}
```

#### 2.5 清除用户搜索历史

**方法名**: `clearUserSearchHistory`  
**描述**: 清除指定用户的搜索历史  

**请求参数**:
- `userId` (Long): 用户ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = searchService.clearUserSearchHistory(1001L);

if (result.isSuccess()) {
    System.out.println("用户搜索历史已清除");
}
```

### 3. 搜索索引管理

#### 3.1 重建搜索索引

**方法名**: `rebuildSearchIndex`  
**描述**: 重建搜索索引，可指定内容类型或重建全部索引  

**请求参数**:
- `contentType` (String, 可选): 内容类型（null表示全部）
- `async` (Boolean): 是否异步执行

**返回值**: `Result<Object>`

**调用示例**:
```java
// 异步重建知识库索引
Result<Object> result = searchService.rebuildSearchIndex("knowledge", true);

if (result.isSuccess()) {
    System.out.println("索引重建任务已启动");
    Object taskInfo = result.getData();
    System.out.println("任务信息: " + taskInfo);
}
```

#### 3.2 增量更新搜索索引

**方法名**: `updateSearchIndex`  
**描述**: 增量更新搜索索引  

**请求参数**:
- `contentType` (String): 内容类型
- `contentId` (Long): 内容ID
- `operation` (String): 操作类型（CREATE, UPDATE, DELETE）

**返回值**: `Result<Void>`

**调用示例**:
```java
// 更新知识内容的索引
Result<Void> result = searchService.updateSearchIndex(
    "knowledge", 
    12345L, 
    "UPDATE"
);

if (result.isSuccess()) {
    System.out.println("索引更新成功");
}
```

#### 3.3 批量更新搜索索引

**方法名**: `batchUpdateSearchIndex`  
**描述**: 批量更新搜索索引  

**请求参数**:
- `updates` (List<Object>): 更新列表

**返回值**: `Result<Void>`

**调用示例**:
```java
List<Object> updates = Arrays.asList(
    Map.of("contentType", "knowledge", "contentId", 1L, "operation", "UPDATE"),
    Map.of("contentType", "solution", "contentId", 2L, "operation", "CREATE"),
    Map.of("contentType", "knowledge", "contentId", 3L, "operation", "DELETE")
);

Result<Void> result = searchService.batchUpdateSearchIndex(updates);

if (result.isSuccess()) {
    System.out.println("批量索引更新成功");
}
```

#### 3.4 获取索引状态

**方法名**: `getIndexStatus`  
**描述**: 获取搜索索引的状态信息  

**请求参数**:
- `contentType` (String, 可选): 内容类型过滤

**返回值**: `Result<Object>`

**调用示例**:
```java
Result<Object> result = searchService.getIndexStatus("knowledge");

if (result.isSuccess()) {
    Object status = result.getData();
    System.out.println("知识库索引状态: " + status);
}
```

#### 3.5 优化搜索索引

**方法名**: `optimizeSearchIndex`  
**描述**: 优化搜索索引以提高性能  

**请求参数**:
- `contentType` (String, 可选): 内容类型（null表示全部）

**返回值**: `Result<Object>`

**调用示例**:
```java
Result<Object> result = searchService.optimizeSearchIndex(null);

if (result.isSuccess()) {
    Object optimizeResult = result.getData();
    System.out.println("索引优化完成: " + optimizeResult);
}
```

### 4. 搜索统计和分析

#### 4.1 记录搜索行为

**方法名**: `recordSearchBehavior`  
**描述**: 记录用户的搜索行为，用于统计分析和个性化推荐  

**请求参数**:
- `keyword` (String): 搜索关键词
- `userId` (Long): 用户ID
- `resultCount` (Long): 结果数量
- `clickedItemId` (Long, 可选): 点击的结果项ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = searchService.recordSearchBehavior(
    "Spring Boot",
    1001L,
    25L,
    12345L  // 用户点击的结果项ID
);

if (result.isSuccess()) {
    System.out.println("搜索行为记录成功");
}
```

#### 4.2 获取搜索统计

**方法名**: `getSearchStatistics`  
**描述**: 获取指定时间范围内的搜索统计信息  

**请求参数**:
- `startDate` (String): 开始日期
- `endDate` (String): 结束日期
- `contentType` (String, 可选): 内容类型过滤

**返回值**: `Result<Object>`

**调用示例**:
```java
Result<Object> result = searchService.getSearchStatistics(
    "2024-01-01",
    "2024-01-31",
    "knowledge"
);

if (result.isSuccess()) {
    Object statistics = result.getData();
    System.out.println("搜索统计信息: " + statistics);
}
```

#### 4.3 获取搜索热力图数据

**方法名**: `getSearchHeatmapData`  
**描述**: 获取搜索热力图数据，用于可视化展示搜索活跃度  

**请求参数**:
- `days` (Integer): 统计天数

**返回值**: `Result<Object>`

**调用示例**:
```java
Result<Object> result = searchService.getSearchHeatmapData(30);

if (result.isSuccess()) {
    Object heatmapData = result.getData();
    System.out.println("过去30天搜索热力图数据: " + heatmapData);
}
```

#### 4.4 获取无结果搜索关键词

**方法名**: `getNoResultKeywords`  
**描述**: 获取没有搜索结果的关键词列表，用于优化内容和索引  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求
- `days` (Integer): 统计天数

**返回值**: `Result<Object>`

**调用示例**:
```java
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

Result<Object> result = searchService.getNoResultKeywords(pageRequest, 7);

if (result.isSuccess()) {
    Object noResultKeywords = result.getData();
    System.out.println("过去7天无结果搜索关键词: " + noResultKeywords);
}
```

#### 4.5 分析搜索趋势

**方法名**: `analyzeSearchTrend`  
**描述**: 分析指定关键词的搜索趋势  

**请求参数**:
- `keyword` (String): 关键词
- `days` (Integer): 分析天数

**返回值**: `Result<Object>`

**调用示例**:
```java
Result<Object> result = searchService.analyzeSearchTrend("Spring Boot", 30);

if (result.isSuccess()) {
    Object trendData = result.getData();
    System.out.println("Spring Boot搜索趋势: " + trendData);
}
```

### 5. 个性化搜索

#### 5.1 获取个性化搜索推荐

**方法名**: `getPersonalizedSearchRecommendations`  
**描述**: 根据用户历史行为获取个性化搜索推荐  

**请求参数**:
- `userId` (Long): 用户ID
- `maxRecommendations` (Integer): 最大推荐数量

**返回值**: `Result<List<String>>`

**调用示例**:
```java
Result<List<String>> result = searchService.getPersonalizedSearchRecommendations(1001L, 10);

if (result.isSuccess()) {
    List<String> recommendations = result.getData();
    System.out.println("个性化搜索推荐:");
    for (String recommendation : recommendations) {
        System.out.println("- " + recommendation);
    }
}
```

#### 5.2 更新用户搜索偏好

**方法名**: `updateUserSearchPreferences`  
**描述**: 更新用户的搜索偏好设置  

**请求参数**:
- `userId` (Long): 用户ID
- `preferences` (Map<String, Object>): 搜索偏好设置

**返回值**: `Result<Void>`

**调用示例**:
```java
Map<String, Object> preferences = new HashMap<>();
preferences.put("preferredContentTypes", Arrays.asList("knowledge", "solution"));
preferences.put("preferredLanguage", "zh-CN");
preferences.put("searchResultsPerPage", 20);
preferences.put("enablePersonalization", true);

Result<Void> result = searchService.updateUserSearchPreferences(1001L, preferences);

if (result.isSuccess()) {
    System.out.println("用户搜索偏好更新成功");
}
```

#### 5.3 获取用户搜索偏好

**方法名**: `getUserSearchPreferences`  
**描述**: 获取用户的搜索偏好设置  

**请求参数**:
- `userId` (Long): 用户ID

**返回值**: `Result<Map<String, Object>>`

**调用示例**:
```java
Result<Map<String, Object>> result = searchService.getUserSearchPreferences(1001L);

if (result.isSuccess()) {
    Map<String, Object> preferences = result.getData();
    System.out.println("用户搜索偏好:");
    for (Map.Entry<String, Object> entry : preferences.entrySet()) {
        System.out.println("- " + entry.getKey() + ": " + entry.getValue());
    }
}
```

#### 5.4 基于用户行为的智能搜索

**方法名**: `intelligentSearch`  
**描述**: 基于用户历史行为和偏好进行智能搜索  

**请求参数**:
- `keyword` (String): 搜索关键词
- `userId` (Long): 用户ID
- `pageRequest` (PageRequest): 分页请求

**返回值**: `Result<SearchResultDTO>`

**调用示例**:
```java
Result<SearchResultDTO> result = searchService.intelligentSearch(
    "微服务架构",
    1001L,
    pageRequest
);

if (result.isSuccess()) {
    SearchResultDTO searchResult = result.getData();
    System.out.println("智能搜索找到 " + searchResult.getTotal() + " 条个性化结果");
}
```

## 错误处理

所有接口都返回统一的 `Result<T>` 结果对象，包含以下信息：

- `success`: 操作是否成功
- `code`: 错误码
- `message`: 错误信息或成功信息
- `data`: 返回的数据

**常见错误码**:
- `200`: 操作成功
- `400`: 请求参数错误
- `404`: 搜索结果不存在
- `429`: 搜索频率限制
- `500`: 搜索服务内部错误
- `503`: 搜索服务不可用

**错误处理示例**:
```java
Result<SearchResultDTO> result = searchService.search(keyword, pageRequest, filters, userId);

if (result.isSuccess()) {
    // 处理成功情况
    SearchResultDTO searchResult = result.getData();
    // 业务逻辑处理
} else {
    // 处理错误情况
    System.err.println("搜索失败: " + result.getMessage());
    System.err.println("错误码: " + result.getCode());
    
    // 根据错误码进行不同处理
    switch (result.getCode()) {
        case 400:
            System.out.println("搜索参数错误，请检查输入");
            break;
        case 429:
            System.out.println("搜索过于频繁，请稍后再试");
            break;
        case 503:
            System.out.println("搜索服务暂时不可用");
            break;
        default:
            System.out.println("其他搜索错误");
    }
}
```

## 最佳实践

### 1. 搜索性能优化
- 合理设置分页大小，避免一次性返回过多结果
- 使用过滤条件缩小搜索范围
- 对于频繁搜索的关键词，考虑缓存搜索结果
- 使用异步方式进行索引重建和优化

### 2. 搜索体验优化
- 提供搜索建议和自动补全功能
- 记录和分析用户搜索行为
- 实现个性化搜索推荐
- 处理搜索无结果的情况

### 3. 索引管理
- 定期优化搜索索引
- 及时更新内容变更的索引
- 监控索引状态和性能
- 合理规划索引重建时间

### 4. 调用示例综合场景

```java
// 综合示例：完整的搜索功能实现
public class SearchServiceExample {
    
    private SearchService searchService;
    
    public void comprehensiveSearchExample() {
        try {
            Long userId = 1001L;
            String keyword = "Spring Boot微服务";
            
            // 1. 获取搜索建议
            Result<List<String>> suggestionsResult = searchService.getSearchSuggestions(
                keyword.substring(0, Math.min(keyword.length(), 5)), 5, null);
            
            if (suggestionsResult.isSuccess()) {
                System.out.println("搜索建议: " + suggestionsResult.getData());
            }
            
            // 2. 执行智能搜索
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPageNum(1);
            pageRequest.setPageSize(20);
            
            SearchFilterDTO filters = new SearchFilterDTO();
            filters.setContentTypes(Arrays.asList("knowledge", "solution"));
            
            Result<SearchResultDTO> searchResult = searchService.intelligentSearch(
                keyword, userId, pageRequest);
            
            if (searchResult.isSuccess()) {
                SearchResultDTO result = searchResult.getData();
                System.out.println("找到 " + result.getTotal() + " 条结果");
                
                // 3. 记录搜索行为
                searchService.recordSearchBehavior(
                    keyword, userId, result.getTotal(), null);
                
                // 4. 处理搜索结果
                for (SearchItemDTO item : result.getItems()) {
                    System.out.println("标题: " + item.getTitle());
                    System.out.println("摘要: " + item.getSummary());
                }
            }
            
            // 5. 获取个性化推荐
            Result<List<String>> recommendationsResult = 
                searchService.getPersonalizedSearchRecommendations(userId, 5);
            
            if (recommendationsResult.isSuccess()) {
                System.out.println("推荐搜索: " + recommendationsResult.getData());
            }
            
        } catch (Exception e) {
            System.err.println("搜索操作失败: " + e.getMessage());
        }
    }
    
    // 管理员搜索管理示例
    public void adminSearchManagementExample() {
        try {
            // 1. 获取搜索统计
            Result<Object> statisticsResult = searchService.getSearchStatistics(
                "2024-01-01", "2024-01-31", null);
            
            if (statisticsResult.isSuccess()) {
                System.out.println("搜索统计: " + statisticsResult.getData());
            }
            
            // 2. 获取无结果关键词
            PageRequest pageRequest = new PageRequest();
            pageRequest.setPageNum(1);
            pageRequest.setPageSize(10);
            
            Result<Object> noResultResult = searchService.getNoResultKeywords(
                pageRequest, 7);
            
            if (noResultResult.isSuccess()) {
                System.out.println("无结果关键词: " + noResultResult.getData());
            }
            
            // 3. 优化搜索索引
            Result<Object> optimizeResult = searchService.optimizeSearchIndex(null);
            
            if (optimizeResult.isSuccess()) {
                System.out.println("索引优化完成");
            }
            
        } catch (Exception e) {
            System.err.println("搜索管理操作失败: " + e.getMessage());
        }
    }
}
```

---

**文档版本**: 1.0.0  
**最后更新**: 2025-07-17  
**维护团队**: AI Community Development Team
