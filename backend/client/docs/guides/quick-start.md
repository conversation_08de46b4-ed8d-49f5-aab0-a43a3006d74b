# 快速开始指南

## 概述

本指南将帮助您快速上手 AI Community Core Service Client SDK，通过简单的步骤完成SDK的集成和基本使用。

## 前置条件

### 环境要求
- Java 17+
- Maven 3.6+
- Spring Boot 3.0+ (可选)

### 依赖配置
```xml
<dependency>
    <groupId>com.jdl.aic.core.service</groupId>
    <artifactId>client</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>
```

## 核心概念

### 概念1: AicCoreServiceClient
SDK的核心客户端类，提供所有服务接口的访问入口。通过该客户端可以获取各种服务实例。

### 概念2: Result包装器
所有服务方法都返回Result<T>类型，包含操作结果、状态码和错误信息，提供统一的结果处理方式。

### 概念3: 服务分类
SDK提供17个服务接口，分为基础服务、业务服务、支撑服务和专用服务四大类。

## 实施步骤

### 步骤1: 创建客户端实例

**目标**: 初始化SDK客户端，准备调用服务接口

**操作说明**:
1. 导入必要的包
2. 创建AicCoreServiceClient实例
3. 验证客户端是否正常工作

**代码示例**:

```java
import com.jdl.aic.core.service.client.AicCoreServiceClient;

// 创建客户端实例
AicCoreServiceClient client = new AicCoreServiceClient();

// 验证客户端
System.out.

        println("SDK客户端初始化成功");
```

**注意事项**:
- 客户端实例是线程安全的，建议作为单例使用
- 客户端会自动管理服务实例的生命周期

### 步骤2: 获取服务实例

**目标**: 从客户端获取需要的服务接口实例

**操作说明**:
1. 通过客户端的get{ServiceName}()方法获取服务
2. 服务实例可以重复使用
3. 不同服务实例之间相互独立

**代码示例**:
```java
// 获取用户管理服务
UserService userService = client.getUserService();

// 获取知识管理服务
KnowledgeService knowledgeService = client.getKnowledgeService();

// 获取数据分析服务
AnalyticsService analyticsService = client.getAnalyticsService();
```

**注意事项**:
- 服务实例获取是轻量级操作，可以频繁调用
- 建议在需要时获取，而不是预先获取所有服务

### 步骤3: 调用服务方法

**目标**: 使用服务接口完成具体的业务操作

**操作说明**:
1. 准备方法调用所需的参数
2. 调用服务方法
3. 处理返回的Result结果

**代码示例**:
```java
// 调用用户查询方法
Result<UserDTO> result = userService.getUserById(1001L);

// 处理结果
if (result.isSuccess()) {
    UserDTO user = result.getData();
    System.out.println("用户名: " + user.getUsername());
    System.out.println("显示名: " + user.getDisplayName());
} else {
    System.err.println("查询失败: " + result.getMessage());
}
```

**注意事项**:
- 始终检查Result的success状态
- 根据业务需要处理错误情况

## 完整示例

### 示例场景
演示如何使用SDK完成用户查询、知识内容获取和数据统计的完整流程

### 实现代码
```java
package com.example.aic.demo;

import com.jdl.aic.core.service.client.AicCoreServiceClient;
import com.jdl.aic.core.service.client.service.*;
import com.jdl.aic.core.service.client.common.*;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AI Community SDK 快速开始示例
 * 
 * 演示SDK的基本使用方法，包括用户查询、知识获取和数据统计
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class QuickStartExample {
    
    private static final Logger log = LoggerFactory.getLogger(QuickStartExample.class);
    
    private final AicCoreServiceClient client;
    
    public QuickStartExample() {
        this.client = new AicCoreServiceClient();
    }
    
    /**
     * 主要演示方法
     */
    public void demonstrateBasicUsage() {
        try {
            log.info("开始SDK基本功能演示...");
            
            // 步骤1: 用户信息查询
            UserDTO user = queryUserInfo();
            log.info("用户查询完成: {}", user.getDisplayName());
            
            // 步骤2: 知识内容获取
            PageResult<KnowledgeDTO> knowledgeList = getKnowledgeContent();
            log.info("知识内容获取完成，共{}条", knowledgeList.getTotal());
            
            // 步骤3: 数据统计分析
            Object statistics = getDataStatistics();
            log.info("数据统计完成: {}", statistics);
            
            log.info("SDK基本功能演示成功完成");
            
        } catch (Exception e) {
            log.error("SDK功能演示失败: {}", e.getMessage(), e);
            throw new RuntimeException("演示执行失败", e);
        }
    }
    
    /**
     * 步骤1: 查询用户信息
     */
    private UserDTO queryUserInfo() {
        UserService userService = client.getUserService();
        
        // 根据ID查询用户
        Result<UserDTO> result = userService.getUserById(1001L);
        
        // 处理结果
        return handleResult(result, "用户查询");
    }
    
    /**
     * 步骤2: 获取知识内容列表
     */
    private PageResult<KnowledgeDTO> getKnowledgeContent() {
        KnowledgeService knowledgeService = client.getKnowledgeService();
        
        // 构建分页请求
        PageRequest pageRequest = new PageRequest(1, 10);
        
        // 构建查询条件
        Map<String, Object> filters = new HashMap<>();
        filters.put("status", "published");
        filters.put("typeId", 1L);
        
        // 查询知识列表
        Result<PageResult<KnowledgeDTO>> result = knowledgeService.getKnowledgeList(
            pageRequest, filters
        );
        
        // 处理分页结果
        return handleResult(result, "知识内容查询");
    }
    
    /**
     * 步骤3: 获取数据统计
     */
    private Object getDataStatistics() {
        AnalyticsService analyticsService = client.getAnalyticsService();
        
        // 构建统计参数
        Map<String, Object> params = new HashMap<>();
        params.put("period", "month");
        params.put("startDate", "2025-01-01");
        params.put("endDate", "2025-07-15");
        
        // 获取知识内容统计
        Result<Object> result = analyticsService.getStatistics(
            "knowledge", "count", params
        );
        
        // 处理统计结果
        return handleResult(result, "数据统计");
    }
    
    /**
     * 统一结果处理
     */
    private <T> T handleResult(Result<T> result, String operation) {
        if (result.isSuccess()) {
            return result.getData();
        } else {
            String errorMsg = String.format("%s失败: %s", operation, result.getMessage());
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }
    }
    
    /**
     * 主方法 - 运行演示
     */
    public static void main(String[] args) {
        QuickStartExample example = new QuickStartExample();
        example.demonstrateBasicUsage();
    }
}
```

### 运行结果
```
2025-07-15 14:30:00 INFO  - 开始SDK基本功能演示...
2025-07-15 14:30:01 INFO  - 用户查询完成: 张三
2025-07-15 14:30:02 INFO  - 知识内容获取完成，共156条
2025-07-15 14:30:03 INFO  - 数据统计完成: {totalCount=156, monthlyGrowth=12.5}
2025-07-15 14:30:03 INFO  - SDK基本功能演示成功完成
```

## 最佳实践

### 1. 客户端实例管理

**建议**: 将客户端实例作为单例使用

**原因**: 客户端实例创建成本较高，且线程安全

**示例**:
```java
// ✅ 推荐做法 - 单例模式
@Component
public class AicServiceManager {
    private final AicCoreServiceClient client = new AicCoreServiceClient();
    
    public UserService getUserService() {
        return client.getUserService();
    }
}

// ❌ 不推荐做法 - 频繁创建实例
public void badExample() {
    AicCoreServiceClient client = new AicCoreServiceClient(); // 每次都创建新实例
    UserService userService = client.getUserService();
}
```

### 2. 错误处理策略

**建议**: 建立统一的错误处理机制

**原因**: 提高代码复用性和错误处理的一致性

**示例**:
```java
// ✅ 推荐做法 - 统一错误处理
public class ServiceHelper {
    public static <T> T handleResult(Result<T> result) {
        if (result.isSuccess()) {
            return result.getData();
        }
        throw new BusinessException(result.getCode(), result.getMessage());
    }
}

// 使用示例
UserDTO user = ServiceHelper.handleResult(
    userService.getUserById(1001L)
);
```

## 常见问题

### 问题1: 如何处理服务调用超时？

**症状**: 服务调用长时间无响应或抛出超时异常

**原因**: 网络延迟或服务端处理时间过长

**解决方案**:
1. 检查网络连接状态
2. 调整客户端超时配置
3. 实现重试机制

**预防措施**: 在生产环境中配置合适的超时时间和重试策略

### 问题2: Result结果为null怎么处理？

**症状**: 调用服务方法返回的Result对象为null

**原因**: 通常是客户端配置问题或服务端异常

**解决方案**:
1. 检查客户端初始化是否正确
2. 验证服务端是否正常运行
3. 查看详细的错误日志

**预防措施**: 在调用前进行null检查，并建立完善的日志记录

## 相关资源

### 相关指南
- [Maven 依赖配置](maven-setup.md) - 详细的依赖配置说明
- [Spring Boot 集成](spring-boot-integration.md) - Spring Boot项目集成指南

### 相关服务文档
- [UserService](../services/UserService.md) - 用户管理服务详细文档
- [KnowledgeService](../services/KnowledgeService.md) - 知识管理服务详细文档

### 示例代码
- [用户管理示例](../examples/user-management-examples.md) - 更多用户管理场景
- [知识管理示例](../examples/knowledge-management-examples.md) - 知识管理完整示例

## 总结

通过本快速开始指南，您已经学会了：

### 关键要点
1. 如何创建和配置SDK客户端
2. 如何获取和使用服务接口
3. 如何处理服务调用结果和错误

### 下一步
- 深入学习具体服务的详细功能
- 了解更多高级特性和最佳实践
- 根据业务需求选择合适的服务组合

---

## 反馈和支持

如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：

- **GitHub Issues**: [提交问题](https://github.com/ai-community/issues)
- **邮件支持**: <EMAIL>
- **技术论坛**: [内部技术论坛](https://forum.company.com)

---

*本文档基于 AI Community Core Service Client SDK v1.0.0 编写*
