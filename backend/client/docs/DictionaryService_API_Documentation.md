# DictionaryService API 文档

## 接口概述

**接口名称**: DictionaryService  
**包路径**: com.jdl.aic.core.service.client.service.DictionaryService  
**功能描述**: 字典管理服务接口，提供字典管理功能，包括字典项的CRUD操作、按类型分组管理字典项、字典项排序和状态管理、字典项搜索和批量操作。  
**版本**: 1.0.0  
**作者**: AI Community Development Team  

## 数据模型

### DictionaryDTO
字典项数据传输对象

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | Long | 字典项ID |
| key | String | 字典键 |
| value | String | 字典值 |
| type | String | 字典类型 |
| description | String | 描述 |
| sortOrder | Integer | 排序权重 |
| isActive | Boolean | 是否启用 |
| parentId | Long | 父级ID |
| level | Integer | 层级 |
| createTime | LocalDateTime | 创建时间 |
| updateTime | LocalDateTime | 更新时间 |
| createdBy | String | 创建人 |
| updatedBy | String | 更新人 |

## API 接口列表

## 一、字典项管理

### 1.1 获取字典项列表（分页）

**方法名**: `getDictionaryList`  
**描述**: 获取字典项列表，支持分页、类型过滤、状态过滤和搜索  

**请求参数**:
- `pageRequest` (PageRequest): 分页请求参数
- `type` (String, 可选): 字典类型过滤
- `isActive` (Boolean, 可选): 启用状态过滤
- `search` (String, 可选): 搜索关键词

**返回值**: `Result<PageResult<DictionaryDTO>>`

**调用示例**:
```java
// 基本分页查询
PageRequest pageRequest = new PageRequest();
pageRequest.setPageNum(1);
pageRequest.setPageSize(20);

Result<PageResult<DictionaryDTO>> result = dictionaryService.getDictionaryList(
    pageRequest, null, true, null);

if (result.isSuccess()) {
    PageResult<DictionaryDTO> pageResult = result.getData();
    System.out.println("总数: " + pageResult.getTotal());
    
    for (DictionaryDTO dict : pageResult.getList()) {
        System.out.println("字典项: " + dict.getKey() + " = " + dict.getValue());
        System.out.println("类型: " + dict.getType());
        System.out.println("状态: " + (dict.getIsActive() ? "启用" : "禁用"));
    }
}

// 按类型查询
Result<PageResult<DictionaryDTO>> typeResult = dictionaryService.getDictionaryList(
    pageRequest, "USER_STATUS", true, null);

// 搜索字典项
Result<PageResult<DictionaryDTO>> searchResult = dictionaryService.getDictionaryList(
    pageRequest, null, null, "状态");
```

### 1.2 根据ID获取字典项详情

**方法名**: `getDictionaryById`  
**描述**: 根据字典项ID获取详细信息  

**请求参数**:
- `id` (Long): 字典项ID

**返回值**: `Result<DictionaryDTO>`

**调用示例**:
```java
Result<DictionaryDTO> result = dictionaryService.getDictionaryById(1001L);

if (result.isSuccess()) {
    DictionaryDTO dict = result.getData();
    System.out.println("字典键: " + dict.getKey());
    System.out.println("字典值: " + dict.getValue());
    System.out.println("字典类型: " + dict.getType());
    System.out.println("描述: " + dict.getDescription());
    System.out.println("排序: " + dict.getSortOrder());
    System.out.println("状态: " + (dict.getIsActive() ? "启用" : "禁用"));
}
```

### 1.3 根据键和类型获取字典项

**方法名**: `getDictionaryByKeyAndType`  
**描述**: 根据字典键和类型获取字典项信息  

**请求参数**:
- `key` (String): 字典键
- `type` (String): 字典类型

**返回值**: `Result<DictionaryDTO>`

**调用示例**:
```java
Result<DictionaryDTO> result = dictionaryService.getDictionaryByKeyAndType("ACTIVE", "USER_STATUS");

if (result.isSuccess()) {
    DictionaryDTO dict = result.getData();
    System.out.println("字典值: " + dict.getValue());
    System.out.println("描述: " + dict.getDescription());
}
```

### 1.4 根据键获取字典项列表

**方法名**: `getDictionariesByKey`  
**描述**: 根据字典键获取字典项列表  

**请求参数**:
- `key` (String): 字典键
- `isActive` (Boolean, 可选): 是否启用

**返回值**: `Result<List<DictionaryDTO>>`

**调用示例**:
```java
Result<List<DictionaryDTO>> result = dictionaryService.getDictionariesByKey("STATUS", true);

if (result.isSuccess()) {
    List<DictionaryDTO> dictList = result.getData();
    System.out.println("找到 " + dictList.size() + " 个字典项");
    
    for (DictionaryDTO dict : dictList) {
        System.out.println("类型: " + dict.getType() + ", 值: " + dict.getValue());
    }
}
```

### 1.5 根据类型获取字典项列表

**方法名**: `getDictionariesByType`  
**描述**: 根据字典类型获取字典项列表  

**请求参数**:
- `type` (String): 字典类型
- `isActive` (Boolean, 可选): 是否启用

**返回值**: `Result<List<DictionaryDTO>>`

**调用示例**:
```java
Result<List<DictionaryDTO>> result = dictionaryService.getDictionariesByType("USER_STATUS", true);

if (result.isSuccess()) {
    List<DictionaryDTO> dictList = result.getData();
    System.out.println("用户状态字典项:");
    
    for (DictionaryDTO dict : dictList) {
        System.out.println("- " + dict.getKey() + ": " + dict.getValue());
    }
}

// 获取所有状态的字典项（包括禁用的）
Result<List<DictionaryDTO>> allResult = dictionaryService.getDictionariesByType("USER_STATUS", null);
```

### 1.6 创建字典项

**方法名**: `createDictionary`  
**描述**: 创建新的字典项  

**请求参数**:
- `dictionary` (DictionaryDTO): 字典项信息

**返回值**: `Result<DictionaryDTO>`

**调用示例**:
```java
DictionaryDTO newDict = new DictionaryDTO();
newDict.setKey("PENDING");
newDict.setValue("待处理");
newDict.setType("ORDER_STATUS");
newDict.setDescription("订单待处理状态");
newDict.setSortOrder(10);
newDict.setIsActive(true);

Result<DictionaryDTO> result = dictionaryService.createDictionary(newDict);

if (result.isSuccess()) {
    DictionaryDTO createdDict = result.getData();
    System.out.println("字典项创建成功，ID: " + createdDict.getId());
}
```

### 1.7 更新字典项

**方法名**: `updateDictionary`  
**描述**: 更新字典项信息  

**请求参数**:
- `id` (Long): 字典项ID
- `dictionary` (DictionaryDTO): 更新的字典项信息

**返回值**: `Result<DictionaryDTO>`

**调用示例**:
```java
DictionaryDTO updateDict = new DictionaryDTO();
updateDict.setValue("处理中");
updateDict.setDescription("订单正在处理中");
updateDict.setSortOrder(15);

Result<DictionaryDTO> result = dictionaryService.updateDictionary(1001L, updateDict);

if (result.isSuccess()) {
    DictionaryDTO updatedDict = result.getData();
    System.out.println("字典项更新成功: " + updatedDict.getValue());
}
```

### 1.8 删除字典项

**方法名**: `deleteDictionary`  
**描述**: 删除指定的字典项  

**请求参数**:
- `id` (Long): 字典项ID

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = dictionaryService.deleteDictionary(1001L);

if (result.isSuccess()) {
    System.out.println("字典项删除成功");
} else {
    System.err.println("删除失败: " + result.getMessage());
}
```

## 二、字典项排序和状态管理

### 2.1 更新字典项排序权重

**方法名**: `updateDictionarySortOrder`  
**描述**: 更新字典项的排序权重  

**请求参数**:
- `id` (Long): 字典项ID
- `sortOrder` (Integer): 排序权重

**返回值**: `Result<Void>`

**调用示例**:
```java
Result<Void> result = dictionaryService.updateDictionarySortOrder(1001L, 20);

if (result.isSuccess()) {
    System.out.println("排序权重更新成功");
}

// 批量调整排序
List<Long> ids = Arrays.asList(1001L, 1002L, 1003L);
List<Integer> orders = Arrays.asList(10, 20, 30);

for (int i = 0; i < ids.size(); i++) {
    dictionaryService.updateDictionarySortOrder(ids.get(i), orders.get(i));
}
```

### 2.2 更新字典项状态

**方法名**: `updateDictionaryStatus`  
**描述**: 更新字典项的启用状态  

**请求参数**:
- `id` (Long): 字典项ID
- `isActive` (Boolean): 是否启用

**返回值**: `Result<Void>`

**调用示例**:
```java
// 启用字典项
Result<Void> result = dictionaryService.updateDictionaryStatus(1001L, true);

if (result.isSuccess()) {
    System.out.println("字典项已启用");
}

// 禁用字典项
Result<Void> disableResult = dictionaryService.updateDictionaryStatus(1002L, false);
```

## 三、字典类型管理

### 3.1 获取所有字典类型

**方法名**: `getAllDictionaryTypes`  
**描述**: 获取系统中所有的字典类型  

**请求参数**: 无

**返回值**: `Result<List<String>>`

**调用示例**:
```java
Result<List<String>> result = dictionaryService.getAllDictionaryTypes();

if (result.isSuccess()) {
    List<String> types = result.getData();
    System.out.println("系统字典类型:");
    
    for (String type : types) {
        System.out.println("- " + type);
    }
}
```

### 3.2 根据类型统计字典项数量

**方法名**: `countDictionariesByType`  
**描述**: 统计指定类型的字典项数量  

**请求参数**:
- `type` (String): 字典类型
- `isActive` (Boolean, 可选): 是否启用

**返回值**: `Result<Integer>`

**调用示例**:
```java
// 统计用户状态类型的启用字典项数量
Result<Integer> result = dictionaryService.countDictionariesByType("USER_STATUS", true);

if (result.isSuccess()) {
    Integer count = result.getData();
    System.out.println("用户状态类型启用字典项数量: " + count);
}

// 统计所有字典项数量（包括禁用的）
Result<Integer> totalResult = dictionaryService.countDictionariesByType("USER_STATUS", null);
```

## 四、批量操作

### 4.1 批量创建字典项

**方法名**: `batchCreateDictionaries`  
**描述**: 批量创建多个字典项  

**请求参数**:
- `dictionaries` (List<DictionaryDTO>): 字典项列表

**返回值**: `Result<List<DictionaryDTO>>`

**调用示例**:
```java
List<DictionaryDTO> dictList = new ArrayList<>();

// 创建订单状态字典项
DictionaryDTO dict1 = new DictionaryDTO();
dict1.setKey("CREATED");
dict1.setValue("已创建");
dict1.setType("ORDER_STATUS");
dict1.setSortOrder(10);
dict1.setIsActive(true);
dictList.add(dict1);

DictionaryDTO dict2 = new DictionaryDTO();
dict2.setKey("PAID");
dict2.setValue("已支付");
dict2.setType("ORDER_STATUS");
dict2.setSortOrder(20);
dict2.setIsActive(true);
dictList.add(dict2);

DictionaryDTO dict3 = new DictionaryDTO();
dict3.setKey("SHIPPED");
dict3.setValue("已发货");
dict3.setType("ORDER_STATUS");
dict3.setSortOrder(30);
dict3.setIsActive(true);
dictList.add(dict3);

Result<List<DictionaryDTO>> result = dictionaryService.batchCreateDictionaries(dictList);

if (result.isSuccess()) {
    List<DictionaryDTO> createdList = result.getData();
    System.out.println("批量创建成功，共创建: " + createdList.size() + "个字典项");
    
    for (DictionaryDTO dict : createdList) {
        System.out.println("- " + dict.getKey() + ": " + dict.getValue() + " (ID: " + dict.getId() + ")");
    }
}
```

### 4.2 批量更新字典项状态

**方法名**: `batchUpdateDictionaryStatus`  
**描述**: 批量更新多个字典项的状态  

**请求参数**:
- `ids` (List<Long>): 字典项ID列表
- `isActive` (Boolean): 是否启用

**返回值**: `Result<Void>`

**调用示例**:
```java
List<Long> dictIds = Arrays.asList(1001L, 1002L, 1003L, 1004L);

// 批量启用字典项
Result<Void> result = dictionaryService.batchUpdateDictionaryStatus(dictIds, true);

if (result.isSuccess()) {
    System.out.println("批量启用成功，共启用: " + dictIds.size() + "个字典项");
} else {
    System.err.println("批量启用失败: " + result.getMessage());
}

// 批量禁用字典项
List<Long> disableIds = Arrays.asList(1005L, 1006L, 1007L);
Result<Void> disableResult = dictionaryService.batchUpdateDictionaryStatus(disableIds, false);
```

### 4.3 批量删除字典项

**方法名**: `batchDeleteDictionaries`  
**描述**: 批量删除多个字典项  

**请求参数**:
- `ids` (List<Long>): 字典项ID列表

**返回值**: `Result<Void>`

**调用示例**:
```java
List<Long> dictIds = Arrays.asList(1001L, 1002L, 1003L);

Result<Void> result = dictionaryService.batchDeleteDictionaries(dictIds);

if (result.isSuccess()) {
    System.out.println("批量删除成功，共删除: " + dictIds.size() + "个字典项");
} else {
    System.err.println("批量删除失败: " + result.getMessage());
}
```

## 五、搜索功能

### 5.1 搜索字典项

**方法名**: `searchDictionaries`  
**描述**: 根据关键词搜索字典项  

**请求参数**:
- `keyword` (String): 搜索关键词
- `type` (String, 可选): 字典类型过滤
- `isActive` (Boolean, 可选): 是否启用

**返回值**: `Result<List<DictionaryDTO>>`

**调用示例**:
```java
// 搜索包含"状态"的字典项
Result<List<DictionaryDTO>> result = dictionaryService.searchDictionaries("状态", null, true);

if (result.isSuccess()) {
    List<DictionaryDTO> dictList = result.getData();
    System.out.println("搜索到 " + dictList.size() + " 个相关字典项:");
    
    for (DictionaryDTO dict : dictList) {
        System.out.println("- " + dict.getKey() + ": " + dict.getValue() + " (" + dict.getType() + ")");
    }
}

// 在特定类型中搜索
Result<List<DictionaryDTO>> typeSearchResult = dictionaryService.searchDictionaries(
    "处理", "ORDER_STATUS", true);

// 搜索所有状态的字典项（包括禁用的）
Result<List<DictionaryDTO>> allSearchResult = dictionaryService.searchDictionaries(
    "用户", "USER_STATUS", null);
```

## 错误处理

### 常见错误码

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查请求参数格式和必填字段 |
| 403 | 权限不足 | 确认用户是否有相应操作权限 |
| 404 | 字典项不存在 | 确认字典项ID是否正确 |
| 409 | 字典项键值冲突 | 同一类型下的键值不能重复 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 错误处理示例

```java
Result<DictionaryDTO> result = dictionaryService.getDictionaryById(1001L);

if (!result.isSuccess()) {
    switch (result.getCode()) {
        case 403:
            System.err.println("权限不足: " + result.getMessage());
            break;
        case 404:
            System.err.println("字典项不存在: " + result.getMessage());
            break;
        case 409:
            System.err.println("字典项冲突: " + result.getMessage());
            break;
        case 500:
            System.err.println("服务器错误: " + result.getMessage());
            break;
        default:
            System.err.println("未知错误: " + result.getMessage());
    }
}
```

## 最佳实践

### 1. 字典类型设计
建议按照业务模块设计字典类型：
- 用户相关：USER_STATUS、USER_TYPE、USER_ROLE
- 订单相关：ORDER_STATUS、ORDER_TYPE、PAYMENT_STATUS
- 系统相关：SYSTEM_CONFIG、LOG_LEVEL、NOTIFICATION_TYPE

```java
// 创建用户状态字典
List<DictionaryDTO> userStatusList = Arrays.asList(
    createDict("ACTIVE", "激活", "USER_STATUS", 10),
    createDict("INACTIVE", "未激活", "USER_STATUS", 20),
    createDict("LOCKED", "锁定", "USER_STATUS", 30),
    createDict("DELETED", "已删除", "USER_STATUS", 40)
);

dictionaryService.batchCreateDictionaries(userStatusList);

private DictionaryDTO createDict(String key, String value, String type, Integer sortOrder) {
    DictionaryDTO dict = new DictionaryDTO();
    dict.setKey(key);
    dict.setValue(value);
    dict.setType(type);
    dict.setSortOrder(sortOrder);
    dict.setIsActive(true);
    return dict;
}
```

### 2. 排序权重管理
合理设置排序权重，便于后续调整：

```java
// 使用10的倍数作为排序权重，便于插入新项
// 10, 20, 30, 40... 如需在20和30之间插入，可使用25
dictionaryService.updateDictionarySortOrder(dictId, 25);
```

### 3. 缓存策略
对于频繁访问的字典数据，建议使用缓存：

```java
// 获取并缓存字典类型
private Map<String, List<DictionaryDTO>> dictCache = new ConcurrentHashMap<>();

public List<DictionaryDTO> getCachedDictionaries(String type) {
    return dictCache.computeIfAbsent(type, k -> {
        Result<List<DictionaryDTO>> result = dictionaryService.getDictionariesByType(k, true);
        return result.isSuccess() ? result.getData() : Collections.emptyList();
    });
}
```

### 4. 数据验证
创建字典项时进行必要的验证：

```java
public Result<DictionaryDTO> createDictionaryWithValidation(DictionaryDTO dict) {
    // 验证必填字段
    if (StringUtils.isBlank(dict.getKey()) || StringUtils.isBlank(dict.getValue()) 
        || StringUtils.isBlank(dict.getType())) {
        return Result.error("字典键、值和类型不能为空");
    }
    
    // 检查是否已存在
    Result<DictionaryDTO> existResult = dictionaryService.getDictionaryByKeyAndType(
        dict.getKey(), dict.getType());
    if (existResult.isSuccess()) {
        return Result.error("字典项已存在");
    }
    
    // 创建字典项
    return dictionaryService.createDictionary(dict);
}
```

### 5. 批量操作优化
对于大量数据操作，建议分批处理：

```java
public void batchCreateDictionariesInChunks(List<DictionaryDTO> dictionaries) {
    int chunkSize = 100; // 每批处理100个
    
    for (int i = 0; i < dictionaries.size(); i += chunkSize) {
        int end = Math.min(i + chunkSize, dictionaries.size());
        List<DictionaryDTO> chunk = dictionaries.subList(i, end);
        
        Result<List<DictionaryDTO>> result = dictionaryService.batchCreateDictionaries(chunk);
        if (!result.isSuccess()) {
            System.err.println("批次 " + (i/chunkSize + 1) + " 处理失败: " + result.getMessage());
        }
    }
}
```

## 使用场景示例

### 场景1：下拉框数据源
```java
// 获取用户状态下拉框数据
public List<SelectOption> getUserStatusOptions() {
    Result<List<DictionaryDTO>> result = dictionaryService.getDictionariesByType("USER_STATUS", true);
    
    if (result.isSuccess()) {
        return result.getData().stream()
            .sorted(Comparator.comparing(DictionaryDTO::getSortOrder))
            .map(dict -> new SelectOption(dict.getKey(), dict.getValue()))
            .collect(Collectors.toList());
    }
    
    return Collections.emptyList();
}
```

### 场景2：状态码转换
```java
// 将状态码转换为显示文本
public String getStatusText(String statusCode, String type) {
    Result<DictionaryDTO> result = dictionaryService.getDictionaryByKeyAndType(statusCode, type);
    return result.isSuccess() ? result.getData().getValue() : statusCode;
}

// 使用示例
String orderStatusText = getStatusText("PAID", "ORDER_STATUS"); // 返回"已支付"
```

### 场景3：配置管理
```java
// 使用字典管理系统配置
public String getSystemConfig(String configKey) {
    Result<DictionaryDTO> result = dictionaryService.getDictionaryByKeyAndType(configKey, "SYSTEM_CONFIG");
    return result.isSuccess() ? result.getData().getValue() : null;
}

// 更新系统配置
public void updateSystemConfig(String configKey, String configValue) {
    Result<DictionaryDTO> result = dictionaryService.getDictionaryByKeyAndType(configKey, "SYSTEM_CONFIG");
    
    if (result.isSuccess()) {
        DictionaryDTO dict = result.getData();
        dict.setValue(configValue);
        dictionaryService.updateDictionary(dict.getId(), dict);
    } else {
        // 创建新配置
        DictionaryDTO newDict = new DictionaryDTO();
        newDict.setKey(configKey);
        newDict.setValue(configValue);
        newDict.setType("SYSTEM_CONFIG");
        newDict.setIsActive(true);
        dictionaryService.createDictionary(newDict);
    }
}
```

## 版本历史

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2025-07-17 | 初始版本，提供完整的字典管理功能 |

## 联系方式

如有问题或建议，请联系：
- 技术支持：AI Community Development Team
- 邮箱：<EMAIL>
- 文档更新：定期更新，请关注最新版本
