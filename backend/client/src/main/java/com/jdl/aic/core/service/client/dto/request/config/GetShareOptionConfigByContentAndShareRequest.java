package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 根据内容类型和分享类型获取配置请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetShareOptionConfigByContentAndShareRequest {
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 分享类型
     */
    private String shareType;
    
    /**
     * 默认构造函数
     */
    public GetShareOptionConfigByContentAndShareRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param shareType 分享类型
     */
    public GetShareOptionConfigByContentAndShareRequest(String contentType, String shareType) {
        this.contentType = contentType;
        this.shareType = shareType;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getShareType() {
        return shareType;
    }
    
    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
    
    @Override
    public String toString() {
        return "GetShareOptionConfigByContentAndShareRequest{" +
                "contentType='" + contentType + '\'' +
                ", shareType='" + shareType + '\'' +
                '}';
    }
}
