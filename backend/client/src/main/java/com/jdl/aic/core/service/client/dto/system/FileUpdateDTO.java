package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * 文件更新DTO
 * 
 * <p>用于文件信息更新的数据传输对象，支持文件名、访问权限、业务关联等信息的修改。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FileUpdateDTO {
    
    /**
     * 文件ID
     */
    @NotNull(message = "文件ID不能为空")
    private Long id;
    
    /**
     * 新文件名
     */
    private String fileName;
    
    /**
     * 文件状态（0:上传中, 1:可用, 2:已删除, 3:损坏）
     */
    private Integer status;
    
    /**
     * 业务类型（avatar:头像, knowledge:知识附件, solution:解决方案附件, learning:学习资源）
     */
    private String businessType;
    
    /**
     * 关联业务ID
     */
    private Long businessId;
    
    /**
     * 访问权限（0:私有, 1:团队可见, 2:公开）
     */
    private Integer accessLevel;
    
    /**
     * 过期时间
     */
    private LocalDateTime expiresAt;
    
    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public FileUpdateDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getBusinessType() {
        return businessType;
    }
    
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }
    
    public Long getBusinessId() {
        return businessId;
    }
    
    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }
    
    public Integer getAccessLevel() {
        return accessLevel;
    }
    
    public void setAccessLevel(Integer accessLevel) {
        this.accessLevel = accessLevel;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public String toString() {
        return "FileUpdateDTO{" +
                "id=" + id +
                ", fileName='" + fileName + '\'' +
                ", status=" + status +
                ", businessType='" + businessType + '\'' +
                ", businessId=" + businessId +
                ", accessLevel=" + accessLevel +
                ", expiresAt=" + expiresAt +
                ", updatedBy=" + updatedBy +
                '}';
    }
}
