package com.jdl.aic.core.service.client.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 收藏DTO
 * 
 * <p>封装用户收藏内容的信息，支持对知识、解决方案、学习资源等内容的收藏。
 * 支持收藏夹分类管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FavoriteDTO {
    
    /**
     * 收藏ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 内容类型（0:知识, 1:资讯, 2:解决方案）
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 当content_type为知识时，关联知识类型ID
     */
    private Long relatedKnowledgeTypeId;

    /**
     * 内容标题
     */
    private String contentTitle;
    
    /**
     * 内容描述
     */
    private String contentDescription;
    
    /**
     * 内容URL
     */
    private String contentUrl;
    
    /**
     * 收藏夹名称
     */
    private String folderName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 默认构造函数
     */
    public FavoriteDTO() {
    }
    
    /**
     * 构造函数
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     */
    public FavoriteDTO(Long userId, Integer contentType, Long contentId) {
        this.userId = userId;
        this.contentType = contentType;
        this.contentId = contentId;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public Long getRelatedKnowledgeTypeId() {
        return relatedKnowledgeTypeId;
    }

    public void setRelatedKnowledgeTypeId(Long relatedKnowledgeTypeId) {
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
    }

    public String getContentTitle() {
        return contentTitle;
    }
    
    public void setContentTitle(String contentTitle) {
        this.contentTitle = contentTitle;
    }
    
    public String getContentDescription() {
        return contentDescription;
    }
    
    public void setContentDescription(String contentDescription) {
        this.contentDescription = contentDescription;
    }
    
    public String getContentUrl() {
        return contentUrl;
    }
    
    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }
    
    public String getFolderName() {
        return folderName;
    }
    
    public void setFolderName(String folderName) {
        this.folderName = folderName;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "FavoriteDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                ", relatedKnowledgeTypeId=" + relatedKnowledgeTypeId +
                ", contentTitle='" + contentTitle + '\'' +
                ", contentDescription='" + contentDescription + '\'' +
                ", contentUrl='" + contentUrl + '\'' +
                ", folderName='" + folderName + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
}
