package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.category.CategoryDTO;
import com.jdl.aic.core.service.client.dto.category.TagDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.client.dto.request.content.ContentAssociationRequest;
import com.jdl.aic.core.service.client.dto.request.content.AssociateContentWithCategoryRequest;
import java.util.List;

/**
 * 内容关联管理服务接口
 * 
 * <p>提供内容与分类标签关联管理功能，包括：
 * <ul>
 *   <li>内容与分类的关联和解除关联</li>
 *   <li>内容与标签的关联和解除关联</li>
 *   <li>批量关联操作和替换操作</li>
 *   <li>内容关联关系查询</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public interface ContentAssociationService {
    
    // ==================== 内容分类关联管理 ====================

    /**
     * 关联内容与分类
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     * @return 操作结果
     */
    Result<Void> associateContentWithCategory(ContentType contentType, Long contentId, Long categoryId);

    /**
     * 解除内容与分类的关联
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     * @return 操作结果
     */
    Result<Void> dissociateContentFromCategory(ContentType contentType, Long contentId, Long categoryId);

    /**
     * 获取内容关联的分类列表
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 分类列表
     */
    Result<List<CategoryDTO>> getCategoriesByContent(ContentType contentType, Long contentId);

    // ==================== 内容标签关联管理 ====================

    /**
     * 关联内容与标签
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param tagId 标签ID
     * @return 操作结果
     */
    Result<Void> associateContentWithTag(ContentType contentType, Long contentId, Long tagId);

    /**
     * 解除内容与标签的关联
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param tagId 标签ID
     * @return 操作结果
     */
    Result<Void> dissociateContentFromTag(ContentType contentType, Long contentId, Long tagId);

    /**
     * 获取内容关联的标签列表
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 标签列表
     */
    Result<List<TagDTO>> getTagsByContent(ContentType contentType, Long contentId);

    // ==================== 批量关联操作 ====================

    /**
     * 批量关联内容与分类
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryIds 分类ID列表
     * @return 操作结果
     */
    Result<Void> batchAssociateContentWithCategories(ContentType contentType, Long contentId, List<Long> categoryIds);

    /**
     * 批量解除内容与分类的关联
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryIds 分类ID列表
     * @return 操作结果
     */
    Result<Void> batchDissociateContentFromCategories(ContentType contentType, Long contentId, List<Long> categoryIds);

    /**
     * 批量关联内容与标签
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param tagIds 标签ID列表
     * @return 操作结果
     */
    Result<Void> batchAssociateContentWithTags(ContentType contentType, Long contentId, List<Long> tagIds);

    /**
     * 批量解除内容与标签的关联
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param tagIds 标签ID列表
     * @return 操作结果
     */
    Result<Void> batchDissociateContentFromTags(ContentType contentType, Long contentId, List<Long> tagIds);

    // ==================== 替换关联操作 ====================

    /**
     * 替换内容的所有分类关联
     * 
     * <p>先清除内容的所有分类关联，然后关联新的分类列表。
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryIds 新的分类ID列表
     * @return 操作结果
     */
    Result<Void> replaceContentCategories(ContentType contentType, Long contentId, List<Long> categoryIds);

    /**
     * 替换内容的所有标签关联
     * 
     * <p>先清除内容的所有标签关联，然后关联新的标签列表。
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param tagIds 新的标签ID列表
     * @return 操作结果
     */
    Result<Void> replaceContentTags(ContentType contentType, Long contentId, List<Long> tagIds);
}
