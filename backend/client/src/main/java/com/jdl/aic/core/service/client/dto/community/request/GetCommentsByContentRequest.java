package com.jdl.aic.core.service.client.dto.community.request;

import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.constraints.NotNull;

/**
 * 获取内容评论列表请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetCommentsByContentRequest {

    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 分页请求
     */
    @NotNull(message = "分页参数不能为空")
    private PageRequest pageRequest;

    /**
     * 排序方式（time:时间, like:点赞数）
     */
    private String sortBy;

    public GetCommentsByContentRequest() {
    }

    public GetCommentsByContentRequest(Integer contentType, Long contentId, PageRequest pageRequest) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.pageRequest = pageRequest;
    }

    public GetCommentsByContentRequest(Integer contentType, Long contentId, PageRequest pageRequest, String sortBy) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.pageRequest = pageRequest;
        this.sortBy = sortBy;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public PageRequest getPageRequest() {
        return pageRequest;
    }

    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    @Override
    public String toString() {
        return "GetCommentsByContentRequest{" +
                "contentType=" + contentType +
                ", contentId=" + contentId +
                ", pageRequest=" + pageRequest +
                ", sortBy='" + sortBy + '\'' +
                '}';
    }
}
