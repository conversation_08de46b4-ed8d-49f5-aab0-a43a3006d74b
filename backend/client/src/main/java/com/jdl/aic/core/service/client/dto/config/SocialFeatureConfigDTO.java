package com.jdl.aic.core.service.client.dto.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 社交功能配置DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SocialFeatureConfigDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 内容类型编码
     */
    @NotBlank(message = "内容类型编码不能为空")
    private String contentType;
    
    /**
     * 功能类型：like,favorite,share,comment,read_track
     */
    @NotBlank(message = "功能类型不能为空")
    private String featureType;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 功能配置JSON
     */
    private String configJson;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public SocialFeatureConfigDTO() {
    }

    /**
     * 基础构造函数
     *
     * @param contentType 内容类型编码
     * @param featureType 功能类型
     */
    public SocialFeatureConfigDTO(String contentType, String featureType) {
        this.contentType = contentType;
        this.featureType = featureType;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getFeatureType() {
        return featureType;
    }
    
    public void setFeatureType(String featureType) {
        this.featureType = featureType;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    public String getConfigJson() {
        return configJson;
    }
    
    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public String toString() {
        return "SocialFeatureConfigDTO{" +
                "id=" + id +
                ", contentType='" + contentType + '\'' +
                ", featureType='" + featureType + '\'' +
                ", isEnabled=" + isEnabled +
                ", configJson='" + configJson + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SocialFeatureConfigDTO that = (SocialFeatureConfigDTO) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (contentType != null ? !contentType.equals(that.contentType) : that.contentType != null) return false;
        if (featureType != null ? !featureType.equals(that.featureType) : that.featureType != null) return false;
        return isEnabled != null ? isEnabled.equals(that.isEnabled) : that.isEnabled == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (contentType != null ? contentType.hashCode() : 0);
        result = 31 * result + (featureType != null ? featureType.hashCode() : 0);
        result = 31 * result + (isEnabled != null ? isEnabled.hashCode() : 0);
        return result;
    }
}
