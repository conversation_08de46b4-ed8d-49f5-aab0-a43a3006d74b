package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.FileStorageDTO;
import com.jdl.aic.core.service.client.dto.system.FileUpdateDTO;
import com.jdl.aic.core.service.client.dto.system.FileDownloadDTO;

import java.util.List;

/**
 * 文件存储服务接口
 * 
 * <p>提供文件存储管理功能，包括：
 * <ul>
 *   <li>文件上传和下载</li>
 *   <li>文件信息管理</li>
 *   <li>文件访问权限控制</li>
 *   <li>图片处理和缩略图生成</li>
 *   <li>文件存储统计和清理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface FileStorageService {
    
    // ==================== 文件上传和下载 ====================
    
    /**
     * 上传文件
     * 
     * @param fileData 文件数据（Base64编码或字节数组）
     * @param fileName 文件名
     * @param contentType 文件类型
     * @param businessType 业务类型（avatar, knowledge, solution, learning）
     * @param businessId 关联业务ID
     * @param uploadUserId 上传用户ID
     * @return 上传结果
     */
    Result<FileStorageDTO> uploadFile(
            Object fileData,
            String fileName,
            String contentType,
            String businessType,
            Long businessId,
            Long uploadUserId);
    
    /**
     * 批量上传文件
     * 
     * @param files 文件列表
     * @param businessType 业务类型
     * @param businessId 关联业务ID
     * @param uploadUserId 上传用户ID
     * @return 批量上传结果
     */
    Result<List<FileStorageDTO>> batchUploadFiles(
            List<Object> files,
            String businessType,
            Long businessId,
            Long uploadUserId);
    
    /**
     * 生成文件下载链接
     * 
     * @param fileId 文件ID
     * @param userId 请求用户ID
     * @param expirationMinutes 链接有效期（分钟）
     * @return 下载信息
     */
    Result<FileDownloadDTO> generateDownloadLink(Long fileId, Long userId, Integer expirationMinutes);
    
    /**
     * 下载文件
     * 
     * @param fileId 文件ID
     * @param userId 下载用户ID
     * @return 文件数据
     */
    Result<Object> downloadFile(Long fileId, Long userId);
    
    /**
     * 通过访问令牌下载文件
     * 
     * @param accessToken 访问令牌
     * @return 文件数据
     */
    Result<Object> downloadFileByToken(String accessToken);
    
    // ==================== 文件信息管理 ====================
    
    /**
     * 获取文件列表（分页）
     * 
     * @param pageRequest 分页请求
     * @param businessType 业务类型过滤
     * @param businessId 业务ID过滤
     * @param uploadUserId 上传用户过滤
     * @param status 状态过滤
     * @param search 搜索关键词（文件名）
     * @return 文件列表
     */
    Result<PageResult<FileStorageDTO>> getFileList(
            PageRequest pageRequest,
            String businessType,
            Long businessId,
            Long uploadUserId,
            Integer status,
            String search);
    
    /**
     * 根据ID获取文件详情
     * 
     * @param id 文件ID
     * @return 文件详情
     */
    Result<FileStorageDTO> getFileById(Long id);
    
    /**
     * 根据业务关联获取文件列表
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 关联文件列表
     */
    Result<List<FileStorageDTO>> getFilesByBusiness(String businessType, Long businessId);
    
    /**
     * 更新文件信息
     * 
     * @param fileUpdate 文件更新信息
     * @return 更新结果
     */
    Result<FileStorageDTO> updateFile(FileUpdateDTO fileUpdate);
    
    /**
     * 删除文件
     * 
     * @param id 文件ID
     * @param userId 操作用户ID
     * @return 删除结果
     */
    Result<Void> deleteFile(Long id, Long userId);
    
    /**
     * 批量删除文件
     * 
     * @param fileIds 文件ID列表
     * @param userId 操作用户ID
     * @return 删除结果
     */
    Result<Void> batchDeleteFiles(List<Long> fileIds, Long userId);
    
    // ==================== 文件访问权限 ====================
    
    /**
     * 检查文件访问权限
     * 
     * @param fileId 文件ID
     * @param userId 用户ID
     * @return 是否有访问权限
     */
    Result<Boolean> checkFileAccess(Long fileId, Long userId);
    
    /**
     * 更新文件访问权限
     * 
     * @param fileId 文件ID
     * @param accessLevel 访问级别（0:私有, 1:团队可见, 2:公开）
     * @param userId 操作用户ID
     * @return 操作结果
     */
    Result<Void> updateFileAccess(Long fileId, Integer accessLevel, Long userId);
    
    /**
     * 设置文件过期时间
     * 
     * @param fileId 文件ID
     * @param expirationDays 过期天数
     * @param userId 操作用户ID
     * @return 操作结果
     */
    Result<Void> setFileExpiration(Long fileId, Integer expirationDays, Long userId);
    
    // ==================== 图片处理 ====================
    
    /**
     * 生成图片缩略图
     * 
     * @param fileId 图片文件ID
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @param quality 图片质量（1-100）
     * @return 缩略图文件信息
     */
    Result<FileStorageDTO> generateThumbnail(Long fileId, Integer width, Integer height, Integer quality);
    
    /**
     * 压缩图片
     * 
     * @param fileId 图片文件ID
     * @param quality 压缩质量（1-100）
     * @param maxWidth 最大宽度
     * @param maxHeight 最大高度
     * @return 压缩后文件信息
     */
    Result<FileStorageDTO> compressImage(Long fileId, Integer quality, Integer maxWidth, Integer maxHeight);
    
    /**
     * 获取图片信息
     * 
     * @param fileId 图片文件ID
     * @return 图片信息（尺寸、格式等）
     */
    Result<Object> getImageInfo(Long fileId);
    
    // ==================== 存储统计和清理 ====================
    
    /**
     * 获取存储统计信息
     * 
     * @param businessType 业务类型过滤
     * @param userId 用户ID过滤
     * @return 存储统计
     */
    Result<Object> getStorageStats(String businessType, Long userId);
    
    /**
     * 获取用户存储使用情况
     * 
     * @param userId 用户ID
     * @return 用户存储统计
     */
    Result<Object> getUserStorageUsage(Long userId);
    
    /**
     * 清理过期文件
     * 
     * @param dryRun 是否为试运行（不实际删除）
     * @return 清理结果
     */
    Result<Object> cleanupExpiredFiles(Boolean dryRun);
    
    /**
     * 清理孤立文件（没有业务关联的文件）
     * 
     * @param dryRun 是否为试运行
     * @return 清理结果
     */
    Result<Object> cleanupOrphanFiles(Boolean dryRun);
    
    /**
     * 验证文件完整性
     * 
     * @param fileId 文件ID
     * @return 验证结果
     */
    Result<Boolean> verifyFileIntegrity(Long fileId);
    
    /**
     * 批量验证文件完整性
     * 
     * @param fileIds 文件ID列表
     * @return 验证结果
     */
    Result<Object> batchVerifyFileIntegrity(List<Long> fileIds);
    
    /**
     * 迁移文件存储位置
     * 
     * @param fileId 文件ID
     * @param targetStorageType 目标存储类型
     * @return 迁移结果
     */
    Result<FileStorageDTO> migrateFileStorage(Long fileId, String targetStorageType);
}
