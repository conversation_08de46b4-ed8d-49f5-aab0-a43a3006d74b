package com.jdl.aic.core.service.portal.client;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;

import java.util.List;

/**
 * 收藏数据服务接口
 *
 * <p>提供收藏相关的业务功能，包括：
 * <ul>
 *   <li>收藏内容的添加和取消</li>
 *   <li>用户收藏列表查询</li>
 *   <li>收藏统计和分析</li>
 *   <li>收藏状态管理</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface FavoriteDataService {

    // ==================== 收藏基本操作 ====================

    /**
     * 添加收藏
     *
     * @param request 添加收藏请求参数
     * @return 收藏结果
     */
    Result<FavoriteDTO> addFavorite(AddFavoriteRequest request);

    /**
     * 取消收藏
     *
     * @param request 取消收藏请求参数
     * @return 操作结果
     */
    Result<Void> removeFavorite(RemoveFavoriteRequest request);

    /**
     * 切换收藏状态（如果已收藏则取消，如果未收藏则添加）
     *
     * @param request 切换收藏状态请求参数
     * @return 操作结果，包含当前收藏状态
     */
    Result<Boolean> toggleFavorite(ToggleFavoriteRequest request);

    /**
     * 检查用户是否已收藏指定内容
     *
     * @param request 检查收藏状态请求参数
     * @return 收藏状态
     */
    Result<Boolean> isFavorited(CheckFavoriteRequest request);

    // ==================== 收藏查询 ====================

    /**
     * 获取用户收藏列表（分页）
     *
     * @param request 获取用户收藏列表请求参数
     * @return 收藏列表
     */
    Result<PageResult<FavoriteDTO>> getUserFavorites(GetUserFavoritesRequest request);

    /**
     * 获取用户最近收藏的内容
     *
     * @param userId 获取最近收藏请求参数
     * @return 最近收藏列表
     */
    Result<List<FavoriteDTO>> getRecentFavorites(Long userId, Integer limit);

    /**
     * 根据内容获取收藏列表
     *
     * @param request 根据内容获取收藏列表请求参数
     * @return 收藏该内容的用户列表
     */
    Result<PageResult<FavoriteDTO>> getFavoritesByContent(GetFavoritesByContentRequest request);

    /**
     * 批量获取用户对内容列表的收藏状态
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 收藏状态映射（contentId -> isFavorited）
     */
    Result<List<Object>> getFavoriteStatusBatch(Long userId, Integer contentType, List<Long> contentIds);

    // ==================== 收藏统计 ====================

    /**
     * 统计用户收藏总数
     *
     * @param request 获取用户收藏总数请求参数
     * @return 收藏总数
     */
    Result<Integer> getUserFavoriteCount(Long userId);

    /**
     * 统计用户各类型内容的收藏数
     *
     * @param userId 获取用户收藏总数请求参数
     * @return 各类型收藏统计
     */
    Result<List<Object>> getUserFavoriteCountByType(Long userId);

    /**
     * 统计指定内容的收藏数
     *
     * @param request 获取内容收藏数请求参数
     * @return 收藏数
     */
    Result<Integer> getContentFavoriteCount(GetContentFavoriteCountRequest request);

    /**
     * 批量统计内容列表的收藏数
     *
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 收藏数统计列表
     */
    Result<List<Object>> getContentFavoriteCountBatch(Integer contentType, List<Long> contentIds);

    /**
     * 获取热门收藏内容
     *
     * @param contentType 内容类型
     * @param limit 限制数量
     * @param days 统计天数（可选）
     * @return 热门收藏内容列表
     */
    Result<List<Object>> getPopularFavoriteContent(Integer contentType, Integer limit, Integer days);

    // ==================== 批量操作 ====================

    /**
     * 批量添加收藏
     *
     * @param userId 用户ID
     * @param favorites 收藏列表
     * @return 批量操作结果
     */
    Result<List<FavoriteDTO>> batchAddFavorites(Long userId, List<FavoriteDTO> favorites);

    /**
     * 批量取消收藏
     *
     * @param userId 用户ID
     * @param favoriteIds 收藏ID列表
     * @return 批量操作结果
     */
    Result<Void> batchRemoveFavorites(Long userId, List<Long> favoriteIds);

    /**
     * 批量取消收藏（根据内容）
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 批量操作结果
     */
    Result<Void> batchRemoveFavoritesByContent(Long userId, Integer contentType, List<Long> contentIds);

    // ==================== 收藏管理 ====================

    /**
     * 清理用户已删除的收藏记录
     *
     * @param userId 用户ID
     * @param days 清理天数前的记录
     * @return 清理结果
     */
    Result<Integer> cleanupDeletedFavorites(Long userId, Integer days);

    /**
     * 恢复已删除的收藏
     *
     * @param userId 用户ID
     * @param favoriteId 收藏ID
     * @return 恢复结果
     */
    Result<FavoriteDTO> restoreFavorite(Long userId, Long favoriteId);

    /**
     * 导出用户收藏数据
     *
     * @param userId 用户ID
     * @param contentType 内容类型过滤（可选）
     * @param format 导出格式（json, csv等）
     * @return 导出结果
     */
    Result<Object> exportUserFavorites(Long userId, Integer contentType, String format);

    // ==================== 收藏分析 ====================

    /**
     * 获取用户收藏趋势分析
     *
     * @param userId 用户ID
     * @param days 分析天数
     * @return 收藏趋势数据
     */
    Result<Object> getUserFavoriteTrend(Long userId, Integer days);

    /**
     * 获取用户收藏偏好分析
     *
     * @param userId 用户ID
     * @return 收藏偏好数据
     */
    Result<Object> getUserFavoritePreference(Long userId);

    /**
     * 获取内容收藏趋势分析
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param days 分析天数
     * @return 收藏趋势数据
     */
    Result<Object> getContentFavoriteTrend(Integer contentType, Long contentId, Integer days);

    // ==================== 收藏推荐 ====================

    /**
     * 基于用户收藏历史推荐相关内容
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param limit 推荐数量
     * @return 推荐内容列表
     */
    Result<List<Object>> getRecommendedContent(Long userId, Integer contentType, Integer limit);

    /**
     * 获取相似收藏偏好的用户
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 相似用户列表
     */
    Result<List<Object>> getSimilarUsers(Long userId, Integer limit);

    /**
     * 获取用户可能感兴趣的收藏内容
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param limit 限制数量
     * @return 推荐收藏列表
     */
    Result<List<Object>> getRecommendedFavorites(Long userId, Integer contentType, Integer limit);
}
