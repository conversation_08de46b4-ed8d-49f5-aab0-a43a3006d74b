package com.jdl.aic.core.service.client.dto.learning;

import java.io.Serializable;
import java.util.List;

/**
 * 分类统计数据传输对象
 */
public class CategoryStatisticsDTO implements Serializable {

    /** 分类ID */
    private String categoryId;

    /** 分类名称 */
    private String categoryName;

    /** 父分类ID */
    private String parentCategoryId;

    /** 该分类下资源数量 */
    private Integer resourceCount;

    /** 该分类下课程数量 */
    private Integer courseCount;

    /** 子分类统计 */
    private List<CategoryStatisticsDTO> children;

    /** 分类图标URL（可选） */
    private String iconUrl;

    /** 分类排序权重 */
    private Integer sortOrder;

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(String parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public Integer getResourceCount() {
        return resourceCount;
    }

    public void setResourceCount(Integer resourceCount) {
        this.resourceCount = resourceCount;
    }

    public Integer getCourseCount() {
        return courseCount;
    }

    public void setCourseCount(Integer courseCount) {
        this.courseCount = courseCount;
    }

    public List<CategoryStatisticsDTO> getChildren() {
        return children;
    }

    public void setChildren(List<CategoryStatisticsDTO> children) {
        this.children = children;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
}
