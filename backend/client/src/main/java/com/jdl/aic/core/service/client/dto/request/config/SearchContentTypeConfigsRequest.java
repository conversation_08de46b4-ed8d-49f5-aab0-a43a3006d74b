package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 搜索内容类型配置请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchContentTypeConfigsRequest {
    
    /**
     * 搜索关键词（匹配名称和描述）
     */
    private String keyword;
    
    /**
     * 是否启用（可选）
     */
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public SearchContentTypeConfigsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param keyword 搜索关键词
     * @param isActive 是否启用
     */
    public SearchContentTypeConfigsRequest(String keyword, Boolean isActive) {
        this.keyword = keyword;
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "SearchContentTypeConfigsRequest{" +
                "keyword='" + keyword + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
