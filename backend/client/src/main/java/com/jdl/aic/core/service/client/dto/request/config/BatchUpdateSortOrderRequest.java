package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * 批量更新配置排序请求（通用）
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchUpdateSortOrderRequest {
    
    /**
     * 配置ID列表
     */
    private List<Long> configIds;
    
    /**
     * 对应的排序值列表
     */
    private List<Integer> sortOrders;
    
    /**
     * 默认构造函数
     */
    public BatchUpdateSortOrderRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param configIds 配置ID列表
     * @param sortOrders 对应的排序值列表
     */
    public BatchUpdateSortOrderRequest(List<Long> configIds, List<Integer> sortOrders) {
        this.configIds = configIds;
        this.sortOrders = sortOrders;
    }
    
    // Getter and Setter methods
    
    public List<Long> getConfigIds() {
        return configIds;
    }
    
    public void setConfigIds(List<Long> configIds) {
        this.configIds = configIds;
    }
    
    public List<Integer> getSortOrders() {
        return sortOrders;
    }
    
    public void setSortOrders(List<Integer> sortOrders) {
        this.sortOrders = sortOrders;
    }
    
    @Override
    public String toString() {
        return "BatchUpdateSortOrderRequest{" +
                "configIds=" + configIds +
                ", sortOrders=" + sortOrders +
                '}';
    }
}
