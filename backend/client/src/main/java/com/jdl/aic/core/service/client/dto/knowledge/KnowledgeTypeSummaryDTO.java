package com.jdl.aic.core.service.client.dto.knowledge;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 知识类型摘要DTO（用于列表展示）
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KnowledgeTypeSummaryDTO {
    
    /**
     * 知识类型ID
     */
    private Long id;
    
    /**
     * 知识类型唯一编码
     */
    private String code;
    
    /**
     * 知识类型名称
     */
    private String name;
    
    /**
     * 知识类型描述
     */
    private String description;
    
    /**
     * 知识类型图标URL
     */
    private String iconUrl;
    
    /**
     * 状态（ACTIVE, INACTIVE, DEPRECATED）
     */
    private String status;
    
    /**
     * 当前用户是否可以创建此类型的知识内容
     */
    private Boolean canUserContribute;
    
    /**
     * 默认构造函数
     */
    public KnowledgeTypeSummaryDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param id ID
     * @param code 编码
     * @param name 名称
     * @param description 描述
     * @param status 状态
     */
    public KnowledgeTypeSummaryDTO(Long id, String code, String name, String description, String status) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.description = description;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getIconUrl() {
        return iconUrl;
    }
    
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Boolean getCanUserContribute() {
        return canUserContribute;
    }
    
    public void setCanUserContribute(Boolean canUserContribute) {
        this.canUserContribute = canUserContribute;
    }
    
    @Override
    public String toString() {
        return "KnowledgeTypeSummaryDTO{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", status='" + status + '\'' +
                ", canUserContribute=" + canUserContribute +
                '}';
    }
}
