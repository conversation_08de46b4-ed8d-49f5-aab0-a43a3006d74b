package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 获取用户评论数量请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetUserCommentCountRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    public GetUserCommentCountRequest() {
    }

    public GetUserCommentCountRequest(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "GetUserCommentCountRequest{" +
                "userId=" + userId +
                '}';
    }
}
