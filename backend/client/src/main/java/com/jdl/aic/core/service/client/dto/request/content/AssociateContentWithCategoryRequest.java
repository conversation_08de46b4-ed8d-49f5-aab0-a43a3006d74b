package com.jdl.aic.core.service.client.dto.request.content;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import jakarta.validation.constraints.NotNull;

/**
 * 关联内容与分类请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AssociateContentWithCategoryRequest {
    
    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private ContentType contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;
    
    /**
     * 默认构造函数
     */
    public AssociateContentWithCategoryRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     */
    public AssociateContentWithCategoryRequest(ContentType contentType, Long contentId, Long categoryId) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.categoryId = categoryId;
    }
    
    // Getter and Setter methods
    
    public ContentType getContentType() {
        return contentType;
    }
    
    public void setContentType(ContentType contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public Long getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    @Override
    public String toString() {
        return "AssociateContentWithCategoryRequest{" +
                "contentType=" + contentType +
                ", contentId=" + contentId +
                ", categoryId=" + categoryId +
                '}';
    }
}
