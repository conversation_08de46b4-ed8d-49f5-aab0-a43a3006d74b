package com.jdl.aic.core.service.client.constants;

/**
 * 配置常量定义
 * 
 * <p>定义了AI社区系统中配置相关的常量，包括配置键名、默认值、配置分组等。
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public final class ConfigConstants {
    
    // ==================== 系统配置分组 ====================
    
    /**
     * 系统基础配置分组
     */
    public static final String CONFIG_GROUP_SYSTEM = "system";
    
    /**
     * 文件存储配置分组
     */
    public static final String CONFIG_GROUP_FILE_STORAGE = "file_storage";
    
    /**
     * 搜索引擎配置分组
     */
    public static final String CONFIG_GROUP_SEARCH = "search";
    
    /**
     * 通知服务配置分组
     */
    public static final String CONFIG_GROUP_NOTIFICATION = "notification";
    
    /**
     * 安全配置分组
     */
    public static final String CONFIG_GROUP_SECURITY = "security";
    
    /**
     * 缓存配置分组
     */
    public static final String CONFIG_GROUP_CACHE = "cache";
    
    /**
     * 数据库配置分组
     */
    public static final String CONFIG_GROUP_DATABASE = "database";
    
    /**
     * AI服务配置分组
     */
    public static final String CONFIG_GROUP_AI_SERVICE = "ai_service";
    
    // ==================== 系统基础配置键 ====================
    
    /**
     * 系统名称
     */
    public static final String CONFIG_SYSTEM_NAME = "system.name";
    
    /**
     * 系统版本
     */
    public static final String CONFIG_SYSTEM_VERSION = "system.version";
    
    /**
     * 系统描述
     */
    public static final String CONFIG_SYSTEM_DESCRIPTION = "system.description";
    
    /**
     * 系统维护模式
     */
    public static final String CONFIG_SYSTEM_MAINTENANCE_MODE = "system.maintenance_mode";
    
    /**
     * 系统时区
     */
    public static final String CONFIG_SYSTEM_TIMEZONE = "system.timezone";
    
    /**
     * 系统语言
     */
    public static final String CONFIG_SYSTEM_LANGUAGE = "system.language";
    
    /**
     * 系统日志级别
     */
    public static final String CONFIG_SYSTEM_LOG_LEVEL = "system.log_level";
    
    // ==================== 文件存储配置键 ====================
    
    /**
     * 文件存储类型
     */
    public static final String CONFIG_FILE_STORAGE_TYPE = "file_storage.type";
    
    /**
     * 文件存储路径
     */
    public static final String CONFIG_FILE_STORAGE_PATH = "file_storage.path";
    
    /**
     * 文件最大大小
     */
    public static final String CONFIG_FILE_MAX_SIZE = "file_storage.max_size";
    
    /**
     * 允许的文件类型
     */
    public static final String CONFIG_FILE_ALLOWED_TYPES = "file_storage.allowed_types";
    
    /**
     * 文件过期天数
     */
    public static final String CONFIG_FILE_EXPIRY_DAYS = "file_storage.expiry_days";
    
    /**
     * 图片压缩质量
     */
    public static final String CONFIG_IMAGE_COMPRESSION_QUALITY = "file_storage.image_compression_quality";
    
    /**
     * 缩略图尺寸
     */
    public static final String CONFIG_THUMBNAIL_SIZE = "file_storage.thumbnail_size";
    
    // ==================== 搜索引擎配置键 ====================
    
    /**
     * 搜索引擎类型
     */
    public static final String CONFIG_SEARCH_ENGINE_TYPE = "search.engine_type";
    
    /**
     * 搜索索引名称
     */
    public static final String CONFIG_SEARCH_INDEX_NAME = "search.index_name";
    
    /**
     * 搜索结果最大数量
     */
    public static final String CONFIG_SEARCH_MAX_RESULTS = "search.max_results";
    
    /**
     * 搜索超时时间
     */
    public static final String CONFIG_SEARCH_TIMEOUT = "search.timeout";
    
    /**
     * 搜索高亮标签
     */
    public static final String CONFIG_SEARCH_HIGHLIGHT_TAG = "search.highlight_tag";
    
    /**
     * 搜索建议数量
     */
    public static final String CONFIG_SEARCH_SUGGESTION_COUNT = "search.suggestion_count";
    
    // ==================== 通知服务配置键 ====================
    
    /**
     * 邮件服务器地址
     */
    public static final String CONFIG_EMAIL_SERVER_HOST = "notification.email.server_host";
    
    /**
     * 邮件服务器端口
     */
    public static final String CONFIG_EMAIL_SERVER_PORT = "notification.email.server_port";
    
    /**
     * 邮件发送者
     */
    public static final String CONFIG_EMAIL_SENDER = "notification.email.sender";
    
    /**
     * 短信服务提供商
     */
    public static final String CONFIG_SMS_PROVIDER = "notification.sms.provider";
    
    /**
     * 推送服务提供商
     */
    public static final String CONFIG_PUSH_PROVIDER = "notification.push.provider";
    
    /**
     * 通知重试次数
     */
    public static final String CONFIG_NOTIFICATION_RETRY_COUNT = "notification.retry_count";
    
    /**
     * 通知批量大小
     */
    public static final String CONFIG_NOTIFICATION_BATCH_SIZE = "notification.batch_size";
    
    // ==================== 安全配置键 ====================
    
    /**
     * JWT密钥
     */
    public static final String CONFIG_JWT_SECRET = "security.jwt.secret";
    
    /**
     * JWT过期时间
     */
    public static final String CONFIG_JWT_EXPIRATION = "security.jwt.expiration";
    
    /**
     * 密码最小长度
     */
    public static final String CONFIG_PASSWORD_MIN_LENGTH = "security.password.min_length";
    
    /**
     * 密码复杂度要求
     */
    public static final String CONFIG_PASSWORD_COMPLEXITY = "security.password.complexity";
    
    /**
     * 登录失败锁定次数
     */
    public static final String CONFIG_LOGIN_LOCK_ATTEMPTS = "security.login.lock_attempts";
    
    /**
     * 登录锁定时间
     */
    public static final String CONFIG_LOGIN_LOCK_DURATION = "security.login.lock_duration";
    
    /**
     * API限流配置
     */
    public static final String CONFIG_API_RATE_LIMIT = "security.api.rate_limit";
    
    // ==================== 缓存配置键 ====================
    
    /**
     * 缓存类型
     */
    public static final String CONFIG_CACHE_TYPE = "cache.type";
    
    /**
     * 缓存服务器地址
     */
    public static final String CONFIG_CACHE_SERVER_HOST = "cache.server_host";
    
    /**
     * 缓存服务器端口
     */
    public static final String CONFIG_CACHE_SERVER_PORT = "cache.server_port";
    
    /**
     * 缓存默认过期时间
     */
    public static final String CONFIG_CACHE_DEFAULT_EXPIRATION = "cache.default_expiration";
    
    /**
     * 缓存最大内存
     */
    public static final String CONFIG_CACHE_MAX_MEMORY = "cache.max_memory";
    
    // ==================== AI服务配置键 ====================
    
    /**
     * AI服务提供商
     */
    public static final String CONFIG_AI_SERVICE_PROVIDER = "ai_service.provider";
    
    /**
     * AI服务API密钥
     */
    public static final String CONFIG_AI_SERVICE_API_KEY = "ai_service.api_key";
    
    /**
     * AI服务端点
     */
    public static final String CONFIG_AI_SERVICE_ENDPOINT = "ai_service.endpoint";
    
    /**
     * AI分析超时时间
     */
    public static final String CONFIG_AI_ANALYSIS_TIMEOUT = "ai_service.analysis_timeout";
    
    /**
     * AI摘要最大长度
     */
    public static final String CONFIG_AI_SUMMARY_MAX_LENGTH = "ai_service.summary_max_length";
    
    // ==================== 配置类型常量 ====================
    
    /**
     * 配置类型：字符串
     */
    public static final String CONFIG_TYPE_STRING = "string";
    
    /**
     * 配置类型：数字
     */
    public static final String CONFIG_TYPE_NUMBER = "number";
    
    /**
     * 配置类型：布尔值
     */
    public static final String CONFIG_TYPE_BOOLEAN = "boolean";
    
    /**
     * 配置类型：JSON
     */
    public static final String CONFIG_TYPE_JSON = "json";
    
    /**
     * 配置类型：数组
     */
    public static final String CONFIG_TYPE_ARRAY = "array";
    
    // ==================== 默认配置值 ====================
    
    /**
     * 默认系统名称
     */
    public static final String DEFAULT_SYSTEM_NAME = "AI Community";
    
    /**
     * 默认系统时区
     */
    public static final String DEFAULT_SYSTEM_TIMEZONE = "Asia/Shanghai";
    
    /**
     * 默认系统语言
     */
    public static final String DEFAULT_SYSTEM_LANGUAGE = "zh-CN";
    
    /**
     * 默认文件最大大小（字节）
     */
    public static final String DEFAULT_FILE_MAX_SIZE = "10485760"; // 10MB
    
    /**
     * 默认文件过期天数
     */
    public static final String DEFAULT_FILE_EXPIRY_DAYS = "30";
    
    /**
     * 默认搜索结果最大数量
     */
    public static final String DEFAULT_SEARCH_MAX_RESULTS = "100";
    
    /**
     * 默认搜索超时时间（毫秒）
     */
    public static final String DEFAULT_SEARCH_TIMEOUT = "5000";
    
    /**
     * 默认通知重试次数
     */
    public static final String DEFAULT_NOTIFICATION_RETRY_COUNT = "3";
    
    /**
     * 默认密码最小长度
     */
    public static final String DEFAULT_PASSWORD_MIN_LENGTH = "8";
    
    /**
     * 默认登录失败锁定次数
     */
    public static final String DEFAULT_LOGIN_LOCK_ATTEMPTS = "5";
    
    /**
     * 默认缓存过期时间（秒）
     */
    public static final String DEFAULT_CACHE_EXPIRATION = "3600";
    
    /**
     * 私有构造函数，防止实例化
     */
    private ConfigConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
