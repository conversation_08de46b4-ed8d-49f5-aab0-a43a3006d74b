package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 检查内容类型和功能类型组合是否已存在请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckSocialFeatureConfigExistsRequest {
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 功能类型
     */
    private String featureType;
    
    /**
     * 排除的配置ID（用于更新时检查）
     */
    private Long excludeId;
    
    /**
     * 默认构造函数
     */
    public CheckSocialFeatureConfigExistsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param featureType 功能类型
     * @param excludeId 排除的配置ID
     */
    public CheckSocialFeatureConfigExistsRequest(String contentType, String featureType, Long excludeId) {
        this.contentType = contentType;
        this.featureType = featureType;
        this.excludeId = excludeId;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getFeatureType() {
        return featureType;
    }
    
    public void setFeatureType(String featureType) {
        this.featureType = featureType;
    }
    
    public Long getExcludeId() {
        return excludeId;
    }
    
    public void setExcludeId(Long excludeId) {
        this.excludeId = excludeId;
    }
    
    @Override
    public String toString() {
        return "CheckSocialFeatureConfigExistsRequest{" +
                "contentType='" + contentType + '\'' +
                ", featureType='" + featureType + '\'' +
                ", excludeId=" + excludeId +
                '}';
    }
}
