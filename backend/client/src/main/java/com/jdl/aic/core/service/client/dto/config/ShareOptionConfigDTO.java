package com.jdl.aic.core.service.client.dto.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 分享选项配置DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShareOptionConfigDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 内容类型编码
     */
    @NotBlank(message = "内容类型编码不能为空")
    private String contentType;
    
    /**
     * 分享类型：internal,wechat,email,link_copy,teams,slack
     */
    @NotBlank(message = "分享类型不能为空")
    private String shareType;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 显示名称
     */
    @NotBlank(message = "显示名称不能为空")
    private String displayName;
    
    /**
     * 图标URL
     */
    private String iconUrl;
    
    /**
     * 排序
     */
    private Integer sortOrder;
    
    /**
     * 分享配置JSON
     */
    private String configJson;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public ShareOptionConfigDTO() {
    }

    /**
     * 基础构造函数
     *
     * @param contentType 内容类型编码
     * @param shareType 分享类型
     * @param displayName 显示名称
     */
    public ShareOptionConfigDTO(String contentType, String shareType, String displayName) {
        this.contentType = contentType;
        this.shareType = shareType;
        this.displayName = displayName;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getShareType() {
        return shareType;
    }
    
    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getIconUrl() {
        return iconUrl;
    }
    
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public String getConfigJson() {
        return configJson;
    }
    
    public void setConfigJson(String configJson) {
        this.configJson = configJson;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public String toString() {
        return "ShareOptionConfigDTO{" +
                "id=" + id +
                ", contentType='" + contentType + '\'' +
                ", shareType='" + shareType + '\'' +
                ", isEnabled=" + isEnabled +
                ", displayName='" + displayName + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", sortOrder=" + sortOrder +
                ", configJson='" + configJson + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        ShareOptionConfigDTO that = (ShareOptionConfigDTO) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (contentType != null ? !contentType.equals(that.contentType) : that.contentType != null) return false;
        if (shareType != null ? !shareType.equals(that.shareType) : that.shareType != null) return false;
        if (displayName != null ? !displayName.equals(that.displayName) : that.displayName != null) return false;
        return isEnabled != null ? isEnabled.equals(that.isEnabled) : that.isEnabled == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (contentType != null ? contentType.hashCode() : 0);
        result = 31 * result + (shareType != null ? shareType.hashCode() : 0);
        result = 31 * result + (displayName != null ? displayName.hashCode() : 0);
        result = 31 * result + (isEnabled != null ? isEnabled.hashCode() : 0);
        return result;
    }
}
