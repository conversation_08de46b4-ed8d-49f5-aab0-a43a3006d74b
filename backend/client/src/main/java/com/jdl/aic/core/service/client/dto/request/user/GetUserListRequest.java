package com.jdl.aic.core.service.client.dto.request.user;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取用户列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetUserListRequest {
    
    /**
     * 部门过滤
     */
    private String department;
    
    /**
     * 活跃状态过滤
     */
    private Boolean isActive;
    
    /**
     * 搜索关键词（用户名、显示名称、邮箱）
     */
    private String search;
    
    /**
     * 默认构造函数
     */
    public GetUserListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param department 部门过滤
     * @param isActive 活跃状态过滤
     * @param search 搜索关键词
     */
    public GetUserListRequest(String department, Boolean isActive, String search) {
        this.department = department;
        this.isActive = isActive;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    @Override
    public String toString() {
        return "GetUserListRequest{" +
                "department='" + department + '\'' +
                ", isActive=" + isActive +
                ", search='" + search + '\'' +
                '}';
    }
}
