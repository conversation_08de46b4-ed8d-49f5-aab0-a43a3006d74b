package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 检查表名是否已被使用请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckContentTypeTableNameExistsRequest {
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 排除的配置ID（用于更新时检查）
     */
    private Long excludeId;
    
    /**
     * 默认构造函数
     */
    public CheckContentTypeTableNameExistsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param tableName 表名
     * @param excludeId 排除的配置ID
     */
    public CheckContentTypeTableNameExistsRequest(String tableName, Long excludeId) {
        this.tableName = tableName;
        this.excludeId = excludeId;
    }
    
    // Getter and Setter methods
    
    public String getTableName() {
        return tableName;
    }
    
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
    
    public Long getExcludeId() {
        return excludeId;
    }
    
    public void setExcludeId(Long excludeId) {
        this.excludeId = excludeId;
    }
    
    @Override
    public String toString() {
        return "CheckContentTypeTableNameExistsRequest{" +
                "tableName='" + tableName + '\'' +
                ", excludeId=" + excludeId +
                '}';
    }
}
