package com.jdl.aic.core.service.client.dto.request.system;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取通知模板列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetNotificationTemplatesRequest {
    
    /**
     * 通知类型过滤
     */
    private String notificationType;
    
    /**
     * 渠道过滤
     */
    private String channel;
    
    /**
     * 语言过滤
     */
    private String languageCode;
    
    /**
     * 启用状态过滤
     */
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public GetNotificationTemplatesRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param notificationType 通知类型过滤
     * @param channel 渠道过滤
     * @param languageCode 语言过滤
     * @param isActive 启用状态过滤
     */
    public GetNotificationTemplatesRequest(String notificationType, String channel, String languageCode, Boolean isActive) {
        this.notificationType = notificationType;
        this.channel = channel;
        this.languageCode = languageCode;
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public String getNotificationType() {
        return notificationType;
    }
    
    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }
    
    public String getChannel() {
        return channel;
    }
    
    public void setChannel(String channel) {
        this.channel = channel;
    }
    
    public String getLanguageCode() {
        return languageCode;
    }
    
    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "GetNotificationTemplatesRequest{" +
                "notificationType='" + notificationType + '\'' +
                ", channel='" + channel + '\'' +
                ", languageCode='" + languageCode + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
