package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.solution.SolutionDTO;
import com.jdl.aic.core.service.client.dto.solution.SolutionStepDTO;
import com.jdl.aic.core.service.client.dto.request.solution.GetSolutionListRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetPopularSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetRecommendedSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetUserSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.SearchSolutionsRequest;

import java.util.List;

/**
 * 场景解决方案服务接口
 * 
 * <p>提供场景解决方案管理功能，包括：
 * <ul>
 *   <li>解决方案的CRUD操作</li>
 *   <li>解决方案步骤管理</li>
 *   <li>解决方案状态管理</li>
 *   <li>解决方案搜索和过滤</li>
 *   <li>解决方案统计和分析</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface SolutionService {
    
    // ==================== 解决方案管理 ====================
    
    /**
     * 获取解决方案列表（分页）
     *
     * @param request 获取解决方案列表请求
     * @return 解决方案列表
     */
    Result<PageResult<SolutionDTO>> getSolutionList(GetSolutionListRequest request);
    
    /**
     * 根据ID获取解决方案详情
     * 
     * @param id 解决方案ID
     * @return 解决方案详情
     */
    Result<SolutionDTO> getSolutionById(Long id);
    
    /**
     * 创建解决方案
     * 
     * @param solution 解决方案信息
     * @return 创建结果
     */
    Result<SolutionDTO> createSolution(SolutionDTO solution);
    
    /**
     * 更新解决方案
     * 
     * @param id 解决方案ID
     * @param solution 解决方案信息
     * @return 更新结果
     */
    Result<SolutionDTO> updateSolution(Long id, SolutionDTO solution);
    
    /**
     * 删除解决方案
     * 
     * @param id 解决方案ID
     * @return 删除结果
     */
    Result<Void> deleteSolution(Long id);
    
    /**
     * 更新解决方案状态
     * 
     * @param id 解决方案ID
     * @param status 新状态（0:草稿, 1:发布, 2:下线）
     * @return 操作结果
     */
    Result<Void> updateSolutionStatus(Long id, Integer status);
    
    /**
     * 批量更新解决方案状态
     * 
     * @param ids 解决方案ID列表
     * @param status 新状态
     * @return 操作结果
     */
    Result<Void> batchUpdateSolutionStatus(List<Long> ids, Integer status);
    
    // ==================== 解决方案步骤管理 ====================
    
    /**
     * 获取解决方案的步骤列表
     * 
     * @param solutionId 解决方案ID
     * @return 步骤列表
     */
    Result<List<SolutionStepDTO>> getSolutionSteps(Long solutionId);
    
    /**
     * 根据ID获取解决方案步骤详情
     * 
     * @param stepId 步骤ID
     * @return 步骤详情
     */
    Result<SolutionStepDTO> getSolutionStepById(Long stepId);
    
    /**
     * 为解决方案添加步骤
     * 
     * @param solutionId 解决方案ID
     * @param step 步骤信息
     * @return 创建结果
     */
    Result<SolutionStepDTO> addSolutionStep(Long solutionId, SolutionStepDTO step);
    
    /**
     * 更新解决方案步骤
     * 
     * @param stepId 步骤ID
     * @param step 步骤信息
     * @return 更新结果
     */
    Result<SolutionStepDTO> updateSolutionStep(Long stepId, SolutionStepDTO step);
    
    /**
     * 删除解决方案步骤
     * 
     * @param stepId 步骤ID
     * @return 删除结果
     */
    Result<Void> deleteSolutionStep(Long stepId);
    
    /**
     * 调整解决方案步骤顺序
     * 
     * @param solutionId 解决方案ID
     * @param stepIds 步骤ID列表（按新顺序排列）
     * @return 操作结果
     */
    Result<Void> reorderSolutionSteps(Long solutionId, List<Long> stepIds);
    
    // ==================== 解决方案统计和分析 ====================
    
    /**
     * 增加解决方案浏览次数
     * 
     * @param id 解决方案ID
     * @return 操作结果
     */
    Result<Void> incrementViewCount(Long id);
    
    /**
     * 增加解决方案点赞次数
     * 
     * @param id 解决方案ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> incrementLikeCount(Long id, Long userId);
    
    /**
     * 取消解决方案点赞
     * 
     * @param id 解决方案ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> decrementLikeCount(Long id, Long userId);
    
    /**
     * 增加解决方案使用次数
     * 
     * @param id 解决方案ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> incrementUseCount(Long id, Long userId);
    
    /**
     * 获取热门解决方案列表
     *
     * @param request 获取热门解决方案请求
     * @return 热门解决方案列表
     */
    Result<PageResult<SolutionDTO>> getPopularSolutions(GetPopularSolutionsRequest request);
    
    /**
     * 获取推荐解决方案列表
     *
     * @param request 获取推荐解决方案请求
     * @return 推荐解决方案列表
     */
    Result<PageResult<SolutionDTO>> getRecommendedSolutions(GetRecommendedSolutionsRequest request);
    
    /**
     * 获取用户创建的解决方案列表
     *
     * @param request 获取用户解决方案请求
     * @return 用户解决方案列表
     */
    Result<PageResult<SolutionDTO>> getUserSolutions(GetUserSolutionsRequest request);
    
    /**
     * 获取解决方案分类统计
     * 
     * @return 分类统计信息
     */
    Result<List<Object>> getSolutionCategoryStats();
    
    /**
     * 搜索解决方案
     *
     * @param request 搜索解决方案请求
     * @return 搜索结果
     */
    Result<PageResult<SolutionDTO>> searchSolutions(SearchSolutionsRequest request);
}
