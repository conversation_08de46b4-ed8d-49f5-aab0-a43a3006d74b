package com.jdl.aic.core.service.client.dto.learning;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学习路径资源关联DTO
 *
 * <p>封装学习路径中包含的学习资源信息，包括资源在路径中的顺序、
 * 是否必修等属性。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@ToString
public class LearningPathResourceDTO {
    
    /**
     * 关联ID
     */
    private Long id;
    
    /**
     * 学习路径资源ID
     */
    private Long learningPathId;

    /**
     * 关联的学习资源ID
     */
    @NotNull(message = "学习资源ID不能为空")
    private Long resourceId;

    /**
     * 在路径中的顺序
     */
    private Integer sequenceOrder;

    /**
     * 阶段名称（如：第一阶段、第二阶段）
     */
    private String stageName;

    /**
     * 预估学习时长（小时）
     */
    private BigDecimal estimatedHours;

    /**
     * 是否可选
     */
    private Boolean isOptional;

    /**
     * 学习建议
     */
    private String notes;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 资源信息
     */
    private List<LearningResourceDTO> resources;
}
