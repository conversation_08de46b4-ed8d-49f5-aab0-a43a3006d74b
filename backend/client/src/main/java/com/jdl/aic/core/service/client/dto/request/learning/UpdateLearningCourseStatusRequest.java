package com.jdl.aic.core.service.client.dto.request.learning;

import jakarta.validation.constraints.NotNull;

/**
 * 更新学习课程状态请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class UpdateLearningCourseStatusRequest {
    
    /**
     * 课程ID
     */
    @NotNull(message = "课程ID不能为空")
    private Long id;
    
    /**
     * 新状态（DRAFT:草稿, PUBLISHED:已发布, ARCHIVED:已归档）
     */
    @NotNull(message = "状态不能为空")
    private String status;
    
    /**
     * 更新用户ID
     */
    private String updatedBy;

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public String toString() {
        return "UpdateLearningCourseStatusRequest{" +
                "id=" + id +
                ", status='" + status + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                '}';
    }
}
