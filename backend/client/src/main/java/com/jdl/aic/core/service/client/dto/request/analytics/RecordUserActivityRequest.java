package com.jdl.aic.core.service.client.dto.request.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 记录用户活动请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RecordUserActivityRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 活动类型
     */
    @NotBlank(message = "活动类型不能为空")
    private String activityType;
    
    /**
     * 活动数据
     */
    private Object activityData;
    
    /**
     * 默认构造函数
     */
    public RecordUserActivityRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param activityType 活动类型
     * @param activityData 活动数据
     */
    public RecordUserActivityRequest(Long userId, String activityType, Object activityData) {
        this.userId = userId;
        this.activityType = activityType;
        this.activityData = activityData;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getActivityType() {
        return activityType;
    }
    
    public void setActivityType(String activityType) {
        this.activityType = activityType;
    }
    
    public Object getActivityData() {
        return activityData;
    }
    
    public void setActivityData(Object activityData) {
        this.activityData = activityData;
    }
    
    @Override
    public String toString() {
        return "RecordUserActivityRequest{" +
                "userId=" + userId +
                ", activityType='" + activityType + '\'' +
                ", activityData=" + activityData +
                '}';
    }
}
