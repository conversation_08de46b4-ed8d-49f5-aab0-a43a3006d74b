package com.jdl.aic.core.service.client.dto.solution;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 解决方案步骤DTO
 * 
 * <p>封装解决方案中单个步骤的详细信息，包括步骤描述、类型、
 * 关联的知识内容以及步骤配置参数。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SolutionStepDTO {
    
    /**
     * 步骤ID
     */
    private Long id;
    
    /**
     * 所属解决方案ID
     */
    @NotNull(message = "解决方案ID不能为空")
    private Long solutionId;
    
    /**
     * 步骤顺序
     */
    @NotNull(message = "步骤顺序不能为空")
    private Integer stepOrder;
    
    /**
     * 步骤标题
     */
    @NotBlank(message = "步骤标题不能为空")
    private String stepTitle;
    
    /**
     * 步骤描述
     */
    private String stepDescription;
    
    /**
     * 步骤类型（knowledge:知识, tool:工具, action:操作）
     */
    private String stepType;
    
    /**
     * 关联的知识ID
     */
    private Long relatedKnowledgeId;
    
    /**
     * 关联的知识标题
     */
    private String relatedKnowledgeTitle;
    
    /**
     * 步骤配置参数（JSON格式）
     */
    private Map<String, Object> stepConfig;
    
    /**
     * 默认构造函数
     */
    public SolutionStepDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param solutionId 解决方案ID
     * @param stepOrder 步骤顺序
     * @param stepTitle 步骤标题
     */
    public SolutionStepDTO(Long solutionId, Integer stepOrder, String stepTitle) {
        this.solutionId = solutionId;
        this.stepOrder = stepOrder;
        this.stepTitle = stepTitle;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getSolutionId() {
        return solutionId;
    }
    
    public void setSolutionId(Long solutionId) {
        this.solutionId = solutionId;
    }
    
    public Integer getStepOrder() {
        return stepOrder;
    }
    
    public void setStepOrder(Integer stepOrder) {
        this.stepOrder = stepOrder;
    }
    
    public String getStepTitle() {
        return stepTitle;
    }
    
    public void setStepTitle(String stepTitle) {
        this.stepTitle = stepTitle;
    }
    
    public String getStepDescription() {
        return stepDescription;
    }
    
    public void setStepDescription(String stepDescription) {
        this.stepDescription = stepDescription;
    }
    
    public String getStepType() {
        return stepType;
    }
    
    public void setStepType(String stepType) {
        this.stepType = stepType;
    }
    
    public Long getRelatedKnowledgeId() {
        return relatedKnowledgeId;
    }
    
    public void setRelatedKnowledgeId(Long relatedKnowledgeId) {
        this.relatedKnowledgeId = relatedKnowledgeId;
    }
    
    public String getRelatedKnowledgeTitle() {
        return relatedKnowledgeTitle;
    }
    
    public void setRelatedKnowledgeTitle(String relatedKnowledgeTitle) {
        this.relatedKnowledgeTitle = relatedKnowledgeTitle;
    }
    
    public Map<String, Object> getStepConfig() {
        return stepConfig;
    }
    
    public void setStepConfig(Map<String, Object> stepConfig) {
        this.stepConfig = stepConfig;
    }
    
    @Override
    public String toString() {
        return "SolutionStepDTO{" +
                "id=" + id +
                ", solutionId=" + solutionId +
                ", stepOrder=" + stepOrder +
                ", stepTitle='" + stepTitle + '\'' +
                ", stepDescription='" + stepDescription + '\'' +
                ", stepType='" + stepType + '\'' +
                ", relatedKnowledgeId=" + relatedKnowledgeId +
                ", relatedKnowledgeTitle='" + relatedKnowledgeTitle + '\'' +
                ", stepConfig=" + stepConfig +
                '}';
    }
}
