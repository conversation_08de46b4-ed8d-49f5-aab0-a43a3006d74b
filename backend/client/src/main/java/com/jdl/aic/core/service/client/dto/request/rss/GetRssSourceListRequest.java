package com.jdl.aic.core.service.client.dto.request.rss;

import jakarta.validation.constraints.Size;

/**
 * 获取RSS源列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetRssSourceListRequest {
    
    /**
     * 搜索关键词（匹配名称或描述）
     */
    @Size(max = 100, message = "搜索关键词长度不能超过100个字符")
    private String search;
    
    /**
     * RSS源分类过滤
     */
    @Size(max = 50, message = "分类长度不能超过50个字符")
    private String category;
    
    /**
     * 源类型过滤（0:官方, 1:用户订阅）
     */
    private Integer type;
    
    /**
     * 源状态过滤（0:活跃, 1:暂停, 2:失败）
     */
    private Integer status;
    
    /**
     * 所有者ID过滤（用于查询特定用户的RSS源）
     */
    private String ownerId;
    
    /**
     * 是否只查询活跃状态的RSS源
     */
    private Boolean activeOnly;
    
    /**
     * 默认构造函数
     */
    public GetRssSourceListRequest() {
    }

    /**
     * 构造函数
     *
     * @param search 搜索关键词
     * @param category RSS源分类
     * @param type 源类型
     * @param status 源状态
     */
    public GetRssSourceListRequest(String search, String category, Integer type, Integer status) {
        this.search = search;
        this.category = category;
        this.type = type;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getOwnerId() {
        return ownerId;
    }
    
    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }
    
    public Boolean getActiveOnly() {
        return activeOnly;
    }
    
    public void setActiveOnly(Boolean activeOnly) {
        this.activeOnly = activeOnly;
    }
    
    @Override
    public String toString() {
        return "GetRssSourceListRequest{" +
                "search='" + search + '\'' +
                ", category='" + category + '\'' +
                ", type=" + type +
                ", status=" + status +
                ", ownerId='" + ownerId + '\'' +
                ", activeOnly=" + activeOnly +
                '}';
    }
}
