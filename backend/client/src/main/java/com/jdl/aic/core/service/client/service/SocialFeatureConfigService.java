package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.SocialFeatureConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;

import java.util.List;

/**
 * 社交功能配置管理服务接口
 * 
 * <p>提供社交功能配置管理功能，包括：
 * <ul>
 *   <li>社交功能配置的CRUD操作</li>
 *   <li>按内容类型和功能类型管理配置</li>
 *   <li>配置状态管理</li>
 *   <li>批量操作支持</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface SocialFeatureConfigService {
    
    // ==================== 社交功能配置管理 ====================

    /**
     * 获取社交功能配置列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询条件请求
     * @return 社交功能配置列表
     */
    Result<PageResult<SocialFeatureConfigDTO>> getConfigList(
            PageRequest pageRequest,
            GetSocialFeatureConfigListRequest request);

    /**
     * 根据ID获取社交功能配置详情
     *
     * @param id 配置ID
     * @return 社交功能配置详情
     */
    Result<SocialFeatureConfigDTO> getConfigById(Long id);

    /**
     * 根据内容类型和功能类型获取配置
     *
     * @param request 查询请求
     * @return 社交功能配置详情
     */
    Result<SocialFeatureConfigDTO> getConfigByContentAndFeature(GetSocialFeatureConfigByContentAndFeatureRequest request);

    /**
     * 创建社交功能配置
     * 
     * @param config 社交功能配置信息
     * @return 创建结果
     */
    Result<SocialFeatureConfigDTO> createConfig(SocialFeatureConfigDTO config);

    /**
     * 更新社交功能配置信息
     * 
     * @param id 配置ID
     * @param config 社交功能配置信息
     * @return 更新结果
     */
    Result<SocialFeatureConfigDTO> updateConfig(Long id, SocialFeatureConfigDTO config);

    /**
     * 删除社交功能配置
     * 
     * @param id 配置ID
     * @return 删除结果
     */
    Result<Void> deleteConfig(Long id);

    /**
     * 启用/禁用社交功能配置
     *
     * @param request 启用/禁用请求
     * @return 操作结果
     */
    Result<Void> toggleConfigStatus(ToggleSocialFeatureConfigStatusRequest request);

    /**
     * 根据内容类型获取所有功能配置
     *
     * @param request 查询请求
     * @return 功能配置列表
     */
    Result<List<SocialFeatureConfigDTO>> getConfigsByContentType(GetConfigsByContentTypeRequest request);

    /**
     * 根据功能类型获取所有配置
     *
     * @param request 查询请求
     * @return 配置列表
     */
    Result<List<SocialFeatureConfigDTO>> getConfigsByFeatureType(GetSocialFeatureConfigsByFeatureTypeRequest request);

    /**
     * 获取所有启用的社交功能配置
     * 
     * @return 启用的社交功能配置列表
     */
    Result<List<SocialFeatureConfigDTO>> getAllEnabledConfigs();

    /**
     * 批量更新内容类型的功能状态
     * 
     * @param contentType 内容类型
     * @param isEnabled 是否启用
     * @return 操作结果
     */
    Result<Void> batchUpdateStatusByContentType(String contentType, Boolean isEnabled);

    /**
     * 批量更新功能类型的状态
     * 
     * @param featureType 功能类型
     * @param isEnabled 是否启用
     * @return 操作结果
     */
    Result<Void> batchUpdateStatusByFeatureType(String featureType, Boolean isEnabled);

    /**
     * 检查内容类型和功能类型组合是否已存在
     * 
     * @param contentType 内容类型
     * @param featureType 功能类型
     * @param excludeId 排除的配置ID（用于更新时检查）
     * @return 是否存在
     */
    Result<Boolean> checkConfigExists(String contentType, String featureType, Long excludeId);

    /**
     * 为内容类型初始化默认社交功能配置
     * 
     * @param contentType 内容类型
     * @return 操作结果
     */
    Result<List<SocialFeatureConfigDTO>> initDefaultConfigs(String contentType);

    /**
     * 获取支持的功能类型列表
     * 
     * @return 功能类型列表
     */
    Result<List<String>> getSupportedFeatureTypes();

    /**
     * 复制配置到新的内容类型
     * 
     * @param sourceContentType 源内容类型
     * @param targetContentType 目标内容类型
     * @return 操作结果
     */
    Result<List<SocialFeatureConfigDTO>> copyConfigsToContentType(String sourceContentType, String targetContentType);
}
