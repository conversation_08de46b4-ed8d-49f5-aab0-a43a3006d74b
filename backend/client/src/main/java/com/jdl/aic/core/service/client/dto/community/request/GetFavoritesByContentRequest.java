package com.jdl.aic.core.service.client.dto.community.request;

import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.constraints.NotNull;

/**
 * 根据内容获取收藏列表请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetFavoritesByContentRequest {

    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 分页请求
     */
    @NotNull(message = "分页参数不能为空")
    private PageRequest pageRequest;

    public GetFavoritesByContentRequest() {
    }

    public GetFavoritesByContentRequest(Integer contentType, Long contentId, PageRequest pageRequest) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.pageRequest = pageRequest;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public PageRequest getPageRequest() {
        return pageRequest;
    }

    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }

    @Override
    public String toString() {
        return "GetFavoritesByContentRequest{" +
                "contentType=" + contentType +
                ", contentId=" + contentId +
                ", pageRequest=" + pageRequest +
                '}';
    }
}
