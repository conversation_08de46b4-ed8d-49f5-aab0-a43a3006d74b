package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;

import java.util.List;
import java.util.Map;

/**
 * Portal推荐服务接口
 * 
 * <p>提供Portal端个性化推荐功能，包括：
 * <ul>
 *   <li>内容个性化推荐</li>
 *   <li>用户智能匹配</li>
 *   <li>学习路径推荐</li>
 *   <li>热门趋势推荐</li>
 *   <li>推荐算法优化</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalRecommendationService {
    
    // ==================== 内容个性化推荐 ====================
    
    /**
     * 获取个性化内容推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @param excludeViewed 是否排除已浏览内容
     * @return 个性化推荐内容列表
     */
    Result<PageResult<Object>> getPersonalizedContentRecommendations(
            Long userId,
            PageRequest pageRequest,
            String contentType,
            Boolean excludeViewed);
    
    /**
     * 获取基于协同过滤的推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型
     * @return 协同过滤推荐列表
     */
    Result<PageResult<Object>> getCollaborativeFilteringRecommendations(
            Long userId,
            PageRequest pageRequest,
            String contentType);
    
    /**
     * 获取基于内容的推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型
     * @param basedOnContent 基于的内容ID
     * @return 基于内容的推荐列表
     */
    Result<PageResult<Object>> getContentBasedRecommendations(
            Long userId,
            PageRequest pageRequest,
            String contentType,
            Long basedOnContent);
    
    /**
     * 获取相似内容推荐
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param pageRequest 分页请求
     * @param userId 用户ID（用于个性化调整）
     * @return 相似内容推荐列表
     */
    Result<PageResult<Object>> getSimilarContentRecommendations(
            String contentType,
            Long contentId,
            PageRequest pageRequest,
            Long userId);
    
    /**
     * 获取热门内容推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @param timeRange 时间范围（day, week, month）
     * @return 热门内容推荐列表
     */
    Result<PageResult<Object>> getTrendingContentRecommendations(
            Long userId,
            PageRequest pageRequest,
            String contentType,
            String timeRange);
    
    /**
     * 获取新内容推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @param hours 新内容时间范围（小时）
     * @return 新内容推荐列表
     */
    Result<PageResult<Object>> getNewContentRecommendations(
            Long userId,
            PageRequest pageRequest,
            String contentType,
            Integer hours);
    
    // ==================== 用户智能匹配 ====================
    
    /**
     * 获取相似用户推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param similarityType 相似度类型（interest, behavior, profile）
     * @return 相似用户推荐列表
     */
    Result<PageResult<Object>> getSimilarUserRecommendations(
            Long userId,
            PageRequest pageRequest,
            String similarityType);
    
    /**
     * 获取专家用户推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param expertiseArea 专业领域
     * @return 专家用户推荐列表
     */
    Result<PageResult<Object>> getExpertUserRecommendations(
            Long userId,
            PageRequest pageRequest,
            String expertiseArea);
    
    /**
     * 获取活跃用户推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param activityType 活跃类型（posting, commenting, sharing）
     * @param days 统计天数
     * @return 活跃用户推荐列表
     */
    Result<PageResult<Object>> getActiveUserRecommendations(
            Long userId,
            PageRequest pageRequest,
            String activityType,
            Integer days);
    
    /**
     * 获取团队成员推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param department 部门过滤
     * @return 团队成员推荐列表
     */
    Result<PageResult<Object>> getTeamMemberRecommendations(
            Long userId,
            PageRequest pageRequest,
            String department);
    
    // ==================== 学习路径推荐 ====================
    
    /**
     * 获取个性化学习路径推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param skillLevel 技能水平（beginner, intermediate, advanced）
     * @param learningGoal 学习目标
     * @return 学习路径推荐列表
     */
    Result<PageResult<Object>> getPersonalizedLearningPathRecommendations(
            Long userId,
            PageRequest pageRequest,
            String skillLevel,
            String learningGoal);
    
    /**
     * 获取下一步学习推荐
     * 
     * @param userId 用户ID
     * @param currentResourceId 当前学习资源ID
     * @param pageRequest 分页请求
     * @return 下一步学习推荐列表
     */
    Result<PageResult<Object>> getNextStepLearningRecommendations(
            Long userId,
            Long currentResourceId,
            PageRequest pageRequest);
    
    /**
     * 获取技能补强推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param skillGaps 技能缺口列表
     * @return 技能补强推荐列表
     */
    Result<PageResult<Object>> getSkillGapRecommendations(
            Long userId,
            PageRequest pageRequest,
            List<String> skillGaps);
    
    /**
     * 获取学习伙伴推荐
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param learningPath 学习路径ID
     * @return 学习伙伴推荐列表
     */
    Result<PageResult<Object>> getLearningPartnerRecommendations(
            Long userId,
            PageRequest pageRequest,
            Long learningPath);
    
    // ==================== 智能搜索推荐 ====================
    
    /**
     * 获取搜索建议推荐
     * 
     * @param userId 用户ID
     * @param query 搜索查询
     * @param maxSuggestions 最大建议数量
     * @return 搜索建议列表
     */
    Result<List<String>> getSearchSuggestionRecommendations(
            Long userId,
            String query,
            Integer maxSuggestions);
    
    /**
     * 获取个性化搜索推荐
     * 
     * @param userId 用户ID
     * @param maxRecommendations 最大推荐数量
     * @return 个性化搜索推荐列表
     */
    Result<List<String>> getPersonalizedSearchRecommendations(Long userId, Integer maxRecommendations);
    
    /**
     * 获取热门搜索推荐
     * 
     * @param userId 用户ID
     * @param maxRecommendations 最大推荐数量
     * @param timeRange 时间范围
     * @return 热门搜索推荐列表
     */
    Result<List<String>> getTrendingSearchRecommendations(
            Long userId,
            Integer maxRecommendations,
            String timeRange);
    
    // ==================== 推荐反馈和优化 ====================
    
    /**
     * 记录推荐反馈
     * 
     * @param userId 用户ID
     * @param recommendationType 推荐类型
     * @param recommendedItemId 推荐项ID
     * @param feedbackType 反馈类型（like, dislike, click, ignore）
     * @param feedbackValue 反馈值
     * @return 记录结果
     */
    Result<Void> recordRecommendationFeedback(
            Long userId,
            String recommendationType,
            Long recommendedItemId,
            String feedbackType,
            Object feedbackValue);
    
    /**
     * 更新用户兴趣模型
     * 
     * @param userId 用户ID
     * @param interactionData 交互数据
     * @return 更新结果
     */
    Result<Void> updateUserInterestModel(Long userId, Object interactionData);
    
    /**
     * 获取用户兴趣画像
     * 
     * @param userId 用户ID
     * @return 用户兴趣画像
     */
    Result<Object> getUserInterestProfile(Long userId);
    
    /**
     * 重新计算用户推荐
     * 
     * @param userId 用户ID
     * @param recommendationType 推荐类型
     * @return 重新计算结果
     */
    Result<Void> recalculateUserRecommendations(Long userId, String recommendationType);
    
    /**
     * 获取推荐解释
     * 
     * @param userId 用户ID
     * @param recommendationType 推荐类型
     * @param recommendedItemId 推荐项ID
     * @return 推荐解释
     */
    Result<Object> getRecommendationExplanation(
            Long userId,
            String recommendationType,
            Long recommendedItemId);
    
    /**
     * 设置推荐偏好
     * 
     * @param userId 用户ID
     * @param preferences 推荐偏好设置
     * @return 设置结果
     */
    Result<Object> setRecommendationPreferences(Long userId, Map<String, Object> preferences);
    
    /**
     * 获取推荐偏好
     * 
     * @param userId 用户ID
     * @return 推荐偏好设置
     */
    Result<Map<String, Object>> getRecommendationPreferences(Long userId);
    
    /**
     * 获取推荐统计
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 推荐统计信息
     */
    Result<Object> getRecommendationStatistics(Long userId, Integer days);
    
    /**
     * 清除用户推荐缓存
     * 
     * @param userId 用户ID
     * @param recommendationType 推荐类型（null表示全部）
     * @return 清除结果
     */
    Result<Void> clearUserRecommendationCache(Long userId, String recommendationType);
}
