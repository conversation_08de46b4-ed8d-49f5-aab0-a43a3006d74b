package com.jdl.aic.core.service.portal.client;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.enrollment.UserCourseEnrollmentDTO;
import com.jdl.aic.core.service.client.dto.request.enrollment.GetUserEnrollmentListRequest;
import com.jdl.aic.core.service.client.dto.request.enrollment.UpdateEnrollmentProgressRequest;
import com.jdl.aic.core.service.client.dto.request.enrollment.UpdateEnrollmentStatusRequest;

import java.util.List;

/**
 * 用户课程报名服务接口
 * 
 * <p>提供用户课程报名管理功能，包括：
 * <ul>
 *   <li>用户课程报名的CRUD操作</li>
 *   <li>学习进度跟踪和更新</li>
 *   <li>报名状态管理</li>
 *   <li>学习统计和分析</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface UserCourseEnrollmentService {
    
    // ==================== 报名管理 ====================

    /**
     * 获取用户报名列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 报名列表
     */
    Result<PageResult<UserCourseEnrollmentDTO>> getUserEnrollmentList(
            PageRequest pageRequest,
            GetUserEnrollmentListRequest request);

    /**
     * 根据ID获取报名详情
     * 
     * @param id 报名ID
     * @return 报名详情
     */
    Result<UserCourseEnrollmentDTO> getEnrollmentById(Long id);

    /**
     * 创建课程报名
     * 
     * @param enrollment 报名信息
     * @return 创建结果
     */
    Result<UserCourseEnrollmentDTO> createEnrollment(UserCourseEnrollmentDTO enrollment);

    /**
     * 更新报名信息
     * 
     * @param id 报名ID
     * @param enrollment 报名信息
     * @return 更新结果
     */
    Result<UserCourseEnrollmentDTO> updateEnrollment(Long id, UserCourseEnrollmentDTO enrollment);

    /**
     * 删除报名记录
     * 
     * @param id 报名ID
     * @return 删除结果
     */
    Result<Void> deleteEnrollment(Long id);

    // ==================== 状态管理 ====================

    /**
     * 更新报名状态
     *
     * @param request 更新状态请求参数
     * @return 操作结果
     */
    Result<Void> updateEnrollmentStatus(UpdateEnrollmentStatusRequest request);

    /**
     * 更新学习进度
     *
     * @param request 更新进度请求参数
     * @return 操作结果
     */
    Result<Void> updateEnrollmentProgress(UpdateEnrollmentProgressRequest request);

    // ==================== 业务查询 ====================

    /**
     * 检查用户是否已报名指定课程
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否已报名
     */
    Result<Boolean> isUserEnrolledInCourse(Long userId, Long courseId);

    /**
     * 获取用户在指定课程的报名信息
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 报名信息
     */
    Result<UserCourseEnrollmentDTO> getUserCourseEnrollment(Long userId, Long courseId);

    /**
     * 获取用户的所有报名课程
     * 
     * @param userId 用户ID
     * @return 报名课程列表
     */
    Result<List<UserCourseEnrollmentDTO>> getUserEnrollments(Long userId);

    /**
     * 获取课程的所有报名用户
     * 
     * @param courseId 课程ID
     * @return 报名用户列表
     */
    Result<List<UserCourseEnrollmentDTO>> getCourseEnrollments(Long courseId);

    /**
     * 获取用户正在学习的课程
     * 
     * @param userId 用户ID
     * @return 正在学习的课程列表
     */
    Result<List<UserCourseEnrollmentDTO>> getUserInProgressCourses(Long userId);

    /**
     * 获取用户已完成的课程
     * 
     * @param userId 用户ID
     * @return 已完成的课程列表
     */
    Result<List<UserCourseEnrollmentDTO>> getUserCompletedCourses(Long userId);

    // ==================== 统计分析 ====================

    /**
     * 获取用户学习统计信息
     * 
     * @param userId 用户ID
     * @return 学习统计信息
     */
    Result<UserCourseEnrollmentDTO> getUserLearningStats(Long userId);

    /**
     * 获取课程报名统计信息
     * 
     * @param courseId 课程ID
     * @return 课程报名统计信息
     */
    Result<UserCourseEnrollmentDTO> getCourseEnrollmentStats(Long courseId);

    /**
     * 批量更新学习时长
     * 
     * @param enrollmentIds 报名ID列表
     * @param additionalHours 增加的学习时长
     * @return 操作结果
     */
    Result<Void> batchUpdateStudyHours(List<Long> enrollmentIds, Double additionalHours);
}
