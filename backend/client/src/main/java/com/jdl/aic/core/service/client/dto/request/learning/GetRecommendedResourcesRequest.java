package com.jdl.aic.core.service.client.dto.request.learning;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 获取推荐学习资源请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetRecommendedResourcesRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 分页请求参数
     */
    @NotNull(message = "分页请求参数不能为空")
    @Valid
    private PageRequest pageRequest;
    
    /**
     * 分类过滤
     */
    private String category;
    
    /**
     * 默认构造函数
     */
    public GetRecommendedResourcesRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求参数
     * @param category 分类过滤
     */
    public GetRecommendedResourcesRequest(Long userId, PageRequest pageRequest, String category) {
        this.userId = userId;
        this.pageRequest = pageRequest;
        this.category = category;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public PageRequest getPageRequest() {
        return pageRequest;
    }
    
    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    @Override
    public String toString() {
        return "GetRecommendedResourcesRequest{" +
                "userId=" + userId +
                ", pageRequest=" + pageRequest +
                ", category='" + category + '\'' +
                '}';
    }
}
