package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 检查内容类型编码是否已存在请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckContentTypeCodeExistsRequest {
    
    /**
     * 内容类型编码
     */
    private String code;
    
    /**
     * 排除的配置ID（用于更新时检查）
     */
    private Long excludeId;
    
    /**
     * 默认构造函数
     */
    public CheckContentTypeCodeExistsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param code 内容类型编码
     * @param excludeId 排除的配置ID
     */
    public CheckContentTypeCodeExistsRequest(String code, Long excludeId) {
        this.code = code;
        this.excludeId = excludeId;
    }
    
    // Getter and Setter methods
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public Long getExcludeId() {
        return excludeId;
    }
    
    public void setExcludeId(Long excludeId) {
        this.excludeId = excludeId;
    }
    
    @Override
    public String toString() {
        return "CheckContentTypeCodeExistsRequest{" +
                "code='" + code + '\'' +
                ", excludeId=" + excludeId +
                '}';
    }
}
