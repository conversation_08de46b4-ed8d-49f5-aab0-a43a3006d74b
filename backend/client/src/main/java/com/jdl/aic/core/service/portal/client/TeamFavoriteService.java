package com.jdl.aic.core.service.portal.client;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.TeamFavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.request.AddTeamFavoriteRequest;
import com.jdl.aic.core.service.client.dto.community.request.CheckTeamFavoriteRequest;
import com.jdl.aic.core.service.client.dto.community.request.GetUserTeamFavoritesRequest;
import com.jdl.aic.core.service.client.dto.community.request.RemoveTeamFavoriteRequest;

import java.util.List;

/**
 * 团队收藏服务接口
 *
 * <p>提供团队收藏相关的业务功能，包括：
 * <ul>
 *   <li>团队收藏的添加和取消</li>
 *   <li>用户团队收藏列表查询</li>
 *   <li>团队收藏统计和分析</li>
 *   <li>团队收藏状态管理</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface TeamFavoriteService {

    // ==================== 团队收藏基本操作 ====================

    /**
     * 添加团队收藏
     *
     * @param request 添加团队收藏请求参数
     * @return 收藏结果
     */
    Result<TeamFavoriteDTO> addTeamFavorite(AddTeamFavoriteRequest request);

    /**
     * 取消团队收藏
     *
     * @param request 取消团队收藏请求参数
     * @return 操作结果
     */
    Result<Void> removeTeamFavorite(RemoveTeamFavoriteRequest request);

    /**
     * 切换团队收藏状态（如果已收藏则取消，如果未收藏则添加）
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 操作结果，包含当前收藏状态
     */
    Result<Boolean> toggleTeamFavorite(Long userId, Long teamId);

    /**
     * 检查用户是否已收藏指定团队
     *
     * @param request 检查团队收藏状态请求参数
     * @return 收藏状态
     */
    Result<Boolean> isTeamFavorited(CheckTeamFavoriteRequest request);

    // ==================== 团队收藏查询 ====================

    /**
     * 获取用户团队收藏列表（分页）
     *
     * @param request 获取用户团队收藏列表请求参数
     * @return 收藏列表
     */
    Result<PageResult<TeamFavoriteDTO>> getUserTeamFavorites(GetUserTeamFavoritesRequest request);

    /**
     * 获取用户最近收藏的团队
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近收藏团队列表
     */
    Result<List<TeamFavoriteDTO>> getRecentTeamFavorites(Long userId, Integer limit);

    /**
     * 获取团队的收藏用户列表
     *
     * @param teamId 团队ID
     * @param limit 限制数量
     * @return 收藏该团队的用户列表
     */
    Result<List<TeamFavoriteDTO>> getTeamFavoriteUsers(Long teamId, Integer limit);

    /**
     * 根据收藏ID获取团队收藏详情
     *
     * @param favoriteId 收藏ID
     * @return 团队收藏详情
     */
    Result<TeamFavoriteDTO> getTeamFavoriteById(Long favoriteId);

    // ==================== 团队收藏统计 ====================

    /**
     * 统计用户收藏的团队数量
     *
     * @param userId 用户ID
     * @return 收藏团队数量
     */
    Result<Integer> countUserTeamFavorites(Long userId);

    /**
     * 统计团队被收藏的次数
     *
     * @param teamId 团队ID
     * @return 被收藏次数
     */
    Result<Integer> countTeamFavorites(Long teamId);

    /**
     * 批量统计团队被收藏次数
     *
     * @param teamIds 团队ID列表
     * @return 团队收藏次数映射
     */
    Result<List<Object>> getTeamFavoriteCountBatch(List<Long> teamIds);

    /**
     * 获取热门收藏团队列表
     *
     * @param limit 限制数量
     * @param days 统计天数（可选）
     * @return 热门团队列表
     */
    Result<List<Object>> getPopularTeams(Integer limit, Integer days);

    // ==================== 团队收藏批量操作 ====================

    /**
     * 批量添加团队收藏
     *
     * @param userId 用户ID
     * @param teamIds 团队ID列表
     * @return 批量操作结果
     */
    Result<List<TeamFavoriteDTO>> batchAddTeamFavorites(Long userId, List<Long> teamIds);

    /**
     * 批量取消团队收藏
     *
     * @param userId 用户ID
     * @param teamIds 团队ID列表
     * @return 批量操作结果
     */
    Result<Void> batchRemoveTeamFavorites(Long userId, List<Long> teamIds);

    /**
     * 批量取消团队收藏（根据收藏ID）
     *
     * @param userId 用户ID
     * @param favoriteIds 收藏ID列表
     * @return 批量操作结果
     */
    Result<Void> batchRemoveTeamFavoritesByIds(Long userId, List<Long> favoriteIds);

    // ==================== 团队收藏管理 ====================

    /**
     * 清理用户已删除的团队收藏记录
     *
     * @param userId 用户ID
     * @param days 清理天数前的记录
     * @return 清理结果
     */
    Result<Integer> cleanupDeletedTeamFavorites(Long userId, Integer days);

    /**
     * 恢复已删除的团队收藏
     *
     * @param userId 用户ID
     * @param favoriteId 收藏ID
     * @return 恢复结果
     */
    Result<TeamFavoriteDTO> restoreTeamFavorite(Long userId, Long favoriteId);

    // ==================== 团队收藏分析 ====================

    /**
     * 获取用户团队收藏趋势
     *
     * @param userId 用户ID
     * @param days 统计天数
     * @return 收藏趋势数据
     */
    Result<Object> getUserTeamFavoriteTrend(Long userId, Integer days);

    /**
     * 获取用户团队收藏偏好分析
     *
     * @param userId 用户ID
     * @return 收藏偏好数据
     */
    Result<Object> getUserTeamFavoritePreference(Long userId);

    /**
     * 导出用户团队收藏数据
     *
     * @param userId 用户ID
     * @param format 导出格式（excel, csv等）
     * @return 导出结果
     */
    Result<Object> exportUserTeamFavorites(Long userId, String format);

    // ==================== 团队收藏推荐 ====================

    /**
     * 基于用户收藏历史推荐相关团队
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐团队列表
     */
    Result<List<Object>> getRecommendedTeams(Long userId, Integer limit);

    /**
     * 获取相似收藏偏好的用户
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 相似用户列表
     */
    Result<List<Object>> getSimilarUsers(Long userId, Integer limit);

    /**
     * 获取用户可能感兴趣的团队收藏
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐收藏列表
     */
    Result<List<Object>> getRecommendedTeamFavorites(Long userId, Integer limit);
}
