package com.jdl.aic.core.service.portal.client;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.team.TeamDTO;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamListRequest;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamTreeRequest;
import com.jdl.aic.core.service.client.dto.request.team.ToggleTeamStatusRequest;
import com.jdl.aic.core.service.client.dto.request.team.MoveTeamToParentRequest;
import com.jdl.aic.core.service.client.dto.team.TeamStatisticsDTO;
import java.util.List;

/**
 * 团队管理服务接口
 * 
 * <p>提供团队管理功能，包括：
 * <ul>
 *   <li>团队的CRUD操作和层级管理</li>
 *   <li>团队树形结构管理</li>
 *   <li>团队移动操作</li>
 *   <li>团队状态管理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface TeamDataService {
    
    // ==================== 团队管理 ====================

    /**
     * 获取团队列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 团队列表
     */
    Result<PageResult<TeamDTO>> getTeamList(
            PageRequest pageRequest,
            GetTeamListRequest request);

    /**
     * 获取团队树形结构
     *
     * @param request 查询请求参数
     * @return 团队树
     */
    Result<List<TeamDTO>> getTeamTree(GetTeamTreeRequest request);

    /**
     * 根据ID获取团队详情
     * 
     * @param id 团队ID
     * @return 团队详情
     */
    Result<TeamDTO> getTeamById(Long id);

    /**
     * 创建团队
     * 
     * @param team 团队信息
     * @return 创建结果
     */
    Result<TeamDTO> createTeam(TeamDTO team);

    /**
     * 更新团队信息
     * 
     * @param id 团队ID
     * @param team 团队信息
     * @return 更新结果
     */
    Result<TeamDTO> updateTeam(Long id, TeamDTO team);

    /**
     * 删除团队
     * 
     * @param id 团队ID
     * @return 删除结果
     */
    Result<Void> deleteTeam(Long id);

    /**
     * 启用/禁用团队
     *
     * @param request 切换状态请求参数
     * @return 操作结果
     */
    Result<Void> toggleTeamStatus(ToggleTeamStatusRequest request);

    /**
     * 移动团队到新的父团队下
     *
     * @param request 移动团队请求参数
     * @return 操作结果
     */
    Result<Void> moveTeamToParent(MoveTeamToParentRequest request);

    /**
     * 获取所有活跃团队列表
     * 
     * @return 活跃团队列表
     */
    Result<List<TeamDTO>> getActiveTeams();

    /**
     * 根据名称搜索团队
     * 
     * @param name 团队名称关键词
     * @return 匹配的团队列表
     */
    Result<List<TeamDTO>> searchTeamsByName(String name);

    /**
     * 获取根团队列表（没有父团队的团队）
     * 
     * @param isActive 是否只获取活跃团队
     * @return 根团队列表
     */
    Result<List<TeamDTO>> getRootTeams(Boolean isActive);

    /**
     * 获取指定团队的子团队列表
     * 
     * @param parentId 父团队ID
     * @param isActive 是否只获取活跃团队
     * @return 子团队列表
     */
    Result<List<TeamDTO>> getChildTeams(Long parentId, Boolean isActive);
    
    /**
     * 获取团队统计信息，包括团队人数和团队文章总数
     *
     * @param teamId 团队ID
     * @return 团队统计信息
     */
    Result<TeamStatisticsDTO> getTeamStatistics(Long teamId);
}
