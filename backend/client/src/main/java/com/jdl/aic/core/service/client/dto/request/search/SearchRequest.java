package com.jdl.aic.core.service.client.dto.request.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.system.SearchFilterDTO;
import jakarta.validation.constraints.NotBlank;

/**
 * 全文搜索请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchRequest {
    
    /**
     * 搜索关键词
     */
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;
    
    /**
     * 搜索过滤条件
     */
    private SearchFilterDTO filters;
    
    /**
     * 搜索用户ID（用于个性化）
     */
    private Long userId;
    
    /**
     * 默认构造函数
     */
    public SearchRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param keyword 搜索关键词
     * @param filters 搜索过滤条件
     * @param userId 搜索用户ID
     */
    public SearchRequest(String keyword, SearchFilterDTO filters, Long userId) {
        this.keyword = keyword;
        this.filters = filters;
        this.userId = userId;
    }
    
    // Getter and Setter methods
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public SearchFilterDTO getFilters() {
        return filters;
    }
    
    public void setFilters(SearchFilterDTO filters) {
        this.filters = filters;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Override
    public String toString() {
        return "SearchRequest{" +
                "keyword='" + keyword + '\'' +
                ", filters=" + filters +
                ", userId=" + userId +
                '}';
    }
}
