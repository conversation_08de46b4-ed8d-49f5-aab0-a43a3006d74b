package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 取消点赞请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class RemoveLikeRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    public RemoveLikeRequest() {
    }

    public RemoveLikeRequest(Long userId, Integer contentType, Long contentId) {
        this.userId = userId;
        this.contentType = contentType;
        this.contentId = contentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    @Override
    public String toString() {
        return "RemoveLikeRequest{" +
                "userId=" + userId +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                '}';
    }
}
