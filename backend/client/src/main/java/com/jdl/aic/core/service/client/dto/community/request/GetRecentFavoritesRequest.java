package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 获取最近收藏请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetRecentFavoritesRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 限制数量
     */
    private Integer limit;

    public GetRecentFavoritesRequest() {
    }

    public GetRecentFavoritesRequest(Long userId, Integer limit) {
        this.userId = userId;
        this.limit = limit;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    @Override
    public String toString() {
        return "GetRecentFavoritesRequest{" +
                "userId=" + userId +
                ", limit=" + limit +
                '}';
    }
}
