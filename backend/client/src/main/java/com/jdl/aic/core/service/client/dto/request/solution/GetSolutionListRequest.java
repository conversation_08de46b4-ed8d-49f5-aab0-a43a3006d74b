package com.jdl.aic.core.service.client.dto.request.solution;

import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 获取解决方案列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetSolutionListRequest {
    
    /**
     * 分页请求参数
     */
    @NotNull(message = "分页请求参数不能为空")
    @Valid
    private PageRequest pageRequest;
    
    /**
     * 分类过滤
     */
    private String category;
    
    /**
     * 状态过滤（0:草稿, 1:发布, 2:下线）
     */
    private Integer status;
    
    /**
     * 作者ID过滤
     */
    private String authorId;
    
    /**
     * 搜索关键词（标题、描述）
     */
    private String search;
    
    /**
     * 默认构造函数
     */
    public GetSolutionListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param pageRequest 分页请求参数
     */
    public GetSolutionListRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    /**
     * 构造函数
     *
     * @param pageRequest 分页请求参数
     * @param category 分类过滤
     * @param status 状态过滤
     * @param authorId 作者ID过滤
     * @param search 搜索关键词
     */
    public GetSolutionListRequest(PageRequest pageRequest, String category, Integer status, String authorId, String search) {
        this.pageRequest = pageRequest;
        this.category = category;
        this.status = status;
        this.authorId = authorId;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public PageRequest getPageRequest() {
        return pageRequest;
    }
    
    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    @Override
    public String toString() {
        return "GetSolutionListRequest{" +
                "pageRequest=" + pageRequest +
                ", category='" + category + '\'' +
                ", status=" + status +
                ", authorId=" + authorId +
                ", search='" + search + '\'' +
                '}';
    }
}
