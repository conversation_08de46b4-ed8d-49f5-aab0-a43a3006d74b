package com.jdl.aic.core.service.client.dto.request.category;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;

/**
 * 根据细分类型获取分类列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCategoriesBySubTypeRequest {
    
    /**
     * 内容类别
     */
    @NotBlank(message = "内容类别不能为空")
    private String contentCategory;
    
    /**
     * 细分类型ID
     */
    private Long subTypeId;
    
    /**
     * 默认构造函数
     */
    public GetCategoriesBySubTypeRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentCategory 内容类别
     * @param subTypeId 细分类型ID
     */
    public GetCategoriesBySubTypeRequest(String contentCategory, Long subTypeId) {
        this.contentCategory = contentCategory;
        this.subTypeId = subTypeId;
    }
    
    // Getter and Setter methods
    
    public String getContentCategory() {
        return contentCategory;
    }
    
    public void setContentCategory(String contentCategory) {
        this.contentCategory = contentCategory;
    }
    
    public Long getSubTypeId() {
        return subTypeId;
    }
    
    public void setSubTypeId(Long subTypeId) {
        this.subTypeId = subTypeId;
    }
    
    @Override
    public String toString() {
        return "GetCategoriesBySubTypeRequest{" +
                "contentCategory='" + contentCategory + '\'' +
                ", subTypeId=" + subTypeId +
                '}';
    }
}
