package com.jdl.aic.core.service.portal.client;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.LikeDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;

import java.util.List;

/**
 * 点赞数据服务接口
 *
 * <p>提供点赞相关的业务功能，包括：
 * <ul>
 *   <li>内容点赞和取消点赞</li>
 *   <li>用户点赞列表查询</li>
 *   <li>点赞统计和分析</li>
 *   <li>点赞状态管理</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface LikeDataService {

    // ==================== 点赞基本操作 ====================

    /**
     * 添加点赞
     *
     * @param request 添加点赞请求参数
     * @return 点赞结果
     */
    Result<LikeDTO> addLike(AddLikeRequest request);

    /**
     * 取消点赞
     *
     * @param request 取消点赞请求参数
     * @return 操作结果
     */
    Result<Void> removeLike(RemoveLikeRequest request);

    /**
     * 切换点赞状态（如果已点赞则取消，如果未点赞则添加）
     *
     * @param request 切换点赞状态请求参数
     * @return 操作结果，包含当前点赞状态
     */
    Result<Boolean> toggleLike(ToggleLikeRequest request);

    /**
     * 检查用户是否已点赞指定内容
     *
     * @param request 检查点赞状态请求参数
     * @return 点赞状态
     */
    Result<Boolean> isLiked(CheckLikeRequest request);

    // ==================== 点赞查询 ====================

    /**
     * 获取用户点赞列表（分页）
     *
     * @param request 获取用户点赞列表请求参数
     * @return 点赞列表
     */
    Result<PageResult<LikeDTO>> getUserLikes(GetUserLikesRequest request);

    /**
     * 获取用户最近点赞的内容
     *
     * @param request 获取用户点赞列表请求参数（使用limit字段）
     * @return 最近点赞列表
     */
    Result<List<LikeDTO>> getRecentLikes(GetUserLikesRequest request);

    /**
     * 根据内容获取点赞列表
     *
     * @param request 根据内容获取点赞列表请求参数
     * @return 点赞该内容的用户列表
     */
    Result<PageResult<LikeDTO>> getLikesByContent(GetLikesByContentRequest request);

    /**
     * 批量获取用户对内容列表的点赞状态
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 点赞状态映射（contentId -> isLiked）
     */
    Result<List<Object>> getLikeStatusBatch(Long userId, Integer contentType, List<Long> contentIds);

    // ==================== 点赞统计 ====================

    /**
     * 统计用户点赞总数
     *
     * @param request 获取用户点赞总数请求参数
     * @return 点赞总数
     */
    Result<Integer> getUserLikeCount(GetUserLikeCountRequest request);

    /**
     * 统计用户各类型内容的点赞数
     *
     * @param request 获取用户点赞总数请求参数
     * @return 各类型点赞统计
     */
    Result<List<Object>> getUserLikeCountByType(GetUserLikeCountRequest request);

    /**
     * 统计指定内容的点赞数
     *
     * @param request 获取内容点赞数请求参数
     * @return 点赞数
     */
    Result<Integer> getContentLikeCount(GetContentLikeCountRequest request);

    /**
     * 批量统计内容列表的点赞数
     *
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 点赞数统计列表
     */
    Result<List<Object>> getContentLikeCountBatch(Integer contentType, List<Long> contentIds);

    /**
     * 获取热门点赞内容
     *
     * @param contentType 内容类型
     * @param limit 限制数量
     * @param days 统计天数（可选）
     * @return 热门点赞内容列表
     */
    Result<List<Object>> getPopularLikedContent(Integer contentType, Integer limit, Integer days);

    // ==================== 批量操作 ====================

    /**
     * 批量添加点赞
     *
     * @param userId 用户ID
     * @param likes 点赞列表
     * @return 批量操作结果
     */
    Result<List<LikeDTO>> batchAddLikes(Long userId, List<LikeDTO> likes);

    /**
     * 批量取消点赞
     *
     * @param userId 用户ID
     * @param likeIds 点赞ID列表
     * @return 批量操作结果
     */
    Result<Void> batchRemoveLikes(Long userId, List<Long> likeIds);

    /**
     * 批量取消点赞（根据内容）
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 批量操作结果
     */
    Result<Void> batchRemoveLikesByContent(Long userId, Integer contentType, List<Long> contentIds);

    // ==================== 点赞管理 ====================

    /**
     * 清理用户已删除的点赞记录
     *
     * @param userId 用户ID
     * @param days 清理天数前的记录
     * @return 清理结果
     */
    Result<Integer> cleanupDeletedLikes(Long userId, Integer days);

    /**
     * 恢复已删除的点赞
     *
     * @param userId 用户ID
     * @param likeId 点赞ID
     * @return 恢复结果
     */
    Result<LikeDTO> restoreLike(Long userId, Long likeId);

    /**
     * 导出用户点赞数据
     *
     * @param userId 用户ID
     * @param contentType 内容类型过滤（可选）
     * @param format 导出格式（json, csv等）
     * @return 导出结果
     */
    Result<Object> exportUserLikes(Long userId, Integer contentType, String format);

    // ==================== 点赞分析 ====================

    /**
     * 获取用户点赞趋势分析
     *
     * @param userId 用户ID
     * @param days 分析天数
     * @return 点赞趋势数据
     */
    Result<Object> getUserLikeTrend(Long userId, Integer days);

    /**
     * 获取用户点赞偏好分析
     *
     * @param userId 用户ID
     * @return 点赞偏好数据
     */
    Result<Object> getUserLikePreference(Long userId);

    /**
     * 获取内容点赞趋势分析
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param days 分析天数
     * @return 点赞趋势数据
     */
    Result<Object> getContentLikeTrend(Integer contentType, Long contentId, Integer days);

    /**
     * 获取用户点赞活跃度分析
     *
     * @param userId 用户ID
     * @param days 分析天数
     * @return 活跃度数据
     */
    Result<Object> getUserLikeActivity(Long userId, Integer days);

    // ==================== 点赞推荐 ====================

    /**
     * 基于用户点赞历史推荐相关内容
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param limit 推荐数量
     * @return 推荐内容列表
     */
    Result<List<Object>> getRecommendedContent(Long userId, Integer contentType, Integer limit);

    /**
     * 获取相似点赞偏好的用户
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 相似用户列表
     */
    Result<List<Object>> getSimilarUsers(Long userId, Integer limit);

    /**
     * 获取用户可能感兴趣的点赞内容
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param limit 限制数量
     * @return 推荐点赞列表
     */
    Result<List<Object>> getRecommendedLikes(Long userId, Integer contentType, Integer limit);

    // ==================== 点赞互动 ====================

    /**
     * 获取用户点赞的内容被其他用户点赞的情况
     *
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @return 互动点赞列表
     */
    Result<PageResult<Object>> getUserLikeInteractions(Long userId, PageRequest pageRequest);

    /**
     * 获取用户点赞行为的社交影响力
     *
     * @param userId 用户ID
     * @return 影响力数据
     */
    Result<Object> getUserLikeInfluence(Long userId);

    /**
     * 获取内容的点赞网络分析
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 点赞网络数据
     */
    Result<Object> getContentLikeNetwork(Integer contentType, Long contentId);
}
