package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.recommendation.TeamRecommendationDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 团队推荐内容服务接口
 * 
 * <p>提供团队推荐内容管理功能，包括：
 * <ul>
 *   <li>团队推荐内容的CRUD操作</li>
 *   <li>推荐内容状态管理</li>
 *   <li>推荐内容统计和分析</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface TeamRecommendationService {

    // ==================== 基础CRUD操作 ====================

    /**
     * 根据ID获取团队推荐详情
     * 
     * @param id 推荐ID
     * @return 团队推荐详情
     */
    Result<TeamRecommendationDTO> getTeamRecommendationById(Long id);

    /**
     * 获取团队推荐列表（分页，支持多条件过滤）
     * 
     * @param pageRequest 分页请求
     * @param teamId 团队ID过滤
     * @param userId 用户ID过滤
     * @param contentType 内容类型过滤
     * @param status 状态过滤
     * @param search 搜索关键词
     * @return 团队推荐列表
     */
    Result<PageResult<TeamRecommendationDTO>> getTeamRecommendationList(
            PageRequest pageRequest,
            Long teamId,
            Long userId,
            String contentType,
            String status,
            String search);

    /**
     * 创建团队推荐
     * 
     * @param teamRecommendation 团队推荐信息
     * @return 创建结果
     */
    Result<TeamRecommendationDTO> createTeamRecommendation(TeamRecommendationDTO teamRecommendation);

    /**
     * 推荐内容到团队
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param reason 推荐理由
     * @return 创建结果
     */
    Result<TeamRecommendationDTO> recommendContentToTeam(Long teamId, Long userId, Long contentId, String contentType, String reason);

    /**
     * 更新团队推荐
     * 
     * @param id 推荐ID
     * @param teamRecommendation 团队推荐信息
     * @return 更新结果
     */
    Result<TeamRecommendationDTO> updateTeamRecommendation(Long id, TeamRecommendationDTO teamRecommendation);

    /**
     * 删除团队推荐
     * 
     * @param id 推荐ID
     * @return 删除结果
     */
    Result<Void> deleteTeamRecommendation(Long id);

    /**
     * 软删除团队推荐
     * 
     * @param id 推荐ID
     * @return 删除结果
     */
    Result<Void> softDeleteTeamRecommendation(Long id);

    // ==================== 团队推荐查询 ====================

    /**
     * 根据团队ID获取推荐内容列表
     * 
     * @param teamId 团队ID
     * @param limit 限制数量
     * @return 团队推荐列表
     */
    Result<List<TeamRecommendationDTO>> getTeamRecommendationsByTeamId(Long teamId, Integer limit);

    /**
     * 根据用户ID获取推荐记录列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 团队推荐列表
     */
    Result<List<TeamRecommendationDTO>> getTeamRecommendationsByUserId(Long userId, Integer limit);

    /**
     * 根据内容ID和类型获取推荐记录列表
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param limit 限制数量
     * @return 团队推荐列表
     */
    Result<List<TeamRecommendationDTO>> getTeamRecommendationsByContentIdAndType(Long contentId, String contentType, Integer limit);

    /**
     * 根据团队ID和内容类型获取推荐列表
     * 
     * @param teamId 团队ID
     * @param contentType 内容类型
     * @param limit 限制数量
     * @return 团队推荐列表
     */
    Result<List<TeamRecommendationDTO>> getTeamRecommendationsByTeamIdAndContentType(Long teamId, String contentType, Integer limit);

    /**
     * 根据状态获取推荐记录列表
     * 
     * @param status 推荐状态
     * @param limit 限制数量
     * @return 团队推荐列表
     */
    Result<List<TeamRecommendationDTO>> getTeamRecommendationsByStatus(String status, Integer limit);

    /**
     * 获取所有推荐记录列表
     * 
     * @param limit 限制数量
     * @return 团队推荐列表
     */
    Result<List<TeamRecommendationDTO>> getAllTeamRecommendations(Integer limit);

    /**
     * 获取活跃的推荐记录列表
     * 
     * @param limit 限制数量
     * @return 团队推荐列表
     */
    Result<List<TeamRecommendationDTO>> getActiveTeamRecommendations(Integer limit);

    /**
     * 根据条件查询推荐记录列表
     * 
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param status 推荐状态
     * @param limit 限制数量
     * @return 团队推荐列表
     */
    Result<List<TeamRecommendationDTO>> getTeamRecommendationsByCondition(Long teamId, Long userId, String contentType, String status, Integer limit);

    // ==================== 状态管理 ====================

    /**
     * 更新推荐状态
     * 
     * @param id 推荐ID
     * @param status 推荐状态
     * @return 更新结果
     */
    Result<Void> updateRecommendationStatus(Long id, String status);

    /**
     * 激活推荐
     * 
     * @param id 推荐ID
     * @return 更新结果
     */
    Result<Void> activateRecommendation(Long id);

    /**
     * 停用推荐
     * 
     * @param id 推荐ID
     * @return 更新结果
     */
    Result<Void> deactivateRecommendation(Long id);

    // ==================== 批量操作 ====================

    /**
     * 批量创建团队推荐
     * 
     * @param teamRecommendations 团队推荐列表
     * @return 创建结果
     */
    Result<List<TeamRecommendationDTO>> batchCreateTeamRecommendations(List<TeamRecommendationDTO> teamRecommendations);

    /**
     * 批量更新推荐状态
     * 
     * @param ids 推荐ID列表
     * @param status 推荐状态
     * @return 更新结果
     */
    Result<Void> batchUpdateRecommendationStatus(List<Long> ids, String status);

    /**
     * 批量软删除推荐记录
     * 
     * @param ids 推荐ID列表
     * @return 删除结果
     */
    Result<Void> batchSoftDeleteTeamRecommendations(List<Long> ids);

    // ==================== 统计和验证 ====================

    /**
     * 检查推荐记录是否存在
     * 
     * @param teamId 团队ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 是否存在
     */
    Result<Boolean> isRecommendationExists(Long teamId, Long contentId, String contentType);

    /**
     * 统计推荐记录数量
     * 
     * @return 推荐记录数量
     */
    Result<Integer> getTeamRecommendationCount();

    /**
     * 统计活跃推荐记录数量
     * 
     * @return 活跃推荐记录数量
     */
    Result<Integer> getActiveTeamRecommendationCount();

    /**
     * 统计团队的推荐数量
     * 
     * @param teamId 团队ID
     * @return 团队推荐数量
     */
    Result<Integer> getTeamRecommendationCountByTeamId(Long teamId);

    /**
     * 统计用户的推荐数量
     * 
     * @param userId 用户ID
     * @return 用户推荐数量
     */
    Result<Integer> getTeamRecommendationCountByUserId(Long userId);

    /**
     * 统计内容的推荐数量
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 内容推荐数量
     */
    Result<Integer> getTeamRecommendationCountByContentIdAndType(Long contentId, String contentType);

    /**
     * 获取团队推荐统计信息
     * 
     * @param teamId 团队ID
     * @return 统计信息
     */
    Result<Map<String, Object>> getTeamRecommendationStatistics(Long teamId);

    /**
     * 获取热门推荐内容类型
     * 
     * @return 热门内容类型列表
     */
    Result<List<String>> getPopularContentTypes();

    /**
     * 获取用户最近的推荐记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近推荐记录
     */
    Result<List<TeamRecommendationDTO>> getUserRecentRecommendations(Long userId, Integer limit);

    /**
     * 获取团队最近的推荐记录
     * 
     * @param teamId 团队ID
     * @param limit 限制数量
     * @return 最近推荐记录
     */
    Result<List<TeamRecommendationDTO>> getTeamRecentRecommendations(Long teamId, Integer limit);

}