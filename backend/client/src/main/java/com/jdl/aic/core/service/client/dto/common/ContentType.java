package com.jdl.aic.core.service.client.dto.common;

/**
 * 内容类型枚举
 * 
 * <p>定义系统中支持的所有内容类型，用于统一关联表的内容类型标识。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public enum ContentType {
    
    /**
     * 知识内容
     */
    KNOWLEDGE("knowledge", "知识内容"),
    
    /**
     * 解决方案
     */
    SOLUTION("solution", "解决方案"),
    
    /**
     * 新闻动态
     */
    NEWS_FEED("news_feed", "新闻动态"),
    
    /**
     * 学习资源
     */
    LEARNING_RESOURCE("learning_resource", "学习资源"),

    /**
     * 学习课程
     */
    LEARNING_COURSE("learning_course", "学习课程");
    
    /**
     * 枚举值
     */
    private final String value;
    
    /**
     * 显示名称
     */
    private final String displayName;
    
    /**
     * 构造函数
     * 
     * @param value 枚举值
     * @param displayName 显示名称
     */
    ContentType(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }
    
    /**
     * 获取枚举值
     * 
     * @return 枚举值
     */
    public String getValue() {
        return value;
    }
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 根据值获取枚举
     * 
     * @param value 枚举值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static ContentType fromValue(String value) {
        if (value == null) {
            return null;
        }
        
        for (ContentType type : ContentType.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        
        return null;
    }
    
    /**
     * 检查值是否有效
     * 
     * @param value 要检查的值
     * @return 如果值有效返回true，否则返回false
     */
    public static boolean isValid(String value) {
        return fromValue(value) != null;
    }
    
    @Override
    public String toString() {
        return value;
    }
}
