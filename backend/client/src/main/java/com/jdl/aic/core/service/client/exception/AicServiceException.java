package com.jdl.aic.core.service.client.exception;

/**
 * AI社区服务异常基类
 * 
 * <p>所有AI社区服务相关的异常都应该继承此类。
 * 提供统一的异常处理机制和错误信息格式。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class AicServiceException extends RuntimeException {
    
    /**
     * 错误码
     */
    private final String errorCode;
    
    /**
     * 详细错误信息
     */
    private final Object details;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public AicServiceException(String message) {
        super(message);
        this.errorCode = "UNKNOWN_ERROR";
        this.details = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     */
    public AicServiceException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.details = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public AicServiceException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.details = null;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param details 详细错误信息
     */
    public AicServiceException(String errorCode, String message, Object details) {
        super(message);
        this.errorCode = errorCode;
        this.details = details;
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误码
     * @param message 错误消息
     * @param details 详细错误信息
     * @param cause 原因异常
     */
    public AicServiceException(String errorCode, String message, Object details, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.details = details;
    }
    
    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public String getErrorCode() {
        return errorCode;
    }
    
    /**
     * 获取详细错误信息
     * 
     * @return 详细错误信息
     */
    public Object getDetails() {
        return details;
    }
    
    @Override
    public String toString() {
        return "AicServiceException{" +
                "errorCode='" + errorCode + '\'' +
                ", message='" + getMessage() + '\'' +
                ", details=" + details +
                '}';
    }
}
