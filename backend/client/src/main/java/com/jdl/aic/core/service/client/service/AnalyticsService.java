package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.request.analytics.GetStatisticsRequest;
import com.jdl.aic.core.service.client.dto.request.analytics.GetRankingRequest;
import com.jdl.aic.core.service.client.dto.request.analytics.GetTrendAnalysisRequest;
import com.jdl.aic.core.service.client.dto.request.analytics.GetComparisonAnalysisRequest;
import com.jdl.aic.core.service.client.dto.request.analytics.RecordUserActivityRequest;
import java.util.List;
import java.util.Map;

/**
 * 统一数据分析服务接口
 * 
 * <p>整合所有数据统计和分析功能，包括：
 * <ul>
 *   <li>业务数据统计分析</li>
 *   <li>用户行为分析</li>
 *   <li>内容质量评估</li>
 *   <li>智能推荐算法</li>
 *   <li>元数据分析</li>
 *   <li>排行榜和趋势分析</li>
 *   <li>数据导出功能</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public interface AnalyticsService {
    
    // ==================== 通用统计分析 ====================
    
    /**
     * 获取统计数据
     *
     * @param request 统计请求参数
     * @return 统计数据
     */
    Result<Object> getStatistics(GetStatisticsRequest request);

    /**
     * 获取排行榜数据
     *
     * @param request 排行榜请求参数
     * @return 排行榜数据
     */
    Result<List<Object>> getRanking(GetRankingRequest request);

    /**
     * 获取趋势分析
     *
     * @param request 趋势分析请求参数
     * @return 趋势分析数据
     */
    Result<Object> getTrendAnalysis(GetTrendAnalysisRequest request);

    /**
     * 获取对比分析
     *
     * @param request 对比分析请求参数
     * @return 对比分析结果
     */
    Result<Object> getComparisonAnalysis(GetComparisonAnalysisRequest request);
    
    // ==================== 用户行为分析 ====================

    /**
     * 记录用户活动
     *
     * @param request 记录用户活动请求参数
     * @return 记录结果
     */
    Result<Void> recordUserActivity(RecordUserActivityRequest request);

    /**
     * 获取用户画像
     *
     * @param userId 用户ID
     * @param profileType 画像类型（basic, detailed, behavioral）
     * @return 用户画像数据
     */
    Result<Object> getUserProfile(Long userId, String profileType);

    /**
     * 获取用户行为分析
     *
     * @param userId 用户ID
     * @param behaviorType 行为类型（reading, interaction, contribution等）
     * @param timeRange 时间范围
     * @return 用户行为分析结果
     */
    Result<Object> getUserBehaviorAnalysis(Long userId, String behaviorType, Map<String, Object> timeRange);

    /**
     * 获取用户留存分析
     *
     * @param cohortType 队列类型（daily, weekly, monthly）
     * @param startDate 开始日期
     * @param periods 分析周期数
     * @param filters 过滤条件
     * @return 用户留存分析
     */
    Result<Object> getUserRetentionAnalysis(String cohortType, String startDate, Integer periods, Map<String, Object> filters);

    // ==================== 智能推荐 ====================

    /**
     * 获取个性化推荐
     *
     * @param recommendationType 推荐类型（content, user, category, tag）
     * @param targetId 目标ID（用户ID或内容ID）
     * @param pageRequest 分页请求
     * @param params 推荐参数
     * @return 推荐结果
     */
    Result<PageResult<Object>> getRecommendations(
            String recommendationType,
            Long targetId,
            PageRequest pageRequest,
            Map<String, Object> params);

    /**
     * 获取相似性分析
     *
     * @param similarityType 相似性类型（content, user, behavior）
     * @param sourceId 源ID
     * @param limit 返回数量限制
     * @param params 相似性参数
     * @return 相似性分析结果
     */
    Result<List<Object>> getSimilarityAnalysis(String similarityType, Long sourceId, Integer limit, Map<String, Object> params);
    
    // ==================== 元数据分析 ====================

    /**
     * 获取分类标签使用分析
     *
     * @param analysisType 分析类型（usage, correlation, optimization）
     * @param contentCategory 内容类别过滤
     * @param timeRange 时间范围
     * @return 分类标签分析结果
     */
    Result<Object> getMetadataAnalysis(String analysisType, String contentCategory, Map<String, Object> timeRange);

    /**
     * 获取标签云数据
     *
     * @param contentCategory 内容类别过滤
     * @param limit 标签数量限制
     * @param filters 过滤条件
     * @return 标签云数据
     */
    Result<List<Object>> getTagCloudData(String contentCategory, Integer limit, Map<String, Object> filters);

    // ==================== 质量评估 ====================

    /**
     * 获取内容质量评估
     *
     * @param contentType 内容类型（knowledge, solution, learning_resource）
     * @param contentId 内容ID
     * @param assessmentType 评估类型（completeness, accuracy, popularity）
     * @return 质量评估结果
     */
    Result<Object> getContentQualityAssessment(String contentType, Long contentId, String assessmentType);

    // ==================== 数据导出 ====================

    /**
     * 导出分析数据
     *
     * @param dataType 数据类型（statistics, ranking, trend, user_profile）
     * @param format 导出格式（json, csv, excel, pdf）
     * @param exportParams 导出参数
     * @return 导出结果
     */
    Result<Object> exportAnalysisData(String dataType, String format, Map<String, Object> exportParams);

    /**
     * 生成分析报告
     *
     * @param reportType 报告类型（business, user, content, system）
     * @param reportParams 报告参数
     * @return 分析报告
     */
    Result<Object> generateAnalysisReport(String reportType, Map<String, Object> reportParams);
}
