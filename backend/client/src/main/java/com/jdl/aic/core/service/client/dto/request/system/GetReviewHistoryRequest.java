package com.jdl.aic.core.service.client.dto.request.system;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取审核历史请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetReviewHistoryRequest {
    
    /**
     * 内容类型过滤
     */
    private String contentType;
    
    /**
     * 审核人过滤
     */
    private Long reviewerId;
    
    /**
     * 审核状态过滤
     */
    private Integer reviewStatus;
    
    /**
     * 开始日期
     */
    private String startDate;
    
    /**
     * 结束日期
     */
    private String endDate;
    
    /**
     * 默认构造函数
     */
    public GetReviewHistoryRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型过滤
     * @param reviewerId 审核人过滤
     * @param reviewStatus 审核状态过滤
     * @param startDate 开始日期
     * @param endDate 结束日期
     */
    public GetReviewHistoryRequest(String contentType, Long reviewerId, Integer reviewStatus, String startDate, String endDate) {
        this.contentType = contentType;
        this.reviewerId = reviewerId;
        this.reviewStatus = reviewStatus;
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getReviewerId() {
        return reviewerId;
    }
    
    public void setReviewerId(Long reviewerId) {
        this.reviewerId = reviewerId;
    }
    
    public Integer getReviewStatus() {
        return reviewStatus;
    }
    
    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    
    public String getStartDate() {
        return startDate;
    }
    
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    
    public String getEndDate() {
        return endDate;
    }
    
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
    
    @Override
    public String toString() {
        return "GetReviewHistoryRequest{" +
                "contentType='" + contentType + '\'' +
                ", reviewerId=" + reviewerId +
                ", reviewStatus=" + reviewStatus +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                '}';
    }
}
