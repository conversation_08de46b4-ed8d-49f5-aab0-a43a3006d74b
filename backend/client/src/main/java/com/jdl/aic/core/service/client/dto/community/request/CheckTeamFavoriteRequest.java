package com.jdl.aic.core.service.client.dto.community.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 检查团队收藏状态请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckTeamFavoriteRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 团队ID
     */
    @NotNull(message = "团队ID不能为空")
    private Long teamId;
    
    /**
     * 默认构造函数
     */
    public CheckTeamFavoriteRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param teamId 团队ID
     */
    public CheckTeamFavoriteRequest(Long userId, Long teamId) {
        this.userId = userId;
        this.teamId = teamId;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getTeamId() {
        return teamId;
    }
    
    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
    
    @Override
    public String toString() {
        return "CheckTeamFavoriteRequest{" +
                "userId=" + userId +
                ", teamId=" + teamId +
                '}';
    }
}
