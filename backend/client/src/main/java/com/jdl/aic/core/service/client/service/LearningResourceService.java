package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.learning.*;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningResourceListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningPathListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetRecommendedResourcesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetPopularResourcesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.SearchLearningResourcesRequest;

import java.util.List;

/**
 * 学习资源服务接口
 * 
 * <p>提供学习资源管理功能，包括：
 * <ul>
 *   <li>学习资源的CRUD操作</li>
 *   <li>学习路径管理</li>
 *   <li>学习进度跟踪</li>
 *   <li>学习资源搜索和推荐</li>
 *   <li>学习统计和分析</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface LearningResourceService {
    
    // ==================== 学习资源管理 ====================
    
    /**
     * 获取学习资源列表（分页）
     *
     * @param request 获取学习资源列表请求
     * @return 学习资源列表
     */
    Result<PageResult<LearningResourceDTO>> getLearningResourceList(GetLearningResourceListRequest request);
    
    /**
     * 根据ID获取学习资源详情
     * 
     * @param id 学习资源ID
     * @return 学习资源详情
     */
    Result<LearningResourceDTO> getLearningResourceById(Long id);
    
    /**
     * 创建学习资源
     * 
     * @param resource 学习资源信息
     * @return 创建结果
     */
    Result<LearningResourceDTO> createLearningResource(LearningResourceDTO resource);
    
    /**
     * 更新学习资源
     * 
     * @param id 学习资源ID
     * @param resource 学习资源信息
     * @return 更新结果
     */
    Result<LearningResourceDTO> updateLearningResource(Long id, LearningResourceDTO resource);
    
    /**
     * 删除学习资源
     * 
     * @param id 学习资源ID
     * @return 删除结果
     */
    Result<Void> deleteLearningResource(Long id);
    
    /**
     * 更新学习资源状态
     * 
     * @param id 学习资源ID
     * @param status 新状态（0:草稿, 1:发布, 2:下线）
     * @return 操作结果
     */
    Result<Void> updateLearningResourceStatus(Long id, Integer status);
    
    /**
     * 批量更新学习资源状态
     * 
     * @param ids 学习资源ID列表
     * @param status 新状态
     * @return 操作结果
     */
    Result<Void> batchUpdateLearningResourceStatus(List<Long> ids, Integer status);
    
    // ==================== 学习路径管理 ====================
    
    /**
     * 获取学习路径列表（分页）
     *
     * @param request 获取学习路径列表请求
     * @return 学习路径列表
     */
    Result<PageResult<LearningPathDTO>> getLearningPathList(GetLearningPathListRequest request);
    
    /**
     * 根据ID获取学习路径详情
     * 
     * @param id 学习路径ID
     * @return 学习路径详情
     */
    Result<LearningPathDTO> getLearningPathById(Long id);
    
    /**
     * 创建学习路径
     * 
     * @param learningPath 学习路径信息
     * @return 创建结果
     */
    Result<LearningPathDTO> createLearningPath(LearningPathDTO learningPath);
    
    /**
     * 更新学习路径
     * 
     * @param id 学习路径ID
     * @param learningPath 学习路径信息
     * @return 更新结果
     */
    Result<LearningPathDTO> updateLearningPath(Long id, LearningPathDTO learningPath);
    
    /**
     * 删除学习路径
     * 
     * @param id 学习路径ID
     * @return 删除结果
     */
    Result<Void> deleteLearningPath(Long id);
    
    /**
     * 获取学习路径的资源列表
     * 
     * @param pathId 学习路径ID
     * @return 路径资源列表
     */
    Result<List<LearningPathResourceDTO>> getLearningPathResources(Long pathId);
    
    /**
     * 为学习路径添加资源
     * 
     * @param pathId 学习路径ID
     * @param resourceId 学习资源ID
     * @param sortOrder 排序顺序
     * @return 操作结果
     */
    Result<LearningPathResourceDTO> addResourceToPath(Long pathId, Long resourceId, Integer sortOrder);
    
    /**
     * 从学习路径移除资源
     * 
     * @param pathId 学习路径ID
     * @param resourceId 学习资源ID
     * @return 操作结果
     */
    Result<Void> removeResourceFromPath(Long pathId, Long resourceId);
    
    /**
     * 调整学习路径中资源的顺序
     * 
     * @param pathId 学习路径ID
     * @param resourceIds 资源ID列表（按新顺序排列）
     * @return 操作结果
     */
    Result<Void> reorderPathResources(Long pathId, List<Long> resourceIds);
    
    // ==================== 学习进度跟踪 ====================
    
    /**
     * 获取用户学习进度
     * 
     * @param userId 用户ID
     * @param resourceId 学习资源ID
     * @return 学习进度
     */
    Result<UserLearningProgressDTO> getUserLearningProgress(Long userId, Long resourceId);
    
    /**
     * 更新用户学习进度
     * 
     * @param userId 用户ID
     * @param resourceId 学习资源ID
     * @param progress 学习进度信息
     * @return 更新结果
     */
    Result<UserLearningProgressDTO> updateUserLearningProgress(
            Long userId, 
            Long resourceId, 
            UserLearningProgressDTO progress);
    
    /**
     * 标记学习资源为已完成
     * 
     * @param userId 用户ID
     * @param resourceId 学习资源ID
     * @return 操作结果
     */
    Result<Void> markResourceAsCompleted(Long userId, Long resourceId);
    
    /**
     * 获取用户学习路径进度
     * 
     * @param userId 用户ID
     * @param pathId 学习路径ID
     * @return 路径学习进度
     */
    Result<List<UserLearningProgressDTO>> getUserPathProgress(Long userId, Long pathId);
    
    /**
     * 获取用户学习统计
     * 
     * @param userId 用户ID
     * @return 学习统计信息
     */
    Result<Object> getUserLearningStats(Long userId);
    
    // ==================== 学习资源推荐和搜索 ====================

    /**
     * 获取学习资源分类统计信息
     * @return 分类统计结果
     */
    Result<List<CategoryStatisticsDTO>> getResourceCategoryStatistics();
    
    /**
     * 获取推荐学习资源
     *
     * @param request 获取推荐学习资源请求
     * @return 推荐学习资源列表
     */
    Result<PageResult<LearningResourceDTO>> getRecommendedResources(GetRecommendedResourcesRequest request);
    
    /**
     * 获取热门学习资源
     *
     * @param request 获取热门学习资源请求
     * @return 热门学习资源列表
     */
    Result<PageResult<LearningResourceDTO>> getPopularResources(GetPopularResourcesRequest request);
    
    /**
     * 搜索学习资源
     *
     * @param request 搜索学习资源请求
     * @return 搜索结果
     */
    Result<PageResult<LearningResourceDTO>> searchLearningResources(SearchLearningResourcesRequest request);
    
    /**
     * 增加学习资源浏览次数
     * 
     * @param id 学习资源ID
     * @return 操作结果
     */
    Result<Void> incrementViewCount(Long id);
    
    /**
     * 增加学习资源下载次数
     * 
     * @param id 学习资源ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> incrementDownloadCount(Long id, Long userId);
}
