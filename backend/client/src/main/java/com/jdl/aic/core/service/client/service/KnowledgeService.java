package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO;
import com.jdl.aic.core.service.client.dto.request.knowledge.GetKnowledgeListRequest;

import java.util.List;

/**
 * 知识管理服务接口
 *
 * <p>整合知识类型和知识内容管理功能，提供完整的知识管理能力：
 * <ul>
 *   <li>知识类型的配置和管理</li>
 *   <li>知识内容的全生命周期管理</li>
 *   <li>知识搜索和查询功能</li>
 *   <li>知识版本控制</li>
 *   <li>批量操作支持</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public interface KnowledgeService {

    // ==================== 知识类型管理 ====================

    /**
     * 获取知识类型列表（分页）
     *
     * @param pageRequest 分页请求
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     * @return 知识类型列表
     */
    Result<PageResult<KnowledgeTypeDTO>> getKnowledgeTypeList(
            PageRequest pageRequest,
            Boolean isActive,
            String search);

    /**
     * 根据ID获取知识类型详情
     *
     * @param id 知识类型ID
     * @return 知识类型详情
     */
    Result<KnowledgeTypeDTO> getKnowledgeTypeById(Long id);

    /**
     * 根据编码获取知识类型详情
     *
     * @param code 知识类型编码
     * @return 知识类型详情
     */
    Result<KnowledgeTypeDTO> getKnowledgeTypeByCode(String code);

    /**
     * 创建知识类型
     *
     * @param knowledgeType 知识类型信息
     * @return 创建结果
     */
    Result<KnowledgeTypeDTO> createKnowledgeType(KnowledgeTypeDTO knowledgeType);

    /**
     * 更新知识类型
     *
     * @param id 知识类型ID
     * @param knowledgeType 知识类型信息
     * @return 更新结果
     */
    Result<KnowledgeTypeDTO> updateKnowledgeType(Long id, KnowledgeTypeDTO knowledgeType);

    /**
     * 删除知识类型
     *
     * @param id 知识类型ID
     * @return 删除结果
     */
    Result<Void> deleteKnowledgeType(Long id);

    /**
     * 启用/禁用知识类型
     *
     * @param id 知识类型ID
     * @param isActive 是否启用
     * @return 操作结果
     */
    Result<Void> toggleKnowledgeTypeStatus(Long id, Boolean isActive);

    // ==================== 知识内容管理 ====================

    /**
     * 获取知识内容列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 知识内容列表
     */
    Result<PageResult<KnowledgeDTO>> getKnowledgeList(
            PageRequest pageRequest,
            GetKnowledgeListRequest request);

    /**
     * 根据ID获取知识内容详情
     *
     * @param id 知识内容ID
     * @return 知识内容详情
     */
    Result<KnowledgeDTO> getKnowledgeById(Long id);

    /**
     * 创建知识内容
     *
     * @param knowledge 知识内容信息
     * @return 创建结果
     */
    Result<KnowledgeDTO> createKnowledge(KnowledgeDTO knowledge);

    /**
     * 更新知识内容
     *
     * @param id 知识内容ID
     * @param knowledge 知识内容信息
     * @return 更新结果
     */
    Result<KnowledgeDTO> updateKnowledge(Long id, KnowledgeDTO knowledge);

    /**
     * 删除知识内容
     *
     * @param id 知识内容ID
     * @return 删除结果
     */
    Result<Void> deleteKnowledge(Long id);

    /**
     * 更新知识内容状态
     *
     * @param id 知识内容ID
     * @param status 新状态（0:草稿, 1:待审核, 2:已发布, 3:已下线, 4:已拒绝）
     * @return 操作结果
     */
    Result<Void> updateKnowledgeStatus(Long id, Integer status);

    /**
     * 搜索知识内容
     *
     * @param keyword 搜索关键词
     * @param pageRequest 分页请求
     * @param knowledgeTypeCode 知识类型编码过滤
     * @param status 状态过滤
     * @param visibility 可见性过滤
     * @return 搜索结果
     */
    Result<PageResult<KnowledgeDTO>> searchKnowledge(
            String keyword,
            PageRequest pageRequest,
            String knowledgeTypeCode,
            Integer status,
            Integer visibility);

    /**
     * 增加知识内容阅读次数
     *
     * @param id 知识内容ID
     * @return 操作结果
     */
    Result<Void> incrementReadCount(Long id);

    // ==================== 批量操作 ====================

    /**
     * 批量更新知识内容状态
     *
     * @param ids 知识内容ID列表
     * @param status 新状态
     * @return 操作结果
     */
    Result<Void> batchUpdateKnowledgeStatus(List<Long> ids, Integer status);

    /**
     * 批量删除知识内容
     *
     * @param ids 知识内容ID列表
     * @return 批量删除结果
     */
    Result<Void> batchDeleteKnowledge(List<Long> ids);

    // ==================== 版本管理 ====================

    /**
     * 获取知识版本历史
     *
     * @param knowledgeId 知识ID
     * @param pageRequest 分页请求
     * @return 版本历史列表
     */
    Result<PageResult<Object>> getKnowledgeVersionHistory(Long knowledgeId, PageRequest pageRequest);

    /**
     * 创建知识版本
     *
     * @param knowledgeId 知识ID
     * @param versionComment 版本说明
     * @return 创建结果
     */
    Result<Object> createKnowledgeVersion(Long knowledgeId, String versionComment);
}
