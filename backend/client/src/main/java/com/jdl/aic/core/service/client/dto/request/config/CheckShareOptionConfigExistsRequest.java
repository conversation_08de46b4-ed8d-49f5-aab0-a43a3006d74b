package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 检查内容类型和分享类型组合是否已存在请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CheckShareOptionConfigExistsRequest {
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 分享类型
     */
    private String shareType;
    
    /**
     * 排除的配置ID（用于更新时检查）
     */
    private Long excludeId;
    
    /**
     * 默认构造函数
     */
    public CheckShareOptionConfigExistsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param shareType 分享类型
     * @param excludeId 排除的配置ID
     */
    public CheckShareOptionConfigExistsRequest(String contentType, String shareType, Long excludeId) {
        this.contentType = contentType;
        this.shareType = shareType;
        this.excludeId = excludeId;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getShareType() {
        return shareType;
    }
    
    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
    
    public Long getExcludeId() {
        return excludeId;
    }
    
    public void setExcludeId(Long excludeId) {
        this.excludeId = excludeId;
    }
    
    @Override
    public String toString() {
        return "CheckShareOptionConfigExistsRequest{" +
                "contentType='" + contentType + '\'' +
                ", shareType='" + shareType + '\'' +
                ", excludeId=" + excludeId +
                '}';
    }
}
