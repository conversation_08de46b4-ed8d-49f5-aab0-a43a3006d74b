package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 搜索结果DTO
 * 
 * <p>封装搜索结果的完整信息，包括搜索统计、结果列表、分面信息等。
 * 支持多种内容类型的统一搜索结果展示。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchResultDTO {
    
    /**
     * 搜索关键词
     */
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;
    
    /**
     * 总结果数
     */
    @NotNull(message = "总结果数不能为空")
    private Long totalCount;
    
    /**
     * 搜索耗时（毫秒）
     */
    private Long searchTime;
    
    /**
     * 当前页码
     */
    private Integer currentPage;
    
    /**
     * 每页大小
     */
    private Integer pageSize;
    
    /**
     * 总页数
     */
    private Integer totalPages;
    
    /**
     * 搜索结果列表
     */
    private List<SearchItemDTO> items;
    
    /**
     * 分面统计信息（按内容类型、分类等分组统计）
     */
    private Map<String, Map<String, Long>> facets;
    
    /**
     * 搜索建议（拼写纠正、相关搜索等）
     */
    private List<String> suggestions;
    
    /**
     * 高亮配置
     */
    private Map<String, Object> highlightConfig;
    
    /**
     * 搜索过滤器
     */
    private SearchFilterDTO filters;
    
    /**
     * 搜索时间
     */
    private LocalDateTime searchedAt;
    
    /**
     * 搜索用户ID
     */
    private Long searchUserId;
    
    /**
     * 默认构造函数
     */
    public SearchResultDTO() {
    }
    
    // Getter and Setter methods
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public Long getTotalCount() {
        return totalCount;
    }
    
    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }
    
    public Long getSearchTime() {
        return searchTime;
    }
    
    public void setSearchTime(Long searchTime) {
        this.searchTime = searchTime;
    }
    
    public Integer getCurrentPage() {
        return currentPage;
    }
    
    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }
    
    public Integer getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    public Integer getTotalPages() {
        return totalPages;
    }
    
    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }
    
    public List<SearchItemDTO> getItems() {
        return items;
    }
    
    public void setItems(List<SearchItemDTO> items) {
        this.items = items;
    }
    
    public Map<String, Map<String, Long>> getFacets() {
        return facets;
    }
    
    public void setFacets(Map<String, Map<String, Long>> facets) {
        this.facets = facets;
    }
    
    public List<String> getSuggestions() {
        return suggestions;
    }
    
    public void setSuggestions(List<String> suggestions) {
        this.suggestions = suggestions;
    }
    
    public Map<String, Object> getHighlightConfig() {
        return highlightConfig;
    }
    
    public void setHighlightConfig(Map<String, Object> highlightConfig) {
        this.highlightConfig = highlightConfig;
    }
    
    public SearchFilterDTO getFilters() {
        return filters;
    }
    
    public void setFilters(SearchFilterDTO filters) {
        this.filters = filters;
    }
    
    public LocalDateTime getSearchedAt() {
        return searchedAt;
    }
    
    public void setSearchedAt(LocalDateTime searchedAt) {
        this.searchedAt = searchedAt;
    }
    
    public Long getSearchUserId() {
        return searchUserId;
    }
    
    public void setSearchUserId(Long searchUserId) {
        this.searchUserId = searchUserId;
    }
    
    @Override
    public String toString() {
        return "SearchResultDTO{" +
                "keyword='" + keyword + '\'' +
                ", totalCount=" + totalCount +
                ", searchTime=" + searchTime +
                ", currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                ", totalPages=" + totalPages +
                ", items=" + items +
                ", facets=" + facets +
                ", suggestions=" + suggestions +
                ", highlightConfig=" + highlightConfig +
                ", filters=" + filters +
                ", searchedAt=" + searchedAt +
                ", searchUserId=" + searchUserId +
                '}';
    }
}
