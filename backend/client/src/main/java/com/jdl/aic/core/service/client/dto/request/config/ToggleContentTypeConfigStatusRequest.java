package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 启用/禁用内容类型配置请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToggleContentTypeConfigStatusRequest {
    
    /**
     * 配置ID
     */
    private Long id;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public ToggleContentTypeConfigStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 配置ID
     * @param isActive 是否启用
     */
    public ToggleContentTypeConfigStatusRequest(Long id, Boolean isActive) {
        this.id = id;
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "ToggleContentTypeConfigStatusRequest{" +
                "id=" + id +
                ", isActive=" + isActive +
                '}';
    }
}
