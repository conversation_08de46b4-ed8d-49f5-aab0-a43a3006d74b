package com.jdl.aic.core.service.client.dto.request.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 获取用户评论列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetUserCommentsRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 状态过滤
     */
    private Integer status;
    
    /**
     * 默认构造函数
     */
    public GetUserCommentsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param status 状态过滤
     */
    public GetUserCommentsRequest(Long userId, Integer status) {
        this.userId = userId;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "GetUserCommentsRequest{" +
                "userId=" + userId +
                ", status=" + status +
                '}';
    }
}
