package com.jdl.aic.core.service.client.dto.request.category;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 更新分类排序权重请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateCategorySortOrderRequest {
    
    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long id;
    
    /**
     * 排序权重
     */
    @NotNull(message = "排序权重不能为空")
    private Integer sortOrder;
    
    /**
     * 默认构造函数
     */
    public UpdateCategorySortOrderRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 分类ID
     * @param sortOrder 排序权重
     */
    public UpdateCategorySortOrderRequest(Long id, Integer sortOrder) {
        this.id = id;
        this.sortOrder = sortOrder;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    @Override
    public String toString() {
        return "UpdateCategorySortOrderRequest{" +
                "id=" + id +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
