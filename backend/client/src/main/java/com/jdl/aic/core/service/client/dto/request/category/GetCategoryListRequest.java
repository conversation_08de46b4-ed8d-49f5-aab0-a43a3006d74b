package com.jdl.aic.core.service.client.dto.request.category;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取分类列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCategoryListRequest {
    
    /**
     * 内容类别过滤
     */
    private String contentCategory;
    
    /**
     * 细分类型ID过滤（用于知识类型专属分类）
     */
    private Long subTypeId;
    
    /**
     * 父分类ID过滤
     */
    private Long parentId;
    
    /**
     * 启用状态过滤
     */
    private Boolean isActive;
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 默认构造函数
     */
    public GetCategoryListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentCategory 内容类别过滤
     * @param subTypeId 细分类型ID过滤
     * @param parentId 父分类ID过滤
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     */
    public GetCategoryListRequest(String contentCategory, Long subTypeId, Long parentId, Boolean isActive, String search) {
        this.contentCategory = contentCategory;
        this.subTypeId = subTypeId;
        this.parentId = parentId;
        this.isActive = isActive;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public String getContentCategory() {
        return contentCategory;
    }
    
    public void setContentCategory(String contentCategory) {
        this.contentCategory = contentCategory;
    }
    
    public Long getSubTypeId() {
        return subTypeId;
    }
    
    public void setSubTypeId(Long subTypeId) {
        this.subTypeId = subTypeId;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    @Override
    public String toString() {
        return "GetCategoryListRequest{" +
                "contentCategory='" + contentCategory + '\'' +
                ", subTypeId=" + subTypeId +
                ", parentId=" + parentId +
                ", isActive=" + isActive +
                ", search='" + search + '\'' +
                '}';
    }
}
