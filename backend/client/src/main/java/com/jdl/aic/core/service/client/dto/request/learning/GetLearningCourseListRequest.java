package com.jdl.aic.core.service.client.dto.request.learning;

import com.jdl.aic.core.service.client.common.PageRequest;

/**
 * 获取学习课程列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetLearningCourseListRequest {
    
    /**
     * 分页请求参数
     */
    private PageRequest pageRequest;
    
    /**
     * 课程分类过滤
     */
    private String category;
    
    /**
     * 难度级别过滤
     */
    private String difficultyLevel;
    
    /**
     * 课程状态过滤
     */
    private String status;
    
    /**
     * 是否官方课程过滤
     */
    private Boolean isOfficial;
    
    /**
     * 创建者ID过滤
     */
    private String creatorId;
    
    /**
     * 搜索关键词（课程名称、描述）
     */
    private String search;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向（ASC, DESC）
     */
    private String sortDirection;

    // Getter and Setter methods
    public PageRequest getPageRequest() {
        return pageRequest;
    }

    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(Boolean isOfficial) {
        this.isOfficial = isOfficial;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }

    @Override
    public String toString() {
        return "GetLearningCourseListRequest{" +
                "pageRequest=" + pageRequest +
                ", category='" + category + '\'' +
                ", difficultyLevel='" + difficultyLevel + '\'' +
                ", status='" + status + '\'' +
                ", isOfficial=" + isOfficial +
                ", creatorId='" + creatorId + '\'' +
                ", search='" + search + '\'' +
                ", sortBy='" + sortBy + '\'' +
                ", sortDirection='" + sortDirection + '\'' +
                '}';
    }
}
