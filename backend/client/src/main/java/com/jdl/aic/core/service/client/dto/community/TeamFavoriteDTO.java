package com.jdl.aic.core.service.client.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * 团队收藏DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TeamFavoriteDTO {
    
    /**
     * 收藏ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 团队ID
     */
    @NotNull(message = "团队ID不能为空")
    private Long teamId;
    
    /**
     * 团队名称（关联查询获取）
     */
    private String teamName;
    
    /**
     * 团队描述（关联查询获取）
     */
    private String teamDescription;
    
    /**
     * 团队头像URL（关联查询获取）
     */
    private String teamAvatarUrl;
    
    /**
     * 团队隐私设置（关联查询获取）
     */
    private String teamPrivacy;
    
    /**
     * 团队是否活跃（关联查询获取）
     */
    private Boolean teamIsActive;
    
    /**
     * 收藏时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 软删除时间
     */
    private LocalDateTime deletedAt;
    
    /**
     * 默认构造函数
     */
    public TeamFavoriteDTO() {
    }
    
    /**
     * 基础构造函数
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     */
    public TeamFavoriteDTO(Long userId, Long teamId) {
        this.userId = userId;
        this.teamId = teamId;
    }
    
    /**
     * 完整构造函数
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param teamName 团队名称
     * @param teamDescription 团队描述
     */
    public TeamFavoriteDTO(Long userId, Long teamId, String teamName, String teamDescription) {
        this.userId = userId;
        this.teamId = teamId;
        this.teamName = teamName;
        this.teamDescription = teamDescription;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getTeamId() {
        return teamId;
    }
    
    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
    
    public String getTeamName() {
        return teamName;
    }
    
    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }
    
    public String getTeamDescription() {
        return teamDescription;
    }
    
    public void setTeamDescription(String teamDescription) {
        this.teamDescription = teamDescription;
    }
    
    public String getTeamAvatarUrl() {
        return teamAvatarUrl;
    }
    
    public void setTeamAvatarUrl(String teamAvatarUrl) {
        this.teamAvatarUrl = teamAvatarUrl;
    }
    
    public String getTeamPrivacy() {
        return teamPrivacy;
    }
    
    public void setTeamPrivacy(String teamPrivacy) {
        this.teamPrivacy = teamPrivacy;
    }
    
    public Boolean getTeamIsActive() {
        return teamIsActive;
    }
    
    public void setTeamIsActive(Boolean teamIsActive) {
        this.teamIsActive = teamIsActive;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getDeletedAt() {
        return deletedAt;
    }
    
    public void setDeletedAt(LocalDateTime deletedAt) {
        this.deletedAt = deletedAt;
    }
    
    @Override
    public String toString() {
        return "TeamFavoriteDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", teamId=" + teamId +
                ", teamName='" + teamName + '\'' +
                ", teamDescription='" + teamDescription + '\'' +
                ", teamAvatarUrl='" + teamAvatarUrl + '\'' +
                ", teamPrivacy='" + teamPrivacy + '\'' +
                ", teamIsActive=" + teamIsActive +
                ", createdAt=" + createdAt +
                ", deletedAt=" + deletedAt +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        TeamFavoriteDTO that = (TeamFavoriteDTO) o;
        
        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (userId != null ? !userId.equals(that.userId) : that.userId != null) return false;
        return teamId != null ? teamId.equals(that.teamId) : that.teamId == null;
    }
    
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        result = 31 * result + (teamId != null ? teamId.hashCode() : 0);
        return result;
    }
}
