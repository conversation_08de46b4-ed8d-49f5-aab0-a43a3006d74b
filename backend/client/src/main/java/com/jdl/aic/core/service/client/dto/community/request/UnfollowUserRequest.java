package com.jdl.aic.core.service.client.dto.community.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 取消关注用户请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnfollowUserRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 被关注人ID
     */
    @NotNull(message = "被关注人ID不能为空")
    private Long followedId;
    
    /**
     * 默认构造函数
     */
    public UnfollowUserRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param followedId 被关注人ID
     */
    public UnfollowUserRequest(Long userId, Long followedId) {
        this.userId = userId;
        this.followedId = followedId;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getFollowedId() {
        return followedId;
    }
    
    public void setFollowedId(Long followedId) {
        this.followedId = followedId;
    }
    
    @Override
    public String toString() {
        return "UnfollowUserRequest{" +
                "userId=" + userId +
                ", followedId=" + followedId +
                '}';
    }
}
