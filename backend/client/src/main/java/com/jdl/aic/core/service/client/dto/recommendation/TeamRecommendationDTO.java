package com.jdl.aic.core.service.client.dto.recommendation;


import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 团队推荐内容DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class TeamRecommendationDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 被推荐的团队ID
     */
    private Long teamId;
    
    /**
     * 推荐人用户ID
     */
    private Long userId;
    
    /**
     * 被推荐内容的ID
     */
    private Long contentId;
    
    /**
     * 被推荐内容的类型
     */
    private String contentType;
    
    /**
     * 推荐理由
     */
    private String reason;
    
    /**
     * 推荐状态（active/inactive/deleted）
     */
    private String status;
    
    /**
     * 软删除标志，0-未删除，1-已删除
     */
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 团队名称（冗余字段，非数据库字段）
     */
    private String teamName;
    
    /**
     * 用户名称（冗余字段，非数据库字段）
     */
    private String userName;
    
    /**
     * 内容标题（冗余字段，非数据库字段）
     */
    private String contentTitle;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getContentTitle() {
        return contentTitle;
    }

    public void setContentTitle(String contentTitle) {
        this.contentTitle = contentTitle;
    }
}