package com.jdl.aic.core.service.client.dto.request.team;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * 获取团队列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetTeamListRequest {
    
    /**
     * 父团队ID过滤
     */
    private Long parentId;
    
    /**
     * 启用状态过滤
     */
    private Boolean isActive;
    
    /**
     * 搜索关键词
     */
    private String search;

    /**
     * 标签过滤（包含任一标签的团队）
     */
    private List<String> tags;
    
    /**
     * 默认构造函数
     */
    public GetTeamListRequest() {
    }
    
    /**
     * 构造函数
     *
     * @param parentId 父团队ID过滤
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     */
    public GetTeamListRequest(Long parentId, Boolean isActive, String search) {
        this.parentId = parentId;
        this.isActive = isActive;
        this.search = search;
    }

    /**
     * 完整构造函数
     *
     * @param parentId 父团队ID过滤
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     * @param tags 标签过滤
     */
    public GetTeamListRequest(Long parentId, Boolean isActive, String search, List<String> tags) {
        this.parentId = parentId;
        this.isActive = isActive;
        this.search = search;
        this.tags = tags;
    }
    
    // Getter and Setter methods
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return "GetTeamListRequest{" +
                "parentId=" + parentId +
                ", isActive=" + isActive +
                ", search='" + search + '\'' +
                ", tags=" + tags +
                '}';
    }
}
