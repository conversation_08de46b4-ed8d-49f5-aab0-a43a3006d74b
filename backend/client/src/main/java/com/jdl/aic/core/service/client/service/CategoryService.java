package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.category.CategoryDTO;
import com.jdl.aic.core.service.client.dto.request.category.GetCategoryListRequest;
import com.jdl.aic.core.service.client.dto.request.category.GetCategoryTreeRequest;
import com.jdl.aic.core.service.client.dto.request.category.ToggleCategoryStatusRequest;
import com.jdl.aic.core.service.client.dto.request.category.UpdateCategorySortOrderRequest;
import com.jdl.aic.core.service.client.dto.request.category.MoveCategoryToParentRequest;
import com.jdl.aic.core.service.client.dto.request.category.GetCategoriesBySubTypeRequest;
import java.util.List;

/**
 * 分类管理服务接口
 * 
 * <p>提供分类管理功能，包括：
 * <ul>
 *   <li>分类的CRUD操作和层级管理</li>
 *   <li>分类树形结构管理</li>
 *   <li>分类排序和移动操作</li>
 *   <li>知识类型专属分类查询</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public interface CategoryService {
    
    // ==================== 分类管理 ====================

    /**
     * 获取分类列表（分页，支持细分类型过滤）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 分类列表
     */
    Result<PageResult<CategoryDTO>> getCategoryList(
            PageRequest pageRequest,
            GetCategoryListRequest request);

    /**
     * 获取分类树形结构（支持细分类型过滤）
     *
     * @param request 查询请求参数
     * @return 分类树
     */
    Result<List<CategoryDTO>> getCategoryTree(GetCategoryTreeRequest request);

    /**
     * 根据ID获取分类详情
     * 
     * @param id 分类ID
     * @return 分类详情
     */
    Result<CategoryDTO> getCategoryById(Long id);

    /**
     * 创建分类
     * 
     * @param category 分类信息
     * @return 创建结果
     */
    Result<CategoryDTO> createCategory(CategoryDTO category);

    /**
     * 更新分类信息
     * 
     * @param id 分类ID
     * @param category 分类信息
     * @return 更新结果
     */
    Result<CategoryDTO> updateCategory(Long id, CategoryDTO category);

    /**
     * 删除分类
     * 
     * @param id 分类ID
     * @return 删除结果
     */
    Result<Void> deleteCategory(Long id);

    /**
     * 启用/禁用分类
     *
     * @param request 切换状态请求参数
     * @return 操作结果
     */
    Result<Void> toggleCategoryStatus(ToggleCategoryStatusRequest request);

    /**
     * 更新分类排序权重
     *
     * @param request 更新排序权重请求参数
     * @return 操作结果
     */
    Result<Void> updateCategorySortOrder(UpdateCategorySortOrderRequest request);

    /**
     * 移动分类到新的父分类下
     *
     * @param request 移动分类请求参数
     * @return 操作结果
     */
    Result<Void> moveCategoryToParent(MoveCategoryToParentRequest request);

    /**
     * 获取知识类型可用的分类列表
     * 
     * <p>返回指定知识类型可以使用的分类，包括：
     * <ul>
     *   <li>该知识类型的专属分类（sub_type_id匹配）</li>
     *   <li>通用知识分类（sub_type_id为null且content_category为knowledge）</li>
     *   <li>全局通用分类（content_category为general）</li>
     * </ul>
     * 
     * @param knowledgeTypeId 知识类型ID
     * @return 可用分类列表
     */
    Result<List<CategoryDTO>> getAvailableCategoriesForKnowledgeType(Long knowledgeTypeId);

    /**
     * 根据细分类型获取分类列表
     *
     * @param request 查询请求参数
     * @return 分类列表
     */
    Result<List<CategoryDTO>> getCategoriesBySubType(GetCategoriesBySubTypeRequest request);
}
