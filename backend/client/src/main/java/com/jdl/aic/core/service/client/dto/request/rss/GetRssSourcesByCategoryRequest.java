package com.jdl.aic.core.service.client.dto.request.rss;

import jakarta.validation.constraints.Size;

/**
 * 根据分类获取RSS源请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetRssSourcesByCategoryRequest {
    
    /**
     * RSS源分类
     */
    @Size(max = 50, message = "分类长度不能超过50个字符")
    private String category;
    
    /**
     * 是否只查询活跃状态的RSS源
     */
    private Boolean activeOnly;
    
    /**
     * 源类型过滤（0:官方, 1:用户订阅）
     */
    private Integer type;
    
    /**
     * 默认构造函数
     */
    public GetRssSourcesByCategoryRequest() {
    }

    /**
     * 构造函数
     *
     * @param category RSS源分类
     * @param activeOnly 是否只查询活跃状态
     */
    public GetRssSourcesByCategoryRequest(String category, Boolean activeOnly) {
        this.category = category;
        this.activeOnly = activeOnly;
    }

    /**
     * 完整构造函数
     *
     * @param category RSS源分类
     * @param activeOnly 是否只查询活跃状态
     * @param type 源类型
     */
    public GetRssSourcesByCategoryRequest(String category, Boolean activeOnly, Integer type) {
        this.category = category;
        this.activeOnly = activeOnly;
        this.type = type;
    }
    
    // Getter and Setter methods
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Boolean getActiveOnly() {
        return activeOnly;
    }
    
    public void setActiveOnly(Boolean activeOnly) {
        this.activeOnly = activeOnly;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    @Override
    public String toString() {
        return "GetRssSourcesByCategoryRequest{" +
                "category='" + category + '\'' +
                ", activeOnly=" + activeOnly +
                ", type=" + type +
                '}';
    }
}
