package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.admin.AdminUserDTO;
import com.jdl.aic.core.service.client.dto.admin.AdminRoleDTO;
import com.jdl.aic.core.service.client.dto.admin.AdminPermissionDTO;
import com.jdl.aic.core.service.client.dto.request.admin.GetAdminUsersRequest;
import com.jdl.aic.core.service.client.dto.request.admin.ToggleAdminUserStatusRequest;
import com.jdl.aic.core.service.client.dto.request.admin.AssignRolesToUserRequest;
import com.jdl.aic.core.service.client.dto.request.admin.RemoveRolesFromUserRequest;
import com.jdl.aic.core.service.client.dto.request.admin.AssignPermissionsToRoleRequest;
import com.jdl.aic.core.service.client.dto.request.admin.RemovePermissionsFromRoleRequest;
import com.jdl.aic.core.service.client.dto.request.admin.HasPermissionRequest;
import com.jdl.aic.core.service.client.dto.request.admin.HasRoleRequest;
import java.util.List;

/**
 * 管理员服务接口
 * 
 * <p>提供管理员权限管理功能，包括：
 * <ul>
 *   <li>管理员用户的CRUD操作</li>
 *   <li>角色和权限管理</li>
 *   <li>用户角色分配</li>
 *   <li>权限验证</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface AdminService {
    
    // ==================== 管理员用户管理 ====================
    
    /**
     * 获取管理员用户列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 管理员用户列表
     */
    Result<PageResult<AdminUserDTO>> getAdminUsers(
            PageRequest pageRequest,
            GetAdminUsersRequest request);
    
    /**
     * 根据ID获取管理员用户详情
     * 
     * @param id 管理员用户ID
     * @return 管理员用户详情
     */
    Result<AdminUserDTO> getAdminUserById(Long id);
    
    /**
     * 根据SSO ID获取管理员用户详情
     * 
     * @param ssoId SSO唯一标识
     * @return 管理员用户详情
     */
    Result<AdminUserDTO> getAdminUserBySsoId(String ssoId);
    
    /**
     * 创建管理员用户
     * 
     * @param adminUser 管理员用户信息
     * @return 创建结果
     */
    Result<AdminUserDTO> createAdminUser(AdminUserDTO adminUser);
    
    /**
     * 更新管理员用户信息
     * 
     * @param id 管理员用户ID
     * @param adminUser 管理员用户信息
     * @return 更新结果
     */
    Result<AdminUserDTO> updateAdminUser(Long id, AdminUserDTO adminUser);
    
    /**
     * 删除管理员用户
     * 
     * @param id 管理员用户ID
     * @return 删除结果
     */
    Result<Void> deleteAdminUser(Long id);
    
    /**
     * 启用/禁用管理员用户
     *
     * @param request 切换状态请求参数
     * @return 操作结果
     */
    Result<Void> toggleAdminUserStatus(ToggleAdminUserStatusRequest request);
    
    // ==================== 角色管理 ====================
    
    /**
     * 获取所有角色列表
     * 
     * @return 角色列表
     */
    Result<List<AdminRoleDTO>> getAllRoles();
    
    /**
     * 根据ID获取角色详情
     * 
     * @param id 角色ID
     * @return 角色详情
     */
    Result<AdminRoleDTO> getRoleById(Long id);
    
    /**
     * 创建角色
     * 
     * @param role 角色信息
     * @return 创建结果
     */
    Result<AdminRoleDTO> createRole(AdminRoleDTO role);
    
    /**
     * 更新角色信息
     * 
     * @param id 角色ID
     * @param role 角色信息
     * @return 更新结果
     */
    Result<AdminRoleDTO> updateRole(Long id, AdminRoleDTO role);
    
    /**
     * 删除角色
     * 
     * @param id 角色ID
     * @return 删除结果
     */
    Result<Void> deleteRole(Long id);
    
    // ==================== 权限管理 ====================
    
    /**
     * 获取所有权限列表
     * 
     * @return 权限列表
     */
    Result<List<AdminPermissionDTO>> getAllPermissions();
    
    /**
     * 根据ID获取权限详情
     * 
     * @param id 权限ID
     * @return 权限详情
     */
    Result<AdminPermissionDTO> getPermissionById(Long id);
    
    /**
     * 创建权限
     * 
     * @param permission 权限信息
     * @return 创建结果
     */
    Result<AdminPermissionDTO> createPermission(AdminPermissionDTO permission);
    
    /**
     * 更新权限信息
     * 
     * @param id 权限ID
     * @param permission 权限信息
     * @return 更新结果
     */
    Result<AdminPermissionDTO> updatePermission(Long id, AdminPermissionDTO permission);
    
    /**
     * 删除权限
     * 
     * @param id 权限ID
     * @return 删除结果
     */
    Result<Void> deletePermission(Long id);
    
    // ==================== 用户角色分配 ====================
    
    /**
     * 为用户分配角色
     *
     * @param request 分配角色请求参数
     * @return 操作结果
     */
    Result<Void> assignRolesToUser(AssignRolesToUserRequest request);

    /**
     * 移除用户的角色
     *
     * @param request 移除角色请求参数
     * @return 操作结果
     */
    Result<Void> removeRolesFromUser(RemoveRolesFromUserRequest request);
    
    /**
     * 获取用户的所有角色
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    Result<List<AdminRoleDTO>> getUserRoles(Long userId);
    
    /**
     * 获取用户的所有权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    Result<List<String>> getUserPermissions(Long userId);
    
    // ==================== 角色权限分配 ====================
    
    /**
     * 为角色分配权限
     *
     * @param request 分配权限请求参数
     * @return 操作结果
     */
    Result<Void> assignPermissionsToRole(AssignPermissionsToRoleRequest request);

    /**
     * 移除角色的权限
     *
     * @param request 移除权限请求参数
     * @return 操作结果
     */
    Result<Void> removePermissionsFromRole(RemovePermissionsFromRoleRequest request);
    
    /**
     * 获取角色的所有权限
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    Result<List<AdminPermissionDTO>> getRolePermissions(Long roleId);
    
    // ==================== 权限验证 ====================
    
    /**
     * 验证用户是否具有指定权限
     *
     * @param request 权限验证请求参数
     * @return 是否具有权限
     */
    Result<Boolean> hasPermission(HasPermissionRequest request);

    /**
     * 验证用户是否具有指定角色
     *
     * @param request 角色验证请求参数
     * @return 是否具有角色
     */
    Result<Boolean> hasRole(HasRoleRequest request);
}
