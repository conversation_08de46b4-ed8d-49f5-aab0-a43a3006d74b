package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.CrawlerContentDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 爬虫内容管理服务接口
 * 
 * <p>提供爬虫内容管理功能，包括：
 * <ul>
 *   <li>爬虫内容的CRUD操作</li>
 *   <li>内容去重和校验</li>
 *   <li>内容状态和质量管理</li>
 *   <li>内容搜索和分析</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface CrawlerContentService {
    
    // ==================== 爬虫内容管理 ====================

    /**
     * 获取爬虫内容列表（分页，支持多条件过滤）
     * 
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @param language 语言过滤
     * @param status 状态过滤
     * @param isFeatured 是否精品内容过滤
     * @param search 搜索关键词
     * @return 爬虫内容列表
     */
    Result<PageResult<CrawlerContentDTO>> getCrawlerContentList(
            PageRequest pageRequest,
            String contentType,
            String language,
            Integer status,
            Boolean isFeatured,
            String search,
            String type,
            String taskId,
            String taskName);

    /**
     * 根据ID获取爬虫内容详情
     * 
     * @param id 内容ID
     * @return 爬虫内容详情
     */
    Result<CrawlerContentDTO> getCrawlerContentById(Long id);

    /**
     * 根据MD5获取爬虫内容
     * 
     * @param contentMd5 内容MD5值
     * @return 爬虫内容信息
     */
    Result<CrawlerContentDTO> getCrawlerContentByMd5(String contentMd5);

    /**
     * 根据链接获取爬虫内容
     * 
     * @param link 原始链接
     * @return 爬虫内容信息
     */
    Result<CrawlerContentDTO> getCrawlerContentByLink(String link);

    /**
     * 创建爬虫内容
     * 
     * @param crawlerContent 爬虫内容信息
     * @return 创建结果
     */
    Result<CrawlerContentDTO> createCrawlerContent(CrawlerContentDTO crawlerContent);

    /**
     * 更新爬虫内容
     * 
     * @param id 内容ID
     * @param crawlerContent 爬虫内容信息
     * @return 更新结果
     */
    Result<CrawlerContentDTO> updateCrawlerContent(Long id, CrawlerContentDTO crawlerContent);

    /**
     * 删除爬虫内容
     * 
     * @param id 内容ID
     * @return 删除结果
     */
    Result<Void> deleteCrawlerContent(Long id);

    // ==================== 内容状态管理 ====================

    /**
     * 根据状态获取爬虫内容列表
     * 
     * @param status 内容状态
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    Result<List<CrawlerContentDTO>> getCrawlerContentsByStatus(Integer status, Integer limit);

    /**
     * 更新内容状态
     * 
     * @param id 内容ID
     * @param status 内容状态
     * @return 更新结果
     */
    Result<Void> updateCrawlerContentStatus(Long id, Integer status);

    /**
     * 批量更新内容状态
     * 
     * @param ids 内容ID列表
     * @param status 内容状态
     * @return 更新结果
     */
    Result<Void> batchUpdateCrawlerContentStatus(List<Long> ids, Integer status);

    // ==================== 内容质量管理 ====================

    /**
     * 更新AI总结
     * 
     * @param id 内容ID
     * @param aiSummary AI总结内容
     * @return 更新结果
     */
    Result<Void> updateAiSummary(Long id, String aiSummary);

    /**
     * 更新质量评分
     * 
     * @param id 内容ID
     * @param qualityScore 质量评分
     * @return 更新结果
     */
    Result<Void> updateQualityScore(Long id, BigDecimal qualityScore);

    /**
     * 更新精品状态
     * 
     * @param id 内容ID
     * @param isFeatured 是否精品内容
     * @return 更新结果
     */
    Result<Void> updateFeaturedStatus(Long id, Boolean isFeatured);

    /**
     * 根据质量评分范围获取内容列表
     * 
     * @param minScore 最小评分
     * @param maxScore 最大评分
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    Result<List<CrawlerContentDTO>> getCrawlerContentsByQualityScore(
            BigDecimal minScore, BigDecimal maxScore, Integer limit);

    // ==================== 内容分类查询 ====================

    /**
     * 根据内容类型获取内容列表
     * 
     * @param contentType 内容类型
     * @param isFeatured 是否精品内容
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    Result<List<CrawlerContentDTO>> getCrawlerContentsByType(
            String contentType, Boolean isFeatured, Integer limit);

    /**
     * 根据语言获取内容列表
     * 
     * @param language 内容语言
     * @param status 内容状态
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    Result<List<CrawlerContentDTO>> getCrawlerContentsByLanguage(
            String language, Integer status, Integer limit);

    /**
     * 根据发布时间范围获取内容列表
     * 
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param status 内容状态
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    Result<List<CrawlerContentDTO>> getCrawlerContentsByPubDateRange(
            LocalDateTime startDate, LocalDateTime endDate, Integer status, Integer limit);

    // ==================== 批量操作 ====================

    /**
     * 批量创建爬虫内容
     * 
     * @param contents 爬虫内容列表
     * @return 创建结果
     */
    Result<List<CrawlerContentDTO>> batchCreateCrawlerContents(List<CrawlerContentDTO> contents);

    /**
     * 批量删除爬虫内容
     * 
     * @param ids 内容ID列表
     * @return 删除结果
     */
    Result<Void> batchDeleteCrawlerContents(List<Long> ids);

    // ==================== 搜索和统计 ====================

    /**
     * 搜索爬虫内容
     * 
     * @param keyword 搜索关键词
     * @param contentType 内容类型过滤
     * @param language 语言过滤
     * @param status 状态过滤
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    Result<List<CrawlerContentDTO>> searchCrawlerContents(
            String keyword, String contentType, String language, Integer status, Integer limit);

    /**
     * 统计各状态的内容数量
     * 
     * @return 状态统计结果
     */
    Result<List<Object>> getContentStatusStatistics();

    /**
     * 统计各内容类型的数量
     * 
     * @return 内容类型统计结果
     */
    Result<List<Object>> getContentTypeStatistics();

    // ==================== 内容去重 ====================

    /**
     * 检查内容是否已存在（根据MD5）
     * 
     * @param contentMd5 内容MD5值
     * @return 是否存在
     */
    Result<Boolean> isContentExists(String contentMd5);

    /**
     * 检查链接是否已存在
     *
     * @param link 原始链接
     * @return 是否存在
     */
    Result<Boolean> isLinkExists(String link);

    /**
     * 根据新增字段查询爬虫内容
     *
     * @param type 内容类型（新字段）
     * @param taskId 任务ID
     * @param status 状态过滤
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    Result<List<CrawlerContentDTO>> selectByNewFields(String type, String taskId, Integer status, Integer limit);

    /**
     * 根据多个条件组合查询（支持 IN 查询）
     *
     * @param ids ID列表
     * @param contentTypes 内容类型列表
     * @param languages 语言列表
     * @param statuses 状态列表
     * @param types 新内容类型列表
     * @param taskIds 任务ID列表
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    Result<List<CrawlerContentDTO>> selectByMultipleConditions(
            List<Long> ids,
            List<String> contentTypes,
            List<String> languages,
            List<Integer> statuses,
            List<String> types,
            List<String> taskIds,
            Integer limit);
            
    /**
     * 根据type查询去重后的taskName和taskId
     *
     * @param type 内容类型
     * @return 去重后的任务信息列表
     */
    Result<List<CrawlerContentDTO>> getDistinctTaskInfoByType(String type);
    
    /**
     * 统计各type类型的内容数量
     *
     * @return type类型统计结果，包含type和count字段
     */
    Result<List<Map<String, Object>>> countContentByType();
}
