package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 启用/禁用分享选项配置请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToggleShareOptionConfigStatusRequest {
    
    /**
     * 配置ID
     */
    private Long id;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 默认构造函数
     */
    public ToggleShareOptionConfigStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 配置ID
     * @param isEnabled 是否启用
     */
    public ToggleShareOptionConfigStatusRequest(Long id, Boolean isEnabled) {
        this.id = id;
        this.isEnabled = isEnabled;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    @Override
    public String toString() {
        return "ToggleShareOptionConfigStatusRequest{" +
                "id=" + id +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
