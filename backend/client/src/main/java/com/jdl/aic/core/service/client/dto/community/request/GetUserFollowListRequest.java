package com.jdl.aic.core.service.client.dto.community.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.constraints.NotNull;

/**
 * 获取用户关注列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetUserFollowListRequest extends PageRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 关注类型（following:关注的人, followers:粉丝）
     */
    private String followType;
    
    /**
     * 被关注人用户名过滤（模糊搜索）
     */
    private String followedUsername;
    
    /**
     * 被关注人显示名称过滤（模糊搜索）
     */
    private String followedDisplayName;
    
    /**
     * 被关注人部门过滤
     */
    private String followedDepartment;
    
    /**
     * 被关注人是否活跃过滤
     */
    private Boolean followedIsActive;
    
    /**
     * 开始时间过滤
     */
    private String startDate;
    
    /**
     * 结束时间过滤
     */
    private String endDate;
    
    /**
     * 默认构造函数
     */
    public GetUserFollowListRequest() {
        super();
    }
    
    /**
     * 构造函数
     * 
     * @param page 页码
     * @param size 页大小
     * @param userId 用户ID
     */
    public GetUserFollowListRequest(Integer page, Integer size, Long userId) {
        super(page, size);
        this.userId = userId;
    }
    
    /**
     * 构造函数
     * 
     * @param page 页码
     * @param size 页大小
     * @param userId 用户ID
     * @param followType 关注类型
     */
    public GetUserFollowListRequest(Integer page, Integer size, Long userId, String followType) {
        super(page, size);
        this.userId = userId;
        this.followType = followType;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getFollowType() {
        return followType;
    }
    
    public void setFollowType(String followType) {
        this.followType = followType;
    }
    
    public String getFollowedUsername() {
        return followedUsername;
    }
    
    public void setFollowedUsername(String followedUsername) {
        this.followedUsername = followedUsername;
    }
    
    public String getFollowedDisplayName() {
        return followedDisplayName;
    }
    
    public void setFollowedDisplayName(String followedDisplayName) {
        this.followedDisplayName = followedDisplayName;
    }
    
    public String getFollowedDepartment() {
        return followedDepartment;
    }
    
    public void setFollowedDepartment(String followedDepartment) {
        this.followedDepartment = followedDepartment;
    }
    
    public Boolean getFollowedIsActive() {
        return followedIsActive;
    }
    
    public void setFollowedIsActive(Boolean followedIsActive) {
        this.followedIsActive = followedIsActive;
    }
    
    public String getStartDate() {
        return startDate;
    }
    
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    
    public String getEndDate() {
        return endDate;
    }
    
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
    
    @Override
    public String toString() {
        return "GetUserFollowListRequest{" +
                "userId=" + userId +
                ", followType='" + followType + '\'' +
                ", followedUsername='" + followedUsername + '\'' +
                ", followedDisplayName='" + followedDisplayName + '\'' +
                ", followedDepartment='" + followedDepartment + '\'' +
                ", followedIsActive=" + followedIsActive +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", page=" + getPage() +
                ", size=" + getSize() +
                '}';
    }
}
