package com.jdl.aic.core.service.client.dto.request.newsfeed;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 更新资讯状态请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateNewsFeedStatusRequest {
    
    /**
     * 资讯ID
     */
    @NotNull(message = "资讯ID不能为空")
    private Long id;
    
    /**
     * 状态（0:待审核, 1:已发布, 2:已下线）
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
    
    /**
     * 默认构造函数
     */
    public UpdateNewsFeedStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 资讯ID
     * @param status 状态
     */
    public UpdateNewsFeedStatusRequest(Long id, Integer status) {
        this.id = id;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "UpdateNewsFeedStatusRequest{" +
                "id=" + id +
                ", status=" + status +
                '}';
    }
}
