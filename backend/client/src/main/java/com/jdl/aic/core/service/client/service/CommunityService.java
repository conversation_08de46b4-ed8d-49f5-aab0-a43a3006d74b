package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.CommentDTO;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.ShareDTO;
import com.jdl.aic.core.service.client.dto.request.community.GetCommentListRequest;
import com.jdl.aic.core.service.client.dto.request.community.DeleteCommentRequest;
import com.jdl.aic.core.service.client.dto.request.community.GetUserCommentsRequest;
import com.jdl.aic.core.service.client.dto.request.community.BatchReviewCommentsRequest;
import com.jdl.aic.core.service.client.dto.request.community.LikeContentRequest;
import com.jdl.aic.core.service.client.dto.request.community.UnlikeContentRequest;
import com.jdl.aic.core.service.client.dto.request.community.IsContentLikedRequest;

import java.util.List;

/**
 * 社区互动服务接口
 * 
 * <p>提供社区互动功能，包括：
 * <ul>
 *   <li>评论管理</li>
 *   <li>点赞和收藏</li>
 *   <li>内容分享</li>
 *   <li>用户关注</li>
 *   <li>社区统计</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface CommunityService {
    
    // ==================== 评论管理 ====================
    
    /**
     * 获取内容的评论列表（分页）
     *
     * @param request 获取评论列表请求参数
     * @param pageRequest 分页请求
     * @return 评论列表
     */
    Result<PageResult<CommentDTO>> getCommentList(
            GetCommentListRequest request,
            PageRequest pageRequest);
    
    /**
     * 根据ID获取评论详情
     * 
     * @param id 评论ID
     * @return 评论详情
     */
    Result<CommentDTO> getCommentById(Long id);
    
    /**
     * 创建评论
     * 
     * @param comment 评论信息
     * @return 创建结果
     */
    Result<CommentDTO> createComment(CommentDTO comment);
    
    /**
     * 更新评论
     * 
     * @param id 评论ID
     * @param comment 评论信息
     * @return 更新结果
     */
    Result<CommentDTO> updateComment(Long id, CommentDTO comment);
    
    /**
     * 删除评论
     *
     * @param request 删除评论请求参数
     * @return 删除结果
     */
    Result<Void> deleteComment(DeleteCommentRequest request);
    
    /**
     * 获取用户的评论列表
     *
     * @param request 获取用户评论请求参数
     * @param pageRequest 分页请求
     * @return 用户评论列表
     */
    Result<PageResult<CommentDTO>> getUserComments(
            GetUserCommentsRequest request,
            PageRequest pageRequest);
    
    /**
     * 批量审核评论
     *
     * @param request 批量审核评论请求参数
     * @return 操作结果
     */
    Result<Void> batchReviewComments(BatchReviewCommentsRequest request);
    
    // ==================== 点赞管理 ====================
    
    /**
     * 点赞内容
     *
     * @param request 点赞内容请求参数
     * @return 操作结果
     */
    Result<Void> likeContent(LikeContentRequest request);
    
    /**
     * 取消点赞
     *
     * @param request 取消点赞请求参数
     * @return 操作结果
     */
    Result<Void> unlikeContent(UnlikeContentRequest request);
    
    /**
     * 检查用户是否已点赞
     *
     * @param request 检查点赞状态请求参数
     * @return 是否已点赞
     */
    Result<Boolean> isContentLiked(IsContentLikedRequest request);
    
    /**
     * 获取内容的点赞用户列表
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param pageRequest 分页请求
     * @return 点赞用户列表
     */
    Result<PageResult<Object>> getContentLikers(
            String contentType,
            Long contentId,
            PageRequest pageRequest);
    
    /**
     * 获取用户点赞的内容列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @return 用户点赞列表
     */
    Result<PageResult<Object>> getUserLikes(
            Long userId,
            PageRequest pageRequest,
            String contentType);
    
    // ==================== 收藏管理 ====================
    
    /**
     * 收藏内容
     * 
     * @param favorite 收藏信息
     * @return 收藏结果
     */
    Result<FavoriteDTO> favoriteContent(FavoriteDTO favorite);
    
    /**
     * 取消收藏
     * 
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 操作结果
     */
    Result<Void> unfavoriteContent(Long userId, String contentType, Long contentId);
    
    /**
     * 检查用户是否已收藏
     * 
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 是否已收藏
     */
    Result<Boolean> isContentFavorited(Long userId, String contentType, Long contentId);
    
    /**
     * 获取用户收藏列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @param folderName 收藏夹名称过滤
     * @return 用户收藏列表
     */
    Result<PageResult<FavoriteDTO>> getUserFavorites(
            Long userId,
            PageRequest pageRequest,
            String contentType,
            String folderName);
    
    /**
     * 获取用户收藏夹列表
     * 
     * @param userId 用户ID
     * @return 收藏夹列表
     */
    Result<List<String>> getUserFavoriteFolders(Long userId);
    
    /**
     * 移动收藏到指定收藏夹
     * 
     * @param favoriteId 收藏ID
     * @param folderName 目标收藏夹名称
     * @return 操作结果
     */
    Result<Void> moveFavoriteToFolder(Long favoriteId, String folderName);
    
    // ==================== 分享管理 ====================
    
    /**
     * 分享内容
     * 
     * @param share 分享信息
     * @return 分享结果
     */
    Result<ShareDTO> shareContent(ShareDTO share);
    
    /**
     * 获取内容分享记录
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param pageRequest 分页请求
     * @return 分享记录列表
     */
    Result<PageResult<ShareDTO>> getContentShares(
            String contentType,
            Long contentId,
            PageRequest pageRequest);
    
    /**
     * 获取用户分享记录
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @return 用户分享记录
     */
    Result<PageResult<ShareDTO>> getUserShares(
            Long userId,
            PageRequest pageRequest,
            String contentType);
    
    /**
     * 增加分享次数统计
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 操作结果
     */
    Result<Void> incrementShareCount(String contentType, Long contentId);
    
    // ==================== 用户关注 ====================
    
    /**
     * 关注用户
     * 
     * @param followerId 关注者ID
     * @param followeeId 被关注者ID
     * @return 操作结果
     */
    Result<Void> followUser(Long followerId, Long followeeId);
    
    /**
     * 取消关注
     * 
     * @param followerId 关注者ID
     * @param followeeId 被关注者ID
     * @return 操作结果
     */
    Result<Void> unfollowUser(Long followerId, Long followeeId);
    
    /**
     * 检查是否已关注
     * 
     * @param followerId 关注者ID
     * @param followeeId 被关注者ID
     * @return 是否已关注
     */
    Result<Boolean> isUserFollowed(Long followerId, Long followeeId);
    
    /**
     * 获取用户关注列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @return 关注列表
     */
    Result<PageResult<Object>> getUserFollowing(Long userId, PageRequest pageRequest);
    
    /**
     * 获取用户粉丝列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @return 粉丝列表
     */
    Result<PageResult<Object>> getUserFollowers(Long userId, PageRequest pageRequest);
    
    /**
     * 获取用户社区统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息（关注数、粉丝数、点赞数、评论数等）
     */
    Result<Object> getUserCommunityStats(Long userId);
}
