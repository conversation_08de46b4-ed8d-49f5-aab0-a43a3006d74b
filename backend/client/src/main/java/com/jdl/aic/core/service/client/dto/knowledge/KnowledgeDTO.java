package com.jdl.aic.core.service.client.dto.knowledge;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 知识内容DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KnowledgeDTO {
    
    /**
     * 知识ID
     */
    private Long id;
    
    /**
     * 知识标题
     */
    @NotBlank(message = "知识标题不能为空")
    private String title;
    
    /**
     * 知识描述
     */
    private String description;
    
    /**
     * 知识内容
     */
    private String content;
    
    /**
     * 知识类型ID
     */
    @NotNull(message = "知识类型不能为空")
    private Long knowledgeTypeId;
    
    /**
     * 知识类型编码
     */
    private String knowledgeTypeCode;
    
    /**
     * 知识类型名称
     */
    private String knowledgeTypeName;
    
    /**
     * 作者ID
     */
    private String authorId;
    
    /**
     * 作者姓名
     */
    private String authorName;
    
    /**
     * 状态（0:草稿, 1:待审核, 2:已发布, 3:已下线, 4:已拒绝）
     */
    private Integer status;
    
    /**
     * 可见性（0:私有, 1:团队可见, 2:公开）
     */
    private Integer visibility;
    
    /**
     * 团队ID（当visibility为团队可见时）
     */
    private Long teamId;
    
    /**
     * 团队名称
     */
    private String teamName;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 阅读次数
     */
    private Integer readCount;
    
    /**
     * 点赞次数
     */
    private Integer likeCount;
    
    /**
     * 评论次数
     */
    private Integer commentCount;
    
    /**
     * Fork次数
     */
    private Integer forkCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 分享次数
     */
    private Integer shareCount;

    /**
     * 社交热度分数
     */
    private java.math.BigDecimal socialScore;

    /**
     * 最后社交活动时间
     */
    private LocalDateTime lastSocialActivityAt;

    /**
     * 封面图片URL
     */
    private String coverImageUrl;
    
    /**
     * 元数据JSON（知识类型特有的结构化数据）
     */
    private Map<String, Object> metadataJson;
    
    /**
     * AI审核状态（0:未审, 1:通过, 2:拒绝, 3:人工复审）
     */
    private Integer aiReviewStatus;
    
    /**
     * AI推荐标签
     */
    private List<String> aiTags;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;

    /**
     * 关联的分类
     */
    private List<ContentCategoryRelationDTO> categories;

    /**
     * 默认构造函数
     */
    public KnowledgeDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Long getKnowledgeTypeId() {
        return knowledgeTypeId;
    }
    
    public void setKnowledgeTypeId(Long knowledgeTypeId) {
        this.knowledgeTypeId = knowledgeTypeId;
    }
    
    public String getKnowledgeTypeCode() {
        return knowledgeTypeCode;
    }
    
    public void setKnowledgeTypeCode(String knowledgeTypeCode) {
        this.knowledgeTypeCode = knowledgeTypeCode;
    }
    
    public String getKnowledgeTypeName() {
        return knowledgeTypeName;
    }
    
    public void setKnowledgeTypeName(String knowledgeTypeName) {
        this.knowledgeTypeName = knowledgeTypeName;
    }
    
    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }
    
    public String getAuthorName() {
        return authorName;
    }
    
    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getVisibility() {
        return visibility;
    }
    
    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }
    
    public Long getTeamId() {
        return teamId;
    }
    
    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
    
    public String getTeamName() {
        return teamName;
    }
    
    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }
    
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public Integer getReadCount() {
        return readCount;
    }
    
    public void setReadCount(Integer readCount) {
        this.readCount = readCount;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public Integer getCommentCount() {
        return commentCount;
    }
    
    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }
    
    public Integer getForkCount() {
        return forkCount;
    }
    
    public void setForkCount(Integer forkCount) {
        this.forkCount = forkCount;
    }

    public Integer getFavoriteCount() {
        return favoriteCount;
    }

    public void setFavoriteCount(Integer favoriteCount) {
        this.favoriteCount = favoriteCount;
    }

    public Integer getShareCount() {
        return shareCount;
    }

    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    public java.math.BigDecimal getSocialScore() {
        return socialScore;
    }

    public void setSocialScore(java.math.BigDecimal socialScore) {
        this.socialScore = socialScore;
    }

    public LocalDateTime getLastSocialActivityAt() {
        return lastSocialActivityAt;
    }

    public void setLastSocialActivityAt(LocalDateTime lastSocialActivityAt) {
        this.lastSocialActivityAt = lastSocialActivityAt;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }
    
    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }
    
    public Map<String, Object> getMetadataJson() {
        return metadataJson;
    }
    
    public void setMetadataJson(Map<String, Object> metadataJson) {
        this.metadataJson = metadataJson;
    }
    
    public Integer getAiReviewStatus() {
        return aiReviewStatus;
    }
    
    public void setAiReviewStatus(Integer aiReviewStatus) {
        this.aiReviewStatus = aiReviewStatus;
    }
    
    public List<String> getAiTags() {
        return aiTags;
    }
    
    public void setAiTags(List<String> aiTags) {
        this.aiTags = aiTags;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public List<ContentCategoryRelationDTO> getCategories() {
        return categories;
    }

    public void setCategories(List<ContentCategoryRelationDTO> categories) {
        this.categories = categories;
    }
}
