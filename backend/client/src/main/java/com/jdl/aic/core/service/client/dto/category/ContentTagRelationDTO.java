package com.jdl.aic.core.service.client.dto.category;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 内容标签关联DTO
 * 
 * <p>用于管理统一内容标签关联表的数据传输，支持所有内容类型与标签的关联关系。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContentTagRelationDTO {
    
    /**
     * 关联ID
     */
    private Long id;
    
    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private ContentType contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 标签ID
     */
    @NotNull(message = "标签ID不能为空")
    private Long tagId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 默认构造函数
     */
    public ContentTagRelationDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param tagId 标签ID
     */
    public ContentTagRelationDTO(ContentType contentType, Long contentId, Long tagId) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.tagId = tagId;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public ContentType getContentType() {
        return contentType;
    }
    
    public void setContentType(ContentType contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public Long getTagId() {
        return tagId;
    }
    
    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "ContentTagRelationDTO{" +
                "id=" + id +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                ", tagId=" + tagId +
                ", createdAt=" + createdAt +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ContentTagRelationDTO that = (ContentTagRelationDTO) o;
        
        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (contentType != that.contentType) return false;
        if (contentId != null ? !contentId.equals(that.contentId) : that.contentId != null) return false;
        return tagId != null ? tagId.equals(that.tagId) : that.tagId == null;
    }
    
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (contentType != null ? contentType.hashCode() : 0);
        result = 31 * result + (contentId != null ? contentId.hashCode() : 0);
        result = 31 * result + (tagId != null ? tagId.hashCode() : 0);
        return result;
    }
}
