package com.jdl.aic.core.service.client.dto.request.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量审核内容请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchReviewContentRequest {
    
    /**
     * 审核ID列表
     */
    @NotEmpty(message = "审核ID列表不能为空")
    private List<Long> reviewIds;
    
    /**
     * 审核状态
     */
    @NotNull(message = "审核状态不能为空")
    private Integer reviewStatus;
    
    /**
     * 审核意见
     */
    private String reviewComment;
    
    /**
     * 审核人ID
     */
    @NotNull(message = "审核人ID不能为空")
    private Long reviewerId;
    
    /**
     * 默认构造函数
     */
    public BatchReviewContentRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param reviewIds 审核ID列表
     * @param reviewStatus 审核状态
     * @param reviewComment 审核意见
     * @param reviewerId 审核人ID
     */
    public BatchReviewContentRequest(List<Long> reviewIds, Integer reviewStatus, String reviewComment, Long reviewerId) {
        this.reviewIds = reviewIds;
        this.reviewStatus = reviewStatus;
        this.reviewComment = reviewComment;
        this.reviewerId = reviewerId;
    }
    
    // Getter and Setter methods
    
    public List<Long> getReviewIds() {
        return reviewIds;
    }
    
    public void setReviewIds(List<Long> reviewIds) {
        this.reviewIds = reviewIds;
    }
    
    public Integer getReviewStatus() {
        return reviewStatus;
    }
    
    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    
    public String getReviewComment() {
        return reviewComment;
    }
    
    public void setReviewComment(String reviewComment) {
        this.reviewComment = reviewComment;
    }
    
    public Long getReviewerId() {
        return reviewerId;
    }
    
    public void setReviewerId(Long reviewerId) {
        this.reviewerId = reviewerId;
    }
    
    @Override
    public String toString() {
        return "BatchReviewContentRequest{" +
                "reviewIds=" + reviewIds +
                ", reviewStatus=" + reviewStatus +
                ", reviewComment='" + reviewComment + '\'' +
                ", reviewerId=" + reviewerId +
                '}';
    }
}
