package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 复制配置到新的内容类型请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CopyConfigsToContentTypeRequest {
    
    /**
     * 源内容类型
     */
    private String sourceContentType;
    
    /**
     * 目标内容类型
     */
    private String targetContentType;
    
    /**
     * 默认构造函数
     */
    public CopyConfigsToContentTypeRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param sourceContentType 源内容类型
     * @param targetContentType 目标内容类型
     */
    public CopyConfigsToContentTypeRequest(String sourceContentType, String targetContentType) {
        this.sourceContentType = sourceContentType;
        this.targetContentType = targetContentType;
    }
    
    // Getter and Setter methods
    
    public String getSourceContentType() {
        return sourceContentType;
    }
    
    public void setSourceContentType(String sourceContentType) {
        this.sourceContentType = sourceContentType;
    }
    
    public String getTargetContentType() {
        return targetContentType;
    }
    
    public void setTargetContentType(String targetContentType) {
        this.targetContentType = targetContentType;
    }
    
    @Override
    public String toString() {
        return "CopyConfigsToContentTypeRequest{" +
                "sourceContentType='" + sourceContentType + '\'' +
                ", targetContentType='" + targetContentType + '\'' +
                '}';
    }
}
