package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 搜索项DTO
 * 
 * <p>封装单个搜索结果项的信息，包括内容基本信息、匹配度、高亮片段等。
 * 支持多种内容类型的统一展示格式。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchItemDTO {
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long id;
    
    /**
     * 内容类型（knowledge:知识, solution:解决方案, learning_resource:学习资源, user:用户）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;
    
    /**
     * 标题
     */
    @NotBlank(message = "标题不能为空")
    private String title;
    
    /**
     * 描述/摘要
     */
    private String description;
    
    /**
     * 内容URL
     */
    private String url;
    
    /**
     * 封面图片URL
     */
    private String coverImageUrl;
    
    /**
     * 作者ID
     */
    private Long authorId;
    
    /**
     * 作者姓名
     */
    private String authorName;
    
    /**
     * 作者头像
     */
    private String authorAvatar;
    
    /**
     * 分类信息
     */
    private String category;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 搜索匹配度评分
     */
    private Double score;
    
    /**
     * 高亮片段
     */
    private Map<String, List<String>> highlights;
    
    /**
     * 统计信息（浏览数、点赞数等）
     */
    private Map<String, Integer> statistics;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> attributes;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public SearchItemDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getCoverImageUrl() {
        return coverImageUrl;
    }
    
    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }
    
    public Long getAuthorId() {
        return authorId;
    }
    
    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }
    
    public String getAuthorName() {
        return authorName;
    }
    
    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }
    
    public String getAuthorAvatar() {
        return authorAvatar;
    }
    
    public void setAuthorAvatar(String authorAvatar) {
        this.authorAvatar = authorAvatar;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public Double getScore() {
        return score;
    }
    
    public void setScore(Double score) {
        this.score = score;
    }
    
    public Map<String, List<String>> getHighlights() {
        return highlights;
    }
    
    public void setHighlights(Map<String, List<String>> highlights) {
        this.highlights = highlights;
    }
    
    public Map<String, Integer> getStatistics() {
        return statistics;
    }
    
    public void setStatistics(Map<String, Integer> statistics) {
        this.statistics = statistics;
    }
    
    public Map<String, Object> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(Map<String, Object> attributes) {
        this.attributes = attributes;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "SearchItemDTO{" +
                "id=" + id +
                ", contentType='" + contentType + '\'' +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", url='" + url + '\'' +
                ", coverImageUrl='" + coverImageUrl + '\'' +
                ", authorId=" + authorId +
                ", authorName='" + authorName + '\'' +
                ", authorAvatar='" + authorAvatar + '\'' +
                ", category='" + category + '\'' +
                ", tags=" + tags +
                ", score=" + score +
                ", highlights=" + highlights +
                ", statistics=" + statistics +
                ", attributes=" + attributes +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
