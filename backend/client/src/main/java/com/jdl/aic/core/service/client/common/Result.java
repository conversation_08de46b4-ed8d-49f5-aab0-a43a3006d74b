package com.jdl.aic.core.service.client.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;

/**
 * 统一响应结果封装
 * 
 * @param <T> 响应数据类型
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> {
    
    /**
     * 响应状态码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 请求ID（用于追踪）
     */
    private String requestId;
    
    /**
     * 详细错误信息（可选）
     */
    private Object details;
    
    /**
     * 默认构造函数
     */
    public Result() {
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     * 
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     */
    public Result(String code, String message, T data) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 创建成功响应
     * 
     * @param <T> 数据类型
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> Result<T> success(T data) {
        return new Result<>("SUCCESS", "操作成功", data);
    }
    
    /**
     * 创建成功响应（无数据）
     * 
     * @return 成功响应
     */
    public static Result<Void> success() {
        return new Result<>("SUCCESS", "操作成功", null);
    }
    
    /**
     * 创建成功响应（自定义消息）
     * 
     * @param <T> 数据类型
     * @param message 自定义消息
     * @param data 响应数据
     * @return 成功响应
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>("SUCCESS", message, data);
    }
    
    /**
     * 创建失败响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @return 失败响应
     */
    public static Result<Void> error(String code, String message) {
        return new Result<>(code, message, null);
    }

    /**
     * 创建错误结果的泛型辅助方法
     */
    @SuppressWarnings("unchecked")
    public static <T> Result<T> errorResult(String code, String message) {
        return (Result<T>) Result.error(code, message);
    }
    
    /**
     * 创建失败响应（带详细信息）
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param details 详细信息
     * @return 失败响应
     */
    public static Result<Void> error(String code, String message, Object details) {
        Result<Void> result = new Result<>(code, message, null);
        result.setDetails(details);
        return result;
    }
    
    /**
     * 判断是否成功
     * 
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(this.code);
    }
    
    // Getter and Setter methods
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getRequestId() {
        return requestId;
    }
    
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }
    
    public Object getDetails() {
        return details;
    }
    
    public void setDetails(Object details) {
        this.details = details;
    }
    
    @Override
    public String toString() {
        return "Result{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                ", requestId='" + requestId + '\'' +
                ", details=" + details +
                '}';
    }
}
