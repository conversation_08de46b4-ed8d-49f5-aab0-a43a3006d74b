package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 根据分享类型获取所有配置请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetShareOptionConfigsByShareTypeRequest {
    
    /**
     * 分享类型
     */
    private String shareType;
    
    /**
     * 是否启用（可选）
     */
    private Boolean isEnabled;
    
    /**
     * 默认构造函数
     */
    public GetShareOptionConfigsByShareTypeRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param shareType 分享类型
     * @param isEnabled 是否启用
     */
    public GetShareOptionConfigsByShareTypeRequest(String shareType, Boolean isEnabled) {
        this.shareType = shareType;
        this.isEnabled = isEnabled;
    }
    
    // Getter and Setter methods
    
    public String getShareType() {
        return shareType;
    }
    
    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    @Override
    public String toString() {
        return "GetShareOptionConfigsByShareTypeRequest{" +
                "shareType='" + shareType + '\'' +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
