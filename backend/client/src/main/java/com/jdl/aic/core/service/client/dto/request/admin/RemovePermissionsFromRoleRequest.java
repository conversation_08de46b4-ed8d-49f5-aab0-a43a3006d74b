package com.jdl.aic.core.service.client.dto.request.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 移除角色权限请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RemovePermissionsFromRoleRequest {
    
    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    private Long roleId;
    
    /**
     * 权限ID列表
     */
    @NotEmpty(message = "权限ID列表不能为空")
    private List<Long> permissionIds;
    
    /**
     * 默认构造函数
     */
    public RemovePermissionsFromRoleRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    public RemovePermissionsFromRoleRequest(Long roleId, List<Long> permissionIds) {
        this.roleId = roleId;
        this.permissionIds = permissionIds;
    }
    
    // Getter and Setter methods
    
    public Long getRoleId() {
        return roleId;
    }
    
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
    
    public List<Long> getPermissionIds() {
        return permissionIds;
    }
    
    public void setPermissionIds(List<Long> permissionIds) {
        this.permissionIds = permissionIds;
    }
    
    @Override
    public String toString() {
        return "RemovePermissionsFromRoleRequest{" +
                "roleId=" + roleId +
                ", permissionIds=" + permissionIds +
                '}';
    }
}
