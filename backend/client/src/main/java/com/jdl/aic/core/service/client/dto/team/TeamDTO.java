package com.jdl.aic.core.service.client.dto.team;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 团队DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TeamDTO {
    
    /**
     * 团队ID
     */
    private Long id;
    
    /**
     * 团队名称
     */
    @NotBlank(message = "团队名称不能为空")
    private String name;
    
    /**
     * 团队描述
     */
    private String description;
    
    /**
     * 父团队ID
     */
    private Long parentId;
    
    /**
     * 父团队名称
     */
    private String parentName;
    
    /**
     * 团队是否活跃
     */
    private Boolean isActive;
    
    /**
     * 子团队列表
     */
    private List<TeamDTO> children;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;

    /**
     * 团队标签列表
     */
    private List<String> tags;

    /**
     * 团队头像URL
     */
    private String avatarUrl;

    /**
     * 团队隐私设置（0:公开, 1:私有）
     */
    private Integer privacy;

    /**
     * 团队邀请设置（0:任何人可邀请, 1:仅管理员可邀请, 2:仅创建者可邀请）
     */
    private Integer inviteSetting;

    /**
     * 默认构造函数
     */
    public TeamDTO() {
    }

    /**
     * 基础构造函数
     *
     * @param name 团队名称
     */
    public TeamDTO(String name) {
        this.name = name;
    }

    /**
     * 完整构造函数
     *
     * @param name 团队名称
     * @param description 团队描述
     * @param parentId 父团队ID
     */
    public TeamDTO(String name, String description, Long parentId) {
        this.name = name;
        this.description = description;
        this.parentId = parentId;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public String getParentName() {
        return parentName;
    }
    
    public void setParentName(String parentName) {
        this.parentName = parentName;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public List<TeamDTO> getChildren() {
        return children;
    }
    
    public void setChildren(List<TeamDTO> children) {
        this.children = children;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }
    
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public Integer getPrivacy() {
        return privacy;
    }

    public void setPrivacy(Integer privacy) {
        this.privacy = privacy;
    }

    public Integer getInviteSetting() {
        return inviteSetting;
    }

    public void setInviteSetting(Integer inviteSetting) {
        this.inviteSetting = inviteSetting;
    }

    @Override
    public String toString() {
        return "TeamDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", parentId=" + parentId +
                ", parentName='" + parentName + '\'' +
                ", isActive=" + isActive +
                ", tags=" + tags +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", privacy=" + privacy +
                ", inviteSetting=" + inviteSetting +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy='" + createdBy + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TeamDTO teamDTO = (TeamDTO) o;

        if (id != null ? !id.equals(teamDTO.id) : teamDTO.id != null) return false;
        if (name != null ? !name.equals(teamDTO.name) : teamDTO.name != null) return false;
        if (description != null ? !description.equals(teamDTO.description) : teamDTO.description != null) return false;
        if (parentId != null ? !parentId.equals(teamDTO.parentId) : teamDTO.parentId != null) return false;
        if (parentName != null ? !parentName.equals(teamDTO.parentName) : teamDTO.parentName != null) return false;
        if (isActive != null ? !isActive.equals(teamDTO.isActive) : teamDTO.isActive != null) return false;
        if (tags != null ? !tags.equals(teamDTO.tags) : teamDTO.tags != null) return false;
        if (avatarUrl != null ? !avatarUrl.equals(teamDTO.avatarUrl) : teamDTO.avatarUrl != null) return false;
        if (privacy != null ? !privacy.equals(teamDTO.privacy) : teamDTO.privacy != null) return false;
        if (inviteSetting != null ? !inviteSetting.equals(teamDTO.inviteSetting) : teamDTO.inviteSetting != null) return false;
        if (createdBy != null ? !createdBy.equals(teamDTO.createdBy) : teamDTO.createdBy != null) return false;
        return updatedBy != null ? updatedBy.equals(teamDTO.updatedBy) : teamDTO.updatedBy == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        result = 31 * result + (parentId != null ? parentId.hashCode() : 0);
        result = 31 * result + (parentName != null ? parentName.hashCode() : 0);
        result = 31 * result + (isActive != null ? isActive.hashCode() : 0);
        result = 31 * result + (tags != null ? tags.hashCode() : 0);
        result = 31 * result + (avatarUrl != null ? avatarUrl.hashCode() : 0);
        result = 31 * result + (privacy != null ? privacy.hashCode() : 0);
        result = 31 * result + (inviteSetting != null ? inviteSetting.hashCode() : 0);
        result = 31 * result + (createdBy != null ? createdBy.hashCode() : 0);
        result = 31 * result + (updatedBy != null ? updatedBy.hashCode() : 0);
        return result;
    }
}
