package com.jdl.aic.core.service.client.dto.request.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 获取内容评论列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCommentListRequest {
    
    /**
     * 内容类型（knowledge, solution, learning_resource）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 父评论ID（获取回复时使用）
     */
    private Long parentId;
    
    /**
     * 默认构造函数
     */
    public GetCommentListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param parentId 父评论ID
     */
    public GetCommentListRequest(String contentType, Long contentId, Long parentId) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.parentId = parentId;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    @Override
    public String toString() {
        return "GetCommentListRequest{" +
                "contentType='" + contentType + '\'' +
                ", contentId=" + contentId +
                ", parentId=" + parentId +
                '}';
    }
}
