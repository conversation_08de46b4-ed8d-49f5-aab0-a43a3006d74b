package com.jdl.aic.core.service.client.dto.community.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.constraints.NotNull;

/**
 * 获取用户团队收藏列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetUserTeamFavoritesRequest extends PageRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 团队名称过滤（模糊搜索）
     */
    private String teamName;
    
    /**
     * 团队隐私设置过滤
     */
    private String teamPrivacy;
    
    /**
     * 团队是否活跃过滤
     */
    private Boolean teamIsActive;
    
    /**
     * 开始时间过滤
     */
    private String startDate;
    
    /**
     * 结束时间过滤
     */
    private String endDate;
    
    /**
     * 默认构造函数
     */
    public GetUserTeamFavoritesRequest() {
        super();
    }
    
    /**
     * 构造函数
     * 
     * @param page 页码
     * @param size 页大小
     * @param userId 用户ID
     */
    public GetUserTeamFavoritesRequest(Integer page, Integer size, Long userId) {
        super(page, size);
        this.userId = userId;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getTeamName() {
        return teamName;
    }
    
    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }
    
    public String getTeamPrivacy() {
        return teamPrivacy;
    }
    
    public void setTeamPrivacy(String teamPrivacy) {
        this.teamPrivacy = teamPrivacy;
    }
    
    public Boolean getTeamIsActive() {
        return teamIsActive;
    }
    
    public void setTeamIsActive(Boolean teamIsActive) {
        this.teamIsActive = teamIsActive;
    }
    
    public String getStartDate() {
        return startDate;
    }
    
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    
    public String getEndDate() {
        return endDate;
    }
    
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
    
    @Override
    public String toString() {
        return "GetUserTeamFavoritesRequest{" +
                "userId=" + userId +
                ", teamName='" + teamName + '\'' +
                ", teamPrivacy='" + teamPrivacy + '\'' +
                ", teamIsActive=" + teamIsActive +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", page=" + getPage() +
                ", size=" + getSize() +
                '}';
    }
}
