package com.jdl.aic.core.service.client.dto.request.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 验证用户角色请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HasRoleRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    private String roleName;
    
    /**
     * 默认构造函数
     */
    public HasRoleRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param roleName 角色名称
     */
    public HasRoleRequest(Long userId, String roleName) {
        this.userId = userId;
        this.roleName = roleName;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getRoleName() {
        return roleName;
    }
    
    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
    
    @Override
    public String toString() {
        return "HasRoleRequest{" +
                "userId=" + userId +
                ", roleName='" + roleName + '\'' +
                '}';
    }
}
