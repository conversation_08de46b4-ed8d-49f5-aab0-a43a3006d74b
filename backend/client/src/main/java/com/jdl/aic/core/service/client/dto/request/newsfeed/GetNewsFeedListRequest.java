package com.jdl.aic.core.service.client.dto.request.newsfeed;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取资讯列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetNewsFeedListRequest {
    
    /**
     * 资讯类型过滤（0:采集, 1:官方发布）
     */
    private Integer type;
    
    /**
     * 状态过滤（0:待审核, 1:已发布, 2:已下线）
     */
    private Integer status;
    
    /**
     * AI审核状态过滤（0:未审, 1:通过, 2:拒绝, 3:人工复审）
     */
    private Integer aiReviewStatus;
    
    /**
     * RSS源ID过滤
     */
    private Long rssSourceId;
    
    /**
     * 来源名称过滤
     */
    private String sourceName;
    
    /**
     * 作者过滤
     */
    private String author;
    
    /**
     * 搜索关键词（匹配标题和摘要）
     */
    private String search;
    
    /**
     * 开始发布时间
     */
    private String publishedAtStart;
    
    /**
     * 结束发布时间
     */
    private String publishedAtEnd;
    
    /**
     * 默认构造函数
     */
    public GetNewsFeedListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param type 资讯类型过滤
     * @param status 状态过滤
     * @param search 搜索关键词
     */
    public GetNewsFeedListRequest(Integer type, Integer status, String search) {
        this.type = type;
        this.status = status;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getAiReviewStatus() {
        return aiReviewStatus;
    }
    
    public void setAiReviewStatus(Integer aiReviewStatus) {
        this.aiReviewStatus = aiReviewStatus;
    }
    
    public Long getRssSourceId() {
        return rssSourceId;
    }
    
    public void setRssSourceId(Long rssSourceId) {
        this.rssSourceId = rssSourceId;
    }
    
    public String getSourceName() {
        return sourceName;
    }
    
    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }
    
    public String getAuthor() {
        return author;
    }
    
    public void setAuthor(String author) {
        this.author = author;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    public String getPublishedAtStart() {
        return publishedAtStart;
    }
    
    public void setPublishedAtStart(String publishedAtStart) {
        this.publishedAtStart = publishedAtStart;
    }
    
    public String getPublishedAtEnd() {
        return publishedAtEnd;
    }
    
    public void setPublishedAtEnd(String publishedAtEnd) {
        this.publishedAtEnd = publishedAtEnd;
    }
    
    @Override
    public String toString() {
        return "GetNewsFeedListRequest{" +
                "type=" + type +
                ", status=" + status +
                ", aiReviewStatus=" + aiReviewStatus +
                ", rssSourceId=" + rssSourceId +
                ", sourceName='" + sourceName + '\'' +
                ", author='" + author + '\'' +
                ", search='" + search + '\'' +
                ", publishedAtStart='" + publishedAtStart + '\'' +
                ", publishedAtEnd='" + publishedAtEnd + '\'' +
                '}';
    }
}
