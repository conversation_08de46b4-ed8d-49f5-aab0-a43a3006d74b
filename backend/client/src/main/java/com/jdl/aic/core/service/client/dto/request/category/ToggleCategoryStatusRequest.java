package com.jdl.aic.core.service.client.dto.request.category;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 启用/禁用分类请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToggleCategoryStatusRequest {
    
    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long id;
    
    /**
     * 是否启用
     */
    @NotNull(message = "启用状态不能为空")
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public ToggleCategoryStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 分类ID
     * @param isActive 是否启用
     */
    public ToggleCategoryStatusRequest(Long id, Boolean isActive) {
        this.id = id;
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "ToggleCategoryStatusRequest{" +
                "id=" + id +
                ", isActive=" + isActive +
                '}';
    }
}
