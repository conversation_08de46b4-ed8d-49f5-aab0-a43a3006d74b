package com.jdl.aic.core.service.client.constants;

/**
 * API常量定义
 * 
 * <p>定义了AI社区系统中API相关的常量，包括HTTP状态码、请求头、响应格式等。
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public final class ApiConstants {
    
    // ==================== HTTP状态码常量 ====================
    
    /**
     * 成功
     */
    public static final int HTTP_STATUS_OK = 200;
    
    /**
     * 创建成功
     */
    public static final int HTTP_STATUS_CREATED = 201;
    
    /**
     * 无内容
     */
    public static final int HTTP_STATUS_NO_CONTENT = 204;
    
    /**
     * 请求错误
     */
    public static final int HTTP_STATUS_BAD_REQUEST = 400;
    
    /**
     * 未授权
     */
    public static final int HTTP_STATUS_UNAUTHORIZED = 401;
    
    /**
     * 禁止访问
     */
    public static final int HTTP_STATUS_FORBIDDEN = 403;
    
    /**
     * 资源不存在
     */
    public static final int HTTP_STATUS_NOT_FOUND = 404;
    
    /**
     * 方法不允许
     */
    public static final int HTTP_STATUS_METHOD_NOT_ALLOWED = 405;
    
    /**
     * 请求超时
     */
    public static final int HTTP_STATUS_REQUEST_TIMEOUT = 408;
    
    /**
     * 冲突
     */
    public static final int HTTP_STATUS_CONFLICT = 409;
    
    /**
     * 请求实体过大
     */
    public static final int HTTP_STATUS_PAYLOAD_TOO_LARGE = 413;
    
    /**
     * 请求过于频繁
     */
    public static final int HTTP_STATUS_TOO_MANY_REQUESTS = 429;
    
    /**
     * 服务器内部错误
     */
    public static final int HTTP_STATUS_INTERNAL_SERVER_ERROR = 500;
    
    /**
     * 服务不可用
     */
    public static final int HTTP_STATUS_SERVICE_UNAVAILABLE = 503;
    
    /**
     * 网关超时
     */
    public static final int HTTP_STATUS_GATEWAY_TIMEOUT = 504;
    
    // ==================== 请求头常量 ====================
    
    /**
     * 内容类型
     */
    public static final String HEADER_CONTENT_TYPE = "Content-Type";
    
    /**
     * 授权头
     */
    public static final String HEADER_AUTHORIZATION = "Authorization";
    
    /**
     * 用户代理
     */
    public static final String HEADER_USER_AGENT = "User-Agent";
    
    /**
     * 请求ID
     */
    public static final String HEADER_REQUEST_ID = "X-Request-ID";
    
    /**
     * 用户ID
     */
    public static final String HEADER_USER_ID = "X-User-ID";
    
    /**
     * 客户端版本
     */
    public static final String HEADER_CLIENT_VERSION = "X-Client-Version";
    
    /**
     * API版本
     */
    public static final String HEADER_API_VERSION = "X-API-Version";
    
    /**
     * 时间戳
     */
    public static final String HEADER_TIMESTAMP = "X-Timestamp";
    
    /**
     * 签名
     */
    public static final String HEADER_SIGNATURE = "X-Signature";
    
    // ==================== 内容类型常量 ====================
    
    /**
     * JSON格式
     */
    public static final String CONTENT_TYPE_JSON = "application/json";
    
    /**
     * XML格式
     */
    public static final String CONTENT_TYPE_XML = "application/xml";
    
    /**
     * 表单格式
     */
    public static final String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded";
    
    /**
     * 多部分表单
     */
    public static final String CONTENT_TYPE_MULTIPART = "multipart/form-data";
    
    /**
     * 纯文本
     */
    public static final String CONTENT_TYPE_TEXT = "text/plain";
    
    /**
     * HTML格式
     */
    public static final String CONTENT_TYPE_HTML = "text/html";
    
    /**
     * CSV格式
     */
    public static final String CONTENT_TYPE_CSV = "text/csv";
    
    /**
     * Excel格式
     */
    public static final String CONTENT_TYPE_EXCEL = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    
    /**
     * PDF格式
     */
    public static final String CONTENT_TYPE_PDF = "application/pdf";
    
    // ==================== 响应格式常量 ====================
    
    /**
     * 成功响应码
     */
    public static final String RESPONSE_CODE_SUCCESS = "SUCCESS";
    
    /**
     * 失败响应码
     */
    public static final String RESPONSE_CODE_FAILURE = "FAILURE";
    
    /**
     * 错误响应码
     */
    public static final String RESPONSE_CODE_ERROR = "ERROR";
    
    /**
     * 成功消息
     */
    public static final String RESPONSE_MESSAGE_SUCCESS = "操作成功";
    
    /**
     * 失败消息
     */
    public static final String RESPONSE_MESSAGE_FAILURE = "操作失败";
    
    /**
     * 系统错误消息
     */
    public static final String RESPONSE_MESSAGE_SYSTEM_ERROR = "系统错误";
    
    // ==================== API路径常量 ====================
    
    /**
     * API基础路径
     */
    public static final String API_BASE_PATH = "/api";
    
    /**
     * API版本路径
     */
    public static final String API_VERSION_PATH = "/v1";
    
    /**
     * 知识管理API路径
     */
    public static final String API_KNOWLEDGE_PATH = "/knowledge";
    
    /**
     * 用户管理API路径
     */
    public static final String API_USER_PATH = "/user";
    
    /**
     * 解决方案API路径
     */
    public static final String API_SOLUTION_PATH = "/solution";
    
    /**
     * 学习资源API路径
     */
    public static final String API_LEARNING_PATH = "/learning";
    
    /**
     * 社区互动API路径
     */
    public static final String API_COMMUNITY_PATH = "/community";
    
    /**
     * 文件存储API路径
     */
    public static final String API_FILE_PATH = "/file";
    
    /**
     * 搜索API路径
     */
    public static final String API_SEARCH_PATH = "/search";
    
    /**
     * 通知API路径
     */
    public static final String API_NOTIFICATION_PATH = "/notification";
    
    /**
     * 管理员API路径
     */
    public static final String API_ADMIN_PATH = "/admin";
    
    /**
     * Portal API路径
     */
    public static final String API_PORTAL_PATH = "/portal";
    
    // ==================== 分页参数常量 ====================
    
    /**
     * 页码参数名
     */
    public static final String PARAM_PAGE = "page";
    
    /**
     * 页面大小参数名
     */
    public static final String PARAM_SIZE = "size";
    
    /**
     * 排序参数名
     */
    public static final String PARAM_SORT = "sort";
    
    /**
     * 搜索参数名
     */
    public static final String PARAM_SEARCH = "search";
    
    /**
     * 过滤参数名
     */
    public static final String PARAM_FILTER = "filter";
    
    // ==================== 缓存常量 ====================
    
    /**
     * 缓存键前缀
     */
    public static final String CACHE_KEY_PREFIX = "aic:";
    
    /**
     * 用户缓存键前缀
     */
    public static final String CACHE_USER_PREFIX = "aic:user:";
    
    /**
     * 知识缓存键前缀
     */
    public static final String CACHE_KNOWLEDGE_PREFIX = "aic:knowledge:";
    
    /**
     * 搜索缓存键前缀
     */
    public static final String CACHE_SEARCH_PREFIX = "aic:search:";
    
    /**
     * 推荐缓存键前缀
     */
    public static final String CACHE_RECOMMENDATION_PREFIX = "aic:recommendation:";
    
    /**
     * 默认缓存过期时间（秒）
     */
    public static final int DEFAULT_CACHE_EXPIRE_SECONDS = 3600;
    
    /**
     * 短期缓存过期时间（秒）
     */
    public static final int SHORT_CACHE_EXPIRE_SECONDS = 300;
    
    /**
     * 长期缓存过期时间（秒）
     */
    public static final int LONG_CACHE_EXPIRE_SECONDS = 86400;
    
    // ==================== 限流常量 ====================
    
    /**
     * 默认限流次数
     */
    public static final int DEFAULT_RATE_LIMIT = 100;
    
    /**
     * 搜索限流次数
     */
    public static final int SEARCH_RATE_LIMIT = 50;
    
    /**
     * 上传限流次数
     */
    public static final int UPLOAD_RATE_LIMIT = 10;
    
    /**
     * 限流时间窗口（秒）
     */
    public static final int RATE_LIMIT_WINDOW_SECONDS = 60;
    
    /**
     * 私有构造函数，防止实例化
     */
    private ApiConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
