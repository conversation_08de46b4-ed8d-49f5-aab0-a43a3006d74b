package com.jdl.aic.core.service.client.dto.request.enrollment;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 更新报名状态请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateEnrollmentStatusRequest {
    
    /**
     * 报名ID
     */
    @NotNull(message = "报名ID不能为空")
    private Long id;
    
    /**
     * 报名状态（ENROLLED:已报名, IN_PROGRESS:学习中, COMPLETED:已完成, DROPPED:已退出）
     */
    @NotBlank(message = "报名状态不能为空")
    private String enrollmentStatus;
    
    /**
     * 默认构造函数
     */
    public UpdateEnrollmentStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 报名ID
     * @param enrollmentStatus 报名状态
     */
    public UpdateEnrollmentStatusRequest(Long id, String enrollmentStatus) {
        this.id = id;
        this.enrollmentStatus = enrollmentStatus;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getEnrollmentStatus() {
        return enrollmentStatus;
    }
    
    public void setEnrollmentStatus(String enrollmentStatus) {
        this.enrollmentStatus = enrollmentStatus;
    }
    
    @Override
    public String toString() {
        return "UpdateEnrollmentStatusRequest{" +
                "id=" + id +
                ", enrollmentStatus='" + enrollmentStatus + '\'' +
                '}';
    }
}
