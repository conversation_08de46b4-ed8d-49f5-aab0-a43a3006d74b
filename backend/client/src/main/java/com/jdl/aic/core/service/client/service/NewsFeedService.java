package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.newsfeed.NewsFeedDTO;
import com.jdl.aic.core.service.client.dto.request.newsfeed.GetNewsFeedListRequest;
import com.jdl.aic.core.service.client.dto.request.newsfeed.UpdateNewsFeedStatusRequest;
import com.jdl.aic.core.service.client.dto.request.newsfeed.BatchUpdateNewsFeedStatusRequest;
import java.util.List;

/**
 * 资讯管理服务接口
 * 
 * <p>提供资讯管理功能，包括：
 * <ul>
 *   <li>资讯的CRUD操作</li>
 *   <li>资讯状态管理（发布、下线、审核）</li>
 *   <li>资讯搜索和过滤</li>
 *   <li>RSS源资讯采集管理</li>
 *   <li>AI审核状态管理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface NewsFeedService {
    
    // ==================== 资讯管理 ====================

    /**
     * 获取资讯列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 资讯列表
     */
    Result<PageResult<NewsFeedDTO>> getNewsFeedList(
            PageRequest pageRequest,
            GetNewsFeedListRequest request);

    /**
     * 根据ID获取资讯详情
     * 
     * @param id 资讯ID
     * @return 资讯详情
     */
    Result<NewsFeedDTO> getNewsFeedById(Long id);

    /**
     * 创建资讯
     * 
     * @param newsFeed 资讯信息
     * @return 创建结果
     */
    Result<NewsFeedDTO> createNewsFeed(NewsFeedDTO newsFeed);

    /**
     * 更新资讯信息
     * 
     * @param id 资讯ID
     * @param newsFeed 资讯信息
     * @return 更新结果
     */
    Result<NewsFeedDTO> updateNewsFeed(Long id, NewsFeedDTO newsFeed);

    /**
     * 删除资讯
     * 
     * @param id 资讯ID
     * @return 删除结果
     */
    Result<Void> deleteNewsFeed(Long id);

    /**
     * 批量删除资讯
     * 
     * @param ids 资讯ID列表
     * @return 删除结果
     */
    Result<Void> batchDeleteNewsFeed(List<Long> ids);

    // ==================== 状态管理 ====================

    /**
     * 更新资讯状态
     *
     * @param request 更新状态请求参数
     * @return 操作结果
     */
    Result<Void> updateNewsFeedStatus(UpdateNewsFeedStatusRequest request);

    /**
     * 批量更新资讯状态
     *
     * @param request 批量更新状态请求参数
     * @return 操作结果
     */
    Result<Void> batchUpdateNewsFeedStatus(BatchUpdateNewsFeedStatusRequest request);

    /**
     * 发布资讯
     * 
     * @param id 资讯ID
     * @return 操作结果
     */
    Result<Void> publishNewsFeed(Long id);

    /**
     * 下线资讯
     * 
     * @param id 资讯ID
     * @return 操作结果
     */
    Result<Void> offlineNewsFeed(Long id);

    // ==================== AI审核管理 ====================

    /**
     * 更新AI审核状态
     * 
     * @param id 资讯ID
     * @param aiReviewStatus AI审核状态
     * @return 操作结果
     */
    Result<Void> updateAiReviewStatus(Long id, Integer aiReviewStatus);

    /**
     * 获取待AI审核的资讯列表
     * 
     * @param pageRequest 分页请求
     * @return 待审核资讯列表
     */
    Result<PageResult<NewsFeedDTO>> getPendingAiReviewNewsFeed(PageRequest pageRequest);

    // ==================== RSS源管理 ====================

    /**
     * 根据RSS源ID获取资讯列表
     * 
     * @param rssSourceId RSS源ID
     * @param pageRequest 分页请求
     * @return 资讯列表
     */
    Result<PageResult<NewsFeedDTO>> getNewsFeedByRssSource(Long rssSourceId, PageRequest pageRequest);

    /**
     * 同步RSS源资讯
     * 
     * @param rssSourceId RSS源ID
     * @return 同步结果
     */
    Result<Integer> syncRssSourceNewsFeed(Long rssSourceId);

    // ==================== 统计查询 ====================

    /**
     * 获取资讯统计信息
     * 
     * @return 统计信息
     */
    Result<NewsFeedStatistics> getNewsFeedStatistics();

    /**
     * 获取热门资讯列表
     * 
     * @param limit 限制数量
     * @return 热门资讯列表
     */
    Result<List<NewsFeedDTO>> getPopularNewsFeed(Integer limit);

    /**
     * 获取最新资讯列表
     * 
     * @param limit 限制数量
     * @return 最新资讯列表
     */
    Result<List<NewsFeedDTO>> getLatestNewsFeed(Integer limit);

    /**
     * 资讯统计信息内部类
     */
    class NewsFeedStatistics {
        private Long totalCount;        // 总数量
        private Long publishedCount;    // 已发布数量
        private Long pendingCount;      // 待审核数量
        private Long offlineCount;      // 已下线数量
        private Long todayCount;        // 今日新增数量
        
        // 构造函数、getter和setter方法
        public NewsFeedStatistics() {}
        
        public NewsFeedStatistics(Long totalCount, Long publishedCount, Long pendingCount, 
                                Long offlineCount, Long todayCount) {
            this.totalCount = totalCount;
            this.publishedCount = publishedCount;
            this.pendingCount = pendingCount;
            this.offlineCount = offlineCount;
            this.todayCount = todayCount;
        }
        
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }
        
        public Long getPublishedCount() { return publishedCount; }
        public void setPublishedCount(Long publishedCount) { this.publishedCount = publishedCount; }
        
        public Long getPendingCount() { return pendingCount; }
        public void setPendingCount(Long pendingCount) { this.pendingCount = pendingCount; }
        
        public Long getOfflineCount() { return offlineCount; }
        public void setOfflineCount(Long offlineCount) { this.offlineCount = offlineCount; }
        
        public Long getTodayCount() { return todayCount; }
        public void setTodayCount(Long todayCount) { this.todayCount = todayCount; }
    }
}
