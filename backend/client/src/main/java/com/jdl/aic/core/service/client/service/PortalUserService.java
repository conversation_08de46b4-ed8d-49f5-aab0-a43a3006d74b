package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.system.NotificationSettingDTO;

import java.util.List;
import java.util.Map;

/**
 * Portal用户服务接口
 * 
 * <p>提供Portal端用户专用功能，包括：
 * <ul>
 *   <li>个人中心管理</li>
 *   <li>用户偏好设置</li>
 *   <li>历史记录管理</li>
 *   <li>个人统计信息</li>
 *   <li>社交功能管理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface PortalUserService {
    
    // ==================== 个人中心管理 ====================
    
    /**
     * 获取当前用户个人信息
     * 
     * @param userId 用户ID
     * @return 用户个人信息
     */
    Result<UserDTO> getCurrentUserProfile(Long userId);
    
    /**
     * 更新个人基本信息
     * 
     * @param userId 用户ID
     * @param userProfile 用户信息
     * @return 更新结果
     */
    Result<UserDTO> updateUserProfile(Long userId, UserDTO userProfile);
    
    /**
     * 上传用户头像
     * 
     * @param userId 用户ID
     * @param avatarData 头像数据
     * @return 上传结果
     */
    Result<String> uploadUserAvatar(Long userId, Object avatarData);
    
    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 更新结果
     */
    Result<Void> updateUserPassword(Long userId, String oldPassword, String newPassword);
    
    /**
     * 获取用户个人统计
     * 
     * @param userId 用户ID
     * @return 个人统计信息
     */
    Result<Object> getUserPersonalStats(Long userId);
    
    /**
     * 获取用户成就信息
     * 
     * @param userId 用户ID
     * @return 用户成就列表
     */
    Result<List<Object>> getUserAchievements(Long userId);
    
    /**
     * 获取用户积分信息
     * 
     * @param userId 用户ID
     * @return 用户积分详情
     */
    Result<Object> getUserPoints(Long userId);
    
    // ==================== 用户偏好设置 ====================
    
    /**
     * 获取用户偏好设置
     * 
     * @param userId 用户ID
     * @return 用户偏好设置
     */
    Result<Map<String, Object>> getUserPreferences(Long userId);
    
    /**
     * 更新用户偏好设置
     * 
     * @param userId 用户ID
     * @param preferences 偏好设置
     * @return 更新结果
     */
    Result<Map<String, Object>> updateUserPreferences(Long userId, Map<String, Object> preferences);
    
    /**
     * 获取用户通知设置
     * 
     * @param userId 用户ID
     * @return 通知设置列表
     */
    Result<List<NotificationSettingDTO>> getUserNotificationSettings(Long userId);
    
    /**
     * 更新用户通知设置
     * 
     * @param userId 用户ID
     * @param settings 通知设置
     * @return 更新结果
     */
    Result<List<NotificationSettingDTO>> updateUserNotificationSettings(
            Long userId,
            List<NotificationSettingDTO> settings);
    
    /**
     * 获取用户隐私设置
     * 
     * @param userId 用户ID
     * @return 隐私设置
     */
    Result<Map<String, Object>> getUserPrivacySettings(Long userId);
    
    /**
     * 更新用户隐私设置
     * 
     * @param userId 用户ID
     * @param privacySettings 隐私设置
     * @return 更新结果
     */
    Result<Map<String, Object>> updateUserPrivacySettings(
            Long userId,
            Map<String, Object> privacySettings);
    
    /**
     * 获取用户主题设置
     * 
     * @param userId 用户ID
     * @return 主题设置
     */
    Result<Object> getUserThemeSettings(Long userId);
    
    /**
     * 更新用户主题设置
     * 
     * @param userId 用户ID
     * @param themeSettings 主题设置
     * @return 更新结果
     */
    Result<Object> updateUserThemeSettings(Long userId, Object themeSettings);
    
    // ==================== 历史记录管理 ====================
    
    /**
     * 获取用户浏览历史
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @param days 查询天数
     * @return 浏览历史列表
     */
    Result<PageResult<Object>> getUserBrowsingHistory(
            Long userId,
            PageRequest pageRequest,
            String contentType,
            Integer days);
    
    /**
     * 清除用户浏览历史
     * 
     * @param userId 用户ID
     * @param contentType 内容类型（null表示全部）
     * @param days 清除天数（null表示全部）
     * @return 清除结果
     */
    Result<Void> clearUserBrowsingHistory(Long userId, String contentType, Integer days);
    
    /**
     * 获取用户搜索历史
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param days 查询天数
     * @return 搜索历史列表
     */
    Result<PageResult<String>> getUserSearchHistory(Long userId, PageRequest pageRequest, Integer days);
    
    /**
     * 清除用户搜索历史
     * 
     * @param userId 用户ID
     * @param days 清除天数（null表示全部）
     * @return 清除结果
     */
    Result<Void> clearUserSearchHistory(Long userId, Integer days);
    
    /**
     * 获取用户下载历史
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param fileType 文件类型过滤
     * @param days 查询天数
     * @return 下载历史列表
     */
    Result<PageResult<Object>> getUserDownloadHistory(
            Long userId,
            PageRequest pageRequest,
            String fileType,
            Integer days);
    
    /**
     * 获取用户操作日志
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param actionType 操作类型过滤
     * @param days 查询天数
     * @return 操作日志列表
     */
    Result<PageResult<Object>> getUserActionLogs(
            Long userId,
            PageRequest pageRequest,
            String actionType,
            Integer days);
    
    // ==================== 收藏和关注管理 ====================
    
    /**
     * 获取用户收藏列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @param folderName 收藏夹过滤
     * @return 收藏列表
     */
    Result<PageResult<FavoriteDTO>> getUserFavorites(
            Long userId,
            PageRequest pageRequest,
            String contentType,
            String folderName);
    
    /**
     * 获取用户收藏夹列表
     * 
     * @param userId 用户ID
     * @return 收藏夹列表
     */
    Result<List<Object>> getUserFavoriteFolders(Long userId);
    
    /**
     * 创建收藏夹
     * 
     * @param userId 用户ID
     * @param folderName 收藏夹名称
     * @param description 收藏夹描述
     * @return 创建结果
     */
    Result<Object> createFavoriteFolder(Long userId, String folderName, String description);
    
    /**
     * 删除收藏夹
     * 
     * @param userId 用户ID
     * @param folderName 收藏夹名称
     * @return 删除结果
     */
    Result<Void> deleteFavoriteFolder(Long userId, String folderName);
    
    /**
     * 获取用户关注列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @return 关注用户列表
     */
    Result<PageResult<UserDTO>> getUserFollowing(Long userId, PageRequest pageRequest);
    
    /**
     * 获取用户粉丝列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @return 粉丝用户列表
     */
    Result<PageResult<UserDTO>> getUserFollowers(Long userId, PageRequest pageRequest);
    
    /**
     * 获取互相关注的用户列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @return 互相关注用户列表
     */
    Result<PageResult<UserDTO>> getMutualFollowing(Long userId, PageRequest pageRequest);
    
    // ==================== 个人内容管理 ====================
    
    /**
     * 获取用户创建的内容列表
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤
     * @param status 状态过滤
     * @return 用户内容列表
     */
    Result<PageResult<Object>> getUserCreatedContent(
            Long userId,
            PageRequest pageRequest,
            String contentType,
            Integer status);
    
    /**
     * 获取用户参与的讨论
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param days 查询天数
     * @return 参与讨论列表
     */
    Result<PageResult<Object>> getUserParticipatedDiscussions(
            Long userId,
            PageRequest pageRequest,
            Integer days);
    
    /**
     * 获取用户学习进度
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param resourceType 资源类型过滤
     * @return 学习进度列表
     */
    Result<PageResult<Object>> getUserLearningProgress(
            Long userId,
            PageRequest pageRequest,
            String resourceType);
    
    /**
     * 获取用户贡献统计
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 贡献统计信息
     */
    Result<Object> getUserContributionStats(Long userId, Integer days);
    
    /**
     * 注销用户账户
     * 
     * @param userId 用户ID
     * @param reason 注销原因
     * @param password 确认密码
     * @return 注销结果
     */
    Result<Void> deactivateUserAccount(Long userId, String reason, String password);
    
    /**
     * 导出用户数据
     * 
     * @param userId 用户ID
     * @param dataTypes 数据类型列表
     * @param format 导出格式
     * @return 导出结果
     */
    Result<Object> exportUserData(Long userId, List<String> dataTypes, String format);
}
