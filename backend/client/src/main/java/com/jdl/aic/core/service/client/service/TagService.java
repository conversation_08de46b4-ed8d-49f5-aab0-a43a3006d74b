package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.category.TagDTO;
import java.util.List;

/**
 * 标签管理服务接口
 * 
 * <p>提供标签管理功能，包括：
 * <ul>
 *   <li>标签的CRUD操作和使用统计</li>
 *   <li>标签热门排行和推荐</li>
 *   <li>标签批量操作和状态管理</li>
 *   <li>知识类型专属标签查询</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public interface TagService {
    
    // ==================== 标签管理 ====================
    
    /**
     * 获取标签列表（分页）
     * 
     * @param pageRequest 分页请求
     * @param contentCategory 内容类别过滤
     * @param tagCategory 标签分组过滤
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     * @return 标签列表
     */
    Result<PageResult<TagDTO>> getTagList(
            PageRequest pageRequest,
            String contentCategory,
            String tagCategory,
            Boolean isActive,
            String search);

    /**
     * 获取标签列表（分页，支持细分类型过滤）
     * 
     * @param pageRequest 分页请求
     * @param contentCategory 内容类别过滤
     * @param subTypeId 细分类型ID过滤（用于知识类型专属标签）
     * @param tagCategory 标签分组过滤
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     * @return 标签列表
     */
    Result<PageResult<TagDTO>> getTagList(
            PageRequest pageRequest,
            String contentCategory,
            Long subTypeId,
            String tagCategory,
            Boolean isActive,
            String search);

    /**
     * 获取热门标签
     * 
     * @param contentCategory 内容类别过滤
     * @param limit 返回数量限制
     * @return 热门标签列表
     */
    Result<List<TagDTO>> getPopularTags(String contentCategory, Integer limit);

    /**
     * 获取热门标签（支持细分类型过滤）
     * 
     * @param contentCategory 内容类别过滤
     * @param subTypeId 细分类型ID过滤
     * @param limit 返回数量限制
     * @return 热门标签列表
     */
    Result<List<TagDTO>> getPopularTags(String contentCategory, Long subTypeId, Integer limit);
    
    /**
     * 根据ID获取标签详情
     * 
     * @param id 标签ID
     * @return 标签详情
     */
    Result<TagDTO> getTagById(Long id);
    
    /**
     * 根据名称获取标签
     * 
     * @param name 标签名称
     * @param contentCategory 内容类别
     * @return 标签信息
     */
    Result<TagDTO> getTagByName(String name, String contentCategory);
    
    /**
     * 创建标签
     * 
     * @param tag 标签信息
     * @return 创建结果
     */
    Result<TagDTO> createTag(TagDTO tag);
    
    /**
     * 更新标签信息
     * 
     * @param id 标签ID
     * @param tag 标签信息
     * @return 更新结果
     */
    Result<TagDTO> updateTag(Long id, TagDTO tag);
    
    /**
     * 删除标签
     * 
     * @param id 标签ID
     * @return 删除结果
     */
    Result<Void> deleteTag(Long id);
    
    /**
     * 启用/禁用标签
     * 
     * @param id 标签ID
     * @param isActive 是否启用
     * @return 操作结果
     */
    Result<Void> toggleTagStatus(Long id, Boolean isActive);
    
    /**
     * 批量创建标签
     * 
     * @param tagNames 标签名称列表
     * @param contentCategory 内容类别
     * @return 创建结果
     */
    Result<List<TagDTO>> batchCreateTags(List<String> tagNames, String contentCategory);
    
    /**
     * 增加标签使用次数
     * 
     * @param id 标签ID
     * @return 操作结果
     */
    Result<Void> incrementTagUsage(Long id);
    
    /**
     * 减少标签使用次数
     * 
     * @param id 标签ID
     * @return 操作结果
     */
    Result<Void> decrementTagUsage(Long id);

    /**
     * 获取知识类型可用的标签列表
     * 
     * <p>返回指定知识类型可以使用的标签，包括：
     * <ul>
     *   <li>该知识类型的专属标签（sub_type_id匹配）</li>
     *   <li>通用知识标签（sub_type_id为null且content_category为knowledge）</li>
     *   <li>全局通用标签（content_category为general）</li>
     * </ul>
     * 
     * @param knowledgeTypeId 知识类型ID
     * @return 可用标签列表
     */
    Result<List<TagDTO>> getAvailableTagsForKnowledgeType(Long knowledgeTypeId);

    /**
     * 根据细分类型获取标签列表
     * 
     * @param contentCategory 内容类别
     * @param subTypeId 细分类型ID
     * @return 标签列表
     */
    Result<List<TagDTO>> getTagsBySubType(String contentCategory, Long subTypeId);
}
