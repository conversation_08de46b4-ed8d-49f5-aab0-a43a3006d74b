package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO;
import com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningCourseListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.SearchLearningCoursesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.UpdateLearningCourseStatusRequest;

import java.util.List;

/**
 * 学习课程管理服务接口
 * 
 * <p>提供学习课程管理功能，包括：
 * <ul>
 *   <li>课程的CRUD操作</li>
 *   <li>课程状态管理</li>
 *   <li>课程搜索和过滤</li>
 *   <li>课程统计和分析</li>
 *   <li>课程推荐功能</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface LearningCourseService {
    
    // ==================== 课程管理 ====================

    /**
     * 获取学习课程列表（分页）
     *
     * @param request 查询请求参数
     * @return 课程列表
     */
    Result<PageResult<LearningCourseDTO>> getLearningCourseList(GetLearningCourseListRequest request);

    /**
     * 根据ID获取学习课程详情
     * 
     * @param id 课程ID
     * @return 课程详情
     */
    Result<LearningCourseDTO> getLearningCourseById(Long id);

    /**
     * 创建学习课程
     * 
     * @param course 课程信息
     * @return 创建结果
     */
    Result<LearningCourseDTO> createLearningCourse(LearningCourseDTO course);

    /**
     * 更新学习课程信息
     * 
     * @param id 课程ID
     * @param course 课程信息
     * @return 更新结果
     */
    Result<LearningCourseDTO> updateLearningCourse(Long id, LearningCourseDTO course);

    /**
     * 删除学习课程
     * 
     * @param id 课程ID
     * @return 删除结果
     */
    Result<Void> deleteLearningCourse(Long id);

    /**
     * 更新课程状态
     *
     * @param request 更新状态请求参数
     * @return 操作结果
     */
    Result<Void> updateLearningCourseStatus(UpdateLearningCourseStatusRequest request);

    // ==================== 课程搜索和过滤 ====================

    /**
     * 获取学习课程分类统计信息
     * @return 分类统计结果
     */
    Result<List<CategoryStatisticsDTO>> getCourseCategoryStatistics();

    /**
     * 搜索学习课程
     *
     * @param request 搜索请求参数
     * @return 搜索结果
     */
    Result<PageResult<LearningCourseDTO>> searchLearningCourses(SearchLearningCoursesRequest request);

    /**
     * 根据分类获取课程列表
     * 
     * @param category 课程分类
     * @param limit 限制数量
     * @return 课程列表
     */
    Result<List<LearningCourseDTO>> getLearningCoursesByCategory(String category, Integer limit);

    /**
     * 根据难度级别获取课程列表
     * 
     * @param difficultyLevel 难度级别
     * @param limit 限制数量
     * @return 课程列表
     */
    Result<List<LearningCourseDTO>> getLearningCoursesByDifficulty(String difficultyLevel, Integer limit);

    /**
     * 根据创建者获取课程列表
     * 
     * @param creatorId 创建者ID
     * @param limit 限制数量
     * @return 课程列表
     */
    Result<List<LearningCourseDTO>> getLearningCoursesByCreator(String creatorId, Integer limit);

    // ==================== 课程统计和推荐 ====================

    /**
     * 获取热门课程列表
     * 
     * @param limit 限制数量
     * @return 热门课程列表
     */
    Result<List<LearningCourseDTO>> getPopularLearningCourses(Integer limit);

    /**
     * 获取推荐课程列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐课程列表
     */
    Result<List<LearningCourseDTO>> getRecommendedLearningCourses(String userId, Integer limit);

    /**
     * 获取最新课程列表
     * 
     * @param limit 限制数量
     * @return 最新课程列表
     */
    Result<List<LearningCourseDTO>> getLatestLearningCourses(Integer limit);

    /**
     * 获取官方课程列表
     * 
     * @param limit 限制数量
     * @return 官方课程列表
     */
    Result<List<LearningCourseDTO>> getOfficialLearningCourses(Integer limit);

    // ==================== 课程统计 ====================

    /**
     * 增加课程报名人数
     * 
     * @param courseId 课程ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> incrementEnrolledCount(Long courseId, String userId);

    /**
     * 增加课程完成人数
     * 
     * @param courseId 课程ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> incrementCompletionCount(Long courseId, String userId);

    /**
     * 更新课程资源数量
     * 
     * @param courseId 课程ID
     * @param resourceCount 资源数量
     * @return 操作结果
     */
    Result<Void> updateResourceCount(Long courseId, Integer resourceCount);

    /**
     * 计算并更新课程完成率
     * 
     * @param courseId 课程ID
     * @return 操作结果
     */
    Result<Void> updateCompletionRate(Long courseId);

    // ==================== 批量操作 ====================

    /**
     * 批量更新课程状态
     * 
     * @param courseIds 课程ID列表
     * @param status 新状态
     * @param updatedBy 更新用户
     * @return 操作结果
     */
    Result<Void> batchUpdateStatus(List<Long> courseIds, String status, String updatedBy);

    /**
     * 批量删除课程
     * 
     * @param courseIds 课程ID列表
     * @return 操作结果
     */
    Result<Void> batchDeleteLearningCourses(List<Long> courseIds);
}
