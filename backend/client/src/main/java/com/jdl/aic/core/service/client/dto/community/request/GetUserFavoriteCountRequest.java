package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 获取用户收藏总数请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetUserFavoriteCountRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    public GetUserFavoriteCountRequest() {
    }

    public GetUserFavoriteCountRequest(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "GetUserFavoriteCountRequest{" +
                "userId=" + userId +
                '}';
    }
}
