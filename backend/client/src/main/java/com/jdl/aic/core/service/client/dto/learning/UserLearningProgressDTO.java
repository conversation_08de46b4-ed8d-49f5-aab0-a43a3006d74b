package com.jdl.aic.core.service.client.dto.learning;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户学习进度DTO
 * 
 * <p>封装用户在学习资源上的进度信息，包括学习进度百分比、
 * 已花费时间、当前学习步骤等详细信息。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserLearningProgressDTO {
    
    /**
     * 进度记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 学习资源ID
     */
    @NotNull(message = "学习资源ID不能为空")
    private Long resourceId;
    
    /**
     * 资源标题
     */
    private String resourceTitle;
    
    /**
     * 资源类型
     */
    private String resourceType;
    
    /**
     * 学习进度百分比（0-100）
     */
    private Integer progress;
    
    /**
     * 已花费时间（分钟）
     */
    private Integer timeSpent;
    
    /**
     * 当前学习步骤
     */
    private String currentStep;
    
    /**
     * 进度详细数据（JSON格式）
     */
    private Map<String, Object> progressData;
    
    /**
     * 是否已完成
     */
    private Boolean isCompleted;
    
    /**
     * 开始学习时间
     */
    private LocalDateTime startedAt;
    
    /**
     * 最后访问时间
     */
    private LocalDateTime lastAccessAt;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedAt;
    
    /**
     * 默认构造函数
     */
    public UserLearningProgressDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param resourceId 学习资源ID
     */
    public UserLearningProgressDTO(Long userId, Long resourceId) {
        this.userId = userId;
        this.resourceId = resourceId;
        this.progress = 0;
        this.timeSpent = 0;
        this.isCompleted = false;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getResourceId() {
        return resourceId;
    }
    
    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }
    
    public String getResourceTitle() {
        return resourceTitle;
    }
    
    public void setResourceTitle(String resourceTitle) {
        this.resourceTitle = resourceTitle;
    }
    
    public String getResourceType() {
        return resourceType;
    }
    
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }
    
    public Integer getProgress() {
        return progress;
    }
    
    public void setProgress(Integer progress) {
        this.progress = progress;
    }
    
    public Integer getTimeSpent() {
        return timeSpent;
    }
    
    public void setTimeSpent(Integer timeSpent) {
        this.timeSpent = timeSpent;
    }
    
    public String getCurrentStep() {
        return currentStep;
    }
    
    public void setCurrentStep(String currentStep) {
        this.currentStep = currentStep;
    }
    
    public Map<String, Object> getProgressData() {
        return progressData;
    }
    
    public void setProgressData(Map<String, Object> progressData) {
        this.progressData = progressData;
    }
    
    public Boolean getIsCompleted() {
        return isCompleted;
    }
    
    public void setIsCompleted(Boolean isCompleted) {
        this.isCompleted = isCompleted;
    }
    
    public LocalDateTime getStartedAt() {
        return startedAt;
    }
    
    public void setStartedAt(LocalDateTime startedAt) {
        this.startedAt = startedAt;
    }
    
    public LocalDateTime getLastAccessAt() {
        return lastAccessAt;
    }
    
    public void setLastAccessAt(LocalDateTime lastAccessAt) {
        this.lastAccessAt = lastAccessAt;
    }
    
    public LocalDateTime getCompletedAt() {
        return completedAt;
    }
    
    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }
    
    @Override
    public String toString() {
        return "UserLearningProgressDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", resourceId=" + resourceId +
                ", resourceTitle='" + resourceTitle + '\'' +
                ", resourceType='" + resourceType + '\'' +
                ", progress=" + progress +
                ", timeSpent=" + timeSpent +
                ", currentStep='" + currentStep + '\'' +
                ", progressData=" + progressData +
                ", isCompleted=" + isCompleted +
                ", startedAt=" + startedAt +
                ", lastAccessAt=" + lastAccessAt +
                ", completedAt=" + completedAt +
                '}';
    }
}
