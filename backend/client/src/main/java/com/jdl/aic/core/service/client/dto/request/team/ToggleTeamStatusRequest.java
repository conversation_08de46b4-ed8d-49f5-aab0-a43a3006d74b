package com.jdl.aic.core.service.client.dto.request.team;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 切换团队状态请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToggleTeamStatusRequest {
    
    /**
     * 团队ID
     */
    @NotNull(message = "团队ID不能为空")
    private Long teamId;
    
    /**
     * 是否启用
     */
    @NotNull(message = "启用状态不能为空")
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public ToggleTeamStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param teamId 团队ID
     * @param isActive 是否启用
     */
    public ToggleTeamStatusRequest(Long teamId, Boolean isActive) {
        this.teamId = teamId;
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public Long getTeamId() {
        return teamId;
    }
    
    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "ToggleTeamStatusRequest{" +
                "teamId=" + teamId +
                ", isActive=" + isActive +
                '}';
    }
}
