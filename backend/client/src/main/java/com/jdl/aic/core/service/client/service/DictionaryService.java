package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.DictionaryDTO;

import java.util.List;

/**
 * 字典管理服务接口
 * 
 * <p>提供字典管理功能，包括：
 * <ul>
 *   <li>字典项的CRUD操作</li>
 *   <li>按类型分组管理字典项</li>
 *   <li>字典项排序和状态管理</li>
 *   <li>字典项搜索和批量操作</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface DictionaryService {
    
    // ==================== 字典项管理 ====================

    /**
     * 获取字典项列表（分页，支持类型过滤）
     * 
     * @param pageRequest 分页请求
     * @param type 字典类型过滤
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     * @return 字典项列表
     */
    Result<PageResult<DictionaryDTO>> getDictionaryList(
            PageRequest pageRequest,
            String type,
            Boolean isActive,
            String search);

    /**
     * 根据ID获取字典项详情
     * 
     * @param id 字典项ID
     * @return 字典项详情
     */
    Result<DictionaryDTO> getDictionaryById(Long id);

    /**
     * 根据键和类型获取字典项
     * 
     * @param key 字典键
     * @param type 字典类型
     * @return 字典项信息
     */
    Result<DictionaryDTO> getDictionaryByKeyAndType(String key, String type);

    /**
     * 根据键获取字典项列表
     * 
     * @param key 字典键
     * @param isActive 是否启用
     * @return 字典项列表
     */
    Result<List<DictionaryDTO>> getDictionariesByKey(String key, Boolean isActive);

    /**
     * 根据类型获取字典项列表
     * 
     * @param type 字典类型
     * @param isActive 是否启用
     * @return 字典项列表
     */
    Result<List<DictionaryDTO>> getDictionariesByType(String type, Boolean isActive);

    /**
     * 创建字典项
     * 
     * @param dictionary 字典项信息
     * @return 创建结果
     */
    Result<DictionaryDTO> createDictionary(DictionaryDTO dictionary);

    /**
     * 更新字典项
     * 
     * @param id 字典项ID
     * @param dictionary 字典项信息
     * @return 更新结果
     */
    Result<DictionaryDTO> updateDictionary(Long id, DictionaryDTO dictionary);

    /**
     * 删除字典项
     * 
     * @param id 字典项ID
     * @return 删除结果
     */
    Result<Void> deleteDictionary(Long id);

    // ==================== 字典项排序和状态管理 ====================

    /**
     * 更新字典项排序权重
     * 
     * @param id 字典项ID
     * @param sortOrder 排序权重
     * @return 更新结果
     */
    Result<Void> updateDictionarySortOrder(Long id, Integer sortOrder);

    /**
     * 更新字典项状态
     * 
     * @param id 字典项ID
     * @param isActive 是否启用
     * @return 更新结果
     */
    Result<Void> updateDictionaryStatus(Long id, Boolean isActive);

    // ==================== 字典类型管理 ====================

    /**
     * 获取所有字典类型
     * 
     * @return 字典类型列表
     */
    Result<List<String>> getAllDictionaryTypes();

    /**
     * 根据类型统计字典项数量
     * 
     * @param type 字典类型
     * @param isActive 是否启用
     * @return 字典项数量
     */
    Result<Integer> countDictionariesByType(String type, Boolean isActive);

    // ==================== 批量操作 ====================

    /**
     * 批量创建字典项
     * 
     * @param dictionaries 字典项列表
     * @return 创建结果
     */
    Result<List<DictionaryDTO>> batchCreateDictionaries(List<DictionaryDTO> dictionaries);

    /**
     * 批量更新字典项状态
     * 
     * @param ids 字典项ID列表
     * @param isActive 是否启用
     * @return 更新结果
     */
    Result<Void> batchUpdateDictionaryStatus(List<Long> ids, Boolean isActive);

    /**
     * 批量删除字典项
     * 
     * @param ids 字典项ID列表
     * @return 删除结果
     */
    Result<Void> batchDeleteDictionaries(List<Long> ids);

    // ==================== 搜索功能 ====================

    /**
     * 搜索字典项
     * 
     * @param keyword 搜索关键词
     * @param type 字典类型过滤
     * @param isActive 是否启用
     * @return 字典项列表
     */
    Result<List<DictionaryDTO>> searchDictionaries(String keyword, String type, Boolean isActive);
}
