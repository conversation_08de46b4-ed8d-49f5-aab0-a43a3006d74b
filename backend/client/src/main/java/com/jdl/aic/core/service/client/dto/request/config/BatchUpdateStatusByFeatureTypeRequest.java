package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 批量更新功能类型的状态请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchUpdateStatusByFeatureTypeRequest {
    
    /**
     * 功能类型
     */
    private String featureType;
    
    /**
     * 是否启用
     */
    private Boolean isEnabled;
    
    /**
     * 默认构造函数
     */
    public BatchUpdateStatusByFeatureTypeRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param featureType 功能类型
     * @param isEnabled 是否启用
     */
    public BatchUpdateStatusByFeatureTypeRequest(String featureType, Boolean isEnabled) {
        this.featureType = featureType;
        this.isEnabled = isEnabled;
    }
    
    // Getter and Setter methods
    
    public String getFeatureType() {
        return featureType;
    }
    
    public void setFeatureType(String featureType) {
        this.featureType = featureType;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    @Override
    public String toString() {
        return "BatchUpdateStatusByFeatureTypeRequest{" +
                "featureType='" + featureType + '\'' +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
