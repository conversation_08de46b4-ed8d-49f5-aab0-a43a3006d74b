package com.jdl.aic.core.service.client.dto.request.system;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取系统日志请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetSystemLogsRequest {
    
    /**
     * 日志级别过滤
     */
    private String logLevel;
    
    /**
     * 模块过滤
     */
    private String module;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 默认构造函数
     */
    public GetSystemLogsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param logLevel 日志级别过滤
     * @param module 模块过滤
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    public GetSystemLogsRequest(String logLevel, String module, String startTime, String endTime) {
        this.logLevel = logLevel;
        this.module = module;
        this.startTime = startTime;
        this.endTime = endTime;
    }
    
    // Getter and Setter methods
    
    public String getLogLevel() {
        return logLevel;
    }
    
    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }
    
    public String getModule() {
        return module;
    }
    
    public void setModule(String module) {
        this.module = module;
    }
    
    public String getStartTime() {
        return startTime;
    }
    
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }
    
    public String getEndTime() {
        return endTime;
    }
    
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
    
    @Override
    public String toString() {
        return "GetSystemLogsRequest{" +
                "logLevel='" + logLevel + '\'' +
                ", module='" + module + '\'' +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                '}';
    }
}
