package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 内容审核DTO
 * 
 * <p>封装内容审核的信息，包括审核状态、审核意见、审核人员等。
 * 支持AI自动审核和人工审核的流程管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContentReviewDTO {
    
    /**
     * 审核ID
     */
    private Long id;
    
    /**
     * 内容类型（knowledge, solution, learning_resource, comment）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 内容标题
     */
    private String contentTitle;
    
    /**
     * 内容作者ID
     */
    private Long contentAuthorId;
    
    /**
     * 内容作者名称
     */
    private String contentAuthorName;
    
    /**
     * 审核状态（0:待审核, 1:通过, 2:拒绝, 3:需人工复审）
     */
    @NotNull(message = "审核状态不能为空")
    private Integer reviewStatus;
    
    /**
     * 审核类型（auto:自动审核, manual:人工审核）
     */
    @NotBlank(message = "审核类型不能为空")
    private String reviewType;
    
    /**
     * 审核人员ID
     */
    private Long reviewerId;
    
    /**
     * 审核人员姓名
     */
    private String reviewerName;
    
    /**
     * 审核意见
     */
    private String reviewComment;
    
    /**
     * 拒绝原因
     */
    private String rejectReason;
    
    /**
     * AI审核结果
     */
    private Map<String, Object> aiReviewResult;
    
    /**
     * 风险等级（0:无风险, 1:低风险, 2:中风险, 3:高风险）
     */
    private Integer riskLevel;
    
    /**
     * 敏感词检测结果
     */
    private Map<String, Object> sensitiveWordsResult;
    
    /**
     * 审核优先级（0:低, 1:普通, 2:高, 3:紧急）
     */
    private Integer priority;
    
    /**
     * 审核时间
     */
    private LocalDateTime reviewedAt;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public ContentReviewDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public String getContentTitle() {
        return contentTitle;
    }
    
    public void setContentTitle(String contentTitle) {
        this.contentTitle = contentTitle;
    }
    
    public Long getContentAuthorId() {
        return contentAuthorId;
    }
    
    public void setContentAuthorId(Long contentAuthorId) {
        this.contentAuthorId = contentAuthorId;
    }
    
    public String getContentAuthorName() {
        return contentAuthorName;
    }
    
    public void setContentAuthorName(String contentAuthorName) {
        this.contentAuthorName = contentAuthorName;
    }
    
    public Integer getReviewStatus() {
        return reviewStatus;
    }
    
    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    
    public String getReviewType() {
        return reviewType;
    }
    
    public void setReviewType(String reviewType) {
        this.reviewType = reviewType;
    }
    
    public Long getReviewerId() {
        return reviewerId;
    }
    
    public void setReviewerId(Long reviewerId) {
        this.reviewerId = reviewerId;
    }
    
    public String getReviewerName() {
        return reviewerName;
    }
    
    public void setReviewerName(String reviewerName) {
        this.reviewerName = reviewerName;
    }
    
    public String getReviewComment() {
        return reviewComment;
    }
    
    public void setReviewComment(String reviewComment) {
        this.reviewComment = reviewComment;
    }
    
    public String getRejectReason() {
        return rejectReason;
    }
    
    public void setRejectReason(String rejectReason) {
        this.rejectReason = rejectReason;
    }
    
    public Map<String, Object> getAiReviewResult() {
        return aiReviewResult;
    }
    
    public void setAiReviewResult(Map<String, Object> aiReviewResult) {
        this.aiReviewResult = aiReviewResult;
    }
    
    public Integer getRiskLevel() {
        return riskLevel;
    }
    
    public void setRiskLevel(Integer riskLevel) {
        this.riskLevel = riskLevel;
    }
    
    public Map<String, Object> getSensitiveWordsResult() {
        return sensitiveWordsResult;
    }
    
    public void setSensitiveWordsResult(Map<String, Object> sensitiveWordsResult) {
        this.sensitiveWordsResult = sensitiveWordsResult;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public LocalDateTime getReviewedAt() {
        return reviewedAt;
    }
    
    public void setReviewedAt(LocalDateTime reviewedAt) {
        this.reviewedAt = reviewedAt;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "ContentReviewDTO{" +
                "id=" + id +
                ", contentType='" + contentType + '\'' +
                ", contentId=" + contentId +
                ", contentTitle='" + contentTitle + '\'' +
                ", contentAuthorId=" + contentAuthorId +
                ", contentAuthorName='" + contentAuthorName + '\'' +
                ", reviewStatus=" + reviewStatus +
                ", reviewType='" + reviewType + '\'' +
                ", reviewerId=" + reviewerId +
                ", reviewerName='" + reviewerName + '\'' +
                ", reviewComment='" + reviewComment + '\'' +
                ", rejectReason='" + rejectReason + '\'' +
                ", aiReviewResult=" + aiReviewResult +
                ", riskLevel=" + riskLevel +
                ", sensitiveWordsResult=" + sensitiveWordsResult +
                ", priority=" + priority +
                ", reviewedAt=" + reviewedAt +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
