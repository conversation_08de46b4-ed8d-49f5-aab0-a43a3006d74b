package com.jdl.aic.core.service.client.dto.request.solution;

import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 获取热门解决方案请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetPopularSolutionsRequest {
    
    /**
     * 分页请求参数
     */
    @NotNull(message = "分页请求参数不能为空")
    @Valid
    private PageRequest pageRequest;
    
    /**
     * 分类过滤
     */
    private String category;
    
    /**
     * 时间范围（天数），用于统计热度
     */
    private Integer days;
    
    /**
     * 默认构造函数
     */
    public GetPopularSolutionsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param pageRequest 分页请求参数
     */
    public GetPopularSolutionsRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    /**
     * 构造函数
     * 
     * @param pageRequest 分页请求参数
     * @param category 分类过滤
     * @param days 时间范围（天数）
     */
    public GetPopularSolutionsRequest(PageRequest pageRequest, String category, Integer days) {
        this.pageRequest = pageRequest;
        this.category = category;
        this.days = days;
    }
    
    // Getter and Setter methods
    
    public PageRequest getPageRequest() {
        return pageRequest;
    }
    
    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Integer getDays() {
        return days;
    }
    
    public void setDays(Integer days) {
        this.days = days;
    }
    
    @Override
    public String toString() {
        return "GetPopularSolutionsRequest{" +
                "pageRequest=" + pageRequest +
                ", category='" + category + '\'' +
                ", days=" + days +
                '}';
    }
}
