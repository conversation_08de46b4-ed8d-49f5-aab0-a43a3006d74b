package com.jdl.aic.core.service.client.dto.rss;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * RSS源DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RssSourceDTO {
    
    /**
     * RSS源ID
     */
    private Long id;
    
    /**
     * RSS源名称
     */
    @NotBlank(message = "RSS源名称不能为空")
    @Size(max = 100, message = "RSS源名称长度不能超过100个字符")
    private String name;
    
    /**
     * RSS订阅地址
     */
    @NotBlank(message = "RSS订阅地址不能为空")
    @Size(max = 512, message = "RSS订阅地址长度不能超过512个字符")
    @Pattern(regexp = "^https?://.*", message = "RSS订阅地址必须是有效的HTTP或HTTPS URL")
    private String feedUrl;
    
    /**
     * 源描述
     */
    @Size(max = 1000, message = "源描述长度不能超过1000个字符")
    private String description;
    
    /**
     * RSS源的分类
     */
    @Size(max = 50, message = "分类长度不能超过50个字符")
    private String category;
    
    /**
     * 源类型（0:官方, 1:用户订阅）
     */
    @NotNull(message = "源类型不能为空")
    private Integer type;
    
    /**
     * 所有者ID（如果是用户订阅）
     */
    private String ownerId;
    
    /**
     * 所有者姓名
     */
    @Size(max = 100, message = "所有者姓名长度不能超过100个字符")
    private String ownerName;
    
    /**
     * 上次成功抓取时间
     */
    private LocalDateTime lastFetchedAt;
    
    /**
     * 源状态（0:活跃, 1:暂停, 2:失败）
     */
    @NotNull(message = "源状态不能为空")
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public RssSourceDTO() {
    }

    /**
     * 基础构造函数
     *
     * @param name RSS源名称
     * @param feedUrl RSS订阅地址
     * @param type 源类型
     */
    public RssSourceDTO(String name, String feedUrl, Integer type) {
        this.name = name;
        this.feedUrl = feedUrl;
        this.type = type;
        this.status = 0; // 默认为活跃状态
    }

    /**
     * 完整构造函数
     *
     * @param name RSS源名称
     * @param feedUrl RSS订阅地址
     * @param description 源描述
     * @param category RSS源分类
     * @param type 源类型
     * @param status 源状态
     */
    public RssSourceDTO(String name, String feedUrl, String description, String category, Integer type, Integer status) {
        this.name = name;
        this.feedUrl = feedUrl;
        this.description = description;
        this.category = category;
        this.type = type;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getFeedUrl() {
        return feedUrl;
    }
    
    public void setFeedUrl(String feedUrl) {
        this.feedUrl = feedUrl;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public String getOwnerId() {
        return ownerId;
    }
    
    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }
    
    public String getOwnerName() {
        return ownerName;
    }
    
    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }
    
    public LocalDateTime getLastFetchedAt() {
        return lastFetchedAt;
    }
    
    public void setLastFetchedAt(LocalDateTime lastFetchedAt) {
        this.lastFetchedAt = lastFetchedAt;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    /**
     * 源类型常量
     */
    public static class Type {
        public static final int OFFICIAL = 0;  // 官方
        public static final int USER_SUBSCRIBED = 1;   // 用户订阅
    }
    
    /**
     * 源状态常量
     */
    public static class Status {
        public static final int ACTIVE = 0;  // 活跃
        public static final int PAUSED = 1;  // 暂停
        public static final int FAILED = 2;  // 失败
    }
    
    @Override
    public String toString() {
        return "RssSourceDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", feedUrl='" + feedUrl + '\'' +
                ", description='" + description + '\'' +
                ", category='" + category + '\'' +
                ", type=" + type +
                ", ownerId='" + ownerId + '\'' +
                ", ownerName='" + ownerName + '\'' +
                ", lastFetchedAt=" + lastFetchedAt +
                ", status=" + status +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy='" + createdBy + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RssSourceDTO that = (RssSourceDTO) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (name != null ? !name.equals(that.name) : that.name != null) return false;
        if (feedUrl != null ? !feedUrl.equals(that.feedUrl) : that.feedUrl != null) return false;
        if (description != null ? !description.equals(that.description) : that.description != null) return false;
        if (category != null ? !category.equals(that.category) : that.category != null) return false;
        if (type != null ? !type.equals(that.type) : that.type != null) return false;
        if (ownerId != null ? !ownerId.equals(that.ownerId) : that.ownerId != null) return false;
        if (ownerName != null ? !ownerName.equals(that.ownerName) : that.ownerName != null) return false;
        return status != null ? status.equals(that.status) : that.status == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (feedUrl != null ? feedUrl.hashCode() : 0);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        result = 31 * result + (category != null ? category.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (ownerId != null ? ownerId.hashCode() : 0);
        result = 31 * result + (ownerName != null ? ownerName.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        return result;
    }
}
