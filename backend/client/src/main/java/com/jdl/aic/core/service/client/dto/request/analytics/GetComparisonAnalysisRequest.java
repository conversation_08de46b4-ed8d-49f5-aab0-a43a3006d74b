package com.jdl.aic.core.service.client.dto.request.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 获取对比分析请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetComparisonAnalysisRequest {
    
    /**
     * 对比类型（period, category, user_group等）
     */
    @NotBlank(message = "对比类型不能为空")
    private String compareType;
    
    /**
     * 基准参数
     */
    @NotNull(message = "基准参数不能为空")
    private Map<String, Object> baselineParams;
    
    /**
     * 对比参数
     */
    @NotNull(message = "对比参数不能为空")
    private Map<String, Object> compareParams;
    
    /**
     * 默认构造函数
     */
    public GetComparisonAnalysisRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param compareType 对比类型
     * @param baselineParams 基准参数
     * @param compareParams 对比参数
     */
    public GetComparisonAnalysisRequest(String compareType, Map<String, Object> baselineParams, Map<String, Object> compareParams) {
        this.compareType = compareType;
        this.baselineParams = baselineParams;
        this.compareParams = compareParams;
    }
    
    // Getter and Setter methods
    
    public String getCompareType() {
        return compareType;
    }
    
    public void setCompareType(String compareType) {
        this.compareType = compareType;
    }
    
    public Map<String, Object> getBaselineParams() {
        return baselineParams;
    }
    
    public void setBaselineParams(Map<String, Object> baselineParams) {
        this.baselineParams = baselineParams;
    }
    
    public Map<String, Object> getCompareParams() {
        return compareParams;
    }
    
    public void setCompareParams(Map<String, Object> compareParams) {
        this.compareParams = compareParams;
    }
    
    @Override
    public String toString() {
        return "GetComparisonAnalysisRequest{" +
                "compareType='" + compareType + '\'' +
                ", baselineParams=" + baselineParams +
                ", compareParams=" + compareParams +
                '}';
    }
}
