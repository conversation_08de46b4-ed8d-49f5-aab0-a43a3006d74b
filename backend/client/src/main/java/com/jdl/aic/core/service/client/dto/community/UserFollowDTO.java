package com.jdl.aic.core.service.client.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * 用户关注DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserFollowDTO {
    
    /**
     * 关注ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 被关注人ID
     */
    @NotNull(message = "被关注人ID不能为空")
    private Long followedId;
    
    /**
     * 被关注人用户名（关联查询获取）
     */
    private String followedUsername;
    
    /**
     * 被关注人显示名称（关联查询获取）
     */
    private String followedDisplayName;
    
    /**
     * 被关注人邮箱（关联查询获取）
     */
    private String followedEmail;
    
    /**
     * 被关注人头像URL（关联查询获取）
     */
    private String followedAvatarUrl;
    
    /**
     * 被关注人部门（关联查询获取）
     */
    private String followedDepartment;
    
    /**
     * 被关注人职位（关联查询获取）
     */
    private String followedTitle;
    
    /**
     * 被关注人个人简介（关联查询获取）
     */
    private String followedBio;
    
    /**
     * 被关注人是否活跃（关联查询获取）
     */
    private Boolean followedIsActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public UserFollowDTO() {
    }
    
    /**
     * 基础构造函数
     *
     * @param userId 用户ID
     * @param followedId 被关注人ID
     */
    public UserFollowDTO(Long userId, Long followedId) {
        this.userId = userId;
        this.followedId = followedId;
    }
    
    /**
     * 完整构造函数
     *
     * @param userId 用户ID
     * @param followedId 被关注人ID
     * @param followedUsername 被关注人用户名
     * @param followedDisplayName 被关注人显示名称
     */
    public UserFollowDTO(Long userId, Long followedId, String followedUsername, String followedDisplayName) {
        this.userId = userId;
        this.followedId = followedId;
        this.followedUsername = followedUsername;
        this.followedDisplayName = followedDisplayName;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getFollowedId() {
        return followedId;
    }
    
    public void setFollowedId(Long followedId) {
        this.followedId = followedId;
    }
    
    public String getFollowedUsername() {
        return followedUsername;
    }
    
    public void setFollowedUsername(String followedUsername) {
        this.followedUsername = followedUsername;
    }
    
    public String getFollowedDisplayName() {
        return followedDisplayName;
    }
    
    public void setFollowedDisplayName(String followedDisplayName) {
        this.followedDisplayName = followedDisplayName;
    }
    
    public String getFollowedEmail() {
        return followedEmail;
    }
    
    public void setFollowedEmail(String followedEmail) {
        this.followedEmail = followedEmail;
    }
    
    public String getFollowedAvatarUrl() {
        return followedAvatarUrl;
    }
    
    public void setFollowedAvatarUrl(String followedAvatarUrl) {
        this.followedAvatarUrl = followedAvatarUrl;
    }
    
    public String getFollowedDepartment() {
        return followedDepartment;
    }
    
    public void setFollowedDepartment(String followedDepartment) {
        this.followedDepartment = followedDepartment;
    }
    
    public String getFollowedTitle() {
        return followedTitle;
    }
    
    public void setFollowedTitle(String followedTitle) {
        this.followedTitle = followedTitle;
    }
    
    public String getFollowedBio() {
        return followedBio;
    }
    
    public void setFollowedBio(String followedBio) {
        this.followedBio = followedBio;
    }
    
    public Boolean getFollowedIsActive() {
        return followedIsActive;
    }
    
    public void setFollowedIsActive(Boolean followedIsActive) {
        this.followedIsActive = followedIsActive;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "UserFollowDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", followedId=" + followedId +
                ", followedUsername='" + followedUsername + '\'' +
                ", followedDisplayName='" + followedDisplayName + '\'' +
                ", followedEmail='" + followedEmail + '\'' +
                ", followedAvatarUrl='" + followedAvatarUrl + '\'' +
                ", followedDepartment='" + followedDepartment + '\'' +
                ", followedTitle='" + followedTitle + '\'' +
                ", followedBio='" + followedBio + '\'' +
                ", followedIsActive=" + followedIsActive +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        UserFollowDTO that = (UserFollowDTO) o;
        
        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (userId != null ? !userId.equals(that.userId) : that.userId != null) return false;
        return followedId != null ? followedId.equals(that.followedId) : that.followedId == null;
    }
    
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        result = 31 * result + (followedId != null ? followedId.hashCode() : 0);
        return result;
    }
}
