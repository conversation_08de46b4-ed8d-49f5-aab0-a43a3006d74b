package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.SystemConfigDTO;
import com.jdl.aic.core.service.client.dto.system.ContentReviewDTO;
import com.jdl.aic.core.service.client.dto.system.NotificationTemplateDTO;
import com.jdl.aic.core.service.client.dto.request.system.GetSystemConfigsRequest;
import com.jdl.aic.core.service.client.dto.request.system.GetPendingReviewsRequest;
import com.jdl.aic.core.service.client.dto.request.system.GetReviewHistoryRequest;
import com.jdl.aic.core.service.client.dto.request.system.ReviewContentRequest;
import com.jdl.aic.core.service.client.dto.request.system.BatchReviewContentRequest;
import com.jdl.aic.core.service.client.dto.request.system.GetNotificationTemplatesRequest;
import com.jdl.aic.core.service.client.dto.request.system.GetSystemLogsRequest;

import java.util.List;
import java.util.Map;

/**
 * 管理员系统服务接口
 * 
 * <p>提供管理员专用的系统管理功能，包括：
 * <ul>
 *   <li>系统配置管理</li>
 *   <li>内容审核管理</li>
 *   <li>通知模板管理</li>
 *   <li>系统监控和维护</li>
 *   <li>用户权限管理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface AdminSystemService {
    
    // ==================== 系统配置管理 ====================
    
    /**
     * 获取系统配置列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 系统配置列表
     */
    Result<PageResult<SystemConfigDTO>> getSystemConfigs(
            PageRequest pageRequest,
            GetSystemConfigsRequest request);
    
    /**
     * 根据配置键获取配置值
     * 
     * @param configKey 配置键
     * @return 配置值
     */
    Result<String> getConfigValue(String configKey);
    
    /**
     * 批量获取配置值
     * 
     * @param configKeys 配置键列表
     * @return 配置键值对
     */
    Result<Map<String, String>> getBatchConfigValues(List<String> configKeys);
    
    /**
     * 根据分组获取配置
     * 
     * @param configGroup 配置分组
     * @return 分组配置列表
     */
    Result<List<SystemConfigDTO>> getConfigsByGroup(String configGroup);
    
    /**
     * 创建系统配置
     * 
     * @param config 系统配置
     * @return 创建结果
     */
    Result<SystemConfigDTO> createSystemConfig(SystemConfigDTO config);
    
    /**
     * 更新系统配置
     * 
     * @param id 配置ID
     * @param config 系统配置
     * @return 更新结果
     */
    Result<SystemConfigDTO> updateSystemConfig(Long id, SystemConfigDTO config);
    
    /**
     * 批量更新系统配置
     * 
     * @param configs 配置列表
     * @return 批量更新结果
     */
    Result<List<SystemConfigDTO>> batchUpdateSystemConfigs(List<SystemConfigDTO> configs);
    
    /**
     * 删除系统配置
     * 
     * @param id 配置ID
     * @return 删除结果
     */
    Result<Void> deleteSystemConfig(Long id);
    
    /**
     * 重置配置为默认值
     * 
     * @param configKey 配置键
     * @return 操作结果
     */
    Result<Void> resetConfigToDefault(String configKey);
    
    // ==================== 内容审核管理 ====================
    
    /**
     * 获取待审核内容列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 待审核内容列表
     */
    Result<PageResult<ContentReviewDTO>> getPendingReviews(
            PageRequest pageRequest,
            GetPendingReviewsRequest request);
    
    /**
     * 获取审核历史（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 审核历史列表
     */
    Result<PageResult<ContentReviewDTO>> getReviewHistory(
            PageRequest pageRequest,
            GetReviewHistoryRequest request);
    
    /**
     * 审核内容
     *
     * @param request 审核请求参数
     * @return 审核结果
     */
    Result<ContentReviewDTO> reviewContent(ReviewContentRequest request);
    
    /**
     * 批量审核内容
     *
     * @param request 批量审核请求参数
     * @return 批量审核结果
     */
    Result<List<ContentReviewDTO>> batchReviewContent(BatchReviewContentRequest request);
    
    /**
     * 分配审核任务
     * 
     * @param reviewIds 审核ID列表
     * @param reviewerId 审核人ID
     * @return 分配结果
     */
    Result<Void> assignReviewTasks(List<Long> reviewIds, Long reviewerId);
    
    /**
     * 获取审核统计
     * 
     * @param reviewerId 审核人ID过滤
     * @param days 统计天数
     * @return 审核统计信息
     */
    Result<Object> getReviewStatistics(Long reviewerId, Integer days);
    
    // ==================== 通知模板管理 ====================
    
    /**
     * 获取通知模板列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 通知模板列表
     */
    Result<PageResult<NotificationTemplateDTO>> getNotificationTemplates(
            PageRequest pageRequest,
            GetNotificationTemplatesRequest request);
    
    /**
     * 创建通知模板
     * 
     * @param template 通知模板
     * @return 创建结果
     */
    Result<NotificationTemplateDTO> createNotificationTemplate(NotificationTemplateDTO template);
    
    /**
     * 更新通知模板
     * 
     * @param id 模板ID
     * @param template 通知模板
     * @return 更新结果
     */
    Result<NotificationTemplateDTO> updateNotificationTemplate(Long id, NotificationTemplateDTO template);
    
    /**
     * 删除通知模板
     * 
     * @param id 模板ID
     * @return 删除结果
     */
    Result<Void> deleteNotificationTemplate(Long id);
    
    /**
     * 预览通知模板
     * 
     * @param templateId 模板ID
     * @param variables 模板变量
     * @return 预览结果
     */
    Result<Object> previewNotificationTemplate(Long templateId, Map<String, Object> variables);
    
    /**
     * 测试发送通知模板
     * 
     * @param templateId 模板ID
     * @param testRecipient 测试接收者
     * @param variables 模板变量
     * @return 测试结果
     */
    Result<Object> testNotificationTemplate(
            Long templateId,
            String testRecipient,
            Map<String, Object> variables);
    
    // ==================== 系统监控和维护 ====================
    
    /**
     * 获取系统状态
     * 
     * @return 系统状态信息
     */
    Result<Object> getSystemStatus();
    
    /**
     * 获取系统性能指标
     * 
     * @param hours 统计小时数
     * @return 性能指标
     */
    Result<Object> getSystemPerformanceMetrics(Integer hours);
    
    /**
     * 获取系统日志
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 系统日志列表
     */
    Result<PageResult<Object>> getSystemLogs(
            PageRequest pageRequest,
            GetSystemLogsRequest request);
    
    /**
     * 清理系统缓存
     * 
     * @param cacheType 缓存类型（null表示全部）
     * @return 清理结果
     */
    Result<Object> clearSystemCache(String cacheType);
    
    /**
     * 执行系统维护任务
     * 
     * @param taskType 任务类型
     * @param parameters 任务参数
     * @return 执行结果
     */
    Result<Object> executeMaintenanceTask(String taskType, Map<String, Object> parameters);
    
    /**
     * 获取数据库状态
     * 
     * @return 数据库状态信息
     */
    Result<Object> getDatabaseStatus();
    
    /**
     * 备份系统数据
     * 
     * @param backupType 备份类型（full, incremental）
     * @param includeFiles 是否包含文件
     * @return 备份结果
     */
    Result<Object> backupSystemData(String backupType, Boolean includeFiles);
}
