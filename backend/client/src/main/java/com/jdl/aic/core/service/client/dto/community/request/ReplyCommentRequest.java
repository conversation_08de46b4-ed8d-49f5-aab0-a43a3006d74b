package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 回复评论请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class ReplyCommentRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 父评论ID
     */
    @NotNull(message = "父评论ID不能为空")
    private Long parentCommentId;

    /**
     * 回复内容
     */
    @NotBlank(message = "回复内容不能为空")
    @Size(max = 1000, message = "回复内容不能超过1000字符")
    private String commentText;

    public ReplyCommentRequest() {
    }

    public ReplyCommentRequest(Long userId, Long parentCommentId, String commentText) {
        this.userId = userId;
        this.parentCommentId = parentCommentId;
        this.commentText = commentText;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getParentCommentId() {
        return parentCommentId;
    }

    public void setParentCommentId(Long parentCommentId) {
        this.parentCommentId = parentCommentId;
    }

    public String getCommentText() {
        return commentText;
    }

    public void setCommentText(String commentText) {
        this.commentText = commentText;
    }

    @Override
    public String toString() {
        return "ReplyCommentRequest{" +
                "userId=" + userId +
                ", parentCommentId=" + parentCommentId +
                ", commentText='" + commentText + '\'' +
                '}';
    }
}
