package com.jdl.aic.core.service.client.dto.category;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 内容分类关联DTO
 * 
 * <p>用于管理统一内容分类关联表的数据传输，支持所有内容类型与分类的关联关系。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContentCategoryRelationDTO {
    
    /**
     * 关联ID
     */
    private Long id;
    
    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private ContentType contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 默认构造函数
     */
    public ContentCategoryRelationDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param categoryId 分类ID
     */
    public ContentCategoryRelationDTO(ContentType contentType, Long contentId, Long categoryId) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.categoryId = categoryId;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public ContentType getContentType() {
        return contentType;
    }
    
    public void setContentType(ContentType contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public Long getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "ContentCategoryRelationDTO{" +
                "id=" + id +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                ", categoryId=" + categoryId +
                ", createdAt=" + createdAt +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        ContentCategoryRelationDTO that = (ContentCategoryRelationDTO) o;
        
        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (contentType != that.contentType) return false;
        if (contentId != null ? !contentId.equals(that.contentId) : that.contentId != null) return false;
        return categoryId != null ? categoryId.equals(that.categoryId) : that.categoryId == null;
    }
    
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (contentType != null ? contentType.hashCode() : 0);
        result = 31 * result + (contentId != null ? contentId.hashCode() : 0);
        result = 31 * result + (categoryId != null ? categoryId.hashCode() : 0);
        return result;
    }
}
