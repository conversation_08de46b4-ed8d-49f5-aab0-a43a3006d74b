package com.jdl.aic.core.service.client.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 点赞DTO
 * 
 * <p>封装用户点赞内容的信息，支持对知识、资讯、评论、解决方案等内容的点赞。
 * 支持点赞状态管理和统计。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LikeDTO {
    
    /**
     * 点赞ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 内容类型（0:知识, 1:资讯, 2:评论, 3:解决方案）
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 当content_type为知识时，关联知识类型ID
     */
    private Long relatedKnowledgeTypeId;
    
    /**
     * 内容标题（冗余字段，用于展示）
     */
    private String contentTitle;
    
    /**
     * 内容描述（冗余字段，用于展示）
     */
    private String contentDescription;
    
    /**
     * 用户名（冗余字段，用于展示）
     */
    private String userName;
    
    /**
     * 用户头像（冗余字段，用于展示）
     */
    private String userAvatar;
    
    /**
     * 点赞时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 是否已点赞（用于前端状态显示）
     */
    private Boolean isLiked;
    
    /**
     * 默认构造函数
     */
    public LikeDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     */
    public LikeDTO(Long userId, Integer contentType, Long contentId) {
        this.userId = userId;
        this.contentType = contentType;
        this.contentId = contentId;
        this.isLiked = true;
    }
    
    /**
     * 构造函数（包含知识类型ID）
     * 
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param relatedKnowledgeTypeId 关联知识类型ID
     */
    public LikeDTO(Long userId, Integer contentType, Long contentId, Long relatedKnowledgeTypeId) {
        this(userId, contentType, contentId);
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Integer getContentType() {
        return contentType;
    }
    
    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public Long getRelatedKnowledgeTypeId() {
        return relatedKnowledgeTypeId;
    }
    
    public void setRelatedKnowledgeTypeId(Long relatedKnowledgeTypeId) {
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
    }
    
    public String getContentTitle() {
        return contentTitle;
    }
    
    public void setContentTitle(String contentTitle) {
        this.contentTitle = contentTitle;
    }
    
    public String getContentDescription() {
        return contentDescription;
    }
    
    public void setContentDescription(String contentDescription) {
        this.contentDescription = contentDescription;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getUserAvatar() {
        return userAvatar;
    }
    
    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public Boolean getIsLiked() {
        return isLiked;
    }
    
    public void setIsLiked(Boolean isLiked) {
        this.isLiked = isLiked;
    }
    
    @Override
    public String toString() {
        return "LikeDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                ", relatedKnowledgeTypeId=" + relatedKnowledgeTypeId +
                ", contentTitle='" + contentTitle + '\'' +
                ", contentDescription='" + contentDescription + '\'' +
                ", userName='" + userName + '\'' +
                ", userAvatar='" + userAvatar + '\'' +
                ", createdAt=" + createdAt +
                ", isLiked=" + isLiked +
                '}';
    }
}
