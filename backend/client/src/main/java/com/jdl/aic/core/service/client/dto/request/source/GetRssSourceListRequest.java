package com.jdl.aic.core.service.client.dto.request.source;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取RSS源列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetRssSourceListRequest {
    
    /**
     * 分类过滤
     */
    private String category;
    
    /**
     * 状态过滤（0:禁用, 1:启用, 2:异常）
     */
    private Integer status;
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 默认构造函数
     */
    public GetRssSourceListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param category 分类过滤
     * @param status 状态过滤
     * @param search 搜索关键词
     */
    public GetRssSourceListRequest(String category, Integer status, String search) {
        this.category = category;
        this.status = status;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    @Override
    public String toString() {
        return "GetRssSourceListRequest{" +
                "category='" + category + '\'' +
                ", status=" + status +
                ", search='" + search + '\'' +
                '}';
    }
}
