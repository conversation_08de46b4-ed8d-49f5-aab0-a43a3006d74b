package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.ShareOptionConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;

import java.util.List;

/**
 * 分享选项配置管理服务接口
 * 
 * <p>提供分享选项配置管理功能，包括：
 * <ul>
 *   <li>分享选项配置的CRUD操作</li>
 *   <li>按内容类型和分享类型管理配置</li>
 *   <li>配置状态和排序管理</li>
 *   <li>批量操作支持</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface ShareOptionConfigService {
    
    // ==================== 分享选项配置管理 ====================

    /**
     * 获取分享选项配置列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询条件请求
     * @return 分享选项配置列表
     */
    Result<PageResult<ShareOptionConfigDTO>> getConfigList(
            PageRequest pageRequest,
            GetShareOptionConfigListRequest request);

    /**
     * 根据ID获取分享选项配置详情
     *
     * @param id 配置ID
     * @return 分享选项配置详情
     */
    Result<ShareOptionConfigDTO> getConfigById(Long id);

    /**
     * 根据内容类型和分享类型获取配置
     *
     * @param request 查询请求
     * @return 分享选项配置详情
     */
    Result<ShareOptionConfigDTO> getConfigByContentAndShare(GetShareOptionConfigByContentAndShareRequest request);

    /**
     * 创建分享选项配置
     * 
     * @param config 分享选项配置信息
     * @return 创建结果
     */
    Result<ShareOptionConfigDTO> createConfig(ShareOptionConfigDTO config);

    /**
     * 更新分享选项配置信息
     * 
     * @param id 配置ID
     * @param config 分享选项配置信息
     * @return 更新结果
     */
    Result<ShareOptionConfigDTO> updateConfig(Long id, ShareOptionConfigDTO config);

    /**
     * 删除分享选项配置
     * 
     * @param id 配置ID
     * @return 删除结果
     */
    Result<Void> deleteConfig(Long id);

    /**
     * 启用/禁用分享选项配置
     *
     * @param request 启用/禁用请求
     * @return 操作结果
     */
    Result<Void> toggleConfigStatus(ToggleShareOptionConfigStatusRequest request);

    /**
     * 更新分享选项配置排序
     *
     * @param request 更新排序请求
     * @return 操作结果
     */
    Result<Void> updateConfigSortOrder(UpdateShareOptionConfigSortOrderRequest request);

    /**
     * 根据内容类型获取所有分享选项配置
     *
     * @param request 查询请求
     * @return 分享选项配置列表
     */
    Result<List<ShareOptionConfigDTO>> getConfigsByContentType(GetConfigsByContentTypeRequest request);

    /**
     * 根据分享类型获取所有配置
     *
     * @param request 查询请求
     * @return 配置列表
     */
    Result<List<ShareOptionConfigDTO>> getConfigsByShareType(GetShareOptionConfigsByShareTypeRequest request);

    /**
     * 获取所有启用的分享选项配置
     * 
     * @return 启用的分享选项配置列表
     */
    Result<List<ShareOptionConfigDTO>> getAllEnabledConfigs();

    /**
     * 批量更新内容类型的分享选项状态
     *
     * @param request 批量更新请求
     * @return 操作结果
     */
    Result<Void> batchUpdateStatusByContentType(BatchUpdateStatusByContentTypeRequest request);

    /**
     * 批量更新分享类型的状态
     *
     * @param request 批量更新请求
     * @return 操作结果
     */
    Result<Void> batchUpdateStatusByShareType(BatchUpdateStatusByShareTypeRequest request);

    /**
     * 批量更新分享选项配置排序
     *
     * @param request 批量更新排序请求
     * @return 操作结果
     */
    Result<Void> batchUpdateSortOrder(BatchUpdateSortOrderRequest request);

    /**
     * 检查内容类型和分享类型组合是否已存在
     *
     * @param request 检查存在请求
     * @return 是否存在
     */
    Result<Boolean> checkConfigExists(CheckShareOptionConfigExistsRequest request);

    /**
     * 搜索分享选项配置
     *
     * @param request 搜索请求
     * @return 搜索结果列表
     */
    Result<List<ShareOptionConfigDTO>> searchConfigs(SearchConfigsRequest request);

    /**
     * 为内容类型初始化默认分享选项配置
     * 
     * @param contentType 内容类型
     * @return 操作结果
     */
    Result<List<ShareOptionConfigDTO>> initDefaultConfigs(String contentType);

    /**
     * 获取支持的分享类型列表
     * 
     * @return 分享类型列表
     */
    Result<List<String>> getSupportedShareTypes();

    /**
     * 复制配置到新的内容类型
     *
     * @param request 复制配置请求
     * @return 操作结果
     */
    Result<List<ShareOptionConfigDTO>> copyConfigsToContentType(CopyConfigsToContentTypeRequest request);
}
