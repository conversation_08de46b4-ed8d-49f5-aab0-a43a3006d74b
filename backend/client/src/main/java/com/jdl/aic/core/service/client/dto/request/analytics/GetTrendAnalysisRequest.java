package com.jdl.aic.core.service.client.dto.request.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 获取趋势分析请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetTrendAnalysisRequest {
    
    /**
     * 分析域（knowledge, user, content等）
     */
    @NotBlank(message = "分析域不能为空")
    private String domain;
    
    /**
     * 分析周期（day, week, month, quarter）
     */
    @NotBlank(message = "分析周期不能为空")
    private String period;
    
    /**
     * 周期数量
     */
    private Integer periods;
    
    /**
     * 过滤条件
     */
    private Map<String, Object> filters;
    
    /**
     * 默认构造函数
     */
    public GetTrendAnalysisRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param domain 分析域
     * @param period 分析周期
     * @param periods 周期数量
     * @param filters 过滤条件
     */
    public GetTrendAnalysisRequest(String domain, String period, Integer periods, Map<String, Object> filters) {
        this.domain = domain;
        this.period = period;
        this.periods = periods;
        this.filters = filters;
    }
    
    // Getter and Setter methods
    
    public String getDomain() {
        return domain;
    }
    
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    public String getPeriod() {
        return period;
    }
    
    public void setPeriod(String period) {
        this.period = period;
    }
    
    public Integer getPeriods() {
        return periods;
    }
    
    public void setPeriods(Integer periods) {
        this.periods = periods;
    }
    
    public Map<String, Object> getFilters() {
        return filters;
    }
    
    public void setFilters(Map<String, Object> filters) {
        this.filters = filters;
    }
    
    @Override
    public String toString() {
        return "GetTrendAnalysisRequest{" +
                "domain='" + domain + '\'' +
                ", period='" + period + '\'' +
                ", periods=" + periods +
                ", filters=" + filters +
                '}';
    }
}
