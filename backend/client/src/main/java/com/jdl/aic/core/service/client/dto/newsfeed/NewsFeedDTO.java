package com.jdl.aic.core.service.client.dto.newsfeed;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资讯DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NewsFeedDTO {
    
    /**
     * 资讯ID
     */
    private Long id;
    
    /**
     * 资讯标题
     */
    @NotBlank(message = "资讯标题不能为空")
    @Size(max = 255, message = "资讯标题长度不能超过255个字符")
    private String title;
    
    /**
     * 资讯作者
     */
    @Size(max = 100, message = "作者名称长度不能超过100个字符")
    private String author;
    
    /**
     * 资讯原文链接
     */
    @NotBlank(message = "资讯原文链接不能为空")
    @Size(max = 512, message = "原文链接长度不能超过512个字符")
    private String sourceUrl;
    
    /**
     * 资讯发布时间（原文时间）
     */
    @NotNull(message = "资讯发布时间不能为空")
    private LocalDateTime publishedAt;
    
    /**
     * 资讯摘要（AI生成或人工编辑）
     */
    private String contentSummary;
    
    /**
     * 资讯内容 HTML 格式（全文抓取）
     */
    private String contentHtml;
    
    /**
     * 资讯封面图
     */
    @Size(max = 255, message = "封面图URL长度不能超过255个字符")
    private String coverImageUrl;
    
    /**
     * 外键关联 rss_source.id，标识来源 RSS
     */
    private Long rssSourceId;
    
    /**
     * RSS来源名称（查询时关联获取）
     */
    private String rssSourceName;
    
    /**
     * 来源名称（如"新华网"，当 rss_source_id 为空时）
     */
    @Size(max = 100, message = "来源名称长度不能超过100个字符")
    private String sourceName;
    
    /**
     * 资讯类型（0:采集, 1:官方发布）
     */
    @NotNull(message = "资讯类型不能为空")
    private Integer type;
    
    /**
     * 资讯类型描述
     */
    private String typeDesc;
    
    /**
     * 状态（0:待审核, 1:已发布, 2:已下线）
     */
    @NotNull(message = "资讯状态不能为空")
    private Integer status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * AI 审核状态（0:未审, 1:通过, 2:拒绝, 3:人工复审）
     */
    private Integer aiReviewStatus;
    
    /**
     * AI审核状态描述
     */
    private String aiReviewStatusDesc;
    
    /**
     * AI 推荐的标签列表（JSON格式）
     */
    private String aiTagsJson;
    
    /**
     * AI 推荐的标签列表（解析后的列表）
     */
    private List<String> aiTags;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public NewsFeedDTO() {
    }

    /**
     * 基础构造函数
     *
     * @param title 资讯标题
     * @param sourceUrl 原文链接
     * @param publishedAt 发布时间
     */
    public NewsFeedDTO(String title, String sourceUrl, LocalDateTime publishedAt) {
        this.title = title;
        this.sourceUrl = sourceUrl;
        this.publishedAt = publishedAt;
    }

    /**
     * 完整构造函数
     *
     * @param title 资讯标题
     * @param author 作者
     * @param sourceUrl 原文链接
     * @param publishedAt 发布时间
     * @param type 资讯类型
     * @param status 状态
     */
    public NewsFeedDTO(String title, String author, String sourceUrl, 
                      LocalDateTime publishedAt, Integer type, Integer status) {
        this.title = title;
        this.author = author;
        this.sourceUrl = sourceUrl;
        this.publishedAt = publishedAt;
        this.type = type;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getAuthor() {
        return author;
    }
    
    public void setAuthor(String author) {
        this.author = author;
    }
    
    public String getSourceUrl() {
        return sourceUrl;
    }
    
    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }
    
    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }
    
    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }
    
    public String getContentSummary() {
        return contentSummary;
    }
    
    public void setContentSummary(String contentSummary) {
        this.contentSummary = contentSummary;
    }
    
    public String getContentHtml() {
        return contentHtml;
    }
    
    public void setContentHtml(String contentHtml) {
        this.contentHtml = contentHtml;
    }
    
    public String getCoverImageUrl() {
        return coverImageUrl;
    }
    
    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }
    
    public Long getRssSourceId() {
        return rssSourceId;
    }
    
    public void setRssSourceId(Long rssSourceId) {
        this.rssSourceId = rssSourceId;
    }
    
    public String getRssSourceName() {
        return rssSourceName;
    }
    
    public void setRssSourceName(String rssSourceName) {
        this.rssSourceName = rssSourceName;
    }
    
    public String getSourceName() {
        return sourceName;
    }
    
    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }
    
    public Integer getType() {
        return type;
    }
    
    public void setType(Integer type) {
        this.type = type;
    }
    
    public String getTypeDesc() {
        return typeDesc;
    }
    
    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getStatusDesc() {
        return statusDesc;
    }
    
    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
    
    public Integer getAiReviewStatus() {
        return aiReviewStatus;
    }
    
    public void setAiReviewStatus(Integer aiReviewStatus) {
        this.aiReviewStatus = aiReviewStatus;
    }
    
    public String getAiReviewStatusDesc() {
        return aiReviewStatusDesc;
    }
    
    public void setAiReviewStatusDesc(String aiReviewStatusDesc) {
        this.aiReviewStatusDesc = aiReviewStatusDesc;
    }
    
    public String getAiTagsJson() {
        return aiTagsJson;
    }
    
    public void setAiTagsJson(String aiTagsJson) {
        this.aiTagsJson = aiTagsJson;
    }
    
    public List<String> getAiTags() {
        return aiTags;
    }
    
    public void setAiTags(List<String> aiTags) {
        this.aiTags = aiTags;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Override
    public String toString() {
        return "NewsFeedDTO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", author='" + author + '\'' +
                ", sourceUrl='" + sourceUrl + '\'' +
                ", publishedAt=" + publishedAt +
                ", contentSummary='" + contentSummary + '\'' +
                ", coverImageUrl='" + coverImageUrl + '\'' +
                ", rssSourceId=" + rssSourceId +
                ", sourceName='" + sourceName + '\'' +
                ", type=" + type +
                ", status=" + status +
                ", aiReviewStatus=" + aiReviewStatus +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        NewsFeedDTO that = (NewsFeedDTO) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (title != null ? !title.equals(that.title) : that.title != null) return false;
        if (author != null ? !author.equals(that.author) : that.author != null) return false;
        if (sourceUrl != null ? !sourceUrl.equals(that.sourceUrl) : that.sourceUrl != null) return false;
        if (publishedAt != null ? !publishedAt.equals(that.publishedAt) : that.publishedAt != null) return false;
        if (type != null ? !type.equals(that.type) : that.type != null) return false;
        return status != null ? status.equals(that.status) : that.status == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (title != null ? title.hashCode() : 0);
        result = 31 * result + (author != null ? author.hashCode() : 0);
        result = 31 * result + (sourceUrl != null ? sourceUrl.hashCode() : 0);
        result = 31 * result + (publishedAt != null ? publishedAt.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        return result;
    }
}
