package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 搜索过滤器DTO
 * 
 * <p>封装搜索过滤条件，支持多维度的内容筛选。
 * 包括内容类型、分类、标签、时间范围、作者等过滤条件。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchFilterDTO {
    
    /**
     * 内容类型过滤（knowledge, solution, learning_resource, user）
     */
    private List<String> contentTypes;
    
    /**
     * 分类过滤
     */
    private List<String> categories;
    
    /**
     * 标签过滤
     */
    private List<String> tags;
    
    /**
     * 作者ID过滤
     */
    private List<Long> authorIds;
    
    /**
     * 状态过滤（发布状态等）
     */
    private List<Integer> statuses;
    
    /**
     * 创建时间范围 - 开始时间
     */
    private LocalDateTime createdAfter;
    
    /**
     * 创建时间范围 - 结束时间
     */
    private LocalDateTime createdBefore;
    
    /**
     * 更新时间范围 - 开始时间
     */
    private LocalDateTime updatedAfter;
    
    /**
     * 更新时间范围 - 结束时间
     */
    private LocalDateTime updatedBefore;
    
    /**
     * 最小评分
     */
    private Double minScore;
    
    /**
     * 最大评分
     */
    private Double maxScore;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向（asc, desc）
     */
    private String sortOrder;
    
    /**
     * 是否包含已删除内容
     */
    private Boolean includeDeleted;
    
    /**
     * 访问权限过滤（0:私有, 1:团队可见, 2:公开）
     */
    private List<Integer> accessLevels;
    
    /**
     * 默认构造函数
     */
    public SearchFilterDTO() {
    }
    
    // Getter and Setter methods
    
    public List<String> getContentTypes() {
        return contentTypes;
    }
    
    public void setContentTypes(List<String> contentTypes) {
        this.contentTypes = contentTypes;
    }
    
    public List<String> getCategories() {
        return categories;
    }
    
    public void setCategories(List<String> categories) {
        this.categories = categories;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public List<Long> getAuthorIds() {
        return authorIds;
    }
    
    public void setAuthorIds(List<Long> authorIds) {
        this.authorIds = authorIds;
    }
    
    public List<Integer> getStatuses() {
        return statuses;
    }
    
    public void setStatuses(List<Integer> statuses) {
        this.statuses = statuses;
    }
    
    public LocalDateTime getCreatedAfter() {
        return createdAfter;
    }
    
    public void setCreatedAfter(LocalDateTime createdAfter) {
        this.createdAfter = createdAfter;
    }
    
    public LocalDateTime getCreatedBefore() {
        return createdBefore;
    }
    
    public void setCreatedBefore(LocalDateTime createdBefore) {
        this.createdBefore = createdBefore;
    }
    
    public LocalDateTime getUpdatedAfter() {
        return updatedAfter;
    }
    
    public void setUpdatedAfter(LocalDateTime updatedAfter) {
        this.updatedAfter = updatedAfter;
    }
    
    public LocalDateTime getUpdatedBefore() {
        return updatedBefore;
    }
    
    public void setUpdatedBefore(LocalDateTime updatedBefore) {
        this.updatedBefore = updatedBefore;
    }
    
    public Double getMinScore() {
        return minScore;
    }
    
    public void setMinScore(Double minScore) {
        this.minScore = minScore;
    }
    
    public Double getMaxScore() {
        return maxScore;
    }
    
    public void setMaxScore(Double maxScore) {
        this.maxScore = maxScore;
    }
    
    public String getSortBy() {
        return sortBy;
    }
    
    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }
    
    public String getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Boolean getIncludeDeleted() {
        return includeDeleted;
    }
    
    public void setIncludeDeleted(Boolean includeDeleted) {
        this.includeDeleted = includeDeleted;
    }
    
    public List<Integer> getAccessLevels() {
        return accessLevels;
    }
    
    public void setAccessLevels(List<Integer> accessLevels) {
        this.accessLevels = accessLevels;
    }
    
    @Override
    public String toString() {
        return "SearchFilterDTO{" +
                "contentTypes=" + contentTypes +
                ", categories=" + categories +
                ", tags=" + tags +
                ", authorIds=" + authorIds +
                ", statuses=" + statuses +
                ", createdAfter=" + createdAfter +
                ", createdBefore=" + createdBefore +
                ", updatedAfter=" + updatedAfter +
                ", updatedBefore=" + updatedBefore +
                ", minScore=" + minScore +
                ", maxScore=" + maxScore +
                ", sortBy='" + sortBy + '\'' +
                ", sortOrder='" + sortOrder + '\'' +
                ", includeDeleted=" + includeDeleted +
                ", accessLevels=" + accessLevels +
                '}';
    }
}
