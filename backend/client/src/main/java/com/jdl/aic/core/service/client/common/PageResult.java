package com.jdl.aic.core.service.client.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

/**
 * 分页响应结果封装
 * 
 * @param <T> 数据项类型
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageResult<T> {
    
    /**
     * 数据列表
     */
    private List<T> records;
    
    /**
     * 分页信息
     */
    private PaginationInfo pagination;
    
    /**
     * 默认构造函数
     */
    public PageResult() {
    }
    
    /**
     * 构造函数
     * 
     * @param records 数据列表
     * @param pagination 分页信息
     */
    public PageResult(List<T> records, PaginationInfo pagination) {
        this.records = records;
        this.pagination = pagination;
    }
    
    /**
     * 创建分页结果
     * 
     * @param <T> 数据类型
     * @param records 数据列表
     * @param totalElements 总记录数
     * @param currentPage 当前页码（从0开始）
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, long totalElements, int currentPage, int pageSize) {
        PaginationInfo pagination = new PaginationInfo();
        pagination.setTotalElements(totalElements);
        pagination.setCurrentPage(currentPage);
        pagination.setPageSize(pageSize);
        pagination.setTotalPages((int) Math.ceil((double) totalElements / pageSize));
        pagination.setHasNext(currentPage < pagination.getTotalPages() - 1);
        pagination.setHasPrevious(currentPage > 0);
        
        return new PageResult<>(records, pagination);
    }
    
    // Getter and Setter methods
    
    public List<T> getRecords() {
        return records;
    }
    
    public void setRecords(List<T> records) {
        this.records = records;
    }
    
    public PaginationInfo getPagination() {
        return pagination;
    }
    
    public void setPagination(PaginationInfo pagination) {
        this.pagination = pagination;
    }
    
    /**
     * 分页信息
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PaginationInfo {
        
        /**
         * 总记录数
         */
        private Long totalElements;
        
        /**
         * 总页数
         */
        private Integer totalPages;
        
        /**
         * 当前页码（从0开始）
         */
        private Integer currentPage;
        
        /**
         * 每页大小
         */
        private Integer pageSize;
        
        /**
         * 是否有下一页
         */
        private Boolean hasNext;
        
        /**
         * 是否有上一页
         */
        private Boolean hasPrevious;
        
        // Getter and Setter methods
        
        public Long getTotalElements() {
            return totalElements;
        }
        
        public void setTotalElements(Long totalElements) {
            this.totalElements = totalElements;
        }
        
        public Integer getTotalPages() {
            return totalPages;
        }
        
        public void setTotalPages(Integer totalPages) {
            this.totalPages = totalPages;
        }
        
        public Integer getCurrentPage() {
            return currentPage;
        }
        
        public void setCurrentPage(Integer currentPage) {
            this.currentPage = currentPage;
        }
        
        public Integer getPageSize() {
            return pageSize;
        }
        
        public void setPageSize(Integer pageSize) {
            this.pageSize = pageSize;
        }
        
        public Boolean getHasNext() {
            return hasNext;
        }
        
        public void setHasNext(Boolean hasNext) {
            this.hasNext = hasNext;
        }
        
        public Boolean getHasPrevious() {
            return hasPrevious;
        }
        
        public void setHasPrevious(Boolean hasPrevious) {
            this.hasPrevious = hasPrevious;
        }
        
        @Override
        public String toString() {
            return "PaginationInfo{" +
                    "totalElements=" + totalElements +
                    ", totalPages=" + totalPages +
                    ", currentPage=" + currentPage +
                    ", pageSize=" + pageSize +
                    ", hasNext=" + hasNext +
                    ", hasPrevious=" + hasPrevious +
                    '}';
        }
    }
    
    @Override
    public String toString() {
        return "PageResult{" +
                "records=" + records +
                ", pagination=" + pagination +
                '}';
    }
}
