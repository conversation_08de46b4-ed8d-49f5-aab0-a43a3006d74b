package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.SearchResultDTO;
import com.jdl.aic.core.service.client.dto.system.SearchFilterDTO;

import java.util.List;
import java.util.Map;

/**
 * 搜索服务接口
 * 
 * <p>提供全文搜索功能，包括：
 * <ul>
 *   <li>全文搜索和高级搜索</li>
 *   <li>搜索建议和自动补全</li>
 *   <li>搜索索引管理</li>
 *   <li>搜索统计和分析</li>
 *   <li>个性化搜索推荐</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface SearchService {
    
    // ==================== 全文搜索 ====================
    
    /**
     * 全文搜索
     * 
     * @param keyword 搜索关键词
     * @param pageRequest 分页请求
     * @param filters 搜索过滤条件
     * @param userId 搜索用户ID（用于个性化）
     * @return 搜索结果
     */
    Result<SearchResultDTO> search(
            String keyword,
            PageRequest pageRequest,
            SearchFilterDTO filters,
            Long userId);
    
    /**
     * 高级搜索
     * 
     * @param searchQuery 复杂搜索查询对象
     * @param pageRequest 分页请求
     * @param userId 搜索用户ID
     * @return 搜索结果
     */
    Result<SearchResultDTO> advancedSearch(
            Object searchQuery,
            PageRequest pageRequest,
            Long userId);
    
    /**
     * 按内容类型搜索
     * 
     * @param keyword 搜索关键词
     * @param contentType 内容类型（knowledge, solution, learning_resource, user）
     * @param pageRequest 分页请求
     * @param filters 过滤条件
     * @return 搜索结果
     */
    Result<SearchResultDTO> searchByContentType(
            String keyword,
            String contentType,
            PageRequest pageRequest,
            SearchFilterDTO filters);
    
    /**
     * 相似内容搜索
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param pageRequest 分页请求
     * @return 相似内容列表
     */
    Result<SearchResultDTO> findSimilarContent(
            String contentType,
            Long contentId,
            PageRequest pageRequest);
    
    /**
     * 模糊搜索
     * 
     * @param keyword 搜索关键词
     * @param pageRequest 分页请求
     * @param fuzzyLevel 模糊程度（0-2）
     * @return 搜索结果
     */
    Result<SearchResultDTO> fuzzySearch(
            String keyword,
            PageRequest pageRequest,
            Integer fuzzyLevel);
    
    // ==================== 搜索建议和自动补全 ====================
    
    /**
     * 获取搜索建议
     * 
     * @param keyword 输入关键词
     * @param maxSuggestions 最大建议数量
     * @param contentType 内容类型过滤
     * @return 搜索建议列表
     */
    Result<List<String>> getSearchSuggestions(
            String keyword,
            Integer maxSuggestions,
            String contentType);
    
    /**
     * 获取自动补全建议
     * 
     * @param prefix 输入前缀
     * @param maxCompletions 最大补全数量
     * @return 自动补全列表
     */
    Result<List<String>> getAutoCompletions(String prefix, Integer maxCompletions);
    
    /**
     * 获取热门搜索关键词
     * 
     * @param maxKeywords 最大关键词数量
     * @param days 统计天数
     * @param contentType 内容类型过滤
     * @return 热门关键词列表
     */
    Result<List<Object>> getPopularKeywords(Integer maxKeywords, Integer days, String contentType);
    
    /**
     * 获取用户搜索历史
     * 
     * @param userId 用户ID
     * @param maxHistory 最大历史数量
     * @return 搜索历史列表
     */
    Result<List<String>> getUserSearchHistory(Long userId, Integer maxHistory);
    
    /**
     * 清除用户搜索历史
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> clearUserSearchHistory(Long userId);
    
    // ==================== 搜索索引管理 ====================
    
    /**
     * 重建搜索索引
     * 
     * @param contentType 内容类型（null表示全部）
     * @param async 是否异步执行
     * @return 操作结果
     */
    Result<Object> rebuildSearchIndex(String contentType, Boolean async);
    
    /**
     * 增量更新搜索索引
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param operation 操作类型（CREATE, UPDATE, DELETE）
     * @return 操作结果
     */
    Result<Void> updateSearchIndex(String contentType, Long contentId, String operation);
    
    /**
     * 批量更新搜索索引
     * 
     * @param updates 更新列表
     * @return 操作结果
     */
    Result<Void> batchUpdateSearchIndex(List<Object> updates);
    
    /**
     * 获取索引状态
     * 
     * @param contentType 内容类型过滤
     * @return 索引状态信息
     */
    Result<Object> getIndexStatus(String contentType);
    
    /**
     * 优化搜索索引
     * 
     * @param contentType 内容类型（null表示全部）
     * @return 优化结果
     */
    Result<Object> optimizeSearchIndex(String contentType);
    
    // ==================== 搜索统计和分析 ====================
    
    /**
     * 记录搜索行为
     * 
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param resultCount 结果数量
     * @param clickedItemId 点击的结果项ID
     * @return 操作结果
     */
    Result<Void> recordSearchBehavior(
            String keyword,
            Long userId,
            Long resultCount,
            Long clickedItemId);
    
    /**
     * 获取搜索统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param contentType 内容类型过滤
     * @return 搜索统计信息
     */
    Result<Object> getSearchStatistics(String startDate, String endDate, String contentType);
    
    /**
     * 获取搜索热力图数据
     * 
     * @param days 统计天数
     * @return 热力图数据
     */
    Result<Object> getSearchHeatmapData(Integer days);
    
    /**
     * 获取无结果搜索关键词
     * 
     * @param pageRequest 分页请求
     * @param days 统计天数
     * @return 无结果关键词列表
     */
    Result<Object> getNoResultKeywords(PageRequest pageRequest, Integer days);
    
    /**
     * 分析搜索趋势
     * 
     * @param keyword 关键词
     * @param days 分析天数
     * @return 搜索趋势数据
     */
    Result<Object> analyzeSearchTrend(String keyword, Integer days);
    
    // ==================== 个性化搜索 ====================
    
    /**
     * 获取个性化搜索推荐
     * 
     * @param userId 用户ID
     * @param maxRecommendations 最大推荐数量
     * @return 推荐搜索关键词
     */
    Result<List<String>> getPersonalizedSearchRecommendations(Long userId, Integer maxRecommendations);
    
    /**
     * 更新用户搜索偏好
     * 
     * @param userId 用户ID
     * @param preferences 搜索偏好设置
     * @return 操作结果
     */
    Result<Void> updateUserSearchPreferences(Long userId, Map<String, Object> preferences);
    
    /**
     * 获取用户搜索偏好
     * 
     * @param userId 用户ID
     * @return 用户搜索偏好
     */
    Result<Map<String, Object>> getUserSearchPreferences(Long userId);
    
    /**
     * 基于用户行为的智能搜索
     * 
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @return 智能搜索结果
     */
    Result<SearchResultDTO> intelligentSearch(
            String keyword,
            Long userId,
            PageRequest pageRequest);
}
