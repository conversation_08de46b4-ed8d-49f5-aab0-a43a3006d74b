package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 更新评论请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class UpdateCommentRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 评论ID
     */
    @NotNull(message = "评论ID不能为空")
    private Long commentId;

    /**
     * 新的评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    @Size(max = 1000, message = "评论内容不能超过1000字符")
    private String commentText;

    public UpdateCommentRequest() {
    }

    public UpdateCommentRequest(Long userId, Long commentId, String commentText) {
        this.userId = userId;
        this.commentId = commentId;
        this.commentText = commentText;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCommentId() {
        return commentId;
    }

    public void setCommentId(Long commentId) {
        this.commentId = commentId;
    }

    public String getCommentText() {
        return commentText;
    }

    public void setCommentText(String commentText) {
        this.commentText = commentText;
    }

    @Override
    public String toString() {
        return "UpdateCommentRequest{" +
                "userId=" + userId +
                ", commentId=" + commentId +
                ", commentText='" + commentText + '\'' +
                '}';
    }
}
