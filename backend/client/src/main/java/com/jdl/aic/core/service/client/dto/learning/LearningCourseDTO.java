package com.jdl.aic.core.service.client.dto.learning;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学习课程DTO
 * 
 * <p>封装学习课程的完整信息，包括课程基本信息、难度级别、学习目标、统计数据等。
 * 支持多种难度级别：初级、中级、高级、专家级。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LearningCourseDTO {
    
    /**
     * 课程ID
     */
    private Long id;
    
    /**
     * 课程名称
     */
    @NotBlank(message = "课程名称不能为空")
    private String name;
    
    /**
     * 课程描述
     */
    private String description;
    
    /**
     * 课程分类
     */
    private String category;
    
    /**
     * 难度级别（BEGINNER:初级, INTERMEDIATE:中级, ADVANCED:高级, EXPERT:专家级）
     */
    private String difficultyLevel;
    
    /**
     * 预估总学习时长（小时）
     */
    private BigDecimal totalHours;
    
    /**
     * 包含资源数量
     */
    private Integer resourceCount;
    
    /**
     * 报名学习人数
     */
    private Integer enrolledCount;
    
    /**
     * 完成人数
     */
    private Integer completionCount;
    
    /**
     * 平均完成率
     */
    private BigDecimal completionRate;
    
    /**
     * 前置知识要求
     */
    private String prerequisites;
    
    /**
     * 学习目标
     */
    private String learningGoals;
    
    /**
     * 创建者ID
     */
    @NotBlank(message = "创建者ID不能为空")
    private String creatorId;
    
    /**
     * 创建者姓名
     */
    private String creatorName;
    
    /**
     * 课程状态（DRAFT:草稿, PUBLISHED:已发布, ARCHIVED:已归档）
     */
    private String status;
    
    /**
     * 是否官方课程
     */
    private Boolean isOfficial;
    
    /**
     * 封面图片URL
     */
    private String coverImageUrl;
    
    /**
     * 标签列表，逗号分隔
     */
    private String tags;
    
    /**
     * 标签列表（解析后的数组）
     */
    private List<String> tagList;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;

    /**
     * 关联的分类
     */
    private List<ContentCategoryRelationDTO> categories;

    /**
     * 资源关联表
     */
    private List<LearningPathResourceDTO> paths;

    /**
     * 默认构造函数
     */
    public LearningCourseDTO() {
    }

    /**
     * 基础构造函数
     *
     * @param name 课程名称
     * @param creatorId 创建者ID
     */
    public LearningCourseDTO(String name, String creatorId) {
        this.name = name;
        this.creatorId = creatorId;
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public BigDecimal getTotalHours() {
        return totalHours;
    }

    public void setTotalHours(BigDecimal totalHours) {
        this.totalHours = totalHours;
    }

    public Integer getResourceCount() {
        return resourceCount;
    }

    public void setResourceCount(Integer resourceCount) {
        this.resourceCount = resourceCount;
    }

    public Integer getEnrolledCount() {
        return enrolledCount;
    }

    public void setEnrolledCount(Integer enrolledCount) {
        this.enrolledCount = enrolledCount;
    }

    public Integer getCompletionCount() {
        return completionCount;
    }

    public void setCompletionCount(Integer completionCount) {
        this.completionCount = completionCount;
    }

    public BigDecimal getCompletionRate() {
        return completionRate;
    }

    public void setCompletionRate(BigDecimal completionRate) {
        this.completionRate = completionRate;
    }

    public String getPrerequisites() {
        return prerequisites;
    }

    public void setPrerequisites(String prerequisites) {
        this.prerequisites = prerequisites;
    }

    public String getLearningGoals() {
        return learningGoals;
    }

    public void setLearningGoals(String learningGoals) {
        this.learningGoals = learningGoals;
    }

    public String getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(String creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(Boolean isOfficial) {
        this.isOfficial = isOfficial;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public List<String> getTagList() {
        return tagList;
    }

    public void setTagList(List<String> tagList) {
        this.tagList = tagList;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public List<ContentCategoryRelationDTO> getCategories() {
        return categories;
    }

    public void setCategories(List<ContentCategoryRelationDTO> categories) {
        this.categories = categories;
    }

    public List<LearningPathResourceDTO> getPaths() {
        return paths;
    }

    public void setPaths(List<LearningPathResourceDTO> paths) {
        this.paths = paths;
    }

    @Override
    public String toString() {
        return "LearningCourseDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", category='" + category + '\'' +
                ", difficultyLevel='" + difficultyLevel + '\'' +
                ", totalHours=" + totalHours +
                ", resourceCount=" + resourceCount +
                ", enrolledCount=" + enrolledCount +
                ", completionCount=" + completionCount +
                ", completionRate=" + completionRate +
                ", creatorId='" + creatorId + '\'' +
                ", creatorName='" + creatorName + '\'' +
                ", status='" + status + '\'' +
                ", isOfficial=" + isOfficial +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
