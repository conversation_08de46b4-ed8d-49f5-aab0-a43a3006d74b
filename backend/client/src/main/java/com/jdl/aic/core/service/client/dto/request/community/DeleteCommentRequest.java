package com.jdl.aic.core.service.client.dto.request.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 删除评论请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeleteCommentRequest {
    
    /**
     * 评论ID
     */
    @NotNull(message = "评论ID不能为空")
    private Long id;
    
    /**
     * 操作用户ID
     */
    @NotNull(message = "操作用户ID不能为空")
    private Long userId;
    
    /**
     * 默认构造函数
     */
    public DeleteCommentRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 评论ID
     * @param userId 操作用户ID
     */
    public DeleteCommentRequest(Long id, Long userId) {
        this.id = id;
        this.userId = userId;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Override
    public String toString() {
        return "DeleteCommentRequest{" +
                "id=" + id +
                ", userId=" + userId +
                '}';
    }
}
