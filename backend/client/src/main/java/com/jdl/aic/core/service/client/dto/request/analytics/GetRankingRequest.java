package com.jdl.aic.core.service.client.dto.request.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 获取排行榜数据请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetRankingRequest {
    
    /**
     * 排行类型（popular_content, active_users, trending_tags等）
     */
    @NotBlank(message = "排行类型不能为空")
    private String rankingType;
    
    /**
     * 返回数量限制
     */
    private Integer limit;
    
    /**
     * 排行条件
     */
    private Map<String, Object> criteria;
    
    /**
     * 默认构造函数
     */
    public GetRankingRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param rankingType 排行类型
     * @param limit 返回数量限制
     * @param criteria 排行条件
     */
    public GetRankingRequest(String rankingType, Integer limit, Map<String, Object> criteria) {
        this.rankingType = rankingType;
        this.limit = limit;
        this.criteria = criteria;
    }
    
    // Getter and Setter methods
    
    public String getRankingType() {
        return rankingType;
    }
    
    public void setRankingType(String rankingType) {
        this.rankingType = rankingType;
    }
    
    public Integer getLimit() {
        return limit;
    }
    
    public void setLimit(Integer limit) {
        this.limit = limit;
    }
    
    public Map<String, Object> getCriteria() {
        return criteria;
    }
    
    public void setCriteria(Map<String, Object> criteria) {
        this.criteria = criteria;
    }
    
    @Override
    public String toString() {
        return "GetRankingRequest{" +
                "rankingType='" + rankingType + '\'' +
                ", limit=" + limit +
                ", criteria=" + criteria +
                '}';
    }
}
