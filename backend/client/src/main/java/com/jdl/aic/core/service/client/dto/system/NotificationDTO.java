package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知DTO
 * 
 * <p>封装系统通知的完整信息，包括通知内容、类型、状态、接收者等。
 * 支持多种通知类型和发送渠道。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NotificationDTO {
    
    /**
     * 通知ID
     */
    private Long id;
    
    /**
     * 通知类型（system:系统通知, comment:评论通知, like:点赞通知, follow:关注通知）
     */
    @NotBlank(message = "通知类型不能为空")
    private String type;
    
    /**
     * 通知标题
     */
    @NotBlank(message = "通知标题不能为空")
    private String title;
    
    /**
     * 通知内容
     */
    @NotBlank(message = "通知内容不能为空")
    private String content;
    
    /**
     * 接收用户ID
     */
    @NotNull(message = "接收用户ID不能为空")
    private Long recipientId;
    
    /**
     * 接收用户名
     */
    private String recipientName;
    
    /**
     * 发送用户ID
     */
    private Long senderId;
    
    /**
     * 发送用户名
     */
    private String senderName;
    
    /**
     * 发送用户头像
     */
    private String senderAvatar;
    
    /**
     * 关联内容类型（knowledge, solution, learning_resource, user）
     */
    private String relatedContentType;
    
    /**
     * 关联内容ID
     */
    private Long relatedContentId;
    
    /**
     * 关联内容标题
     */
    private String relatedContentTitle;
    
    /**
     * 关联内容URL
     */
    private String relatedContentUrl;
    
    /**
     * 通知状态（0:未读, 1:已读, 2:已删除）
     */
    private Integer status;
    
    /**
     * 优先级（0:低, 1:普通, 2:高, 3:紧急）
     */
    private Integer priority;
    
    /**
     * 发送渠道（web:站内信, email:邮件, sms:短信, push:推送）
     */
    private String channel;
    
    /**
     * 扩展数据
     */
    private Map<String, Object> extraData;
    
    /**
     * 阅读时间
     */
    private LocalDateTime readAt;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public NotificationDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Long getRecipientId() {
        return recipientId;
    }
    
    public void setRecipientId(Long recipientId) {
        this.recipientId = recipientId;
    }
    
    public String getRecipientName() {
        return recipientName;
    }
    
    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }
    
    public Long getSenderId() {
        return senderId;
    }
    
    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }
    
    public String getSenderName() {
        return senderName;
    }
    
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }
    
    public String getSenderAvatar() {
        return senderAvatar;
    }
    
    public void setSenderAvatar(String senderAvatar) {
        this.senderAvatar = senderAvatar;
    }
    
    public String getRelatedContentType() {
        return relatedContentType;
    }
    
    public void setRelatedContentType(String relatedContentType) {
        this.relatedContentType = relatedContentType;
    }
    
    public Long getRelatedContentId() {
        return relatedContentId;
    }
    
    public void setRelatedContentId(Long relatedContentId) {
        this.relatedContentId = relatedContentId;
    }
    
    public String getRelatedContentTitle() {
        return relatedContentTitle;
    }
    
    public void setRelatedContentTitle(String relatedContentTitle) {
        this.relatedContentTitle = relatedContentTitle;
    }
    
    public String getRelatedContentUrl() {
        return relatedContentUrl;
    }
    
    public void setRelatedContentUrl(String relatedContentUrl) {
        this.relatedContentUrl = relatedContentUrl;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public String getChannel() {
        return channel;
    }
    
    public void setChannel(String channel) {
        this.channel = channel;
    }
    
    public Map<String, Object> getExtraData() {
        return extraData;
    }
    
    public void setExtraData(Map<String, Object> extraData) {
        this.extraData = extraData;
    }
    
    public LocalDateTime getReadAt() {
        return readAt;
    }
    
    public void setReadAt(LocalDateTime readAt) {
        this.readAt = readAt;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "NotificationDTO{" +
                "id=" + id +
                ", type='" + type + '\'' +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", recipientId=" + recipientId +
                ", recipientName='" + recipientName + '\'' +
                ", senderId=" + senderId +
                ", senderName='" + senderName + '\'' +
                ", senderAvatar='" + senderAvatar + '\'' +
                ", relatedContentType='" + relatedContentType + '\'' +
                ", relatedContentId=" + relatedContentId +
                ", relatedContentTitle='" + relatedContentTitle + '\'' +
                ", relatedContentUrl='" + relatedContentUrl + '\'' +
                ", status=" + status +
                ", priority=" + priority +
                ", channel='" + channel + '\'' +
                ", extraData=" + extraData +
                ", readAt=" + readAt +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
