package com.jdl.aic.core.service.portal.client;

import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.UserFollowDTO;
import com.jdl.aic.core.service.client.dto.community.request.CheckFollowStatusRequest;
import com.jdl.aic.core.service.client.dto.community.request.FollowUserRequest;
import com.jdl.aic.core.service.client.dto.community.request.GetUserFollowListRequest;
import com.jdl.aic.core.service.client.dto.community.request.UnfollowUserRequest;

import java.util.List;

/**
 * 用户关注服务接口
 *
 * <p>提供用户关注相关的业务功能，包括：
 * <ul>
 *   <li>用户关注和取消关注</li>
 *   <li>用户关注列表和粉丝列表查询</li>
 *   <li>关注状态检查和统计</li>
 *   <li>关注关系管理</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface UserFollowService {

    // ==================== 用户关注基本操作 ====================

    /**
     * 关注用户
     *
     * @param request 关注用户请求参数
     * @return 关注结果
     */
    Result<UserFollowDTO> followUser(FollowUserRequest request);

    /**
     * 取消关注用户
     *
     * @param request 取消关注用户请求参数
     * @return 操作结果
     */
    Result<Void> unfollowUser(UnfollowUserRequest request);

    /**
     * 切换关注状态（如果已关注则取消，如果未关注则关注）
     *
     * @param userId 用户ID
     * @param followedId 被关注人ID
     * @return 操作结果，包含当前关注状态
     */
    Result<Boolean> toggleFollowStatus(Long userId, Long followedId);

    /**
     * 检查用户是否已关注指定用户
     *
     * @param request 检查关注状态请求参数
     * @return 关注状态
     */
    Result<Boolean> isUserFollowed(CheckFollowStatusRequest request);

    // ==================== 用户关注查询 ====================

    /**
     * 获取用户关注列表（分页）
     *
     * @param request 获取用户关注列表请求参数
     * @return 关注列表
     */
    Result<PageResult<UserFollowDTO>> getUserFollowList(GetUserFollowListRequest request);

    /**
     * 获取用户关注的人列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 关注的人列表
     */
    Result<List<UserFollowDTO>> getUserFollowing(Long userId, Integer limit);

    /**
     * 获取用户的粉丝列表
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 粉丝列表
     */
    Result<List<UserFollowDTO>> getUserFollowers(Long userId, Integer limit);

    /**
     * 获取最近关注的用户
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近关注的用户列表
     */
    Result<List<UserFollowDTO>> getRecentFollowing(Long userId, Integer limit);

    /**
     * 根据关注ID获取关注详情
     *
     * @param followId 关注ID
     * @return 关注详情
     */
    Result<UserFollowDTO> getFollowById(Long followId);

    // ==================== 用户关注统计 ====================

    /**
     * 统计用户关注的人数
     *
     * @param userId 用户ID
     * @return 关注人数
     */
    Result<Integer> countUserFollowing(Long userId);

    /**
     * 统计用户的粉丝数
     *
     * @param userId 用户ID
     * @return 粉丝数
     */
    Result<Integer> countUserFollowers(Long userId);

    /**
     * 获取用户关注统计信息
     *
     * @param userId 用户ID
     * @return 关注统计信息（关注数、粉丝数等）
     */
    Result<Object> getUserFollowStats(Long userId);

    /**
     * 批量获取用户关注统计信息
     *
     * @param userIds 用户ID列表
     * @return 用户关注统计信息映射
     */
    Result<List<Object>> getUserFollowStatsBatch(List<Long> userIds);

    // ==================== 用户关注批量操作 ====================

    /**
     * 批量关注用户
     *
     * @param userId 用户ID
     * @param followedIds 被关注人ID列表
     * @return 批量操作结果
     */
    Result<List<UserFollowDTO>> batchFollowUsers(Long userId, List<Long> followedIds);

    /**
     * 批量取消关注用户
     *
     * @param userId 用户ID
     * @param followedIds 被关注人ID列表
     * @return 批量操作结果
     */
    Result<Void> batchUnfollowUsers(Long userId, List<Long> followedIds);

    /**
     * 批量取消关注用户（根据关注ID）
     *
     * @param userId 用户ID
     * @param followIds 关注ID列表
     * @return 批量操作结果
     */
    Result<Void> batchUnfollowUsersByIds(Long userId, List<Long> followIds);

    // ==================== 用户关注推荐 ====================

    /**
     * 获取推荐关注的用户
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐用户列表
     */
    Result<List<Object>> getRecommendedUsers(Long userId, Integer limit);

    /**
     * 基于共同关注推荐用户
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐用户列表
     */
    Result<List<Object>> getRecommendedUsersByMutualFollows(Long userId, Integer limit);

    /**
     * 基于部门推荐用户
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐用户列表
     */
    Result<List<Object>> getRecommendedUsersByDepartment(Long userId, Integer limit);

    /**
     * 获取可能认识的人
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 可能认识的人列表
     */
    Result<List<Object>> getPeopleYouMayKnow(Long userId, Integer limit);

    // ==================== 用户关注分析 ====================

    /**
     * 获取用户关注趋势
     *
     * @param userId 用户ID
     * @param days 统计天数
     * @return 关注趋势数据
     */
    Result<Object> getUserFollowTrend(Long userId, Integer days);

    /**
     * 获取用户关注偏好分析
     *
     * @param userId 用户ID
     * @return 关注偏好数据
     */
    Result<Object> getUserFollowPreference(Long userId);

    /**
     * 获取共同关注的用户
     *
     * @param userId1 用户1ID
     * @param userId2 用户2ID
     * @return 共同关注的用户列表
     */
    Result<List<UserFollowDTO>> getMutualFollows(Long userId1, Long userId2);

    /**
     * 获取关注网络分析
     *
     * @param userId 用户ID
     * @param depth 分析深度
     * @return 关注网络数据
     */
    Result<Object> getFollowNetworkAnalysis(Long userId, Integer depth);

    // ==================== 用户关注管理 ====================

    /**
     * 清理无效的关注关系
     *
     * @param userId 用户ID
     * @return 清理结果
     */
    Result<Integer> cleanupInvalidFollows(Long userId);

    /**
     * 导出用户关注数据
     *
     * @param userId 用户ID
     * @param format 导出格式（excel, csv等）
     * @return 导出结果
     */
    Result<Object> exportUserFollowData(Long userId, String format);

    /**
     * 获取关注活跃度统计
     *
     * @param userId 用户ID
     * @param days 统计天数
     * @return 活跃度统计数据
     */
    Result<Object> getFollowActivityStats(Long userId, Integer days);

    /**
     * 获取热门被关注用户
     *
     * @param limit 限制数量
     * @param days 统计天数（可选）
     * @return 热门用户列表
     */
    Result<List<Object>> getPopularUsers(Integer limit, Integer days);

    // ==================== 用户关注通知 ====================

    /**
     * 获取关注相关通知
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 通知列表
     */
    Result<List<Object>> getFollowNotifications(Long userId, Integer limit);

    /**
     * 标记关注通知为已读
     *
     * @param userId 用户ID
     * @param notificationIds 通知ID列表
     * @return 操作结果
     */
    Result<Void> markFollowNotificationsAsRead(Long userId, List<Long> notificationIds);
}
