package com.jdl.aic.core.service.client.dto.request.solution;

import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 获取用户解决方案请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetUserSolutionsRequest {
    
    /**
     * 用户ID（必填）
     */
    @NotNull(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 分页请求参数
     */
    @NotNull(message = "分页请求参数不能为空")
    @Valid
    private PageRequest pageRequest;
    
    /**
     * 状态过滤（0:草稿, 1:发布, 2:下线）
     */
    private Integer status;
    
    /**
     * 默认构造函数
     */
    public GetUserSolutionsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求参数
     */
    public GetUserSolutionsRequest(String userId, PageRequest pageRequest) {
        this.userId = userId;
        this.pageRequest = pageRequest;
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求参数
     * @param status 状态过滤
     */
    public GetUserSolutionsRequest(String userId, PageRequest pageRequest, Integer status) {
        this.userId = userId;
        this.pageRequest = pageRequest;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public PageRequest getPageRequest() {
        return pageRequest;
    }
    
    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "GetUserSolutionsRequest{" +
                "userId=" + userId +
                ", pageRequest=" + pageRequest +
                ", status=" + status +
                '}';
    }
}
