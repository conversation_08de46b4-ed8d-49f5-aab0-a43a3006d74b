package com.jdl.aic.core.service.client.dto.community.request;

import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.constraints.NotNull;

/**
 * 获取用户收藏列表请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetUserFavoritesRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 分页请求
     */
    @NotNull(message = "分页参数不能为空")
    private PageRequest pageRequest;

    /**
     * 内容类型过滤（可选）
     */
    private Integer contentType;

    public GetUserFavoritesRequest() {
    }

    public GetUserFavoritesRequest(Long userId, PageRequest pageRequest) {
        this.userId = userId;
        this.pageRequest = pageRequest;
    }

    public GetUserFavoritesRequest(Long userId, PageRequest pageRequest, Integer contentType) {
        this.userId = userId;
        this.pageRequest = pageRequest;
        this.contentType = contentType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public PageRequest getPageRequest() {
        return pageRequest;
    }

    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    @Override
    public String toString() {
        return "GetUserFavoritesRequest{" +
                "userId=" + userId +
                ", pageRequest=" + pageRequest +
                ", contentType=" + contentType +
                '}';
    }
}
