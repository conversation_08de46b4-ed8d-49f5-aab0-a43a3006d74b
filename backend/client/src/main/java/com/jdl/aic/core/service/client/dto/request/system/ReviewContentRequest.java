package com.jdl.aic.core.service.client.dto.request.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 审核内容请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ReviewContentRequest {
    
    /**
     * 审核ID
     */
    @NotNull(message = "审核ID不能为空")
    private Long reviewId;
    
    /**
     * 审核状态（1:通过, 2:拒绝）
     */
    @NotNull(message = "审核状态不能为空")
    private Integer reviewStatus;
    
    /**
     * 审核意见
     */
    private String reviewComment;
    
    /**
     * 审核人ID
     */
    @NotNull(message = "审核人ID不能为空")
    private Long reviewerId;
    
    /**
     * 默认构造函数
     */
    public ReviewContentRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param reviewId 审核ID
     * @param reviewStatus 审核状态
     * @param reviewComment 审核意见
     * @param reviewerId 审核人ID
     */
    public ReviewContentRequest(Long reviewId, Integer reviewStatus, String reviewComment, Long reviewerId) {
        this.reviewId = reviewId;
        this.reviewStatus = reviewStatus;
        this.reviewComment = reviewComment;
        this.reviewerId = reviewerId;
    }
    
    // Getter and Setter methods
    
    public Long getReviewId() {
        return reviewId;
    }
    
    public void setReviewId(Long reviewId) {
        this.reviewId = reviewId;
    }
    
    public Integer getReviewStatus() {
        return reviewStatus;
    }
    
    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    
    public String getReviewComment() {
        return reviewComment;
    }
    
    public void setReviewComment(String reviewComment) {
        this.reviewComment = reviewComment;
    }
    
    public Long getReviewerId() {
        return reviewerId;
    }
    
    public void setReviewerId(Long reviewerId) {
        this.reviewerId = reviewerId;
    }
    
    @Override
    public String toString() {
        return "ReviewContentRequest{" +
                "reviewId=" + reviewId +
                ", reviewStatus=" + reviewStatus +
                ", reviewComment='" + reviewComment + '\'' +
                ", reviewerId=" + reviewerId +
                '}';
    }
}
