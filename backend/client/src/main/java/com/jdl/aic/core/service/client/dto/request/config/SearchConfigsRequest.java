package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 搜索配置请求（通用）
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchConfigsRequest {
    
    /**
     * 搜索关键词
     */
    private String keyword;
    
    /**
     * 是否启用（可选）
     */
    private Boolean isEnabled;
    
    /**
     * 默认构造函数
     */
    public SearchConfigsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param keyword 搜索关键词
     * @param isEnabled 是否启用
     */
    public SearchConfigsRequest(String keyword, Boolean isEnabled) {
        this.keyword = keyword;
        this.isEnabled = isEnabled;
    }
    
    // Getter and Setter methods
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(<PERSON>olean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    @Override
    public String toString() {
        return "SearchConfigsRequest{" +
                "keyword='" + keyword + '\'' +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
