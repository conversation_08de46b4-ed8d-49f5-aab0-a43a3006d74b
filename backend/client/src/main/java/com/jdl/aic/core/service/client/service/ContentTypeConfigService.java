package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.ContentTypeConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;

import java.util.List;

/**
 * 内容类型配置管理服务接口
 * 
 * <p>提供内容类型配置管理功能，包括：
 * <ul>
 *   <li>内容类型配置的CRUD操作</li>
 *   <li>门户模块配置管理</li>
 *   <li>配置状态和排序管理</li>
 *   <li>配置查询和搜索</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface ContentTypeConfigService {
    
    // ==================== 内容类型配置管理 ====================

    /**
     * 获取内容类型配置列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询条件请求
     * @return 内容类型配置列表
     */
    Result<PageResult<ContentTypeConfigDTO>> getConfigList(
            PageRequest pageRequest,
            GetContentTypeConfigListRequest request);

    /**
     * 根据ID获取内容类型配置详情
     * 
     * @param id 配置ID
     * @return 内容类型配置详情
     */
    Result<ContentTypeConfigDTO> getConfigById(Long id);

    /**
     * 根据编码获取内容类型配置详情
     * 
     * @param code 内容类型编码
     * @return 内容类型配置详情
     */
    Result<ContentTypeConfigDTO> getConfigByCode(String code);

    /**
     * 创建内容类型配置
     * 
     * @param config 内容类型配置信息
     * @return 创建结果
     */
    Result<ContentTypeConfigDTO> createConfig(ContentTypeConfigDTO config);

    /**
     * 更新内容类型配置信息
     * 
     * @param id 配置ID
     * @param config 内容类型配置信息
     * @return 更新结果
     */
    Result<ContentTypeConfigDTO> updateConfig(Long id, ContentTypeConfigDTO config);

    /**
     * 删除内容类型配置
     * 
     * @param id 配置ID
     * @return 删除结果
     */
    Result<Void> deleteConfig(Long id);

    /**
     * 启用/禁用内容类型配置
     *
     * @param request 启用/禁用请求
     * @return 操作结果
     */
    Result<Void> toggleConfigStatus(ToggleContentTypeConfigStatusRequest request);

    /**
     * 更新内容类型配置排序
     *
     * @param request 更新排序请求
     * @return 操作结果
     */
    Result<Void> updateConfigSortOrder(UpdateContentTypeConfigSortOrderRequest request);

    /**
     * 获取所有启用的内容类型配置
     * 
     * @return 启用的内容类型配置列表
     */
    Result<List<ContentTypeConfigDTO>> getAllActiveConfigs();

    /**
     * 获取门户模块配置列表
     * 
     * @param isActive 是否启用（可选）
     * @return 门户模块配置列表
     */
    Result<List<ContentTypeConfigDTO>> getPortalModuleConfigs(Boolean isActive);

    /**
     * 根据表名获取内容类型配置
     * 
     * @param tableName 表名
     * @return 内容类型配置详情
     */
    Result<ContentTypeConfigDTO> getConfigByTableName(String tableName);

    /**
     * 搜索内容类型配置
     *
     * @param request 搜索请求
     * @return 搜索结果列表
     */
    Result<List<ContentTypeConfigDTO>> searchConfigs(SearchContentTypeConfigsRequest request);

    /**
     * 批量更新内容类型配置排序
     *
     * @param request 批量更新排序请求
     * @return 操作结果
     */
    Result<Void> batchUpdateSortOrder(BatchUpdateContentTypeConfigSortOrderRequest request);

    /**
     * 检查内容类型编码是否已存在
     *
     * @param request 检查编码存在请求
     * @return 是否存在
     */
    Result<Boolean> checkCodeExists(CheckContentTypeCodeExistsRequest request);

    /**
     * 检查表名是否已被使用
     *
     * @param request 检查表名存在请求
     * @return 是否已被使用
     */
    Result<Boolean> checkTableNameExists(CheckContentTypeTableNameExistsRequest request);
}
