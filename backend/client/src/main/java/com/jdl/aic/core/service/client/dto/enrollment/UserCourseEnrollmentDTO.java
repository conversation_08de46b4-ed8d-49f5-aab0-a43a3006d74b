package com.jdl.aic.core.service.client.dto.enrollment;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户课程报名DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserCourseEnrollmentDTO {
    
    /**
     * 报名ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 课程ID
     */
    @NotNull(message = "课程ID不能为空")
    private Long courseId;
    
    /**
     * 课程名称（关联查询获取）
     */
    private String courseName;
    
    /**
     * 课程描述（关联查询获取）
     */
    private String courseDescription;
    
    /**
     * 课程封面图片URL（关联查询获取）
     */
    private String courseCoverImageUrl;
    
    /**
     * 报名状态（ENROLLED:已报名, IN_PROGRESS:学习中, COMPLETED:已完成, DROPPED:已退出）
     */
    private String enrollmentStatus;
    
    /**
     * 报名时间
     */
    private LocalDateTime enrolledAt;
    
    /**
     * 完成时间
     */
    private LocalDateTime completedAt;
    
    /**
     * 学习进度百分比（0.00-100.00）
     */
    @DecimalMin(value = "0.00", message = "学习进度不能小于0")
    @DecimalMax(value = "100.00", message = "学习进度不能大于100")
    private BigDecimal progressPercentage;
    
    /**
     * 已完成阶段数
     */
    private Integer completedStages;
    
    /**
     * 总阶段数
     */
    private Integer totalStages;
    
    /**
     * 学习时长（小时）
     */
    @DecimalMin(value = "0.00", message = "学习时长不能小于0")
    private BigDecimal studyHours;
    
    /**
     * 最后学习时间
     */
    private LocalDateTime lastStudyAt;
    
    /**
     * 报名来源（WEB:网页, MOBILE:移动端, API:接口）
     */
    private String enrollmentSource;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public UserCourseEnrollmentDTO() {
    }
    
    /**
     * 基础构造函数
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     */
    public UserCourseEnrollmentDTO(Long userId, Long courseId) {
        this.userId = userId;
        this.courseId = courseId;
        this.enrollmentStatus = "ENROLLED";
        this.progressPercentage = BigDecimal.ZERO;
        this.completedStages = 0;
        this.studyHours = BigDecimal.ZERO;
        this.enrollmentSource = "WEB";
    }
    
    /**
     * 完整构造函数
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @param enrollmentStatus 报名状态
     * @param enrollmentSource 报名来源
     */
    public UserCourseEnrollmentDTO(Long userId, Long courseId, String enrollmentStatus, String enrollmentSource) {
        this.userId = userId;
        this.courseId = courseId;
        this.enrollmentStatus = enrollmentStatus;
        this.enrollmentSource = enrollmentSource;
        this.progressPercentage = BigDecimal.ZERO;
        this.completedStages = 0;
        this.studyHours = BigDecimal.ZERO;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getCourseId() {
        return courseId;
    }
    
    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
    
    public String getCourseDescription() {
        return courseDescription;
    }
    
    public void setCourseDescription(String courseDescription) {
        this.courseDescription = courseDescription;
    }
    
    public String getCourseCoverImageUrl() {
        return courseCoverImageUrl;
    }
    
    public void setCourseCoverImageUrl(String courseCoverImageUrl) {
        this.courseCoverImageUrl = courseCoverImageUrl;
    }
    
    public String getEnrollmentStatus() {
        return enrollmentStatus;
    }
    
    public void setEnrollmentStatus(String enrollmentStatus) {
        this.enrollmentStatus = enrollmentStatus;
    }
    
    public LocalDateTime getEnrolledAt() {
        return enrolledAt;
    }
    
    public void setEnrolledAt(LocalDateTime enrolledAt) {
        this.enrolledAt = enrolledAt;
    }
    
    public LocalDateTime getCompletedAt() {
        return completedAt;
    }
    
    public void setCompletedAt(LocalDateTime completedAt) {
        this.completedAt = completedAt;
    }
    
    public BigDecimal getProgressPercentage() {
        return progressPercentage;
    }
    
    public void setProgressPercentage(BigDecimal progressPercentage) {
        this.progressPercentage = progressPercentage;
    }
    
    public Integer getCompletedStages() {
        return completedStages;
    }
    
    public void setCompletedStages(Integer completedStages) {
        this.completedStages = completedStages;
    }
    
    public Integer getTotalStages() {
        return totalStages;
    }
    
    public void setTotalStages(Integer totalStages) {
        this.totalStages = totalStages;
    }
    
    public BigDecimal getStudyHours() {
        return studyHours;
    }
    
    public void setStudyHours(BigDecimal studyHours) {
        this.studyHours = studyHours;
    }
    
    public LocalDateTime getLastStudyAt() {
        return lastStudyAt;
    }
    
    public void setLastStudyAt(LocalDateTime lastStudyAt) {
        this.lastStudyAt = lastStudyAt;
    }
    
    public String getEnrollmentSource() {
        return enrollmentSource;
    }
    
    public void setEnrollmentSource(String enrollmentSource) {
        this.enrollmentSource = enrollmentSource;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "UserCourseEnrollmentDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", courseId=" + courseId +
                ", courseName='" + courseName + '\'' +
                ", enrollmentStatus='" + enrollmentStatus + '\'' +
                ", progressPercentage=" + progressPercentage +
                ", completedStages=" + completedStages +
                ", totalStages=" + totalStages +
                ", studyHours=" + studyHours +
                ", enrollmentSource='" + enrollmentSource + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        UserCourseEnrollmentDTO that = (UserCourseEnrollmentDTO) o;
        
        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (userId != null ? !userId.equals(that.userId) : that.userId != null) return false;
        return courseId != null ? courseId.equals(that.courseId) : that.courseId == null;
    }
    
    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (userId != null ? userId.hashCode() : 0);
        result = 31 * result + (courseId != null ? courseId.hashCode() : 0);
        return result;
    }
}
