package com.jdl.aic.core.service.client.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论DTO
 * 
 * <p>封装评论的完整信息，支持对知识、解决方案、学习资源等内容的评论。
 * 支持嵌套回复和评论状态管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CommentDTO {
    
    /**
     * 评论ID
     */
    private Long id;
    
    /**
     * 内容类型（0:知识, 1:资讯, 2:解决方案）
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 当content_type为知识时，关联知识类型ID
     */
    private Long relatedKnowledgeTypeId;
    
    /**
     * 评论用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 用户头像
     */
    private String userAvatar;
    
    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    private String content;
    
    /**
     * 父评论ID（回复的评论ID）
     */
    private Long parentId;
    
    /**
     * 状态（0:待审核, 1:已发布, 2:已删除）
     */
    private Integer status;
    
    /**
     * 点赞数量
     */
    private Integer likeCount;
    
    /**
     * 当前用户是否点赞
     */
    private Boolean isLiked;
    
    /**
     * 回复列表
     */
    private List<CommentDTO> replies;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public CommentDTO() {
    }
    
    /**
     * 构造函数
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param content 评论内容
     */
    public CommentDTO(Integer contentType, Long contentId, Long userId, String content) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.userId = userId;
        this.content = content;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public Long getRelatedKnowledgeTypeId() {
        return relatedKnowledgeTypeId;
    }

    public void setRelatedKnowledgeTypeId(Long relatedKnowledgeTypeId) {
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
    }

    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getUserAvatar() {
        return userAvatar;
    }
    
    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public Boolean getIsLiked() {
        return isLiked;
    }
    
    public void setIsLiked(Boolean isLiked) {
        this.isLiked = isLiked;
    }
    
    public List<CommentDTO> getReplies() {
        return replies;
    }
    
    public void setReplies(List<CommentDTO> replies) {
        this.replies = replies;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "CommentDTO{" +
                "id=" + id +
                ", contentType='" + contentType + '\'' +
                ", contentId=" + contentId +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", userAvatar='" + userAvatar + '\'' +
                ", content='" + content + '\'' +
                ", parentId=" + parentId +
                ", status=" + status +
                ", likeCount=" + likeCount +
                ", isLiked=" + isLiked +
                ", replies=" + replies +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
