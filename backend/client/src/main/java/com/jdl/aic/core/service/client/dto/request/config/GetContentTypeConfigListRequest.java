package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取内容类型配置列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetContentTypeConfigListRequest {
    
    /**
     * 内容类型编码（可选）
     */
    private String code;
    
    /**
     * 内容类型名称（可选，支持模糊查询）
     */
    private String name;
    
    /**
     * 是否为门户模块（可选）
     */
    private Boolean isPortalModule;
    
    /**
     * 是否启用（可选）
     */
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public GetContentTypeConfigListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param code 内容类型编码
     * @param name 内容类型名称
     * @param isPortalModule 是否为门户模块
     * @param isActive 是否启用
     */
    public GetContentTypeConfigListRequest(String code, String name, Boolean isPortalModule, Boolean isActive) {
        this.code = code;
        this.name = name;
        this.isPortalModule = isPortalModule;
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Boolean getIsPortalModule() {
        return isPortalModule;
    }
    
    public void setIsPortalModule(Boolean isPortalModule) {
        this.isPortalModule = isPortalModule;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "GetContentTypeConfigListRequest{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", isPortalModule=" + isPortalModule +
                ", isActive=" + isActive +
                '}';
    }
}
