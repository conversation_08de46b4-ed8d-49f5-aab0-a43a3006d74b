package com.jdl.aic.core.service.client.dto.request.learning;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 搜索学习资源请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchLearningResourcesRequest {
    
    /**
     * 搜索关键词
     */
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;
    
    /**
     * 分页请求参数
     */
    @NotNull(message = "分页请求参数不能为空")
    @Valid
    private PageRequest pageRequest;
    
    /**
     * 搜索过滤条件
     */
    private Object filters;
    
    /**
     * 默认构造函数
     */
    public SearchLearningResourcesRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param keyword 搜索关键词
     * @param pageRequest 分页请求参数
     * @param filters 搜索过滤条件
     */
    public SearchLearningResourcesRequest(String keyword, PageRequest pageRequest, Object filters) {
        this.keyword = keyword;
        this.pageRequest = pageRequest;
        this.filters = filters;
    }
    
    // Getter and Setter methods
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public PageRequest getPageRequest() {
        return pageRequest;
    }
    
    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    public Object getFilters() {
        return filters;
    }
    
    public void setFilters(Object filters) {
        this.filters = filters;
    }
    
    @Override
    public String toString() {
        return "SearchLearningResourcesRequest{" +
                "keyword='" + keyword + '\'' +
                ", pageRequest=" + pageRequest +
                ", filters=" + filters +
                '}';
    }
}
