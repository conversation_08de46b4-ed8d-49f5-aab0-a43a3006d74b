package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * 文件下载DTO
 * 
 * <p>封装文件下载的相关信息，包括下载URL、有效期、下载权限等。
 * 支持临时下载链接和权限验证。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FileDownloadDTO {
    
    /**
     * 文件ID
     */
    @NotNull(message = "文件ID不能为空")
    private Long fileId;
    
    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;
    
    /**
     * 原始文件名
     */
    private String originalFileName;
    
    /**
     * 文件类型（MIME类型）
     */
    private String contentType;
    
    /**
     * 文件大小（字节）
     */
    private Long fileSize;
    
    /**
     * 下载URL
     */
    @NotBlank(message = "下载URL不能为空")
    private String downloadUrl;
    
    /**
     * 临时访问令牌
     */
    private String accessToken;
    
    /**
     * URL有效期
     */
    private LocalDateTime expiresAt;
    
    /**
     * 是否需要认证
     */
    private Boolean requireAuth;
    
    /**
     * 下载次数限制
     */
    private Integer downloadLimit;
    
    /**
     * 已下载次数
     */
    private Integer downloadCount;
    
    /**
     * 请求用户ID
     */
    private Long requestUserId;
    
    /**
     * 请求用户名
     */
    private String requestUserName;
    
    /**
     * 生成时间
     */
    private LocalDateTime generatedAt;
    
    /**
     * 默认构造函数
     */
    public FileDownloadDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getFileId() {
        return fileId;
    }
    
    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    public String getOriginalFileName() {
        return originalFileName;
    }
    
    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getFileSize() {
        return fileSize;
    }
    
    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }
    
    public String getDownloadUrl() {
        return downloadUrl;
    }
    
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }
    
    public String getAccessToken() {
        return accessToken;
    }
    
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public Boolean getRequireAuth() {
        return requireAuth;
    }
    
    public void setRequireAuth(Boolean requireAuth) {
        this.requireAuth = requireAuth;
    }
    
    public Integer getDownloadLimit() {
        return downloadLimit;
    }
    
    public void setDownloadLimit(Integer downloadLimit) {
        this.downloadLimit = downloadLimit;
    }
    
    public Integer getDownloadCount() {
        return downloadCount;
    }
    
    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }
    
    public Long getRequestUserId() {
        return requestUserId;
    }
    
    public void setRequestUserId(Long requestUserId) {
        this.requestUserId = requestUserId;
    }
    
    public String getRequestUserName() {
        return requestUserName;
    }
    
    public void setRequestUserName(String requestUserName) {
        this.requestUserName = requestUserName;
    }
    
    public LocalDateTime getGeneratedAt() {
        return generatedAt;
    }
    
    public void setGeneratedAt(LocalDateTime generatedAt) {
        this.generatedAt = generatedAt;
    }
    
    @Override
    public String toString() {
        return "FileDownloadDTO{" +
                "fileId=" + fileId +
                ", fileName='" + fileName + '\'' +
                ", originalFileName='" + originalFileName + '\'' +
                ", contentType='" + contentType + '\'' +
                ", fileSize=" + fileSize +
                ", downloadUrl='" + downloadUrl + '\'' +
                ", accessToken='" + accessToken + '\'' +
                ", expiresAt=" + expiresAt +
                ", requireAuth=" + requireAuth +
                ", downloadLimit=" + downloadLimit +
                ", downloadCount=" + downloadCount +
                ", requestUserId=" + requestUserId +
                ", requestUserName='" + requestUserName + '\'' +
                ", generatedAt=" + generatedAt +
                '}';
    }
}
