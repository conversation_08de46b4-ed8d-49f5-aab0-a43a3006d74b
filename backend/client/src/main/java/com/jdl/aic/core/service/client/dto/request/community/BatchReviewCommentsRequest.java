package com.jdl.aic.core.service.client.dto.request.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量审核评论请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchReviewCommentsRequest {
    
    /**
     * 评论ID列表
     */
    @NotEmpty(message = "评论ID列表不能为空")
    private List<Long> commentIds;
    
    /**
     * 审核状态（1:通过, 2:拒绝）
     */
    @NotNull(message = "审核状态不能为空")
    private Integer status;
    
    /**
     * 审核人ID
     */
    @NotNull(message = "审核人ID不能为空")
    private Long reviewerId;
    
    /**
     * 默认构造函数
     */
    public BatchReviewCommentsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param commentIds 评论ID列表
     * @param status 审核状态
     * @param reviewerId 审核人ID
     */
    public BatchReviewCommentsRequest(List<Long> commentIds, Integer status, Long reviewerId) {
        this.commentIds = commentIds;
        this.status = status;
        this.reviewerId = reviewerId;
    }
    
    // Getter and Setter methods
    
    public List<Long> getCommentIds() {
        return commentIds;
    }
    
    public void setCommentIds(List<Long> commentIds) {
        this.commentIds = commentIds;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Long getReviewerId() {
        return reviewerId;
    }
    
    public void setReviewerId(Long reviewerId) {
        this.reviewerId = reviewerId;
    }
    
    @Override
    public String toString() {
        return "BatchReviewCommentsRequest{" +
                "commentIds=" + commentIds +
                ", status=" + status +
                ", reviewerId=" + reviewerId +
                '}';
    }
}
