package com.jdl.aic.core.service.client.dto.request.solution;

import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 获取推荐解决方案请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetRecommendedSolutionsRequest {
    
    /**
     * 用户ID（必填，用于个性化推荐）
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 分页请求参数
     */
    @NotNull(message = "分页请求参数不能为空")
    @Valid
    private PageRequest pageRequest;
    
    /**
     * 默认构造函数
     */
    public GetRecommendedSolutionsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求参数
     */
    public GetRecommendedSolutionsRequest(Long userId, PageRequest pageRequest) {
        this.userId = userId;
        this.pageRequest = pageRequest;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public PageRequest getPageRequest() {
        return pageRequest;
    }
    
    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    @Override
    public String toString() {
        return "GetRecommendedSolutionsRequest{" +
                "userId=" + userId +
                ", pageRequest=" + pageRequest +
                '}';
    }
}
