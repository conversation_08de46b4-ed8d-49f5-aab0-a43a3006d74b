package com.jdl.aic.core.service.client.dto.request.system;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取系统配置列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetSystemConfigsRequest {
    
    /**
     * 配置分组过滤
     */
    private String configGroup;
    
    /**
     * 配置类型过滤
     */
    private String configType;
    
    /**
     * 启用状态过滤
     */
    private Boolean isActive;
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 默认构造函数
     */
    public GetSystemConfigsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param configGroup 配置分组过滤
     * @param configType 配置类型过滤
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     */
    public GetSystemConfigsRequest(String configGroup, String configType, Boolean isActive, String search) {
        this.configGroup = configGroup;
        this.configType = configType;
        this.isActive = isActive;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public String getConfigGroup() {
        return configGroup;
    }
    
    public void setConfigGroup(String configGroup) {
        this.configGroup = configGroup;
    }
    
    public String getConfigType() {
        return configType;
    }
    
    public void setConfigType(String configType) {
        this.configType = configType;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    @Override
    public String toString() {
        return "GetSystemConfigsRequest{" +
                "configGroup='" + configGroup + '\'' +
                ", configType='" + configType + '\'' +
                ", isActive=" + isActive +
                ", search='" + search + '\'' +
                '}';
    }
}
