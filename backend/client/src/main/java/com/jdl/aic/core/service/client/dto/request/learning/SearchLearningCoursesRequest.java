package com.jdl.aic.core.service.client.dto.request.learning;

import com.jdl.aic.core.service.client.common.PageRequest;

import java.util.List;

/**
 * 搜索学习课程请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class SearchLearningCoursesRequest {
    
    /**
     * 分页请求参数
     */
    private PageRequest pageRequest;
    
    /**
     * 搜索关键词
     */
    private String keyword;
    
    /**
     * 课程分类过滤
     */
    private List<String> categories;
    
    /**
     * 难度级别过滤
     */
    private List<String> difficultyLevels;
    
    /**
     * 标签过滤
     */
    private List<String> tags;
    
    /**
     * 是否官方课程过滤
     */
    private Boolean isOfficial;
    
    /**
     * 最小学习时长（小时）
     */
    private Integer minHours;
    
    /**
     * 最大学习时长（小时）
     */
    private Integer maxHours;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向（ASC, DESC）
     */
    private String sortDirection;

    // Getter and Setter methods
    public PageRequest getPageRequest() {
        return pageRequest;
    }

    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public List<String> getCategories() {
        return categories;
    }

    public void setCategories(List<String> categories) {
        this.categories = categories;
    }

    public List<String> getDifficultyLevels() {
        return difficultyLevels;
    }

    public void setDifficultyLevels(List<String> difficultyLevels) {
        this.difficultyLevels = difficultyLevels;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Boolean getIsOfficial() {
        return isOfficial;
    }

    public void setIsOfficial(Boolean isOfficial) {
        this.isOfficial = isOfficial;
    }

    public Integer getMinHours() {
        return minHours;
    }

    public void setMinHours(Integer minHours) {
        this.minHours = minHours;
    }

    public Integer getMaxHours() {
        return maxHours;
    }

    public void setMaxHours(Integer maxHours) {
        this.maxHours = maxHours;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection;
    }

    @Override
    public String toString() {
        return "SearchLearningCoursesRequest{" +
                "pageRequest=" + pageRequest +
                ", keyword='" + keyword + '\'' +
                ", categories=" + categories +
                ", difficultyLevels=" + difficultyLevels +
                ", tags=" + tags +
                ", isOfficial=" + isOfficial +
                ", minHours=" + minHours +
                ", maxHours=" + maxHours +
                ", sortBy='" + sortBy + '\'' +
                ", sortDirection='" + sortDirection + '\'' +
                '}';
    }
}
