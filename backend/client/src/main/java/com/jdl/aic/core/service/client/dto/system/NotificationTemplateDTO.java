package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 通知模板DTO
 * 
 * <p>封装通知模板的信息，包括模板内容、变量定义、适用场景等。
 * 支持多语言和多渠道的模板管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NotificationTemplateDTO {
    
    /**
     * 模板ID
     */
    private Long id;
    
    /**
     * 模板编码
     */
    @NotBlank(message = "模板编码不能为空")
    private String templateCode;
    
    /**
     * 模板名称
     */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;
    
    /**
     * 通知类型（system, comment, like, follow, mention）
     */
    @NotBlank(message = "通知类型不能为空")
    private String notificationType;
    
    /**
     * 发送渠道（web, email, sms, push）
     */
    @NotBlank(message = "发送渠道不能为空")
    private String channel;
    
    /**
     * 语言代码（zh-CN, en-US等）
     */
    private String languageCode;
    
    /**
     * 标题模板
     */
    @NotBlank(message = "标题模板不能为空")
    private String titleTemplate;
    
    /**
     * 内容模板
     */
    @NotBlank(message = "内容模板不能为空")
    private String contentTemplate;
    
    /**
     * 变量定义（JSON格式，定义模板中可用的变量）
     */
    private Map<String, Object> variableDefinitions;
    
    /**
     * 模板描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 优先级（数字越大优先级越高）
     */
    private Integer priority;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public NotificationTemplateDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTemplateCode() {
        return templateCode;
    }
    
    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }
    
    public String getTemplateName() {
        return templateName;
    }
    
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
    
    public String getNotificationType() {
        return notificationType;
    }
    
    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }
    
    public String getChannel() {
        return channel;
    }
    
    public void setChannel(String channel) {
        this.channel = channel;
    }
    
    public String getLanguageCode() {
        return languageCode;
    }
    
    public void setLanguageCode(String languageCode) {
        this.languageCode = languageCode;
    }
    
    public String getTitleTemplate() {
        return titleTemplate;
    }
    
    public void setTitleTemplate(String titleTemplate) {
        this.titleTemplate = titleTemplate;
    }
    
    public String getContentTemplate() {
        return contentTemplate;
    }
    
    public void setContentTemplate(String contentTemplate) {
        this.contentTemplate = contentTemplate;
    }
    
    public Map<String, Object> getVariableDefinitions() {
        return variableDefinitions;
    }
    
    public void setVariableDefinitions(Map<String, Object> variableDefinitions) {
        this.variableDefinitions = variableDefinitions;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public String toString() {
        return "NotificationTemplateDTO{" +
                "id=" + id +
                ", templateCode='" + templateCode + '\'' +
                ", templateName='" + templateName + '\'' +
                ", notificationType='" + notificationType + '\'' +
                ", channel='" + channel + '\'' +
                ", languageCode='" + languageCode + '\'' +
                ", titleTemplate='" + titleTemplate + '\'' +
                ", contentTemplate='" + contentTemplate + '\'' +
                ", variableDefinitions=" + variableDefinitions +
                ", description='" + description + '\'' +
                ", isActive=" + isActive +
                ", priority=" + priority +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy=" + createdBy +
                ", updatedBy=" + updatedBy +
                '}';
    }
}
