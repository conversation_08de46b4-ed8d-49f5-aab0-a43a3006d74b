package com.jdl.aic.core.service.client.dto.learning;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学习路径DTO
 * 
 * <p>封装学习路径的完整信息，包括路径描述、包含的学习资源、
 * 预估学习时间等。学习路径是一系列有序的学习资源组合。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LearningPathDTO {
    
    /**
     * 学习路径ID
     */
    private Long id;
    
    /**
     * 路径标题
     */
    @NotBlank(message = "路径标题不能为空")
    private String title;
    
    /**
     * 路径描述
     */
    private String description;
    
    /**
     * 路径分类
     */
    private String category;
    
    /**
     * 难度级别（beginner:初级, intermediate:中级, advanced:高级）
     */
    private String difficulty;
    
    /**
     * 预估学习时长（小时）
     */
    private Integer estimatedHours;
    
    /**
     * 路径包含的学习资源
     */
    private List<LearningPathResourceDTO> resources;
    
    /**
     * 注册学习人数
     */
    private Integer enrollmentCount;
    
    /**
     * 路径评分
     */
    private Double rating;
    
    /**
     * 封面图片URL
     */
    private String coverImageUrl;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public LearningPathDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param title 路径标题
     * @param category 路径分类
     * @param difficulty 难度级别
     */
    public LearningPathDTO(String title, String category, String difficulty) {
        this.title = title;
        this.category = category;
        this.difficulty = difficulty;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getDifficulty() {
        return difficulty;
    }
    
    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }
    
    public Integer getEstimatedHours() {
        return estimatedHours;
    }
    
    public void setEstimatedHours(Integer estimatedHours) {
        this.estimatedHours = estimatedHours;
    }
    
    public List<LearningPathResourceDTO> getResources() {
        return resources;
    }
    
    public void setResources(List<LearningPathResourceDTO> resources) {
        this.resources = resources;
    }
    
    public Integer getEnrollmentCount() {
        return enrollmentCount;
    }
    
    public void setEnrollmentCount(Integer enrollmentCount) {
        this.enrollmentCount = enrollmentCount;
    }
    
    public Double getRating() {
        return rating;
    }
    
    public void setRating(Double rating) {
        this.rating = rating;
    }
    
    public String getCoverImageUrl() {
        return coverImageUrl;
    }
    
    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public String toString() {
        return "LearningPathDTO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", category='" + category + '\'' +
                ", difficulty='" + difficulty + '\'' +
                ", estimatedHours=" + estimatedHours +
                ", resources=" + resources +
                ", enrollmentCount=" + enrollmentCount +
                ", rating=" + rating +
                ", coverImageUrl='" + coverImageUrl + '\'' +
                ", isActive=" + isActive +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy=" + createdBy +
                ", updatedBy=" + updatedBy +
                '}';
    }
}
