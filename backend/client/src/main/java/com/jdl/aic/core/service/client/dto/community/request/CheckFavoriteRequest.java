package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 检查收藏状态请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class CheckFavoriteRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    public CheckFavoriteRequest() {
    }

    public CheckFavoriteRequest(Long userId, Integer contentType, Long contentId) {
        this.userId = userId;
        this.contentType = contentType;
        this.contentId = contentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    @Override
    public String toString() {
        return "CheckFavoriteRequest{" +
                "userId=" + userId +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                '}';
    }
}
