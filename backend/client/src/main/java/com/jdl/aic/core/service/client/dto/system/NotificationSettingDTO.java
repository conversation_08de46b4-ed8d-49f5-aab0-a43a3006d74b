package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * 通知设置DTO
 * 
 * <p>封装用户通知偏好设置，包括通知类型开关、发送渠道配置等。
 * 支持个性化的通知管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NotificationSettingDTO {
    
    /**
     * 设置ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 通知类型（system, comment, like, follow, mention）
     */
    @NotBlank(message = "通知类型不能为空")
    private String notificationType;
    
    /**
     * 是否启用站内信
     */
    private Boolean webEnabled;
    
    /**
     * 是否启用邮件通知
     */
    private Boolean emailEnabled;
    
    /**
     * 是否启用短信通知
     */
    private Boolean smsEnabled;
    
    /**
     * 是否启用推送通知
     */
    private Boolean pushEnabled;
    
    /**
     * 免打扰开始时间（HH:mm格式）
     */
    private String quietHoursStart;
    
    /**
     * 免打扰结束时间（HH:mm格式）
     */
    private String quietHoursEnd;
    
    /**
     * 是否启用免打扰模式
     */
    private Boolean quietModeEnabled;
    
    /**
     * 通知频率（immediate:立即, hourly:每小时, daily:每日, weekly:每周）
     */
    private String frequency;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 默认构造函数
     */
    public NotificationSettingDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getNotificationType() {
        return notificationType;
    }
    
    public void setNotificationType(String notificationType) {
        this.notificationType = notificationType;
    }
    
    public Boolean getWebEnabled() {
        return webEnabled;
    }
    
    public void setWebEnabled(Boolean webEnabled) {
        this.webEnabled = webEnabled;
    }
    
    public Boolean getEmailEnabled() {
        return emailEnabled;
    }
    
    public void setEmailEnabled(Boolean emailEnabled) {
        this.emailEnabled = emailEnabled;
    }
    
    public Boolean getSmsEnabled() {
        return smsEnabled;
    }
    
    public void setSmsEnabled(Boolean smsEnabled) {
        this.smsEnabled = smsEnabled;
    }
    
    public Boolean getPushEnabled() {
        return pushEnabled;
    }
    
    public void setPushEnabled(Boolean pushEnabled) {
        this.pushEnabled = pushEnabled;
    }
    
    public String getQuietHoursStart() {
        return quietHoursStart;
    }
    
    public void setQuietHoursStart(String quietHoursStart) {
        this.quietHoursStart = quietHoursStart;
    }
    
    public String getQuietHoursEnd() {
        return quietHoursEnd;
    }
    
    public void setQuietHoursEnd(String quietHoursEnd) {
        this.quietHoursEnd = quietHoursEnd;
    }
    
    public Boolean getQuietModeEnabled() {
        return quietModeEnabled;
    }
    
    public void setQuietModeEnabled(Boolean quietModeEnabled) {
        this.quietModeEnabled = quietModeEnabled;
    }
    
    public String getFrequency() {
        return frequency;
    }
    
    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    @Override
    public String toString() {
        return "NotificationSettingDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", notificationType='" + notificationType + '\'' +
                ", webEnabled=" + webEnabled +
                ", emailEnabled=" + emailEnabled +
                ", smsEnabled=" + smsEnabled +
                ", pushEnabled=" + pushEnabled +
                ", quietHoursStart='" + quietHoursStart + '\'' +
                ", quietHoursEnd='" + quietHoursEnd + '\'' +
                ", quietModeEnabled=" + quietModeEnabled +
                ", frequency='" + frequency + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
