package com.jdl.aic.core.service.client.dto.request.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 为用户分配角色请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AssignRolesToUserRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 角色ID列表
     */
    @NotEmpty(message = "角色ID列表不能为空")
    private List<Long> roleIds;
    
    /**
     * 默认构造函数
     */
    public AssignRolesToUserRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    public AssignRolesToUserRequest(Long userId, List<Long> roleIds) {
        this.userId = userId;
        this.roleIds = roleIds;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public List<Long> getRoleIds() {
        return roleIds;
    }
    
    public void setRoleIds(List<Long> roleIds) {
        this.roleIds = roleIds;
    }
    
    @Override
    public String toString() {
        return "AssignRolesToUserRequest{" +
                "userId=" + userId +
                ", roleIds=" + roleIds +
                '}';
    }
}
