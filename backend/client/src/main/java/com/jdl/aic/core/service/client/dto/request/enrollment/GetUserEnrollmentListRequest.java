package com.jdl.aic.core.service.client.dto.request.enrollment;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取用户报名列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetUserEnrollmentListRequest {
    
    /**
     * 用户ID过滤
     */
    private Long userId;
    
    /**
     * 课程ID过滤
     */
    private Long courseId;
    
    /**
     * 报名状态过滤（ENROLLED:已报名, IN_PROGRESS:学习中, COMPLETED:已完成, DROPPED:已退出）
     */
    private String enrollmentStatus;
    
    /**
     * 报名来源过滤（WEB:网页, MOBILE:移动端, API:接口）
     */
    private String enrollmentSource;
    
    /**
     * 搜索关键词（课程名称）
     */
    private String search;
    
    /**
     * 开始时间过滤
     */
    private String startDate;
    
    /**
     * 结束时间过滤
     */
    private String endDate;
    
    /**
     * 默认构造函数
     */
    public GetUserEnrollmentListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID过滤
     * @param enrollmentStatus 报名状态过滤
     */
    public GetUserEnrollmentListRequest(Long userId, String enrollmentStatus) {
        this.userId = userId;
        this.enrollmentStatus = enrollmentStatus;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getCourseId() {
        return courseId;
    }
    
    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }
    
    public String getEnrollmentStatus() {
        return enrollmentStatus;
    }
    
    public void setEnrollmentStatus(String enrollmentStatus) {
        this.enrollmentStatus = enrollmentStatus;
    }
    
    public String getEnrollmentSource() {
        return enrollmentSource;
    }
    
    public void setEnrollmentSource(String enrollmentSource) {
        this.enrollmentSource = enrollmentSource;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    public String getStartDate() {
        return startDate;
    }
    
    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }
    
    public String getEndDate() {
        return endDate;
    }
    
    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }
    
    @Override
    public String toString() {
        return "GetUserEnrollmentListRequest{" +
                "userId=" + userId +
                ", courseId=" + courseId +
                ", enrollmentStatus='" + enrollmentStatus + '\'' +
                ", enrollmentSource='" + enrollmentSource + '\'' +
                ", search='" + search + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                '}';
    }
}
