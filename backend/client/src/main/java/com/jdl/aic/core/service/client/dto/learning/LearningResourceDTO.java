package com.jdl.aic.core.service.client.dto.learning;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import jakarta.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 学习资源DTO
 * 
 * <p>封装学习资源的完整信息，包括资源类型、难度级别、学习目标等。
 * 支持多种类型的学习资源：课程、视频、文档、项目、工具、学习路径等。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LearningResourceDTO {
    
    /**
     * 学习资源ID
     */
    private Long id;
    
    /**
     * 资源标题
     */
    @NotBlank(message = "资源标题不能为空")
    private String title;
    
    /**
     * 资源描述
     */
    private String description;

    /**
     * 学习资源详细内容
     */
    private String content;

    /**
     * 资源类型（course:课程, video:视频, document:文档, project:项目, tool:工具, path:学习路径）
     */
    private String resourceType;
    
    /**
     * 难度级别（beginner:初级, intermediate:中级, advanced:高级）
     */
    private String difficulty;
    
    /**
     * 语言
     */
    private String language;
    
    /**
     * 资源来源URL
     */
    private String sourceUrl;
    
    /**
     * 缩略图URL
     */
    private String thumbnailUrl;
    
    /**
     * 学习时长（分钟）
     */
    private Integer duration;
    
    /**
     * 评分
     */
    private Double rating;
    
    /**
     * 评价数量
     */
    private Integer reviewCount;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 前置要求
     */
    private List<String> prerequisites;

    /**
     * 学习目标
     */
    private List<String> learningObjectives;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 内容配置JSON
     */
    private Map<String, Object> contentConfig;

    /**
     * 嵌入配置JSON
     */
    private Map<String, Object> embedConfig;

    /**
     * 访问配置JSON
     */
    private Map<String, Object> accessConfig;

    /**
     * 媒体元数据JSON
     */
    private Map<String, Object> mediaMetadata;
    
    /**
     * 元数据JSON（扩展信息）
     */
    private Map<String, Object> metadataJson;
    
    /**
     * 是否激活
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;

    /**
     * 关联的分类
     */
    private List<ContentCategoryRelationDTO> categories;
    
    /**
     * 默认构造函数
     */
    public LearningResourceDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param title 资源标题
     * @param resourceType 资源类型
     * @param difficulty 难度级别
     */
    public LearningResourceDTO(String title, String resourceType, String difficulty) {
        this.title = title;
        this.resourceType = resourceType;
        this.difficulty = difficulty;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getResourceType() {
        return resourceType;
    }
    
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }
    
    public String getDifficulty() {
        return difficulty;
    }
    
    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }
    
    public String getLanguage() {
        return language;
    }
    
    public void setLanguage(String language) {
        this.language = language;
    }
    
    public String getSourceUrl() {
        return sourceUrl;
    }
    
    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public Integer getDuration() {
        return duration;
    }
    
    public void setDuration(Integer duration) {
        this.duration = duration;
    }
    
    public Double getRating() {
        return rating;
    }
    
    public void setRating(Double rating) {
        this.rating = rating;
    }
    
    public Integer getReviewCount() {
        return reviewCount;
    }
    
    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public BigDecimal getCompletionRate() {
        return completionRate;
    }

    public void setCompletionRate(BigDecimal completionRate) {
        this.completionRate = completionRate;
    }

    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public List<String> getPrerequisites() {
        return prerequisites;
    }
    
    public void setPrerequisites(List<String> prerequisites) {
        this.prerequisites = prerequisites;
    }
    
    public List<String> getLearningObjectives() {
        return learningObjectives;
    }
    
    public void setLearningObjectives(List<String> learningObjectives) {
        this.learningObjectives = learningObjectives;
    }
    
    public Map<String, Object> getMetadataJson() {
        return metadataJson;
    }
    
    public void setMetadataJson(Map<String, Object> metadataJson) {
        this.metadataJson = metadataJson;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Map<String, Object> getContentConfig() {
        return contentConfig;
    }

    public void setContentConfig(Map<String, Object> contentConfig) {
        this.contentConfig = contentConfig;
    }

    public Map<String, Object> getEmbedConfig() {
        return embedConfig;
    }

    public void setEmbedConfig(Map<String, Object> embedConfig) {
        this.embedConfig = embedConfig;
    }

    public Map<String, Object> getAccessConfig() {
        return accessConfig;
    }

    public void setAccessConfig(Map<String, Object> accessConfig) {
        this.accessConfig = accessConfig;
    }

    public Map<String, Object> getMediaMetadata() {
        return mediaMetadata;
    }

    public void setMediaMetadata(Map<String, Object> mediaMetadata) {
        this.mediaMetadata = mediaMetadata;
    }

    public List<ContentCategoryRelationDTO> getCategories() {
        return categories;
    }

    public void setCategories(List<ContentCategoryRelationDTO> categories) {
        this.categories = categories;
    }

    @Override
    public String toString() {
        return "LearningResourceDTO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", content='" + content + '\'' +
                ", resourceType='" + resourceType + '\'' +
                ", difficulty='" + difficulty + '\'' +
                ", language='" + language + '\'' +
                ", sourceUrl='" + sourceUrl + '\'' +
                ", thumbnailUrl='" + thumbnailUrl + '\'' +
                ", duration=" + duration +
                ", rating=" + rating +
                ", reviewCount=" + reviewCount +
                ", completionRate=" + completionRate +
                ", tags=" + tags +
                ", prerequisites=" + prerequisites +
                ", learningObjectives=" + learningObjectives +
                ", contentType='" + contentType + '\'' +
                ", contentConfig=" + contentConfig +
                ", embedConfig=" + embedConfig +
                ", accessConfig=" + accessConfig +
                ", mediaMetadata=" + mediaMetadata +
                ", metadataJson=" + metadataJson +
                ", isActive=" + isActive +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy=" + createdBy +
                ", updatedBy=" + updatedBy +
                '}';
    }
}
