package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 根据ID获取评论请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetCommentByIdRequest {

    /**
     * 评论ID
     */
    @NotNull(message = "评论ID不能为空")
    private Long commentId;

    public GetCommentByIdRequest() {
    }

    public GetCommentByIdRequest(Long commentId) {
        this.commentId = commentId;
    }

    public Long getCommentId() {
        return commentId;
    }

    public void setCommentId(Long commentId) {
        this.commentId = commentId;
    }

    @Override
    public String toString() {
        return "GetCommentByIdRequest{" +
                "commentId=" + commentId +
                '}';
    }
}
