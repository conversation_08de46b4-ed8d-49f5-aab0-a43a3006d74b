package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 根据内容类型获取配置请求（通用）
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetConfigsByContentTypeRequest {
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 是否启用（可选）
     */
    private Boolean isEnabled;
    
    /**
     * 默认构造函数
     */
    public GetConfigsByContentTypeRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param isEnabled 是否启用
     */
    public GetConfigsByContentTypeRequest(String contentType, Boolean isEnabled) {
        this.contentType = contentType;
        this.isEnabled = isEnabled;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    @Override
    public String toString() {
        return "GetConfigsByContentTypeRequest{" +
                "contentType='" + contentType + '\'' +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
