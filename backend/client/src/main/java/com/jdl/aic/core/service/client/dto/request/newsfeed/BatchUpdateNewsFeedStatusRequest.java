package com.jdl.aic.core.service.client.dto.request.newsfeed;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量更新资讯状态请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchUpdateNewsFeedStatusRequest {
    
    /**
     * 资讯ID列表
     */
    @NotEmpty(message = "资讯ID列表不能为空")
    private List<Long> ids;
    
    /**
     * 状态（0:待审核, 1:已发布, 2:已下线）
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
    
    /**
     * 默认构造函数
     */
    public BatchUpdateNewsFeedStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param ids 资讯ID列表
     * @param status 状态
     */
    public BatchUpdateNewsFeedStatusRequest(List<Long> ids, Integer status) {
        this.ids = ids;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public List<Long> getIds() {
        return ids;
    }
    
    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "BatchUpdateNewsFeedStatusRequest{" +
                "ids=" + ids +
                ", status=" + status +
                '}';
    }
}
