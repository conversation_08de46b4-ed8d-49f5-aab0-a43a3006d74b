package com.jdl.aic.core.service.client.dto.team;

/**
 * 团队统计信息数据传输对象
 */
public class TeamStatisticsDTO {
    
    /**
     * 团队ID
     */
    private Long teamId;
    
    /**
     * 团队人数
     */
    private Integer memberCount;
    
    /**
     * 团队文章总数
     */
    private Integer articleCount;

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Integer getMemberCount() {
        return memberCount;
    }

    public void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
    }

    public Integer getArticleCount() {
        return articleCount;
    }

    public void setArticleCount(Integer articleCount) {
        this.articleCount = articleCount;
    }

    @Override
    public String toString() {
        return "TeamStatisticsDTO{" +
                "teamId=" + teamId +
                ", memberCount=" + memberCount +
                ", articleCount=" + articleCount +
                '}';
    }
}