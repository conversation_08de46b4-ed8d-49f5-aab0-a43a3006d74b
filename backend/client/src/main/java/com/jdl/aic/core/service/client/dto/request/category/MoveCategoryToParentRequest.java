package com.jdl.aic.core.service.client.dto.request.category;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 移动分类到新的父分类下请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MoveCategoryToParentRequest {
    
    /**
     * 分类ID
     */
    @NotNull(message = "分类ID不能为空")
    private Long id;
    
    /**
     * 新父分类ID
     */
    private Long newParentId;
    
    /**
     * 默认构造函数
     */
    public MoveCategoryToParentRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 分类ID
     * @param newParentId 新父分类ID
     */
    public MoveCategoryToParentRequest(Long id, Long newParentId) {
        this.id = id;
        this.newParentId = newParentId;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getNewParentId() {
        return newParentId;
    }
    
    public void setNewParentId(Long newParentId) {
        this.newParentId = newParentId;
    }
    
    @Override
    public String toString() {
        return "MoveCategoryToParentRequest{" +
                "id=" + id +
                ", newParentId=" + newParentId +
                '}';
    }
}
