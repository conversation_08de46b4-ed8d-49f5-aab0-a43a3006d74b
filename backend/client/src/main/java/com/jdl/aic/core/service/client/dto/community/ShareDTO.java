package com.jdl.aic.core.service.client.dto.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 分享DTO
 * 
 * <p>封装用户分享内容的信息，支持多种分享方式：链接分享、微信分享、
 * 邮件分享、内部分享等。记录分享行为和点击统计。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ShareDTO {
    
    /**
     * 分享ID
     */
    private Long id;
    
    /**
     * 分享用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 用户名
     */
    private String userName;
    
    /**
     * 内容类型（knowledge:知识, solution:解决方案, learning_resource:学习资源）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 分享类型（link:链接, wechat:微信, email:邮件, internal:内部）
     */
    @NotBlank(message = "分享类型不能为空")
    private String shareType;
    
    /**
     * 分享备注
     */
    private String shareNote;
    
    /**
     * 点击次数
     */
    private Integer clickCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 默认构造函数
     */
    public ShareDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param shareType 分享类型
     */
    public ShareDTO(Long userId, String contentType, Long contentId, String shareType) {
        this.userId = userId;
        this.contentType = contentType;
        this.contentId = contentId;
        this.shareType = shareType;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getUserName() {
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public String getShareType() {
        return shareType;
    }
    
    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
    
    public String getShareNote() {
        return shareNote;
    }
    
    public void setShareNote(String shareNote) {
        this.shareNote = shareNote;
    }
    
    public Integer getClickCount() {
        return clickCount;
    }
    
    public void setClickCount(Integer clickCount) {
        this.clickCount = clickCount;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    @Override
    public String toString() {
        return "ShareDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", contentType='" + contentType + '\'' +
                ", contentId=" + contentId +
                ", shareType='" + shareType + '\'' +
                ", shareNote='" + shareNote + '\'' +
                ", clickCount=" + clickCount +
                ", createdAt=" + createdAt +
                '}';
    }
}
