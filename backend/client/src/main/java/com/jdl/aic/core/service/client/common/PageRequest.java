package com.jdl.aic.core.service.client.common;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

/**
 * 分页查询请求基类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class PageRequest {
    
    /**
     * 页码（从0开始）
     */
    @Min(value = 1, message = "页码不能小于0")
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer size = 10;
    
    /**
     * 排序字段和方向，格式：field,direction
     * 例如：created_at,desc
     */
    private String sort = "created_at,desc";
    
    /**
     * 默认构造函数
     */
    public PageRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param page 页码
     * @param size 每页大小
     */
    public PageRequest(Integer page, Integer size) {
        this.page = page;
        this.size = size;
    }
    
    /**
     * 构造函数
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序
     */
    public PageRequest(Integer page, Integer size, String sort) {
        this.page = page;
        this.size = size;
        this.sort = sort;
    }
    
    /**
     * 创建分页请求
     * 
     * @param page 页码
     * @param size 每页大小
     * @return 分页请求
     */
    public static PageRequest of(int page, int size) {
        return new PageRequest(page, size);
    }
    
    /**
     * 创建分页请求
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序
     * @return 分页请求
     */
    public static PageRequest of(int page, int size, String sort) {
        return new PageRequest(page, size, sort);
    }
    
    /**
     * 获取偏移量
     * 
     * @return 偏移量
     */
    public long getOffset() {
        return (long) page * size;
    }
    
    // Getter and Setter methods
    
    public Integer getPage() {
        return page;
    }
    
    public void setPage(Integer page) {
        this.page = page;
    }
    
    public Integer getSize() {
        return size;
    }
    
    public void setSize(Integer size) {
        this.size = size;
    }
    
    public String getSort() {
        return sort;
    }
    
    public void setSort(String sort) {
        this.sort = sort;
    }
    
    @Override
    public String toString() {
        return "PageRequest{" +
                "page=" + page +
                ", size=" + size +
                ", sort='" + sort + '\'' +
                '}';
    }
}
