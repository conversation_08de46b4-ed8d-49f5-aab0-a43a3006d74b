package com.jdl.aic.core.service.client.dto.request.team;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 移动团队到新父团队请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MoveTeamToParentRequest {
    
    /**
     * 团队ID
     */
    @NotNull(message = "团队ID不能为空")
    private Long teamId;
    
    /**
     * 新父团队ID（null表示移动到根级别）
     */
    private Long newParentId;
    
    /**
     * 默认构造函数
     */
    public MoveTeamToParentRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param teamId 团队ID
     * @param newParentId 新父团队ID
     */
    public MoveTeamToParentRequest(Long teamId, Long newParentId) {
        this.teamId = teamId;
        this.newParentId = newParentId;
    }
    
    // Getter and Setter methods
    
    public Long getTeamId() {
        return teamId;
    }
    
    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }
    
    public Long getNewParentId() {
        return newParentId;
    }
    
    public void setNewParentId(Long newParentId) {
        this.newParentId = newParentId;
    }
    
    @Override
    public String toString() {
        return "MoveTeamToParentRequest{" +
                "teamId=" + teamId +
                ", newParentId=" + newParentId +
                '}';
    }
}
