package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 切换点赞状态请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class ToggleLikeRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 关联知识类型ID（可选）
     */
    private Long relatedKnowledgeTypeId;

    public ToggleLikeRequest() {
    }

    public ToggleLikeRequest(Long userId, Integer contentType, Long contentId, Long relatedKnowledgeTypeId) {
        this.userId = userId;
        this.contentType = contentType;
        this.contentId = contentId;
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public Long getRelatedKnowledgeTypeId() {
        return relatedKnowledgeTypeId;
    }

    public void setRelatedKnowledgeTypeId(Long relatedKnowledgeTypeId) {
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
    }

    @Override
    public String toString() {
        return "ToggleLikeRequest{" +
                "userId=" + userId +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                ", relatedKnowledgeTypeId=" + relatedKnowledgeTypeId +
                '}';
    }
}
