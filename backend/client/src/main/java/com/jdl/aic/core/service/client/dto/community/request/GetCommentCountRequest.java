package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 获取评论数量请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetCommentCountRequest {

    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    public GetCommentCountRequest() {
    }

    public GetCommentCountRequest(Integer contentType, Long contentId) {
        this.contentType = contentType;
        this.contentId = contentId;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    @Override
    public String toString() {
        return "GetCommentCountRequest{" +
                "contentType=" + contentType +
                ", contentId=" + contentId +
                '}';
    }
}
