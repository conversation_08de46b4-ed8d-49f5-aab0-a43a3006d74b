package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取分享选项配置列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetShareOptionConfigListRequest {
    
    /**
     * 内容类型（可选）
     */
    private String contentType;
    
    /**
     * 分享类型（可选）
     */
    private String shareType;
    
    /**
     * 是否启用（可选）
     */
    private Boolean isEnabled;
    
    /**
     * 默认构造函数
     */
    public GetShareOptionConfigListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param shareType 分享类型
     * @param isEnabled 是否启用
     */
    public GetShareOptionConfigListRequest(String contentType, String shareType, Boolean isEnabled) {
        this.contentType = contentType;
        this.shareType = shareType;
        this.isEnabled = isEnabled;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getShareType() {
        return shareType;
    }
    
    public void setShareType(String shareType) {
        this.shareType = shareType;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    @Override
    public String toString() {
        return "GetShareOptionConfigListRequest{" +
                "contentType='" + contentType + '\'' +
                ", shareType='" + shareType + '\'' +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
