package com.jdl.aic.core.service.client.dto.category;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 标签DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TagDTO {
    
    /**
     * 标签ID
     */
    private Long id;
    
    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    private String name;
    
    /**
     * 内容类别（knowledge, solution, news_feed, learning_resource, general）
     */
    @NotNull(message = "内容类别不能为空")
    private String contentCategory;

    /**
     * 细分类型ID，对应knowledge_type.id，用于知识类型专属标签
     */
    private Long subTypeId;

    /**
     * 细分类型名称（查询时关联获取）
     */
    private String subTypeName;
    
    /**
     * 标签分组
     */
    private String tagCategory;
    
    /**
     * 标签描述
     */
    private String description;
    
    /**
     * 标签颜色（十六进制）
     */
    @Pattern(regexp = "^#[0-9A-Fa-f]{6}$", message = "标签颜色格式不正确")
    private String color;
    
    /**
     * 使用次数统计
     */
    private Integer usageCount;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public TagDTO() {
    }
    
    /**
     * 构造函数
     *
     * @param name 标签名称
     * @param contentCategory 内容类别
     */
    public TagDTO(String name, String contentCategory) {
        this.name = name;
        this.contentCategory = contentCategory;
    }

    /**
     * 支持细分类型的构造函数
     *
     * @param name 标签名称
     * @param contentCategory 内容类别
     * @param subTypeId 细分类型ID
     */
    public TagDTO(String name, String contentCategory, Long subTypeId) {
        this.name = name;
        this.contentCategory = contentCategory;
        this.subTypeId = subTypeId;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getContentCategory() {
        return contentCategory;
    }
    
    public void setContentCategory(String contentCategory) {
        this.contentCategory = contentCategory;
    }

    public Long getSubTypeId() {
        return subTypeId;
    }

    public void setSubTypeId(Long subTypeId) {
        this.subTypeId = subTypeId;
    }

    public String getSubTypeName() {
        return subTypeName;
    }

    public void setSubTypeName(String subTypeName) {
        this.subTypeName = subTypeName;
    }
    
    public String getTagCategory() {
        return tagCategory;
    }
    
    public void setTagCategory(String tagCategory) {
        this.tagCategory = tagCategory;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public Integer getUsageCount() {
        return usageCount;
    }
    
    public void setUsageCount(Integer usageCount) {
        this.usageCount = usageCount;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public String toString() {
        return "TagDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", contentCategory='" + contentCategory + '\'' +
                ", subTypeId=" + subTypeId +
                ", subTypeName='" + subTypeName + '\'' +
                ", tagCategory='" + tagCategory + '\'' +
                ", description='" + description + '\'' +
                ", color='" + color + '\'' +
                ", usageCount=" + usageCount +
                ", isActive=" + isActive +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TagDTO tagDTO = (TagDTO) o;

        if (id != null ? !id.equals(tagDTO.id) : tagDTO.id != null) return false;
        if (name != null ? !name.equals(tagDTO.name) : tagDTO.name != null) return false;
        if (contentCategory != null ? !contentCategory.equals(tagDTO.contentCategory) : tagDTO.contentCategory != null) return false;
        if (subTypeId != null ? !subTypeId.equals(tagDTO.subTypeId) : tagDTO.subTypeId != null) return false;
        if (subTypeName != null ? !subTypeName.equals(tagDTO.subTypeName) : tagDTO.subTypeName != null) return false;
        if (tagCategory != null ? !tagCategory.equals(tagDTO.tagCategory) : tagDTO.tagCategory != null) return false;
        if (description != null ? !description.equals(tagDTO.description) : tagDTO.description != null) return false;
        if (color != null ? !color.equals(tagDTO.color) : tagDTO.color != null) return false;
        if (usageCount != null ? !usageCount.equals(tagDTO.usageCount) : tagDTO.usageCount != null) return false;
        return isActive != null ? isActive.equals(tagDTO.isActive) : tagDTO.isActive == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (contentCategory != null ? contentCategory.hashCode() : 0);
        result = 31 * result + (subTypeId != null ? subTypeId.hashCode() : 0);
        result = 31 * result + (subTypeName != null ? subTypeName.hashCode() : 0);
        result = 31 * result + (tagCategory != null ? tagCategory.hashCode() : 0);
        result = 31 * result + (description != null ? description.hashCode() : 0);
        result = 31 * result + (color != null ? color.hashCode() : 0);
        result = 31 * result + (usageCount != null ? usageCount.hashCode() : 0);
        result = 31 * result + (isActive != null ? isActive.hashCode() : 0);
        return result;
    }
}
