package com.jdl.aic.core.service.client.dto.request.solution;

import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 搜索解决方案请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class SearchSolutionsRequest {
    
    /**
     * 搜索关键词（必填）
     */
    @NotBlank(message = "搜索关键词不能为空")
    private String keyword;
    
    /**
     * 分页请求参数
     */
    @NotNull(message = "分页请求参数不能为空")
    @Valid
    private PageRequest pageRequest;
    
    /**
     * 分类过滤
     */
    private String category;
    
    /**
     * 标签过滤
     */
    private List<String> tags;
    
    /**
     * 默认构造函数
     */
    public SearchSolutionsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param keyword 搜索关键词
     * @param pageRequest 分页请求参数
     */
    public SearchSolutionsRequest(String keyword, PageRequest pageRequest) {
        this.keyword = keyword;
        this.pageRequest = pageRequest;
    }
    
    /**
     * 构造函数
     * 
     * @param keyword 搜索关键词
     * @param pageRequest 分页请求参数
     * @param category 分类过滤
     * @param tags 标签过滤
     */
    public SearchSolutionsRequest(String keyword, PageRequest pageRequest, String category, List<String> tags) {
        this.keyword = keyword;
        this.pageRequest = pageRequest;
        this.category = category;
        this.tags = tags;
    }
    
    // Getter and Setter methods
    
    public String getKeyword() {
        return keyword;
    }
    
    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
    
    public PageRequest getPageRequest() {
        return pageRequest;
    }
    
    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    @Override
    public String toString() {
        return "SearchSolutionsRequest{" +
                "keyword='" + keyword + '\'' +
                ", pageRequest=" + pageRequest +
                ", category='" + category + '\'' +
                ", tags=" + tags +
                '}';
    }
}
