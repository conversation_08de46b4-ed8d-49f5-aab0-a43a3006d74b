package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 删除评论请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class DeleteCommentRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 评论ID
     */
    @NotNull(message = "评论ID不能为空")
    private Long commentId;

    public DeleteCommentRequest() {
    }

    public DeleteCommentRequest(Long userId, Long commentId) {
        this.userId = userId;
        this.commentId = commentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCommentId() {
        return commentId;
    }

    public void setCommentId(Long commentId) {
        this.commentId = commentId;
    }

    @Override
    public String toString() {
        return "DeleteCommentRequest{" +
                "userId=" + userId +
                ", commentId=" + commentId +
                '}';
    }
}
