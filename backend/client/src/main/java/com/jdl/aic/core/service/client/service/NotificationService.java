package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.NotificationDTO;
import com.jdl.aic.core.service.client.dto.system.NotificationSettingDTO;
import com.jdl.aic.core.service.client.dto.system.NotificationTemplateDTO;

import java.util.List;
import java.util.Map;

/**
 * 通知服务接口
 * 
 * <p>提供通知管理功能，包括：
 * <ul>
 *   <li>消息通知的发送和管理</li>
 *   <li>通知设置和偏好管理</li>
 *   <li>通知模板管理</li>
 *   <li>通知统计和分析</li>
 *   <li>多渠道通知支持</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface NotificationService {
    
    // ==================== 通知发送和管理 ====================
    
    /**
     * 发送通知
     * 
     * @param notification 通知信息
     * @return 发送结果
     */
    Result<NotificationDTO> sendNotification(NotificationDTO notification);
    
    /**
     * 批量发送通知
     * 
     * @param notifications 通知列表
     * @return 批量发送结果
     */
    Result<List<NotificationDTO>> batchSendNotifications(List<NotificationDTO> notifications);
    
    /**
     * 发送系统通知
     * 
     * @param title 通知标题
     * @param content 通知内容
     * @param recipientIds 接收用户ID列表
     * @param priority 优先级
     * @param channels 发送渠道列表
     * @return 发送结果
     */
    Result<List<NotificationDTO>> sendSystemNotification(
            String title,
            String content,
            List<Long> recipientIds,
            Integer priority,
            List<String> channels);
    
    /**
     * 发送模板通知
     * 
     * @param templateCode 模板编码
     * @param recipientId 接收用户ID
     * @param variables 模板变量
     * @param channel 发送渠道
     * @return 发送结果
     */
    Result<NotificationDTO> sendTemplateNotification(
            String templateCode,
            Long recipientId,
            Map<String, Object> variables,
            String channel);
    
    /**
     * 获取用户通知列表（分页）
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param status 状态过滤（0:未读, 1:已读, 2:已删除）
     * @param type 类型过滤
     * @param priority 优先级过滤
     * @return 用户通知列表
     */
    Result<PageResult<NotificationDTO>> getUserNotifications(
            Long userId,
            PageRequest pageRequest,
            Integer status,
            String type,
            Integer priority);
    
    /**
     * 根据ID获取通知详情
     * 
     * @param id 通知ID
     * @return 通知详情
     */
    Result<NotificationDTO> getNotificationById(Long id);
    
    /**
     * 标记通知为已读
     * 
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> markNotificationAsRead(Long notificationId, Long userId);
    
    /**
     * 批量标记通知为已读
     * 
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> batchMarkNotificationsAsRead(List<Long> notificationIds, Long userId);
    
    /**
     * 标记所有通知为已读
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> markAllNotificationsAsRead(Long userId);
    
    /**
     * 删除通知
     * 
     * @param notificationId 通知ID
     * @param userId 用户ID
     * @return 删除结果
     */
    Result<Void> deleteNotification(Long notificationId, Long userId);
    
    /**
     * 批量删除通知
     * 
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     * @return 删除结果
     */
    Result<Void> batchDeleteNotifications(List<Long> notificationIds, Long userId);
    
    // ==================== 通知设置管理 ====================
    
    /**
     * 获取用户通知设置
     * 
     * @param userId 用户ID
     * @return 通知设置列表
     */
    Result<List<NotificationSettingDTO>> getUserNotificationSettings(Long userId);
    
    /**
     * 更新用户通知设置
     * 
     * @param userId 用户ID
     * @param settings 通知设置列表
     * @return 更新结果
     */
    Result<List<NotificationSettingDTO>> updateUserNotificationSettings(
            Long userId,
            List<NotificationSettingDTO> settings);
    
    /**
     * 获取特定类型的通知设置
     * 
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 通知设置
     */
    Result<NotificationSettingDTO> getNotificationSetting(Long userId, String notificationType);
    
    /**
     * 更新特定类型的通知设置
     * 
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @param setting 通知设置
     * @return 更新结果
     */
    Result<NotificationSettingDTO> updateNotificationSetting(
            Long userId,
            String notificationType,
            NotificationSettingDTO setting);
    
    /**
     * 重置用户通知设置为默认值
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    Result<Void> resetNotificationSettingsToDefault(Long userId);
    
    // ==================== 通知模板管理 ====================
    
    /**
     * 获取通知模板列表（分页）
     * 
     * @param pageRequest 分页请求
     * @param notificationType 通知类型过滤
     * @param channel 渠道过滤
     * @param isActive 启用状态过滤
     * @param search 搜索关键词
     * @return 通知模板列表
     */
    Result<PageResult<NotificationTemplateDTO>> getNotificationTemplates(
            PageRequest pageRequest,
            String notificationType,
            String channel,
            Boolean isActive,
            String search);
    
    /**
     * 根据ID获取通知模板
     * 
     * @param id 模板ID
     * @return 通知模板
     */
    Result<NotificationTemplateDTO> getNotificationTemplateById(Long id);
    
    /**
     * 根据编码获取通知模板
     * 
     * @param templateCode 模板编码
     * @param channel 渠道
     * @param languageCode 语言代码
     * @return 通知模板
     */
    Result<NotificationTemplateDTO> getNotificationTemplateByCode(
            String templateCode,
            String channel,
            String languageCode);
    
    /**
     * 创建通知模板
     * 
     * @param template 通知模板
     * @return 创建结果
     */
    Result<NotificationTemplateDTO> createNotificationTemplate(NotificationTemplateDTO template);
    
    /**
     * 更新通知模板
     * 
     * @param id 模板ID
     * @param template 通知模板
     * @return 更新结果
     */
    Result<NotificationTemplateDTO> updateNotificationTemplate(Long id, NotificationTemplateDTO template);
    
    /**
     * 删除通知模板
     * 
     * @param id 模板ID
     * @return 删除结果
     */
    Result<Void> deleteNotificationTemplate(Long id);
    
    /**
     * 启用/禁用通知模板
     * 
     * @param id 模板ID
     * @param isActive 是否启用
     * @return 操作结果
     */
    Result<Void> toggleNotificationTemplateStatus(Long id, Boolean isActive);
    
    // ==================== 通知统计和分析 ====================
    
    /**
     * 获取用户未读通知数量
     * 
     * @param userId 用户ID
     * @return 未读通知数量
     */
    Result<Long> getUnreadNotificationCount(Long userId);
    
    /**
     * 获取用户通知统计
     * 
     * @param userId 用户ID
     * @param days 统计天数
     * @return 通知统计信息
     */
    Result<Object> getUserNotificationStats(Long userId, Integer days);
    
    /**
     * 获取系统通知统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param notificationType 通知类型过滤
     * @param channel 渠道过滤
     * @return 系统通知统计
     */
    Result<Object> getSystemNotificationStats(
            String startDate,
            String endDate,
            String notificationType,
            String channel);
    
    /**
     * 获取通知发送成功率统计
     * 
     * @param days 统计天数
     * @param channel 渠道过滤
     * @return 发送成功率统计
     */
    Result<Object> getNotificationDeliveryStats(Integer days, String channel);
    
    /**
     * 清理过期通知
     * 
     * @param days 保留天数
     * @param dryRun 是否为试运行
     * @return 清理结果
     */
    Result<Object> cleanupExpiredNotifications(Integer days, Boolean dryRun);
}
