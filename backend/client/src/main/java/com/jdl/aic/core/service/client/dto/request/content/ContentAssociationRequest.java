package com.jdl.aic.core.service.client.dto.request.content;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import jakarta.validation.constraints.NotNull;

/**
 * 内容关联请求基类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContentAssociationRequest {
    
    /**
     * 内容类型
     */
    @NotNull(message = "内容类型不能为空")
    private ContentType contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 默认构造函数
     */
    public ContentAssociationRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     */
    public ContentAssociationRequest(ContentType contentType, Long contentId) {
        this.contentType = contentType;
        this.contentId = contentId;
    }
    
    // Getter and Setter methods
    
    public ContentType getContentType() {
        return contentType;
    }
    
    public void setContentType(ContentType contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    @Override
    public String toString() {
        return "ContentAssociationRequest{" +
                "contentType=" + contentType +
                ", contentId=" + contentId +
                '}';
    }
}
