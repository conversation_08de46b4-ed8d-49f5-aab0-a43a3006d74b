package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 根据内容类型和功能类型获取配置请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetSocialFeatureConfigByContentAndFeatureRequest {
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 功能类型
     */
    private String featureType;
    
    /**
     * 默认构造函数
     */
    public GetSocialFeatureConfigByContentAndFeatureRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param featureType 功能类型
     */
    public GetSocialFeatureConfigByContentAndFeatureRequest(String contentType, String featureType) {
        this.contentType = contentType;
        this.featureType = featureType;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getFeatureType() {
        return featureType;
    }
    
    public void setFeatureType(String featureType) {
        this.featureType = featureType;
    }
    
    @Override
    public String toString() {
        return "GetSocialFeatureConfigByContentAndFeatureRequest{" +
                "contentType='" + contentType + '\'' +
                ", featureType='" + featureType + '\'' +
                '}';
    }
}
