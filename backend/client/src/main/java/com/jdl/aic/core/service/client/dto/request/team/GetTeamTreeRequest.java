package com.jdl.aic.core.service.client.dto.request.team;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取团队树形结构请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetTeamTreeRequest {
    
    /**
     * 启用状态过滤
     */
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public GetTeamTreeRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param isActive 启用状态过滤
     */
    public GetTeamTreeRequest(Boolean isActive) {
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "GetTeamTreeRequest{" +
                "isActive=" + isActive +
                '}';
    }
}
