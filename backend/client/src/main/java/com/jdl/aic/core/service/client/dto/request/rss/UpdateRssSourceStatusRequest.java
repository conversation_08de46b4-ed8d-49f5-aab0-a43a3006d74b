package com.jdl.aic.core.service.client.dto.request.rss;

import jakarta.validation.constraints.NotNull;

/**
 * 更新RSS源状态请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class UpdateRssSourceStatusRequest {
    
    /**
     * RSS源ID
     */
    @NotNull(message = "RSS源ID不能为空")
    private Long id;
    
    /**
     * 新状态（0:活跃, 1:暂停, 2:失败）
     */
    @NotNull(message = "状态不能为空")
    private Integer status;
    
    /**
     * 默认构造函数
     */
    public UpdateRssSourceStatusRequest() {
    }

    /**
     * 构造函数
     *
     * @param id RSS源ID
     * @param status 新状态
     */
    public UpdateRssSourceStatusRequest(Long id, Integer status) {
        this.id = id;
        this.status = status;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "UpdateRssSourceStatusRequest{" +
                "id=" + id +
                ", status=" + status +
                '}';
    }
}
