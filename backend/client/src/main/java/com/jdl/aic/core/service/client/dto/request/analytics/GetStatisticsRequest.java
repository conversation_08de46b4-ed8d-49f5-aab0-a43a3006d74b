package com.jdl.aic.core.service.client.dto.request.analytics;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 获取统计数据请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetStatisticsRequest {
    
    /**
     * 统计域（knowledge, user, content, category, tag）
     */
    @NotBlank(message = "统计域不能为空")
    private String domain;
    
    /**
     * 指标类型（count, trend, quality, behavior等）
     */
    @NotBlank(message = "指标类型不能为空")
    private String metricType;
    
    /**
     * 过滤条件
     */
    private Map<String, Object> filters;
    
    /**
     * 默认构造函数
     */
    public GetStatisticsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param domain 统计域
     * @param metricType 指标类型
     * @param filters 过滤条件
     */
    public GetStatisticsRequest(String domain, String metricType, Map<String, Object> filters) {
        this.domain = domain;
        this.metricType = metricType;
        this.filters = filters;
    }
    
    // Getter and Setter methods
    
    public String getDomain() {
        return domain;
    }
    
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    public String getMetricType() {
        return metricType;
    }
    
    public void setMetricType(String metricType) {
        this.metricType = metricType;
    }
    
    public Map<String, Object> getFilters() {
        return filters;
    }
    
    public void setFilters(Map<String, Object> filters) {
        this.filters = filters;
    }
    
    @Override
    public String toString() {
        return "GetStatisticsRequest{" +
                "domain='" + domain + '\'' +
                ", metricType='" + metricType + '\'' +
                ", filters=" + filters +
                '}';
    }
}
