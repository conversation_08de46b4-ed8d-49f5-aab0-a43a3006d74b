package com.jdl.aic.core.service.client.dto.request.system;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取待审核内容列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetPendingReviewsRequest {
    
    /**
     * 内容类型过滤
     */
    private String contentType;
    
    /**
     * 审核状态过滤
     */
    private Integer reviewStatus;
    
    /**
     * 优先级过滤
     */
    private Integer priority;
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 默认构造函数
     */
    public GetPendingReviewsRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型过滤
     * @param reviewStatus 审核状态过滤
     * @param priority 优先级过滤
     * @param search 搜索关键词
     */
    public GetPendingReviewsRequest(String contentType, Integer reviewStatus, Integer priority, String search) {
        this.contentType = contentType;
        this.reviewStatus = reviewStatus;
        this.priority = priority;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Integer getReviewStatus() {
        return reviewStatus;
    }
    
    public void setReviewStatus(Integer reviewStatus) {
        this.reviewStatus = reviewStatus;
    }
    
    public Integer getPriority() {
        return priority;
    }
    
    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    @Override
    public String toString() {
        return "GetPendingReviewsRequest{" +
                "contentType='" + contentType + '\'' +
                ", reviewStatus=" + reviewStatus +
                ", priority=" + priority +
                ", search='" + search + '\'' +
                '}';
    }
}
