package com.jdl.aic.core.service.client.dto.request.admin;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取管理员用户列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetAdminUsersRequest {
    
    /**
     * 活跃状态过滤
     */
    private Boolean isActive;
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 默认构造函数
     */
    public GetAdminUsersRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param isActive 活跃状态过滤
     * @param search 搜索关键词
     */
    public GetAdminUsersRequest(Boolean isActive, String search) {
        this.isActive = isActive;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    @Override
    public String toString() {
        return "GetAdminUsersRequest{" +
                "isActive=" + isActive +
                ", search='" + search + '\'' +
                '}';
    }
}
