package com.jdl.aic.core.service.client.dto.system;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 爬虫内容DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CrawlerContentDTO {
    
    /**
     * 内容ID
     */
    private Long id;
    
    /**
     * 内容标题
     */
    @NotBlank(message = "内容标题不能为空")
    private String title;
    
    /**
     * 原始链接URL
     */
    @NotBlank(message = "原始链接不能为空")
    private String link;
    
    /**
     * 内容语言
     */
    private String language;

    /**
     * 作者信息（JSON格式）
     */
    private String author;

    /**
     * 内容描述或摘要
     */
    private String description;
    
    /**
     * 内容发布时间
     */
    private LocalDateTime pubDate;
    
    /**
     * AI智能总结内容
     */
    private String aiSummary;
    
    /**
     * 是否精品内容
     */
    private Boolean isFeatured;
    
    /**
     * 内容MD5值
     */
    @NotBlank(message = "内容MD5值不能为空")
    private String contentMd5;
    
    /**
     * 爬虫抓取的完整内容
     */
    private String content;
    
    /**
     * 内容类型
     */
    private String contentType;
    
    /**
     * 内容状态（0:待处理, 1:已处理, 2:处理失败, 3:已忽略）
     */
    private Integer status;
    
    /**
     * 内容质量评分（0.00-5.00）
     */
    private BigDecimal qualityScore;
    
    /**
     * 内容字数统计
     */
    private Integer wordCount;
    
    /**
     * 内容标签列表
     */
    private List<String> tags;
    
    /**
     * 扩展元数据信息
     */
    private String metadata;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;

    /**
     * 内容类型，区分文章、视频、音频、图片
     */
    private String type;

    /**
     * 附件信息
     */
    private List<Object> attachments;

    /**
     * 媒体资源信息
     */
    private List<Object> media;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 默认构造函数
     */
    public CrawlerContentDTO() {
    }

    /**
     * 基础构造函数
     *
     * @param title 内容标题
     * @param link 原始链接
     * @param contentMd5 内容MD5值
     */
    public CrawlerContentDTO(String title, String link, String contentMd5) {
        this.title = title;
        this.link = link;
        this.contentMd5 = contentMd5;
    }

    // Getter and Setter methods
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getPubDate() {
        return pubDate;
    }

    public void setPubDate(LocalDateTime pubDate) {
        this.pubDate = pubDate;
    }

    public String getAiSummary() {
        return aiSummary;
    }

    public void setAiSummary(String aiSummary) {
        this.aiSummary = aiSummary;
    }

    public Boolean getIsFeatured() {
        return isFeatured;
    }

    public void setIsFeatured(Boolean isFeatured) {
        this.isFeatured = isFeatured;
    }

    public String getContentMd5() {
        return contentMd5;
    }

    public void setContentMd5(String contentMd5) {
        this.contentMd5 = contentMd5;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getQualityScore() {
        return qualityScore;
    }

    public void setQualityScore(BigDecimal qualityScore) {
        this.qualityScore = qualityScore;
    }

    public Integer getWordCount() {
        return wordCount;
    }

    public void setWordCount(Integer wordCount) {
        this.wordCount = wordCount;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<Object> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<Object> attachments) {
        this.attachments = attachments;
    }

    public List<Object> getMedia() {
        return media;
    }

    public void setMedia(List<Object> media) {
        this.media = media;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }
    
    public String getTaskDesc() {
        return taskDesc;
    }

    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc;
    }

    @Override
    public String toString() {
        return "CrawlerContentDTO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", link='" + link + '\'' +
                ", language='" + language + '\'' +
                ", description='" + description + '\'' +
                ", pubDate=" + pubDate +
                ", aiSummary='" + aiSummary + '\'' +
                ", isFeatured=" + isFeatured +
                ", contentMd5='" + contentMd5 + '\'' +
                ", contentType='" + contentType + '\'' +
                ", status=" + status +
                ", qualityScore=" + qualityScore +
                ", wordCount=" + wordCount +
                ", type='" + type + '\'' +
                ", taskId='" + taskId + '\'' +
                ", taskName='" + taskName + '\'' +
                ", taskDesc='" + taskDesc + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CrawlerContentDTO that = (CrawlerContentDTO) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (title != null ? !title.equals(that.title) : that.title != null) return false;
        if (link != null ? !link.equals(that.link) : that.link != null) return false;
        if (language != null ? !language.equals(that.language) : that.language != null) return false;
        if (contentMd5 != null ? !contentMd5.equals(that.contentMd5) : that.contentMd5 != null) return false;
        if (contentType != null ? !contentType.equals(that.contentType) : that.contentType != null) return false;
        if (status != null ? !status.equals(that.status) : that.status != null) return false;
        return isFeatured != null ? isFeatured.equals(that.isFeatured) : that.isFeatured == null;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (title != null ? title.hashCode() : 0);
        result = 31 * result + (link != null ? link.hashCode() : 0);
        result = 31 * result + (language != null ? language.hashCode() : 0);
        result = 31 * result + (contentMd5 != null ? contentMd5.hashCode() : 0);
        result = 31 * result + (contentType != null ? contentType.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (isFeatured != null ? isFeatured.hashCode() : 0);
        return result;
    }
}
