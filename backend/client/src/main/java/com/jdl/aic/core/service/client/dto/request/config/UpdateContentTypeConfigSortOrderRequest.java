package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 更新内容类型配置排序请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateContentTypeConfigSortOrderRequest {
    
    /**
     * 配置ID
     */
    private Long id;
    
    /**
     * 排序值
     */
    private Integer sortOrder;
    
    /**
     * 默认构造函数
     */
    public UpdateContentTypeConfigSortOrderRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 配置ID
     * @param sortOrder 排序值
     */
    public UpdateContentTypeConfigSortOrderRequest(Long id, Integer sortOrder) {
        this.id = id;
        this.sortOrder = sortOrder;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    @Override
    public String toString() {
        return "UpdateContentTypeConfigSortOrderRequest{" +
                "id=" + id +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
