package com.jdl.aic.core.service.portal.client;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.CommentDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;

import java.util.List;

/**
 * 评论数据服务接口
 *
 * <p>提供评论相关的业务功能，包括：
 * <ul>
 *   <li>评论的发布、编辑和删除</li>
 *   <li>评论查询和分页</li>
 *   <li>评论回复和嵌套结构</li>
 *   <li>评论统计和分析</li>
 *   <li>评论审核和管理</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface CommentDataService {

    // ==================== 评论基本操作 ====================

    /**
     * 发布评论
     *
     * @param request 创建评论请求参数
     * @return 评论结果
     */
    Result<CommentDTO> createComment(CreateCommentRequest request);

    /**
     * 回复评论
     *
     * @param request 回复评论请求参数
     * @return 回复结果
     */
    Result<CommentDTO> replyComment(ReplyCommentRequest request);

    /**
     * 编辑评论
     *
     * @param request 更新评论请求参数
     * @return 编辑结果
     */
    Result<CommentDTO> updateComment(UpdateCommentRequest request);

    /**
     * 删除评论（软删除）
     *
     * @param  userId 用户ID
     * @return 删除结果
     */
    Result<Void> deleteComment(Long userId, Long commentId);

    /**
     * 根据ID获取评论详情
     *
     * @param commentId 获取评论详情请求参数
     * @return 评论详情
     */
    Result<CommentDTO> getCommentById(Long commentId);

    // ==================== 评论查询 ====================

    /**
     * 获取内容的评论列表（分页）
     *
     * @param request 获取内容评论列表请求参数
     * @return 评论列表
     */
    Result<PageResult<CommentDTO>> getCommentsByContent(GetCommentsByContentRequest request);

    /**
     * 获取用户评论列表（分页）
     *
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤（可选）
     * @return 用户评论列表
     */
    Result<PageResult<CommentDTO>> getUserComments(Long userId, PageRequest pageRequest, Integer contentType);

    /**
     * 获取评论的回复列表
     *
     * @param parentCommentId 父评论ID
     * @param pageRequest 分页请求
     * @return 回复列表
     */
    Result<PageResult<CommentDTO>> getCommentReplies(Long parentCommentId, PageRequest pageRequest);

    /**
     * 获取评论树结构（包含回复）
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param maxDepth 最大深度
     * @param pageRequest 分页请求
     * @return 评论树列表
     */
    Result<PageResult<CommentDTO>> getCommentTree(Integer contentType, Long contentId, Integer maxDepth, PageRequest pageRequest);

    /**
     * 获取热门评论
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param limit 限制数量
     * @return 热门评论列表
     */
    Result<List<CommentDTO>> getPopularComments(Integer contentType, Long contentId, Integer limit);

    /**
     * 搜索评论
     *
     * @param keyword 关键词
     * @param contentType 内容类型过滤（可选）
     * @param userId 用户ID过滤（可选）
     * @param pageRequest 分页请求
     * @return 搜索结果
     */
    Result<PageResult<CommentDTO>> searchComments(String keyword, Integer contentType, Long userId, PageRequest pageRequest);

    // ==================== 评论统计 ====================

    /**
     * 统计内容的评论数
     *
     * @param request 获取评论数量请求参数
     * @return 评论数
     */
    Result<Integer> getCommentCount(GetCommentCountRequest request);

    /**
     * 批量统计内容列表的评论数
     *
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 评论数统计列表
     */
    Result<List<Object>> getCommentCountBatch(Integer contentType, List<Long> contentIds);

    /**
     * 统计用户评论总数
     *
     * @param userId 获取用户评论数量请求参数
     * @return 用户评论总数
     */
    Result<Integer> getUserCommentCount(Long userId);

    /**
     * 统计评论的回复数
     *
     * @param commentId 评论ID
     * @return 回复数
     */
    Result<Integer> getCommentReplyCount(Long commentId);

    /**
     * 统计各状态评论数量
     *
     * @param contentType 内容类型（可选）
     * @param contentId 内容ID（可选）
     * @return 状态统计
     */
    Result<List<Object>> getCommentStatusStats(Integer contentType, Long contentId);

    // ==================== 评论点赞管理 ====================

    /**
     * 增加评论点赞数
     *
     * @param commentId 评论ID
     * @return 操作结果
     */
    Result<Void> incrementCommentLike(Long commentId);

    /**
     * 减少评论点赞数
     *
     * @param commentId 评论ID
     * @return 操作结果
     */
    Result<Void> decrementCommentLike(Long commentId);

    /**
     * 更新评论点赞数
     *
     * @param commentId 评论ID
     * @param likeCount 新的点赞数
     * @return 操作结果
     */
    Result<Void> updateCommentLikeCount(Long commentId, Integer likeCount);

    // ==================== 评论状态管理 ====================

    /**
     * 更新评论状态
     *
     * @param commentId 评论ID
     * @param status 新状态（0:正常, 1:待审核, 2:已删除）
     * @return 操作结果
     */
    Result<Void> updateCommentStatus(Long commentId, Integer status);

    /**
     * 批量更新评论状态
     *
     * @param commentIds 评论ID列表
     * @param status 新状态
     * @return 操作结果
     */
    Result<Void> batchUpdateCommentStatus(List<Long> commentIds, Integer status);

    /**
     * 恢复已删除的评论
     *
     * @param commentId 评论ID
     * @return 恢复结果
     */
    Result<CommentDTO> restoreComment(Long commentId);

    /**
     * 审核评论
     *
     * @param commentId 评论ID
     * @param approved 是否通过审核
     * @param reason 审核原因（可选）
     * @return 审核结果
     */
    Result<Void> reviewComment(Long commentId, Boolean approved, String reason);

    // ==================== 批量操作 ====================

    /**
     * 批量发布评论
     *
     * @param userId 用户ID
     * @param comments 评论列表
     * @return 批量操作结果
     */
    Result<List<CommentDTO>> batchCreateComments(Long userId, List<CommentDTO> comments);

    /**
     * 批量删除评论
     *
     * @param userId 用户ID
     * @param commentIds 评论ID列表
     * @return 批量操作结果
     */
    Result<Void> batchDeleteComments(Long userId, List<Long> commentIds);

    /**
     * 清理已删除的评论记录
     *
     * @param days 清理天数前的记录
     * @return 清理结果
     */
    Result<Integer> cleanupDeletedComments(Integer days);

    // ==================== 评论分析 ====================

    /**
     * 获取用户评论趋势分析
     *
     * @param userId 用户ID
     * @param days 分析天数
     * @return 评论趋势数据
     */
    Result<Object> getUserCommentTrend(Long userId, Integer days);

    /**
     * 获取内容评论趋势分析
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param days 分析天数
     * @return 评论趋势数据
     */
    Result<Object> getContentCommentTrend(Integer contentType, Long contentId, Integer days);

    /**
     * 获取用户评论活跃度分析
     *
     * @param userId 用户ID
     * @param days 分析天数
     * @return 活跃度数据
     */
    Result<Object> getUserCommentActivity(Long userId, Integer days);

    /**
     * 获取评论质量分析
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 评论质量数据
     */
    Result<Object> getCommentQualityAnalysis(Integer contentType, Long contentId);

    // ==================== 评论导出和管理 ====================

    /**
     * 导出用户评论数据
     *
     * @param userId 用户ID
     * @param contentType 内容类型过滤（可选）
     * @param format 导出格式（json, csv等）
     * @return 导出结果
     */
    Result<Object> exportUserComments(Long userId, Integer contentType, String format);

    /**
     * 导出内容评论数据
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param format 导出格式
     * @return 导出结果
     */
    Result<Object> exportContentComments(Integer contentType, Long contentId, String format);

    /**
     * 获取待审核评论列表
     *
     * @param pageRequest 分页请求
     * @param contentType 内容类型过滤（可选）
     * @return 待审核评论列表
     */
    Result<PageResult<CommentDTO>> getPendingComments(PageRequest pageRequest, Integer contentType);

    /**
     * 获取用户最近评论
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近评论列表
     */
    Result<List<CommentDTO>> getRecentComments(Long userId, Integer limit);
}
