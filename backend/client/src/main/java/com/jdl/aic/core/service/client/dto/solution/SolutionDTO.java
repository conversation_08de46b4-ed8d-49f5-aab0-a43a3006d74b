package com.jdl.aic.core.service.client.dto.solution;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 场景解决方案DTO
 * 
 * <p>封装场景解决方案的完整信息，包括基本信息、步骤流程、关联知识等。
 * 支持多步骤的解决方案流程管理和知识关联。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SolutionDTO {
    
    /**
     * 解决方案ID
     */
    private Long id;
    
    /**
     * 解决方案标题
     */
    @NotBlank(message = "解决方案标题不能为空")
    private String title;
    
    /**
     * 解决方案描述
     */
    private String description;

    /**
     * 解决方案详细内容（Markdown/富文本）
     */
    private String content;

    /**
     * 状态（0:草稿, 1:发布, 2:下线）
     */
    private Integer status;
    
    /**
     * 作者ID
     */
    private String authorId;
    
    /**
     * 作者姓名
     */
    private String authorName;
    
    /**
     * 解决方案步骤列表
     */
    private List<SolutionStepDTO> steps;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 浏览次数
     */
    private Integer viewCount;
    
    /**
     * 点赞次数
     */
    private Integer likeCount;
    
    /**
     * 使用次数
     */
    private Integer useCount;
    
    /**
     * 封面图片URL
     */
    private String coverImageUrl;
    
    /**
     * 元数据JSON（扩展信息）
     */
    private Map<String, Object> metadataJson;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;

    /**
     * 关联的分类
     */
    private List<ContentCategoryRelationDTO> categories;

    /**
     * 默认构造函数
     */
    public SolutionDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }
    
    public String getAuthorName() {
        return authorName;
    }
    
    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }
    
    public List<SolutionStepDTO> getSteps() {
        return steps;
    }
    
    public void setSteps(List<SolutionStepDTO> steps) {
        this.steps = steps;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public Integer getViewCount() {
        return viewCount;
    }
    
    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }
    
    public Integer getLikeCount() {
        return likeCount;
    }
    
    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }
    
    public Integer getUseCount() {
        return useCount;
    }
    
    public void setUseCount(Integer useCount) {
        this.useCount = useCount;
    }
    
    public String getCoverImageUrl() {
        return coverImageUrl;
    }
    
    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }
    
    public Map<String, Object> getMetadataJson() {
        return metadataJson;
    }
    
    public void setMetadataJson(Map<String, Object> metadataJson) {
        this.metadataJson = metadataJson;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public List<ContentCategoryRelationDTO> getCategories() {
        return categories;
    }

    public void setCategories(List<ContentCategoryRelationDTO> categories) {
        this.categories = categories;
    }

    @Override
    public String toString() {
        return "SolutionDTO{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", content='" + content + '\'' +
                ", status=" + status +
                ", authorId=" + authorId +
                ", authorName='" + authorName + '\'' +
                ", steps=" + steps +
                ", tags=" + tags +
                ", viewCount=" + viewCount +
                ", likeCount=" + likeCount +
                ", useCount=" + useCount +
                ", coverImageUrl='" + coverImageUrl + '\'' +
                ", metadataJson=" + metadataJson +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy=" + createdBy +
                ", updatedBy=" + updatedBy +
                '}';
    }
}
