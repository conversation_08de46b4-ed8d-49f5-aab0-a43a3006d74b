package com.jdl.aic.core.service.client.constants;

/**
 * 错误码常量定义
 * 
 * <p>定义了AI社区系统中使用的所有错误码，按照功能模块进行分类。
 * 错误码采用分层设计，便于快速定位问题所在的模块。
 * 
 * <h3>错误码规则：</h3>
 * <ul>
 *   <li>系统级错误 (1000-1999)</li>
 *   <li>认证授权错误 (2000-2999)</li>
 *   <li>业务逻辑错误 (3000-3999)</li>
 *   <li>知识管理特定错误 (4000-4999)</li>
 *   <li>文件存储错误 (5000-5999)</li>
 *   <li>搜索服务错误 (6000-6999)</li>
 *   <li>社区互动错误 (7000-7999)</li>
 *   <li>学习资源错误 (8000-8999)</li>
 *   <li>通知服务错误 (9000-9999)</li>
 *   <li>解决方案错误 (10000-10999)</li>
 *   <li>内容源错误 (11000-11999)</li>
 *   <li>系统管理错误 (12000-12999)</li>
 *   <li>推荐服务错误 (13000-13999)</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public final class ErrorCodes {
    
    // ==================== 系统级错误 (1000-1999) ====================
    
    /**
     * 系统内部错误
     */
    public static final String SYSTEM_ERROR = "SYSTEM_ERROR";
    
    /**
     * 数据库错误
     */
    public static final String DATABASE_ERROR = "DATABASE_ERROR";
    
    /**
     * 外部服务错误
     */
    public static final String EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR";
    
    /**
     * 请求频率超限
     */
    public static final String RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED";
    
    /**
     * 服务暂时不可用
     */
    public static final String SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE";
    
    // ==================== 认证授权错误 (2000-2999) ====================
    
    /**
     * 用户未认证
     */
    public static final String UNAUTHORIZED = "UNAUTHORIZED";
    
    /**
     * 用户无权限
     */
    public static final String FORBIDDEN = "FORBIDDEN";
    
    /**
     * Token已过期
     */
    public static final String TOKEN_EXPIRED = "TOKEN_EXPIRED";
    
    /**
     * Token无效
     */
    public static final String TOKEN_INVALID = "TOKEN_INVALID";
    
    /**
     * 登录失败
     */
    public static final String LOGIN_FAILED = "LOGIN_FAILED";
    
    /**
     * 账号被锁定
     */
    public static final String ACCOUNT_LOCKED = "ACCOUNT_LOCKED";
    
    /**
     * 权限不足
     */
    public static final String INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS";
    
    // ==================== 业务逻辑错误 (3000-3999) ====================
    
    /**
     * 参数校验失败
     */
    public static final String VALIDATION_ERROR = "VALIDATION_ERROR";
    
    /**
     * 资源不存在
     */
    public static final String RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND";
    
    /**
     * 资源冲突
     */
    public static final String RESOURCE_CONFLICT = "RESOURCE_CONFLICT";
    
    /**
     * 无效请求
     */
    public static final String INVALID_REQUEST = "INVALID_REQUEST";
    
    /**
     * 操作不允许
     */
    public static final String OPERATION_NOT_ALLOWED = "OPERATION_NOT_ALLOWED";
    
    /**
     * 资源重复
     */
    public static final String DUPLICATE_RESOURCE = "DUPLICATE_RESOURCE";
    
    /**
     * 无效的状态转换
     */
    public static final String INVALID_STATUS_TRANSITION = "INVALID_STATUS_TRANSITION";
    
    // ==================== 知识管理特定错误 (4000-4999) ====================
    
    /**
     * 知识类型不存在
     */
    public static final String KNOWLEDGE_TYPE_NOT_FOUND = "KNOWLEDGE_TYPE_NOT_FOUND";
    
    /**
     * 元数据校验失败
     */
    public static final String METADATA_VALIDATION_ERROR = "METADATA_VALIDATION_ERROR";
    
    /**
     * 知识状态无效
     */
    public static final String KNOWLEDGE_STATUS_INVALID = "KNOWLEDGE_STATUS_INVALID";
    
    /**
     * 知识类型正在使用中
     */
    public static final String KNOWLEDGE_TYPE_IN_USE = "KNOWLEDGE_TYPE_IN_USE";
    
    /**
     * 知识内容不存在
     */
    public static final String KNOWLEDGE_CONTENT_NOT_FOUND = "KNOWLEDGE_CONTENT_NOT_FOUND";
    
    /**
     * 知识内容访问被拒绝
     */
    public static final String KNOWLEDGE_ACCESS_DENIED = "KNOWLEDGE_ACCESS_DENIED";
    
    /**
     * 知识内容编辑冲突
     */
    public static final String KNOWLEDGE_EDIT_CONFLICT = "KNOWLEDGE_EDIT_CONFLICT";
    
    /**
     * 审核流程错误
     */
    public static final String REVIEW_WORKFLOW_ERROR = "REVIEW_WORKFLOW_ERROR";
    
    // ==================== 文件存储错误 (5000-5999) ====================
    
    /**
     * 文件不存在
     */
    public static final String FILE_NOT_FOUND = "FILE_NOT_FOUND";
    
    /**
     * 文件上传失败
     */
    public static final String FILE_UPLOAD_FAILED = "FILE_UPLOAD_FAILED";
    
    /**
     * 文件大小超限
     */
    public static final String FILE_SIZE_EXCEEDED = "FILE_SIZE_EXCEEDED";
    
    /**
     * 文件类型不支持
     */
    public static final String FILE_TYPE_NOT_SUPPORTED = "FILE_TYPE_NOT_SUPPORTED";
    
    /**
     * 文件存储错误
     */
    public static final String FILE_STORAGE_ERROR = "FILE_STORAGE_ERROR";
    
    /**
     * 文件访问被拒绝
     */
    public static final String FILE_ACCESS_DENIED = "FILE_ACCESS_DENIED";

    /**
     * 文件下载失败
     */
    public static final String FILE_DOWNLOAD_FAILED = "FILE_DOWNLOAD_FAILED";

    /**
     * 文件已过期
     */
    public static final String FILE_EXPIRED = "FILE_EXPIRED";

    /**
     * 文件校验失败
     */
    public static final String FILE_CHECKSUM_FAILED = "FILE_CHECKSUM_FAILED";

    /**
     * 存储空间不足
     */
    public static final String STORAGE_SPACE_INSUFFICIENT = "STORAGE_SPACE_INSUFFICIENT";

    /**
     * 文件处理失败
     */
    public static final String FILE_PROCESSING_FAILED = "FILE_PROCESSING_FAILED";

    /**
     * 缩略图生成失败
     */
    public static final String THUMBNAIL_GENERATION_FAILED = "THUMBNAIL_GENERATION_FAILED";

    /**
     * 文件迁移失败
     */
    public static final String FILE_MIGRATION_FAILED = "FILE_MIGRATION_FAILED";
    
    // ==================== 搜索服务错误 (6000-6999) ====================
    
    /**
     * 搜索服务错误
     */
    public static final String SEARCH_SERVICE_ERROR = "SEARCH_SERVICE_ERROR";
    
    /**
     * 搜索查询无效
     */
    public static final String SEARCH_QUERY_INVALID = "SEARCH_QUERY_INVALID";
    
    /**
     * 搜索超时
     */
    public static final String SEARCH_TIMEOUT = "SEARCH_TIMEOUT";

    /**
     * 搜索索引不存在
     */
    public static final String SEARCH_INDEX_NOT_FOUND = "SEARCH_INDEX_NOT_FOUND";

    /**
     * 搜索索引构建失败
     */
    public static final String SEARCH_INDEX_BUILD_FAILED = "SEARCH_INDEX_BUILD_FAILED";

    /**
     * 搜索结果为空
     */
    public static final String SEARCH_NO_RESULTS = "SEARCH_NO_RESULTS";

    /**
     * 搜索关键词过短
     */
    public static final String SEARCH_KEYWORD_TOO_SHORT = "SEARCH_KEYWORD_TOO_SHORT";

    /**
     * 搜索关键词过长
     */
    public static final String SEARCH_KEYWORD_TOO_LONG = "SEARCH_KEYWORD_TOO_LONG";

    /**
     * 搜索频率超限
     */
    public static final String SEARCH_RATE_LIMIT_EXCEEDED = "SEARCH_RATE_LIMIT_EXCEEDED";

    /**
     * 搜索引擎不可用
     */
    public static final String SEARCH_ENGINE_UNAVAILABLE = "SEARCH_ENGINE_UNAVAILABLE";
    
    // ==================== 社区互动错误 (7000-7999) ====================
    
    /**
     * 评论不存在
     */
    public static final String COMMENT_NOT_FOUND = "COMMENT_NOT_FOUND";
    
    /**
     * 评论访问被拒绝
     */
    public static final String COMMENT_ACCESS_DENIED = "COMMENT_ACCESS_DENIED";
    
    /**
     * 互动操作不允许
     */
    public static final String INTERACTION_NOT_ALLOWED = "INTERACTION_NOT_ALLOWED";
    
    /**
     * 重复互动
     */
    public static final String DUPLICATE_INTERACTION = "DUPLICATE_INTERACTION";

    /**
     * 评论内容违规
     */
    public static final String COMMENT_CONTENT_VIOLATION = "COMMENT_CONTENT_VIOLATION";

    /**
     * 评论过于频繁
     */
    public static final String COMMENT_TOO_FREQUENT = "COMMENT_TOO_FREQUENT";

    /**
     * 收藏夹不存在
     */
    public static final String FAVORITE_FOLDER_NOT_FOUND = "FAVORITE_FOLDER_NOT_FOUND";

    /**
     * 收藏夹已满
     */
    public static final String FAVORITE_FOLDER_FULL = "FAVORITE_FOLDER_FULL";

    /**
     * 关注用户不存在
     */
    public static final String FOLLOW_USER_NOT_FOUND = "FOLLOW_USER_NOT_FOUND";

    /**
     * 不能关注自己
     */
    public static final String CANNOT_FOLLOW_SELF = "CANNOT_FOLLOW_SELF";

    /**
     * 分享链接已过期
     */
    public static final String SHARE_LINK_EXPIRED = "SHARE_LINK_EXPIRED";

    // ==================== 学习资源错误 (8000-8999) ====================

    /**
     * 学习资源不存在
     */
    public static final String LEARNING_RESOURCE_NOT_FOUND = "LEARNING_RESOURCE_NOT_FOUND";

    /**
     * 学习路径不存在
     */
    public static final String LEARNING_PATH_NOT_FOUND = "LEARNING_PATH_NOT_FOUND";

    /**
     * 学习进度无效
     */
    public static final String LEARNING_PROGRESS_INVALID = "LEARNING_PROGRESS_INVALID";

    /**
     * 学习资源访问被拒绝
     */
    public static final String LEARNING_RESOURCE_ACCESS_DENIED = "LEARNING_RESOURCE_ACCESS_DENIED";

    /**
     * 学习路径循环依赖
     */
    public static final String LEARNING_PATH_CIRCULAR_DEPENDENCY = "LEARNING_PATH_CIRCULAR_DEPENDENCY";

    /**
     * 学习资源类型不支持
     */
    public static final String LEARNING_RESOURCE_TYPE_NOT_SUPPORTED = "LEARNING_RESOURCE_TYPE_NOT_SUPPORTED";

    /**
     * 学习进度已完成
     */
    public static final String LEARNING_PROGRESS_ALREADY_COMPLETED = "LEARNING_PROGRESS_ALREADY_COMPLETED";

    /**
     * 学习资源已下线
     */
    public static final String LEARNING_RESOURCE_OFFLINE = "LEARNING_RESOURCE_OFFLINE";

    /**
     * 学习路径资源冲突
     */
    public static final String LEARNING_PATH_RESOURCE_CONFLICT = "LEARNING_PATH_RESOURCE_CONFLICT";

    // ==================== 通知服务错误 (9000-9999) ====================

    /**
     * 通知不存在
     */
    public static final String NOTIFICATION_NOT_FOUND = "NOTIFICATION_NOT_FOUND";

    /**
     * 通知模板不存在
     */
    public static final String NOTIFICATION_TEMPLATE_NOT_FOUND = "NOTIFICATION_TEMPLATE_NOT_FOUND";

    /**
     * 通知发送失败
     */
    public static final String NOTIFICATION_SEND_FAILED = "NOTIFICATION_SEND_FAILED";

    /**
     * 通知渠道不支持
     */
    public static final String NOTIFICATION_CHANNEL_NOT_SUPPORTED = "NOTIFICATION_CHANNEL_NOT_SUPPORTED";

    /**
     * 通知模板变量缺失
     */
    public static final String NOTIFICATION_TEMPLATE_VARIABLE_MISSING = "NOTIFICATION_TEMPLATE_VARIABLE_MISSING";

    /**
     * 通知频率超限
     */
    public static final String NOTIFICATION_RATE_LIMIT_EXCEEDED = "NOTIFICATION_RATE_LIMIT_EXCEEDED";

    /**
     * 通知设置无效
     */
    public static final String NOTIFICATION_SETTING_INVALID = "NOTIFICATION_SETTING_INVALID";

    /**
     * 通知已读
     */
    public static final String NOTIFICATION_ALREADY_READ = "NOTIFICATION_ALREADY_READ";

    /**
     * 通知服务不可用
     */
    public static final String NOTIFICATION_SERVICE_UNAVAILABLE = "NOTIFICATION_SERVICE_UNAVAILABLE";

    // ==================== 解决方案错误 (10000-10999) ====================

    /**
     * 解决方案不存在
     */
    public static final String SOLUTION_NOT_FOUND = "SOLUTION_NOT_FOUND";

    /**
     * 解决方案步骤不存在
     */
    public static final String SOLUTION_STEP_NOT_FOUND = "SOLUTION_STEP_NOT_FOUND";

    /**
     * 解决方案访问被拒绝
     */
    public static final String SOLUTION_ACCESS_DENIED = "SOLUTION_ACCESS_DENIED";

    /**
     * 解决方案步骤顺序无效
     */
    public static final String SOLUTION_STEP_ORDER_INVALID = "SOLUTION_STEP_ORDER_INVALID";

    /**
     * 解决方案状态无效
     */
    public static final String SOLUTION_STATUS_INVALID = "SOLUTION_STATUS_INVALID";

    /**
     * 解决方案步骤依赖错误
     */
    public static final String SOLUTION_STEP_DEPENDENCY_ERROR = "SOLUTION_STEP_DEPENDENCY_ERROR";

    /**
     * 解决方案已发布
     */
    public static final String SOLUTION_ALREADY_PUBLISHED = "SOLUTION_ALREADY_PUBLISHED";

    /**
     * 解决方案编辑冲突
     */
    public static final String SOLUTION_EDIT_CONFLICT = "SOLUTION_EDIT_CONFLICT";

    // ==================== 内容源错误 (11000-11999) ====================

    /**
     * RSS源不存在
     */
    public static final String RSS_SOURCE_NOT_FOUND = "RSS_SOURCE_NOT_FOUND";

    /**
     * RSS源连接失败
     */
    public static final String RSS_SOURCE_CONNECTION_FAILED = "RSS_SOURCE_CONNECTION_FAILED";

    /**
     * RSS源格式无效
     */
    public static final String RSS_SOURCE_FORMAT_INVALID = "RSS_SOURCE_FORMAT_INVALID";

    /**
     * 内容抓取失败
     */
    public static final String CONTENT_FETCH_FAILED = "CONTENT_FETCH_FAILED";

    /**
     * 内容解析失败
     */
    public static final String CONTENT_PARSE_FAILED = "CONTENT_PARSE_FAILED";

    /**
     * AI分析服务不可用
     */
    public static final String AI_ANALYSIS_SERVICE_UNAVAILABLE = "AI_ANALYSIS_SERVICE_UNAVAILABLE";

    /**
     * 内容重复检测失败
     */
    public static final String CONTENT_DUPLICATE_CHECK_FAILED = "CONTENT_DUPLICATE_CHECK_FAILED";

    /**
     * 内容质量评分失败
     */
    public static final String CONTENT_QUALITY_SCORE_FAILED = "CONTENT_QUALITY_SCORE_FAILED";

    /**
     * RSS源已存在
     */
    public static final String RSS_SOURCE_ALREADY_EXISTS = "RSS_SOURCE_ALREADY_EXISTS";

    // ==================== 系统管理错误 (12000-12999) ====================

    /**
     * 系统配置不存在
     */
    public static final String SYSTEM_CONFIG_NOT_FOUND = "SYSTEM_CONFIG_NOT_FOUND";

    /**
     * 系统配置值无效
     */
    public static final String SYSTEM_CONFIG_VALUE_INVALID = "SYSTEM_CONFIG_VALUE_INVALID";

    /**
     * 系统配置不可编辑
     */
    public static final String SYSTEM_CONFIG_NOT_EDITABLE = "SYSTEM_CONFIG_NOT_EDITABLE";

    /**
     * 审核任务不存在
     */
    public static final String REVIEW_TASK_NOT_FOUND = "REVIEW_TASK_NOT_FOUND";

    /**
     * 审核状态无效
     */
    public static final String REVIEW_STATUS_INVALID = "REVIEW_STATUS_INVALID";

    /**
     * 系统维护中
     */
    public static final String SYSTEM_UNDER_MAINTENANCE = "SYSTEM_UNDER_MAINTENANCE";

    /**
     * 备份操作失败
     */
    public static final String BACKUP_OPERATION_FAILED = "BACKUP_OPERATION_FAILED";

    /**
     * 缓存清理失败
     */
    public static final String CACHE_CLEAR_FAILED = "CACHE_CLEAR_FAILED";

    /**
     * 系统监控数据不可用
     */
    public static final String SYSTEM_MONITORING_DATA_UNAVAILABLE = "SYSTEM_MONITORING_DATA_UNAVAILABLE";

    // ==================== 推荐服务错误 (13000-13999) ====================

    /**
     * 推荐算法不可用
     */
    public static final String RECOMMENDATION_ALGORITHM_UNAVAILABLE = "RECOMMENDATION_ALGORITHM_UNAVAILABLE";

    /**
     * 用户画像数据不足
     */
    public static final String USER_PROFILE_DATA_INSUFFICIENT = "USER_PROFILE_DATA_INSUFFICIENT";

    /**
     * 推荐模型训练失败
     */
    public static final String RECOMMENDATION_MODEL_TRAINING_FAILED = "RECOMMENDATION_MODEL_TRAINING_FAILED";

    /**
     * 推荐结果为空
     */
    public static final String RECOMMENDATION_RESULT_EMPTY = "RECOMMENDATION_RESULT_EMPTY";

    /**
     * 推荐偏好设置无效
     */
    public static final String RECOMMENDATION_PREFERENCE_INVALID = "RECOMMENDATION_PREFERENCE_INVALID";

    /**
     * 相似度计算失败
     */
    public static final String SIMILARITY_CALCULATION_FAILED = "SIMILARITY_CALCULATION_FAILED";

    /**
     * 推荐缓存过期
     */
    public static final String RECOMMENDATION_CACHE_EXPIRED = "RECOMMENDATION_CACHE_EXPIRED";

    /**
     * 推荐服务过载
     */
    public static final String RECOMMENDATION_SERVICE_OVERLOADED = "RECOMMENDATION_SERVICE_OVERLOADED";
    
    /**
     * 私有构造函数，防止实例化
     */
    private ErrorCodes() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
