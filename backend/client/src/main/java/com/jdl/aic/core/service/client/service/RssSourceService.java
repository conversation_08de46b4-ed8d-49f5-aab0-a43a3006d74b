package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.rss.RssSourceDTO;
import com.jdl.aic.core.service.client.dto.request.rss.GetRssSourceListRequest;
import com.jdl.aic.core.service.client.dto.request.rss.UpdateRssSourceStatusRequest;
import com.jdl.aic.core.service.client.dto.request.rss.GetRssSourcesByCategoryRequest;
import java.util.List;

/**
 * RSS源管理服务接口
 * 
 * <p>提供RSS源管理功能，包括：
 * <ul>
 *   <li>RSS源的CRUD操作</li>
 *   <li>RSS源状态管理</li>
 *   <li>RSS源分类查询</li>
 *   <li>RSS源抓取状态更新</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface RssSourceService {
    
    // ==================== RSS源管理 ====================

    /**
     * 获取RSS源列表（分页，支持多种过滤条件）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return RSS源列表
     */
    Result<PageResult<RssSourceDTO>> getRssSourceList(
            PageRequest pageRequest,
            GetRssSourceListRequest request);

    /**
     * 根据ID获取RSS源详情
     * 
     * @param id RSS源ID
     * @return RSS源详情
     */
    Result<RssSourceDTO> getRssSourceById(Long id);

    /**
     * 创建RSS源
     * 
     * @param rssSource RSS源信息
     * @return 创建结果
     */
    Result<RssSourceDTO> createRssSource(RssSourceDTO rssSource);

    /**
     * 更新RSS源信息
     * 
     * @param id RSS源ID
     * @param rssSource RSS源信息
     * @return 更新结果
     */
    Result<RssSourceDTO> updateRssSource(Long id, RssSourceDTO rssSource);

    /**
     * 删除RSS源
     * 
     * @param id RSS源ID
     * @return 删除结果
     */
    Result<Void> deleteRssSource(Long id);

    /**
     * 更新RSS源状态
     *
     * @param request 更新状态请求参数
     * @return 操作结果
     */
    Result<Void> updateRssSourceStatus(UpdateRssSourceStatusRequest request);

    /**
     * 根据分类获取RSS源列表
     *
     * @param request 查询请求参数
     * @return RSS源列表
     */
    Result<List<RssSourceDTO>> getRssSourcesByCategory(GetRssSourcesByCategoryRequest request);

    /**
     * 获取所有活跃的RSS源
     * 
     * @return 活跃RSS源列表
     */
    Result<List<RssSourceDTO>> getActiveRssSources();

    /**
     * 获取用户订阅的RSS源列表
     * 
     * @param ownerId 用户ID
     * @return 用户RSS源列表
     */
    Result<List<RssSourceDTO>> getUserRssSources(String ownerId);

    /**
     * 更新RSS源最后抓取时间
     * 
     * @param id RSS源ID
     * @return 更新结果
     */
    Result<Void> updateLastFetchedTime(Long id);

    /**
     * 批量更新RSS源状态
     * 
     * @param ids RSS源ID列表
     * @param status 新状态
     * @return 更新结果
     */
    Result<Void> batchUpdateStatus(List<Long> ids, Integer status);

    /**
     * 检查RSS源URL是否已存在
     * 
     * @param feedUrl RSS订阅地址
     * @param excludeId 排除的RSS源ID（用于更新时检查）
     * @return 是否存在
     */
    Result<Boolean> checkFeedUrlExists(String feedUrl, Long excludeId);

    /**
     * 获取RSS源统计信息
     * 
     * @return 统计信息
     */
    Result<RssSourceStatistics> getRssSourceStatistics();

    /**
     * RSS源统计信息内部类
     */
    class RssSourceStatistics {
        private Long totalCount;
        private Long activeCount;
        private Long pausedCount;
        private Long failedCount;
        private Long officialCount;
        private Long userSubscribedCount;

        // 构造函数
        public RssSourceStatistics() {}

        public RssSourceStatistics(Long totalCount, Long activeCount, Long pausedCount, 
                                 Long failedCount, Long officialCount, Long userSubscribedCount) {
            this.totalCount = totalCount;
            this.activeCount = activeCount;
            this.pausedCount = pausedCount;
            this.failedCount = failedCount;
            this.officialCount = officialCount;
            this.userSubscribedCount = userSubscribedCount;
        }

        // Getter and Setter methods
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }

        public Long getActiveCount() { return activeCount; }
        public void setActiveCount(Long activeCount) { this.activeCount = activeCount; }

        public Long getPausedCount() { return pausedCount; }
        public void setPausedCount(Long pausedCount) { this.pausedCount = pausedCount; }

        public Long getFailedCount() { return failedCount; }
        public void setFailedCount(Long failedCount) { this.failedCount = failedCount; }

        public Long getOfficialCount() { return officialCount; }
        public void setOfficialCount(Long officialCount) { this.officialCount = officialCount; }

        public Long getUserSubscribedCount() { return userSubscribedCount; }
        public void setUserSubscribedCount(Long userSubscribedCount) { this.userSubscribedCount = userSubscribedCount; }

        @Override
        public String toString() {
            return "RssSourceStatistics{" +
                    "totalCount=" + totalCount +
                    ", activeCount=" + activeCount +
                    ", pausedCount=" + pausedCount +
                    ", failedCount=" + failedCount +
                    ", officialCount=" + officialCount +
                    ", userSubscribedCount=" + userSubscribedCount +
                    '}';
        }
    }
}
