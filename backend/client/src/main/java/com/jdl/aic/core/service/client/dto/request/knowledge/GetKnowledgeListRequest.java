package com.jdl.aic.core.service.client.dto.request.knowledge;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取知识内容列表请求
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetKnowledgeListRequest {


    /**
     * 状态过滤
     */
    private Integer status;

    /**
     * 作者ID过滤
     */
    private String authorId;

    /**
     * 团队ID过滤
     */
    private Long teamId;

    /**
     * 搜索关键词
     */
    private String search;
    /**
     * 知识类型ID过滤
     */
    private Long knowledgeTypeId;
    /**
     * 可见性过滤（没传输则全部）
     */
    private Integer visibility;
    /**
     * 分类ID过滤（通过content_category_relation表关联）
     */
    private Long categoryId;

    /**
     * 默认构造函数
     */
    public GetKnowledgeListRequest() {
    }

    /**
     * 构造函数
     *
     * @param knowledgeTypeId 知识类型ID过滤
     * @param status 状态过滤
     * @param visibility 可见性过滤
     * @param authorId 作者ID过滤
     * @param teamId 团队ID过滤
     * @param categoryId 分类ID过滤
     * @param search 搜索关键词
     */
    public GetKnowledgeListRequest(Long knowledgeTypeId,
                                   Integer status, Integer visibility, String authorId,
                                   Long teamId, Long categoryId, String search) {
        this.knowledgeTypeId = knowledgeTypeId;
        this.status = status;
        this.visibility = visibility;
        this.authorId = authorId;
        this.teamId = teamId;
        this.categoryId = categoryId;
        this.search = search;
    }

    // Getter and Setter methods
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAuthorId() {
        return authorId;
    }

    public void setAuthorId(String authorId) {
        this.authorId = authorId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getSearch() {
        return search;
    }

    public void setSearch(String search) {
        this.search = search;
    }

    public Long getKnowledgeTypeId() {
        return knowledgeTypeId;
    }

    public void setKnowledgeTypeId(Long knowledgeTypeId) {
        this.knowledgeTypeId = knowledgeTypeId;
    }

    public Integer getVisibility() {
        return visibility;
    }

    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public String toString() {
        return "GetKnowledgeListRequest{" +
                " knowledgeTypeId=" + knowledgeTypeId +
                ", status=" + status +
                ", visibility=" + visibility +
                ", authorId=" + authorId +
                ", teamId=" + teamId +
                ", categoryId=" + categoryId +
                ", search='" + search + '\'' +
                '}';
    }
}
