package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 创建评论请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Data
public class CreateCommentRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 内容类型（0:知识, 1:资讯, 2:解决方案）
     */
    @NotNull(message = "内容类型不能为空")
    private Integer contentType;

    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    @Size(max = 1000, message = "评论内容不能超过1000字符")
    private String commentText;

    /**
     * 父评论ID
     */
    private Long parentCommentId;

    /**
     * 关联知识类型ID（可选）
     */
    private Long relatedKnowledgeTypeId;

    public CreateCommentRequest() {
    }

    public CreateCommentRequest(Long userId, Integer contentType, Long contentId, String commentText) {
        this.userId = userId;
        this.contentType = contentType;
        this.contentId = contentId;
        this.commentText = commentText;
    }

    public CreateCommentRequest(Long userId, Integer contentType, Long contentId, String commentText, Long relatedKnowledgeTypeId, Long parentCommentId) {
        this.userId = userId;
        this.contentType = contentType;
        this.contentId = contentId;
        this.commentText = commentText;
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
        this.parentCommentId = parentCommentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public String getCommentText() {
        return commentText;
    }

    public void setCommentText(String commentText) {
        this.commentText = commentText;
    }

    public Long getRelatedKnowledgeTypeId() {
        return relatedKnowledgeTypeId;
    }

    public void setRelatedKnowledgeTypeId(Long relatedKnowledgeTypeId) {
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
    }

    @Override
    public String toString() {
        return "CreateCommentRequest{" +
                "userId=" + userId +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                ", commentText='" + commentText + '\'' +
                ", relatedKnowledgeTypeId=" + relatedKnowledgeTypeId +
                '}';
    }
}
