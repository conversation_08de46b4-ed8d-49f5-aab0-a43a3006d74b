package com.jdl.aic.core.service.client.dto.request.enrollment;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

/**
 * 更新学习进度请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateEnrollmentProgressRequest {
    
    /**
     * 报名ID
     */
    @NotNull(message = "报名ID不能为空")
    private Long id;
    
    /**
     * 学习进度百分比（0.00-100.00）
     */
    @DecimalMin(value = "0.00", message = "学习进度不能小于0")
    @DecimalMax(value = "100.00", message = "学习进度不能大于100")
    private BigDecimal progressPercentage;
    
    /**
     * 已完成阶段数
     */
    private Integer completedStages;
    
    /**
     * 总阶段数
     */
    private Integer totalStages;
    
    /**
     * 学习时长（小时）
     */
    @DecimalMin(value = "0.00", message = "学习时长不能小于0")
    private BigDecimal studyHours;
    
    /**
     * 默认构造函数
     */
    public UpdateEnrollmentProgressRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 报名ID
     * @param progressPercentage 学习进度百分比
     */
    public UpdateEnrollmentProgressRequest(Long id, BigDecimal progressPercentage) {
        this.id = id;
        this.progressPercentage = progressPercentage;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public BigDecimal getProgressPercentage() {
        return progressPercentage;
    }
    
    public void setProgressPercentage(BigDecimal progressPercentage) {
        this.progressPercentage = progressPercentage;
    }
    
    public Integer getCompletedStages() {
        return completedStages;
    }
    
    public void setCompletedStages(Integer completedStages) {
        this.completedStages = completedStages;
    }
    
    public Integer getTotalStages() {
        return totalStages;
    }
    
    public void setTotalStages(Integer totalStages) {
        this.totalStages = totalStages;
    }
    
    public BigDecimal getStudyHours() {
        return studyHours;
    }
    
    public void setStudyHours(BigDecimal studyHours) {
        this.studyHours = studyHours;
    }
    
    @Override
    public String toString() {
        return "UpdateEnrollmentProgressRequest{" +
                "id=" + id +
                ", progressPercentage=" + progressPercentage +
                ", completedStages=" + completedStages +
                ", totalStages=" + totalStages +
                ", studyHours=" + studyHours +
                '}';
    }
}
