package com.jdl.aic.core.service.client.dto.request.config;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取社交功能配置列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetSocialFeatureConfigListRequest {
    
    /**
     * 内容类型（可选）
     */
    private String contentType;
    
    /**
     * 功能类型（可选）
     */
    private String featureType;
    
    /**
     * 是否启用（可选）
     */
    private Boolean isEnabled;
    
    /**
     * 默认构造函数
     */
    public GetSocialFeatureConfigListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param featureType 功能类型
     * @param isEnabled 是否启用
     */
    public GetSocialFeatureConfigListRequest(String contentType, String featureType, Boolean isEnabled) {
        this.contentType = contentType;
        this.featureType = featureType;
        this.isEnabled = isEnabled;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public String getFeatureType() {
        return featureType;
    }
    
    public void setFeatureType(String featureType) {
        this.featureType = featureType;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    @Override
    public String toString() {
        return "GetSocialFeatureConfigListRequest{" +
                "contentType='" + contentType + '\'' +
                ", featureType='" + featureType + '\'' +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
