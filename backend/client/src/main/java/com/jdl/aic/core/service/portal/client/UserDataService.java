package com.jdl.aic.core.service.portal.client;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.dto.request.user.GetUserListRequest;
import java.util.List;

/**
 * 用户基础管理服务接口
 *
 * <p>提供用户基础信息管理功能，包括：
 * <ul>
 *   <li>用户基本信息的CRUD操作</li>
 *   <li>用户认证和授权</li>
 *   <li>用户状态管理</li>
 *   <li>用户个人信息管理</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface UserDataService {

    // ==================== 用户基本信息管理 ====================

    /**
     * 获取用户列表（分页）
     *
     * @param pageRequest 分页请求
     * @param request 查询请求参数
     * @return 用户列表
     */
    Result<PageResult<UserDTO>> getUserList(
            PageRequest pageRequest,
            GetUserListRequest request);

    /**
     * 根据ID获取用户详情
     *
     * @param id 用户ID
     * @return 用户详情
     */
    Result<UserDTO> getUserById(Long id);

    /**
     * 根据SSO ID获取用户详情
     *
     * @param ssoId SSO唯一标识
     * @return 用户详情
     */
    Result<UserDTO> getUserBySsoId(String ssoId);

    /**
     * 根据用户名获取用户详情
     *
     * @param username 用户名
     * @return 用户详情
     */
    Result<UserDTO> getUserByUsername(String username);

    /**
     * 创建用户
     *
     * @param user 用户信息
     * @return 创建结果
     */
    Result<UserDTO> createUser(UserDTO user);

    /**
     * 更新用户信息
     *
     * @param id 用户ID
     * @param user 用户信息
     * @return 更新结果
     */
    Result<UserDTO> updateUser(Long id, UserDTO user);

    /**
     * 删除用户（软删除）
     *
     * @param id 用户ID
     * @return 删除结果
     */
    Result<Void> deleteUser(Long id);

    /**
     * 启用/禁用用户
     *
     * @param id 用户ID
     * @param isActive 是否启用
     * @return 操作结果
     */
    Result<Void> toggleUserStatus(Long id, Boolean isActive);

    /**
     * 批量导入用户
     *
     * @param users 用户列表
     * @return 导入结果
     */
    Result<List<UserDTO>> batchImportUsers(List<UserDTO> users);

    // ==================== 用户认证相关 ====================

    /**
     * 用户登录
     *
     * @param ssoId SSO唯一标识
     * @return 用户信息
     */
    Result<UserDTO> login(String ssoId);

    /**
     * 更新用户最后登录时间
     *
     * @param id 用户ID
     * @return 操作结果
     */
    Result<Void> updateLastLoginTime(Long id);

    /**
     * 获取当前用户信息
     *
     * @param userId 当前用户ID
     * @return 用户信息
     */
    Result<UserDTO> getCurrentUser(Long userId);

    /**
     * 更新当前用户个人信息
     *
     * @param userId 当前用户ID
     * @param user 用户信息（仅允许更新部分字段）
     * @return 更新结果
     */
    Result<UserDTO> updateCurrentUserProfile(Long userId, UserDTO user);

    // ==================== 用户统计和偏好设置 ====================

    /**
     * 获取用户统计信息
     *
     * @param userId 用户ID
     * @return 用户统计信息
     */
    Result<UserStatsDTO> getUserStats(Long userId);

    /**
     * 更新用户偏好设置
     *
     * @param userId 用户ID
     * @param preferences 偏好设置
     * @return 更新结果
     */
    Result<Void> updateUserPreferences(Long userId, Object preferences);

    // ==================== 用户团队管理 ====================

    /**
     * 获取用户所属团队列表
     *
     * @param userId 用户ID
     * @return 团队列表
     */
    Result<List<Long>> getUserTeams(Long userId);

    /**
     * 将用户添加到团队
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param role 在团队中的角色（0:成员, 1:管理员, 2:创建者）
     * @return 操作结果
     */
    Result<Void> addUserToTeam(Long userId, Long teamId, Integer role);

    /**
     * 将用户从团队中移除
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 操作结果
     */
    Result<Void> removeUserFromTeam(Long userId, Long teamId);

    /**
     * 更新用户在团队中的角色
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param role 新角色
     * @return 操作结果
     */
    Result<Void> updateUserTeamRole(Long userId, Long teamId, Integer role);

    /**
     * 获取团队成员列表
     *
     * @param teamId 团队ID
     * @param pageRequest 分页请求
     * @return 团队成员列表
     */
    Result<PageResult<UserDTO>> getTeamMembers(Long teamId, PageRequest pageRequest);

    /**
     * 获取用户在团队中的角色
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 用户角色
     */
    Result<Integer> getUserTeamRole(Long userId, Long teamId);

    /**
     * 批量添加用户到团队
     *
     * @param userIds 用户ID列表
     * @param teamId 团队ID
     * @param role 团队角色
     * @return 批量添加结果
     */
    Result<List<Object>> batchAddUsersToTeam(List<Long> userIds, Long teamId, Integer role);

    /**
     * 批量从团队移除用户
     *
     * @param userIds 用户ID列表
     * @param teamId 团队ID
     * @return 批量移除结果
     */
    Result<List<Object>> batchRemoveUsersFromTeam(List<Long> userIds, Long teamId);

    /**
     * 用户统计信息DTO
     */
    class UserStatsDTO {
        private Integer createdKnowledgeCount;
        private Integer favoriteKnowledgeCount;
        private Integer totalReadCount;
        private Integer totalLikeCount;
        private Integer commentCount;

        // Getter and Setter methods
        public Integer getCreatedKnowledgeCount() { return createdKnowledgeCount; }
        public void setCreatedKnowledgeCount(Integer createdKnowledgeCount) { this.createdKnowledgeCount = createdKnowledgeCount; }

        public Integer getFavoriteKnowledgeCount() { return favoriteKnowledgeCount; }
        public void setFavoriteKnowledgeCount(Integer favoriteKnowledgeCount) { this.favoriteKnowledgeCount = favoriteKnowledgeCount; }

        public Integer getTotalReadCount() { return totalReadCount; }
        public void setTotalReadCount(Integer totalReadCount) { this.totalReadCount = totalReadCount; }

        public Integer getTotalLikeCount() { return totalLikeCount; }
        public void setTotalLikeCount(Integer totalLikeCount) { this.totalLikeCount = totalLikeCount; }

        public Integer getCommentCount() { return commentCount; }
        public void setCommentCount(Integer commentCount) { this.commentCount = commentCount; }
    }

    /**
     * 获取用户社交统计信息
     *
     * @param userId 用户ID
     * @return 用户社交统计信息
     */
    Result<UserSocialStatsDTO> getUserSocialStats(Long userId);

    /**
     * 用户社交统计信息DTO
     */
    class UserSocialStatsDTO {
        private Integer followingCount;  // 关注数量
        private Integer followersCount;  // 粉丝数量
        private Integer publishedArticleCount;  // 已发布文章数量

        // Getter and Setter methods
        public Integer getFollowingCount() { return followingCount; }
        public void setFollowingCount(Integer followingCount) { this.followingCount = followingCount; }

        public Integer getFollowersCount() { return followersCount; }
        public void setFollowersCount(Integer followersCount) { this.followersCount = followersCount; }

        public Integer getPublishedArticleCount() { return publishedArticleCount; }
        public void setPublishedArticleCount(Integer publishedArticleCount) { this.publishedArticleCount = publishedArticleCount; }
    }
}
