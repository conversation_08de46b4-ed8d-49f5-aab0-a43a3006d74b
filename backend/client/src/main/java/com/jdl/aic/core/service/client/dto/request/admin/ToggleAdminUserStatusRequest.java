package com.jdl.aic.core.service.client.dto.request.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;

/**
 * 启用/禁用管理员用户请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ToggleAdminUserStatusRequest {
    
    /**
     * 管理员用户ID
     */
    @NotNull(message = "管理员用户ID不能为空")
    private Long id;
    
    /**
     * 是否启用
     */
    @NotNull(message = "启用状态不能为空")
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public ToggleAdminUserStatusRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param id 管理员用户ID
     * @param isActive 是否启用
     */
    public ToggleAdminUserStatusRequest(Long id, Boolean isActive) {
        this.id = id;
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "ToggleAdminUserStatusRequest{" +
                "id=" + id +
                ", isActive=" + isActive +
                '}';
    }
}
