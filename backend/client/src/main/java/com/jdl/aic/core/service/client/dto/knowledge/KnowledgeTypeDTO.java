package com.jdl.aic.core.service.client.dto.knowledge;

import com.fasterxml.jackson.annotation.JsonInclude;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 知识类型DTO
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KnowledgeTypeDTO {
    
    /**
     * 知识类型ID
     */
    private Long id;
    
    /**
     * 知识类型唯一编码
     */
    @NotBlank(message = "知识类型编码不能为空")
    @Pattern(regexp = "^[A-Z0-9_]+$", message = "知识类型编码只能包含大写字母、数字和下划线")
    private String code;
    
    /**
     * 知识类型名称
     */
    @NotBlank(message = "知识类型名称不能为空")
    private String name;
    
    /**
     * 知识类型描述
     */
    private String description;
    
    /**
     * 知识类型图标URL
     */
    private String iconUrl;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 元数据JSON Schema定义
     */
    @NotNull(message = "元数据Schema不能为空")
    private Map<String, Object> metadataSchema;
    
    /**
     * 渲染配置JSON
     */
    private Map<String, Object> renderConfigJson;
    
    /**
     * 社区配置JSON
     */
    private Map<String, Object> communityConfigJson;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 更新用户ID
     */
    private String updatedBy;
    
    /**
     * 默认构造函数
     */
    public KnowledgeTypeDTO() {
    }
    
    // Getter and Setter methods
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getIconUrl() {
        return iconUrl;
    }
    
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public Map<String, Object> getMetadataSchema() {
        return metadataSchema;
    }

    public void setMetadataSchema(Map<String, Object> metadataSchema) {
        this.metadataSchema = metadataSchema;
    }
    
    public Map<String, Object> getRenderConfigJson() {
        return renderConfigJson;
    }
    
    public void setRenderConfigJson(Map<String, Object> renderConfigJson) {
        this.renderConfigJson = renderConfigJson;
    }
    
    public Map<String, Object> getCommunityConfigJson() {
        return communityConfigJson;
    }
    
    public void setCommunityConfigJson(Map<String, Object> communityConfigJson) {
        this.communityConfigJson = communityConfigJson;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }
    
    @Override
    public String toString() {
        return "KnowledgeTypeDTO{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", iconUrl='" + iconUrl + '\'' +
                ", isActive=" + isActive +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
