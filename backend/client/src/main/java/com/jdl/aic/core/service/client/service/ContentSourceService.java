package com.jdl.aic.core.service.client.service;

import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;

import java.util.List;
import java.util.Map;

/**
 * 内容源服务接口
 * 
 * <p>提供内容源管理功能，包括：
 * <ul>
 *   <li>RSS源管理和内容抓取</li>
 *   <li>新闻资讯聚合</li>
 *   <li>AI内容摘要和分析</li>
 *   <li>内容源监控和统计</li>
 *   <li>自动化内容收集</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface ContentSourceService {
    
    // ==================== RSS源管理 ====================
    
    /**
     * 获取RSS源列表（分页）
     * 
     * @param pageRequest 分页请求
     * @param category 分类过滤
     * @param status 状态过滤（0:禁用, 1:启用, 2:异常）
     * @param search 搜索关键词
     * @return RSS源列表
     */
    Result<PageResult<Object>> getRssSourceList(
            PageRequest pageRequest,
            String category,
            Integer status,
            String search);
    
    /**
     * 根据ID获取RSS源详情
     * 
     * @param id RSS源ID
     * @return RSS源详情
     */
    Result<Object> getRssSourceById(Long id);
    
    /**
     * 创建RSS源
     * 
     * @param rssSource RSS源信息
     * @return 创建结果
     */
    Result<Object> createRssSource(Object rssSource);
    
    /**
     * 更新RSS源
     * 
     * @param id RSS源ID
     * @param rssSource RSS源信息
     * @return 更新结果
     */
    Result<Object> updateRssSource(Long id, Object rssSource);
    
    /**
     * 删除RSS源
     * 
     * @param id RSS源ID
     * @return 删除结果
     */
    Result<Void> deleteRssSource(Long id);
    
    /**
     * 启用/禁用RSS源
     * 
     * @param id RSS源ID
     * @param isActive 是否启用
     * @return 操作结果
     */
    Result<Void> toggleRssSourceStatus(Long id, Boolean isActive);
    
    /**
     * 测试RSS源连接
     * 
     * @param rssUrl RSS源URL
     * @return 测试结果
     */
    Result<Object> testRssSource(String rssUrl);
    
    /**
     * 手动抓取RSS源内容
     * 
     * @param id RSS源ID
     * @return 抓取结果
     */
    Result<Object> fetchRssContent(Long id);
    
    /**
     * 批量抓取RSS源内容
     * 
     * @param sourceIds RSS源ID列表
     * @return 批量抓取结果
     */
    Result<Object> batchFetchRssContent(List<Long> sourceIds);
    
    // ==================== 内容抓取和处理 ====================
    
    /**
     * 获取抓取的内容列表（分页）
     * 
     * @param pageRequest 分页请求
     * @param sourceId RSS源ID过滤
     * @param category 分类过滤
     * @param status 处理状态过滤
     * @param search 搜索关键词
     * @return 抓取内容列表
     */
    Result<PageResult<Object>> getFetchedContentList(
            PageRequest pageRequest,
            Long sourceId,
            String category,
            Integer status,
            String search);
    
    /**
     * 根据ID获取抓取内容详情
     * 
     * @param id 内容ID
     * @return 内容详情
     */
    Result<Object> getFetchedContentById(Long id);
    
    /**
     * 更新抓取内容状态
     * 
     * @param id 内容ID
     * @param status 新状态（0:待处理, 1:已处理, 2:已忽略, 3:处理失败）
     * @return 操作结果
     */
    Result<Void> updateFetchedContentStatus(Long id, Integer status);
    
    /**
     * 批量更新抓取内容状态
     * 
     * @param contentIds 内容ID列表
     * @param status 新状态
     * @return 操作结果
     */
    Result<Void> batchUpdateFetchedContentStatus(List<Long> contentIds, Integer status);
    
    /**
     * 删除抓取内容
     * 
     * @param id 内容ID
     * @return 删除结果
     */
    Result<Void> deleteFetchedContent(Long id);
    
    /**
     * 批量删除抓取内容
     * 
     * @param contentIds 内容ID列表
     * @return 删除结果
     */
    Result<Void> batchDeleteFetchedContent(List<Long> contentIds);
    
    // ==================== AI内容分析 ====================
    
    /**
     * 生成内容摘要
     * 
     * @param contentId 内容ID
     * @param summaryLength 摘要长度（字符数）
     * @return 内容摘要
     */
    Result<String> generateContentSummary(Long contentId, Integer summaryLength);
    
    /**
     * 批量生成内容摘要
     * 
     * @param contentIds 内容ID列表
     * @param summaryLength 摘要长度
     * @return 批量摘要结果
     */
    Result<Map<Long, String>> batchGenerateContentSummary(List<Long> contentIds, Integer summaryLength);
    
    /**
     * 提取内容关键词
     * 
     * @param contentId 内容ID
     * @param maxKeywords 最大关键词数量
     * @return 关键词列表
     */
    Result<List<String>> extractContentKeywords(Long contentId, Integer maxKeywords);
    
    /**
     * 分析内容情感
     * 
     * @param contentId 内容ID
     * @return 情感分析结果
     */
    Result<Object> analyzeContentSentiment(Long contentId);
    
    /**
     * 内容分类
     * 
     * @param contentId 内容ID
     * @return 分类结果
     */
    Result<Object> classifyContent(Long contentId);
    
    /**
     * 检测内容重复
     * 
     * @param contentId 内容ID
     * @param threshold 相似度阈值（0-1）
     * @return 重复检测结果
     */
    Result<Object> detectContentDuplication(Long contentId, Double threshold);
    
    // ==================== 内容推荐和筛选 ====================
    
    /**
     * 获取推荐内容
     * 
     * @param userId 用户ID
     * @param pageRequest 分页请求
     * @param category 分类过滤
     * @return 推荐内容列表
     */
    Result<PageResult<Object>> getRecommendedContent(
            Long userId,
            PageRequest pageRequest,
            String category);
    
    /**
     * 获取热门内容
     * 
     * @param pageRequest 分页请求
     * @param category 分类过滤
     * @param days 统计天数
     * @return 热门内容列表
     */
    Result<PageResult<Object>> getPopularContent(
            PageRequest pageRequest,
            String category,
            Integer days);
    
    /**
     * 获取最新内容
     * 
     * @param pageRequest 分页请求
     * @param category 分类过滤
     * @param sourceIds RSS源ID过滤
     * @return 最新内容列表
     */
    Result<PageResult<Object>> getLatestContent(
            PageRequest pageRequest,
            String category,
            List<Long> sourceIds);
    
    /**
     * 内容质量评分
     * 
     * @param contentId 内容ID
     * @return 质量评分结果
     */
    Result<Object> scoreContentQuality(Long contentId);
    
    /**
     * 设置内容过滤规则
     * 
     * @param sourceId RSS源ID
     * @param filterRules 过滤规则
     * @return 操作结果
     */
    Result<Void> setContentFilterRules(Long sourceId, Object filterRules);
    
    // ==================== 监控和统计 ====================
    
    /**
     * 获取内容源统计
     * 
     * @param sourceId RSS源ID过滤
     * @param days 统计天数
     * @return 统计信息
     */
    Result<Object> getContentSourceStats(Long sourceId, Integer days);
    
    /**
     * 获取抓取任务状态
     * 
     * @return 抓取任务状态
     */
    Result<Object> getFetchTaskStatus();
    
    /**
     * 获取内容处理队列状态
     * 
     * @return 处理队列状态
     */
    Result<Object> getContentProcessingQueueStatus();
    
    /**
     * 重启抓取任务
     * 
     * @param sourceId RSS源ID（null表示全部）
     * @return 操作结果
     */
    Result<Void> restartFetchTask(Long sourceId);
    
    /**
     * 清理过期内容
     * 
     * @param days 保留天数
     * @param dryRun 是否为试运行
     * @return 清理结果
     */
    Result<Object> cleanupExpiredContent(Integer days, Boolean dryRun);
    
    /**
     * 导出内容数据
     * 
     * @param sourceId RSS源ID过滤
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param format 导出格式（json, csv, xml）
     * @return 导出结果
     */
    Result<Object> exportContentData(Long sourceId, String startDate, String endDate, String format);
}
