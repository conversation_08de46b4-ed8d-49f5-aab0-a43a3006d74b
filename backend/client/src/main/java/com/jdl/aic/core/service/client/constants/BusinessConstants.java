package com.jdl.aic.core.service.client.constants;

/**
 * 业务常量定义
 * 
 * <p>定义了AI社区系统中使用的所有业务常量，包括状态码、类型定义、配置参数等。
 * 按照功能模块进行分类，便于维护和使用。
 * 
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
public final class BusinessConstants {
    
    // ==================== 通用状态常量 ====================
    
    /**
     * 状态：草稿
     */
    public static final Integer STATUS_DRAFT = 0;
    
    /**
     * 状态：发布/启用
     */
    public static final Integer STATUS_PUBLISHED = 1;
    
    /**
     * 状态：下线/禁用
     */
    public static final Integer STATUS_OFFLINE = 2;
    
    /**
     * 状态：已删除
     */
    public static final Integer STATUS_DELETED = 3;
    
    // ==================== 内容类型常量 ====================
    
    /**
     * 内容类型：知识
     */
    public static final String CONTENT_TYPE_KNOWLEDGE = "knowledge";
    
    /**
     * 内容类型：解决方案
     */
    public static final String CONTENT_TYPE_SOLUTION = "solution";
    
    /**
     * 内容类型：学习资源
     */
    public static final String CONTENT_TYPE_LEARNING_RESOURCE = "learning_resource";
    
    /**
     * 内容类型：用户
     */
    public static final String CONTENT_TYPE_USER = "user";
    
    /**
     * 内容类型：评论
     */
    public static final String CONTENT_TYPE_COMMENT = "comment";
    
    // ==================== 文件存储常量 ====================
    
    /**
     * 存储类型：本地存储
     */
    public static final String STORAGE_TYPE_LOCAL = "local";
    
    /**
     * 存储类型：对象存储
     */
    public static final String STORAGE_TYPE_OSS = "oss";
    
    /**
     * 存储类型：CDN
     */
    public static final String STORAGE_TYPE_CDN = "cdn";
    
    /**
     * 业务类型：头像
     */
    public static final String BUSINESS_TYPE_AVATAR = "avatar";
    
    /**
     * 业务类型：知识附件
     */
    public static final String BUSINESS_TYPE_KNOWLEDGE = "knowledge";
    
    /**
     * 业务类型：解决方案附件
     */
    public static final String BUSINESS_TYPE_SOLUTION = "solution";
    
    /**
     * 业务类型：学习资源
     */
    public static final String BUSINESS_TYPE_LEARNING = "learning";
    
    /**
     * 文件状态：上传中
     */
    public static final Integer FILE_STATUS_UPLOADING = 0;
    
    /**
     * 文件状态：可用
     */
    public static final Integer FILE_STATUS_AVAILABLE = 1;
    
    /**
     * 文件状态：已删除
     */
    public static final Integer FILE_STATUS_DELETED = 2;
    
    /**
     * 文件状态：损坏
     */
    public static final Integer FILE_STATUS_CORRUPTED = 3;
    
    /**
     * 访问权限：私有
     */
    public static final Integer ACCESS_LEVEL_PRIVATE = 0;
    
    /**
     * 访问权限：团队可见
     */
    public static final Integer ACCESS_LEVEL_TEAM = 1;
    
    /**
     * 访问权限：公开
     */
    public static final Integer ACCESS_LEVEL_PUBLIC = 2;
    
    // ==================== 学习资源常量 ====================
    
    /**
     * 资源类型：视频
     */
    public static final String RESOURCE_TYPE_VIDEO = "video";
    
    /**
     * 资源类型：文档
     */
    public static final String RESOURCE_TYPE_DOCUMENT = "document";
    
    /**
     * 资源类型：课程
     */
    public static final String RESOURCE_TYPE_COURSE = "course";
    
    /**
     * 资源类型：教程
     */
    public static final String RESOURCE_TYPE_TUTORIAL = "tutorial";
    
    /**
     * 难度级别：初级
     */
    public static final String DIFFICULTY_BEGINNER = "beginner";
    
    /**
     * 难度级别：中级
     */
    public static final String DIFFICULTY_INTERMEDIATE = "intermediate";
    
    /**
     * 难度级别：高级
     */
    public static final String DIFFICULTY_ADVANCED = "advanced";
    
    // ==================== 通知常量 ====================
    
    /**
     * 通知类型：系统通知
     */
    public static final String NOTIFICATION_TYPE_SYSTEM = "system";
    
    /**
     * 通知类型：评论通知
     */
    public static final String NOTIFICATION_TYPE_COMMENT = "comment";
    
    /**
     * 通知类型：点赞通知
     */
    public static final String NOTIFICATION_TYPE_LIKE = "like";
    
    /**
     * 通知类型：关注通知
     */
    public static final String NOTIFICATION_TYPE_FOLLOW = "follow";
    
    /**
     * 通知类型：提及通知
     */
    public static final String NOTIFICATION_TYPE_MENTION = "mention";
    
    /**
     * 通知状态：未读
     */
    public static final Integer NOTIFICATION_STATUS_UNREAD = 0;
    
    /**
     * 通知状态：已读
     */
    public static final Integer NOTIFICATION_STATUS_READ = 1;
    
    /**
     * 通知状态：已删除
     */
    public static final Integer NOTIFICATION_STATUS_DELETED = 2;
    
    /**
     * 通知优先级：低
     */
    public static final Integer NOTIFICATION_PRIORITY_LOW = 0;
    
    /**
     * 通知优先级：普通
     */
    public static final Integer NOTIFICATION_PRIORITY_NORMAL = 1;
    
    /**
     * 通知优先级：高
     */
    public static final Integer NOTIFICATION_PRIORITY_HIGH = 2;
    
    /**
     * 通知优先级：紧急
     */
    public static final Integer NOTIFICATION_PRIORITY_URGENT = 3;
    
    /**
     * 通知渠道：站内信
     */
    public static final String NOTIFICATION_CHANNEL_WEB = "web";
    
    /**
     * 通知渠道：邮件
     */
    public static final String NOTIFICATION_CHANNEL_EMAIL = "email";
    
    /**
     * 通知渠道：短信
     */
    public static final String NOTIFICATION_CHANNEL_SMS = "sms";
    
    /**
     * 通知渠道：推送
     */
    public static final String NOTIFICATION_CHANNEL_PUSH = "push";
    
    // ==================== 审核常量 ====================
    
    /**
     * 审核状态：待审核
     */
    public static final Integer REVIEW_STATUS_PENDING = 0;
    
    /**
     * 审核状态：通过
     */
    public static final Integer REVIEW_STATUS_APPROVED = 1;
    
    /**
     * 审核状态：拒绝
     */
    public static final Integer REVIEW_STATUS_REJECTED = 2;
    
    /**
     * 审核状态：需人工复审
     */
    public static final Integer REVIEW_STATUS_MANUAL_REVIEW = 3;
    
    /**
     * 审核类型：自动审核
     */
    public static final String REVIEW_TYPE_AUTO = "auto";
    
    /**
     * 审核类型：人工审核
     */
    public static final String REVIEW_TYPE_MANUAL = "manual";
    
    /**
     * 风险等级：无风险
     */
    public static final Integer RISK_LEVEL_NONE = 0;
    
    /**
     * 风险等级：低风险
     */
    public static final Integer RISK_LEVEL_LOW = 1;
    
    /**
     * 风险等级：中风险
     */
    public static final Integer RISK_LEVEL_MEDIUM = 2;
    
    /**
     * 风险等级：高风险
     */
    public static final Integer RISK_LEVEL_HIGH = 3;
    
    // ==================== 搜索常量 ====================
    
    /**
     * 排序方式：相关度
     */
    public static final String SORT_BY_RELEVANCE = "relevance";
    
    /**
     * 排序方式：时间
     */
    public static final String SORT_BY_TIME = "time";
    
    /**
     * 排序方式：热度
     */
    public static final String SORT_BY_POPULARITY = "popularity";
    
    /**
     * 排序方向：升序
     */
    public static final String SORT_ORDER_ASC = "asc";
    
    /**
     * 排序方向：降序
     */
    public static final String SORT_ORDER_DESC = "desc";
    
    // ==================== 默认配置常量 ====================
    
    /**
     * 默认页面大小
     */
    public static final Integer DEFAULT_PAGE_SIZE = 10;
    
    /**
     * 最大页面大小
     */
    public static final Integer MAX_PAGE_SIZE = 100;
    
    /**
     * 默认搜索结果数量
     */
    public static final Integer DEFAULT_SEARCH_LIMIT = 20;
    
    /**
     * 默认推荐数量
     */
    public static final Integer DEFAULT_RECOMMENDATION_LIMIT = 10;
    
    /**
     * 默认文件过期天数
     */
    public static final Integer DEFAULT_FILE_EXPIRY_DAYS = 30;
    
    /**
     * 私有构造函数，防止实例化
     */
    private BusinessConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}
