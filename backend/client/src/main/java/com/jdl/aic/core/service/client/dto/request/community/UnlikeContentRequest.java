package com.jdl.aic.core.service.client.dto.request.community;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 取消点赞内容请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnlikeContentRequest {
    
    /**
     * 内容类型（knowledge, solution, learning_resource, comment）
     */
    @NotBlank(message = "内容类型不能为空")
    private String contentType;
    
    /**
     * 内容ID
     */
    @NotNull(message = "内容ID不能为空")
    private Long contentId;
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 默认构造函数
     */
    public UnlikeContentRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     */
    public UnlikeContentRequest(String contentType, Long contentId, Long userId) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.userId = userId;
    }
    
    // Getter and Setter methods
    
    public String getContentType() {
        return contentType;
    }
    
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }
    
    public Long getContentId() {
        return contentId;
    }
    
    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @Override
    public String toString() {
        return "UnlikeContentRequest{" +
                "contentType='" + contentType + '\'' +
                ", contentId=" + contentId +
                ", userId=" + userId +
                '}';
    }
}
