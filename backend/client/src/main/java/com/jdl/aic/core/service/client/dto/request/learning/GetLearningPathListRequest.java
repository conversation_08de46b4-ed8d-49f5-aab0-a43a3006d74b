package com.jdl.aic.core.service.client.dto.request.learning;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jdl.aic.core.service.client.common.PageRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 获取学习路径列表请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetLearningPathListRequest {
    
    /**
     * 分页请求参数
     */
    @NotNull(message = "分页请求参数不能为空")
    @Valid
    private PageRequest pageRequest;
    
    /**
     * 分类过滤
     */
    private String category;
    
    /**
     * 难度过滤（beginner, intermediate, advanced）
     */
    private String difficulty;
    
    /**
     * 状态过滤（0:草稿, 1:发布, 2:下线）
     */
    private Integer status;
    
    /**
     * 搜索关键词
     */
    private String search;
    
    /**
     * 默认构造函数
     */
    public GetLearningPathListRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param pageRequest 分页请求参数
     * @param category 分类过滤
     * @param difficulty 难度过滤
     * @param status 状态过滤
     * @param search 搜索关键词
     */
    public GetLearningPathListRequest(PageRequest pageRequest, String category, String difficulty, Integer status, String search) {
        this.pageRequest = pageRequest;
        this.category = category;
        this.difficulty = difficulty;
        this.status = status;
        this.search = search;
    }
    
    // Getter and Setter methods
    
    public PageRequest getPageRequest() {
        return pageRequest;
    }
    
    public void setPageRequest(PageRequest pageRequest) {
        this.pageRequest = pageRequest;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getDifficulty() {
        return difficulty;
    }
    
    public void setDifficulty(String difficulty) {
        this.difficulty = difficulty;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public String getSearch() {
        return search;
    }
    
    public void setSearch(String search) {
        this.search = search;
    }
    
    @Override
    public String toString() {
        return "GetLearningPathListRequest{" +
                "pageRequest=" + pageRequest +
                ", category='" + category + '\'' +
                ", difficulty='" + difficulty + '\'' +
                ", status=" + status +
                ", search='" + search + '\'' +
                '}';
    }
}
