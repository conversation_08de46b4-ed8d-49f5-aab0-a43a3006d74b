package com.jdl.aic.core.service.client.dto.community.request;

import jakarta.validation.constraints.NotNull;

/**
 * 获取用户点赞总数请求参数
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public class GetUserLikeCountRequest {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    public GetUserLikeCountRequest() {
    }

    public GetUserLikeCountRequest(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public String toString() {
        return "GetUserLikeCountRequest{" +
                "userId=" + userId +
                '}';
    }
}
