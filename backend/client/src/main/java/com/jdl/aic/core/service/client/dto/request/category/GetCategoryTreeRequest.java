package com.jdl.aic.core.service.client.dto.request.category;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 获取分类树形结构请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetCategoryTreeRequest {
    
    /**
     * 内容类别过滤
     */
    private String contentCategory;
    
    /**
     * 细分类型ID过滤
     */
    private Long subTypeId;
    
    /**
     * 启用状态过滤
     */
    private Boolean isActive;
    
    /**
     * 默认构造函数
     */
    public GetCategoryTreeRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param contentCategory 内容类别过滤
     * @param subTypeId 细分类型ID过滤
     * @param isActive 启用状态过滤
     */
    public GetCategoryTreeRequest(String contentCategory, Long subTypeId, Boolean isActive) {
        this.contentCategory = contentCategory;
        this.subTypeId = subTypeId;
        this.isActive = isActive;
    }
    
    // Getter and Setter methods
    
    public String getContentCategory() {
        return contentCategory;
    }
    
    public void setContentCategory(String contentCategory) {
        this.contentCategory = contentCategory;
    }
    
    public Long getSubTypeId() {
        return subTypeId;
    }
    
    public void setSubTypeId(Long subTypeId) {
        this.subTypeId = subTypeId;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    @Override
    public String toString() {
        return "GetCategoryTreeRequest{" +
                "contentCategory='" + contentCategory + '\'' +
                ", subTypeId=" + subTypeId +
                ", isActive=" + isActive +
                '}';
    }
}
