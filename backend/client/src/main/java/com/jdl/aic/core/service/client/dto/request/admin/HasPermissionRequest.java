package com.jdl.aic.core.service.client.dto.request.admin;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 验证用户权限请求
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HasPermissionRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    private String permission;
    
    /**
     * 默认构造函数
     */
    public HasPermissionRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param permission 权限名称
     */
    public HasPermissionRequest(Long userId, String permission) {
        this.userId = userId;
        this.permission = permission;
    }
    
    // Getter and Setter methods
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getPermission() {
        return permission;
    }
    
    public void setPermission(String permission) {
        this.permission = permission;
    }
    
    @Override
    public String toString() {
        return "HasPermissionRequest{" +
                "userId=" + userId +
                ", permission='" + permission + '\'' +
                '}';
    }
}
