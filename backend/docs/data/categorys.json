[{"id": 1, "name": "大语言模型基础", "description": "大语言模型的基础理论、架构原理和核心概念", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/llm-foundation.svg", "subTypeId": 1, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:00:00.000Z", "updatedAt": "2025-07-20T10:00:00.000Z"}, {"id": 2, "name": "Transformer架构", "description": "Transformer模型架构设计、注意力机制和编码解码原理", "parentId": 1, "contentCategory": "knowledge", "iconUrl": "/icons/transformer.svg", "subTypeId": 2, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:01:00.000Z", "updatedAt": "2025-07-20T10:01:00.000Z"}, {"id": 3, "name": "提示工程技术", "description": "提示词设计、优化策略和工程化实践方法", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/prompt-engineering.svg", "subTypeId": 3, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:02:00.000Z", "updatedAt": "2025-07-20T10:02:00.000Z"}, {"id": 4, "name": "零样本学习", "description": "大模型的零样本和少样本学习能力及应用场景", "parentId": 3, "contentCategory": "knowledge", "iconUrl": "/icons/zero-shot.svg", "subTypeId": 4, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:03:00.000Z", "updatedAt": "2025-07-20T10:03:00.000Z"}, {"id": 5, "name": "思维链推理", "description": "Chain-of-Thought推理方法和复杂问题解决策略", "parentId": 3, "contentCategory": "knowledge", "iconUrl": "/icons/chain-of-thought.svg", "subTypeId": 5, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:04:00.000Z", "updatedAt": "2025-07-20T10:04:00.000Z"}, {"id": 6, "name": "模型训练技术", "description": "大模型训练方法、优化算法和分布式训练策略", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/model-training.svg", "subTypeId": 6, "sortOrder": 3, "isActive": true, "createdAt": "2025-07-20T10:05:00.000Z", "updatedAt": "2025-07-20T10:05:00.000Z"}, {"id": 7, "name": "预训练技术", "description": "大规模预训练的数据处理、训练策略和模型优化", "parentId": 6, "contentCategory": "knowledge", "iconUrl": "/icons/pretraining.svg", "subTypeId": 7, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:06:00.000Z", "updatedAt": "2025-07-20T10:06:00.000Z"}, {"id": 8, "name": "微调与适配", "description": "模型微调、指令调优和领域适配技术方法", "parentId": 6, "contentCategory": "knowledge", "iconUrl": "/icons/fine-tuning.svg", "subTypeId": 8, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:07:00.000Z", "updatedAt": "2025-07-20T10:07:00.000Z"}, {"id": 9, "name": "强化学习优化", "description": "RLHF、PPO等强化学习在大模型中的应用", "parentId": 6, "contentCategory": "knowledge", "iconUrl": "/icons/reinforcement-learning.svg", "subTypeId": 9, "sortOrder": 3, "isActive": true, "createdAt": "2025-07-20T10:08:00.000Z", "updatedAt": "2025-07-20T10:08:00.000Z"}, {"id": 10, "name": "模型部署优化", "description": "大模型推理服务部署、性能优化和资源管理", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/model-deployment.svg", "subTypeId": 1, "sortOrder": 4, "isActive": true, "createdAt": "2025-07-20T10:09:00.000Z", "updatedAt": "2025-07-20T10:09:00.000Z"}, {"id": 11, "name": "模型量化技术", "description": "INT8/INT4量化、剪枝和模型压缩技术", "parentId": 10, "contentCategory": "knowledge", "iconUrl": "/icons/quantization.svg", "subTypeId": 2, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:10:00.000Z", "updatedAt": "2025-07-20T10:10:00.000Z"}, {"id": 12, "name": "推理加速优化", "description": "GPU优化、并行计算和推理性能提升技术", "parentId": 10, "contentCategory": "knowledge", "iconUrl": "/icons/inference-acceleration.svg", "subTypeId": 3, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:11:00.000Z", "updatedAt": "2025-07-20T10:11:00.000Z"}, {"id": 13, "name": "多模态AI技术", "description": "文本、图像、音频多模态大模型技术和应用", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/multimodal-ai.svg", "subTypeId": 4, "sortOrder": 5, "isActive": true, "createdAt": "2025-07-20T10:12:00.000Z", "updatedAt": "2025-07-20T10:12:00.000Z"}, {"id": 14, "name": "视觉语言模型", "description": "图像理解、视觉问答和图文结合的AI模型", "parentId": 13, "contentCategory": "knowledge", "iconUrl": "/icons/vision-language.svg", "subTypeId": 5, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:13:00.000Z", "updatedAt": "2025-07-20T10:13:00.000Z"}, {"id": 15, "name": "语音AI技术", "description": "语音识别、合成和语音理解的AI技术", "parentId": 13, "contentCategory": "knowledge", "iconUrl": "/icons/speech-ai.svg", "subTypeId": 6, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:14:00.000Z", "updatedAt": "2025-07-20T10:14:00.000Z"}, {"id": 16, "name": "AI Agent系统", "description": "智能代理、自主决策和任务执行系统", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/ai-agent.svg", "subTypeId": 7, "sortOrder": 6, "isActive": true, "createdAt": "2025-07-20T10:15:00.000Z", "updatedAt": "2025-07-20T10:15:00.000Z"}, {"id": 17, "name": "工具调用能力", "description": "大模型的函数调用和外部工具集成能力", "parentId": 16, "contentCategory": "knowledge", "iconUrl": "/icons/tool-calling.svg", "subTypeId": 8, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:16:00.000Z", "updatedAt": "2025-07-20T10:16:00.000Z"}, {"id": 18, "name": "任务规划推理", "description": "复杂任务分解、规划和多步推理能力", "parentId": 16, "contentCategory": "knowledge", "iconUrl": "/icons/task-planning.svg", "subTypeId": 9, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:17:00.000Z", "updatedAt": "2025-07-20T10:17:00.000Z"}, {"id": 19, "name": "代码生成AI", "description": "AI辅助编程、代码自动生成和智能开发", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/code-generation.svg", "subTypeId": 1, "sortOrder": 7, "isActive": true, "createdAt": "2025-07-20T10:18:00.000Z", "updatedAt": "2025-07-20T10:18:00.000Z"}, {"id": 20, "name": "Python代码生成", "description": "AI辅助Python编程和代码优化技术", "parentId": 19, "contentCategory": "knowledge", "iconUrl": "/icons/python-ai.svg", "subTypeId": 2, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:19:00.000Z", "updatedAt": "2025-07-20T10:19:00.000Z"}, {"id": 21, "name": "JavaScript AI开发", "description": "前端和后端JavaScript的AI辅助开发", "parentId": 19, "contentCategory": "knowledge", "iconUrl": "/icons/javascript-ai.svg", "subTypeId": 3, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:20:00.000Z", "updatedAt": "2025-07-20T10:20:00.000Z"}, {"id": 22, "name": "自然语言处理", "description": "NLP技术、文本分析和语言理解方法", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/nlp.svg", "subTypeId": 4, "sortOrder": 8, "isActive": true, "createdAt": "2025-07-20T10:21:00.000Z", "updatedAt": "2025-07-20T10:21:00.000Z"}, {"id": 23, "name": "文本分类技术", "description": "基于大模型的文本分类和情感分析", "parentId": 22, "contentCategory": "knowledge", "iconUrl": "/icons/text-classification.svg", "subTypeId": 5, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:22:00.000Z", "updatedAt": "2025-07-20T10:22:00.000Z"}, {"id": 24, "name": "命名实体识别", "description": "NER技术和实体抽取的AI方法", "parentId": 22, "contentCategory": "knowledge", "iconUrl": "/icons/ner.svg", "subTypeId": 6, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:23:00.000Z", "updatedAt": "2025-07-20T10:23:00.000Z"}, {"id": 25, "name": "机器翻译技术", "description": "神经机器翻译和多语言大模型", "parentId": 22, "contentCategory": "knowledge", "iconUrl": "/icons/machine-translation.svg", "subTypeId": 7, "sortOrder": 3, "isActive": true, "createdAt": "2025-07-20T10:24:00.000Z", "updatedAt": "2025-07-20T10:24:00.000Z"}, {"id": 26, "name": "计算机视觉AI", "description": "CV技术、图像处理和视觉AI算法", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/computer-vision.svg", "subTypeId": 8, "sortOrder": 9, "isActive": true, "createdAt": "2025-07-20T10:25:00.000Z", "updatedAt": "2025-07-20T10:25:00.000Z"}, {"id": 27, "name": "图像生成模型", "description": "DALL-E、Midjourney等AI图像生成技术", "parentId": 26, "contentCategory": "knowledge", "iconUrl": "/icons/image-generation.svg", "subTypeId": 9, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:26:00.000Z", "updatedAt": "2025-07-20T10:26:00.000Z"}, {"id": 28, "name": "目标检测算法", "description": "YOLO、R-CNN等AI目标检测技术", "parentId": 26, "contentCategory": "knowledge", "iconUrl": "/icons/object-detection.svg", "subTypeId": 1, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:27:00.000Z", "updatedAt": "2025-07-20T10:27:00.000Z"}, {"id": 29, "name": "推荐系统AI", "description": "AI驱动的个性化推荐算法和系统", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/recommendation-ai.svg", "subTypeId": 2, "sortOrder": 10, "isActive": true, "createdAt": "2025-07-20T10:28:00.000Z", "updatedAt": "2025-07-20T10:28:00.000Z"}, {"id": 30, "name": "协同过滤算法", "description": "基于用户和物品的AI协同过滤技术", "parentId": 29, "contentCategory": "knowledge", "iconUrl": "/icons/collaborative-filtering.svg", "subTypeId": 3, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:29:00.000Z", "updatedAt": "2025-07-20T10:29:00.000Z"}, {"id": 31, "name": "深度推荐模型", "description": "神经网络在推荐系统中的AI应用", "parentId": 29, "contentCategory": "knowledge", "iconUrl": "/icons/deep-recommendation.svg", "subTypeId": 4, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:30:00.000Z", "updatedAt": "2025-07-20T10:30:00.000Z"}, {"id": 32, "name": "知识图谱AI", "description": "知识表示、图神经网络和AI推理", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/knowledge-graph.svg", "subTypeId": 5, "sortOrder": 11, "isActive": true, "createdAt": "2025-07-20T10:31:00.000Z", "updatedAt": "2025-07-20T10:31:00.000Z"}, {"id": 33, "name": "实体关系抽取", "description": "从文本中抽取实体和关系的AI技术", "parentId": 32, "contentCategory": "knowledge", "iconUrl": "/icons/entity-relation.svg", "subTypeId": 6, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:32:00.000Z", "updatedAt": "2025-07-20T10:32:00.000Z"}, {"id": 34, "name": "图神经网络", "description": "GNN在知识图谱和AI推理中的应用", "parentId": 32, "contentCategory": "knowledge", "iconUrl": "/icons/gnn.svg", "subTypeId": 7, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:33:00.000Z", "updatedAt": "2025-07-20T10:33:00.000Z"}, {"id": 35, "name": "对话系统AI", "description": "聊天机器人和对话AI技术系统", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/dialogue-ai.svg", "subTypeId": 8, "sortOrder": 12, "isActive": true, "createdAt": "2025-07-20T10:34:00.000Z", "updatedAt": "2025-07-20T10:34:00.000Z"}, {"id": 36, "name": "意图识别技术", "description": "用户意图理解和AI分类技术", "parentId": 35, "contentCategory": "knowledge", "iconUrl": "/icons/intent-recognition.svg", "subTypeId": 9, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:35:00.000Z", "updatedAt": "2025-07-20T10:35:00.000Z"}, {"id": 37, "name": "对话状态管理", "description": "多轮对话状态跟踪和AI管理", "parentId": 35, "contentCategory": "knowledge", "iconUrl": "/icons/dialogue-management.svg", "subTypeId": 1, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:36:00.000Z", "updatedAt": "2025-07-20T10:36:00.000Z"}, {"id": 38, "name": "AI模型评估", "description": "AI模型性能评估指标和测试方法", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/model-evaluation.svg", "subTypeId": 2, "sortOrder": 13, "isActive": true, "createdAt": "2025-07-20T10:37:00.000Z", "updatedAt": "2025-07-20T10:37:00.000Z"}, {"id": 39, "name": "基准测试体系", "description": "GLUE、SuperGLUE等AI评估基准", "parentId": 38, "contentCategory": "knowledge", "iconUrl": "/icons/benchmark.svg", "subTypeId": 3, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:38:00.000Z", "updatedAt": "2025-07-20T10:38:00.000Z"}, {"id": 40, "name": "A/B测试方法", "description": "AI系统的在线评估和对比测试", "parentId": 38, "contentCategory": "knowledge", "iconUrl": "/icons/ab-testing.svg", "subTypeId": 4, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:39:00.000Z", "updatedAt": "2025-07-20T10:39:00.000Z"}, {"id": 41, "name": "AI安全技术", "description": "人工智能安全、对齐和风险控制技术", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/ai-safety.svg", "subTypeId": 5, "sortOrder": 14, "isActive": true, "createdAt": "2025-07-20T10:40:00.000Z", "updatedAt": "2025-07-20T10:40:00.000Z"}, {"id": 42, "name": "对抗攻击防御", "description": "AI模型的对抗样本攻击和防御技术", "parentId": 41, "contentCategory": "knowledge", "iconUrl": "/icons/adversarial-defense.svg", "subTypeId": 6, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:41:00.000Z", "updatedAt": "2025-07-20T10:41:00.000Z"}, {"id": 43, "name": "隐私保护AI", "description": "差分隐私和联邦学习等隐私保护技术", "parentId": 41, "contentCategory": "knowledge", "iconUrl": "/icons/privacy-ai.svg", "subTypeId": 7, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:42:00.000Z", "updatedAt": "2025-07-20T10:42:00.000Z"}, {"id": 44, "name": "可解释AI技术", "description": "AI模型可解释性和透明度技术", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/explainable-ai.svg", "subTypeId": 8, "sortOrder": 15, "isActive": true, "createdAt": "2025-07-20T10:43:00.000Z", "updatedAt": "2025-07-20T10:43:00.000Z"}, {"id": 45, "name": "注意力可视化", "description": "Transformer注意力权重分析和可视化", "parentId": 44, "contentCategory": "knowledge", "iconUrl": "/icons/attention-viz.svg", "subTypeId": 9, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:44:00.000Z", "updatedAt": "2025-07-20T10:44:00.000Z"}, {"id": 46, "name": "特征重要性分析", "description": "SHAP、LIME等AI特征解释方法", "parentId": 44, "contentCategory": "knowledge", "iconUrl": "/icons/feature-importance.svg", "subTypeId": 1, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:45:00.000Z", "updatedAt": "2025-07-20T10:45:00.000Z"}, {"id": 47, "name": "AutoML技术", "description": "自动化机器学习和神经架构搜索", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/automl.svg", "subTypeId": 2, "sortOrder": 16, "isActive": true, "createdAt": "2025-07-20T10:46:00.000Z", "updatedAt": "2025-07-20T10:46:00.000Z"}, {"id": 48, "name": "神经架构搜索", "description": "NAS技术和AI架构自动优化", "parentId": 47, "contentCategory": "knowledge", "iconUrl": "/icons/nas.svg", "subTypeId": 3, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:47:00.000Z", "updatedAt": "2025-07-20T10:47:00.000Z"}, {"id": 49, "name": "超参数优化", "description": "贝叶斯优化和AI超参数自动调优", "parentId": 47, "contentCategory": "knowledge", "iconUrl": "/icons/hyperparameter-opt.svg", "subTypeId": 4, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:48:00.000Z", "updatedAt": "2025-07-20T10:48:00.000Z"}, {"id": 50, "name": "边缘AI计算", "description": "移动设备和边缘计算中的AI技术", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/edge-ai.svg", "subTypeId": 5, "sortOrder": 17, "isActive": true, "createdAt": "2025-07-20T10:49:00.000Z", "updatedAt": "2025-07-20T10:49:00.000Z"}, {"id": 51, "name": "模型轻量化", "description": "知识蒸馏和AI模型压缩技术", "parentId": 12, "contentCategory": "knowledge", "iconUrl": "/icons/model-compression.svg", "subTypeId": 6, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:50:00.000Z", "updatedAt": "2025-07-20T10:50:00.000Z"}, {"id": 52, "name": "移动端AI部署", "description": "TensorFlow Lite和移动AI部署技术", "parentId": 50, "contentCategory": "knowledge", "iconUrl": "/icons/mobile-ai.svg", "subTypeId": 7, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:51:00.000Z", "updatedAt": "2025-07-20T10:51:00.000Z"}, {"id": 53, "name": "生成式AI技术", "description": "内容生成和创意AI技术系统", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/generative-ai.svg", "subTypeId": 8, "sortOrder": 18, "isActive": true, "createdAt": "2025-07-20T10:52:00.000Z", "updatedAt": "2025-07-20T10:52:00.000Z"}, {"id": 54, "name": "AI文本生成", "description": "GPT系列模型的文本创作和生成能力", "parentId": 53, "contentCategory": "knowledge", "iconUrl": "/icons/text-generation.svg", "subTypeId": 9, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:53:00.000Z", "updatedAt": "2025-07-20T10:53:00.000Z"}, {"id": 55, "name": "AI音乐生成", "description": "AI音乐创作和音频合成技术", "parentId": 53, "contentCategory": "knowledge", "iconUrl": "/icons/music-generation.svg", "subTypeId": 1, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:54:00.000Z", "updatedAt": "2025-07-20T10:54:00.000Z"}, {"id": 56, "name": "AI视频生成", "description": "Sora等AI视频生成模型技术", "parentId": 53, "contentCategory": "knowledge", "iconUrl": "/icons/video-generation.svg", "subTypeId": 2, "sortOrder": 3, "isActive": true, "createdAt": "2025-07-20T10:55:00.000Z", "updatedAt": "2025-07-20T10:55:00.000Z"}, {"id": 57, "name": "MLOps运维", "description": "机器学习运维和AI生产化管理", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/mlops.svg", "subTypeId": 3, "sortOrder": 19, "isActive": true, "createdAt": "2025-07-20T10:56:00.000Z", "updatedAt": "2025-07-20T10:56:00.000Z"}, {"id": 58, "name": "AI模型监控", "description": "生产环境中的AI模型性能监控", "parentId": 57, "contentCategory": "knowledge", "iconUrl": "/icons/model-monitoring.svg", "subTypeId": 4, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T10:57:00.000Z", "updatedAt": "2025-07-20T10:57:00.000Z"}, {"id": 59, "name": "AI版本控制", "description": "AI模型和数据的版本管理系统", "parentId": 57, "contentCategory": "knowledge", "iconUrl": "/icons/ai-version-control.svg", "subTypeId": 5, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T10:58:00.000Z", "updatedAt": "2025-07-20T10:58:00.000Z"}, {"id": 60, "name": "AI伦理治理", "description": "人工智能伦理和社会责任治理", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/ai-ethics.svg", "subTypeId": 6, "sortOrder": 20, "isActive": true, "createdAt": "2025-07-20T10:59:00.000Z", "updatedAt": "2025-07-20T10:59:00.000Z"}, {"id": 61, "name": "算法公平性", "description": "消除AI系统中的偏见和歧视技术", "parentId": 60, "contentCategory": "knowledge", "iconUrl": "/icons/algorithmic-fairness.svg", "subTypeId": 7, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:00:00.000Z", "updatedAt": "2025-07-20T11:00:00.000Z"}, {"id": 62, "name": "责任AI开发", "description": "可信赖和负责任的AI开发实践", "parentId": 60, "contentCategory": "knowledge", "iconUrl": "/icons/responsible-ai.svg", "subTypeId": 8, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:01:00.000Z", "updatedAt": "2025-07-20T11:01:00.000Z"}, {"id": 63, "name": "量子机器学习", "description": "量子计算在AI和机器学习中的应用", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/quantum-ml.svg", "subTypeId": 9, "sortOrder": 21, "isActive": true, "createdAt": "2025-07-20T11:02:00.000Z", "updatedAt": "2025-07-20T11:02:00.000Z"}, {"id": 64, "name": "量子神经网络", "description": "QNN和变分量子AI算法", "parentId": 63, "contentCategory": "knowledge", "iconUrl": "/icons/quantum-neural-network.svg", "subTypeId": 1, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:03:00.000Z", "updatedAt": "2025-07-20T11:03:00.000Z"}, {"id": 65, "name": "量子优化算法", "description": "量子退火和QAOA优化算法", "parentId": 63, "contentCategory": "knowledge", "iconUrl": "/icons/quantum-optimization.svg", "subTypeId": 2, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:04:00.000Z", "updatedAt": "2025-07-20T11:04:00.000Z"}, {"id": 66, "name": "神经形态计算", "description": "类脑芯片和脉冲神经网络技术", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/neuromorphic.svg", "subTypeId": 3, "sortOrder": 22, "isActive": true, "createdAt": "2025-07-20T11:05:00.000Z", "updatedAt": "2025-07-20T11:05:00.000Z"}, {"id": 67, "name": "脉冲神经网络", "description": "SNN和时序信息处理技术", "parentId": 66, "contentCategory": "knowledge", "iconUrl": "/icons/spiking-neural-network.svg", "subTypeId": 4, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:06:00.000Z", "updatedAt": "2025-07-20T11:06:00.000Z"}, {"id": 68, "name": "忆阻器AI网络", "description": "忆阻器在神经网络中的AI应用", "parentId": 66, "contentCategory": "knowledge", "iconUrl": "/icons/memristor.svg", "subTypeId": 5, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:07:00.000Z", "updatedAt": "2025-07-20T11:07:00.000Z"}, {"id": 69, "name": "元学习技术", "description": "学会学习和快速适应的AI技术", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/meta-learning.svg", "subTypeId": 6, "sortOrder": 23, "isActive": true, "createdAt": "2025-07-20T11:08:00.000Z", "updatedAt": "2025-07-20T11:08:00.000Z"}, {"id": 70, "name": "MAML算法", "description": "模型无关的元学习AI方法", "parentId": 69, "contentCategory": "knowledge", "iconUrl": "/icons/maml.svg", "subTypeId": 7, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:09:00.000Z", "updatedAt": "2025-07-20T11:09:00.000Z"}, {"id": 71, "name": "原型网络学习", "description": "基于原型的少样本AI学习", "parentId": 69, "contentCategory": "knowledge", "iconUrl": "/icons/prototypical-network.svg", "subTypeId": 8, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:10:00.000Z", "updatedAt": "2025-07-20T11:10:00.000Z"}, {"id": 72, "name": "联邦学习技术", "description": "分布式机器学习和隐私保护AI", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/federated-learning.svg", "subTypeId": 9, "sortOrder": 24, "isActive": true, "createdAt": "2025-07-20T11:11:00.000Z", "updatedAt": "2025-07-20T11:11:00.000Z"}, {"id": 73, "name": "FedAvg算法", "description": "联邦平均和参数聚合AI算法", "parentId": 72, "contentCategory": "knowledge", "iconUrl": "/icons/fedavg.svg", "subTypeId": 1, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:12:00.000Z", "updatedAt": "2025-07-20T11:12:00.000Z"}, {"id": 74, "name": "个性化联邦AI", "description": "pFedMe和个性化AI模型训练", "parentId": 72, "contentCategory": "knowledge", "iconUrl": "/icons/personalized-fl.svg", "subTypeId": 2, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:13:00.000Z", "updatedAt": "2025-07-20T11:13:00.000Z"}, {"id": 75, "name": "持续学习AI", "description": "终身学习和灾难性遗忘解决方案", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/continual-learning.svg", "subTypeId": 3, "sortOrder": 25, "isActive": true, "createdAt": "2025-07-20T11:14:00.000Z", "updatedAt": "2025-07-20T11:14:00.000Z"}, {"id": 76, "name": "弹性权重巩固", "description": "EWC和重要性权重保护技术", "parentId": 75, "contentCategory": "knowledge", "iconUrl": "/icons/ewc.svg", "subTypeId": 4, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:15:00.000Z", "updatedAt": "2025-07-20T11:15:00.000Z"}, {"id": 77, "name": "渐进神经网络", "description": "Progressive Networks和任务特定AI模块", "parentId": 75, "contentCategory": "knowledge", "iconUrl": "/icons/progressive-networks.svg", "subTypeId": 5, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:16:00.000Z", "updatedAt": "2025-07-20T11:16:00.000Z"}, {"id": 78, "name": "因果推理AI", "description": "因果关系发现和AI推断技术", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/causal-inference.svg", "subTypeId": 6, "sortOrder": 26, "isActive": true, "createdAt": "2025-07-20T11:17:00.000Z", "updatedAt": "2025-07-20T11:17:00.000Z"}, {"id": 79, "name": "因果图模型", "description": "DAG和结构因果AI模型", "parentId": 78, "contentCategory": "knowledge", "iconUrl": "/icons/causal-graph.svg", "subTypeId": 7, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:18:00.000Z", "updatedAt": "2025-07-20T11:18:00.000Z"}, {"id": 80, "name": "反事实AI推理", "description": "反事实分析和因果效应AI估计", "parentId": 78, "contentCategory": "knowledge", "iconUrl": "/icons/counterfactual.svg", "subTypeId": 8, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:19:00.000Z", "updatedAt": "2025-07-20T11:19:00.000Z"}, {"id": 81, "name": "具身智能AI", "description": "机器人学习和环境交互AI技术", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/embodied-ai.svg", "subTypeId": 9, "sortOrder": 27, "isActive": true, "createdAt": "2025-07-20T11:20:00.000Z", "updatedAt": "2025-07-20T11:20:00.000Z"}, {"id": 82, "name": "机器人AI学习", "description": "机器人技能学习和模仿学习AI", "parentId": 81, "contentCategory": "knowledge", "iconUrl": "/icons/robot-learning.svg", "subTypeId": 1, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:21:00.000Z", "updatedAt": "2025-07-20T11:21:00.000Z"}, {"id": 83, "name": "环境感知AI", "description": "SLAM和3D场景理解AI技术", "parentId": 81, "contentCategory": "knowledge", "iconUrl": "/icons/environment-perception.svg", "subTypeId": 2, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:22:00.000Z", "updatedAt": "2025-07-20T11:22:00.000Z"}, {"id": 84, "name": "神经符号AI", "description": "神经网络与符号推理的AI结合", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/neuro-symbolic.svg", "subTypeId": 3, "sortOrder": 28, "isActive": true, "createdAt": "2025-07-20T11:23:00.000Z", "updatedAt": "2025-07-20T11:23:00.000Z"}, {"id": 85, "name": "逻辑推理AI", "description": "符号逻辑和定理证明AI技术", "parentId": 84, "contentCategory": "knowledge", "iconUrl": "/icons/logical-reasoning.svg", "subTypeId": 4, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:24:00.000Z", "updatedAt": "2025-07-20T11:24:00.000Z"}, {"id": 86, "name": "概念学习AI", "description": "概念表示和抽象推理AI技术", "parentId": 84, "contentCategory": "knowledge", "iconUrl": "/icons/concept-learning.svg", "subTypeId": 5, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:25:00.000Z", "updatedAt": "2025-07-20T11:25:00.000Z"}, {"id": 87, "name": "大模型应用场景", "description": "大模型在各行业的实际应用案例", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/llm-applications.svg", "subTypeId": 6, "sortOrder": 29, "isActive": true, "createdAt": "2025-07-20T11:26:00.000Z", "updatedAt": "2025-07-20T11:26:00.000Z"}, {"id": 88, "name": "智能客服AI", "description": "基于大模型的客户服务AI系统", "parentId": 87, "contentCategory": "knowledge", "iconUrl": "/icons/intelligent-customer-service.svg", "subTypeId": 7, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:27:00.000Z", "updatedAt": "2025-07-20T11:27:00.000Z"}, {"id": 89, "name": "AI智能写作", "description": "AI辅助内容创作和编辑系统", "parentId": 87, "contentCategory": "knowledge", "iconUrl": "/icons/ai-writing.svg", "subTypeId": 8, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:28:00.000Z", "updatedAt": "2025-07-20T11:28:00.000Z"}, {"id": 90, "name": "教育AI系统", "description": "个性化学习和智能辅导AI", "parentId": 87, "contentCategory": "knowledge", "iconUrl": "/icons/education-ai.svg", "subTypeId": 9, "sortOrder": 3, "isActive": true, "createdAt": "2025-07-20T11:29:00.000Z", "updatedAt": "2025-07-20T11:29:00.000Z"}, {"id": 91, "name": "医疗AI应用", "description": "AI在医疗诊断和药物发现中的应用", "parentId": 87, "contentCategory": "knowledge", "iconUrl": "/icons/medical-ai.svg", "subTypeId": 1, "sortOrder": 4, "isActive": true, "createdAt": "2025-07-20T11:30:00.000Z", "updatedAt": "2025-07-20T11:30:00.000Z"}, {"id": 92, "name": "金融AI技术", "description": "智能投顾和风险控制AI系统", "parentId": 87, "contentCategory": "knowledge", "iconUrl": "/icons/fintech-ai.svg", "subTypeId": 2, "sortOrder": 5, "isActive": true, "createdAt": "2025-07-20T11:31:00.000Z", "updatedAt": "2025-07-20T11:31:00.000Z"}, {"id": 93, "name": "AI治理政策", "description": "人工智能治理和监管政策框架", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/ai-governance.svg", "subTypeId": 3, "sortOrder": 30, "isActive": true, "createdAt": "2025-07-20T11:32:00.000Z", "updatedAt": "2025-07-20T11:32:00.000Z"}, {"id": 94, "name": "AI标准制定", "description": "人工智能技术标准和规范制定", "parentId": 93, "contentCategory": "knowledge", "iconUrl": "/icons/ai-standards.svg", "subTypeId": 4, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:33:00.000Z", "updatedAt": "2025-07-20T11:33:00.000Z"}, {"id": 95, "name": "AI合规监管", "description": "AI系统合规性检查和监管要求", "parentId": 93, "contentCategory": "knowledge", "iconUrl": "/icons/ai-compliance.svg", "subTypeId": 5, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:34:00.000Z", "updatedAt": "2025-07-20T11:34:00.000Z"}, {"id": 96, "name": "AI产业生态", "description": "人工智能产业链和生态系统发展", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/ai-ecosystem.svg", "subTypeId": 6, "sortOrder": 31, "isActive": true, "createdAt": "2025-07-20T11:35:00.000Z", "updatedAt": "2025-07-20T11:35:00.000Z"}, {"id": 97, "name": "AI芯片技术", "description": "专用AI芯片和硬件加速技术", "parentId": 96, "contentCategory": "knowledge", "iconUrl": "/icons/ai-chips.svg", "subTypeId": 7, "sortOrder": 1, "isActive": true, "createdAt": "2025-07-20T11:36:00.000Z", "updatedAt": "2025-07-20T11:36:00.000Z"}, {"id": 98, "name": "AI云服务平台", "description": "云端AI服务和平台化解决方案", "parentId": 96, "contentCategory": "knowledge", "iconUrl": "/icons/ai-cloud.svg", "subTypeId": 8, "sortOrder": 2, "isActive": true, "createdAt": "2025-07-20T11:37:00.000Z", "updatedAt": "2025-07-20T11:37:00.000Z"}, {"id": 99, "name": "AI开源社区", "description": "人工智能开源项目和社区生态", "parentId": 96, "contentCategory": "knowledge", "iconUrl": "/icons/ai-opensource.svg", "subTypeId": 9, "sortOrder": 3, "isActive": true, "createdAt": "2025-07-20T11:38:00.000Z", "updatedAt": "2025-07-20T11:38:00.000Z"}, {"id": 100, "name": "AI未来趋势", "description": "人工智能技术发展趋势和未来展望", "parentId": null, "contentCategory": "knowledge", "iconUrl": "/icons/ai-future.svg", "subTypeId": 1, "sortOrder": 32, "isActive": true, "createdAt": "2025-07-20T11:39:00.000Z", "updatedAt": "2025-07-20T11:39:00.000Z"}]