[{"id": 1, "code": "prompt", "name": "提示词", "description": "AI提示词模板、优化技巧和最佳实践，帮助提升AI交互效果", "iconUrl": "/icons/prompt.svg", "isActive": true, "sortOrder": 1, "isRecommended": true, "count": 25, "tags": ["AI", "提示词", "优化", "模板"], "category": "AI工具", "lastUpdated": "2025-01-18T10:30:00Z", "renderConfigJson": {"display_template_id": "prompt-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_prompt", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["Prompt信息"], "main_sections": ["模型参数配置"]}, "search_fields": ["use_case", "target_model"], "display_sections": [{"title": "Prompt信息", "fields": ["target_model", "use_case", "variables_count", "effectiveness_rating", "test_url"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}, {"title": "模型参数配置", "fields": ["model_parameters", "target_model"], "component": "ModelParametersDisplay", "layout": "parameter_grid", "position": "main", "collapsible": false, "enable_parameter_slider": false, "show_parameter_description": true, "enable_preset_templates": false, "show_performance_tips": true}], "prompt_integration": {"enable_test_link": true, "test_url": "https://chat.deepseek.com/", "test_fields": ["target_model", "use_case"]}, "list_view_config": {"card_template": "SimplifiedPromptCard", "preview_fields": ["use_case", "target_model", "variables_count", "effectiveness_rating"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "effectiveness_rating", "label": "效果评分", "direction": "desc"}, {"field": "variables_count", "label": "变量数量", "direction": "asc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}], "filter_options": [{"field": "target_model", "label": "适用模型", "type": "select"}, {"field": "use_case", "label": "适用场景", "type": "select"}, {"field": "variables_count", "label": "变量数量", "type": "range"}, {"field": "effectiveness_rating", "label": "效果评分", "type": "range"}]}}, "communityConfigJson": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt Social Features Configuration", "description": "Prompt类型的社交功能配置", "version": "2.0.0", "social_features": {"like": {"enabled": true, "display_name": "点赞", "icon": "fas fa-heart", "color": "#ef4444", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 1}, "favorite": {"enabled": true, "display_name": "收藏", "icon": "fas fa-bookmark", "color": "#f59e0b", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 2, "folder_support": true}, "share": {"enabled": true, "display_name": "分享", "icon": "fas fa-share-alt", "color": "#10b981", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 3}, "comment": {"enabled": true, "display_name": "评论", "icon": "fas fa-comment", "color": "#6366f1", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 4}, "follow": {"enabled": true, "display_name": "关注", "icon": "fas fa-user-plus", "color": "#8b5cf6", "show_count": false, "show_in_list": true, "show_in_detail": true, "priority": 5}, "fork": {"enabled": true, "display_name": "Fork", "icon": "fas fa-code-branch", "color": "#8b5cf6", "show_count": true, "show_in_list": true, "show_in_detail": true, "priority": 6}, "read": {"enabled": true, "display_name": "阅读", "icon": "fas fa-eye", "color": "#6b7280", "show_count": true, "show_in_list": false, "show_in_detail": true, "priority": 7, "track_progress": true}}, "share_options": [{"type": "internal", "display_name": "站内分享", "icon": "fas fa-users", "enabled": true, "order": 1}, {"type": "link", "display_name": "复制链接", "icon": "fas fa-link", "enabled": true, "order": 2}, {"type": "wechat", "display_name": "微信", "icon": "fab fa-weixin", "enabled": true, "order": 3}, {"type": "email", "display_name": "邮件", "icon": "fas fa-envelope", "enabled": true, "order": 4}, {"type": "twitter", "display_name": "Twitter", "icon": "fab fa-twitter", "enabled": true, "order": 5}, {"type": "linkedin", "display_name": "LinkedIn", "icon": "fab fa-linkedin", "enabled": true, "order": 6}, {"type": "prompt_share", "display_name": "Prompt分享", "icon": "fas fa-code", "enabled": true, "order": 7}], "ui_config": {"layout": {"list_view": {"layout": "horizontal", "size": "medium", "theme": "light", "show_labels": false, "icon_only": false, "max_visible_features": 4}, "detail_view": {"layout": "horizontal", "size": "large", "theme": "light", "show_labels": true, "icon_only": false, "max_visible_features": 7}}, "animation": {"enabled": true, "duration": 200, "easing": "easeOutCubic"}, "feedback": {"show_tooltips": true, "show_success_messages": true, "show_error_messages": true}}, "display_priority": ["like", "favorite", "fork", "share", "comment", "follow", "read"], "content_type_config": {"content_type": "prompt", "supports_rating": true, "supports_tagging": true, "supports_forking": true, "moderation_level": "basic", "auto_approve_comments": true}, "permissions": {"guest_can_view_stats": true, "guest_can_like": false, "guest_can_favorite": false, "guest_can_share": true, "guest_can_comment": false, "require_login_for_actions": true}, "notifications": {"notify_on_like": false, "notify_on_favorite": false, "notify_on_share": false, "notify_on_comment": true, "notify_on_follow": true, "notify_on_fork": true, "notify_on_improvement_suggestion": true, "notify_on_high_rating": true, "notify_on_milestone_usage": true}, "stats_config": {"show_detailed_stats": true, "show_trends": true, "cache_duration": 300, "real_time_updates": false}, "can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options_legacy": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin"], "can_test_online": true, "test_api_endpoint_template": "/api/v1/prompt/test/{{knowledge_id}}", "test_requires_credentials": false, "test_ui_config": {"show_model_selector": true, "show_parameter_editor": true, "show_variable_editor": true, "enable_real_time_preview": true}, "can_suggest_improvements": true, "improvement_suggestion_types": ["variable_optimization", "template_enhancement", "parameter_tuning", "use_case_expansion"], "can_rate_effectiveness": true, "effectiveness_rating_criteria": ["accuracy", "creativity", "usefulness", "clarity"], "show_version_history": true, "version_comparison_enabled": true, "can_create_variants": true, "variant_creation_options": {"can_modify_variables": true, "can_change_model": true, "can_adjust_parameters": true, "require_attribution": true}, "community_features": {"show_usage_stats": true, "show_success_stories": true, "enable_community_tags": true, "allow_user_examples": true}, "moderation": {"auto_review_enabled": true, "content_filters": ["inappropriate_content", "spam_detection", "quality_check"], "community_reporting": true}, "analytics": {"track_test_usage": true, "track_fork_success": true, "track_effectiveness_ratings": true, "generate_usage_insights": true}, "gamification": {"award_points_for_creation": 10, "award_points_for_high_rating": 5, "award_points_for_popular_fork": 3, "enable_achievement_badges": true, "achievement_types": ["prompt_master", "community_favorite", "innovation_leader", "helpful_contributor"]}}, "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Prompt <PERSON><PERSON><PERSON>", "description": "Prompt的metadata_json结构定义", "type": "object", "properties": {"target_model": {"type": "string", "title": "适用模型", "description": "推荐或限制使用的AI模型", "enum": ["gpt-4-turbo", "gpt-4o", "claude-3-opus", "claude-3-sonnet", "ernie-bot-4", "llama-3-8b", "llama-3-70b", "gemini-pro", "qwen-max", "other"]}, "use_case": {"type": "string", "title": "适用场景", "description": "此提示词主要应用的业务场景", "minLength": 2, "maxLength": 100, "examples": ["内容生成", "代码审查", "数据分析", "翻译润色", "创意写作", "技术文档", "客服对话", "教育培训"]}, "variables_count": {"type": "integer", "title": "变量数量", "description": "提示词模板中包含的变量总数", "minimum": 0, "maximum": 20, "default": 0}, "effectiveness_rating": {"type": "number", "title": "效果评分", "description": "社区用户对此提示词效果的平均评分", "minimum": 1, "maximum": 5, "default": 0}, "test_url": {"type": "string", "title": "测试使用", "description": "在线测试平台链接", "format": "uri", "default": "https://chat.deepseek.com/"}, "model_parameters": {"type": "object", "title": "模型参数配置", "description": "AI模型调用的推荐参数配置", "properties": {"temperature": {"type": "number", "title": "温度参数", "description": "控制输出的随机性，0-2之间", "minimum": 0, "maximum": 2, "default": 0.7}, "max_tokens": {"type": "integer", "title": "最大令牌数", "description": "生成内容的最大长度", "minimum": 1, "maximum": 8192, "default": 500}, "top_p": {"type": "number", "title": "Top-P参数", "description": "核采样参数，0-1之间", "minimum": 0, "maximum": 1, "default": 0.9}, "frequency_penalty": {"type": "number", "title": "频率惩罚", "description": "减少重复内容的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}, "presence_penalty": {"type": "number", "title": "存在惩罚", "description": "鼓励谈论新话题的惩罚系数", "minimum": -2, "maximum": 2, "default": 0}}, "additionalProperties": false}}, "required": ["target_model", "use_case", "variables_count", "effectiveness_rating"], "additionalProperties": false, "examples": [{"target_model": "gpt-4-turbo", "use_case": "内容生成", "variables_count": 5, "effectiveness_rating": 4.5, "test_url": "https://chat.deepseek.com/", "input_example": "role: 产品经理, requirements: 为新的AI助手产品写一份功能介绍", "output_example": "作为产品经理，我为您介绍这款AI助手的核心功能：\n1. 智能对话：支持多轮对话，理解上下文\n2. 知识问答：基于大规模知识库回答问题\n3. 内容生成：协助创作文档、邮件、报告等\n4. 代码辅助：提供编程建议和代码优化\n5. 多语言支持：支持中英文等多种语言交互", "model_parameters": {"temperature": 0.7, "max_tokens": 500, "top_p": 0.9}}]}}, {"id": 2, "code": "mcp", "name": "MCP服务", "description": "Model Context Protocol服务的开发、部署和集成指南", "iconUrl": "/icons/mcp_service.svg", "isActive": true, "sortOrder": 2, "isRecommended": true, "count": 25, "tags": ["MCP", "协议", "服务", "集成"], "category": "技术服务", "lastUpdated": "2025-01-18T10:30:00Z", "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "MCP Service Metadata Schema", "description": "MCP服务的metadata_json结构定义 - 精简版，仅包含核心信息", "type": "object", "properties": {"service_type": {"type": "string", "title": "服务类型", "description": "服务部署类型", "enum": ["Local", "Remote"]}, "service_source": {"type": "string", "title": "服务来源", "description": "服务的来源类型", "enum": ["开源", "内部"]}, "protocol_type": {"type": "string", "title": "协议类型", "description": "MCP服务使用的协议类型", "enum": ["Stdio", "SSE", "HTTP"]}, "service_homepage": {"type": "string", "title": "服务主页", "description": "服务的官方主页或文档地址", "format": "uri", "examples": ["https://github.com/modelcontextprotocol/servers", "https://docs.mcp.com/filesystem-service"]}, "installation_deployment": {"type": "object", "title": "安装部署", "description": "服务的安装和部署信息", "properties": {"installation_command": {"type": "string", "title": "安装命令", "description": "一键安装命令", "maxLength": 500, "examples": ["npm install @modelcontextprotocol/server-filesystem", "pip install mcp-server-database", "docker pull mcp/web-search-service"]}, "installation_steps": {"type": "array", "title": "安装步骤", "description": "详细的安装步骤", "items": {"type": "object", "properties": {"title": {"type": "string", "title": "步骤标题", "maxLength": 100}, "description": {"type": "string", "title": "步骤描述", "maxLength": 300}, "command": {"type": "string", "title": "执行命令", "maxLength": 500}, "language": {"type": "string", "title": "命令语言", "enum": ["bash", "powershell", "cmd", "javascript", "python", "json", "yaml"], "default": "bash"}}, "required": ["title", "description"], "additionalProperties": false}, "maxItems": 10}}, "required": ["installation_command"], "additionalProperties": false}}, "required": ["service_type", "service_source", "protocol_type", "service_homepage", "installation_deployment"], "additionalProperties": false, "examples": [{"service_type": "Local", "service_source": "开源", "protocol_type": "Stdio", "service_homepage": "https://github.com/modelcontextprotocol/servers", "installation_deployment": {"installation_command": "npm install @modelcontextprotocol/server-filesystem", "installation_steps": [{"title": "安装服务", "description": "使用npm安装MCP文件系统服务", "command": "npm install @modelcontextprotocol/server-filesystem", "language": "bash"}, {"title": "配置服务", "description": "在Claude Desktop配置文件中添加服务配置", "command": "{\n  \"mcpServers\": {\n    \"filesystem\": {\n      \"command\": \"npx\",\n      \"args\": [\"@modelcontextprotocol/server-filesystem\", \"/path/to/allowed/directory\"]\n    }\n  }\n}", "language": "json"}]}}]}, "renderConfigJson": {"display_template_id": "mcp-service-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_service", "header_style": "clean_header", "enable_code_highlighting": true, "enable_installation_wizard": true, "sidebar_sections": ["服务信息"], "main_sections": ["安装部署"]}, "search_fields": ["service_type", "service_source", "protocol_type", "installation_deployment"], "display_sections": [{"title": "服务信息", "subtitle": "MCP服务基本信息", "component": "ServiceInfoCard", "fields": ["service_type", "service_source", "protocol_type", "service_homepage"], "layout": "info_grid", "position": "sidebar", "collapsible": false, "bordered": true, "elevated": true, "actions": [{"key": "visit-homepage", "label": "访问主页", "icon": "fas fa-external-link-alt", "variant": "primary", "handler": "visitServiceHomepage"}], "fieldConfig": {"service_type": {"title": "服务类型", "icon": "fas fa-server", "variant": "primary"}, "service_source": {"title": "服务来源", "icon": "fas fa-code", "variant": "info"}, "protocol_type": {"title": "协议类型", "icon": "fas fa-plug", "variant": "secondary"}, "service_homepage": {"title": "服务主页", "icon": "fas fa-home", "variant": "outline"}}}, {"title": "安装部署", "subtitle": "服务安装和配置指南", "component": "InstallationGuide", "fields": ["installation_deployment"], "layout": "installation_steps", "position": "main", "collapsible": false, "bordered": true, "elevated": true, "guide_features": {"enable_step_by_step": true, "enable_command_copying": true, "show_installation_progress": true}, "actions": [{"key": "copy-command", "label": "复制安装命令", "icon": "fas fa-copy", "variant": "primary", "handler": "copyInstallationCommand"}]}], "default_tab": "rendered", "editor_config": {"enable_code_highlighting": true, "enable_installation_wizard": true, "auto_save_interval": 30, "enable_collaborative_editing": false, "enable_version_comparison": true}, "interaction_config": {"enable_code_copying": true, "enable_installation_wizard": true, "enable_keyboard_shortcuts": true, "keyboard_shortcuts": {"copy_command": "Ctrl+C", "visit_homepage": "Ctrl+H"}, "enable_context_menu": true, "enable_tooltips": true}, "list_view_config": {"card_fields": ["service_type", "service_source", "protocol_type"], "sort_options": ["created_at", "updated_at", "like_count", "service_type"], "filter_options": [{"field": "service_type", "type": "select", "label": "服务类型"}, {"field": "service_source", "type": "select", "label": "服务来源"}, {"field": "protocol_type", "type": "select", "label": "协议类型"}], "enable_bulk_operations": true, "bulk_operations": ["install", "bookmark"]}, "validation_rules": {"service_type": {"required": true, "message": "请选择服务类型"}, "service_source": {"required": true, "message": "请选择服务来源"}, "protocol_type": {"required": true, "message": "请选择协议类型"}, "service_homepage": {"required": true, "message": "请提供服务主页"}, "installation_deployment": {"required": true, "message": "请提供安装部署信息"}}, "performance_config": {"enable_lazy_loading": true, "enable_api_caching": true, "debounce_api_calls": 300}}, "communityConfigJson": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "mcp_features": {"test_service": true, "configuration_generator": true, "compatibility_checker": true, "performance_monitoring": true}, "developer_support": {"code_examples": true, "integration_guides": true, "troubleshooting_help": true, "community_plugins": true}, "gamification": {"award_points_for_contributions": 20, "award_points_for_testing": 15, "enable_mcp_badges": true}}}, {"id": 3, "code": "agent_rules", "name": "Agent Rules", "description": "AI Agent规则配置、行为定义和最佳实践指南", "iconUrl": "/icons/agent_rules.svg", "isActive": true, "sortOrder": 3, "isRecommended": true, "count": 25, "tags": ["Agent", "规则", "配置", "AI"], "category": "AI工具", "lastUpdated": "2025-01-18T10:30:00Z", "renderConfigJson": {"display_template_id": "agent-rules-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_rules", "header_style": "clean_header", "enable_star_rating": true, "enable_step_guide": true, "sidebar_sections": ["规则信息"], "main_sections": ["配置说明"]}, "search_fields": ["rule_scope", "applicable_agents", "recommendation_level"], "display_sections": [{"title": "规则信息", "subtitle": "Agent规则基本信息", "component": "RuleInfoCard", "fields": ["rule_scope", "applicable_agents", "recommendation_level", "reference_url"], "layout": "info_grid", "position": "sidebar", "collapsible": false, "bordered": true, "elevated": true, "actions": [{"key": "visit-reference", "label": "查看参考资料", "icon": "fas fa-external-link-alt", "variant": "primary", "handler": "visitReference"}], "fieldConfig": {"rule_scope": {"title": "使用范围", "icon": "fas fa-layer-group", "variant": "primary"}, "applicable_agents": {"title": "适用Agent", "icon": "fas fa-robot", "variant": "info"}, "recommendation_level": {"title": "推荐程度", "icon": "fas fa-star", "variant": "warning", "display": "star-rating"}, "reference_url": {"title": "参考资料", "icon": "fas fa-link", "variant": "outline"}}}, {"title": "配置说明", "subtitle": "规则配置和使用指南", "component": "ConfigurationGuide", "fields": ["configuration_steps"], "layout": "step_guide", "position": "main", "collapsible": false, "bordered": true, "elevated": true, "guide_features": {"enable_step_by_step": true, "enable_step_copying": true, "show_step_progress": true, "enable_platform_selection": true, "enable_code_highlighting": true, "enable_completion_tracking": true, "show_platform_icons": true, "enable_copy_all_steps": true}, "actions": [{"key": "copy-all-steps", "label": "复制全部步骤", "icon": "fas fa-copy", "variant": "primary", "handler": "copyAllConfigurationSteps"}, {"key": "reset-progress", "label": "重置进度", "icon": "fas fa-undo", "variant": "outline", "handler": "resetConfigurationProgress"}, {"key": "export-config", "label": "导出配置", "icon": "fas fa-download", "variant": "secondary", "handler": "exportConfiguration"}]}], "default_tab": "rendered", "editor_config": {"enable_star_rating": true, "enable_step_guide": true, "auto_save_interval": 30, "enable_collaborative_editing": false, "enable_version_comparison": true}, "interaction_config": {"enable_step_copying": true, "enable_reference_linking": true, "enable_keyboard_shortcuts": true, "keyboard_shortcuts": {"copy_steps": "Ctrl+C", "visit_reference": "Ctrl+R"}, "enable_context_menu": true, "enable_tooltips": true}, "list_view_config": {"card_fields": ["rule_scope", "applicable_agents", "recommendation_level"], "sort_options": ["created_at", "updated_at", "like_count", "recommendation_level"], "filter_options": [{"field": "rule_scope", "type": "select", "label": "使用范围"}, {"field": "applicable_agents", "type": "select", "label": "适用Agent"}, {"field": "recommendation_level", "type": "select", "label": "推荐程度"}], "enable_bulk_operations": true, "bulk_operations": ["bookmark", "export"]}, "validation_rules": {"rule_scope": {"required": true, "message": "请填写使用范围"}, "applicable_agents": {"required": true, "message": "请填写适用Agent"}, "recommendation_level": {"required": true, "min": 1, "max": 5, "message": "推荐程度必须在1-5星之间"}, "configuration_steps": {"required": true, "maxItems": 8, "message": "请提供配置步骤，最多支持8个平台", "itemValidation": {"platform": {"required": true, "message": "平台名称不能为空"}, "title": {"required": true, "message": "配置标题不能为空"}, "steps": {"required": true, "minItems": 1, "maxItems": 15, "message": "每个平台至少需要1个步骤，最多15个步骤"}}}}, "performance_config": {"enable_lazy_loading": true, "enable_api_caching": true, "debounce_api_calls": 300}}, "communityConfigJson": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "rule_features": {"rule_testing": true, "conflict_detection": true, "compliance_monitoring": true, "rule_versioning": true}, "governance_support": {"rule_approval_workflow": true, "impact_assessment": true, "rollback_mechanism": true, "audit_logging": true}, "gamification": {"award_points_for_contributions": 15, "award_points_for_testing": 10, "enable_governance_badges": true}}, "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Agent <PERSON> <PERSON>ada<PERSON>", "description": "Agent规则的metadata_json结构定义 - 精简版", "type": "object", "properties": {"rule_scope": {"type": "string", "title": "使用范围", "description": "规则的使用范围，如User Rule、Project Rule、Agent Rule等"}, "applicable_agents": {"type": "string", "title": "适用Agent", "description": "适用的Agent，如All、Cursor、Augment等"}, "recommendation_level": {"type": "integer", "title": "推荐程度", "description": "推荐程度，1-5星", "minimum": 1, "maximum": 5}, "reference_url": {"type": "string", "title": "参考资料", "description": "参考资料链接", "format": "uri"}, "configuration_steps": {"type": "array", "title": "配置说明", "description": "多平台配置步骤说明", "items": {"type": "object", "properties": {"platform": {"type": "string", "title": "平台名称", "description": "AI工具平台名称，如Cursor、Augment、<PERSON>等"}, "title": {"type": "string", "title": "配置标题", "description": "该平台的配置指南标题"}, "steps": {"type": "array", "title": "配置步骤", "description": "具体的配置步骤列表", "items": {"type": "string"}, "maxItems": 15}}, "required": ["platform", "title", "steps"], "additionalProperties": false}, "maxItems": 8}}, "required": ["rule_scope", "applicable_agents", "recommendation_level", "configuration_steps"], "additionalProperties": false, "examples": [{"rule_scope": "Project Rule", "applicable_agents": "All", "recommendation_level": 4, "reference_url": "https://example.com/agent-rules-guide", "configuration_steps": [{"platform": "<PERSON><PERSON><PERSON>", "title": "在Cursor中配置代码审查规则", "steps": ["1. 打开Cursor设置 (Cmd/Ctrl + ,)", "2. 搜索 'Rules' 或导航到 Extensions > Cursor Rules", "3. 点击 'Add Rule' 创建新规则", "4. 设置规则名称为 'Code Review Agent'", "5. 在规则内容中粘贴配置JSON", "6. 设置触发条件：文件保存时自动执行", "7. 启用规则并测试效果"]}, {"platform": "Augment", "title": "在Augment中配置代码审查规则", "steps": ["1. 在项目根目录创建 .augment/rules/ 文件夹", "2. 创建 code-review.json 配置文件", "3. 添加规则配置JSON", "4. 在 .augment/config.json 中启用规则", "5. 重启Augment服务使配置生效", "6. 验证规则是否正常工作"]}]}]}}, {"id": 4, "code": "open_source_project", "name": "开源软件", "description": "精选开源软件推荐、使用指南和贡献方法", "iconUrl": "/icons/open_source_project.svg", "isActive": true, "sortOrder": 4, "isRecommended": false, "count": 25, "tags": ["开源", "软件", "GitHub", "社区"], "category": "开发资源", "lastUpdated": "2025-01-18T10:30:00Z", "renderConfigJson": {"display_template_id": "open-source-project-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_project", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["仓库信息"], "main_sections": ["安装部署", "开源协议"]}, "search_fields": ["primary_language", "license"], "display_sections": [{"title": "仓库信息", "fields": ["repository_url", "primary_language", "stars", "forks", "last_updated"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}, {"title": "安装部署", "fields": ["installation_steps"], "component": "<PERSON><PERSON><PERSON><PERSON>", "layout": "markdown_content", "position": "main", "collapsible": false, "enable_syntax_highlighting": true, "enable_copy_code": true}, {"title": "开源协议", "fields": ["license"], "component": "InfoCardGrid", "layout": "license_card", "position": "main", "collapsible": false, "enable_license_link": true, "show_license_details": true}], "github_integration": {"enable_auto_sync": true, "sync_fields": ["stars", "forks", "last_updated"], "sync_interval": "daily"}, "list_view_config": {"card_template": "SimplifiedProjectCard", "preview_fields": ["stars", "forks", "primary_language"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "stars", "label": "Star数量", "direction": "desc"}, {"field": "last_updated", "label": "最近更新", "direction": "desc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}], "filter_options": [{"field": "primary_language", "label": "编程语言", "type": "select"}, {"field": "license", "label": "开源协议", "type": "select"}, {"field": "stars", "label": "Star数量", "type": "range"}]}}, "communityConfigJson": {"can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin", "reddit", "hacker_news"], "can_contribute": true, "contribution_types": ["code_contribution", "documentation", "bug_report", "feature_request", "translation", "testing"], "can_track_issues": true, "issue_integration": {"sync_with_github": true, "show_open_issues": true, "show_recent_commits": true, "track_pull_requests": true}, "can_rate_project": true, "rating_criteria": [{"name": "code_quality", "label": "代码质量", "description": "代码的可读性、可维护性和架构设计", "weight": 0.3}, {"name": "documentation", "label": "文档质量", "description": "文档的完整性和清晰度", "weight": 0.25}, {"name": "community_activity", "label": "社区活跃度", "description": "项目的维护频率和社区参与度", "weight": 0.25}, {"name": "usefulness", "label": "实用性", "description": "项目解决实际问题的能力", "weight": 0.2}], "can_submit_showcase": true, "showcase_categories": ["production_usage", "personal_project", "learning_example", "integration_demo", "performance_benchmark"], "can_request_features": true, "feature_request_workflow": {"require_use_case": true, "allow_voting": true, "auto_forward_to_maintainer": true}, "developer_tools": {"show_dependency_graph": true, "show_code_statistics": true, "enable_code_search": true, "provide_api_docs": true}, "community_features": {"show_contributor_stats": true, "show_commit_activity": true, "enable_project_discussions": true, "allow_maintainer_ama": true, "show_related_projects": true}, "learning_resources": {"link_to_tutorials": true, "show_getting_started": true, "provide_code_examples": true, "enable_interactive_demos": true}, "project_health": {"show_maintenance_status": true, "track_response_time": true, "show_security_advisories": true, "display_compatibility_info": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["spam_detection", "inappropriate_content", "license_compliance", "security_concerns"], "community_reporting": true, "maintainer_moderation": true}, "analytics_tracking": {"track_project_views": true, "track_clone_attempts": true, "track_contribution_interest": true, "track_showcase_submissions": true, "generate_project_insights": true}, "gamification": {"award_points_for_sharing": 10, "award_points_for_contributions": 20, "award_points_for_showcases": 15, "award_points_for_reviews": 5, "enable_contributor_badges": true, "badge_types": ["project_maintainer", "active_contributor", "documentation_hero", "bug_hunter", "community_champion"]}, "integration_features": {"github_webhook_support": true, "ci_cd_status_display": true, "package_manager_links": true, "docker_hub_integration": true}}, "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Open Source Project Metadata <PERSON>a", "description": "开源项目的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"repository_url": {"type": "string", "title": "仓库地址", "description": "项目的源代码仓库地址", "format": "uri", "pattern": "^https?://", "examples": ["https://github.com/openai/gpt-2", "https://github.com/huggingface/transformers", "https://gitlab.com/example/project"]}, "primary_language": {"type": "string", "title": "主要编程语言", "description": "项目的主要编程语言", "enum": ["Python", "JavaScript", "TypeScript", "Java", "C++", "C#", "Go", "Rust", "Swift", "<PERSON><PERSON><PERSON>", "PHP", "<PERSON>", "Scala", "R", "MATLAB", "Shell", "Other"]}, "license": {"type": "string", "title": "开源协议", "description": "项目的开源许可证", "enum": ["MIT", "Apache-2.0", "GPL-3.0", "GPL-2.0", "LGPL-3.0", "BSD-3-<PERSON><PERSON>", "BSD-2-<PERSON><PERSON>", "ISC", "MPL-2.0", "AGPL-3.0", "Unlicense", "Custom", "Proprietary"]}, "stars": {"type": "integer", "title": "Star数量", "description": "GitHub Star数量（可自动同步）", "minimum": 0, "maximum": 1000000, "examples": [1520, 15420, 89300]}, "forks": {"type": "integer", "title": "Fork数量", "description": "GitHub Fork数量（可自动同步）", "minimum": 0, "maximum": 100000, "examples": [150, 2340, 8900]}, "issues": {"type": "integer", "title": "Issues数量", "description": "GitHub Issues数量（可自动同步）", "minimum": 0, "maximum": 50000, "examples": [12, 234, 1890]}, "contributors_count": {"type": "integer", "title": "贡献者数量", "description": "项目贡献者总数", "minimum": 1, "maximum": 10000, "examples": [5, 45, 234]}, "last_updated": {"type": "string", "title": "最后更新时间", "description": "项目最后更新的时间", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "installation_steps": {"type": "string", "title": "安装部署", "description": "项目的安装和部署步骤说明，支持Markdown格式", "maxLength": 2000, "examples": ["## 安装步骤\n\n1. 使用pip安装：\n```bash\npip install package-name\n```\n\n2. 从源码安装：\n```bash\ngit clone https://github.com/user/repo.git\ncd repo\npython setup.py install\n```"]}}, "required": ["repository_url", "primary_language", "license"], "additionalProperties": false, "examples": [{"repository_url": "https://github.com/vuejs/core", "primary_language": "JavaScript", "license": "MIT", "stars": 45800, "forks": 8200, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用包管理器安装\n\n```bash\n# 使用npm\nnpm install vue@next\n\n# 使用yarn\nyarn add vue@next\n\n# 使用pnpm\npnpm add vue@next\n```\n\n### CDN引入\n\n```html\n<script src=\"https://unpkg.com/vue@next\"></script>\n```"}, {"repository_url": "https://github.com/openai/gpt-2", "primary_language": "Python", "license": "MIT", "stars": 15420, "forks": 3890, "last_updated": "2024-01-15", "installation_steps": "## 安装步骤\n\n### 使用pip安装\n\n```bash\npip install transformers torch\n```\n\n### 从源码安装\n\n```bash\ngit clone https://github.com/openai/gpt-2.git\ncd gpt-2\npip install -r requirements.txt\n```"}]}}, {"id": 5, "code": "ai_tool", "name": "AI工具", "description": "AI工具、平台和服务的评测、使用指南和对比分析", "iconUrl": "/icons/ai_tool_platform.svg", "isActive": true, "sortOrder": 5, "isRecommended": false, "count": 25, "tags": ["AI工具", "平台", "评测", "对比"], "category": "AI工具", "lastUpdated": "2025-01-18T10:30:00Z", "renderConfigJson": {"display_template_id": "ai-tool-platform-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_tool", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["工具信息"], "main_sections": ["使用说明", "视频演示"]}, "search_fields": ["tool_type", "vendor_name", "application_scenarios"], "display_sections": [{"title": "工具信息", "fields": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}, {"title": "使用说明", "fields": ["usage_instructions"], "component": "<PERSON><PERSON><PERSON><PERSON>", "layout": "markdown_content", "position": "main", "collapsible": false, "enable_syntax_highlighting": true, "enable_copy_code": true, "enable_image_display": true}, {"title": "视频演示", "fields": ["video_demo"], "component": "VideoPlayer", "layout": "video_content", "position": "main", "collapsible": false, "enable_fullscreen": true, "auto_play": false}], "list_view_config": {"card_template": "SimplifiedToolCard", "preview_fields": ["tool_type", "vendor_name", "application_scenarios"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "created_at", "label": "创建时间", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}], "filter_options": [{"field": "tool_type", "label": "工具类型", "type": "select"}, {"field": "pricing_model", "label": "定价模式", "type": "select"}, {"field": "application_scenarios", "label": "应用场景", "type": "checkboxes"}]}}, "communityConfigJson": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "twitter", "linkedin"], "can_rate_product": true, "rating_criteria": [{"name": "functionality", "label": "功能性", "description": "产品功能的完整性和实用性", "weight": 0.3}, {"name": "ease_of_use", "label": "易用性", "description": "产品的学习成本和使用便利性", "weight": 0.25}, {"name": "value_for_money", "label": "性价比", "description": "产品价格与价值的匹配度", "weight": 0.25}, {"name": "support_quality", "label": "支持质量", "description": "客户服务和技术支持的质量", "weight": 0.2}], "can_submit_use_case": true, "use_case_categories": ["business_automation", "content_creation", "data_analysis", "customer_service", "education_training", "research_development", "personal_productivity"], "can_compare_products": true, "comparison_features": ["pricing_model", "core_features", "target_users", "integration_options", "support_channels"], "can_request_demo": true, "demo_request_config": {"require_contact_info": true, "require_use_case": true, "auto_forward_to_vendor": true}, "can_track_updates": true, "update_notification_types": ["new_features", "pricing_changes", "integration_updates", "security_updates"], "community_features": {"show_user_reviews": true, "show_usage_statistics": true, "enable_expert_reviews": true, "allow_vendor_responses": true, "show_alternative_suggestions": true}, "vendor_interaction": {"allow_vendor_profile": true, "enable_vendor_updates": true, "allow_vendor_responses_to_reviews": true, "require_vendor_verification": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["spam_detection", "inappropriate_content", "fake_reviews", "promotional_content"], "community_reporting": true, "expert_moderation": true}, "analytics_tracking": {"track_product_views": true, "track_demo_requests": true, "track_comparison_usage": true, "track_user_journey": true, "generate_vendor_insights": true}, "gamification": {"award_points_for_reviews": 5, "award_points_for_use_cases": 8, "award_points_for_helpful_votes": 2, "enable_reviewer_badges": true, "badge_types": ["verified_user", "expert_reviewer", "early_adopter", "helpful_contributor"]}, "integration_features": {"enable_affiliate_tracking": true, "support_trial_links": true, "enable_pricing_alerts": true, "support_bulk_evaluation": true}}, "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "AI Tool Platform Metadata Schema", "description": "AI工具和平台的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"official_url": {"type": "string", "title": "官方主页", "description": "产品官方网站链接", "format": "uri", "pattern": "^https?://", "examples": ["https://openai.com/chatgpt", "https://www.midjourney.com", "https://claude.ai"]}, "vendor_name": {"type": "string", "title": "厂商名称", "description": "工具提供商或开发公司名称", "minLength": 1, "maxLength": 100, "examples": ["OpenAI", "Google", "Microsoft", "Anthropic", "Hugging Face", "Stability AI"]}, "tool_type": {"type": "string", "title": "工具类型", "description": "工具的开放性类型", "enum": ["开源", "闭源免费", "高级付费"]}, "pricing_model": {"type": "string", "title": "定价模式", "description": "产品的定价策略", "enum": ["免费", "免费增值", "订阅制", "按量付费", "企业定制", "开源", "一次性购买"]}, "application_scenarios": {"type": "array", "title": "应用场景", "description": "工具的主要应用场景，多标签", "items": {"type": "string", "minLength": 2, "maxLength": 50}, "minItems": 1, "maxItems": 8, "uniqueItems": true, "examples": [["对话生成", "代码辅助", "文档写作"], ["图像生成", "风格转换", "图像编辑"], ["数据分析", "可视化", "预测建模"]]}, "usage_instructions": {"type": "string", "title": "使用说明", "description": "工具的使用说明，支持Markdown格式和图片显示", "maxLength": 5000, "examples": ["## 使用步骤\n\n1. 注册账号\n2. 选择合适的套餐\n3. 开始使用\n\n![使用界面](https://example.com/screenshot.png)"]}, "video_demo": {"type": "string", "title": "视频演示", "description": "演示视频的URL地址", "format": "uri", "pattern": "^https?://", "examples": ["https://www.youtube.com/watch?v=example", "https://vimeo.com/example", "https://example.com/demo.mp4"]}}, "required": ["official_url", "vendor_name", "tool_type", "pricing_model", "application_scenarios"], "additionalProperties": false, "examples": [{"official_url": "https://openai.com/chatgpt", "vendor_name": "OpenAI", "tool_type": "高级付费", "pricing_model": "免费增值", "application_scenarios": ["对话生成", "代码辅助", "文档写作", "翻译润色"], "usage_instructions": "## 使用步骤\n\n1. 访问官网注册账号\n2. 选择合适的套餐\n3. 开始与AI对话\n\n### 基本功能\n- 文本对话\n- 代码生成\n- 文档写作\n\n![ChatGPT界面](https://example.com/chatgpt-ui.png)", "video_demo": "https://www.youtube.com/watch?v=chatgpt-demo"}, {"official_url": "https://www.midjourney.com", "vendor_name": "Midjourney", "tool_type": "高级付费", "pricing_model": "订阅制", "application_scenarios": ["图像生成", "艺术创作", "设计辅助"], "usage_instructions": "## 使用Midjourney\n\n1. 加入Discord服务器\n2. 使用/imagine命令\n3. 输入描述文字\n4. 等待AI生成图像\n\n### 提示词技巧\n- 详细描述场景\n- 指定艺术风格\n- 调整参数设置"}]}}, {"id": 6, "code": "middleware_guide", "name": "京东中间件", "description": "京东中间件的安装、配置、使用和故障排除指南", "iconUrl": "/icons/middleware_guide.svg", "isActive": true, "sortOrder": 6, "isRecommended": false, "count": 25, "tags": ["中间件", "配置", "部署", "运维"], "category": "技术服务", "lastUpdated": "2025-01-18T10:30:00Z", "renderConfigJson": {"display_template_id": "middleware-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_middleware", "header_style": "clean_header", "enable_statistics_sidebar": true, "sidebar_sections": ["中间件信息"], "main_sections": []}, "search_fields": ["official_homepage", "help_documentation", "ops_contact"], "display_sections": [{"title": "中间件信息", "fields": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "enable_external_links": true, "show_icons": true}], "list_view_config": {"card_template": "SimplifiedMiddlewareCard", "preview_fields": ["official_homepage", "help_documentation", "ops_contact"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "created_at", "label": "创建时间", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}], "filter_options": [{"field": "ops_contact", "label": "运维支持", "type": "text"}]}}, "communityConfigJson": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "middleware_features": {"code_examples": true, "integration_testing": true, "performance_benchmarks": true, "troubleshooting_guide": true}, "developer_support": {"configuration_generator": true, "compatibility_checker": true, "migration_guides": true, "best_practices": true}, "gamification": {"award_points_for_guides": 20, "award_points_for_examples": 15, "enable_integration_badges": true}}, "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Middleware Guide Metadata Schema", "description": "Middleware_Guide的metadata_json结构定义", "type": "object", "properties": {"official_homepage": {"type": "string", "title": "官方主页", "description": "中间件的官方网站地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com", "https://koajs.com", "https://www.fastify.io", "https://nestjs.com"]}, "help_documentation": {"type": "string", "title": "帮助文档", "description": "中间件的官方文档地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/guide/", "https://koajs.com/#guide", "https://www.fastify.io/docs/", "https://docs.nestjs.com"]}, "faq_url": {"type": "string", "title": "常见问题", "description": "常见问题解答页面地址", "format": "uri", "pattern": "^https?://", "examples": ["https://expressjs.com/en/starter/faq.html", "https://github.com/koajs/koa/wiki/FAQ", "https://www.fastify.io/docs/latest/FAQ/", "https://docs.nestjs.com/faq"]}, "ops_contact": {"type": "string", "title": "运维咚咚", "description": "运维支持联系方式或群组信息", "maxLength": 200, "examples": ["运维支持群：12345678", "技术支持：<EMAIL>", "内部咚咚群：中间件运维支持", "联系人：张三（工号：12345）"]}, "required": ["official_homepage", "help_documentation", "faq_url", "ops_contact"], "additionalProperties": false, "examples": [{"official_homepage": "https://expressjs.com", "help_documentation": "https://expressjs.com/en/guide/", "faq_url": "https://expressjs.com/en/starter/faq.html", "ops_contact": "运维支持群：Express中间件技术支持"}, {"official_homepage": "https://koajs.com", "help_documentation": "https://koajs.com/#guide", "faq_url": "https://github.com/koajs/koa/wiki/FAQ", "ops_contact": "技术支持：<EMAIL>"}, {"official_homepage": "https://www.fastify.io", "help_documentation": "https://www.fastify.io/docs/", "faq_url": "https://www.fastify.io/docs/latest/FAQ/", "ops_contact": "内部咚咚群：Fastify运维支持"}, {"official_homepage": "https://nestjs.com", "help_documentation": "https://docs.nestjs.com", "faq_url": "https://docs.nestjs.com/faq", "ops_contact": "联系人：李四（工号：67890）"}]}}}, {"id": 7, "code": "development_standard", "name": "标准规范", "description": "软件开发标准、编码规范和最佳实践指南", "iconUrl": "/icons/development_standard.svg", "isActive": true, "sortOrder": 7, "isRecommended": false, "count": 25, "tags": ["标准", "规范", "开发", "质量"], "category": "开发资源", "lastUpdated": "2025-01-18T10:30:00Z", "renderConfigJson": {"display_template_id": "development-standard-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_standard", "header_style": "clean_header", "enable_standard_sidebar": true, "sidebar_sections": ["规范信息"], "main_sections": ["阅读原文"]}, "search_fields": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version"], "display_sections": [{"title": "规范信息", "fields": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "show_icons": true, "field_mappings": {"standard_level": {"company": "公司级", "department": "部门级", "team": "团队级", "project": "项目级"}, "standard_status": {"draft": "草案", "review": "评审中", "approved": "已批准", "active": "生效中", "deprecated": "已废弃"}}}, {"title": "阅读原文", "fields": ["document_source"], "component": "DocumentViewer", "layout": "document_viewer", "position": "main", "collapsible": false, "enable_pdf_viewer": true, "enable_url_viewer": true, "show_file_info": true}], "list_view_config": {"card_template": "StandardCard", "preview_fields": ["standard_level", "standard_category", "standard_status", "standard_version"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "publish_date", "label": "发布日期", "direction": "desc"}, {"field": "standard_version", "label": "版本号", "direction": "desc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}], "filter_options": [{"field": "standard_level", "label": "规范等级", "type": "select"}, {"field": "standard_category", "label": "规范类别", "type": "select"}, {"field": "standard_status", "label": "规范状态", "type": "select"}, {"field": "applicable_scope", "label": "适用范围", "type": "checkboxes"}]}}, "communityConfigJson": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "standard_features": {"compliance_checking": true, "automated_validation": true, "exception_tracking": true, "impact_analysis": true}, "governance_support": {"approval_workflow": true, "version_control": true, "change_management": true, "training_materials": true}, "gamification": {"award_points_for_compliance": 15, "award_points_for_contributions": 20, "enable_quality_badges": true}}, "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Development Standard Metadata Schema", "description": "Development_Standard的metadata_json结构定义", "type": "object", "properties": {"standard_level": {"type": "string", "title": "规范等级", "description": "标准规范的等级分类", "enum": ["collective_standard", "retail_standard", "logistics_standard"]}, "standard_category": {"type": "string", "title": "规范类别", "description": "标准规范的类别分类", "enum": ["project_management", "prd_specification", "coding_standard", "design_guideline", "testing_standard", "deployment_standard", "security_standard", "documentation_standard"]}, "applicable_scope": {"type": "array", "title": "适用范围", "description": "规范的适用范围", "items": {"type": "string", "enum": ["human_readable", "ai_readable"]}, "maxItems": 2, "examples": [["human_readable"], ["ai_readable"], ["human_readable", "ai_readable"]]}, "standard_status": {"type": "string", "title": "规范状态", "description": "标准规范的当前状态", "enum": ["draft", "review", "approved", "published", "deprecated", "archived"]}, "standard_version": {"type": "string", "title": "规范版本", "description": "标准规范的版本号", "pattern": "^v?\\d+\\.\\d+(\\.\\d+)?(-[a-zA-Z0-9]+)?$", "examples": ["v1.0", "v2.1.0", "v1.0.0-beta", "2.0", "1.5.2"]}, "publish_date": {"type": "string", "title": "发布日期", "description": "标准规范的发布日期", "format": "date", "examples": ["2024-01-15", "2023-12-20", "2024-02-01"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "标准规范的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/standards/coding-standard.html", "https://example.com/docs/project-management.pdf"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.2MB", "5.8MB", "12.3MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 200, "examples": [15, 32, 68]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["standard_level", "standard_category", "applicable_scope", "standard_status", "standard_version", "publish_date"], "additionalProperties": false, "examples": [{"standard_level": "collective_standard", "standard_category": "coding_standard", "applicable_scope": ["human_readable", "ai_readable"], "standard_status": "published", "standard_version": "v2.1.0", "publish_date": "2024-01-15", "document_source": {"source_type": "pdf", "source_url": "https://example.com/standards/coding-standard-v2.1.pdf", "pdf_size": "5.8MB", "page_count": 32, "language": "zh-CN"}}, {"standard_level": "retail_standard", "standard_category": "project_management", "applicable_scope": ["human_readable"], "standard_status": "approved", "standard_version": "v1.0", "publish_date": "2023-12-20", "document_source": {"source_type": "url", "source_url": "https://example.com/standards/project-management.html", "language": "zh-CN"}}]}}, {"id": 8, "code": "sop", "name": "SOP文档", "description": "标准作业程序、流程规范和操作指南", "iconUrl": "/icons/sop.svg", "isActive": true, "sortOrder": 8, "isRecommended": false, "count": 25, "tags": ["SOP", "流程", "标准", "操作"], "category": "管理规范", "lastUpdated": "2025-01-18T10:30:00Z", "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "SOP Metadata Schema", "description": "标准SOP的metadata_json结构定义 - 精简版，专注核心信息", "type": "object", "properties": {"target_role": {"type": "string", "title": "目标角色", "description": "SOP适用的目标角色或岗位", "maxLength": 100, "examples": ["产品经理", "开发工程师", "测试工程师", "运维工程师", "项目经理", "设计师", "数据分析师", "客服专员"]}, "application_scenario": {"type": "string", "title": "应用场景", "description": "SOP适用的具体应用场景", "maxLength": 200, "examples": ["产品需求评审", "代码发布流程", "故障应急处理", "用户反馈处理", "项目启动流程", "安全事件响应", "数据备份恢复", "客户投诉处理"]}, "execution_requirement": {"type": "string", "title": "执行要求", "description": "SOP的执行要求级别", "enum": ["must_follow", "reference_suggestion"]}, "difficulty_level": {"type": "string", "title": "难度等级", "description": "SOP执行的难度等级", "enum": ["beginner", "intermediate", "advanced", "expert"]}, "violation_handling": {"type": "string", "title": "违反处理", "description": "违反SOP时的处理方式", "maxLength": 300, "examples": ["口头警告，记录在案", "书面警告，上报主管", "暂停相关权限，强制培训", "严重违规，按公司制度处理", "立即停止操作，上报安全团队"]}, "document_source": {"type": "object", "title": "阅读原文", "description": "SOP的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"]}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/sop/deployment-process.pdf", "https://wiki.company.com/sop/incident-response"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["1.5MB", "3.2MB", "8.7MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 100, "examples": [8, 15, 25]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "additionalProperties": false}, "renderConfigJson": {"display_template_id": "sop-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_sop", "header_style": "clean_header", "enable_sop_sidebar": true, "sidebar_sections": ["SOP信息"], "main_sections": ["阅读原文"]}, "search_fields": ["target_role", "application_scenario", "execution_requirement", "difficulty_level"], "display_sections": [{"title": "SOP信息", "fields": ["target_role", "application_scenario", "execution_requirement", "difficulty_level", "violation_handling"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "show_icons": true, "field_mappings": {"execution_requirement": {"must_follow": "必须遵守", "reference_suggestion": "参考建议"}, "difficulty_level": {"beginner": "初级", "intermediate": "中级", "advanced": "高级", "expert": "专家级"}}}, {"title": "阅读原文", "fields": ["document_source"], "component": "DocumentViewer", "layout": "document_viewer", "position": "main", "collapsible": false, "enable_pdf_viewer": true, "enable_url_preview": true, "enable_download": true, "show_document_info": true}], "list_view_config": {"card_template": "SOPCard", "preview_fields": ["target_role", "application_scenario", "execution_requirement", "difficulty_level"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "execution_requirement", "label": "执行要求", "direction": "desc"}, {"field": "difficulty_level", "label": "难度等级", "direction": "asc"}, {"field": "created_at", "label": "创建时间", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}], "filter_options": [{"field": "target_role", "label": "目标角色", "type": "select"}, {"field": "application_scenario", "label": "应用场景", "type": "select"}, {"field": "execution_requirement", "label": "执行要求", "type": "select"}, {"field": "difficulty_level", "label": "难度等级", "type": "select"}]}}, "communityConfigJson": {"can_comment": true, "can_like": true, "can_favorite": true, "can_fork": true, "can_share": true, "share_options": ["internal", "wechat", "email", "link_copy", "teams", "slack"], "can_execute_tracking": true, "execution_tracking": {"track_completion_rate": true, "track_execution_time": true, "collect_feedback": true, "show_success_metrics": true}, "can_suggest_improvements": true, "improvement_categories": ["step_optimization", "tool_recommendation", "time_estimation", "clarity_enhancement", "safety_improvement"], "can_rate_effectiveness": true, "effectiveness_criteria": [{"name": "clarity", "label": "清晰度", "description": "步骤说明的清晰程度", "weight": 0.3}, {"name": "completeness", "label": "完整性", "description": "流程覆盖的完整程度", "weight": 0.25}, {"name": "efficiency", "label": "效率", "description": "执行流程的效率", "weight": 0.25}, {"name": "practicality", "label": "实用性", "description": "在实际工作中的适用性", "weight": 0.2}], "can_submit_execution_report": true, "execution_report_fields": ["actual_time_taken", "encountered_issues", "suggested_improvements", "success_outcome", "difficulty_rating"], "can_create_checklist": true, "checklist_features": {"auto_generate_from_steps": true, "customizable_items": true, "progress_tracking": true, "team_collaboration": true}, "version_control": {"track_sop_versions": true, "show_version_history": true, "compare_versions": true, "approval_workflow": true}, "team_features": {"assign_to_team_members": true, "track_team_compliance": true, "generate_team_reports": true, "enable_team_discussions": true}, "compliance_tracking": {"track_execution_compliance": true, "generate_audit_reports": true, "set_review_reminders": true, "monitor_adherence_rates": true}, "training_integration": {"link_to_training_materials": true, "track_training_completion": true, "assess_competency": true, "provide_certification": true}, "quality_assurance": {"peer_review_system": true, "expert_validation": true, "regular_review_cycles": true, "continuous_improvement": true}, "content_moderation": {"auto_review_enabled": true, "review_criteria": ["safety_compliance", "accuracy_verification", "completeness_check", "clarity_assessment"], "expert_moderation": true, "community_reporting": true}, "analytics_tracking": {"track_sop_usage": true, "track_execution_success": true, "track_improvement_suggestions": true, "track_team_performance": true, "generate_efficiency_reports": true}, "gamification": {"award_points_for_execution": 10, "award_points_for_improvements": 15, "award_points_for_reviews": 8, "award_points_for_compliance": 5, "enable_process_badges": true, "badge_types": ["process_expert", "efficiency_champion", "quality_guardian", "improvement_leader", "compliance_master"]}, "integration_features": {"workflow_automation": true, "calendar_integration": true, "notification_system": true, "reporting_dashboard": true}}}, {"id": 9, "code": "industry_report", "name": "行业报告", "description": "行业分析报告、市场趋势和技术发展动态", "iconUrl": "/icons/industry_report.svg", "isActive": true, "sortOrder": 9, "isRecommended": false, "count": 25, "tags": ["行业", "报告", "分析", "趋势"], "category": "行业资讯", "lastUpdated": "2025-01-18T10:30:00Z", "renderConfigJson": {"display_template_id": "industry-report-simplified", "layout_style": "simplified", "layout_config": {"type": "simplified_report", "header_style": "clean_header", "enable_pdf_viewer": true, "sidebar_sections": ["报告信息", "作者信息"], "main_sections": ["阅读原文"]}, "search_fields": ["author_name", "author_organization", "report_type", "industry_focus"], "display_sections": [{"title": "报告信息", "fields": ["report_type", "industry_focus"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": true, "show_icons": true, "field_mappings": {"report_type": {"market_analysis": "市场分析", "technology_trend": "技术趋势", "industry_outlook": "行业展望", "competitive_analysis": "竞争分析", "investment_report": "投资报告"}}}, {"title": "作者信息", "fields": ["author_name", "author_organization"], "component": "InfoCardGrid", "layout": "info_grid", "position": "sidebar", "collapsible": false, "show_badges": false, "show_icons": true}, {"title": "阅读原文", "fields": ["document_source"], "component": "DocumentViewer", "layout": "document_viewer", "position": "main", "collapsible": false, "enable_pdf_viewer": true, "enable_url_viewer": true, "show_file_info": true}], "list_view_config": {"card_template": "ReportCard", "preview_fields": ["author_name", "author_organization", "report_type", "industry_focus"], "thumbnail_field": "cover_image_url", "sort_options": [{"field": "created_at", "label": "最新发布", "direction": "desc"}, {"field": "like_count", "label": "最受欢迎", "direction": "desc"}, {"field": "author_organization", "label": "机构名称", "direction": "asc"}], "filter_options": [{"field": "report_type", "label": "报告类型", "type": "select"}, {"field": "industry_focus", "label": "行业领域", "type": "checkboxes"}, {"field": "author_organization", "label": "所属机构", "type": "text"}]}}, "communityConfigJson": {"can_comment": true, "can_like": true, "can_favorite": true, "can_share": true, "report_features": {"data_visualization": true, "trend_analysis": true, "comparative_analysis": true, "executive_summary": true}, "business_support": {"market_insights": true, "investment_guidance": true, "strategic_recommendations": true, "risk_assessment": true}, "gamification": {"award_points_for_insights": 25, "award_points_for_analysis": 20, "enable_analyst_badges": true}}, "metadataJsonSchema": {"$schema": "http://json-schema.org/draft-07/schema#", "title": "Industry Report Metadata <PERSON>a", "description": "Industry_Report的metadata_json结构定义", "type": "object", "properties": {"author_name": {"type": "string", "title": "文章作者", "description": "报告的主要作者或作者团队", "maxLength": 100, "examples": ["李研究员", "张分析师", "McKinsey团队"]}, "author_organization": {"type": "string", "title": "所属机构", "description": "作者所属的机构或组织", "maxLength": 150, "examples": ["麦肯锡咨询", "中国信息通信研究院", "IDC", "<PERSON><PERSON><PERSON>"]}, "report_type": {"type": "string", "title": "报告类型", "description": "报告的主要类别", "enum": ["market_analysis", "technology_trends", "competitive_landscape", "investment_report", "policy_analysis", "forecast_report", "white_paper"]}, "industry_focus": {"type": "array", "title": "行业领域", "description": "报告涵盖的行业领域", "items": {"type": "string", "enum": ["artificial_intelligence", "machine_learning", "cloud_computing", "cybersecurity", "fintech", "healthcare_tech", "autonomous_vehicles", "blockchain", "iot", "robotics", "e_commerce", "digital_transformation"]}, "maxItems": 3, "examples": [["artificial_intelligence", "machine_learning"], ["fintech", "blockchain"]]}, "document_source": {"type": "object", "title": "阅读原文", "description": "行业报告的原文档信息，支持URL和PDF两种形态", "properties": {"source_type": {"type": "string", "title": "文档类型", "description": "原文档的类型", "enum": ["url", "pdf"], "default": "pdf"}, "source_url": {"type": "string", "title": "文档链接", "description": "原文档的访问链接", "format": "uri", "pattern": "^https?://", "examples": ["https://example.com/reports/ai-industry-report-2024.pdf", "https://example.com/reports/fintech-trends.html"]}, "pdf_size": {"type": "string", "title": "文件大小", "description": "PDF文件的大小（仅当source_type为pdf时需要）", "pattern": "^[0-9]+(\\.[0-9]+)?[KMGTB]B$", "examples": ["2.5MB", "15.8MB", "25.4MB"]}, "page_count": {"type": "integer", "title": "页数", "description": "PDF文档的总页数（仅当source_type为pdf时需要）", "minimum": 1, "maximum": 500, "examples": [45, 128, 256]}, "language": {"type": "string", "title": "文档语言", "description": "原文档的主要语言", "enum": ["zh-CN", "en-US", "zh-TW"], "default": "zh-CN"}}, "required": ["source_type", "source_url"], "additionalProperties": false}}, "required": ["author_name", "author_organization", "report_type", "industry_focus"], "additionalProperties": false, "examples": [{"author_name": "李研究员", "author_organization": "中国信息通信研究院", "report_type": "market_analysis", "industry_focus": ["artificial_intelligence", "machine_learning"], "pdf_document": {"pdf_url": "https://example.com/reports/ai-industry-report-2024.pdf", "pdf_size": "15.8MB", "page_count": 128, "language": "zh-CN"}}, {"author_name": "张分析师", "author_organization": "麦肯锡咨询", "report_type": "technology_trends", "industry_focus": ["fintech", "blockchain"], "pdf_document": {"pdf_url": "https://example.com/reports/fintech-trends-2024.pdf", "pdf_size": "25.4MB", "page_count": 256, "language": "zh-CN"}}]}}]