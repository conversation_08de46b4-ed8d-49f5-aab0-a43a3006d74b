[{"title": "线性代数的本质", "description": "3Blue1Brown制作的线性代数可视化教程，通过几何直觉理解线性代数的核心概念", "content": "这是一个革命性的线性代数教程，通过精美的动画和几何直觉，让抽象的数学概念变得生动易懂。课程涵盖向量、矩阵变换、特征值等核心内容。", "learningGoals": "理解向量的几何意义，掌握矩阵变换的本质，建立线性代数的几何直觉", "prerequisites": "高中数学基础", "resourceType": "VIDEO", "sourceType": "EXTERNAL", "sourceUrl": "https://www.bilibili.com/video/BV1ys411472E", "sourcePlatform": "Bilibili", "difficultyLevel": "BEGINNER", "estimatedDurationHours": 8.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["线性代数", "3Blue1Brown", "数学可视化", "几何直觉"], "contentType": "video", "metadata": {"thumbnailUrl": "https://i0.hdslb.com/bfs/archive/thumbnail.jpg", "author": {"name": "3Blue1Brown", "channel": "3Blue1Brown官方", "platform": "YouTube/Bilibili"}, "keywords": ["线性代数", "向量", "矩阵", "特征值"], "publishDate": "2016-08-05", "wordCount": 0, "readingTime": 0}, "contentConfig": {"videoType": "educational", "duration": 480, "chapters": [{"title": "向量的本质", "startTime": 0, "endTime": 120, "description": "理解向量的几何意义"}, {"title": "线性组合", "startTime": 120, "endTime": 240, "description": "向量的线性组合和张成"}, {"title": "矩阵与线性变换", "startTime": 240, "endTime": 360, "description": "矩阵作为线性变换的表示"}, {"title": "特征值与特征向量", "startTime": 360, "endTime": 480, "description": "特征值的几何意义"}], "quality": ["1080p", "720p", "480p"], "isLive": false, "subtitles": [{"language": "zh-CN", "url": "https://subtitle.url", "defaultSubtitle": true}]}, "status": "ACTIVE", "createdBy": "system", "categoryName": "数学基础"}, {"title": "MIT 18.06 线性代数", "description": "MIT Gilbert Strang教授的经典线性代数课程，深入系统地讲解线性代数理论", "content": "这是MIT最受欢迎的数学课程之一，Gilbert Strang教授以其独特的教学风格，深入浅出地讲解线性代数的核心理论和应用。", "learningGoals": "系统掌握线性代数理论，理解向量空间、矩阵分解等高级概念", "prerequisites": "微积分基础", "resourceType": "COURSE", "sourceType": "EXTERNAL", "sourceUrl": "https://ocw.mit.edu/courses/18-06-linear-algebra-spring-2010/", "sourcePlatform": "MIT OpenCourseWare", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 50.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["MIT", "<PERSON>", "线性代数", "大学课程"], "contentType": "external_link", "metadata": {"author": {"name": "<PERSON>", "department": "MIT Mathematics", "title": "Professor"}, "keywords": ["线性代数", "向量空间", "矩阵分解", "特征值"], "publishDate": "2010-01-01", "difficulty": "intermediate"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "数学基础"}, {"title": "概率论与数理统计", "description": "北京理工大学概率论与数理统计课程，系统讲解概率论基础和统计推断", "content": "全面覆盖概率论基础、随机变量、概率分布、大数定律、中心极限定理、参数估计、假设检验等内容。", "learningGoals": "掌握概率论基本概念，理解统计推断原理，具备数据分析基础", "prerequisites": "微积分基础", "resourceType": "COURSE", "sourceType": "EXTERNAL", "sourceUrl": "https://www.icourse163.org/course/BIT-1001654005", "sourcePlatform": "中国大学MOOC", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 45.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["概率论", "数理统计", "北京理工", "中文课程"], "contentType": "external_link", "metadata": {"author": {"name": "北京理工大学", "department": "数学与统计学院"}, "keywords": ["概率论", "统计学", "随机变量", "假设检验"], "publishDate": "2020-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "数学基础"}, {"title": "微积分精要", "description": "李永乐老师的微积分科普视频系列，用通俗易懂的方式讲解微积分概念", "content": "李永乐老师以其独特的科普风格，将复杂的微积分概念用生动的例子和直观的解释呈现给观众。", "learningGoals": "理解微积分的基本概念，掌握导数和积分的应用", "prerequisites": "高中数学", "resourceType": "VIDEO", "sourceType": "EXTERNAL", "sourceUrl": "https://space.bilibili.com/9458053", "sourcePlatform": "Bilibili", "difficultyLevel": "BEGINNER", "estimatedDurationHours": 20.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["李永乐", "微积分", "数学科普", "中文教学"], "contentType": "video", "metadata": {"author": {"name": "李永乐", "channel": "李永乐老师官方", "platform": "Bilibili"}, "keywords": ["微积分", "导数", "积分", "数学科普"], "publishDate": "2018-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "数学基础"}, {"title": "NumPy线性代数实践", "description": "使用NumPy进行线性代数计算的实践教程，结合代码演示数学概念", "content": "通过Python NumPy库的实际操作，将抽象的线性代数概念转化为可执行的代码，加深理解。", "learningGoals": "掌握NumPy线性代数函数，能够用代码实现数学计算", "prerequisites": "Python基础，线性代数基础", "resourceType": "PROJECT", "sourceType": "EXTERNAL", "sourceUrl": "https://github.com/numpy/numpy-tutorials", "sourcePlatform": "GitHub", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 15.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["NumPy", "线性代数", "Python", "实践项目"], "contentType": "interactive", "metadata": {"author": {"name": "NumPy Community", "platform": "GitHub"}, "keywords": ["NumPy", "线性代数", "Python", "数值计算"], "publishDate": "2020-01-01", "fileSize": 1024000}, "status": "ACTIVE", "createdBy": "system", "categoryName": "数学基础"}, {"title": "人工智能简史", "description": "从图灵测试到深度学习，全面回顾人工智能的发展历程和重要里程碑", "content": "详细介绍人工智能从1950年代诞生至今的发展历程，包括符号主义、连接主义、行为主义等不同学派的贡献。", "learningGoals": "了解AI发展历史，理解不同AI方法的演进，建立AI知识框架", "prerequisites": "无特殊要求", "resourceType": "DOCUMENT", "sourceType": "EXTERNAL", "sourceUrl": "https://www.jiqizhixin.com/articles/2019-07-12-12", "sourcePlatform": "机器之心", "difficultyLevel": "BEGINNER", "estimatedDurationHours": 3.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["AI历史", "人工智能", "发展历程", "科普"], "contentType": "article", "metadata": {"author": {"name": "机器之心编辑部", "platform": "机器之心"}, "keywords": ["人工智能", "AI历史", "图灵测试", "深度学习"], "publishDate": "2019-07-12", "wordCount": 8000, "readingTime": 20}, "status": "ACTIVE", "createdBy": "system", "categoryName": "AI概念入门"}, {"title": "李飞飞AI公开课", "description": "斯坦福李飞飞教授的AI入门课程，深入浅出地介绍人工智能基本概念", "content": "斯坦福大学李飞飞教授主讲的人工智能入门课程，涵盖机器学习、深度学习、计算机视觉等核心领域。", "learningGoals": "建立AI整体认知，理解机器学习基本原理，了解AI应用领域", "prerequisites": "基本数学知识", "resourceType": "VIDEO", "sourceType": "EXTERNAL", "sourceUrl": "https://www.bilibili.com/video/BV1K7411W7So", "sourcePlatform": "Bilibili", "difficultyLevel": "BEGINNER", "estimatedDurationHours": 25.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["李飞飞", "斯坦福", "AI入门", "中文字幕"], "contentType": "video", "metadata": {"author": {"name": "李飞飞", "department": "斯坦福大学", "title": "教授"}, "keywords": ["人工智能", "机器学习", "计算机视觉"], "publishDate": "2017-01-01"}, "contentConfig": {"videoType": "educational", "duration": 1500, "quality": ["1080p", "720p"], "isLive": false, "subtitles": [{"language": "zh-CN", "url": "", "defaultSubtitle": true}]}, "status": "ACTIVE", "createdBy": "system", "categoryName": "AI概念入门"}, {"title": "MIT 6.034 人工智能", "description": "MIT经典的人工智能入门课程，系统介绍AI的基本概念和方法", "content": "MIT计算机科学系的人工智能入门课程，涵盖搜索、知识表示、机器学习、神经网络等核心主题。", "learningGoals": "掌握AI基本概念，理解搜索算法，了解知识表示方法", "prerequisites": "编程基础，数学基础", "resourceType": "COURSE", "sourceType": "EXTERNAL", "sourceUrl": "https://ocw.mit.edu/courses/6-034-artificial-intelligence-fall-2010/", "sourcePlatform": "MIT OpenCourseWare", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 40.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["MIT", "人工智能", "搜索算法", "知识表示"], "contentType": "external_link", "metadata": {"author": {"name": "MIT EECS", "department": "MIT", "title": "Course"}, "keywords": ["人工智能", "搜索", "机器学习", "神经网络"], "publishDate": "2010-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "AI概念入门"}, {"title": "AI术语词典", "description": "全面的AI术语解释词典，涵盖机器学习、深度学习、NLP等领域的专业术语", "content": "收录了人工智能领域的重要术语和概念，提供准确的定义和解释，是学习AI的重要参考资料。", "learningGoals": "掌握AI专业术语，建立准确的概念理解", "prerequisites": "无", "resourceType": "TOOL_GUIDE", "sourceType": "EXTERNAL", "sourceUrl": "https://developers.google.com/machine-learning/glossary", "sourcePlatform": "Google Developers", "difficultyLevel": "BEGINNER", "estimatedDurationHours": 5.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["AI术语", "词典", "Google", "参考资料"], "contentType": "tool", "metadata": {"author": {"name": "Google Developers", "company": "Google"}, "keywords": ["AI术语", "机器学习", "深度学习", "词典"], "publishDate": "2018-01-01", "lastUpdated": "2024-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "AI概念入门"}, {"title": "代码随想录算法训练营", "description": "系统的算法学习路线和LeetCode题解，适合算法基础薄弱的学习者", "content": "提供完整的算法学习路线，包含数组、链表、栈队列、二叉树、回溯、贪心、动态规划等专题的详细讲解。", "learningGoals": "掌握常用数据结构和算法，提高编程能力和算法思维", "prerequisites": "基本编程能力", "resourceType": "PROJECT", "sourceType": "EXTERNAL", "sourceUrl": "https://github.com/youngyangyang04/leetcode-master", "sourcePlatform": "GitHub", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 30.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["算法训练", "LeetCode", "代码随想录", "系统学习"], "contentType": "interactive", "metadata": {"author": {"name": "代码随想录", "platform": "GitHub"}, "keywords": ["算法", "数据结构", "LeetCode", "编程训练"], "publishDate": "2020-01-01", "fileSize": 2048000}, "status": "ACTIVE", "createdBy": "system", "categoryName": "算法与数据结构"}, {"title": "labuladong算法小抄", "description": "通俗易懂的算法思维和解题套路，帮助理解算法的本质", "content": "用通俗的语言和生动的比喻，解释复杂的算法思想，提供解题的思维框架和套路。", "learningGoals": "建立算法思维，掌握解题套路，提高算法理解能力", "prerequisites": "基本编程能力", "resourceType": "DOCUMENT", "sourceType": "EXTERNAL", "sourceUrl": "https://github.com/labuladong/fucking-algorithm", "sourcePlatform": "GitHub", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 25.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["算法思维", "解题套路", "labuladong", "通俗易懂"], "contentType": "article", "metadata": {"author": {"name": "labuladong", "platform": "GitHub"}, "keywords": ["算法", "解题套路", "编程思维"], "publishDate": "2019-01-01", "wordCount": 50000, "readingTime": 120}, "status": "ACTIVE", "createdBy": "system", "categoryName": "算法与数据结构"}, {"title": "廖雪峰Python教程", "description": "最受欢迎的Python中文入门教程，从零基础到实战应用", "content": "系统全面的Python教程，涵盖基础语法、面向对象、函数式编程、常用库等内容，适合零基础学习者。", "learningGoals": "掌握Python基础语法，理解面向对象编程，能够编写简单的Python程序", "prerequisites": "无编程基础要求", "resourceType": "DOCUMENT", "sourceType": "EXTERNAL", "sourceUrl": "https://www.liaoxuefeng.com/wiki/1016959663602400", "sourcePlatform": "廖雪峰官网", "difficultyLevel": "BEGINNER", "estimatedDurationHours": 20.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["廖雪峰", "Python入门", "中文教程", "经典教材"], "contentType": "article", "metadata": {"author": {"name": "廖雪峰", "website": "https://www.liaoxuefeng.com"}, "keywords": ["Python", "编程入门", "基础语法"], "publishDate": "2013-01-01", "wordCount": 100000, "readingTime": 300}, "status": "ACTIVE", "createdBy": "system", "categoryName": "Python编程基础"}, {"title": "莫烦Python教程", "description": "系统的Python和机器学习教程，中文讲解，理论与实践并重", "content": "莫烦老师制作的Python教程系列，包含Python基础、NumPy、Pandas、Matplotlib等数据科学必备库的使用。", "learningGoals": "掌握Python数据科学库，具备数据处理和可视化能力", "prerequisites": "Python基础", "resourceType": "VIDEO", "sourceType": "EXTERNAL", "sourceUrl": "https://mofanpy.com/", "sourcePlatform": "莫烦Python", "difficultyLevel": "BEGINNER", "estimatedDurationHours": 35.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["莫烦Python", "中文教程", "机器学习", "系统学习"], "contentType": "video", "metadata": {"author": {"name": "莫烦", "platform": "莫烦Python"}, "keywords": ["Python", "NumPy", "<PERSON><PERSON>", "机器学习"], "publishDate": "2016-01-01"}, "contentConfig": {"videoType": "educational", "duration": 2100, "quality": ["1080p", "720p"], "isLive": false}, "status": "ACTIVE", "createdBy": "system", "categoryName": "Python编程基础"}, {"title": "吴恩达机器学习课程", "description": "斯坦福大学吴恩达教授的经典机器学习课程，ML领域的入门必修课", "content": "全球最受欢迎的机器学习入门课程，系统讲解监督学习、无监督学习、神经网络等核心概念。", "learningGoals": "理解机器学习基本概念，掌握常用算法原理，具备ML项目实践能力", "prerequisites": "线性代数、概率统计、编程基础", "resourceType": "COURSE", "sourceType": "EXTERNAL", "sourceUrl": "https://www.coursera.org/learn/machine-learning", "sourcePlatform": "Coursera", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 60.0, "language": "en-US", "isFree": false, "priceInfo": "$49/月", "tags": ["吴恩达", "机器学习", "Coursera", "经典课程"], "contentType": "external_link", "metadata": {"author": {"name": "<PERSON>", "department": "Stanford University", "title": "Professor"}, "keywords": ["机器学习", "监督学习", "神经网络"], "publishDate": "2011-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "监督学习"}, {"title": "Scikit-learn用户指南", "description": "Python机器学习库Scikit-learn的官方用户指南，包含丰富的算法实现", "content": "详细介绍Scikit-learn库的使用方法，涵盖分类、回归、聚类、降维等各种机器学习算法的实现。", "learningGoals": "掌握Scikit-learn库的使用，能够实现常用机器学习算法", "prerequisites": "Python基础，机器学习概念", "resourceType": "TOOL_GUIDE", "sourceType": "EXTERNAL", "sourceUrl": "https://scikit-learn.org/stable/user_guide.html", "sourcePlatform": "Scikit-learn", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 25.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["Scikit-learn", "机器学习", "Python库", "官方文档"], "contentType": "tool", "metadata": {"author": {"name": "Scikit-learn Team", "platform": "Scikit-learn"}, "keywords": ["Scikit-learn", "机器学习", "Python", "算法实现"], "publishDate": "2007-01-01", "lastUpdated": "2024-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "监督学习"}, {"title": "StatQuest机器学习", "description": "<PERSON>mer用简单动画解释复杂的统计和ML概念，生动易懂", "content": "通过精美的动画和简单的语言，将复杂的统计学和机器学习概念变得通俗易懂。", "learningGoals": "理解统计学习的核心概念，掌握算法的直观理解", "prerequisites": "基本数学知识", "resourceType": "VIDEO", "sourceType": "EXTERNAL", "sourceUrl": "https://www.youtube.com/c/joshstarmer", "sourcePlatform": "YouTube", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 15.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["StatQuest", "可视化解释", "统计学习", "动画教学"], "contentType": "video", "metadata": {"author": {"name": "<PERSON>", "channel": "StatQuest", "platform": "YouTube"}, "keywords": ["统计学", "机器学习", "可视化", "概念解释"], "publishDate": "2017-01-01"}, "contentConfig": {"videoType": "educational", "duration": 900, "quality": ["1080p", "720p"], "isLive": false}, "status": "ACTIVE", "createdBy": "system", "categoryName": "监督学习"}, {"title": "李沐《动手学深度学习》", "description": "亚马逊首席科学家李沐团队的深度学习教程，理论与实践完美结合", "content": "这是一本面向中文读者的能运行、可讨论的深度学习教科书，包含代码、数学和讨论。", "learningGoals": "掌握深度学习理论基础，具备实际项目开发能力", "prerequisites": "Python基础，线性代数，概率统计", "resourceType": "COURSE", "sourceType": "EXTERNAL", "sourceUrl": "https://zh.d2l.ai/", "sourcePlatform": "d2l.ai", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 120.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["李沐", "d2l.ai", "动手学习", "实践导向"], "contentType": "external_link", "metadata": {"author": {"name": "李沐", "company": "亚马逊", "title": "首席科学家"}, "keywords": ["深度学习", "神经网络", "PyTorch", "实践"], "publishDate": "2019-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "神经网络基础"}, {"title": "3Blue1Brown神经网络系列", "description": "用可视化方式解释神经网络的工作原理，直观理解深度学习", "content": "通过精美的动画演示，从神经元到反向传播，深入浅出地解释神经网络的核心概念。", "learningGoals": "建立神经网络的直观理解，掌握反向传播算法原理", "prerequisites": "基本数学知识", "resourceType": "VIDEO", "sourceType": "EXTERNAL", "sourceUrl": "https://www.bilibili.com/video/BV1bx411M7Zx", "sourcePlatform": "Bilibili", "difficultyLevel": "BEGINNER", "estimatedDurationHours": 8.0, "language": "zh-CN", "isFree": true, "priceInfo": "免费", "tags": ["3Blue1Brown", "神经网络", "可视化", "反向传播"], "contentType": "video", "metadata": {"author": {"name": "3Blue1Brown", "platform": "YouTube/Bilibili"}, "keywords": ["神经网络", "深度学习", "可视化", "数学"], "publishDate": "2017-10-01"}, "contentConfig": {"videoType": "educational", "duration": 480, "quality": ["1080p", "720p"], "isLive": false, "subtitles": [{"language": "zh-CN", "url": "", "defaultSubtitle": true}]}, "status": "ACTIVE", "createdBy": "system", "categoryName": "神经网络基础"}, {"title": "CS231n斯坦福视觉识别课程", "description": "斯坦福大学计算机视觉课程，深度学习在图像识别中的应用", "content": "由李飞飞教授团队开设的计算机视觉课程，系统讲解CNN、目标检测、图像分割等技术。", "learningGoals": "掌握计算机视觉核心技术，理解CNN架构设计", "prerequisites": "深度学习基础，Python编程", "resourceType": "COURSE", "sourceType": "EXTERNAL", "sourceUrl": "http://cs231n.stanford.edu/", "sourcePlatform": "Stanford University", "difficultyLevel": "ADVANCED", "estimatedDurationHours": 50.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["CS231n", "斯坦福", "计算机视觉", "CNN"], "contentType": "external_link", "metadata": {"author": {"name": "Stanford CS231n Team", "department": "Stanford University"}, "keywords": ["计算机视觉", "CNN", "目标检测", "图像分割"], "publishDate": "2015-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "卷积神经网络"}, {"title": "OpenCV官方教程", "description": "计算机视觉库OpenCV的官方教程，从基础到高级应用", "content": "全面介绍OpenCV库的使用方法，涵盖图像处理、特征检测、机器学习等功能。", "learningGoals": "掌握OpenCV库的使用，具备计算机视觉项目开发能力", "prerequisites": "Python或C++基础", "resourceType": "TOOL_GUIDE", "sourceType": "EXTERNAL", "sourceUrl": "https://docs.opencv.org/4.x/d9/df8/tutorial_root.html", "sourcePlatform": "OpenCV", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 30.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["OpenCV", "计算机视觉", "图像处理", "官方文档"], "contentType": "tool", "metadata": {"author": {"name": "OpenCV Team", "platform": "OpenCV"}, "keywords": ["OpenCV", "计算机视觉", "图像处理", "特征检测"], "publishDate": "2000-01-01", "lastUpdated": "2024-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "计算机视觉"}, {"title": "斯坦福CS224N自然语言处理", "description": "斯坦福大学NLP课程，深度学习在自然语言处理中的应用", "content": "系统讲解自然语言处理的核心技术，包括词向量、RNN、Transformer、BERT等前沿方法。", "learningGoals": "掌握NLP核心技术，理解语言模型原理", "prerequisites": "深度学习基础，Python编程", "resourceType": "COURSE", "sourceType": "EXTERNAL", "sourceUrl": "http://web.stanford.edu/class/cs224n/", "sourcePlatform": "Stanford University", "difficultyLevel": "ADVANCED", "estimatedDurationHours": 45.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["CS224N", "斯坦福", "NLP", "Transformer"], "contentType": "external_link", "metadata": {"author": {"name": "Stanford CS224N Team", "department": "Stanford University"}, "keywords": ["NLP", "Transformer", "BERT", "语言模型"], "publishDate": "2017-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "自然语言处理"}, {"title": "Hugging Face Transformers", "description": "最流行的Transformer模型库，提供预训练模型和简单易用的API", "content": "包含BERT、GPT、T5等主流预训练模型，提供简单的API接口，支持多种NLP任务。", "learningGoals": "掌握预训练模型的使用，能够快速开发NLP应用", "prerequisites": "Python基础，NLP概念", "resourceType": "TOOL_GUIDE", "sourceType": "EXTERNAL", "sourceUrl": "https://huggingface.co/docs/transformers/index", "sourcePlatform": "Hugging Face", "difficultyLevel": "INTERMEDIATE", "estimatedDurationHours": 20.0, "language": "en-US", "isFree": true, "priceInfo": "免费", "tags": ["Hugging Face", "Transformers", "预训练模型", "NLP"], "contentType": "tool", "metadata": {"author": {"name": "Hugging Face Team", "company": "Hugging Face"}, "keywords": ["Transformers", "BERT", "GPT", "预训练模型"], "publishDate": "2018-01-01", "lastUpdated": "2024-01-01"}, "status": "ACTIVE", "createdBy": "system", "categoryName": "自然语言处理"}]