# 多数据源配置说明

## 概述

系统已配置三个数据源，分别对应不同的业务场景：

1. **主数据源 (Primary)** - `ai_community_shared`
   - 核心业务数据
   - 用户管理、内容管理、评论等

2. **管理端数据源 (Admin)** - `ai_community_admin`
   - 管理后台相关数据
   - 系统配置、操作日志、权限管理等

3. **门户端数据源 (Portal)** - `ai_community_portal`
   - 门户展示相关数据
   - 统计数据、缓存数据、展示配置等

## 配置文件

### application.yml
```yaml
spring:
  datasource:
    # 主数据源
    primary:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ******************************************
      username: your_username
      password: your_password
      druid:
        initial-size: 5
        min-idle: 10
        max-active: 20
        # ... 其他配置
    
    # 管理端数据源
    admin:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *****************************************
      username: your_username
      password: your_password
      druid:
        initial-size: 3
        min-idle: 5
        max-active: 15
        # ... 其他配置
    
    # 门户端数据源
    portal:
      type: com.alibaba.druid.pool.DruidDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ******************************************
      username: your_username
      password: your_password
      druid:
        initial-size: 3
        min-idle: 5
        max-active: 15
        # ... 其他配置
```

## 目录结构

```
dao/
├── entity/
│   ├── primary/     # 主数据源实体类
│   ├── admin/       # 管理端数据源实体类
│   └── portal/      # 门户端数据源实体类
├── mapper/
│   ├── primary/     # 主数据源Mapper接口
│   ├── admin/       # 管理端数据源Mapper接口
│   └── portal/      # 门户端数据源Mapper接口
└── resources/
    └── mapper/
        ├── primary/ # 主数据源Mapper XML
        ├── admin/   # 管理端数据源Mapper XML
        └── portal/  # 门户端数据源Mapper XML
```

## 使用方法

### 1. 创建实体类
```java
// 主数据源实体
package com.jdl.aic.core.service.dao.entity.primary;
public class User {
    // 用户实体
}

// 管理端数据源实体
package com.jdl.aic.core.service.dao.entity.admin;
public class SystemConfig {
    // 系统配置实体
}

// 门户端数据源实体
package com.jdl.aic.core.service.dao.entity.portal;
public class PortalStats {
    // 门户统计实体
}
```

### 2. 创建Mapper接口
```java
// 主数据源Mapper
package com.jdl.aic.core.service.dao.mapper.primary;
public interface UserMapper {
    // 用户相关操作
}

// 管理端数据源Mapper
package com.jdl.aic.core.service.dao.mapper.admin;
public interface SystemConfigMapper {
    // 系统配置相关操作
}

// 门户端数据源Mapper
package com.jdl.aic.core.service.dao.mapper.portal;
public interface PortalStatsMapper {
    // 门户统计相关操作
}
```

### 3. 在Service中使用事务
```java
@Service
public class UserService {
    
    // 使用主数据源事务
    @Transactional(transactionManager = "primaryTransactionManager")
    public void createUser(User user) {
        // 主数据源操作
    }
}

@Service
public class AdminService {
    
    // 使用管理端数据源事务
    @Transactional(transactionManager = "adminTransactionManager")
    public void updateSystemConfig(SystemConfig config) {
        // 管理端数据源操作
    }
}

@Service
public class PortalService {
    
    // 使用门户端数据源事务
    @Transactional(transactionManager = "portalTransactionManager")
    public void updateStats(PortalStats stats) {
        // 门户端数据源操作
    }
}
```

### 4. 直接注入数据源
```java
@Service
public class CustomService {
    
    @Autowired
    @Qualifier("primaryDataSource")
    private DataSource primaryDataSource;
    
    @Autowired
    @Qualifier("adminDataSource")
    private DataSource adminDataSource;
    
    @Autowired
    @Qualifier("portalDataSource")
    private DataSource portalDataSource;
    
    // 使用数据源进行操作
}
```

## 数据库创建

```sql
-- 创建数据库
CREATE DATABASE ai_community_shared CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE ai_community_admin CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE ai_community_portal CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 授权用户访问
GRANT ALL PRIVILEGES ON ai_community_shared.* TO 'your_username'@'%';
GRANT ALL PRIVILEGES ON ai_community_admin.* TO 'your_username'@'%';
GRANT ALL PRIVILEGES ON ai_community_portal.* TO 'your_username'@'%';
FLUSH PRIVILEGES;
```

## 注意事项

1. **包路径规范**：严格按照包路径放置文件，系统会根据包路径自动选择对应的数据源
2. **事务管理**：使用对应的事务管理器，避免跨数据源事务问题
3. **连接池配置**：根据业务需求调整各数据源的连接池参数
4. **监控配置**：Druid监控页面会显示所有数据源的状态
5. **测试环境**：测试环境也配置了对应的多数据源，连接池参数较小

## 迁移指南

如果需要将现有的Mapper迁移到多数据源：

1. 将Mapper接口移动到对应的包路径下
2. 将Mapper XML文件移动到对应的resources目录下
3. 将实体类移动到对应的包路径下
4. 更新Service中的事务管理器注解
5. 测试数据源连接和业务功能
