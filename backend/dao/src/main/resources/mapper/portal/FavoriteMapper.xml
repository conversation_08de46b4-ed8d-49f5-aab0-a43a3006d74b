<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.FavoriteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.Favorite">
        <id column="id" property="id" />
        <result column="content_type" property="contentType" />
        <result column="content_id" property="contentId" />
        <result column="related_knowledge_type_id" property="relatedKnowledgeTypeId" />
        <result column="user_id" property="userId" />
        <result column="created_at" property="createdAt" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content_type, content_id, related_knowledge_type_id, user_id, created_at, deleted_at
    </sql>

    <!-- 插入收藏记录 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.Favorite" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO favorite (
            content_type, content_id, related_knowledge_type_id, user_id, created_at
        ) VALUES (
            #{contentType}, #{contentId}, #{relatedKnowledgeTypeId}, #{userId}, #{createdAt}
        )
    </insert>

    <!-- 根据ID删除收藏记录（物理删除） -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM favorite WHERE id = #{id}
    </delete>

    <!-- 软删除收藏记录 -->
    <update id="softDeleteById">
        UPDATE favorite
        SET deleted_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据用户ID和内容信息删除收藏记录 -->
    <delete id="deleteByUserAndContent">
        DELETE FROM favorite 
        WHERE user_id = #{userId} 
        AND content_type = #{contentType} 
        AND content_id = #{contentId}
    </delete>

    <!-- 根据ID查询收藏记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM favorite
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID查询收藏列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM favorite
        WHERE user_id = #{userId} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID和内容类型查询收藏列表 -->
    <select id="selectByUserIdAndContentType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM favorite
        WHERE user_id = #{userId} 
        AND content_type = #{contentType} 
        AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据内容信息查询收藏列表 -->
    <select id="selectByContent" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM favorite
        WHERE content_type = #{contentType} 
        AND content_id = #{contentId} 
        AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 检查用户是否已收藏指定内容 -->
    <select id="selectByUserAndContent" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM favorite
        WHERE user_id = #{userId} 
        AND content_type = #{contentType} 
        AND content_id = #{contentId}
        AND deleted_at IS NULL
        LIMIT 1
    </select>

    <!-- 根据条件查询收藏列表 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM favorite
        <where>
            deleted_at IS NULL
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="contentType != null">
                AND content_type = #{contentType}
            </if>
            <if test="relatedKnowledgeTypeId != null">
                AND related_knowledge_type_id = #{relatedKnowledgeTypeId}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 统计用户收藏总数 -->
    <select id="countByUserId" resultType="int">
        SELECT COUNT(*)
        FROM favorite
        WHERE user_id = #{userId} AND deleted_at IS NULL
    </select>

    <!-- 统计用户指定类型内容的收藏数 -->
    <select id="countByUserIdAndContentType" resultType="int">
        SELECT COUNT(*)
        FROM favorite
        WHERE user_id = #{userId} 
        AND content_type = #{contentType} 
        AND deleted_at IS NULL
    </select>

    <!-- 统计指定内容的收藏数 -->
    <select id="countByContent" resultType="int">
        SELECT COUNT(*)
        FROM favorite
        WHERE content_type = #{contentType} 
        AND content_id = #{contentId} 
        AND deleted_at IS NULL
    </select>

    <!-- 统计指定内容列表的收藏数 -->
    <select id="countByContentIds" resultType="java.util.Map">
        SELECT content_id as contentId, COUNT(*) as count
        FROM favorite
        WHERE content_type = #{contentType} 
        AND content_id IN
        <foreach collection="contentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
        GROUP BY content_id
    </select>

    <!-- 批量插入收藏记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO favorite (
            content_type, content_id, related_knowledge_type_id, user_id, created_at
        ) VALUES
        <foreach collection="favorites" item="favorite" separator=",">
            (#{favorite.contentType}, #{favorite.contentId}, #{favorite.relatedKnowledgeTypeId}, #{favorite.userId}, #{favorite.createdAt})
        </foreach>
    </insert>

    <!-- 批量删除收藏记录 -->
    <delete id="batchDeleteByIds">
        DELETE FROM favorite
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量软删除收藏记录 -->
    <update id="batchSoftDeleteByIds">
        UPDATE favorite
        SET deleted_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 恢复已删除的收藏记录 -->
    <update id="restoreById">
        UPDATE favorite
        SET deleted_at = NULL
        WHERE id = #{id}
    </update>

    <!-- 清理指定天数前的已删除收藏记录 -->
    <delete id="cleanupDeletedRecords">
        DELETE FROM favorite
        WHERE deleted_at IS NOT NULL
        AND deleted_at &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

    <!-- 获取用户最近收藏的内容 -->
    <select id="selectRecentByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM favorite
        WHERE user_id = #{userId} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 获取热门收藏内容 -->
    <select id="selectPopularContent" resultType="java.util.Map">
        SELECT content_id as contentId, COUNT(*) as favoriteCount
        FROM favorite
        WHERE content_type = #{contentType} 
        AND deleted_at IS NULL
        <if test="days != null">
            AND created_at >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        </if>
        GROUP BY content_id
        ORDER BY favoriteCount DESC
    </select>

    <!-- 检查收藏记录是否存在 -->
    <select id="existsByUserAndContent" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM favorite
        WHERE user_id = #{userId} 
        AND content_type = #{contentType} 
        AND content_id = #{contentId}
    </select>

</mapper>
