<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.UserBadgesMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.UserBadges">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="user_id" jdbcType="BIGINT"/>
        <result column="badge_id" property="badge_id" jdbcType="BIGINT"/>
        <result column="earned_at" property="earned_at" jdbcType="TIMESTAMP"/>
        <result column="created_at" property="created_at" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updated_at" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="created_by" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, user_id, badge_id, earned_at, created_at, updated_at, created_by, deleted
    </sql>

    <!-- 根据ID查询用户徽章关联 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_badges
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID查询用户的所有徽章 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_badges
        WHERE user_id = #{userId} AND deleted = 0
        ORDER BY earned_at DESC
    </select>

    <!-- 根据徽章ID查询拥有该徽章的所有用户 -->
    <select id="selectByBadgeId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_badges
        WHERE badge_id = #{badgeId} AND deleted = 0
        ORDER BY earned_at DESC
    </select>

    <!-- 根据用户ID和徽章ID查询关联记录 -->
    <select id="selectByUserIdAndBadgeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_badges
        WHERE user_id = #{userId} AND badge_id = #{badgeId}
    </select>

    <!-- 查询所有用户徽章关联 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_badges
        ORDER BY created_at DESC
    </select>

    <!-- 查询未删除的用户徽章关联 -->
    <select id="selectActive" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM user_badges
        WHERE deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 插入用户徽章关联 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserBadges" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_badges (
            user_id, badge_id, earned_at, created_at, updated_at, created_by, deleted
        ) VALUES (
            #{user_id}, #{badge_id}, #{earned_at}, #{created_at}, #{updated_at}, #{created_by}, #{deleted}
        )
    </insert>

    <!-- 根据ID更新用户徽章关联 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserBadges">
        UPDATE user_badges
        <set>
            <if test="user_id != null">user_id = #{user_id},</if>
            <if test="badge_id != null">badge_id = #{badge_id},</if>
            <if test="earned_at != null">earned_at = #{earned_at},</if>
            <if test="updated_at != null">updated_at = #{updated_at},</if>
            <if test="created_by != null">created_by = #{created_by},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除用户徽章关联（物理删除） -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM user_badges WHERE id = #{id}
    </delete>

    <!-- 根据ID软删除用户徽章关联 -->
    <update id="softDeleteById" parameterType="java.lang.Long">
        UPDATE user_badges
        SET deleted = 1, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据用户ID和徽章ID删除关联 -->
    <delete id="deleteByUserIdAndBadgeId">
        DELETE FROM user_badges 
        WHERE user_id = #{userId} AND badge_id = #{badgeId}
    </delete>

    <!-- 统计用户徽章关联数量 -->
    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM user_badges
    </select>

    <!-- 统计用户的徽章数量 -->
    <select id="countByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM user_badges WHERE user_id = #{userId} AND deleted = 0
    </select>

    <!-- 统计徽章的用户数量 -->
    <select id="countByBadgeId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM user_badges WHERE badge_id = #{badgeId} AND deleted = 0
    </select>

    <!-- 批量插入用户徽章关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_badges (
            user_id, badge_id, earned_at, created_at, updated_at, created_by, deleted
        ) VALUES
        <foreach collection="userBadges" item="userBadge" separator=",">
            (#{userBadge.user_id}, #{userBadge.badge_id}, #{userBadge.earned_at}, 
             #{userBadge.created_at}, #{userBadge.updated_at}, #{userBadge.created_by}, #{userBadge.deleted})
        </foreach>
    </insert>

    <!-- 批量删除用户的徽章 -->
    <delete id="batchDeleteByUserId">
        DELETE FROM user_badges 
        WHERE user_id = #{userId} AND badge_id IN
        <foreach collection="badgeIds" item="badgeId" open="(" separator="," close=")">
            #{badgeId}
        </foreach>
    </delete>

    <!-- 检查用户是否拥有指定徽章 -->
    <select id="existsByUserIdAndBadgeId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM user_badges 
        WHERE user_id = #{userId} AND badge_id = #{badgeId} AND deleted = 0
    </select>

    <!-- 获取用户徽章统计信息 -->
    <select id="getUserBadgeStatistics" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_badges,
            COUNT(CASE WHEN earned_at &gt;= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_badges
        FROM user_badges 
        WHERE user_id = #{userId} AND deleted = 0
    </select>

</mapper>
