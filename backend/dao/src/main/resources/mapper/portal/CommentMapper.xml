<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.CommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.Comment">
        <id column="id" property="id" />
        <result column="content_type" property="contentType" />
        <result column="content_id" property="contentId" />
        <result column="related_knowledge_type_id" property="relatedKnowledgeTypeId" />
        <result column="user_id" property="userId" />
        <result column="parent_comment_id" property="parentCommentId" />
        <result column="comment_text" property="commentText" />
        <result column="status" property="status" />
        <result column="like_count" property="likeCount" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content_type, content_id, related_knowledge_type_id, user_id, parent_comment_id, 
        comment_text, status, like_count, created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <!-- 插入评论 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.Comment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO comment (
            content_type, content_id, related_knowledge_type_id, user_id, parent_comment_id,
            comment_text, status, like_count, created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{contentType}, #{contentId}, #{relatedKnowledgeTypeId}, #{userId}, #{parentCommentId},
            #{commentText}, #{status}, #{likeCount}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除评论（物理删除） -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM comment WHERE id = #{id}
    </delete>

    <!-- 软删除评论 -->
    <update id="softDeleteById">
        UPDATE comment
        SET status = 2, deleted_at = NOW(), updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新评论信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.Comment">
        UPDATE comment
        <set>
            <if test="commentText != null">comment_text = #{commentText},</if>
            <if test="status != null">status = #{status},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询评论 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        WHERE id = #{id}
    </select>

    <!-- 根据内容信息查询评论列表 -->
    <select id="selectByContent" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        WHERE content_type = #{contentType} 
        AND content_id = #{contentId} 
        AND status != 2 
        AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID查询评论列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        WHERE user_id = #{userId} 
        AND status != 2 
        AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据父评论ID查询回复列表 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        WHERE parent_comment_id = #{parentCommentId} 
        AND status != 2 
        AND deleted_at IS NULL
        ORDER BY created_at ASC
    </select>

    <!-- 根据条件查询评论列表 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        <where>
            deleted_at IS NULL
            <if test="contentType != null">
                AND content_type = #{contentType}
            </if>
            <if test="contentId != null">
                AND content_id = #{contentId}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="parentCommentId != null">
                AND parent_comment_id = #{parentCommentId}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 查询顶级评论列表 -->
    <select id="selectTopLevelComments" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        WHERE content_type = #{contentType} 
        AND content_id = #{contentId} 
        AND parent_comment_id IS NULL
        AND deleted_at IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 统计指定内容的评论数 -->
    <select id="countByContent" resultType="int">
        SELECT COUNT(*)
        FROM comment
        WHERE content_type = #{contentType} 
        AND content_id = #{contentId} 
        AND status != 2 
        AND deleted_at IS NULL
    </select>

    <!-- 统计用户评论总数 -->
    <select id="countByUserId" resultType="int">
        SELECT COUNT(*)
        FROM comment
        WHERE user_id = #{userId} 
        AND status != 2 
        AND deleted_at IS NULL
    </select>

    <!-- 统计指定评论的回复数 -->
    <select id="countRepliesByParentId" resultType="int">
        SELECT COUNT(*)
        FROM comment
        WHERE parent_comment_id = #{parentCommentId} 
        AND status != 2 
        AND deleted_at IS NULL
    </select>

    <!-- 统计指定内容列表的评论数 -->
    <select id="countByContentIds" resultType="java.util.Map">
        SELECT content_id as contentId, COUNT(*) as count
        FROM comment
        WHERE content_type = #{contentType} 
        AND content_id IN
        <foreach collection="contentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status != 2 
        AND deleted_at IS NULL
        GROUP BY content_id
    </select>

    <!-- 统计各状态评论数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM comment
        <where>
            deleted_at IS NULL
            <if test="contentType != null">
                AND content_type = #{contentType}
            </if>
            <if test="contentId != null">
                AND content_id = #{contentId}
            </if>
        </where>
        GROUP BY status
    </select>

    <!-- 更新评论状态 -->
    <update id="updateStatus">
        UPDATE comment
        SET status = #{status}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量更新评论状态 -->
    <update id="batchUpdateStatus">
        UPDATE comment
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 恢复已删除的评论 -->
    <update id="restoreById">
        UPDATE comment
        SET status = 0, deleted_at = NULL, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 增加评论点赞数 -->
    <update id="incrementLikeCount">
        UPDATE comment
        SET like_count = COALESCE(like_count, 0) + 1, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 减少评论点赞数 -->
    <update id="decrementLikeCount">
        UPDATE comment
        SET like_count = GREATEST(COALESCE(like_count, 0) - 1, 0), updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新评论点赞数 -->
    <update id="updateLikeCount">
        UPDATE comment
        SET like_count = #{likeCount}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量插入评论 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO comment (
            content_type, content_id, related_knowledge_type_id, user_id, parent_comment_id,
            comment_text, status, like_count, created_at, updated_at, created_by, updated_by
        ) VALUES
        <foreach collection="comments" item="comment" separator=",">
            (#{comment.contentType}, #{comment.contentId}, #{comment.relatedKnowledgeTypeId}, 
             #{comment.userId}, #{comment.parentCommentId}, #{comment.commentText}, 
             #{comment.status}, #{comment.likeCount}, #{comment.createdAt}, #{comment.updatedAt}, 
             #{comment.createdBy}, #{comment.updatedBy})
        </foreach>
    </insert>

    <!-- 批量删除评论 -->
    <delete id="batchDeleteByIds">
        DELETE FROM comment
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量软删除评论 -->
    <update id="batchSoftDeleteByIds">
        UPDATE comment
        SET status = 2, deleted_at = NOW(), updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 获取用户最近评论 -->
    <select id="selectRecentByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        WHERE user_id = #{userId} 
        AND status != 2 
        AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 获取热门评论 -->
    <select id="selectPopularComments" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        WHERE content_type = #{contentType} 
        AND content_id = #{contentId} 
        AND status != 2 
        AND deleted_at IS NULL
        ORDER BY like_count DESC, created_at DESC
    </select>

    <!-- 搜索评论内容 -->
    <select id="searchComments" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM comment
        <where>
            comment_text LIKE CONCAT('%', #{keyword}, '%')
            AND status != 2 
            AND deleted_at IS NULL
            <if test="contentType != null">
                AND content_type = #{contentType}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 获取评论树结构 -->
    <select id="selectCommentTree" resultMap="BaseResultMap">
        WITH RECURSIVE comment_tree AS (
            SELECT <include refid="Base_Column_List" />, 0 as depth
            FROM comment
            WHERE content_type = #{contentType} 
            AND content_id = #{contentId} 
            AND parent_comment_id IS NULL
            AND status != 2 
            AND deleted_at IS NULL
            
            UNION ALL
            
            SELECT c.<include refid="Base_Column_List" />, ct.depth + 1
            FROM comment c
            INNER JOIN comment_tree ct ON c.parent_comment_id = ct.id
            WHERE c.status != 2 
            AND c.deleted_at IS NULL
            AND ct.depth &lt; #{maxDepth}
        )
        SELECT * FROM comment_tree
        ORDER BY depth, created_at DESC
    </select>

    <!-- 清理指定天数前的已删除评论记录 -->
    <delete id="cleanupDeletedRecords">
        DELETE FROM comment
        WHERE deleted_at IS NOT NULL
        AND deleted_at &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

    <!-- 检查评论是否存在 -->
    <select id="existsById" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM comment
        WHERE id = #{id}
    </select>

</mapper>
