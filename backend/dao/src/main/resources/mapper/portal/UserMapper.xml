<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.User">
        <id column="id" property="id" />
        <result column="sso_id" property="ssoId" />
        <result column="username" property="username" />
        <result column="display_name" property="displayName" />
        <result column="email" property="email" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="department" property="department" />
        <result column="title" property="title" />
        <result column="bio" property="bio" />
        <result column="tags" property="tags" />
        <result column="is_active" property="isActive" />
        <result column="last_login_at" property="lastLoginAt" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sso_id, username, display_name, email, avatar_url, department, title, bio, tags, is_active, last_login_at, created_at, updated_at
    </sql>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user (
            id, sso_id, username, display_name, email, avatar_url, department, title, bio, tags, is_active, last_login_at, created_at, updated_at
        ) VALUES (
            #{id}, #{ssoId}, #{username}, #{displayName}, #{email}, #{avatarUrl}, #{department}, #{title}, #{bio}, #{tags}, #{isActive}, #{lastLoginAt}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 根据ID删除用户（这里实际上是软删除，但由于表结构没有deleted_at字段，暂时使用硬删除） -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM user WHERE id = #{id}
    </delete>

    <!-- 更新用户信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.User">
        UPDATE user
        <set>
            <if test="ssoId != null">sso_id = #{ssoId},</if>
            <if test="username != null">username = #{username},</if>
            <if test="displayName != null">display_name = #{displayName},</if>
            <if test="email != null">email = #{email},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="department != null">department = #{department},</if>
            <if test="title != null">title = #{title},</if>
            <if test="bio != null">bio = #{bio},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="lastLoginAt != null">last_login_at = #{lastLoginAt},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询用户 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE id = #{id}
    </select>

    <!-- 根据SSO ID查询用户 -->
    <select id="selectBySsoId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE sso_id = #{ssoId}
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE username = #{username}
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="selectByEmail" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE email = #{email}
    </select>

    <!-- 根据条件查询用户列表 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        <where>
            <if test="department != null and department != ''">
                AND department = #{department}
            </if>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
            <if test="search != null and search != ''">
                AND (
                    username LIKE CONCAT('%', #{search}, '%')
                    OR display_name LIKE CONCAT('%', #{search}, '%')
                    OR email LIKE CONCAT('%', #{search}, '%')
                )
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户实体条件查询用户列表 -->
    <select id="selectByUserCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        <where>
            <if test="user.department != null and user.department != ''">
                AND department = #{user.department}
            </if>
            <if test="user.isActive != null">
                AND is_active = #{user.isActive}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID列表查询用户 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        WHERE id IN
        <foreach collection="userIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY created_at DESC
    </select>

    <!-- 启用/禁用用户 -->
    <update id="toggleUserStatus">
        UPDATE user
        SET is_active = #{isActive}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新用户最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE user
        SET last_login_at = #{lastLoginAt}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量插入用户 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user (
            sso_id, username, display_name, email, avatar_url, department, title, bio, tags, is_active, last_login_at, created_at, updated_at
        ) VALUES
        <foreach collection="users" item="user" separator=",">
            (#{user.ssoId}, #{user.username}, #{user.displayName}, #{user.email}, #{user.avatarUrl}, #{user.department}, #{user.title}, #{user.bio}, #{user.tags}, #{user.isActive}, #{user.lastLoginAt}, #{user.createdAt}, #{user.updatedAt})
        </foreach>
    </insert>

    <!-- 批量更新用户状态 -->
    <update id="batchUpdateStatus">
        UPDATE user
        SET is_active = #{isActive}, updated_at = NOW()
        WHERE id IN
        <foreach collection="userIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 统计用户总数 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(*)
        FROM user
        <where>
            <if test="department != null and department != ''">
                AND department = #{department}
            </if>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
            <if test="search != null and search != ''">
                AND (
                    username LIKE CONCAT('%', #{search}, '%')
                    OR display_name LIKE CONCAT('%', #{search}, '%')
                    OR email LIKE CONCAT('%', #{search}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 统计活跃用户数 -->
    <select id="countActiveUsers" resultType="int">
        SELECT COUNT(*)
        FROM user
        WHERE is_active = 1
    </select>

    <!-- 根据部门统计用户数 -->
    <select id="countByDepartment" resultType="int">
        SELECT COUNT(*)
        FROM user
        WHERE department = #{department}
    </select>

    <!-- 搜索用户 -->
    <select id="searchUsers" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user
        <where>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                    username LIKE CONCAT('%', #{keyword}, '%')
                    OR display_name LIKE CONCAT('%', #{keyword}, '%')
                    OR email LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 检查SSO ID是否存在 -->
    <select id="existsBySsoId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user
        WHERE sso_id = #{ssoId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user
        WHERE username = #{username}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user
        WHERE email = #{email}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

</mapper>
