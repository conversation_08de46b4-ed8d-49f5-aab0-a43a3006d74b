<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.UserFollowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.UserFollow">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="followed_id" property="followedId" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 包含被关注人信息的查询映射结果 -->
    <resultMap id="UserFollowWithUserInfoResultMap" type="com.jdl.aic.core.service.dao.entity.portal.UserFollow">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="followed_id" property="followedId" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <!-- 被关注人信息字段 -->
        <result column="followed_username" property="followedUsername" />
        <result column="followed_display_name" property="followedDisplayName" />
        <result column="followed_email" property="followedEmail" />
        <result column="followed_avatar_url" property="followedAvatarUrl" />
        <result column="followed_department" property="followedDepartment" />
        <result column="followed_title" property="followedTitle" />
        <result column="followed_bio" property="followedBio" />
        <result column="followed_is_active" property="followedIsActive" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, followed_id, created_at, updated_at
    </sql>

    <!-- 包含被关注人信息的查询结果列 -->
    <sql id="User_Info_Column_List">
        uf.id, uf.user_id, uf.followed_id, uf.created_at, uf.updated_at,
        u.username as followed_username, u.display_name as followed_display_name, 
        u.email as followed_email, u.avatar_url as followed_avatar_url, 
        u.department as followed_department, u.title as followed_title, 
        u.bio as followed_bio, u.is_active as followed_is_active
    </sql>

    <!-- 插入用户关注记录 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserFollow" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_follow (
            user_id, followed_id, created_at, updated_at
        ) VALUES (
            #{userId}, #{followedId}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 根据ID删除用户关注记录（物理删除） -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM user_follow WHERE id = #{id}
    </delete>

    <!-- 根据用户ID和被关注人ID删除关注记录 -->
    <delete id="deleteByUserIdAndFollowedId">
        DELETE FROM user_follow 
        WHERE user_id = #{userId} AND followed_id = #{followedId}
    </delete>

    <!-- 更新用户关注记录 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserFollow">
        UPDATE user_follow
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="followedId != null">followed_id = #{followedId},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询用户关注记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_follow
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID和被关注人ID查询关注记录 -->
    <select id="selectByUserIdAndFollowedId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_follow
        WHERE user_id = #{userId} AND followed_id = #{followedId}
        LIMIT 1
    </select>

    <!-- 根据用户ID查询关注列表（用户关注的人） -->
    <select id="selectFollowingByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_follow
        WHERE user_id = #{userId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID查询关注列表（包含被关注人信息） -->
    <select id="selectFollowingByUserIdWithUserInfo" parameterType="java.lang.Long" resultMap="UserFollowWithUserInfoResultMap">
        SELECT <include refid="User_Info_Column_List" />
        FROM user_follow uf
        LEFT JOIN user u ON uf.followed_id = u.id
        WHERE uf.user_id = #{userId}
        ORDER BY uf.created_at DESC
    </select>

    <!-- 根据被关注人ID查询粉丝列表 -->
    <select id="selectFollowersByFollowedId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_follow
        WHERE followed_id = #{followedId}
        ORDER BY created_at DESC
    </select>

    <!-- 根据被关注人ID查询粉丝列表（包含关注者信息） -->
    <select id="selectFollowersByFollowedIdWithUserInfo" parameterType="java.lang.Long" resultMap="UserFollowWithUserInfoResultMap">
        SELECT uf.id, uf.user_id, uf.followed_id, uf.created_at, uf.updated_at,
               u.username as followed_username, u.display_name as followed_display_name, 
               u.email as followed_email, u.avatar_url as followed_avatar_url, 
               u.department as followed_department, u.title as followed_title, 
               u.bio as followed_bio, u.is_active as followed_is_active
        FROM user_follow uf
        LEFT JOIN user u ON uf.user_id = u.id
        WHERE uf.followed_id = #{followedId}
        ORDER BY uf.created_at DESC
    </select>

    <!-- 根据条件查询用户关注列表 -->
    <select id="selectByCondition" resultMap="UserFollowWithUserInfoResultMap">
        SELECT <include refid="User_Info_Column_List" />
        FROM user_follow uf
        LEFT JOIN user u ON 
        <choose>
            <when test="followType == 'followers'">
                uf.user_id = u.id
            </when>
            <otherwise>
                uf.followed_id = u.id
            </otherwise>
        </choose>
        <where>
            <choose>
                <when test="followType == 'followers'">
                    uf.followed_id = #{userId}
                </when>
                <otherwise>
                    uf.user_id = #{userId}
                </otherwise>
            </choose>
            <if test="followedUsername != null and followedUsername != ''">
                AND u.username LIKE CONCAT('%', #{followedUsername}, '%')
            </if>
            <if test="followedDisplayName != null and followedDisplayName != ''">
                AND u.display_name LIKE CONCAT('%', #{followedDisplayName}, '%')
            </if>
            <if test="followedDepartment != null and followedDepartment != ''">
                AND u.department = #{followedDepartment}
            </if>
            <if test="followedIsActive != null">
                AND u.is_active = #{followedIsActive}
            </if>
            <if test="startDate != null and startDate != ''">
                AND DATE(uf.created_at) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(uf.created_at) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY uf.created_at DESC
    </select>

    <!-- 获取用户最近关注的人 -->
    <select id="selectRecentFollowingByUserId" resultMap="UserFollowWithUserInfoResultMap">
        SELECT <include refid="User_Info_Column_List" />
        FROM user_follow uf
        LEFT JOIN user u ON uf.followed_id = u.id
        WHERE uf.user_id = #{userId}
        ORDER BY uf.created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取用户最近的粉丝 -->
    <select id="selectRecentFollowersByFollowedId" resultMap="UserFollowWithUserInfoResultMap">
        SELECT uf.id, uf.user_id, uf.followed_id, uf.created_at, uf.updated_at,
               u.username as followed_username, u.display_name as followed_display_name, 
               u.email as followed_email, u.avatar_url as followed_avatar_url, 
               u.department as followed_department, u.title as followed_title, 
               u.bio as followed_bio, u.is_active as followed_is_active
        FROM user_follow uf
        LEFT JOIN user u ON uf.user_id = u.id
        WHERE uf.followed_id = #{followedId}
        ORDER BY uf.created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计用户关注的人数 -->
    <select id="countFollowingByUserId" parameterType="java.lang.Long" resultType="int">
        SELECT COUNT(*)
        FROM user_follow
        WHERE user_id = #{userId}
    </select>

    <!-- 统计用户的粉丝数 -->
    <select id="countFollowersByFollowedId" parameterType="java.lang.Long" resultType="int">
        SELECT COUNT(*)
        FROM user_follow
        WHERE followed_id = #{followedId}
    </select>

    <!-- 批量统计用户关注信息 -->
    <select id="countFollowStatsBatch" resultType="java.util.Map">
        SELECT 
            u.id as user_id,
            COALESCE(following.following_count, 0) as following_count,
            COALESCE(followers.followers_count, 0) as followers_count
        FROM (
            SELECT id FROM user WHERE id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        ) u
        LEFT JOIN (
            SELECT user_id, COUNT(*) as following_count
            FROM user_follow
            WHERE user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            GROUP BY user_id
        ) following ON u.id = following.user_id
        LEFT JOIN (
            SELECT followed_id, COUNT(*) as followers_count
            FROM user_follow
            WHERE followed_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
            GROUP BY followed_id
        ) followers ON u.id = followers.followed_id
    </select>

    <!-- 获取热门被关注用户列表 -->
    <select id="selectPopularUsers" resultType="java.util.Map">
        SELECT uf.followed_id, u.username, u.display_name, COUNT(*) as followers_count
        FROM user_follow uf
        LEFT JOIN user u ON uf.followed_id = u.id
        <where>
            u.is_active = 1
            <if test="days != null and days > 0">
                AND uf.created_at &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
            </if>
        </where>
        GROUP BY uf.followed_id, u.username, u.display_name
        ORDER BY followers_count DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 检查用户关注记录是否存在 -->
    <select id="existsByUserIdAndFollowedId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user_follow
        WHERE user_id = #{userId} AND followed_id = #{followedId}
    </select>

    <!-- 检查是否存在相互关注 -->
    <select id="existsMutualFollow" resultType="boolean">
        SELECT COUNT(*) = 2
        FROM user_follow
        WHERE (user_id = #{userId1} AND followed_id = #{userId2})
           OR (user_id = #{userId2} AND followed_id = #{userId1})
    </select>

    <!-- 批量插入用户关注记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_follow (user_id, followed_id, created_at, updated_at)
        VALUES
        <foreach collection="userFollows" item="item" separator=",">
            (#{item.userId}, #{item.followedId}, #{item.createdAt}, #{item.updatedAt})
        </foreach>
    </insert>

    <!-- 批量删除用户关注记录 -->
    <delete id="batchDeleteByIds">
        DELETE FROM user_follow
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量删除用户关注记录（根据用户ID和被关注人ID） -->
    <delete id="batchDeleteByUserIdAndFollowedIds">
        DELETE FROM user_follow
        WHERE user_id = #{userId}
        AND followed_id IN
        <foreach collection="followedIds" item="followedId" open="(" separator="," close=")">
            #{followedId}
        </foreach>
    </delete>

    <!-- 获取共同关注的用户 -->
    <select id="selectMutualFollows" resultMap="UserFollowWithUserInfoResultMap">
        SELECT uf1.id, uf1.user_id, uf1.followed_id, uf1.created_at, uf1.updated_at,
               u.username as followed_username, u.display_name as followed_display_name, 
               u.email as followed_email, u.avatar_url as followed_avatar_url, 
               u.department as followed_department, u.title as followed_title, 
               u.bio as followed_bio, u.is_active as followed_is_active
        FROM user_follow uf1
        INNER JOIN user_follow uf2 ON uf1.followed_id = uf2.followed_id
        LEFT JOIN user u ON uf1.followed_id = u.id
        WHERE uf1.user_id = #{userId1} AND uf2.user_id = #{userId2}
        ORDER BY uf1.created_at DESC
    </select>

    <!-- 获取用户关注趋势数据 -->
    <select id="selectUserFollowTrend" resultType="java.util.Map">
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM user_follow
        WHERE user_id = #{userId}
        AND created_at &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    </select>

    <!-- 获取用户关注偏好数据 -->
    <select id="selectUserFollowPreference" resultType="java.util.Map">
        SELECT u.department, COUNT(*) as count
        FROM user_follow uf
        LEFT JOIN user u ON uf.followed_id = u.id
        WHERE uf.user_id = #{userId}
        GROUP BY u.department
        ORDER BY count DESC
    </select>

    <!-- 根据部门推荐用户 -->
    <select id="selectRecommendedUsersByDepartment" resultType="java.util.Map">
        SELECT u.id, u.username, u.display_name, u.department, u.title
        FROM user u
        WHERE u.department = (
            SELECT department FROM user WHERE id = #{userId}
        )
        AND u.id != #{userId}
        AND u.is_active = 1
        AND u.id NOT IN (
            SELECT followed_id FROM user_follow WHERE user_id = #{userId}
        )
        ORDER BY u.created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 基于共同关注推荐用户 -->
    <select id="selectRecommendedUsersByMutualFollows" resultType="java.util.Map">
        SELECT u.id, u.username, u.display_name, u.department, COUNT(*) as mutual_count
        FROM user_follow uf1
        INNER JOIN user_follow uf2 ON uf1.followed_id = uf2.user_id
        LEFT JOIN user u ON uf2.followed_id = u.id
        WHERE uf1.user_id = #{userId}
        AND u.id != #{userId}
        AND u.is_active = 1
        AND u.id NOT IN (
            SELECT followed_id FROM user_follow WHERE user_id = #{userId}
        )
        GROUP BY u.id, u.username, u.display_name, u.department
        ORDER BY mutual_count DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取可能认识的人 -->
    <select id="selectPeopleYouMayKnow" resultType="java.util.Map">
        SELECT u.id, u.username, u.display_name, u.department, u.title
        FROM user u
        WHERE u.id != #{userId}
        AND u.is_active = 1
        AND u.id NOT IN (
            SELECT followed_id FROM user_follow WHERE user_id = #{userId}
        )
        AND (
            u.department = (SELECT department FROM user WHERE id = #{userId})
            OR u.id IN (
                SELECT DISTINCT uf2.followed_id
                FROM user_follow uf1
                INNER JOIN user_follow uf2 ON uf1.followed_id = uf2.user_id
                WHERE uf1.user_id = #{userId}
                AND uf2.followed_id != #{userId}
            )
        )
        ORDER BY RAND()
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据时间范围查询关注列表 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_follow
        <where>
            <if test="userId != null">user_id = #{userId}</if>
            <if test="startDate != null and startDate != ''">
                AND DATE(created_at) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(created_at) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 获取关注活跃度统计 -->
    <select id="selectFollowActivityStats" resultType="java.util.Map">
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as follow_count,
            COUNT(DISTINCT followed_id) as unique_followed_count
        FROM user_follow
        WHERE user_id = #{userId}
        AND created_at &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    </select>

    <!-- 清理无效的关注关系 -->
    <delete id="cleanupInvalidFollows">
        DELETE uf FROM user_follow uf
        LEFT JOIN user u ON uf.followed_id = u.id
        WHERE uf.user_id = #{userId}
        AND (u.id IS NULL OR u.is_active = 0)
    </delete>

</mapper>
