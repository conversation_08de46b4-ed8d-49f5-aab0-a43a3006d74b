<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.UserCourseEnrollmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.UserCourseEnrollment">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="course_id" property="courseId" />
        <result column="enrollment_status" property="enrollmentStatus" />
        <result column="enrolled_at" property="enrolledAt" />
        <result column="completed_at" property="completedAt" />
        <result column="progress_percentage" property="progressPercentage" />
        <result column="completed_stages" property="completedStages" />
        <result column="total_stages" property="totalStages" />
        <result column="study_hours" property="studyHours" />
        <result column="last_study_at" property="lastStudyAt" />
        <result column="enrollment_source" property="enrollmentSource" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, course_id, enrollment_status, enrolled_at, completed_at, 
        progress_percentage, completed_stages, total_stages, study_hours, 
        last_study_at, enrollment_source, created_at, updated_at, deleted_at
    </sql>

    <!-- 插入报名记录 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserCourseEnrollment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_course_enrollment (
            user_id, course_id, enrollment_status, enrolled_at, completed_at,
            progress_percentage, completed_stages, total_stages, study_hours,
            last_study_at, enrollment_source, created_at, updated_at
        ) VALUES (
            #{userId}, #{courseId}, #{enrollmentStatus}, #{enrolledAt}, #{completedAt},
            #{progressPercentage}, #{completedStages}, #{totalStages}, #{studyHours},
            #{lastStudyAt}, #{enrollmentSource}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 根据ID删除报名记录 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM user_course_enrollment WHERE id = #{id}
    </delete>

    <!-- 软删除报名记录 -->
    <update id="softDeleteById" parameterType="java.lang.Long">
        UPDATE user_course_enrollment 
        SET deleted_at = NOW(), updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新报名信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserCourseEnrollment">
        UPDATE user_course_enrollment
        <set>
            <if test="enrollmentStatus != null">enrollment_status = #{enrollmentStatus},</if>
            <if test="completedAt != null">completed_at = #{completedAt},</if>
            <if test="progressPercentage != null">progress_percentage = #{progressPercentage},</if>
            <if test="completedStages != null">completed_stages = #{completedStages},</if>
            <if test="totalStages != null">total_stages = #{totalStages},</if>
            <if test="studyHours != null">study_hours = #{studyHours},</if>
            <if test="lastStudyAt != null">last_study_at = #{lastStudyAt},</if>
            <if test="enrollmentSource != null">enrollment_source = #{enrollmentSource},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据ID查询报名记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_course_enrollment
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <!-- 根据条件查询报名列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserCourseEnrollment" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_course_enrollment
        <where>
            deleted_at IS NULL
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="courseId != null">AND course_id = #{courseId}</if>
            <if test="enrollmentStatus != null and enrollmentStatus != ''">AND enrollment_status = #{enrollmentStatus}</if>
            <if test="enrollmentSource != null and enrollmentSource != ''">AND enrollment_source = #{enrollmentSource}</if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID查询报名列表 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_course_enrollment
        WHERE user_id = #{userId} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据课程ID查询报名列表 -->
    <select id="selectByCourseId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_course_enrollment
        WHERE course_id = #{courseId} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID和课程ID查询报名记录 -->
    <select id="selectByUserIdAndCourseId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_course_enrollment
        WHERE user_id = #{userId} AND course_id = #{courseId} AND deleted_at IS NULL
        LIMIT 1
    </select>

    <!-- 根据用户ID和状态查询报名列表 -->
    <select id="selectByUserIdAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_course_enrollment
        WHERE user_id = #{userId} 
        <if test="enrollmentStatus != null and enrollmentStatus != ''">
            AND enrollment_status = #{enrollmentStatus}
        </if>
        AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据课程ID和状态查询报名列表 -->
    <select id="selectByCourseIdAndStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_course_enrollment
        WHERE course_id = #{courseId}
        <if test="enrollmentStatus != null and enrollmentStatus != ''">
            AND enrollment_status = #{enrollmentStatus}
        </if>
        AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 更新报名状态 -->
    <update id="updateStatus">
        UPDATE user_course_enrollment
        <set>
            enrollment_status = #{enrollmentStatus},
            updated_at = NOW()
            <if test="enrollmentStatus == 'COMPLETED'">
                , completed_at = NOW()
            </if>
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新学习进度 -->
    <update id="updateProgress">
        UPDATE user_course_enrollment
        <set>
            <if test="progressPercentage != null">progress_percentage = #{progressPercentage},</if>
            <if test="completedStages != null">completed_stages = #{completedStages},</if>
            <if test="totalStages != null">total_stages = #{totalStages},</if>
            <if test="studyHours != null">study_hours = #{studyHours},</if>
            last_study_at = NOW(),
            updated_at = NOW()
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新最后学习时间 -->
    <update id="updateLastStudyTime">
        UPDATE user_course_enrollment
        SET last_study_at = NOW(), updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 统计用户报名课程数量 -->
    <select id="countByUserId" resultType="int">
        SELECT COUNT(*)
        FROM user_course_enrollment
        WHERE user_id = #{userId}
        <if test="enrollmentStatus != null and enrollmentStatus != ''">
            AND enrollment_status = #{enrollmentStatus}
        </if>
        AND deleted_at IS NULL
    </select>

    <!-- 统计课程报名人数 -->
    <select id="countByCourseId" resultType="int">
        SELECT COUNT(*)
        FROM user_course_enrollment
        WHERE course_id = #{courseId}
        <if test="enrollmentStatus != null and enrollmentStatus != ''">
            AND enrollment_status = #{enrollmentStatus}
        </if>
        AND deleted_at IS NULL
    </select>

    <!-- 搜索报名记录（根据课程名称） -->
    <select id="searchEnrollments" resultMap="BaseResultMap">
        SELECT uce.id, uce.user_id, uce.course_id, uce.enrollment_status, uce.enrolled_at,
               uce.completed_at, uce.progress_percentage, uce.completed_stages, uce.total_stages,
               uce.study_hours, uce.last_study_at, uce.enrollment_source, uce.created_at,
               uce.updated_at, uce.deleted_at
        FROM user_course_enrollment uce
        LEFT JOIN learning_course lc ON uce.course_id = lc.id
        <where>
            uce.deleted_at IS NULL
            <if test="userId != null">AND uce.user_id = #{userId}</if>
            <if test="keyword != null and keyword != ''">
                AND lc.name LIKE CONCAT('%', #{keyword}, '%')
            </if>
            <if test="enrollmentStatus != null and enrollmentStatus != ''">
                AND uce.enrollment_status = #{enrollmentStatus}
            </if>
        </where>
        ORDER BY uce.created_at DESC
    </select>

    <!-- 批量更新学习时长 -->
    <update id="batchUpdateStudyHours">
        UPDATE user_course_enrollment
        SET study_hours = study_hours + #{additionalHours},
            updated_at = NOW()
        WHERE id IN
        <foreach collection="enrollmentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 获取用户学习统计信息 -->
    <select id="getUserLearningStats" resultMap="BaseResultMap">
        SELECT
            COUNT(*) as total_stages,
            SUM(CASE WHEN enrollment_status = 'ENROLLED' THEN 1 ELSE 0 END) as completed_stages,
            SUM(study_hours) as study_hours,
            AVG(progress_percentage) as progress_percentage
        FROM user_course_enrollment
        WHERE user_id = #{userId} AND deleted_at IS NULL
    </select>

    <!-- 获取课程报名统计信息 -->
    <select id="getCourseEnrollmentStats" resultMap="BaseResultMap">
        SELECT
            COUNT(*) as total_stages,
            SUM(CASE WHEN enrollment_status = 'COMPLETED' THEN 1 ELSE 0 END) as completed_stages,
            AVG(progress_percentage) as progress_percentage,
            AVG(study_hours) as study_hours
        FROM user_course_enrollment
        WHERE course_id = #{courseId} AND deleted_at IS NULL
    </select>

    <!-- 根据时间范围查询报名列表 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_course_enrollment
        <where>
            deleted_at IS NULL
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="startDate != null and startDate != ''">
                AND DATE(enrolled_at) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(enrolled_at) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY enrolled_at DESC
    </select>

</mapper>
