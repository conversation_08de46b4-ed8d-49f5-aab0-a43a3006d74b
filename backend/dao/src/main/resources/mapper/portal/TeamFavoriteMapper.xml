<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.TeamFavoriteMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.TeamFavorite">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="team_id" property="teamId" />
        <result column="created_at" property="createdAt" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 包含团队信息的查询映射结果 -->
    <resultMap id="TeamFavoriteWithTeamInfoResultMap" type="com.jdl.aic.core.service.dao.entity.portal.TeamFavorite">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="team_id" property="teamId" />
        <result column="created_at" property="createdAt" />
        <result column="deleted_at" property="deletedAt" />
        <!-- 团队信息字段 -->
        <result column="team_name" property="teamName" />
        <result column="team_description" property="teamDescription" />
        <result column="team_avatar_url" property="teamAvatarUrl" />
        <result column="team_privacy" property="teamPrivacy" />
        <result column="team_is_active" property="teamIsActive" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, team_id, created_at, deleted_at
    </sql>

    <!-- 包含团队信息的查询结果列 -->
    <sql id="Team_Info_Column_List">
        tf.id, tf.user_id, tf.team_id, tf.created_at, tf.deleted_at,
        t.name as team_name, t.description as team_description, 
        t.avatar_url as team_avatar_url, t.privacy as team_privacy, 
        t.is_active as team_is_active
    </sql>

    <!-- 插入团队收藏记录 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.TeamFavorite" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO team_favorite (
            user_id, team_id, created_at
        ) VALUES (
            #{userId}, #{teamId}, #{createdAt}
        )
    </insert>

    <!-- 根据ID删除团队收藏记录（物理删除） -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM team_favorite WHERE id = #{id}
    </delete>

    <!-- 软删除团队收藏记录 -->
    <update id="softDeleteById">
        UPDATE team_favorite
        SET deleted_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据用户ID和团队ID软删除团队收藏记录 -->
    <update id="softDeleteByUserIdAndTeamId">
        UPDATE team_favorite
        SET deleted_at = NOW()
        WHERE user_id = #{userId} AND team_id = #{teamId} AND deleted_at IS NULL
    </update>

    <!-- 根据ID查询团队收藏记录 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team_favorite
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID和团队ID查询团队收藏记录 -->
    <select id="selectByUserIdAndTeamId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team_favorite
        WHERE user_id = #{userId} AND team_id = #{teamId} AND deleted_at IS NULL
        LIMIT 1
    </select>

    <!-- 根据用户ID查询团队收藏列表 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team_favorite
        WHERE user_id = #{userId} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID查询团队收藏列表（包含团队信息） -->
    <select id="selectByUserIdWithTeamInfo" parameterType="java.lang.Long" resultMap="TeamFavoriteWithTeamInfoResultMap">
        SELECT <include refid="Team_Info_Column_List" />
        FROM team_favorite tf
        LEFT JOIN team t ON tf.team_id = t.id
        WHERE tf.user_id = #{userId} AND tf.deleted_at IS NULL
        ORDER BY tf.created_at DESC
    </select>

    <!-- 根据条件查询用户团队收藏列表 -->
    <select id="selectByCondition" resultMap="TeamFavoriteWithTeamInfoResultMap">
        SELECT <include refid="Team_Info_Column_List" />
        FROM team_favorite tf
        LEFT JOIN team t ON tf.team_id = t.id
        <where>
            tf.deleted_at IS NULL
            <if test="userId != null">AND tf.user_id = #{userId}</if>
            <if test="teamName != null and teamName != ''">
                AND t.name LIKE CONCAT('%', #{teamName}, '%')
            </if>
            <if test="teamPrivacy != null and teamPrivacy != ''">
                AND t.privacy = #{teamPrivacy}
            </if>
            <if test="teamIsActive != null">
                AND t.is_active = #{teamIsActive}
            </if>
            <if test="startDate != null and startDate != ''">
                AND DATE(tf.created_at) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(tf.created_at) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY tf.created_at DESC
    </select>

    <!-- 获取用户最近收藏的团队 -->
    <select id="selectRecentByUserId" resultMap="TeamFavoriteWithTeamInfoResultMap">
        SELECT <include refid="Team_Info_Column_List" />
        FROM team_favorite tf
        LEFT JOIN team t ON tf.team_id = t.id
        WHERE tf.user_id = #{userId} AND tf.deleted_at IS NULL
        ORDER BY tf.created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据团队ID查询收藏用户列表 -->
    <select id="selectByTeamId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team_favorite
        WHERE team_id = #{teamId} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据团队ID查询收藏用户列表（限制数量） -->
    <select id="selectByTeamIdWithLimit" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team_favorite
        WHERE team_id = #{teamId} AND deleted_at IS NULL
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计用户收藏的团队数量 -->
    <select id="countByUserId" parameterType="java.lang.Long" resultType="int">
        SELECT COUNT(*)
        FROM team_favorite
        WHERE user_id = #{userId} AND deleted_at IS NULL
    </select>

    <!-- 统计团队被收藏的次数 -->
    <select id="countByTeamId" parameterType="java.lang.Long" resultType="int">
        SELECT COUNT(*)
        FROM team_favorite
        WHERE team_id = #{teamId} AND deleted_at IS NULL
    </select>

    <!-- 批量统计团队被收藏次数 -->
    <select id="countByTeamIds" resultType="java.util.Map">
        SELECT team_id, COUNT(*) as favorite_count
        FROM team_favorite
        WHERE team_id IN
        <foreach collection="teamIds" item="teamId" open="(" separator="," close=")">
            #{teamId}
        </foreach>
        AND deleted_at IS NULL
        GROUP BY team_id
    </select>

    <!-- 获取热门收藏团队列表 -->
    <select id="selectPopularTeams" resultType="java.util.Map">
        SELECT tf.team_id, t.name as team_name, COUNT(*) as favorite_count
        FROM team_favorite tf
        LEFT JOIN team t ON tf.team_id = t.id
        <where>
            tf.deleted_at IS NULL
            <if test="days != null and days > 0">
                AND tf.created_at &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
            </if>
        </where>
        GROUP BY tf.team_id, t.name
        ORDER BY favorite_count DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 检查团队收藏记录是否存在（不包括已删除的） -->
    <select id="existsByUserIdAndTeamId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM team_favorite
        WHERE user_id = #{userId} AND team_id = #{teamId} AND deleted_at IS NULL
    </select>

    <!-- 检查团队收藏记录是否存在（包括已删除的） -->
    <select id="existsByUserIdAndTeamIdIncludeDeleted" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM team_favorite
        WHERE user_id = #{userId} AND team_id = #{teamId}
    </select>

    <!-- 批量插入团队收藏记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO team_favorite (user_id, team_id, created_at)
        VALUES
        <foreach collection="teamFavorites" item="item" separator=",">
            (#{item.userId}, #{item.teamId}, #{item.createdAt})
        </foreach>
    </insert>

    <!-- 批量删除团队收藏记录 -->
    <delete id="batchDeleteByIds">
        DELETE FROM team_favorite
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量软删除团队收藏记录 -->
    <update id="batchSoftDeleteByIds">
        UPDATE team_favorite
        SET deleted_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 批量软删除团队收藏记录（根据用户ID和团队ID） -->
    <update id="batchSoftDeleteByUserIdAndTeamIds">
        UPDATE team_favorite
        SET deleted_at = NOW()
        WHERE user_id = #{userId}
        AND team_id IN
        <foreach collection="teamIds" item="teamId" open="(" separator="," close=")">
            #{teamId}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 恢复已删除的团队收藏记录 -->
    <update id="restoreById">
        UPDATE team_favorite
        SET deleted_at = NULL
        WHERE id = #{id}
    </update>

    <!-- 清理指定天数前的已删除团队收藏记录 -->
    <delete id="cleanupDeletedRecords">
        DELETE FROM team_favorite
        WHERE deleted_at IS NOT NULL
        AND deleted_at &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

    <!-- 清理指定用户的已删除团队收藏记录 -->
    <delete id="cleanupUserDeletedRecords">
        DELETE FROM team_favorite
        WHERE user_id = #{userId}
        AND deleted_at IS NOT NULL
        AND deleted_at &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
    </delete>

    <!-- 获取团队收藏趋势数据 -->
    <select id="selectUserTeamFavoriteTrend" resultType="java.util.Map">
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM team_favorite
        WHERE user_id = #{userId}
        AND deleted_at IS NULL
        AND created_at &gt;= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    </select>

    <!-- 获取用户团队收藏偏好数据 -->
    <select id="selectUserTeamFavoritePreference" resultType="java.util.Map">
        SELECT t.privacy, COUNT(*) as count
        FROM team_favorite tf
        LEFT JOIN team t ON tf.team_id = t.id
        WHERE tf.user_id = #{userId} AND tf.deleted_at IS NULL
        GROUP BY t.privacy
        ORDER BY count DESC
    </select>

    <!-- 根据时间范围查询团队收藏列表 -->
    <select id="selectByDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team_favorite
        <where>
            deleted_at IS NULL
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="startDate != null and startDate != ''">
                AND DATE(created_at) &gt;= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND DATE(created_at) &lt;= #{endDate}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

</mapper>
