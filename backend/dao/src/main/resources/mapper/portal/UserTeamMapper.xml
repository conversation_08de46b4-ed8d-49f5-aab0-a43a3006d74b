<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.UserTeamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.UserTeam">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="team_id" property="teamId" />
        <result column="role" property="role" />
        <result column="joined_at" property="joinedAt" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 用户结果映射 -->
    <resultMap id="UserResultMap" type="com.jdl.aic.core.service.dao.entity.portal.User">
        <id column="id" property="id" />
        <result column="sso_id" property="ssoId" />
        <result column="username" property="username" />
        <result column="display_name" property="displayName" />
        <result column="email" property="email" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="department" property="department" />
        <result column="title" property="title" />
        <result column="bio" property="bio" />
        <result column="tags" property="tags" />
        <result column="is_active" property="isActive" />
        <result column="last_login_at" property="lastLoginAt" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, team_id, role, joined_at, created_at, updated_at
    </sql>

    <!-- 用户表列 -->
    <sql id="User_Column_List">
        u.id, u.sso_id, u.username, u.display_name, u.email, u.avatar_url, u.department, u.title, u.bio, u.tags, u.is_active, u.last_login_at, u.created_at, u.updated_at
    </sql>

    <!-- 插入用户团队关联 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserTeam" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_team (
            user_id, team_id, role, joined_at, created_at, updated_at
        ) VALUES (
            #{userId}, #{teamId}, #{role}, #{joinedAt}, #{createdAt}, #{updatedAt}
        )
    </insert>

    <!-- 根据ID删除用户团队关联 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM user_team WHERE id = #{id}
    </delete>

    <!-- 根据用户ID和团队ID删除关联 -->
    <delete id="deleteByUserIdAndTeamId">
        DELETE FROM user_team WHERE user_id = #{userId} AND team_id = #{teamId}
    </delete>

    <!-- 更新用户团队关联信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.UserTeam">
        UPDATE user_team
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="role != null">role = #{role},</if>
            <if test="joinedAt != null">joined_at = #{joinedAt},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询用户团队关联 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_team
        WHERE id = #{id}
    </select>

    <!-- 根据用户ID和团队ID查询关联 -->
    <select id="selectByUserIdAndTeamId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_team
        WHERE user_id = #{userId} AND team_id = #{teamId}
    </select>

    <!-- 根据用户ID查询所属团队ID列表 -->
    <select id="selectTeamIdsByUserId" resultType="java.lang.Long">
        SELECT team_id
        FROM user_team
        WHERE user_id = #{userId}
        ORDER BY joined_at DESC
    </select>

    <!-- 根据团队ID查询成员用户ID列表 -->
    <select id="selectUserIdsByTeamId" resultType="java.lang.Long">
        SELECT user_id
        FROM user_team
        WHERE team_id = #{teamId}
        ORDER BY joined_at ASC
    </select>

    <!-- 根据团队ID查询成员用户列表 -->
    <select id="selectUsersByTeamId" resultMap="UserResultMap">
        SELECT <include refid="User_Column_List" />
        FROM user_team ut
        INNER JOIN user u ON ut.user_id = u.id
        WHERE ut.team_id = #{teamId}
        ORDER BY ut.joined_at ASC
    </select>

    <!-- 根据用户ID查询团队关联列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_team
        WHERE user_id = #{userId}
        ORDER BY joined_at DESC
    </select>

    <!-- 根据团队ID查询用户关联列表 -->
    <select id="selectByTeamId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_team
        WHERE team_id = #{teamId}
        ORDER BY joined_at ASC
    </select>

    <!-- 获取用户在团队中的角色 -->
    <select id="selectUserTeamRole" resultType="java.lang.Integer">
        SELECT role
        FROM user_team
        WHERE user_id = #{userId} AND team_id = #{teamId}
    </select>

    <!-- 更新用户在团队中的角色 -->
    <update id="updateUserTeamRole">
        UPDATE user_team
        SET role = #{role}, updated_at = NOW()
        WHERE user_id = #{userId} AND team_id = #{teamId}
    </update>

    <!-- 根据角色查询团队成员 -->
    <select id="selectByTeamIdAndRole" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM user_team
        WHERE team_id = #{teamId} AND role = #{role}
        ORDER BY joined_at ASC
    </select>

    <!-- 批量添加用户到团队 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_team (
            user_id, team_id, role, joined_at, created_at, updated_at
        ) VALUES
        <foreach collection="userTeams" item="userTeam" separator=",">
            (#{userTeam.userId}, #{userTeam.teamId}, #{userTeam.role}, #{userTeam.joinedAt}, #{userTeam.createdAt}, #{userTeam.updatedAt})
        </foreach>
    </insert>

    <!-- 批量删除用户团队关联 -->
    <delete id="batchDeleteByIds">
        DELETE FROM user_team
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据用户ID列表和团队ID删除关联 -->
    <delete id="batchDeleteByUserIdsAndTeamId">
        DELETE FROM user_team
        WHERE team_id = #{teamId} AND user_id IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 根据用户ID删除所有团队关联 -->
    <delete id="deleteAllByUserId">
        DELETE FROM user_team WHERE user_id = #{userId}
    </delete>

    <!-- 根据团队ID删除所有用户关联 -->
    <delete id="deleteAllByTeamId">
        DELETE FROM user_team WHERE team_id = #{teamId}
    </delete>

    <!-- 统计团队成员数量 -->
    <select id="countMembersByTeamId" resultType="int">
        SELECT COUNT(*)
        FROM user_team
        WHERE team_id = #{teamId}
    </select>

    <!-- 统计用户所属团队数量 -->
    <select id="countTeamsByUserId" resultType="int">
        SELECT COUNT(*)
        FROM user_team
        WHERE user_id = #{userId}
    </select>

    <!-- 根据角色统计团队成员数量 -->
    <select id="countMembersByTeamIdAndRole" resultType="int">
        SELECT COUNT(*)
        FROM user_team
        WHERE team_id = #{teamId} AND role = #{role}
    </select>

    <!-- 检查用户是否在团队中 -->
    <select id="existsByUserIdAndTeamId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user_team
        WHERE user_id = #{userId} AND team_id = #{teamId}
    </select>

    <!-- 检查用户是否为团队管理员或创建者 -->
    <select id="isUserTeamAdminOrCreator" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM user_team
        WHERE user_id = #{userId} AND team_id = #{teamId} AND role IN (1, 2)
    </select>

</mapper>
