<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.TeamRecommendationMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.TeamRecommendation">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="team_id" property="team_id" jdbcType="BIGINT"/>
        <result column="user_id" property="user_id" jdbcType="BIGINT"/>
        <result column="content_id" property="content_id" jdbcType="BIGINT"/>
        <result column="content_type" property="content_type" jdbcType="VARCHAR"/>
        <result column="reason" property="reason" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
        <result column="created_at" property="created_at" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updated_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        id, team_id, user_id, content_id, content_type, reason, status, deleted, created_at, updated_at
    </sql>

    <!-- 根据ID查询团队推荐 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        WHERE id = #{id}
    </select>

    <!-- 根据团队ID查询推荐内容 -->
    <select id="selectByTeamId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        WHERE team_id = #{teamId} AND deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 根据用户ID查询推荐记录 -->
    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        WHERE user_id = #{userId} AND deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 根据内容ID和类型查询推荐记录 -->
    <select id="selectByContentIdAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        WHERE content_id = #{contentId} AND content_type = #{contentType} AND deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 根据团队ID和内容类型查询推荐 -->
    <select id="selectByTeamIdAndContentType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        WHERE team_id = #{teamId} AND content_type = #{contentType} AND deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 根据状态查询推荐记录 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        WHERE status = #{status} AND deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 查询所有推荐记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        ORDER BY created_at DESC
    </select>

    <!-- 查询活跃的推荐记录 -->
    <select id="selectActive" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        WHERE status = 'active' AND deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 插入团队推荐 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.TeamRecommendation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO team_recommendation (
            team_id, user_id, content_id, content_type, reason, status, deleted, created_at, updated_at
        ) VALUES (
            #{team_id}, #{user_id}, #{content_id}, #{content_type}, #{reason}, #{status}, #{deleted}, #{created_at}, #{updated_at}
        )
    </insert>

    <!-- 根据ID更新团队推荐 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.TeamRecommendation">
        UPDATE team_recommendation
        <set>
            <if test="team_id != null">team_id = #{team_id},</if>
            <if test="user_id != null">user_id = #{user_id},</if>
            <if test="content_id != null">content_id = #{content_id},</if>
            <if test="content_type != null">content_type = #{content_type},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="status != null">status = #{status},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
            <if test="updated_at != null">updated_at = #{updated_at},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除团队推荐（物理删除） -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM team_recommendation WHERE id = #{id}
    </delete>

    <!-- 根据ID软删除团队推荐 -->
    <update id="softDeleteById" parameterType="java.lang.Long">
        UPDATE team_recommendation
        SET deleted = 1, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新推荐状态 -->
    <update id="updateStatus">
        UPDATE team_recommendation
        SET status = #{status}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 统计推荐记录数量 -->
    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM team_recommendation
    </select>

    <!-- 统计活跃推荐记录数量 -->
    <select id="countActive" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM team_recommendation WHERE status = 'active' AND deleted = 0
    </select>

    <!-- 统计团队的推荐数量 -->
    <select id="countByTeamId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM team_recommendation WHERE team_id = #{teamId} AND deleted = 0
    </select>

    <!-- 统计用户的推荐数量 -->
    <select id="countByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM team_recommendation WHERE user_id = #{userId} AND deleted = 0
    </select>

    <!-- 统计内容的推荐数量 -->
    <select id="countByContentIdAndType" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM team_recommendation 
        WHERE content_id = #{contentId} AND content_type = #{contentType} AND deleted = 0
    </select>

    <!-- 批量插入团队推荐 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO team_recommendation (
            team_id, user_id, content_id, content_type, reason, status, deleted, created_at, updated_at
        ) VALUES
        <foreach collection="teamRecommendations" item="rec" separator=",">
            (#{rec.team_id}, #{rec.user_id}, #{rec.content_id}, #{rec.content_type}, #{rec.reason}, 
             #{rec.status}, #{rec.deleted}, #{rec.created_at}, #{rec.updated_at})
        </foreach>
    </insert>

    <!-- 批量更新推荐状态 -->
    <update id="batchUpdateStatus">
        UPDATE team_recommendation
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量软删除推荐记录 -->
    <update id="batchSoftDelete">
        UPDATE team_recommendation
        SET deleted = 1, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 检查推荐记录是否存在 -->
    <select id="existsByTeamIdAndContentIdAndType" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM team_recommendation 
        WHERE team_id = #{teamId} AND content_id = #{contentId} AND content_type = #{contentType} AND deleted = 0
    </select>

    <!-- 根据条件查询推荐记录 -->
    <select id="selectByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM team_recommendation
        <where>
            deleted = 0
            <if test="teamId != null">
                AND team_id = #{teamId}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="contentType != null and contentType != ''">
                AND content_type = #{contentType}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 获取团队推荐统计信息 -->
    <select id="getTeamRecommendationStatistics" parameterType="java.lang.Long" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_recommendations,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_recommendations,
            COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_recommendations,
            COUNT(DISTINCT content_type) as content_types_count,
            COUNT(DISTINCT user_id) as recommenders_count
        FROM team_recommendation 
        WHERE team_id = #{teamId} AND deleted = 0
    </select>

</mapper>
