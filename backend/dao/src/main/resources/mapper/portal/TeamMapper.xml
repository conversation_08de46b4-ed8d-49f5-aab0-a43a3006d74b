<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.TeamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.Team">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="parent_id" property="parentId" />
        <result column="is_active" property="isActive" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="tags" property="tagsJson" />
        <result column="avatar_url" property="avatarUrl" />
        <result column="privacy" property="privacy" />
        <result column="invite_setting" property="inviteSetting" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, parent_id, is_active, created_at, updated_at, created_by, updated_by, tags, avatar_url, privacy, invite_setting
    </sql>

    <!-- 插入团队 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.Team" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO team (
            name, description, parent_id, is_active, created_at, updated_at, created_by, updated_by, tags, avatar_url, privacy, invite_setting
        ) VALUES (
            #{name}, #{description}, #{parentId}, #{isActive}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}, #{tagsJson}, #{avatarUrl}, #{privacy}, #{inviteSetting}
        )
    </insert>

    <!-- 根据ID删除团队 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM team WHERE id = #{id}
    </delete>

    <!-- 根据ID更新团队 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.Team">
        UPDATE team
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="isActive != null">
                is_active = #{isActive},
            </if>
            <if test="tagsJson != null">
                tags = #{tagsJson},
            </if>
            <if test="avatarUrl != null">
                avatar_url = #{avatarUrl},
            </if>
            <if test="privacy != null">
                privacy = #{privacy},
            </if>
            <if test="inviteSetting != null">
                invite_setting = #{inviteSetting},
            </if>
            <if test="updatedAt != null">
                updated_at = #{updatedAt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询团队 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team
        WHERE id = #{id}
    </select>

    <!-- 根据条件查询团队列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.portal.Team" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team
        <where>
            <if test="name != null and name != ''">
                AND name = #{name}
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 根据父团队ID查询子团队列表 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team
        WHERE parent_id = #{parentId}
        ORDER BY created_at ASC
    </select>

    <!-- 查询根团队列表 -->
    <select id="selectRootTeams" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team
        WHERE parent_id IS NULL
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY created_at ASC
    </select>

    <!-- 根据名称和父团队查询团队 -->
    <select id="selectByNameAndParent" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team
        WHERE name = #{name}
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
    </select>

    <!-- 查询团队的子团队数量 -->
    <select id="countByParentId" parameterType="java.lang.Long" resultType="int">
        SELECT COUNT(*)
        FROM team
        WHERE parent_id = #{parentId}
    </select>

    <!-- 更新团队状态 -->
    <update id="updateStatus">
        UPDATE team
        SET is_active = #{isActive}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新团队父级 -->
    <update id="updateParent">
        UPDATE team
        SET parent_id = #{newParentId}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 搜索团队 -->
    <select id="searchTeams" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team
        <where>
            <if test="keyword != null and keyword != ''">
                AND (name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 获取所有活跃团队 -->
    <select id="selectActiveTeams" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team
        WHERE is_active = 1
        ORDER BY created_at ASC
    </select>

    <!-- 根据名称模糊查询团队 -->
    <select id="selectByNameLike" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM team
        WHERE name LIKE CONCAT('%', #{name}, '%')
        ORDER BY created_at ASC
    </select>

</mapper>
