<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.portal.BadgesMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.portal.Badges">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="icon_url" property="icon_url" jdbcType="VARCHAR"/>
        <result column="award_condition" property="award_condition" jdbcType="VARCHAR"/>
        <result column="created_at" property="created_at" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updated_at" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="created_by" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, description, icon_url, award_condition, created_at, updated_at, created_by, deleted
    </sql>

    <!-- 根据ID查询徽章 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM badges
        WHERE id = #{id}
    </select>

    <!-- 根据名称查询徽章 -->
    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM badges
        WHERE name = #{name}
    </select>

    <!-- 查询所有徽章 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM badges
        ORDER BY created_at DESC
    </select>

    <!-- 查询未删除的徽章 -->
    <select id="selectActive" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM badges
        WHERE deleted = 0
        ORDER BY created_at DESC
    </select>

    <!-- 插入徽章 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.portal.Badges" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO badges (
            name, description, icon_url, award_condition, created_at, updated_at, created_by, deleted
        ) VALUES (
            #{name}, #{description}, #{icon_url}, #{award_condition}, #{created_at}, #{updated_at}, #{created_by}, #{deleted}
        )
    </insert>

    <!-- 根据ID更新徽章 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.portal.Badges">
        UPDATE badges
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="icon_url != null">icon_url = #{icon_url},</if>
            <if test="award_condition != null">award_condition = #{award_condition},</if>
            <if test="updated_at != null">updated_at = #{updated_at},</if>
            <if test="created_by != null">created_by = #{created_by},</if>
            <if test="deleted != null">deleted = #{deleted},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除徽章（物理删除） -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM badges WHERE id = #{id}
    </delete>

    <!-- 根据ID软删除徽章 -->
    <update id="softDeleteById" parameterType="java.lang.Long">
        UPDATE badges
        SET deleted = 1, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 统计徽章数量 -->
    <select id="count" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM badges
    </select>

    <!-- 统计活跃徽章数量 -->
    <select id="countActive" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM badges WHERE deleted = 0
    </select>

    <!-- 批量插入徽章 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO badges (
            name, description, icon_url, award_condition, created_at, updated_at, created_by, deleted
        ) VALUES
        <foreach collection="badges" item="badge" separator=",">
            (#{badge.name}, #{badge.description}, #{badge.icon_url}, #{badge.award_condition}, 
             #{badge.created_at}, #{badge.updated_at}, #{badge.created_by}, #{badge.deleted})
        </foreach>
    </insert>

    <!-- 批量软删除徽章 -->
    <update id="batchSoftDelete">
        UPDATE badges
        SET deleted = 1, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 搜索徽章 -->
    <select id="searchBadges" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM badges
        WHERE deleted = 0
        AND (name LIKE CONCAT('%', #{keyword}, '%') 
             OR description LIKE CONCAT('%', #{keyword}, '%'))
        ORDER BY created_at DESC
    </select>

    <!-- 根据创建人查询徽章 -->
    <select id="selectByCreatedBy" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM badges
        WHERE created_by = #{createdBy}
        ORDER BY created_at DESC
    </select>

</mapper>
