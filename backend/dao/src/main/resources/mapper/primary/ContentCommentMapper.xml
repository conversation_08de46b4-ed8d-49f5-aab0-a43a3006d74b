<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ContentCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ContentComment">
        <id column="id" property="id" />
        <result column="content_id" property="contentId" />
        <result column="user_id" property="userId" />
        <result column="parent_id" property="parentId" />
        <result column="content" property="content" />
        <result column="like_count" property="likeCount" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content_id, user_id, parent_id, content, like_count, created_at, updated_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentComment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content_comment (content_id, user_id, parent_id, content, like_count, created_at, updated_at)
        VALUES (#{contentId}, #{userId}, #{parentId}, #{content}, #{likeCount}, #{createdAt}, #{updatedAt})
    </insert>

    <delete id="deleteById">
        DELETE FROM content_comment WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentComment">
        UPDATE content_comment
        <set>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_comment
        WHERE id = #{id}
    </select>

    <select id="selectByContentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_comment
        WHERE content_id = #{contentId}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_comment
        WHERE user_id = #{userId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_comment
    </select>

    <select id="countByContentId" resultType="int">
        SELECT COUNT(*)
        FROM content_comment
        WHERE content_id = #{contentId}
    </select>

    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_comment
        WHERE parent_id = #{parentId}
    </select>

    <update id="updateLikeCount">
        UPDATE content_comment
        SET like_count = #{likeCount}
        WHERE id = #{id}
    </update>

</mapper>
