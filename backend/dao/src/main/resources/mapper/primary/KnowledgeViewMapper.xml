<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.KnowledgeViewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.KnowledgeView">
        <id column="id" property="id" />
        <result column="knowledge_id" property="knowledgeId" />
        <result column="user_id" property="userId" />
        <result column="view_time" property="viewTime" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, knowledge_id, user_id, view_time, created_at, updated_at, created_by, updated_by
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeView" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO knowledge_view
        (knowledge_id, user_id, view_time, created_at, updated_at, created_by, updated_by)
        VALUES
        (#{knowledgeId}, #{userId}, #{viewTime}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy})
    </insert>

    <delete id="deleteById">
        DELETE FROM knowledge_view WHERE id = #{id}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_view
        WHERE id = #{id}
    </select>

    <select id="selectByKnowledgeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_view
        WHERE knowledge_id = #{knowledgeId}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_view
        WHERE user_id = #{userId}
    </select>

    <select id="countByKnowledgeId" resultType="int">
        SELECT COUNT(*)
        FROM knowledge_view
        WHERE knowledge_id = #{knowledgeId}
    </select>

    <select id="selectByTimeRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_view
        WHERE view_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="existsByKnowledgeIdAndUserId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM knowledge_view
        WHERE knowledge_id = #{knowledgeId} AND user_id = #{userId}
    </select>

    <select id="selectRecentViewsByKnowledgeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_view
        WHERE knowledge_id = #{knowledgeId}
        ORDER BY view_time DESC
    </select>

</mapper>
