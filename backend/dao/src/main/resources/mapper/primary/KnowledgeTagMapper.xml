<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.KnowledgeTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTag">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, created_at, updated_at, created_by, updated_by
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTag" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO knowledge_tag
        (name, description, created_at, updated_at, created_by, updated_by)
        VALUES
        (#{name}, #{description}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy})
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTag">
        UPDATE knowledge_tag
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM knowledge_tag WHERE id = #{id}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_tag
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_tag
    </select>

    <select id="selectByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_tag
        WHERE name = #{name}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO knowledge_tag
        (name, description, created_at, updated_at, created_by, updated_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.name}, #{item.description}, #{item.createdAt}, #{item.updatedAt}, #{item.createdBy}, #{item.updatedBy})
        </foreach>
    </insert>

    <select id="selectLikeName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_tag
        WHERE name LIKE CONCAT('%', #{name}, '%')
    </select>

</mapper>
