<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.DictionaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.Dictionary">
        <id column="id" property="id" />
        <result column="key" property="key" />
        <result column="value" property="value" />
        <result column="type" property="type" />
        <result column="description" property="description" />
        <result column="sort_order" property="sortOrder" />
        <result column="is_active" property="isActive" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `key`, `value`, `type`, description, sort_order, 
        is_active, created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <!-- 插入字典项 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.Dictionary" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO dictionary (
            `key`, `value`, `type`, description, sort_order, 
            is_active, created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{key}, #{value}, #{type}, #{description}, #{sortOrder},
            #{isActive}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除字典项 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM dictionary WHERE id = #{id}
    </delete>

    <!-- 更新字典项信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.Dictionary">
        UPDATE dictionary
        <set>
            <if test="key != null">`key` = #{key},</if>
            <if test="value != null">`value` = #{value},</if>
            <if test="type != null">`type` = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据ID查询字典项 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dictionary
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <!-- 根据条件查询字典项列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.primary.Dictionary" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dictionary
        WHERE deleted_at IS NULL
        <if test="key != null and key != ''">
            AND `key` LIKE CONCAT('%', #{key}, '%')
        </if>
        <if test="value != null and value != ''">
            AND `value` LIKE CONCAT('%', #{value}, '%')
        </if>
        <if test="type != null and type != ''">
            AND `type` = #{type}
        </if>
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 根据类型查询字典项列表 -->
    <select id="selectByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dictionary
        WHERE `type` = #{type} AND deleted_at IS NULL
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 根据键和类型查询字典项 -->
    <select id="selectByKeyAndType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dictionary
        WHERE `key` = #{key} AND `type` = #{type} AND deleted_at IS NULL
    </select>

    <!-- 根据键查询字典项列表 -->
    <select id="selectByKey" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dictionary
        WHERE `key` = #{key} AND deleted_at IS NULL
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 更新字典项排序权重 -->
    <update id="updateSortOrder">
        UPDATE dictionary
        SET sort_order = #{sortOrder}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新字典项状态 -->
    <update id="updateStatus">
        UPDATE dictionary
        SET is_active = #{isActive}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 搜索字典项（根据键、值、描述） -->
    <select id="searchDictionaries" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM dictionary
        WHERE deleted_at IS NULL
        <if test="keyword != null and keyword != ''">
            AND (`key` LIKE CONCAT('%', #{keyword}, '%') 
                 OR `value` LIKE CONCAT('%', #{keyword}, '%') 
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="type != null and type != ''">
            AND `type` = #{type}
        </if>
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 获取所有字典类型 -->
    <select id="selectAllTypes" resultType="java.lang.String">
        SELECT DISTINCT `type`
        FROM dictionary
        WHERE deleted_at IS NULL
        ORDER BY `type` ASC
    </select>

    <!-- 根据类型统计字典项数量 -->
    <select id="countByType" resultType="int">
        SELECT COUNT(*)
        FROM dictionary
        WHERE `type` = #{type} AND deleted_at IS NULL
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
    </select>

    <!-- 批量插入字典项 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO dictionary (
            `key`, `value`, `type`, description, sort_order, 
            is_active, created_at, updated_at, created_by, updated_by
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.key}, #{item.value}, #{item.type}, #{item.description}, #{item.sortOrder},
             #{item.isActive}, #{item.createdAt}, #{item.updatedAt}, #{item.createdBy}, #{item.updatedBy})
        </foreach>
    </insert>

    <!-- 批量更新字典项状态 -->
    <update id="batchUpdateStatus">
        UPDATE dictionary
        SET is_active = #{isActive}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

</mapper>
