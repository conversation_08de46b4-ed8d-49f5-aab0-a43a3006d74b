<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ContentCategoryRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation">
        <id column="id" property="id" />
        <result column="content_id" property="contentId" />
        <result column="category_id" property="categoryId" />
        <result column="content_type" property="contentType" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content_id, category_id, content_type, created_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content_category_relation (content_id, category_id, content_type, created_at, updated_at)
        VALUES (#{contentId}, #{categoryId}, #{contentType}, #{createdAt}
    </insert>

    <delete id="deleteById">
        DELETE FROM content_category_relation WHERE id = #{id}
    </delete>

    <delete id="deleteByContentIdAndCategoryId">
        DELETE FROM content_category_relation WHERE content_id = #{contentId} AND category_id = #{categoryId}
    </delete>

    <update id="update" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation">
        UPDATE content_category_relation
        <set>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_category_relation
        WHERE id = #{id}
    </select>

    <select id="selectByContentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_category_relation
        WHERE content_id = #{contentId}
    </select>

    <select id="selectByCategoryId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_category_relation
        WHERE category_id = #{categoryId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_category_relation
    </select>

    <insert id="batchInsert">
        INSERT INTO content_category_relation (content_id, category_id, content_type, created_at)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.contentId}, #{item.categoryId}, #{item.contentType}, #{item.createdAt})
        </foreach>
    </insert>

    <delete id="batchDelete">
        DELETE FROM content_category_relation WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByContentIdAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_category_relation
        WHERE content_id = #{contentId} AND content_type = #{contentType}
    </select>

    <delete id="deleteByContentIdAndType">
        DELETE FROM content_category_relation
        WHERE content_id = #{contentId} AND content_type = #{contentType}
    </delete>

</mapper>
