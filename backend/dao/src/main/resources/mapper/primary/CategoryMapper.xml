<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.CategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.Category">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="parent_id" property="parentId" />
        <result column="content_category" property="contentCategory" />
        <result column="sub_type_id" property="subTypeId" />
        <result column="icon_url" property="iconUrl" />
        <result column="sort_order" property="sortOrder" />
        <result column="is_active" property="isActive" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, parent_id, content_category, sub_type_id, icon_url, sort_order,
        is_active, created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <!-- 插入分类 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.Category" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO category (
            name, description, parent_id, content_category, sub_type_id, icon_url, sort_order,
            is_active, created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{name}, #{description}, #{parentId}, #{contentCategory}, #{subTypeId}, #{iconUrl}, #{sortOrder},
            #{isActive}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除分类 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM category WHERE id = #{id}
    </delete>

    <!-- 更新分类信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.Category">
        UPDATE category
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="contentCategory != null">content_category = #{contentCategory},</if>
            <if test="subTypeId != null">sub_type_id = #{subTypeId},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据ID查询分类 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM category
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <!-- 根据条件查询分类列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.primary.Category" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM category
        WHERE deleted_at IS NULL
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="contentCategory != null and contentCategory != ''">
            AND content_category = #{contentCategory}
        </if>
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        <if test="subTypeId != null">
            AND sub_type_id = #{subTypeId}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 根据父分类ID查询子分类列表 -->
    <select id="selectByParentId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM category
        WHERE parent_id = #{parentId} AND deleted_at IS NULL
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 根据内容类别查询分类列表 -->
    <select id="selectByContentCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM category
        WHERE content_category = #{contentCategory} AND deleted_at IS NULL
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 查询根分类列表 -->
    <select id="selectRootCategories" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM category
        WHERE parent_id IS NULL AND deleted_at IS NULL
        <if test="contentCategory != null and contentCategory != ''">
            AND content_category = #{contentCategory}
        </if>
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        <if test="subTypeId != null">
            AND sub_type_id = #{subTypeId}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 根据名称和内容类别查询分类 -->
    <select id="selectByNameAndCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM category
        WHERE name = #{name} 
        AND content_category = #{contentCategory}
        AND deleted_at IS NULL
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
    </select>

    <!-- 查询分类的子分类数量 -->
    <select id="countByParentId" parameterType="java.lang.Long" resultType="int">
        SELECT COUNT(*)
        FROM category
        WHERE parent_id = #{parentId} AND deleted_at IS NULL
    </select>

    <!-- 更新分类排序权重 -->
    <update id="updateSortOrder">
        UPDATE category
        SET sort_order = #{sortOrder}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新分类状态 -->
    <update id="updateStatus">
        UPDATE category
        SET is_active = #{isActive}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新分类父级 -->
    <update id="updateParent">
        UPDATE category
        SET parent_id = #{newParentId}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 搜索分类 -->
    <select id="searchCategories" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM category
        WHERE deleted_at IS NULL
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="contentCategory != null and contentCategory != ''">
            AND content_category = #{contentCategory}
        </if>
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        <if test="subTypeId != null">
            AND sub_type_id = #{subTypeId}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

</mapper>
