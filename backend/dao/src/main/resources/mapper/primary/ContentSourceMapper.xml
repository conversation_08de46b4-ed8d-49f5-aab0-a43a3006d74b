<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ContentSourceMapper">
  <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ContentSource">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="owner_id" jdbcType="BIGINT" property="ownerId" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, name, description, url, type, owner_id, owner_name, status, created_at, updated_at,
    created_by, updated_by, deleted_at
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from content_source
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from content_source
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentSource">
    insert into content_source (name, description, url, 
      type, owner_id, owner_name, 
      status, created_at, updated_at, 
      created_by, updated_by, deleted_at)
    values (#{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{ownerId,jdbcType=BIGINT}, #{ownerName,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{deletedAt,jdbcType=TIMESTAMP})
  </insert>
  
  <update id="updateByPrimaryKey" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentSource">
    update content_source
    set name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      owner_id = #{ownerId,jdbcType=BIGINT},
      owner_name = #{ownerName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      deleted_at = #{deletedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectAll" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from content_source
  </select>
</mapper>
