<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.LearningPathResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.LearningPathResource">
        <id column="id" property="id" />
        <result column="learning_path_id" property="learningPathId" />
        <result column="resource_id" property="resourceId" />
        <result column="sequence_order" property="sequenceOrder" />
        <result column="stage_name" property="stageName" />
        <result column="estimated_hours" property="estimatedHours" />
        <result column="is_optional" property="isOptional" />
        <result column="notes" property="notes" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, learning_path_id, resource_id, sequence_order, stage_name, estimated_hours, is_optional, notes, created_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.LearningPathResource" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO learning_path_resource
        (learning_path_id, resource_id, sequence_order, stage_name, estimated_hours, is_optional, notes, created_at)
        VALUES
        (#{learningPathId}, #{resourceId}, #{sequenceOrder}, #{stageName}, #{estimatedHours}, #{isOptional}, #{notes}, #{createdAt})
    </insert>

    <delete id="deleteById">
        DELETE FROM learning_path_resource WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.jdl.aic.core.service.dao.entity.primary.LearningPathResource">
        UPDATE learning_path_resource
        <set>
            <if test="learningPathId != null">learning_path_id = #{learningPathId},</if>
            <if test="resourceId != null">resource_id = #{resourceId},</if>
            <if test="sequenceOrder != null">sequence_order = #{sequenceOrder},</if>
            <if test="stageName != null">stage_name = #{stageName},</if>
            <if test="estimatedHours != null">estimated_hours = #{estimatedHours},</if>
            <if test="isOptional != null">is_optional = #{isOptional},</if>
            <if test="notes != null">notes = #{notes},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_path_resource
        WHERE id = #{id}
    </select>

    <select id="selectByLearningPathId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_path_resource
        WHERE learning_path_id = #{learningPathId}
        ORDER BY sequence_order
    </select>

    <select id="selectByResourceId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_path_resource
        WHERE resource_id = #{resourceId}
    </select>

    <select id="countByLearningPathId" resultType="int">
        SELECT COUNT(*)
        FROM learning_path_resource
        WHERE learning_path_id = #{learningPathId}
    </select>

    <insert id="batchInsert">
        INSERT INTO learning_path_resource
        (learning_path_id, resource_id, sequence_order, stage_name, estimated_hours, is_optional, notes, created_at)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.learningPathId}, #{item.resourceId}, #{item.sequenceOrder}, #{item.stageName}, #{item.estimatedHours}, #{item.isOptional}, #{item.notes}, #{item.createdAt})
        </foreach>
    </insert>

    <delete id="deleteByLearningPathId">
        DELETE FROM learning_path_resource WHERE learning_path_id = #{learningPathId}
    </delete>

    <!-- 根据学习路径ID和资源ID删除关联记录 -->
    <delete id="deleteByPathAndResource">
        DELETE FROM learning_path_resource
        WHERE learning_path_id = #{learningPathId} AND resource_id = #{resourceId}
    </delete>

    <!-- 根据学习路径ID查询关联的资源（按顺序排列） -->
    <select id="selectByLearningPathIdOrderBySequence" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_path_resource
        WHERE learning_path_id = #{learningPathId}
        ORDER BY sequence_order ASC
    </select>

    <!-- 更新资源在学习路径中的顺序 -->
    <update id="updateSequenceOrder">
        UPDATE learning_path_resource
        SET sequence_order = #{newOrder}
        WHERE learning_path_id = #{learningPathId} AND resource_id = #{resourceId}
    </update>

    <!-- 批量更新资源顺序 -->
    <update id="batchUpdateSequenceOrder">
        <foreach collection="updates" item="item" separator=";">
            UPDATE learning_path_resource
            SET sequence_order = #{item.sequenceOrder}
            WHERE learning_path_id = #{item.learningPathId} AND resource_id = #{item.resourceId}
        </foreach>
    </update>

    <!-- 检查资源是否已在学习路径中 -->
    <select id="countByPathAndResource" resultType="int">
        SELECT COUNT(*)
        FROM learning_path_resource
        WHERE learning_path_id = #{learningPathId} AND resource_id = #{resourceId}
    </select>

    <!-- 获取学习路径中的最大顺序号 -->
    <select id="getMaxSequenceOrder" resultType="java.lang.Integer">
        SELECT MAX(sequence_order)
        FROM learning_path_resource
        WHERE learning_path_id = #{learningPathId}
    </select>

</mapper>
