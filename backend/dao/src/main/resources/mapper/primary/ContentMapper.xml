<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ContentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.Content">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="author_id" property="authorId" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="view_count" property="viewCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="favorite_count" property="favoriteCount" />
        <result column="share_count" property="shareCount" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="published_at" property="publishedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, content, author_id, type, status, view_count, like_count, comment_count, favorite_count, share_count, created_at, updated_at, published_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.Content" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content (title, content, author_id, type, status, view_count, like_count, comment_count, favorite_count, share_count, created_at, updated_at, published_at)
        VALUES (#{title}, #{content}, #{authorId}, #{type}, #{status}, #{viewCount}, #{likeCount}, #{commentCount}, #{favoriteCount}, #{shareCount}, #{createdAt}, #{updatedAt}, #{publishedAt})
    </insert>

    <delete id="deleteById">
        DELETE FROM content WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.jdl.aic.core.service.dao.entity.primary.Content">
        UPDATE content
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="authorId != null">author_id = #{authorId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="commentCount != null">comment_count = #{commentCount},</if>
            <if test="favoriteCount != null">favorite_count = #{favoriteCount},</if>
            <if test="shareCount != null">share_count = #{shareCount},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="publishedAt != null">published_at = #{publishedAt},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content
    </select>

    <select id="selectByAuthorId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content
        WHERE author_id = #{authorId}
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content
        WHERE status = #{status}
    </select>

    <select id="selectByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content
        WHERE type = #{type}
    </select>

    <update id="updateStatus">
        UPDATE content
        SET status = #{status}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <update id="incrementViewCount">
        UPDATE content
        SET view_count = view_count + #{increment}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <update id="incrementLikeCount">
        UPDATE content
        SET like_count = like_count + #{increment}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <update id="incrementCommentCount">
        UPDATE content
        SET comment_count = comment_count + #{increment}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <update id="incrementFavoriteCount">
        UPDATE content
        SET favorite_count = favorite_count + #{increment}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <update id="incrementShareCount">
        UPDATE content
        SET share_count = share_count + #{increment}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <select id="selectHotContents" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content
        ORDER BY (view_count + like_count + comment_count + favorite_count + share_count) DESC
    </select>

</mapper>
