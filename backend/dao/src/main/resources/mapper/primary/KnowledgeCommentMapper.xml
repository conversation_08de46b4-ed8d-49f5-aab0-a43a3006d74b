<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.KnowledgeCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.KnowledgeComment">
        <id column="id" property="id" />
        <result column="knowledge_id" property="knowledgeId" />
        <result column="content" property="content" />
        <result column="parent_id" property="parentId" />
        <result column="author_id" property="authorId" />
        <result column="author_name" property="authorName" />
        <result column="like_count" property="likeCount" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, knowledge_id, content, parent_id, author_id, author_name, like_count, 
        created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeComment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO knowledge_comment
        (knowledge_id, content, parent_id, author_id, author_name, like_count, 
         created_at, updated_at, created_by, updated_by)
        VALUES
        (#{knowledgeId}, #{content}, #{parentId}, #{authorId}, #{authorName}, #{likeCount}, 
         #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy})
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeComment">
        UPDATE knowledge_comment
        <set>
            <if test="content != null">content = #{content},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <update id="deleteById">
        UPDATE knowledge_comment SET deleted_at = NOW() WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_comment
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <select id="selectByKnowledgeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_comment
        WHERE knowledge_id = #{knowledgeId} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_comment
        WHERE parent_id = #{parentId} AND deleted_at IS NULL
        ORDER BY created_at ASC
    </select>

    <update id="updateLikeCount">
        UPDATE knowledge_comment
        SET like_count = like_count + #{increment},
            updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <select id="selectByAuthorId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_comment
        WHERE author_id = #{authorId} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <select id="countByKnowledgeId" resultType="int">
        SELECT COUNT(*)
        FROM knowledge_comment
        WHERE knowledge_id = #{knowledgeId} AND deleted_at IS NULL
    </select>

</mapper>
