<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ContentShareMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ContentShare">
        <id column="id" property="id" />
        <result column="content_id" property="contentId" />
        <result column="user_id" property="userId" />
        <result column="platform" property="platform" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content_id, user_id, platform, created_at, updated_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentShare" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content_share (content_id, user_id, platform, created_at, updated_at)
        VALUES (#{contentId}, #{userId}, #{platform}, #{createdAt}, #{updatedAt})
    </insert>

    <delete id="deleteById">
        DELETE FROM content_share WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentShare">
        UPDATE content_share
        <set>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_share
        WHERE id = #{id}
    </select>

    <select id="selectByContentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_share
        WHERE content_id = #{contentId}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_share
        WHERE user_id = #{userId}
    </select>

    <select id="selectByContentIdAndUserId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_share
        WHERE content_id = #{contentId} AND user_id = #{userId}
    </select>

    <delete id="deleteByContentId">
        DELETE FROM content_share WHERE content_id = #{contentId}
    </delete>

    <delete id="deleteByUserId">
        DELETE FROM content_share WHERE user_id = #{userId}
    </delete>

    <select id="countByContentId" resultType="int">
        SELECT COUNT(*)
        FROM content_share
        WHERE content_id = #{contentId}
    </select>

    <select id="countByContentIdAndPlatform" resultType="int">
        SELECT COUNT(*)
        FROM content_share
        WHERE content_id = #{contentId} AND platform = #{platform}
    </select>

</mapper>
