<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.CrawlerContentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.CrawlerContent">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="link" property="link" />
        <result column="language" property="language" />
        <result column="author" property="author" />
        <result column="description" property="description" />
        <result column="pub_date" property="pubDate" />
        <result column="ai_summary" property="aiSummary" />
        <result column="is_featured" property="isFeatured" />
        <result column="content_md5" property="contentMd5" />
        <result column="content" property="content" />
        <result column="content_type" property="contentType" />
        <result column="status" property="status" />
        <result column="quality_score" property="qualityScore" />
        <result column="word_count" property="wordCount" />
        <result column="tags" property="tags" />
        <result column="metadata" property="metadata" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
        <result column="type" property="type" />
        <result column="attachments" property="attachments" />
        <result column="media" property="media" />
        <result column="task_id" property="taskId" />
        <result column="task_name" property="taskName" />
        <result column="task_desc" property="taskDesc" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, link, language, author, description, pub_date, ai_summary, is_featured,
        content_md5, content, content_type, status, quality_score, word_count,
        tags, metadata, created_at, updated_at, created_by, updated_by, deleted_at,
        type, attachments, media, task_id, task_name, task_desc
    </sql>

    <!-- 插入爬虫内容 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.CrawlerContent" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO crawler_content (
            title, link, language, author, description, pub_date, ai_summary, is_featured,
            content_md5, content, content_type, status, quality_score, word_count,
            tags, metadata, created_at, updated_at, created_by, updated_by,
            type, attachments, media, task_id, task_name, task_desc
        ) VALUES (
            #{title}, #{link}, #{language}, #{author}, #{description}, #{pubDate}, #{aiSummary}, #{isFeatured},
            #{contentMd5}, #{content}, #{contentType}, #{status}, #{qualityScore}, #{wordCount},
            #{tags}, #{metadata}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy},
            #{type}, #{attachments}, #{media}, #{taskId}, #{taskName}, #{taskDesc}
        )
    </insert>

    <!-- 根据ID删除爬虫内容 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM crawler_content WHERE id = #{id}
    </delete>

    <!-- 更新爬虫内容信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.CrawlerContent">
        UPDATE crawler_content
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="link != null">link = #{link},</if>
            <if test="language != null">language = #{language},</if>
            <if test="author != null">author = #{author},</if>
            <if test="description != null">description = #{description},</if>
            <if test="pubDate != null">pub_date = #{pubDate},</if>
            <if test="aiSummary != null">ai_summary = #{aiSummary},</if>
            <if test="isFeatured != null">is_featured = #{isFeatured},</if>
            <if test="contentMd5 != null">content_md5 = #{contentMd5},</if>
            <if test="content != null">content = #{content},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="qualityScore != null">quality_score = #{qualityScore},</if>
            <if test="wordCount != null">word_count = #{wordCount},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="metadata != null">metadata = #{metadata},</if>
            <if test="type != null">type = #{type},</if>
            <if test="attachments != null">attachments = #{attachments},</if>
            <if test="media != null">media = #{media},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="taskDesc != null">task_desc = #{taskDesc},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据ID查询爬虫内容 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <!-- 根据条件查询爬虫内容列表（增强版，支持更多字段） -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.primary.CrawlerContent" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE deleted_at IS NULL
        <!-- 基础字段条件 -->
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="link != null and link != ''">
            AND link = #{link}
        </if>
        <if test="language != null and language != ''">
            AND language = #{language}
        </if>
        <if test="author != null and author != ''">
            AND author LIKE CONCAT('%', #{author}, '%')
        </if>
        <if test="description != null and description != ''">
            AND description LIKE CONCAT('%', #{description}, '%')
        </if>
        <if test="contentType != null and contentType != ''">
            AND content_type = #{contentType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="isFeatured != null">
            AND is_featured = #{isFeatured}
        </if>
        <if test="contentMd5 != null and contentMd5 != ''">
            AND content_md5 = #{contentMd5}
        </if>
        <!-- 新增字段条件 -->
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="taskId != null and taskId != ''">
            AND task_id = #{taskId}
        </if>
        <if test="taskName != null and taskName != ''">
            AND task_name LIKE CONCAT('%', #{taskName}, '%')
        </if>
        <!-- 数值范围条件 -->
        <if test="qualityScore != null">
            AND quality_score = #{qualityScore}
        </if>
        <if test="wordCount != null">
            AND word_count = #{wordCount}
        </if>
        <!-- 时间条件 -->
        <if test="pubDate != null">
            AND DATE(pub_date) = DATE(#{pubDate})
        </if>
        <if test="createdAt != null">
            AND DATE(created_at) = DATE(#{createdAt})
        </if>
        <if test="updatedAt != null">
            AND DATE(updated_at) = DATE(#{updatedAt})
        </if>
        <!-- 创建者和更新者 -->
        <if test="createdBy != null and createdBy != ''">
            AND created_by = #{createdBy}
        </if>
        <if test="updatedBy != null and updatedBy != ''">
            AND updated_by = #{updatedBy}
        </if>
        <!-- 标签搜索 -->
        <if test="tags != null and tags != ''">
            AND tags LIKE CONCAT('%', #{tags}, '%')
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据MD5查询爬虫内容 -->
    <select id="selectByMd5" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE content_md5 = #{contentMd5} AND deleted_at IS NULL
    </select>

    <!-- 根据链接查询爬虫内容 -->
    <select id="selectByLink" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE link = #{link} AND deleted_at IS NULL
    </select>

    <!-- 根据状态查询爬虫内容列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE status = #{status} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据内容类型查询爬虫内容列表 -->
    <select id="selectByContentType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE content_type = #{contentType} AND deleted_at IS NULL
        <if test="isFeatured != null">
            AND is_featured = #{isFeatured}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据语言查询爬虫内容列表 -->
    <select id="selectByLanguage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE language = #{language} AND deleted_at IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据质量评分范围查询爬虫内容列表 -->
    <select id="selectByQualityScore" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE deleted_at IS NULL
        <if test="minScore != null">
            AND quality_score >= #{minScore}
        </if>
        <if test="maxScore != null">
            AND quality_score &lt; #{maxScore}
        </if>
        ORDER BY quality_score DESC, created_at DESC
    </select>

    <!-- 根据发布时间范围查询爬虫内容列表 -->
    <select id="selectByPubDateRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE deleted_at IS NULL
        <if test="startDate != null">
            AND pub_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND pub_date &lt; #{endDate}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY pub_date DESC
    </select>

    <!-- 更新内容状态 -->
    <update id="updateStatus">
        UPDATE crawler_content
        SET status = #{status}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新AI总结 -->
    <update id="updateAiSummary">
        UPDATE crawler_content
        SET ai_summary = #{aiSummary}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新质量评分 -->
    <update id="updateQualityScore">
        UPDATE crawler_content
        SET quality_score = #{qualityScore}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新精品状态 -->
    <update id="updateFeaturedStatus">
        UPDATE crawler_content
        SET is_featured = #{isFeatured}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 搜索爬虫内容（根据标题、描述、内容） -->
    <select id="searchContents" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE deleted_at IS NULL
        <if test="keyword != null and keyword != ''">
            AND (title LIKE CONCAT('%', #{keyword}, '%') 
                 OR description LIKE CONCAT('%', #{keyword}, '%') 
                 OR content LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="contentType != null and contentType != ''">
            AND content_type = #{contentType}
        </if>
        <if test="language != null and language != ''">
            AND language = #{language}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at ASC
    </select>

    <!-- 统计各状态的内容数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM crawler_content
        WHERE deleted_at IS NULL
        GROUP BY status
    </select>

    <!-- 统计各内容类型的数量 -->
    <select id="countByContentType" resultType="java.util.Map">
        SELECT content_type, COUNT(*) as count
        FROM crawler_content
        WHERE deleted_at IS NULL
        GROUP BY content_type
    </select>

    <!-- 批量插入爬虫内容 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO crawler_content (
            title, link, language, description, pub_date, ai_summary, is_featured,
            content_md5, content, content_type, status, quality_score, word_count,
            tags, metadata, created_at, updated_at, created_by, updated_by,
            type, attachments, media, task_id, task_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.title}, #{item.link}, #{item.language}, #{item.description}, #{item.pubDate},
             #{item.aiSummary}, #{item.isFeatured}, #{item.contentMd5}, #{item.content},
             #{item.contentType}, #{item.status}, #{item.qualityScore}, #{item.wordCount},
             #{item.tags}, #{item.metadata}, #{item.createdAt}, #{item.updatedAt},
             #{item.createdBy}, #{item.updatedBy}, #{item.type}, #{item.attachments},
             #{item.media}, #{item.taskId}, #{item.taskName})
        </foreach>
    </insert>

    <!-- 批量更新内容状态 -->
    <update id="batchUpdateStatus">
        UPDATE crawler_content
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- ==================== 高级条件查询 ==================== -->

    <!-- 高级条件查询爬虫内容总数 -->
    <select id="countByAdvancedCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM crawler_content
        <where>
            <include refid="AdvancedConditions" />
        </where>
    </select>

    <!-- 高级查询条件片段 -->
    <sql id="AdvancedConditions">
        <!-- 默认排除已删除记录 -->
        <if test="params.includeDeleted == null or params.includeDeleted == false">
            AND deleted_at IS NULL
        </if>

        <!-- 基础字段条件 -->
        <if test="params.id != null">
            AND id = #{params.id}
        </if>
        <if test="params.ids != null and params.ids.size() > 0">
            AND id IN
            <foreach collection="params.ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="params.title != null and params.title != ''">
            AND title LIKE CONCAT('%', #{params.title}, '%')
        </if>
        <if test="params.link != null and params.link != ''">
            AND link = #{params.link}
        </if>
        <if test="params.linkLike != null and params.linkLike != ''">
            AND link LIKE CONCAT('%', #{params.linkLike}, '%')
        </if>
        <if test="params.language != null and params.language != ''">
            AND language = #{params.language}
        </if>
        <if test="params.languages != null and params.languages.size() > 0">
            AND language IN
            <foreach collection="params.languages" item="lang" open="(" separator="," close=")">
                #{lang}
            </foreach>
        </if>
        <if test="params.author != null and params.author != ''">
            AND author LIKE CONCAT('%', #{params.author}, '%')
        </if>
        <if test="params.description != null and params.description != ''">
            AND description LIKE CONCAT('%', #{params.description}, '%')
        </if>
        <if test="params.contentType != null and params.contentType != ''">
            AND content_type = #{params.contentType}
        </if>
        <if test="params.contentTypes != null and params.contentTypes.size() > 0">
            AND content_type IN
            <foreach collection="params.contentTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="params.status != null">
            AND status = #{params.status}
        </if>
        <if test="params.statuses != null and params.statuses.size() > 0">
            AND status IN
            <foreach collection="params.statuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="params.isFeatured != null">
            AND is_featured = #{params.isFeatured}
        </if>
        <if test="params.contentMd5 != null and params.contentMd5 != ''">
            AND content_md5 = #{params.contentMd5}
        </if>

        <!-- 新增字段条件 -->
        <if test="params.type != null and params.type != ''">
            AND type = #{params.type}
        </if>
        <if test="params.types != null and params.types.size() > 0">
            AND type IN
            <foreach collection="params.types" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="params.taskId != null and params.taskId != ''">
            AND task_id = #{params.taskId}
        </if>
        <if test="params.taskIds != null and params.taskIds.size() > 0">
            AND task_id IN
            <foreach collection="params.taskIds" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        <if test="params.taskName != null and params.taskName != ''">
            AND task_name LIKE CONCAT('%', #{params.taskName}, '%')
        </if>

        <!-- 数值范围条件 -->
        <if test="params.qualityScoreMin != null">
            AND quality_score >= #{params.qualityScoreMin}
        </if>
        <if test="params.qualityScoreMax != null">
            AND quality_score &lt;= #{params.qualityScoreMax}
        </if>
        <if test="params.wordCountMin != null">
            AND word_count >= #{params.wordCountMin}
        </if>
        <if test="params.wordCountMax != null">
            AND word_count &lt;= #{params.wordCountMax}
        </if>

        <!-- 时间范围条件 -->
        <if test="params.pubDateStart != null">
            AND pub_date >= #{params.pubDateStart}
        </if>
        <if test="params.pubDateEnd != null">
            AND pub_date &lt;= #{params.pubDateEnd}
        </if>
        <if test="params.createdAtStart != null">
            AND created_at >= #{params.createdAtStart}
        </if>
        <if test="params.createdAtEnd != null">
            AND created_at &lt;= #{params.createdAtEnd}
        </if>
        <if test="params.updatedAtStart != null">
            AND updated_at >= #{params.updatedAtStart}
        </if>
        <if test="params.updatedAtEnd != null">
            AND updated_at &lt;= #{params.updatedAtEnd}
        </if>

        <!-- 关键词搜索 -->
        <if test="params.keyword != null and params.keyword != ''">
            AND (title LIKE CONCAT('%', #{params.keyword}, '%')
                 OR description LIKE CONCAT('%', #{params.keyword}, '%')
                 OR content LIKE CONCAT('%', #{params.keyword}, '%'))
        </if>

        <!-- 标签搜索 -->
        <if test="params.tag != null and params.tag != ''">
            AND tags LIKE CONCAT('%', #{params.tag}, '%')
        </if>
        <if test="params.tags != null and params.tags.size() > 0">
            AND (
            <foreach collection="params.tags" item="tag" separator=" OR ">
                tags LIKE CONCAT('%', #{tag}, '%')
            </foreach>
            )
        </if>

        <!-- 创建者和更新者 -->
        <if test="params.createdBy != null and params.createdBy != ''">
            AND created_by = #{params.createdBy}
        </if>
        <if test="params.updatedBy != null and params.updatedBy != ''">
            AND updated_by = #{params.updatedBy}
        </if>

        <!-- JSON 字段查询（新增字段） -->
        <if test="params.hasAttachments != null and params.hasAttachments == true">
            AND attachments IS NOT NULL AND attachments != '[]' AND attachments != ''
        </if>
        <if test="params.hasMedia != null and params.hasMedia == true">
            AND media IS NOT NULL AND media != '[]' AND media != ''
        </if>
    </sql>

    <!-- 根据新增字段查询爬虫内容 -->
    <select id="selectByNewFields" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE deleted_at IS NULL
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="taskId != null and taskId != ''">
            AND task_id = #{taskId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据多个条件组合查询（支持 IN 查询） -->
    <select id="selectByMultipleConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM crawler_content
        WHERE deleted_at IS NULL
        <if test="ids != null and ids.size() > 0">
            AND id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="contentTypes != null and contentTypes.size() > 0">
            AND content_type IN
            <foreach collection="contentTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="languages != null and languages.size() > 0">
            AND language IN
            <foreach collection="languages" item="lang" open="(" separator="," close=")">
                #{lang}
            </foreach>
        </if>
        <if test="statuses != null and statuses.size() > 0">
            AND status IN
            <foreach collection="statuses" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="types != null and types.size() > 0">
            AND type IN
            <foreach collection="types" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="taskIds != null and taskIds.size() > 0">
            AND task_id IN
            <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </if>
        ORDER BY created_at DESC
    </select>
    
    <!-- 根据type查询去重后的taskName和taskId -->
    <select id="selectDistinctTaskInfoByType" resultMap="BaseResultMap">
        SELECT DISTINCT task_id, task_name, task_desc
        FROM crawler_content
        WHERE deleted_at IS NULL
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        ORDER BY task_name
    </select>
    
    <!-- 统计各type类型的内容数量 -->
    <select id="countByType" resultType="java.util.Map">
        SELECT type, COUNT(*) as count
        FROM crawler_content
        WHERE deleted_at IS NULL
        GROUP BY type
        ORDER BY count DESC
    </select>

</mapper>
