<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.SolutionKnowledgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.SolutionKnowledge">
        <id column="id" property="id" />
        <result column="solution_id" property="solutionId" />
        <result column="knowledge_id" property="knowledgeId" />
        <result column="sort_order" property="sortOrder" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, solution_id, knowledge_id, sort_order, created_at, updated_at, created_by, updated_by
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.SolutionKnowledge" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO solution_knowledge
        (solution_id, knowledge_id, sort_order, created_at, updated_at, created_by, updated_by)
        VALUES
        (#{solutionId}, #{knowledgeId}, #{sortOrder}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy})
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.SolutionKnowledge">
        UPDATE solution_knowledge
        <set>
            <if test="solutionId != null">solution_id = #{solutionId},</if>
            <if test="knowledgeId != null">knowledge_id = #{knowledgeId},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM solution_knowledge WHERE id = #{id}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution_knowledge
        WHERE id = #{id}
    </select>

    <select id="selectBySolutionId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution_knowledge
        WHERE solution_id = #{solutionId}
        ORDER BY sort_order ASC
    </select>

    <select id="selectByKnowledgeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution_knowledge
        WHERE knowledge_id = #{knowledgeId}
        ORDER BY sort_order ASC
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO solution_knowledge
        (solution_id, knowledge_id, sort_order, created_at, updated_at, created_by, updated_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.solutionId}, #{item.knowledgeId}, #{item.sortOrder}, #{item.createdAt}, #{item.updatedAt}, #{item.createdBy}, #{item.updatedBy})
        </foreach>
    </insert>

    <delete id="deleteBySolutionId">
        DELETE FROM solution_knowledge WHERE solution_id = #{solutionId}
    </delete>

    <update id="updateSortOrder">
        UPDATE solution_knowledge
        SET sort_order = #{sortOrder},
            updated_at = NOW()
        WHERE id = #{id}
    </update>

</mapper>
