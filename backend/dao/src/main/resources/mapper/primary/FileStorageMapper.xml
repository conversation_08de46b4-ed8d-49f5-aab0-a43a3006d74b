<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.FileStorageMapper">
  <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.FileStorage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_type" jdbcType="VARCHAR" property="fileType" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="storage_type" jdbcType="TINYINT" property="storageType" />
    <result column="bucket_name" jdbcType="VARCHAR" property="bucketName" />
    <result column="object_key" jdbcType="VARCHAR" property="objectKey" />
    <result column="owner_id" jdbcType="BIGINT" property="ownerId" />
    <result column="owner_name" jdbcType="VARCHAR" property="ownerName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, file_name, file_type, file_path, file_size, md5, storage_type, bucket_name, 
    object_key, owner_id, owner_name, status, created_at, updated_at, created_by, 
    updated_by, deleted_at
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from file_storage
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from file_storage
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.FileStorage">
    insert into file_storage (file_name, file_type, file_path, 
      file_size, md5, storage_type, 
      bucket_name, object_key, owner_id, 
      owner_name, status, created_at, 
      updated_at, created_by, updated_by, 
      deleted_at)
    values (#{fileName,jdbcType=VARCHAR}, #{fileType,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR}, 
      #{fileSize,jdbcType=BIGINT}, #{md5,jdbcType=VARCHAR}, #{storageType,jdbcType=TINYINT}, 
      #{bucketName,jdbcType=VARCHAR}, #{objectKey,jdbcType=VARCHAR}, #{ownerId,jdbcType=BIGINT}, 
      #{ownerName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, 
      #{deletedAt,jdbcType=TIMESTAMP})
  </insert>
  
  <update id="updateByPrimaryKey" parameterType="com.jdl.aic.core.service.dao.entity.primary.FileStorage">
    update file_storage
    set file_name = #{fileName,jdbcType=VARCHAR},
      file_type = #{fileType,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      file_size = #{fileSize,jdbcType=BIGINT},
      md5 = #{md5,jdbcType=VARCHAR},
      storage_type = #{storageType,jdbcType=TINYINT},
      bucket_name = #{bucketName,jdbcType=VARCHAR},
      object_key = #{objectKey,jdbcType=VARCHAR},
      owner_id = #{ownerId,jdbcType=BIGINT},
      owner_name = #{ownerName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      deleted_at = #{deletedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <select id="selectAll" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from file_storage
  </select>
</mapper>
