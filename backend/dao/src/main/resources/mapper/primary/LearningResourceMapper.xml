<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.LearningResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.LearningResource">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="content" property="content" />
        <result column="learning_goals" property="learningGoals" />
        <result column="prerequisites" property="prerequisites" />
        <result column="resource_type" property="resourceType" />
        <result column="source_type" property="sourceType" />
        <result column="source_url" property="sourceUrl" />
        <result column="source_platform" property="sourcePlatform" />
        <result column="difficulty_level" property="difficultyLevel" />
        <result column="estimated_duration_hours" property="estimatedDurationHours" />
        <result column="language" property="language" />
        <result column="is_free" property="isFree" />
        <result column="price_info" property="priceInfo" />
        <result column="rating" property="rating" />
        <result column="rating_count" property="ratingCount" />
        <result column="view_count" property="viewCount" />
        <result column="bookmark_count" property="bookmarkCount" />
        <result column="completion_count" property="completionCount" />
        <result column="completion_rate" property="completionRate" />
        <result column="tags" property="tags" />
        <result column="content_type" property="contentType" />
        <result column="content_config" property="contentConfig" />
        <result column="embed_config" property="embedConfig" />
        <result column="access_config" property="accessConfig" />
        <result column="media_metadata" property="mediaMetadata" />
        <result column="metadata" property="metadata" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, description, content, learning_goals, prerequisites, resource_type, source_type, source_url, source_platform,
        difficulty_level, estimated_duration_hours, language, is_free, price_info, rating,
        rating_count, view_count, bookmark_count, completion_count, completion_rate, tags, content_type,
        content_config, embed_config, access_config, media_metadata, metadata, status,
        created_by, updated_by, created_at, updated_at, deleted_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.LearningResource" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO learning_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="content != null">content,</if>
            <if test="learningGoals != null">learning_goals,</if>
            <if test="prerequisites != null">prerequisites,</if>
            <if test="resourceType != null">resource_type,</if>
            <if test="sourceType != null">source_type,</if>
            <if test="sourceUrl != null">source_url,</if>
            <if test="sourcePlatform != null">source_platform,</if>
            <if test="difficultyLevel != null">difficulty_level,</if>
            <if test="estimatedDurationHours != null">estimated_duration_hours,</if>
            <if test="language != null">language,</if>
            <if test="isFree != null">is_free,</if>
            <if test="priceInfo != null">price_info,</if>
            <if test="rating != null">rating,</if>
            <if test="ratingCount != null">rating_count,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="bookmarkCount != null">bookmark_count,</if>
            <if test="completionCount != null">completion_count,</if>
            <if test="completionRate != null">completion_rate,</if>
            <if test="tags != null">tags,</if>
            <if test="contentType != null">content_type,</if>
            <if test="contentConfig != null">content_config,</if>
            <if test="embedConfig != null">embed_config,</if>
            <if test="accessConfig != null">access_config,</if>
            <if test="mediaMetadata != null">media_metadata,</if>
            <if test="metadata != null">metadata,</if>
            <if test="status != null">status,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
            created_at,
            updated_at
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="content != null">#{content},</if>
            <if test="learningGoals != null">#{learningGoals},</if>
            <if test="prerequisites != null">#{prerequisites},</if>
            <if test="resourceType != null">#{resourceType},</if>
            <if test="sourceType != null">#{sourceType},</if>
            <if test="sourceUrl != null">#{sourceUrl},</if>
            <if test="sourcePlatform != null">#{sourcePlatform},</if>
            <if test="difficultyLevel != null">#{difficultyLevel},</if>
            <if test="estimatedDurationHours != null">#{estimatedDurationHours},</if>
            <if test="language != null">#{language},</if>
            <if test="isFree != null">#{isFree},</if>
            <if test="priceInfo != null">#{priceInfo},</if>
            <if test="rating != null">#{rating},</if>
            <if test="ratingCount != null">#{ratingCount},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="bookmarkCount != null">#{bookmarkCount},</if>
            <if test="completionCount != null">#{completionCount},</if>
            <if test="completionRate != null">#{completionRate},</if>
            <if test="tags != null">#{tags},</if>
            <if test="contentType != null">#{contentType},</if>
            <if test="contentConfig != null">#{contentConfig},</if>
            <if test="embedConfig != null">#{embedConfig},</if>
            <if test="accessConfig != null">#{accessConfig},</if>
            <if test="mediaMetadata != null">#{mediaMetadata},</if>
            <if test="metadata != null">#{metadata},</if>
            <if test="status != null">#{status},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            NOW(),
            NOW()
        </trim>
    </insert>

    <delete id="deleteById">
        UPDATE learning_resource SET deleted_at = NOW() WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.jdl.aic.core.service.dao.entity.primary.LearningResource">
        UPDATE learning_resource
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="content != null">content = #{content},</if>
            <if test="learningGoals != null">learning_goals = #{learningGoals},</if>
            <if test="prerequisites != null">prerequisites = #{prerequisites},</if>
            <if test="resourceType != null">resource_type = #{resourceType},</if>
            <if test="sourceType != null">source_type = #{sourceType},</if>
            <if test="sourceUrl != null">source_url = #{sourceUrl},</if>
            <if test="sourcePlatform != null">source_platform = #{sourcePlatform},</if>
            <if test="difficultyLevel != null">difficulty_level = #{difficultyLevel},</if>
            <if test="estimatedDurationHours != null">estimated_duration_hours = #{estimatedDurationHours},</if>
            <if test="language != null">language = #{language},</if>
            <if test="isFree != null">is_free = #{isFree},</if>
            <if test="priceInfo != null">price_info = #{priceInfo},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="ratingCount != null">rating_count = #{ratingCount},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="bookmarkCount != null">bookmark_count = #{bookmarkCount},</if>
            <if test="completionCount != null">completion_count = #{completionCount},</if>
            <if test="completionRate != null">completion_rate = #{completionRate},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="contentType != null">content_type = #{contentType},</if>
            <if test="contentConfig != null">content_config = #{contentConfig},</if>
            <if test="embedConfig != null">embed_config = #{embedConfig},</if>
            <if test="accessConfig != null">access_config = #{accessConfig},</if>
            <if test="mediaMetadata != null">media_metadata = #{mediaMetadata},</if>
            <if test="metadata != null">metadata = #{metadata},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <select id="selectByResourceType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        WHERE resource_type = #{resourceType} AND deleted_at IS NULL
    </select>

    <select id="selectByDifficultyLevel" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        WHERE difficulty_level = #{difficultyLevel} AND deleted_at IS NULL
    </select>

    <select id="selectTopRated" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        WHERE deleted_at IS NULL
        ORDER BY rating DESC
    </select>

    <select id="selectMostPopular" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        WHERE deleted_at IS NULL
        ORDER BY bookmark_count DESC
    </select>

    <select id="searchByKeyword" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        WHERE (title LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%'))
        AND deleted_at IS NULL
    </select>

    <select id="countByResourceType" resultType="int">
        SELECT COUNT(*)
        FROM learning_resource
        WHERE resource_type = #{resourceType} AND deleted_at IS NULL
    </select>

    <!-- 根据条件分页查询学习资源 -->
    <select id="selectByConditions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        <where>
            deleted_at IS NULL
            <if test="resourceType != null and resourceType != ''">
                AND resource_type = #{resourceType}
            </if>
            <if test="category != null and category != ''">
                AND JSON_EXTRACT(metadata, '$.category') = #{category}
            </if>
            <if test="difficulty != null and difficulty != ''">
                AND difficulty_level = #{difficulty}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="search != null and search != ''">
                AND (title LIKE CONCAT('%', #{search}, '%')
                     OR description LIKE CONCAT('%', #{search}, '%'))
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 根据ID列表查询学习资源 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </select>

    <!-- 批量更新学习资源状态 -->
    <update id="batchUpdateStatus">
        UPDATE learning_resource
        SET status = #{status}, updated_by = #{updatedBy}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 增加浏览次数 -->
    <update id="incrementViewCount">
        UPDATE learning_resource
        SET view_count = COALESCE(view_count, 0) + 1, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 增加下载次数 -->
    <update id="incrementDownloadCount">
        UPDATE learning_resource
        SET completion_count = COALESCE(completion_count, 0) + 1, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 获取推荐学习资源 -->
    <select id="selectRecommendedResources" resultMap="BaseResultMap">
        SELECT
         *
        FROM learning_resource lr
        LEFT JOIN resource_recommendation rr ON lr.id = rr.resource_id
        <where>
            <if test="userId != null and userId != ''">
                AND rr.user_id = #{userId}
            </if>
            <if test="category != null and category != ''">
                AND JSON_EXTRACT(lr.metadata, '$.category') = #{category}
            </if>
            <if test="limit != null">
                AND lr.deleted_at IS NULL
            </if>
        </where>
        ORDER BY rr.recommendation_score DESC, lr.created_at DESC
    </select>

    <!-- 获取热门学习资源 -->
    <select id="selectPopularResources" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        <where>
            deleted_at IS NULL
            AND status = 'ACTIVE'
            <if test="category != null and category != ''">
                AND JSON_EXTRACT(metadata, '$.category') = #{category}
            </if>
            <if test="days != null">
                AND created_at >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
            </if>
        </where>
        ORDER BY view_count DESC, bookmark_count DESC
    </select>

    <!-- 高级搜索学习资源 -->
    <select id="advancedSearch" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        <where>
            deleted_at IS NULL
            <if test="keyword != null and keyword != ''">
                AND (title LIKE CONCAT('%', #{keyword}, '%')
                     OR description LIKE CONCAT('%', #{keyword}, '%')
                     OR JSON_EXTRACT(metadata, '$.tags') LIKE CONCAT('%', #{keyword}, '%'))
            </if>
        </where>
        ORDER BY rating DESC, view_count DESC
    </select>

    <!-- 查询所有学习资源 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        WHERE deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据条件查询学习资源 -->
    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.jdl.aic.core.service.dao.entity.primary.LearningResource">
        SELECT
        <include refid="Base_Column_List" />
        FROM learning_resource
        <where>
            deleted_at IS NULL
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="resourceType != null and resourceType != ''">
                AND resource_type = #{resourceType}
            </if>
            <if test="difficultyLevel != null and difficultyLevel != ''">
                AND difficulty_level = #{difficultyLevel}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="createdBy != null and createdBy != ''">
                AND created_by = #{createdBy}
            </if>
        </where>
        ORDER BY created_at DESC
    </select>

</mapper>
