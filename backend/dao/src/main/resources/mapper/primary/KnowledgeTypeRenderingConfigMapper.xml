<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.KnowledgeTypeRenderingConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTypeRenderingConfig">
        <id column="id" property="id" />
        <result column="knowledge_type_id" property="knowledgeTypeId" />
        <result column="render_config_json" property="renderConfigJson" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, knowledge_type_id, render_config_json, created_at, updated_at, created_by, updated_by
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTypeRenderingConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO knowledge_type_rendering_config
        (knowledge_type_id, render_config_json, created_at, updated_at, created_by, updated_by)
        VALUES
        (#{knowledgeTypeId}, #{renderConfigJson}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy})
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTypeRenderingConfig">
        UPDATE knowledge_type_rendering_config
        SET knowledge_type_id = #{knowledgeTypeId},
            render_config_json = #{renderConfigJson},
            updated_at = #{updatedAt},
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM knowledge_type_rendering_config WHERE id = #{id}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_type_rendering_config
        WHERE id = #{id}
    </select>

    <select id="selectByKnowledgeTypeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_type_rendering_config
        WHERE knowledge_type_id = #{knowledgeTypeId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_type_rendering_config
    </select>

</mapper>
