<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.LearningCourseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.LearningCourse">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="category" property="category" />
        <result column="difficulty_level" property="difficultyLevel" />
        <result column="total_hours" property="totalHours" />
        <result column="resource_count" property="resourceCount" />
        <result column="enrolled_count" property="enrolledCount" />
        <result column="completion_count" property="completionCount" />
        <result column="completion_rate" property="completionRate" />
        <result column="prerequisites" property="prerequisites" />
        <result column="learning_goals" property="learningGoals" />
        <result column="creator_id" property="creatorId" />
        <result column="creator_name" property="creatorName" />
        <result column="status" property="status" />
        <result column="is_official" property="isOfficial" />
        <result column="cover_image_url" property="coverImageUrl" />
        <result column="tags" property="tags" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, category, difficulty_level, total_hours, resource_count,
        enrolled_count, completion_count, completion_rate, prerequisites, learning_goals,
        creator_id, creator_name, status, is_official, cover_image_url, tags,
        created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <!-- 插入学习课程 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.LearningCourse" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO learning_course (
            name, description, category, difficulty_level, total_hours, resource_count,
            enrolled_count, completion_count, completion_rate, prerequisites, learning_goals,
            creator_id, creator_name, status, is_official, cover_image_url, tags,
            created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{name}, #{description}, #{category}, #{difficultyLevel}, #{totalHours}, #{resourceCount},
            #{enrolledCount}, #{completionCount}, #{completionRate}, #{prerequisites}, #{learningGoals},
            #{creatorId}, #{creatorName}, #{status}, #{isOfficial}, #{coverImageUrl}, #{tags},
            #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除学习课程（软删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE learning_course
        SET deleted_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新学习课程信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.LearningCourse">
        UPDATE learning_course
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="category != null">category = #{category},</if>
            <if test="difficultyLevel != null">difficulty_level = #{difficultyLevel},</if>
            <if test="totalHours != null">total_hours = #{totalHours},</if>
            <if test="resourceCount != null">resource_count = #{resourceCount},</if>
            <if test="enrolledCount != null">enrolled_count = #{enrolledCount},</if>
            <if test="completionCount != null">completion_count = #{completionCount},</if>
            <if test="completionRate != null">completion_rate = #{completionRate},</if>
            <if test="prerequisites != null">prerequisites = #{prerequisites},</if>
            <if test="learningGoals != null">learning_goals = #{learningGoals},</if>
            <if test="creatorName != null">creator_name = #{creatorName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isOfficial != null">is_official = #{isOfficial},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据ID查询学习课程 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <!-- 根据条件查询学习课程列表 -->
    <select id="selectByConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE deleted_at IS NULL
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        <if test="difficultyLevel != null and difficultyLevel != ''">
            AND difficulty_level = #{difficultyLevel}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="isOfficial != null">
            AND is_official = #{isOfficial}
        </if>
        <if test="creatorId != null and creatorId != ''">
            AND creator_id = #{creatorId}
        </if>
        <if test="search != null and search != ''">
            AND (name LIKE CONCAT('%', #{search}, '%')
                 OR description LIKE CONCAT('%', #{search}, '%')
                 OR tags LIKE CONCAT('%', #{search}, '%'))
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据分类查询学习课程列表 -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE category = #{category} AND deleted_at IS NULL AND status = 'PUBLISHED'
        ORDER BY enrolled_count DESC, created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据难度级别查询学习课程列表 -->
    <select id="selectByDifficulty" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE difficulty_level = #{difficultyLevel} AND deleted_at IS NULL AND status = 'PUBLISHED'
        ORDER BY enrolled_count DESC, created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据创建者查询学习课程列表 -->
    <select id="selectByCreator" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE creator_id = #{creatorId} AND deleted_at IS NULL
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据状态查询学习课程列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE status = #{status} AND deleted_at IS NULL
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 搜索学习课程 -->
    <select id="searchCourses" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE deleted_at IS NULL AND status = 'PUBLISHED'
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%')
                 OR description LIKE CONCAT('%', #{keyword}, '%')
                 OR tags LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="categories != null and categories.size() > 0">
            AND category IN
            <foreach collection="categories" item="category" open="(" separator="," close=")">
                #{category}
            </foreach>
        </if>
        <if test="difficultyLevels != null and difficultyLevels.size() > 0">
            AND difficulty_level IN
            <foreach collection="difficultyLevels" item="level" open="(" separator="," close=")">
                #{level}
            </foreach>
        </if>
        <if test="tags != null and tags.size() > 0">
            AND (
            <foreach collection="tags" item="tag" separator=" OR ">
                tags LIKE CONCAT('%', #{tag}, '%')
            </foreach>
            )
        </if>
        <if test="isOfficial != null">
            AND is_official = #{isOfficial}
        </if>
        <if test="minHours != null">
            AND total_hours >= #{minHours}
        </if>
        <if test="maxHours != null">
            AND total_hours <![CDATA[<=]]> #{maxHours}
        </if>
        ORDER BY enrolled_count DESC, created_at DESC
    </select>

    <!-- 获取热门课程列表 - 修复：移除手动LIMIT，让PageHelper处理分页 -->
    <select id="selectPopularCourses" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE deleted_at IS NULL AND status = 'PUBLISHED'
        ORDER BY enrolled_count DESC, completion_count DESC, created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取最新课程列表 -->
    <select id="selectLatestCourses" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE deleted_at IS NULL AND status = 'PUBLISHED'
        ORDER BY created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 获取官方课程列表 -->
    <select id="selectOfficialCourses" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE deleted_at IS NULL AND status = 'PUBLISHED' AND is_official = 1
        ORDER BY enrolled_count DESC, created_at DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 更新课程状态 -->
    <update id="updateStatus">
        UPDATE learning_course
        SET status = #{status}, updated_by = #{updatedBy}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 增加课程报名人数 -->
    <update id="incrementEnrolledCount">
        UPDATE learning_course
        SET enrolled_count = enrolled_count + 1, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 增加课程完成人数 -->
    <update id="incrementCompletionCount">
        UPDATE learning_course
        SET completion_count = completion_count + 1, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新课程资源数量 -->
    <update id="updateResourceCount">
        UPDATE learning_course
        SET resource_count = #{resourceCount}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新课程完成率 -->
    <update id="updateCompletionRate">
        UPDATE learning_course
        SET completion_rate = #{completionRate}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 批量更新课程状态 -->
    <update id="batchUpdateStatus">
        UPDATE learning_course
        SET status = #{status}, updated_by = #{updatedBy}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 批量删除课程（软删除） -->
    <update id="batchDelete">
        UPDATE learning_course
        SET deleted_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 根据名称查询课程 -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE name = #{name} AND deleted_at IS NULL
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 统计课程总数 -->
    <select id="countByStatus" resultType="int">
        SELECT COUNT(*)
        FROM learning_course
        WHERE deleted_at IS NULL
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <!-- 根据ID列表查询课程 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM learning_course
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

</mapper>
