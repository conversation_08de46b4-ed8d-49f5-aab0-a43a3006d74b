<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.KnowledgeTagRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTagRelation">
        <id column="id" property="id" />
        <result column="knowledge_id" property="knowledgeId" />
        <result column="tag_id" property="tagId" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, knowledge_id, tag_id, created_at, updated_at, created_by, updated_by
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTagRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO knowledge_tag_relation
        (knowledge_id, tag_id, created_at, updated_at, created_by, updated_by)
        VALUES
        (#{knowledgeId}, #{tagId}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy})
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeTagRelation">
        UPDATE knowledge_tag_relation
        <set>
            <if test="knowledgeId != null">knowledge_id = #{knowledgeId},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM knowledge_tag_relation WHERE id = #{id}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_tag_relation
        WHERE id = #{id}
    </select>

    <select id="selectByKnowledgeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_tag_relation
        WHERE knowledge_id = #{knowledgeId}
    </select>

    <select id="selectByTagId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_tag_relation
        WHERE tag_id = #{tagId}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO knowledge_tag_relation
        (knowledge_id, tag_id, created_at, updated_at, created_by, updated_by)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.knowledgeId}, #{item.tagId}, #{item.createdAt}, #{item.updatedAt}, #{item.createdBy}, #{item.updatedBy})
        </foreach>
    </insert>

    <delete id="deleteByKnowledgeId">
        DELETE FROM knowledge_tag_relation WHERE knowledge_id = #{knowledgeId}
    </delete>

    <delete id="deleteByTagId">
        DELETE FROM knowledge_tag_relation WHERE tag_id = #{tagId}
    </delete>

</mapper>
