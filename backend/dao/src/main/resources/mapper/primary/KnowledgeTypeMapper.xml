<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.KnowledgeTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.KnowledgeType">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="description" property="description" />
        <result column="icon_url" property="iconUrl" />
        <result column="is_active" property="isActive" />
        <result column="community_config_json" property="communityConfigJson" />
        <result column="render_config_json" property="renderConfigJson" />
        <result column="metadata_schema" property="metadataSchema" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, description, icon_url, is_active, community_config_json, render_config_json, metadata_schema, created_at, updated_at, created_by, updated_by
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeType" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO knowledge_type
        (name, code, description, icon_url, is_active,community_config_json, render_config_json, metadata_schema, created_at, updated_at, created_by, updated_by)
        VALUES
        (#{name}, #{code}, #{description}, #{iconUrl}, #{isActive}, #{communityConfigJson}, #{renderConfigJson}, #{metadataSchema}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy})
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeType">
        UPDATE knowledge_type
        SET name = #{name},
            code = #{code},
            description = #{description},
            icon_url = #{iconUrl},
        is_active = #{isActive},
        community_config_json = #{communityConfigJson},
        render_config_json = #{renderConfigJson},
        metadata_schema = #{metadataSchema},
        updated_at = #{updatedAt},
        updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM knowledge_type WHERE id = #{id}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_type
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_type
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.jdl.aic.core.service.dao.entity.primary.KnowledgeType">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_type
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="isActive != null">
                AND is_active = #{isActive}
            </if>
        </where>
    </select>

    <select id="selectByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_type
        WHERE name = #{name}
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge_type
        WHERE code = #{code}
    </select>

</mapper>
