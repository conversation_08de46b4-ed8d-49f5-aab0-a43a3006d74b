<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ContentTypeConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ContentTypeConfig">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="table_name" property="tableName" />
        <result column="is_portal_module" property="isPortalModule" />
        <result column="icon_url" property="iconUrl" />
        <result column="sort_order" property="sortOrder" />
        <result column="is_active" property="isActive" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, description, table_name, is_portal_module, icon_url, sort_order, 
        is_active, created_at, updated_at, created_by, updated_by
    </sql>

    <!-- 插入内容类型配置 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentTypeConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content_type_config (
            code, name, description, table_name, is_portal_module, icon_url, sort_order, 
            is_active, created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{code}, #{name}, #{description}, #{tableName}, #{isPortalModule}, #{iconUrl}, #{sortOrder},
            #{isActive}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除内容类型配置 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM content_type_config WHERE id = #{id}
    </delete>

    <!-- 更新内容类型配置 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentTypeConfig">
        UPDATE content_type_config
        SET 
            code = #{code},
            name = #{name},
            description = #{description},
            table_name = #{tableName},
            is_portal_module = #{isPortalModule},
            icon_url = #{iconUrl},
            sort_order = #{sortOrder},
            is_active = #{isActive},
            updated_at = #{updatedAt},
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询内容类型配置 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM content_type_config
        WHERE id = #{id}
    </select>

    <!-- 根据编码查询内容类型配置 -->
    <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM content_type_config
        WHERE code = #{code}
    </select>

    <!-- 根据条件查询内容类型配置列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentTypeConfig" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM content_type_config
        WHERE 1=1
        <if test="code != null and code != ''">
            AND code = #{code}
        </if>
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="tableName != null and tableName != ''">
            AND table_name = #{tableName}
        </if>
        <if test="isPortalModule != null">
            AND is_portal_module = #{isPortalModule}
        </if>
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 查询所有启用的内容类型配置 -->
    <select id="selectAllActive" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM content_type_config
        WHERE is_active = 1
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 查询门户模块配置 -->
    <select id="selectByPortalModule" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM content_type_config
        WHERE is_portal_module = #{isPortalModule}
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 更新内容类型配置状态 -->
    <update id="updateStatus">
        UPDATE content_type_config
        SET is_active = #{isActive}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新内容类型配置排序 -->
    <update id="updateSortOrder">
        UPDATE content_type_config
        SET sort_order = #{sortOrder}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据表名查询内容类型配置 -->
    <select id="selectByTableName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM content_type_config
        WHERE table_name = #{tableName}
    </select>

    <!-- 搜索内容类型配置 -->
    <select id="searchConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM content_type_config
        WHERE 1=1
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="isActive != null">
            AND is_active = #{isActive}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

</mapper>
