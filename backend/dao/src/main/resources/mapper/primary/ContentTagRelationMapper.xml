<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ContentTagRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ContentTagRelation">
        <id column="id" property="id" />
        <result column="content_id" property="contentId" />
        <result column="tag_id" property="tagId" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content_id, tag_id, created_at, updated_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentTagRelation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content_tag_relation (content_id, tag_id, created_at, updated_at)
        VALUES (#{contentId}, #{tagId}, #{createdAt}, #{updatedAt})
    </insert>

    <delete id="deleteById">
        DELETE FROM content_tag_relation WHERE id = #{id}
    </delete>

    <delete id="deleteByContentIdAndTagId">
        DELETE FROM content_tag_relation WHERE content_id = #{contentId} AND tag_id = #{tagId}
    </delete>

    <update id="update" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentTagRelation">
        UPDATE content_tag_relation
        <set>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag_relation
        WHERE id = #{id}
    </select>

    <select id="selectByContentId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag_relation
        WHERE content_id = #{contentId}
    </select>

    <select id="selectByTagId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag_relation
        WHERE tag_id = #{tagId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag_relation
    </select>

    <insert id="batchInsert">
        INSERT INTO content_tag_relation (content_id, tag_id, created_at, updated_at)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.contentId}, #{item.tagId}, #{item.createdAt}, #{item.updatedAt})
        </foreach>
    </insert>

    <delete id="batchDelete">
        DELETE FROM content_tag_relation WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
