<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.RssSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.RssSource">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="feed_url" property="feedUrl" />
        <result column="description" property="description" />
        <result column="category" property="category" />
        <result column="type" property="type" />
        <result column="owner_id" property="ownerId" />
        <result column="owner_name" property="ownerName" />
        <result column="last_fetched_at" property="lastFetchedAt" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 状态统计结果映射 -->
    <resultMap id="StatusCountResultMap" type="com.jdl.aic.core.service.dao.mapper.primary.RssSourceMapper$StatusCount">
        <result column="status" property="status" />
        <result column="count" property="count" />
    </resultMap>

    <!-- 类型统计结果映射 -->
    <resultMap id="TypeCountResultMap" type="com.jdl.aic.core.service.dao.mapper.primary.RssSourceMapper$TypeCount">
        <result column="type" property="type" />
        <result column="count" property="count" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, feed_url, description, category, type, owner_id, owner_name,
        last_fetched_at, status, created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <!-- 插入RSS源 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.RssSource" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO rss_source (
            name, feed_url, description, category, type, owner_id, owner_name,
            status, created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{name}, #{feedUrl}, #{description}, #{category}, #{type}, #{ownerId}, #{ownerName},
            #{status}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除RSS源（软删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE rss_source
        SET deleted_at = NOW(), updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新RSS源信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.RssSource">
        UPDATE rss_source
        <set>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="feedUrl != null and feedUrl != ''">feed_url = #{feedUrl},</if>
            <if test="description != null">description = #{description},</if>
            <if test="category != null">category = #{category},</if>
            <if test="type != null">type = #{type},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="ownerName != null">owner_name = #{ownerName},</if>
            <if test="lastFetchedAt != null">last_fetched_at = #{lastFetchedAt},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据ID查询RSS源 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rss_source
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <!-- 根据条件查询RSS源列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.primary.RssSource" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rss_source
        WHERE deleted_at IS NULL
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="ownerId != null and ownerId != ''">
            AND owner_id = #{ownerId}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据分类查询RSS源列表 -->
    <select id="selectByCategory" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rss_source
        WHERE deleted_at IS NULL
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据所有者ID查询RSS源列表 -->
    <select id="selectByOwnerId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rss_source
        WHERE owner_id = #{ownerId} AND deleted_at IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据状态查询RSS源列表 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rss_source
        WHERE status = #{status} AND deleted_at IS NULL
        ORDER BY created_at DESC
    </select>

    <!-- 根据类型查询RSS源列表 -->
    <select id="selectByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rss_source
        WHERE type = #{type} AND deleted_at IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 根据RSS订阅地址查询RSS源 -->
    <select id="selectByFeedUrl" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rss_source
        WHERE feed_url = #{feedUrl} AND deleted_at IS NULL
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 更新RSS源状态 -->
    <update id="updateStatus">
        UPDATE rss_source
        SET status = #{status}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 更新RSS源最后抓取时间 -->
    <update id="updateLastFetchedTime">
        UPDATE rss_source
        SET last_fetched_at = #{lastFetchedAt}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 批量更新RSS源状态 -->
    <update id="batchUpdateStatus">
        UPDATE rss_source
        SET status = #{status}, updated_at = NOW()
        WHERE deleted_at IS NULL
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 搜索RSS源 -->
    <select id="searchRssSources" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM rss_source
        WHERE deleted_at IS NULL
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_at DESC
    </select>

    <!-- 统计RSS源数量（按状态） -->
    <select id="countByStatus" resultMap="StatusCountResultMap">
        SELECT status, COUNT(*) as count
        FROM rss_source
        WHERE deleted_at IS NULL
        GROUP BY status
    </select>

    <!-- 统计RSS源数量（按类型） -->
    <select id="countByType" resultMap="TypeCountResultMap">
        SELECT type, COUNT(*) as count
        FROM rss_source
        WHERE deleted_at IS NULL
        GROUP BY type
    </select>

    <!-- 获取总数量 -->
    <select id="countTotal" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM rss_source
        WHERE deleted_at IS NULL
    </select>

</mapper>
