<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.KnowledgeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.Knowledge">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="content" property="content" />
        <result column="knowledge_type_id" property="knowledgeTypeId" />
        <result column="author_id" property="authorId" />
        <result column="author_name" property="authorName" />
        <result column="status" property="status" />
        <result column="visibility" property="visibility" />
        <result column="team_id" property="teamId" />
        <result column="team_name" property="teamName" />
        <result column="version" property="version" />
        <result column="read_count" property="readCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="fork_count" property="forkCount" />
        <result column="favorite_count" property="favoriteCount" />
        <result column="share_count" property="shareCount" />
        <result column="social_score" property="socialScore" />
        <result column="last_social_activity_at" property="lastSocialActivityAt" />
        <result column="cover_image_url" property="coverImageUrl" />
        <result column="metadata_json" property="metadataJson" />
        <result column="ai_review_status" property="aiReviewStatus" />
        <result column="ai_tags_json" property="aiTagsJson" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="RelationBaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation">
        <id column="id" property="id" />
        <result column="content_id" property="contentId" />
        <result column="category_id" property="categoryId" />
        <result column="content_type" property="contentType" />
        <result column="created_at" property="createdAt" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, description, content, knowledge_type_id, author_id, author_name, status, visibility,
        team_id, team_name, version, read_count, like_count, comment_count, fork_count, favorite_count,
        share_count, social_score, last_social_activity_at, cover_image_url,
        metadata_json, ai_review_status, ai_tags_json, created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.Knowledge" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO knowledge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="content != null">content,</if>
            <if test="knowledgeTypeId != null">knowledge_type_id,</if>
            <if test="authorId != null">author_id,</if>
            <if test="authorName != null">author_name,</if>
            <if test="status != null">status,</if>
            <if test="visibility != null">visibility,</if>
            <if test="teamId != null">team_id,</if>
            <if test="teamName != null">team_name,</if>
            <if test="version != null">version,</if>
            <if test="readCount != null">read_count,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="commentCount != null">comment_count,</if>
            <if test="forkCount != null">fork_count,</if>
            <if test="favoriteCount != null">favorite_count,</if>
            <if test="shareCount != null">share_count,</if>
            <if test="socialScore != null">social_score,</if>
            <if test="lastSocialActivityAt != null">last_social_activity_at,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="metadataJson != null">metadata_json,</if>
            <if test="aiReviewStatus != null">ai_review_status,</if>
            <if test="aiTagsJson != null">ai_tags_json,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="content != null">#{content},</if>
            <if test="knowledgeTypeId != null">#{knowledgeTypeId},</if>
            <if test="authorId != null">#{authorId},</if>
            <if test="authorName != null">#{authorName},</if>
            <if test="status != null">#{status},</if>
            <if test="visibility != null">#{visibility},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="version != null">#{version},</if>
            <if test="readCount != null">#{readCount},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="commentCount != null">#{commentCount},</if>
            <if test="forkCount != null">#{forkCount},</if>
            <if test="favoriteCount != null">#{favoriteCount},</if>
            <if test="shareCount != null">#{shareCount},</if>
            <if test="socialScore != null">#{socialScore},</if>
            <if test="lastSocialActivityAt != null">#{lastSocialActivityAt},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="metadataJson != null">#{metadataJson},</if>
            <if test="aiReviewStatus != null">#{aiReviewStatus},</if>
            <if test="aiTagsJson != null">#{aiTagsJson},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.Knowledge">
        UPDATE knowledge
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="content != null">content = #{content},</if>
            <if test="knowledgeTypeId != null">knowledge_type_id = #{knowledgeTypeId},</if>
            <if test="authorId != null">author_id = #{authorId},</if>
            <if test="authorName != null">author_name = #{authorName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="visibility != null">visibility = #{visibility},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="version != null">version = #{version},</if>
            <if test="readCount != null">read_count = #{readCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="commentCount != null">comment_count = #{commentCount},</if>
            <if test="forkCount != null">fork_count = #{forkCount},</if>
            <if test="favoriteCount != null">favorite_count = #{favoriteCount},</if>
            <if test="shareCount != null">share_count = #{shareCount},</if>
            <if test="socialScore != null">social_score = #{socialScore},</if>
            <if test="lastSocialActivityAt != null">last_social_activity_at = #{lastSocialActivityAt},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="metadataJson != null">metadata_json = #{metadataJson},</if>
            <if test="aiReviewStatus != null">ai_review_status = #{aiReviewStatus},</if>
            <if test="aiTagsJson != null">ai_tags_json = #{aiTagsJson},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteById">
        UPDATE knowledge SET deleted_at = NOW() WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge
        WHERE deleted_at IS NULL
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.jdl.aic.core.service.dao.entity.primary.Knowledge">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge
        <where>
            deleted_at IS NULL
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="knowledgeTypeId != null">
                AND knowledge_type_id = #{knowledgeTypeId}
            </if>
            <if test="authorId != null">
                AND author_id = #{authorId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="visibility != null">
                AND visibility = #{visibility}
            </if>
        </where>
    </select>

    <select id="selectByKnowledgeTypeId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge
        WHERE knowledge_type_id = #{knowledgeTypeId} AND deleted_at IS NULL
    </select>

    <select id="selectByAuthorId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge
        WHERE author_id = #{authorId} AND deleted_at IS NULL
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM knowledge
        WHERE status = #{status} AND deleted_at IS NULL
    </select>

    <update id="incrementReadCount">
        UPDATE knowledge SET read_count = read_count + 1 WHERE id = #{id}
    </update>

    <update id="incrementLikeCount">
        UPDATE knowledge SET like_count = like_count + 1 WHERE id = #{id}
    </update>

    <update id="incrementCommentCount">
        UPDATE knowledge SET comment_count = comment_count + 1 WHERE id = #{id}
    </update>

    <update id="incrementForkCount">
        UPDATE knowledge SET fork_count = fork_count + 1 WHERE id = #{id}
    </update>

    <update id="incrementFavoriteCount">
        UPDATE knowledge SET favorite_count = favorite_count + 1 WHERE id = #{id}
    </update>

    <update id="incrementShareCount">
        UPDATE knowledge SET share_count = share_count + 1 WHERE id = #{id}
    </update>

    <update id="updateSocialScore">
        UPDATE knowledge SET social_score = #{socialScore} WHERE id = #{id}
    </update>

    <update id="updateLastSocialActivityAt">
        UPDATE knowledge SET last_social_activity_at = NOW() WHERE id = #{id}
    </update>

    <select id="selectByCategoryId" resultMap="BaseResultMap">
        SELECT DISTINCT k.*
        FROM knowledge k
        INNER JOIN content_category_relation ccr ON k.id = ccr.content_id
        WHERE ccr.category_id = #{categoryId}
        AND ccr.content_type = #{contentType}
        AND k.deleted_at IS NULL
    </select>

    <select id="selectByComplexCondition" resultMap="BaseResultMap">
        SELECT DISTINCT k.*
        FROM knowledge k
        <if test="categoryId != null">
            INNER JOIN content_category_relation ccr ON k.id = ccr.content_id
        </if>
        <where>
            k.deleted_at IS NULL
            <if test="knowledgeTypeId != null">
                AND k.knowledge_type_id = #{knowledgeTypeId}
            </if>
            <if test="status != null">
                AND k.status = #{status}
            </if>
            <if test="authorId != null">
                AND k.author_id = #{authorId}
            </if>
            <if test="visibility != null">
                AND k.visibility = #{visibility}
            </if>
            <if test="teamId != null">
                AND k.team_id = #{teamId}
            </if>
            <if test="categoryId != null">
                AND ccr.category_id = #{categoryId}
                AND ccr.content_type = 'knowledge'
            </if>
            <if test="search != null and search != ''">
                AND (k.title LIKE CONCAT('%', #{search}, '%')
                OR k.description LIKE CONCAT('%', #{search}, '%')
                OR k.content LIKE CONCAT('%', #{search}, '%'))
            </if>
        </where>
        ORDER BY k.updated_at DESC
    </select>

    <select id="selectKnowledgeCategories" resultMap="RelationBaseResultMap">
        SELECT
           *
        FROM category c
        INNER JOIN content_category_relation ccr ON c.id = ccr.category_id
        WHERE ccr.content_id = #{knowledgeId}
          AND ccr.content_type = 'knowledge'
          AND c.deleted_at IS NULL
    </select>
</mapper>
