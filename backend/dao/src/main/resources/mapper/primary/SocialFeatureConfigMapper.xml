<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.SocialFeatureConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.SocialFeatureConfig">
        <id column="id" property="id" />
        <result column="content_type" property="contentType" />
        <result column="feature_type" property="featureType" />
        <result column="is_enabled" property="isEnabled" />
        <result column="config_json" property="configJson" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content_type, feature_type, is_enabled, config_json, 
        created_at, updated_at, created_by, updated_by
    </sql>

    <!-- 插入社交功能配置 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.SocialFeatureConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO social_feature_config (
            content_type, feature_type, is_enabled, config_json, 
            created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{contentType}, #{featureType}, #{isEnabled}, #{configJson},
            #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除社交功能配置 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM social_feature_config WHERE id = #{id}
    </delete>

    <!-- 更新社交功能配置 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.SocialFeatureConfig">
        UPDATE social_feature_config
        SET 
            content_type = #{contentType},
            feature_type = #{featureType},
            is_enabled = #{isEnabled},
            config_json = #{configJson},
            updated_at = #{updatedAt},
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询社交功能配置 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM social_feature_config
        WHERE id = #{id}
    </select>

    <!-- 根据内容类型和功能类型查询配置 -->
    <select id="selectByContentAndFeature" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM social_feature_config
        WHERE content_type = #{contentType} AND feature_type = #{featureType}
    </select>

    <!-- 根据内容类型查询所有功能配置 -->
    <select id="selectByContentType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM social_feature_config
        WHERE content_type = #{contentType}
        <if test="isEnabled != null">
            AND is_enabled = #{isEnabled}
        </if>
        ORDER BY feature_type ASC, created_at DESC
    </select>

    <!-- 根据功能类型查询所有配置 -->
    <select id="selectByFeatureType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM social_feature_config
        WHERE feature_type = #{featureType}
        <if test="isEnabled != null">
            AND is_enabled = #{isEnabled}
        </if>
        ORDER BY content_type ASC, created_at DESC
    </select>

    <!-- 根据条件查询社交功能配置列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.primary.SocialFeatureConfig" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM social_feature_config
        WHERE 1=1
        <if test="contentType != null and contentType != ''">
            AND content_type = #{contentType}
        </if>
        <if test="featureType != null and featureType != ''">
            AND feature_type = #{featureType}
        </if>
        <if test="isEnabled != null">
            AND is_enabled = #{isEnabled}
        </if>
        ORDER BY content_type ASC, feature_type ASC, created_at DESC
    </select>

    <!-- 查询所有启用的社交功能配置 -->
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM social_feature_config
        WHERE is_enabled = 1
        ORDER BY content_type ASC, feature_type ASC, created_at DESC
    </select>

    <!-- 更新社交功能配置状态 -->
    <update id="updateStatus">
        UPDATE social_feature_config
        SET is_enabled = #{isEnabled}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量更新内容类型的功能状态 -->
    <update id="updateStatusByContentType">
        UPDATE social_feature_config
        SET is_enabled = #{isEnabled}, updated_at = NOW()
        WHERE content_type = #{contentType}
    </update>

    <!-- 批量更新功能类型的状态 -->
    <update id="updateStatusByFeatureType">
        UPDATE social_feature_config
        SET is_enabled = #{isEnabled}, updated_at = NOW()
        WHERE feature_type = #{featureType}
    </update>

    <!-- 检查内容类型和功能类型组合是否存在 -->
    <select id="countByContentAndFeature" resultType="int">
        SELECT COUNT(*)
        FROM social_feature_config
        WHERE content_type = #{contentType} AND feature_type = #{featureType}
    </select>

</mapper>
