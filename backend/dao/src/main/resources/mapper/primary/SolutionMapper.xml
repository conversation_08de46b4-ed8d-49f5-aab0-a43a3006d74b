<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.SolutionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.Solution">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="content" property="content" />
        <result column="author_id" property="authorId" />
        <result column="author_name" property="authorName" />
        <result column="status" property="status" />
        <result column="visibility" property="visibility" />
        <result column="team_id" property="teamId" />
        <result column="team_name" property="teamName" />
        <result column="read_count" property="readCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="cover_image_url" property="coverImageUrl" />
        <result column="ai_review_status" property="aiReviewStatus" />
        <result column="ai_tags_json" property="aiTagsJson" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, description, content, author_id, author_name, status, visibility,
        team_id, team_name, read_count, like_count, comment_count, cover_image_url,
        ai_review_status, ai_tags_json, created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.Solution" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO solution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null">title,</if>
            <if test="description != null">description,</if>
            <if test="content != null">content,</if>
            <if test="authorId != null">author_id,</if>
            <if test="authorName != null">author_name,</if>
            <if test="status != null">status,</if>
            <if test="visibility != null">visibility,</if>
            <if test="teamId != null">team_id,</if>
            <if test="teamName != null">team_name,</if>
            <if test="readCount != null">read_count,</if>
            <if test="likeCount != null">like_count,</if>
            <if test="commentCount != null">comment_count,</if>
            <if test="coverImageUrl != null">cover_image_url,</if>
            <if test="aiReviewStatus != null">ai_review_status,</if>
            <if test="aiTagsJson != null">ai_tags_json,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="updatedBy != null">updated_by,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="title != null">#{title},</if>
            <if test="description != null">#{description},</if>
            <if test="content != null">#{content},</if>
            <if test="authorId != null">#{authorId},</if>
            <if test="authorName != null">#{authorName},</if>
            <if test="status != null">#{status},</if>
            <if test="visibility != null">#{visibility},</if>
            <if test="teamId != null">#{teamId},</if>
            <if test="teamName != null">#{teamName},</if>
            <if test="readCount != null">#{readCount},</if>
            <if test="likeCount != null">#{likeCount},</if>
            <if test="commentCount != null">#{commentCount},</if>
            <if test="coverImageUrl != null">#{coverImageUrl},</if>
            <if test="aiReviewStatus != null">#{aiReviewStatus},</if>
            <if test="aiTagsJson != null">#{aiTagsJson},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.Solution">
        UPDATE solution
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="content != null">content = #{content},</if>
            <if test="authorId != null">author_id = #{authorId},</if>
            <if test="authorName != null">author_name = #{authorName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="visibility != null">visibility = #{visibility},</if>
            <if test="teamId != null">team_id = #{teamId},</if>
            <if test="teamName != null">team_name = #{teamName},</if>
            <if test="readCount != null">read_count = #{readCount},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="commentCount != null">comment_count = #{commentCount},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="aiReviewStatus != null">ai_review_status = #{aiReviewStatus},</if>
            <if test="aiTagsJson != null">ai_tags_json = #{aiTagsJson},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteById">
        UPDATE solution SET deleted_at = NOW() WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution
        WHERE deleted_at IS NULL
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.jdl.aic.core.service.dao.entity.primary.Solution">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution
        <where>
            deleted_at IS NULL
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="authorId != null">
                AND author_id = #{authorId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="visibility != null">
                AND visibility = #{visibility}
            </if>
        </where>
    </select>

    <select id="selectByAuthorId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution
        WHERE author_id = #{authorId} AND deleted_at IS NULL
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution
        WHERE status = #{status} AND deleted_at IS NULL
    </select>

    <update id="incrementReadCount">
        UPDATE solution SET read_count = read_count + 1 WHERE id = #{id}
    </update>

    <update id="incrementLikeCount">
        UPDATE solution SET like_count = like_count + 1 WHERE id = #{id}
    </update>

    <update id="incrementCommentCount">
        UPDATE solution SET comment_count = comment_count + 1 WHERE id = #{id}
    </update>

    <!-- 减少点赞数 -->
    <update id="decrementLikeCount">
        UPDATE solution SET like_count = like_count - 1 WHERE id = #{id} AND like_count > 0
    </update>

    <!-- 减少评论数 -->
    <update id="decrementCommentCount">
        UPDATE solution SET comment_count = comment_count - 1 WHERE id = #{id} AND comment_count > 0
    </update>

    <!-- 按热度排序查询（点赞数 + 阅读数） -->
    <select id="selectPopularSolutions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution
        WHERE deleted_at IS NULL AND status = 2
        ORDER BY (like_count + read_count) DESC, created_at DESC
    </select>

    <!-- 搜索解决方案 -->
    <select id="searchSolutions" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution
        <where>
            deleted_at IS NULL AND status = 2
            <if test="keyword != null and keyword != ''">
                AND (title LIKE CONCAT('%', #{keyword}, '%')
                     OR description LIKE CONCAT('%', #{keyword}, '%')
                     OR content LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="category != null and category != ''">
                AND ai_tags_json LIKE CONCAT('%', #{category}, '%')
            </if>
        </where>
        ORDER BY (like_count + read_count) DESC, created_at DESC
    </select>

    <!-- 复杂条件查询解决方案 -->
    <select id="selectByComplexCondition" resultMap="BaseResultMap">
        SELECT DISTINCT s.*
        FROM solution s
        <if test="categoryId != null">
            INNER JOIN content_category_relation ccr ON s.id = ccr.content_id
        </if>
        <where>
            s.deleted_at IS NULL
            <if test="status != null">
                AND s.status = #{status}
            </if>
            <if test="authorId != null">
                AND s.author_id = #{authorId}
            </if>
            <if test="visibility != null">
                AND s.visibility = #{visibility}
            </if>
            <if test="teamId != null">
                AND s.team_id = #{teamId}
            </if>
            <if test="aiReviewStatus != null">
                AND s.ai_review_status = #{aiReviewStatus}
            </if>
            <if test="categoryId != null">
                AND ccr.category_id = #{categoryId}
                AND ccr.content_type = 'solution'
            </if>
            <if test="search != null and search != ''">
                AND (s.title LIKE CONCAT('%', #{search}, '%')
                OR s.description LIKE CONCAT('%', #{search}, '%')
                OR s.content LIKE CONCAT('%', #{search}, '%'))
            </if>
        </where>
        ORDER BY s.updated_at DESC
    </select>

    <!-- 按时间范围查询热门解决方案 -->
    <select id="selectPopularSolutionsByDateRange" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM solution
        <where>
            deleted_at IS NULL AND status = 2
            <if test="startDate != null">
                AND created_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND created_at &lt;= #{endDate}
            </if>
        </where>
        ORDER BY (like_count + read_count) DESC, created_at DESC
    </select>

    <!-- 统计解决方案数量 -->
    <select id="countSolutions" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM solution
        <where>
            deleted_at IS NULL
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="authorId != null">
                AND author_id = #{authorId}
            </if>
        </where>
    </select>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE solution
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

</mapper>
