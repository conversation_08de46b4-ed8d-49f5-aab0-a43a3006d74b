<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ContentTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ContentTag">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="use_count" property="useCount" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, description, use_count, status, created_at, updated_at
    </sql>

    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentTag" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO content_tag (name, description, use_count, status, created_at, updated_at)
        VALUES (#{name}, #{description}, #{useCount}, #{status}, #{createdAt}, #{updatedAt})
    </insert>

    <delete id="deleteById">
        DELETE FROM content_tag WHERE id = #{id}
    </delete>

    <update id="update" parameterType="com.jdl.aic.core.service.dao.entity.primary.ContentTag">
        UPDATE content_tag
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="useCount != null">use_count = #{useCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </set>
        WHERE id = #{id}
    </update>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag
    </select>

    <select id="selectByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag
        WHERE name = #{name}
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag
        WHERE status = #{status}
    </select>

    <update id="updateStatus">
        UPDATE content_tag
        SET status = #{status}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <update id="incrementUseCount">
        UPDATE content_tag
        SET use_count = use_count + #{increment}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <select id="selectMostUsedTags" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM content_tag
        ORDER BY use_count DESC
    </select>

</mapper>
