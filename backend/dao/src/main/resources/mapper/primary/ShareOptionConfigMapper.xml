<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.ShareOptionConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.ShareOptionConfig">
        <id column="id" property="id" />
        <result column="content_type" property="contentType" />
        <result column="share_type" property="shareType" />
        <result column="is_enabled" property="isEnabled" />
        <result column="display_name" property="displayName" />
        <result column="icon_url" property="iconUrl" />
        <result column="sort_order" property="sortOrder" />
        <result column="config_json" property="configJson" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, content_type, share_type, is_enabled, display_name, icon_url, sort_order, config_json, 
        created_at, updated_at, created_by, updated_by
    </sql>

    <!-- 插入分享选项配置 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.ShareOptionConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO share_option_config (
            content_type, share_type, is_enabled, display_name, icon_url, sort_order, config_json, 
            created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{contentType}, #{shareType}, #{isEnabled}, #{displayName}, #{iconUrl}, #{sortOrder}, #{configJson},
            #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除分享选项配置 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM share_option_config WHERE id = #{id}
    </delete>

    <!-- 更新分享选项配置 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.ShareOptionConfig">
        UPDATE share_option_config
        SET 
            content_type = #{contentType},
            share_type = #{shareType},
            is_enabled = #{isEnabled},
            display_name = #{displayName},
            icon_url = #{iconUrl},
            sort_order = #{sortOrder},
            config_json = #{configJson},
            updated_at = #{updatedAt},
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询分享选项配置 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM share_option_config
        WHERE id = #{id}
    </select>

    <!-- 根据内容类型和分享类型查询配置 -->
    <select id="selectByContentAndShare" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM share_option_config
        WHERE content_type = #{contentType} AND share_type = #{shareType}
    </select>

    <!-- 根据内容类型查询所有分享选项配置 -->
    <select id="selectByContentType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM share_option_config
        WHERE content_type = #{contentType}
        <if test="isEnabled != null">
            AND is_enabled = #{isEnabled}
        </if>
        ORDER BY sort_order ASC, created_at DESC
    </select>

    <!-- 根据分享类型查询所有配置 -->
    <select id="selectByShareType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM share_option_config
        WHERE share_type = #{shareType}
        <if test="isEnabled != null">
            AND is_enabled = #{isEnabled}
        </if>
        ORDER BY content_type ASC, sort_order ASC, created_at DESC
    </select>

    <!-- 根据条件查询分享选项配置列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.primary.ShareOptionConfig" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM share_option_config
        WHERE 1=1
        <if test="contentType != null and contentType != ''">
            AND content_type = #{contentType}
        </if>
        <if test="shareType != null and shareType != ''">
            AND share_type = #{shareType}
        </if>
        <if test="isEnabled != null">
            AND is_enabled = #{isEnabled}
        </if>
        <if test="displayName != null and displayName != ''">
            AND display_name LIKE CONCAT('%', #{displayName}, '%')
        </if>
        ORDER BY content_type ASC, sort_order ASC, created_at DESC
    </select>

    <!-- 查询所有启用的分享选项配置 -->
    <select id="selectAllEnabled" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM share_option_config
        WHERE is_enabled = 1
        ORDER BY content_type ASC, sort_order ASC, created_at DESC
    </select>

    <!-- 更新分享选项配置状态 -->
    <update id="updateStatus">
        UPDATE share_option_config
        SET is_enabled = #{isEnabled}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新分享选项配置排序 -->
    <update id="updateSortOrder">
        UPDATE share_option_config
        SET sort_order = #{sortOrder}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 批量更新内容类型的分享选项状态 -->
    <update id="updateStatusByContentType">
        UPDATE share_option_config
        SET is_enabled = #{isEnabled}, updated_at = NOW()
        WHERE content_type = #{contentType}
    </update>

    <!-- 批量更新分享类型的状态 -->
    <update id="updateStatusByShareType">
        UPDATE share_option_config
        SET is_enabled = #{isEnabled}, updated_at = NOW()
        WHERE share_type = #{shareType}
    </update>

    <!-- 检查内容类型和分享类型组合是否存在 -->
    <select id="countByContentAndShare" resultType="int">
        SELECT COUNT(*)
        FROM share_option_config
        WHERE content_type = #{contentType} AND share_type = #{shareType}
    </select>

    <!-- 搜索分享选项配置 -->
    <select id="searchConfigs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM share_option_config
        WHERE 1=1
        <if test="keyword != null and keyword != ''">
            AND display_name LIKE CONCAT('%', #{keyword}, '%')
        </if>
        <if test="isEnabled != null">
            AND is_enabled = #{isEnabled}
        </if>
        ORDER BY content_type ASC, sort_order ASC, created_at DESC
    </select>

</mapper>
