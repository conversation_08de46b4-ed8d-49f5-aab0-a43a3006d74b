<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jdl.aic.core.service.dao.mapper.primary.NewsFeedMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jdl.aic.core.service.dao.entity.primary.NewsFeed">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="author" property="author" />
        <result column="source_url" property="sourceUrl" />
        <result column="published_at" property="publishedAt" />
        <result column="content_summary" property="contentSummary" />
        <result column="content_html" property="contentHtml" />
        <result column="cover_image_url" property="coverImageUrl" />
        <result column="rss_source_id" property="rssSourceId" />
        <result column="source_name" property="sourceName" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="ai_review_status" property="aiReviewStatus" />
        <result column="ai_tags_json" property="aiTagsJson" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="created_by" property="createdBy" />
        <result column="updated_by" property="updatedBy" />
        <result column="deleted_at" property="deletedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, author, source_url, published_at, content_summary, content_html,
        cover_image_url, rss_source_id, source_name, type, status, ai_review_status,
        ai_tags_json, created_at, updated_at, created_by, updated_by, deleted_at
    </sql>

    <!-- 插入资讯 -->
    <insert id="insert" parameterType="com.jdl.aic.core.service.dao.entity.primary.NewsFeed" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO news_feed (
            title, author, source_url, published_at, content_summary, content_html,
            cover_image_url, rss_source_id, source_name, type, status, ai_review_status,
            ai_tags_json, created_at, updated_at, created_by, updated_by
        ) VALUES (
            #{title}, #{author}, #{sourceUrl}, #{publishedAt}, #{contentSummary}, #{contentHtml},
            #{coverImageUrl}, #{rssSourceId}, #{sourceName}, #{type}, #{status}, #{aiReviewStatus},
            #{aiTagsJson}, #{createdAt}, #{updatedAt}, #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 根据ID删除资讯（软删除） -->
    <update id="deleteById" parameterType="java.lang.Long">
        UPDATE news_feed
        SET deleted_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 批量删除资讯（软删除） -->
    <update id="batchDeleteByIds">
        UPDATE news_feed
        SET deleted_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 更新资讯信息 -->
    <update id="updateById" parameterType="com.jdl.aic.core.service.dao.entity.primary.NewsFeed">
        UPDATE news_feed
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="author != null">author = #{author},</if>
            <if test="sourceUrl != null">source_url = #{sourceUrl},</if>
            <if test="publishedAt != null">published_at = #{publishedAt},</if>
            <if test="contentSummary != null">content_summary = #{contentSummary},</if>
            <if test="contentHtml != null">content_html = #{contentHtml},</if>
            <if test="coverImageUrl != null">cover_image_url = #{coverImageUrl},</if>
            <if test="rssSourceId != null">rss_source_id = #{rssSourceId},</if>
            <if test="sourceName != null">source_name = #{sourceName},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="aiReviewStatus != null">ai_review_status = #{aiReviewStatus},</if>
            <if test="aiTagsJson != null">ai_tags_json = #{aiTagsJson},</if>
            <if test="updatedBy != null">updated_by = #{updatedBy},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 根据ID查询资讯 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE id = #{id} AND deleted_at IS NULL
    </select>

    <!-- 根据条件查询资讯列表 -->
    <select id="selectByCondition" parameterType="com.jdl.aic.core.service.dao.entity.primary.NewsFeed" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE deleted_at IS NULL
        <if test="title != null and title != ''">
            AND title LIKE CONCAT('%', #{title}, '%')
        </if>
        <if test="author != null and author != ''">
            AND author = #{author}
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="aiReviewStatus != null">
            AND ai_review_status = #{aiReviewStatus}
        </if>
        <if test="rssSourceId != null">
            AND rss_source_id = #{rssSourceId}
        </if>
        <if test="sourceName != null and sourceName != ''">
            AND source_name = #{sourceName}
        </if>
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 根据RSS源ID查询资讯列表 -->
    <select id="selectByRssSourceId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE rss_source_id = #{rssSourceId} AND deleted_at IS NULL
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 根据状态查询资讯列表 -->
    <select id="selectByStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE status = #{status} AND deleted_at IS NULL
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 根据AI审核状态查询资讯列表 -->
    <select id="selectByAiReviewStatus" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE ai_review_status = #{aiReviewStatus} AND deleted_at IS NULL
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 更新资讯状态 -->
    <update id="updateStatus">
        UPDATE news_feed
        SET status = #{status}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 批量更新资讯状态 -->
    <update id="batchUpdateStatus">
        UPDATE news_feed
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted_at IS NULL
    </update>

    <!-- 更新AI审核状态 -->
    <update id="updateAiReviewStatus">
        UPDATE news_feed
        SET ai_review_status = #{aiReviewStatus}, updated_at = NOW()
        WHERE id = #{id} AND deleted_at IS NULL
    </update>

    <!-- 搜索资讯（根据标题和摘要） -->
    <select id="searchNewsFeed" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE deleted_at IS NULL
        <if test="keyword != null and keyword != ''">
            AND (title LIKE CONCAT('%', #{keyword}, '%')
                 OR content_summary LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 根据发布时间范围查询资讯 -->
    <select id="selectByPublishedTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE deleted_at IS NULL
        <if test="startTime != null">
            AND published_at >= #{startTime}
        </if>
        <if test="endTime != null">
            AND published_at &lt;= #{endTime}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 根据来源名称查询资讯 -->
    <select id="selectBySourceName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE source_name = #{sourceName} AND deleted_at IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 根据作者查询资讯 -->
    <select id="selectByAuthor" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE author = #{author} AND deleted_at IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 检查资讯是否存在（根据原文链接） -->
    <select id="selectBySourceUrl" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE source_url = #{sourceUrl} AND deleted_at IS NULL
    </select>

    <!-- 获取资讯统计信息 -->
    <select id="getNewsFeedStatistics" resultType="java.util.Map">
        SELECT
            COUNT(*) as totalCount,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as publishedCount,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pendingCount,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as offlineCount
        FROM news_feed
        WHERE deleted_at IS NULL
    </select>

    <!-- 获取今日新增资讯数量 -->
    <select id="getTodayNewsFeedCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM news_feed
        WHERE DATE(created_at) = CURDATE() AND deleted_at IS NULL
    </select>

    <!-- 获取最新资讯列表 -->
    <select id="selectLatestNewsFeed" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE deleted_at IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 获取热门资讯列表（按阅读量排序） -->
    <select id="selectPopularNewsFeed" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE deleted_at IS NULL
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 复合条件查询资讯列表（用于分页查询） -->
    <select id="selectByComplexCondition" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM news_feed
        WHERE deleted_at IS NULL
        <if test="type != null">
            AND type = #{type}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="aiReviewStatus != null">
            AND ai_review_status = #{aiReviewStatus}
        </if>
        <if test="rssSourceId != null">
            AND rss_source_id = #{rssSourceId}
        </if>
        <if test="sourceName != null and sourceName != ''">
            AND source_name LIKE CONCAT('%', #{sourceName}, '%')
        </if>
        <if test="author != null and author != ''">
            AND author LIKE CONCAT('%', #{author}, '%')
        </if>
        <if test="keyword != null and keyword != ''">
            AND (title LIKE CONCAT('%', #{keyword}, '%')
                 OR content_summary LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="publishedAtStart != null">
            AND published_at >= #{publishedAtStart}
        </if>
        <if test="publishedAtEnd != null">
            AND published_at &lt;= #{publishedAtEnd}
        </if>
        ORDER BY published_at DESC, created_at DESC
    </select>

    <!-- 统计各状态资讯数量 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT
            status,
            COUNT(*) as count
        FROM news_feed
        WHERE deleted_at IS NULL
        GROUP BY status
    </select>

    <!-- 统计各AI审核状态资讯数量 -->
    <select id="countByAiReviewStatus" resultType="java.util.Map">
        SELECT
            ai_review_status,
            COUNT(*) as count
        FROM news_feed
        WHERE deleted_at IS NULL
        GROUP BY ai_review_status
    </select>

</mapper>
