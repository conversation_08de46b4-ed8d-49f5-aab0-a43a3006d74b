package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 内容类型配置表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("content_type_config")
public class ContentTypeConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内容类型编码
     */
    private String code;

    /**
     * 内容类型名称
     */
    private String name;

    /**
     * 内容类型描述
     */
    private String description;

    /**
     * 对应的主表名
     */
    private String tableName;

    /**
     * 是否为门户功能板块（1:是, 0:否）
     */
    private Boolean isPortalModule;

    /**
     * 图标URL
     */
    private String iconUrl;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否启用
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 更新人（跨库引用，无外键约束）
     */
    private String updatedBy;
}
