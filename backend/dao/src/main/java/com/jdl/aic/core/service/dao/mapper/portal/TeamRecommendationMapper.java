package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.TeamRecommendation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队推荐内容数据访问接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface TeamRecommendationMapper {

    /**
     * 根据ID查询团队推荐
     */
    TeamRecommendation selectById(@Param("id") Long id);

    /**
     * 根据团队ID查询推荐内容
     */
    List<TeamRecommendation> selectByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据用户ID查询推荐记录
     */
    List<TeamRecommendation> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据内容ID和类型查询推荐记录
     */
    List<TeamRecommendation> selectByContentIdAndType(@Param("contentId") Long contentId, @Param("contentType") String contentType);

    /**
     * 根据团队ID和内容类型查询推荐
     */
    List<TeamRecommendation> selectByTeamIdAndContentType(@Param("teamId") Long teamId, @Param("contentType") String contentType);

    /**
     * 根据状态查询推荐记录
     */
    List<TeamRecommendation> selectByStatus(@Param("status") String status);

    /**
     * 查询所有推荐记录
     */
    List<TeamRecommendation> selectAll();

    /**
     * 查询活跃的推荐记录
     */
    List<TeamRecommendation> selectActive();

    /**
     * 插入团队推荐
     */
    int insert(TeamRecommendation teamRecommendation);

    /**
     * 更新团队推荐
     */
    int updateById(TeamRecommendation teamRecommendation);

    /**
     * 根据ID删除团队推荐（物理删除）
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID软删除团队推荐
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 更新推荐状态
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 统计推荐记录数量
     */
    int count();

    /**
     * 统计活跃推荐记录数量
     */
    int countActive();

    /**
     * 统计团队的推荐数量
     */
    int countByTeamId(@Param("teamId") Long teamId);

    /**
     * 统计用户的推荐数量
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 统计内容的推荐数量
     */
    int countByContentIdAndType(@Param("contentId") Long contentId, @Param("contentType") String contentType);

    /**
     * 批量插入团队推荐
     */
    int batchInsert(@Param("teamRecommendations") List<TeamRecommendation> teamRecommendations);

    /**
     * 批量更新推荐状态
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status);

    /**
     * 批量软删除推荐记录
     */
    int batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 检查推荐记录是否存在
     */
    boolean existsByTeamIdAndContentIdAndType(@Param("teamId") Long teamId, @Param("contentId") Long contentId, @Param("contentType") String contentType);

    /**
     * 根据条件查询推荐记录
     */
    List<TeamRecommendation> selectByCondition(@Param("teamId") Long teamId, 
                                              @Param("userId") Long userId,
                                              @Param("contentType") String contentType,
                                              @Param("status") String status);

    /**
     * 获取团队推荐统计信息
     */
    List<Object> getTeamRecommendationStatistics(@Param("teamId") Long teamId);
}
