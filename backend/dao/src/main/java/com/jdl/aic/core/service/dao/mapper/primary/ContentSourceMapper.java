package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentSource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface ContentSourceMapper {
    int insert(ContentSource record);
    int updateByPrimaryKey(ContentSource record);
    int deleteByPrimaryKey(Long id);
    ContentSource selectByPrimaryKey(Long id);
    List<ContentSource> selectAll();
}
