package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.LearningResource;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 学习资源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface LearningResourceMapper {

    /**
     * 插入一条学习资源记录
     *
     * @param learningResource 学习资源实体
     * @return 影响的行数
     */
    int insert(LearningResource learningResource);

    /**
     * 根据ID删除学习资源记录
     *
     * @param id 学习资源ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 更新学习资源记录
     *
     * @param learningResource 学习资源实体
     * @return 影响的行数
     */
    int update(LearningResource learningResource);

    /**
     * 根据ID查询学习资源
     *
     * @param id 学习资源ID
     * @return 学习资源实体
     */
    LearningResource selectById(@Param("id") Long id);

    /**
     * 根据资源类型查询学习资源列表
     *
     * @param resourceType 资源类型
     * @return 学习资源实体列表
     */
    List<LearningResource> selectByResourceType(@Param("resourceType") String resourceType);

    /**
     * 根据难度级别查询学习资源列表
     *
     * @param difficultyLevel 难度级别
     * @return 学习资源实体列表
     */
    List<LearningResource> selectByDifficultyLevel(@Param("difficultyLevel") String difficultyLevel);

    /**
     * 查询评分最高的学习资源列表
     *
     * @param limit 限制返回的记录数
     * @return 学习资源实体列表
     */
    List<LearningResource> selectTopRated(@Param("limit") int limit);

    /**
     * 查询最受欢迎（收藏次数最多）的学习资源列表
     *
     * @param limit 限制返回的记录数
     * @return 学习资源实体列表
     */
    List<LearningResource> selectMostPopular(@Param("limit") int limit);

    /**
     * 根据关键字搜索学习资源
     *
     * @param keyword 搜索关键字
     * @return 学习资源实体列表
     */
    List<LearningResource> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 统计特定资源类型的学习资源数量
     *
     * @param resourceType 资源类型
     * @return 资源数量
     */
    int countByResourceType(@Param("resourceType") String resourceType);

    /**
     * 根据条件分页查询学习资源
     *
     * @param resourceType 资源类型过滤
     * @param category 分类过滤
     * @param difficulty 难度过滤
     * @param status 状态过滤
     * @param search 搜索关键词
     * @return 学习资源实体列表
     */
    List<LearningResource> selectByConditions(
            @Param("resourceType") String resourceType,
            @Param("category") String category,
            @Param("difficulty") String difficulty,
            @Param("status") Integer status,
            @Param("search") String search);

    /**
     * 根据ID列表查询学习资源
     *
     * @param ids 学习资源ID列表
     * @return 学习资源实体列表
     */
    List<LearningResource> selectByIds(@Param("ids") List<Long> ids);

    /**
     * 批量更新学习资源状态
     *
     * @param ids 学习资源ID列表
     * @param status 新状态
     * @param updatedBy 更新用户
     * @return 影响的行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status, @Param("updatedBy") String updatedBy);

    /**
     * 增加浏览次数
     *
     * @param id 学习资源ID
     * @return 影响的行数
     */
    int incrementViewCount(@Param("id") Long id);

    /**
     * 增加下载次数
     *
     * @param id 学习资源ID
     * @return 影响的行数
     */
    int incrementDownloadCount(@Param("id") Long id);

    /**
     * 获取推荐学习资源
     *
     * @param userId 用户ID
     * @param category 分类过滤
     * @param limit 限制数量
     * @return 学习资源实体列表
     */
    List<LearningResource> selectRecommendedResources(@Param("userId") Long userId, @Param("category") String category, @Param("limit") Integer limit);

    /**
     * 获取热门学习资源
     *
     * @param category 分类过滤
     * @param days 统计天数
     * @param limit 限制数量
     * @return 学习资源实体列表
     */
    List<LearningResource> selectPopularResources(@Param("category") String category, @Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 高级搜索学习资源
     *
     * @param keyword 搜索关键词
     * @param filters 搜索过滤条件
     * @return 学习资源实体列表
     */
    List<LearningResource> advancedSearch(@Param("keyword") String keyword, @Param("filters") Object filters);

    /**
     * 查询所有学习资源
     *
     * @return 学习资源实体列表
     */
    List<LearningResource> selectAll();

    /**
     * 根据条件查询学习资源
     *
     * @param learningResource 查询条件
     * @return 学习资源实体列表
     */
    List<LearningResource> selectByCondition(LearningResource learningResource);
}
