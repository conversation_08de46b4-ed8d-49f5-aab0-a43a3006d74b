package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.UserBadges;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户徽章关联数据访问接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface UserBadgesMapper {

    /**
     * 根据ID查询用户徽章关联
     */
    UserBadges selectById(@Param("id") Long id);

    /**
     * 根据用户ID查询用户的所有徽章
     */
    List<UserBadges> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据徽章ID查询拥有该徽章的所有用户
     */
    List<UserBadges> selectByBadgeId(@Param("badgeId") Long badgeId);

    /**
     * 根据用户ID和徽章ID查询关联记录
     */
    UserBadges selectByUserIdAndBadgeId(@Param("userId") Long userId, @Param("badgeId") Long badgeId);

    /**
     * 查询所有用户徽章关联
     */
    List<UserBadges> selectAll();

    /**
     * 查询未删除的用户徽章关联
     */
    List<UserBadges> selectActive();

    /**
     * 插入用户徽章关联
     */
    int insert(UserBadges userBadges);

    /**
     * 更新用户徽章关联
     */
    int updateById(UserBadges userBadges);

    /**
     * 根据ID删除用户徽章关联（物理删除）
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID软删除用户徽章关联
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 根据用户ID和徽章ID删除关联
     */
    int deleteByUserIdAndBadgeId(@Param("userId") Long userId, @Param("badgeId") Long badgeId);

    /**
     * 统计用户徽章关联数量
     */
    int count();

    /**
     * 统计用户的徽章数量
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 统计徽章的用户数量
     */
    int countByBadgeId(@Param("badgeId") Long badgeId);

    /**
     * 批量插入用户徽章关联
     */
    int batchInsert(@Param("userBadges") List<UserBadges> userBadges);

    /**
     * 批量删除用户的徽章
     */
    int batchDeleteByUserId(@Param("userId") Long userId, @Param("badgeIds") List<Long> badgeIds);

    /**
     * 检查用户是否拥有指定徽章
     */
    boolean existsByUserIdAndBadgeId(@Param("userId") Long userId, @Param("badgeId") Long badgeId);

    /**
     * 获取用户徽章统计信息
     */
    List<Object> getUserBadgeStatistics(@Param("userId") Long userId);
}
