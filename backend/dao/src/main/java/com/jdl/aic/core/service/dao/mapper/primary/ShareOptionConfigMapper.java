package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ShareOptionConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 分享选项配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-18
 */
@Mapper
public interface ShareOptionConfigMapper {

    /**
     * 插入分享选项配置
     *
     * @param shareOptionConfig 分享选项配置信息
     * @return 影响行数
     */
    int insert(ShareOptionConfig shareOptionConfig);

    /**
     * 根据ID删除分享选项配置
     *
     * @param id 配置ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新分享选项配置信息
     *
     * @param shareOptionConfig 分享选项配置信息
     * @return 影响行数
     */
    int updateById(ShareOptionConfig shareOptionConfig);

    /**
     * 根据ID查询分享选项配置
     *
     * @param id 配置ID
     * @return 分享选项配置信息
     */
    ShareOptionConfig selectById(Long id);

    /**
     * 根据内容类型和分享类型查询配置
     *
     * @param contentType 内容类型
     * @param shareType 分享类型
     * @return 分享选项配置信息
     */
    ShareOptionConfig selectByContentAndShare(@Param("contentType") String contentType, 
                                            @Param("shareType") String shareType);

    /**
     * 根据内容类型查询所有分享选项配置
     *
     * @param contentType 内容类型
     * @param isEnabled 是否启用
     * @return 分享选项配置列表
     */
    List<ShareOptionConfig> selectByContentType(@Param("contentType") String contentType,
                                              @Param("isEnabled") Boolean isEnabled);

    /**
     * 根据分享类型查询所有配置
     *
     * @param shareType 分享类型
     * @param isEnabled 是否启用
     * @return 分享选项配置列表
     */
    List<ShareOptionConfig> selectByShareType(@Param("shareType") String shareType,
                                            @Param("isEnabled") Boolean isEnabled);

    /**
     * 根据条件查询分享选项配置列表
     *
     * @param shareOptionConfig 查询条件
     * @return 分享选项配置列表
     */
    List<ShareOptionConfig> selectByCondition(ShareOptionConfig shareOptionConfig);

    /**
     * 查询所有启用的分享选项配置
     *
     * @return 分享选项配置列表
     */
    List<ShareOptionConfig> selectAllEnabled();

    /**
     * 更新分享选项配置状态
     *
     * @param id 配置ID
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("isEnabled") Boolean isEnabled);

    /**
     * 更新分享选项配置排序
     *
     * @param id 配置ID
     * @param sortOrder 排序值
     * @return 影响行数
     */
    int updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);

    /**
     * 批量更新内容类型的分享选项状态
     *
     * @param contentType 内容类型
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    int updateStatusByContentType(@Param("contentType") String contentType, 
                                @Param("isEnabled") Boolean isEnabled);

    /**
     * 批量更新分享类型的状态
     *
     * @param shareType 分享类型
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    int updateStatusByShareType(@Param("shareType") String shareType, 
                              @Param("isEnabled") Boolean isEnabled);

    /**
     * 检查内容类型和分享类型组合是否存在
     *
     * @param contentType 内容类型
     * @param shareType 分享类型
     * @return 存在数量
     */
    int countByContentAndShare(@Param("contentType") String contentType, 
                             @Param("shareType") String shareType);

    /**
     * 搜索分享选项配置（根据显示名称）
     *
     * @param keyword 搜索关键词
     * @param isEnabled 是否启用
     * @return 分享选项配置列表
     */
    List<ShareOptionConfig> searchConfigs(@Param("keyword") String keyword,
                                        @Param("isEnabled") Boolean isEnabled);
}
