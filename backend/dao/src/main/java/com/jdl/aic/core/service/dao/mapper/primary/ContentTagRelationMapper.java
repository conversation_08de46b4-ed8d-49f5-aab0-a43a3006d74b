package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentTagRelation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容标签关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentTagRelationMapper {

    /**
     * 插入一条内容标签关系记录
     *
     * @param contentTagRelation 内容标签关系实体
     * @return 影响的行数
     */
    int insert(ContentTagRelation contentTagRelation);

    /**
     * 根据ID删除内容标签关系记录
     *
     * @param id 内容标签关系ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据内容ID和标签ID删除内容标签关系记录
     *
     * @param contentId 内容ID
     * @param tagId 标签ID
     * @return 影响的行数
     */
    int deleteByContentIdAndTagId(@Param("contentId") Long contentId, @Param("tagId") Long tagId);

    /**
     * 更新内容标签关系记录
     *
     * @param contentTagRelation 内容标签关系实体
     * @return 影响的行数
     */
    int update(ContentTagRelation contentTagRelation);

    /**
     * 根据ID查询内容标签关系
     *
     * @param id 内容标签关系ID
     * @return 内容标签关系实体
     */
    ContentTagRelation selectById(@Param("id") Long id);

    /**
     * 根据内容ID查询内容标签关系列表
     *
     * @param contentId 内容ID
     * @return 内容标签关系实体列表
     */
    List<ContentTagRelation> selectByContentId(@Param("contentId") Long contentId);

    /**
     * 根据标签ID查询内容标签关系列表
     *
     * @param tagId 标签ID
     * @return 内容标签关系实体列表
     */
    List<ContentTagRelation> selectByTagId(@Param("tagId") Long tagId);

    /**
     * 查询所有内容标签关系
     *
     * @return 内容标签关系实体列表
     */
    List<ContentTagRelation> selectAll();

    /**
     * 批量插入内容标签关系记录
     *
     * @param contentTagRelations 内容标签关系实体列表
     * @return 影响的行数
     */
    int batchInsert(@Param("list") List<ContentTagRelation> contentTagRelations);

    /**
     * 批量删除内容标签关系记录
     *
     * @param ids ID列表
     * @return 影响的行数
     */
    int batchDelete(@Param("list") List<Long> ids);
}
