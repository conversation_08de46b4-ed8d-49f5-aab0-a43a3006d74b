package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation;
import com.jdl.aic.core.service.dao.entity.primary.Knowledge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 知识表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface KnowledgeMapper {

    /**
     * 插入一条知识记录
     *
     * @param knowledge 知识实体
     * @return 影响的行数
     */
    int insert(Knowledge knowledge);

    /**
     * 根据ID更新知识记录
     *
     * @param knowledge 知识实体
     * @return 影响的行数
     */
    int updateById(Knowledge knowledge);

    /**
     * 根据ID删除知识记录（软删除）
     *
     * @param id 知识ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询知识记录
     *
     * @param id 知识ID
     * @return 知识实体
     */
    Knowledge selectById(@Param("id") Long id);

    /**
     * 查询所有知识记录
     *
     * @return 知识实体列表
     */
    List<Knowledge> selectAll();

    /**
     * 根据条件查询知识记录
     *
     * @param knowledge 查询条件
     * @return 知识实体列表
     */
    List<Knowledge> selectByCondition(Knowledge knowledge);

    /**
     * 根据知识类型ID查询知识记录
     *
     * @param knowledgeTypeId 知识类型ID
     * @return 知识实体列表
     */
    List<Knowledge> selectByKnowledgeTypeId(@Param("knowledgeTypeId") Long knowledgeTypeId);

    /**
     * 根据作者ID查询知识记录
     *
     * @param authorId 作者ID
     * @return 知识实体列表
     */
    List<Knowledge> selectByAuthorId(@Param("authorId") String authorId);

    /**
     * 根据状态查询知识记录
     *
     * @param status 知识状态
     * @return 知识实体列表
     */
    List<Knowledge> selectByStatus(@Param("status") Integer status);

    /**
     * 更新知识的阅读次数
     *
     * @param id 知识ID
     * @return 影响的行数
     */
    int incrementReadCount(@Param("id") Long id);

    /**
     * 更新知识的点赞次数
     *
     * @param id 知识ID
     * @return 影响的行数
     */
    int incrementLikeCount(@Param("id") Long id);

    /**
     * 更新知识的评论次数
     *
     * @param id 知识ID
     * @return 影响的行数
     */
    int incrementCommentCount(@Param("id") Long id);

    /**
     * 更新知识的Fork次数
     *
     * @param id 知识ID
     * @return 影响的行数
     */
    int incrementForkCount(@Param("id") Long id);

    /**
     * 更新知识的收藏次数
     *
     * @param id 知识ID
     * @return 影响的行数
     */
    int incrementFavoriteCount(@Param("id") Long id);

    /**
     * 更新知识的分享次数
     *
     * @param id 知识ID
     * @return 影响的行数
     */
    int incrementShareCount(@Param("id") Long id);

    /**
     * 更新知识的社交热度分数
     *
     * @param id 知识ID
     * @param socialScore 社交热度分数
     * @return 影响的行数
     */
    int updateSocialScore(@Param("id") Long id, @Param("socialScore") java.math.BigDecimal socialScore);

    /**
     * 更新知识的最后社交活动时间
     *
     * @param id 知识ID
     * @return 影响的行数
     */
    int updateLastSocialActivityAt(@Param("id") Long id);

    /**
     * 根据复合条件查询知识记录（支持分类关联查询）
     *
     * @param knowledgeTypeId 知识类型ID过滤
     * @param status 状态过滤
     * @param visibility 可见性过滤
     * @param authorId 作者ID过滤
     * @param teamId 团队ID过滤
     * @param categoryId 分类ID过滤
     * @param search 搜索关键词
     * @return 知识实体列表
     */
    List<Knowledge> selectByComplexCondition(
                                             @Param("knowledgeTypeId") Long knowledgeTypeId,
                                             @Param("status") Integer status,
                                             @Param("visibility") Integer visibility,
                                             @Param("authorId") String authorId,
                                             @Param("teamId") Long teamId,
                                             @Param("categoryId") Long categoryId,
                                             @Param("search") String search);

    /**
     * 查询知识的分类信息（包含分类ID和名称）
     *
     * @param knowledgeId 知识ID
     * @return 分类信息列表，每个元素包含id和name
     */
    List<ContentCategoryRelation> selectKnowledgeCategories(@Param("knowledgeId") Long knowledgeId);
    
    /**
     * 统计用户发布的特定状态的文章数量
     *
     * @param authorId 作者ID
     * @param status 文章状态
     * @return 文章数量
     */
    @Select("SELECT COUNT(*) FROM knowledge WHERE author_id = #{authorId} AND status = #{status}")
    int countByAuthorIdAndStatus(@Param("authorId") String authorId, @Param("status") Integer status);
}
