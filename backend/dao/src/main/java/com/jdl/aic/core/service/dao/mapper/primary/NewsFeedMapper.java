package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.NewsFeed;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 资讯表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-19
 */
@Mapper
public interface NewsFeedMapper {

    /**
     * 插入资讯
     *
     * @param newsFeed 资讯信息
     * @return 影响行数
     */
    int insert(NewsFeed newsFeed);

    /**
     * 根据ID删除资讯（软删除）
     *
     * @param id 资讯ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 批量删除资讯（软删除）
     *
     * @param ids 资讯ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 更新资讯信息
     *
     * @param newsFeed 资讯信息
     * @return 影响行数
     */
    int updateById(NewsFeed newsFeed);

    /**
     * 根据ID查询资讯
     *
     * @param id 资讯ID
     * @return 资讯信息
     */
    NewsFeed selectById(Long id);

    /**
     * 根据条件查询资讯列表
     *
     * @param newsFeed 查询条件
     * @return 资讯列表
     */
    List<NewsFeed> selectByCondition(NewsFeed newsFeed);

    /**
     * 根据RSS源ID查询资讯列表
     *
     * @param rssSourceId RSS源ID
     * @return 资讯列表
     */
    List<NewsFeed> selectByRssSourceId(@Param("rssSourceId") Long rssSourceId);

    /**
     * 根据状态查询资讯列表
     *
     * @param status 状态
     * @return 资讯列表
     */
    List<NewsFeed> selectByStatus(@Param("status") Integer status);

    /**
     * 根据AI审核状态查询资讯列表
     *
     * @param aiReviewStatus AI审核状态
     * @return 资讯列表
     */
    List<NewsFeed> selectByAiReviewStatus(@Param("aiReviewStatus") Integer aiReviewStatus);

    /**
     * 更新资讯状态
     *
     * @param id 资讯ID
     * @param status 状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 批量更新资讯状态
     *
     * @param ids 资讯ID列表
     * @param status 状态
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 更新AI审核状态
     *
     * @param id 资讯ID
     * @param aiReviewStatus AI审核状态
     * @return 影响行数
     */
    int updateAiReviewStatus(@Param("id") Long id, @Param("aiReviewStatus") Integer aiReviewStatus);

    /**
     * 搜索资讯（根据标题和摘要）
     *
     * @param keyword 搜索关键词
     * @param type 资讯类型过滤
     * @param status 状态过滤
     * @return 资讯列表
     */
    List<NewsFeed> searchNewsFeed(@Param("keyword") String keyword,
                                 @Param("type") Integer type,
                                 @Param("status") Integer status);

    /**
     * 根据发布时间范围查询资讯
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param status 状态过滤
     * @return 资讯列表
     */
    List<NewsFeed> selectByPublishedTimeRange(@Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime,
                                            @Param("status") Integer status);

    /**
     * 根据来源名称查询资讯
     *
     * @param sourceName 来源名称
     * @param status 状态过滤
     * @return 资讯列表
     */
    List<NewsFeed> selectBySourceName(@Param("sourceName") String sourceName,
                                    @Param("status") Integer status);

    /**
     * 根据作者查询资讯
     *
     * @param author 作者
     * @param status 状态过滤
     * @return 资讯列表
     */
    List<NewsFeed> selectByAuthor(@Param("author") String author,
                                @Param("status") Integer status);

    /**
     * 检查资讯是否存在（根据原文链接）
     *
     * @param sourceUrl 原文链接
     * @return 资讯信息
     */
    NewsFeed selectBySourceUrl(@Param("sourceUrl") String sourceUrl);

    /**
     * 获取资讯统计信息
     *
     * @return 统计信息Map
     */
    Map<String, Long> getNewsFeedStatistics();

    /**
     * 获取今日新增资讯数量
     *
     * @return 今日新增数量
     */
    Long getTodayNewsFeedCount();

    /**
     * 获取最新资讯列表
     *
     * @param limit 限制数量
     * @param status 状态过滤
     * @return 最新资讯列表
     */
    List<NewsFeed> selectLatestNewsFeed(@Param("limit") Integer limit,
                                      @Param("status") Integer status);

    /**
     * 获取热门资讯列表（按阅读量排序）
     *
     * @param limit 限制数量
     * @param status 状态过滤
     * @return 热门资讯列表
     */
    List<NewsFeed> selectPopularNewsFeed(@Param("limit") Integer limit,
                                       @Param("status") Integer status);

    /**
     * 复合条件查询资讯列表（用于分页查询）
     *
     * @param type 资讯类型
     * @param status 状态
     * @param aiReviewStatus AI审核状态
     * @param rssSourceId RSS源ID
     * @param sourceName 来源名称
     * @param author 作者
     * @param keyword 搜索关键词
     * @param publishedAtStart 发布开始时间
     * @param publishedAtEnd 发布结束时间
     * @return 资讯列表
     */
    List<NewsFeed> selectByComplexCondition(@Param("type") Integer type,
                                          @Param("status") Integer status,
                                          @Param("aiReviewStatus") Integer aiReviewStatus,
                                          @Param("rssSourceId") Long rssSourceId,
                                          @Param("sourceName") String sourceName,
                                          @Param("author") String author,
                                          @Param("keyword") String keyword,
                                          @Param("publishedAtStart") LocalDateTime publishedAtStart,
                                          @Param("publishedAtEnd") LocalDateTime publishedAtEnd);

    /**
     * 统计各状态资讯数量
     *
     * @return 状态统计Map
     */
    Map<String, Long> countByStatus();

    /**
     * 统计各AI审核状态资讯数量
     *
     * @return AI审核状态统计Map
     */
    Map<String, Long> countByAiReviewStatus();
}
