package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.KnowledgeLike;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 知识点赞表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface KnowledgeLikeMapper {

    /**
     * 插入一条知识点赞记录
     *
     * @param knowledgeLike 知识点赞实体
     * @return 影响的行数
     */
    int insert(KnowledgeLike knowledgeLike);

    /**
     * 根据ID删除知识点赞记录
     *
     * @param id 知识点赞ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据知识ID和用户ID删除点赞记录
     *
     * @param knowledgeId 知识ID
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteByKnowledgeIdAndUserId(@Param("knowledgeId") Long knowledgeId, @Param("userId") Long userId);

    /**
     * 根据ID查询知识点赞记录
     *
     * @param id 知识点赞ID
     * @return 知识点赞实体
     */
    KnowledgeLike selectById(@Param("id") Long id);

    /**
     * 根据知识ID查询点赞记录
     *
     * @param knowledgeId 知识ID
     * @return 知识点赞实体列表
     */
    List<KnowledgeLike> selectByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 根据用户ID查询点赞记录
     *
     * @param userId 用户ID
     * @return 知识点赞实体列表
     */
    List<KnowledgeLike> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询指定知识的点赞数量
     *
     * @param knowledgeId 知识ID
     * @return 点赞数量
     */
    int countByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 检查用户是否已经点赞了指定的知识
     *
     * @param knowledgeId 知识ID
     * @param userId 用户ID
     * @return 如果已点赞返回true，否则返回false
     */
    boolean existsByKnowledgeIdAndUserId(@Param("knowledgeId") Long knowledgeId, @Param("userId") Long userId);
}
