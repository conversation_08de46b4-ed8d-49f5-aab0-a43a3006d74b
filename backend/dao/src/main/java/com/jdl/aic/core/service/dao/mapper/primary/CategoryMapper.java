package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.Category;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 分类表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-17
 */
@Mapper
public interface CategoryMapper {

    /**
     * 插入分类
     *
     * @param category 分类信息
     * @return 影响行数
     */
    int insert(Category category);

    /**
     * 根据ID删除分类
     *
     * @param id 分类ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新分类信息
     *
     * @param category 分类信息
     * @return 影响行数
     */
    int updateById(Category category);

    /**
     * 根据ID查询分类
     *
     * @param id 分类ID
     * @return 分类信息
     */
    Category selectById(Long id);

    /**
     * 根据条件查询分类列表
     *
     * @param category 查询条件
     * @return 分类列表
     */
    List<Category> selectByCondition(Category category);

    /**
     * 根据父分类ID查询子分类列表
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    List<Category> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 根据内容类别查询分类列表
     *
     * @param contentCategory 内容类别
     * @param isActive 是否启用
     * @return 分类列表
     */
    List<Category> selectByContentCategory(@Param("contentCategory") String contentCategory, 
                                         @Param("isActive") Boolean isActive);

    /**
     * 查询根分类列表（parentId为null）
     *
     * @param contentCategory 内容类别过滤
     * @param isActive 是否启用
     * @return 根分类列表
     */
    List<Category> selectRootCategories(@Param("contentCategory") String contentCategory, @Param("subTypeId") Long subTypeId,
                                      @Param("isActive") Boolean isActive);

    /**
     * 根据名称和内容类别查询分类
     *
     * @param name 分类名称
     * @param contentCategory 内容类别
     * @param parentId 父分类ID
     * @return 分类信息
     */
    Category selectByNameAndCategory(@Param("name") String name, 
                                   @Param("contentCategory") String contentCategory,
                                   @Param("parentId") Long parentId);

    /**
     * 查询分类的子分类数量
     *
     * @param parentId 父分类ID
     * @return 子分类数量
     */
    int countByParentId(@Param("parentId") Long parentId);

    /**
     * 更新分类排序权重
     *
     * @param id 分类ID
     * @param sortOrder 排序权重
     * @return 影响行数
     */
    int updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);

    /**
     * 更新分类状态
     *
     * @param id 分类ID
     * @param isActive 是否启用
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);

    /**
     * 更新分类父级
     *
     * @param id 分类ID
     * @param newParentId 新父分类ID
     * @return 影响行数
     */
    int updateParent(@Param("id") Long id, @Param("newParentId") Long newParentId);

    /**
     * 搜索分类（根据名称和描述）
     *
     * @param keyword 搜索关键词
     * @param contentCategory 内容类别过滤
     * @param isActive 是否启用
     * @return 分类列表
     */
    List<Category> searchCategories(@Param("keyword") String keyword,
                                  @Param("contentCategory") String contentCategory,
                                  @Param("isActive") Boolean isActive);
}
