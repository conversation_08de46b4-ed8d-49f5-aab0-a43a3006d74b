package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.KnowledgeComment;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 知识评论表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface KnowledgeCommentMapper {

    /**
     * 插入一条知识评论记录
     *
     * @param knowledgeComment 知识评论实体
     * @return 影响的行数
     */
    int insert(KnowledgeComment knowledgeComment);

    /**
     * 根据ID更新知识评论记录
     *
     * @param knowledgeComment 知识评论实体
     * @return 影响的行数
     */
    int updateById(KnowledgeComment knowledgeComment);

    /**
     * 根据ID删除知识评论记录（软删除）
     *
     * @param id 知识评论ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询知识评论记录
     *
     * @param id 知识评论ID
     * @return 知识评论实体
     */
    KnowledgeComment selectById(@Param("id") Long id);

    /**
     * 根据知识ID查询评论记录
     *
     * @param knowledgeId 知识ID
     * @return 知识评论实体列表
     */
    List<KnowledgeComment> selectByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 根据父评论ID查询子评论
     *
     * @param parentId 父评论ID
     * @return 知识评论实体列表
     */
    List<KnowledgeComment> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 更新评论的点赞数
     *
     * @param id 评论ID
     * @param increment 增加的点赞数（可以为负数）
     * @return 影响的行数
     */
    int updateLikeCount(@Param("id") Long id, @Param("increment") int increment);

    /**
     * 根据作者ID查询评论
     *
     * @param authorId 作者ID
     * @return 知识评论实体列表
     */
    List<KnowledgeComment> selectByAuthorId(@Param("authorId") Long authorId);

    /**
     * 查询指定知识的评论数量
     *
     * @param knowledgeId 知识ID
     * @return 评论数量
     */
    int countByKnowledgeId(@Param("knowledgeId") Long knowledgeId);
}
