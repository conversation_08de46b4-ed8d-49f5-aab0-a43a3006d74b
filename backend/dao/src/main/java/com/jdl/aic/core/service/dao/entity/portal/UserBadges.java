package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户徽章关联实体类
 * 对应ai_community_portal.user_badges表
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Data
@TableName("user_badges")
public class UserBadges {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long user_id;
    
    /**
     * 徽章ID
     */
    private Long badge_id;
    
    /**
     * 获得时间
     */
    private LocalDateTime earned_at;
    
    /**
     * 创建时间
     */
    private LocalDateTime created_at;
    
    /**
     * 更新时间
     */
    private LocalDateTime updated_at;
    
    /**
     * 创建人ID
     */
    private Long created_by;
    
    /**
     * 软删除标志，0-未删除，1-已删除
     */
    private Integer deleted;
}
