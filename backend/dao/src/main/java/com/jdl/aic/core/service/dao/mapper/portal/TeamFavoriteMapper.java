package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.TeamFavorite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 团队收藏数据访问接口
 * 
 * <p>提供团队收藏相关的数据库操作，包括：
 * <ul>
 *   <li>基本CRUD操作</li>
 *   <li>用户团队收藏查询</li>
 *   <li>团队收藏统计</li>
 *   <li>团队收藏状态管理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface TeamFavoriteMapper {

    // ==================== 基本CRUD操作 ====================

    /**
     * 插入团队收藏记录
     *
     * @param teamFavorite 团队收藏信息
     * @return 影响行数
     */
    int insert(TeamFavorite teamFavorite);

    /**
     * 根据ID删除团队收藏记录（物理删除）
     *
     * @param id 收藏ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 软删除团队收藏记录（标记为已删除）
     *
     * @param id 收藏ID
     * @return 影响行数
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 根据用户ID和团队ID软删除团队收藏记录
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int softDeleteByUserIdAndTeamId(@Param("userId") Long userId, @Param("teamId") Long teamId);

    /**
     * 根据ID查询团队收藏记录
     *
     * @param id 收藏ID
     * @return 团队收藏信息
     */
    TeamFavorite selectById(@Param("id") Long id);

    /**
     * 根据用户ID和团队ID查询团队收藏记录
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 团队收藏信息
     */
    TeamFavorite selectByUserIdAndTeamId(@Param("userId") Long userId, @Param("teamId") Long teamId);

    // ==================== 用户团队收藏查询 ====================

    /**
     * 根据用户ID查询团队收藏列表
     *
     * @param userId 用户ID
     * @return 团队收藏列表
     */
    List<TeamFavorite> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询团队收藏列表（包含团队信息）
     *
     * @param userId 用户ID
     * @return 团队收藏列表（包含团队信息）
     */
    List<TeamFavorite> selectByUserIdWithTeamInfo(@Param("userId") Long userId);

    /**
     * 根据条件查询用户团队收藏列表
     *
     * @param userId 用户ID
     * @param teamName 团队名称（模糊搜索）
     * @param teamPrivacy 团队隐私设置
     * @param teamIsActive 团队是否活跃
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 团队收藏列表
     */
    List<TeamFavorite> selectByCondition(@Param("userId") Long userId,
                                        @Param("teamName") String teamName,
                                        @Param("teamPrivacy") String teamPrivacy,
                                        @Param("teamIsActive") Boolean teamIsActive,
                                        @Param("startDate") String startDate,
                                        @Param("endDate") String endDate);

    /**
     * 获取用户最近收藏的团队
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近收藏团队列表
     */
    List<TeamFavorite> selectRecentByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    // ==================== 团队收藏查询 ====================

    /**
     * 根据团队ID查询收藏用户列表
     *
     * @param teamId 团队ID
     * @return 收藏用户列表
     */
    List<TeamFavorite> selectByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据团队ID查询收藏用户列表（限制数量）
     *
     * @param teamId 团队ID
     * @param limit 限制数量
     * @return 收藏用户列表
     */
    List<TeamFavorite> selectByTeamIdWithLimit(@Param("teamId") Long teamId, @Param("limit") Integer limit);

    // ==================== 统计查询 ====================

    /**
     * 统计用户收藏的团队数量
     *
     * @param userId 用户ID
     * @return 收藏团队数量
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 统计团队被收藏的次数
     *
     * @param teamId 团队ID
     * @return 被收藏次数
     */
    int countByTeamId(@Param("teamId") Long teamId);

    /**
     * 批量统计团队被收藏次数
     *
     * @param teamIds 团队ID列表
     * @return 团队收藏次数列表
     */
    List<Object> countByTeamIds(@Param("teamIds") List<Long> teamIds);

    /**
     * 获取热门收藏团队列表
     *
     * @param limit 限制数量
     * @param days 统计天数（可选）
     * @return 热门团队列表
     */
    List<Object> selectPopularTeams(@Param("limit") Integer limit, @Param("days") Integer days);

    // ==================== 状态检查 ====================

    /**
     * 检查团队收藏记录是否存在（不包括已删除的）
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return true表示存在，false表示不存在
     */
    boolean existsByUserIdAndTeamId(@Param("userId") Long userId, @Param("teamId") Long teamId);

    /**
     * 检查团队收藏记录是否存在（包括已删除的）
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return true表示存在，false表示不存在
     */
    boolean existsByUserIdAndTeamIdIncludeDeleted(@Param("userId") Long userId, @Param("teamId") Long teamId);

    // ==================== 批量操作 ====================

    /**
     * 批量插入团队收藏记录
     *
     * @param teamFavorites 团队收藏列表
     * @return 影响行数
     */
    int batchInsert(@Param("teamFavorites") List<TeamFavorite> teamFavorites);

    /**
     * 批量删除团队收藏记录
     *
     * @param ids 收藏ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 批量软删除团队收藏记录
     *
     * @param ids 收藏ID列表
     * @return 影响行数
     */
    int batchSoftDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 批量软删除团队收藏记录（根据用户ID和团队ID）
     *
     * @param userId 用户ID
     * @param teamIds 团队ID列表
     * @return 影响行数
     */
    int batchSoftDeleteByUserIdAndTeamIds(@Param("userId") Long userId, @Param("teamIds") List<Long> teamIds);

    // ==================== 状态管理 ====================

    /**
     * 恢复已删除的团队收藏记录
     *
     * @param id 收藏ID
     * @return 影响行数
     */
    int restoreById(@Param("id") Long id);

    /**
     * 清理指定天数前的已删除团队收藏记录
     *
     * @param days 天数
     * @return 影响行数
     */
    int cleanupDeletedRecords(@Param("days") Integer days);

    /**
     * 清理指定用户的已删除团队收藏记录
     *
     * @param userId 用户ID
     * @param days 天数
     * @return 影响行数
     */
    int cleanupUserDeletedRecords(@Param("userId") Long userId, @Param("days") Integer days);

    // ==================== 高级查询 ====================

    /**
     * 获取团队收藏趋势数据
     *
     * @param userId 用户ID
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Object> selectUserTeamFavoriteTrend(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 获取用户团队收藏偏好数据
     *
     * @param userId 用户ID
     * @return 偏好数据
     */
    List<Object> selectUserTeamFavoritePreference(@Param("userId") Long userId);

    /**
     * 根据时间范围查询团队收藏列表
     *
     * @param userId 用户ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 团队收藏列表
     */
    List<TeamFavorite> selectByDateRange(@Param("userId") Long userId,
                                        @Param("startDate") String startDate,
                                        @Param("endDate") String endDate);
}
