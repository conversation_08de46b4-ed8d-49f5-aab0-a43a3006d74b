package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.UserFollow;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户关注数据访问接口
 * 
 * <p>提供用户关注相关的数据库操作，包括：
 * <ul>
 *   <li>基本CRUD操作</li>
 *   <li>用户关注查询</li>
 *   <li>关注统计</li>
 *   <li>关注状态管理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface UserFollowMapper {

    // ==================== 基本CRUD操作 ====================

    /**
     * 插入用户关注记录
     *
     * @param userFollow 用户关注信息
     * @return 影响行数
     */
    int insert(UserFollow userFollow);

    /**
     * 根据ID删除用户关注记录（物理删除）
     *
     * @param id 关注ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据用户ID和被关注人ID删除关注记录
     *
     * @param userId 用户ID
     * @param followedId 被关注人ID
     * @return 影响行数
     */
    int deleteByUserIdAndFollowedId(@Param("userId") Long userId, @Param("followedId") Long followedId);

    /**
     * 更新用户关注记录
     *
     * @param userFollow 用户关注信息
     * @return 影响行数
     */
    int updateById(UserFollow userFollow);

    /**
     * 根据ID查询用户关注记录
     *
     * @param id 关注ID
     * @return 用户关注信息
     */
    UserFollow selectById(@Param("id") Long id);

    /**
     * 根据用户ID和被关注人ID查询关注记录
     *
     * @param userId 用户ID
     * @param followedId 被关注人ID
     * @return 用户关注信息
     */
    UserFollow selectByUserIdAndFollowedId(@Param("userId") Long userId, @Param("followedId") Long followedId);

    // ==================== 用户关注查询 ====================

    /**
     * 根据用户ID查询关注列表（用户关注的人）
     *
     * @param userId 用户ID
     * @return 关注列表
     */
    List<UserFollow> selectFollowingByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询关注列表（包含被关注人信息）
     *
     * @param userId 用户ID
     * @return 关注列表（包含被关注人信息）
     */
    List<UserFollow> selectFollowingByUserIdWithUserInfo(@Param("userId") Long userId);

    /**
     * 根据被关注人ID查询粉丝列表
     *
     * @param followedId 被关注人ID
     * @return 粉丝列表
     */
    List<UserFollow> selectFollowersByFollowedId(@Param("followedId") Long followedId);

    /**
     * 根据被关注人ID查询粉丝列表（包含关注者信息）
     *
     * @param followedId 被关注人ID
     * @return 粉丝列表（包含关注者信息）
     */
    List<UserFollow> selectFollowersByFollowedIdWithUserInfo(@Param("followedId") Long followedId);

    /**
     * 根据条件查询用户关注列表
     *
     * @param userId 用户ID
     * @param followType 关注类型（following:关注的人, followers:粉丝）
     * @param followedUsername 被关注人用户名（模糊搜索）
     * @param followedDisplayName 被关注人显示名称（模糊搜索）
     * @param followedDepartment 被关注人部门
     * @param followedIsActive 被关注人是否活跃
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 关注列表
     */
    List<UserFollow> selectByCondition(@Param("userId") Long userId,
                                      @Param("followType") String followType,
                                      @Param("followedUsername") String followedUsername,
                                      @Param("followedDisplayName") String followedDisplayName,
                                      @Param("followedDepartment") String followedDepartment,
                                      @Param("followedIsActive") Boolean followedIsActive,
                                      @Param("startDate") String startDate,
                                      @Param("endDate") String endDate);

    /**
     * 获取用户最近关注的人
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 最近关注的人列表
     */
    List<UserFollow> selectRecentFollowingByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取用户最近的粉丝
     *
     * @param followedId 被关注人ID
     * @param limit 限制数量
     * @return 最近粉丝列表
     */
    List<UserFollow> selectRecentFollowersByFollowedId(@Param("followedId") Long followedId, @Param("limit") Integer limit);

    // ==================== 统计查询 ====================

    /**
     * 统计用户关注的人数
     *
     * @param userId 用户ID
     * @return 关注人数
     */
    int countFollowingByUserId(@Param("userId") Long userId);

    /**
     * 统计用户的粉丝数
     *
     * @param followedId 被关注人ID
     * @return 粉丝数
     */
    int countFollowersByFollowedId(@Param("followedId") Long followedId);

    /**
     * 批量统计用户关注信息
     *
     * @param userIds 用户ID列表
     * @return 用户关注统计列表
     */
    List<Object> countFollowStatsBatch(@Param("userIds") List<Long> userIds);

    /**
     * 获取热门被关注用户列表
     *
     * @param limit 限制数量
     * @param days 统计天数（可选）
     * @return 热门用户列表
     */
    List<Object> selectPopularUsers(@Param("limit") Integer limit, @Param("days") Integer days);

    // ==================== 状态检查 ====================

    /**
     * 检查用户关注记录是否存在
     *
     * @param userId 用户ID
     * @param followedId 被关注人ID
     * @return true表示存在，false表示不存在
     */
    boolean existsByUserIdAndFollowedId(@Param("userId") Long userId, @Param("followedId") Long followedId);

    /**
     * 检查是否存在相互关注
     *
     * @param userId1 用户1ID
     * @param userId2 用户2ID
     * @return true表示相互关注，false表示不是
     */
    boolean existsMutualFollow(@Param("userId1") Long userId1, @Param("userId2") Long userId2);

    // ==================== 批量操作 ====================

    /**
     * 批量插入用户关注记录
     *
     * @param userFollows 用户关注列表
     * @return 影响行数
     */
    int batchInsert(@Param("userFollows") List<UserFollow> userFollows);

    /**
     * 批量删除用户关注记录
     *
     * @param ids 关注ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 批量删除用户关注记录（根据用户ID和被关注人ID）
     *
     * @param userId 用户ID
     * @param followedIds 被关注人ID列表
     * @return 影响行数
     */
    int batchDeleteByUserIdAndFollowedIds(@Param("userId") Long userId, @Param("followedIds") List<Long> followedIds);

    // ==================== 高级查询 ====================

    /**
     * 获取共同关注的用户
     *
     * @param userId1 用户1ID
     * @param userId2 用户2ID
     * @return 共同关注的用户列表
     */
    List<UserFollow> selectMutualFollows(@Param("userId1") Long userId1, @Param("userId2") Long userId2);

    /**
     * 获取用户关注趋势数据
     *
     * @param userId 用户ID
     * @param days 统计天数
     * @return 趋势数据
     */
    List<Object> selectUserFollowTrend(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 获取用户关注偏好数据
     *
     * @param userId 用户ID
     * @return 偏好数据
     */
    List<Object> selectUserFollowPreference(@Param("userId") Long userId);

    /**
     * 根据部门推荐用户
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐用户列表
     */
    List<Object> selectRecommendedUsersByDepartment(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 基于共同关注推荐用户
     *
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐用户列表
     */
    List<Object> selectRecommendedUsersByMutualFollows(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 获取可能认识的人
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 可能认识的人列表
     */
    List<Object> selectPeopleYouMayKnow(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 根据时间范围查询关注列表
     *
     * @param userId 用户ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 关注列表
     */
    List<UserFollow> selectByDateRange(@Param("userId") Long userId,
                                      @Param("startDate") String startDate,
                                      @Param("endDate") String endDate);

    /**
     * 获取关注活跃度统计
     *
     * @param userId 用户ID
     * @param days 统计天数
     * @return 活跃度统计数据
     */
    List<Object> selectFollowActivityStats(@Param("userId") Long userId, @Param("days") Integer days);

    /**
     * 清理无效的关注关系（被关注人已删除或不活跃）
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int cleanupInvalidFollows(@Param("userId") Long userId);
}
