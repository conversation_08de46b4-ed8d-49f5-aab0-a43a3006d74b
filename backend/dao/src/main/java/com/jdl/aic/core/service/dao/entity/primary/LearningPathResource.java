package com.jdl.aic.core.service.dao.entity.primary;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 学习路径资源关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("learning_path_resource")
public class LearningPathResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 学习路径资源ID
     */
    private Long learningPathId;

    /**
     * 关联的学习资源ID
     */
    private Long resourceId;

    /**
     * 在路径中的顺序
     */
    private Integer sequenceOrder;

    /**
     * 阶段名称（如：第一阶段、第二阶段）
     */
    private String stageName;

    /**
     * 预估学习时长（小时）
     */
    private BigDecimal estimatedHours;

    /**
     * 是否可选
     */
    private Boolean isOptional;

    /**
     * 学习建议
     */
    private String notes;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
