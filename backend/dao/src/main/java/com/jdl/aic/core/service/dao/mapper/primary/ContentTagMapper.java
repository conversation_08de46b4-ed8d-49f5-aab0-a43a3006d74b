package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentTag;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentTagMapper {

    /**
     * 插入一条内容标签记录
     *
     * @param contentTag 内容标签实体
     * @return 影响的行数
     */
    int insert(ContentTag contentTag);

    /**
     * 根据ID删除内容标签记录
     *
     * @param id 内容标签ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 更新内容标签记录
     *
     * @param contentTag 内容标签实体
     * @return 影响的行数
     */
    int update(ContentTag contentTag);

    /**
     * 根据ID查询内容标签
     *
     * @param id 内容标签ID
     * @return 内容标签实体
     */
    ContentTag selectById(@Param("id") Long id);

    /**
     * 查询所有内容标签
     *
     * @return 内容标签实体列表
     */
    List<ContentTag> selectAll();

    /**
     * 根据名称查询内容标签
     *
     * @param name 标签名称
     * @return 内容标签实体
     */
    ContentTag selectByName(@Param("name") String name);

    /**
     * 根据状态查询标签
     *
     * @param status 标签状态
     * @return 内容标签实体列表
     */
    List<ContentTag> selectByStatus(@Param("status") Integer status);

    /**
     * 更新标签状态
     *
     * @param id 标签ID
     * @param status 新状态
     * @return 影响的行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 增加标签使用次数
     *
     * @param id 标签ID
     * @param increment 增加的次数
     * @return 影响的行数
     */
    int incrementUseCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 查询使用次数最多的标签
     *
     * @param limit 限制数量
     * @return 内容标签实体列表
     */
    List<ContentTag> selectMostUsedTags(@Param("limit") Integer limit);
}
