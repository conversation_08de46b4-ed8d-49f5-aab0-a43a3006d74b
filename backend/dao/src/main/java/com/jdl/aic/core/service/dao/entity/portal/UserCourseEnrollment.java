package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户课程报名表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_course_enrollment")
public class UserCourseEnrollment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 报名状态（ENROLLED:已报名, IN_PROGRESS:学习中, COMPLETED:已完成, DROPPED:已退出）
     */
    private String enrollmentStatus;

    /**
     * 报名时间
     */
    private LocalDateTime enrolledAt;

    /**
     * 完成时间
     */
    private LocalDateTime completedAt;

    /**
     * 学习进度百分比（0.00-100.00）
     */
    private BigDecimal progressPercentage;

    /**
     * 已完成阶段数
     */
    private Integer completedStages;

    /**
     * 总阶段数
     */
    private Integer totalStages;

    /**
     * 学习时长（小时）
     */
    private BigDecimal studyHours;

    /**
     * 最后学习时间
     */
    private LocalDateTime lastStudyAt;

    /**
     * 报名来源（WEB:网页, MOBILE:移动端, API:接口）
     */
    private String enrollmentSource;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;
}
