package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户数据访问接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface UserMapper {

    // ==================== 基本CRUD操作 ====================

    /**
     * 插入用户
     *
     * @param user 用户信息
     * @return 影响行数
     */
    int insert(User user);

    /**
     * 根据ID删除用户（软删除）
     *
     * @param id 用户ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 更新用户信息
     *
     * @param user 用户信息
     * @return 影响行数
     */
    int updateById(User user);

    /**
     * 根据ID查询用户
     *
     * @param id 用户ID
     * @return 用户信息
     */
    User selectById(@Param("id") Long id);

    // ==================== 条件查询 ====================

    /**
     * 根据SSO ID查询用户
     *
     * @param ssoId SSO唯一标识
     * @return 用户信息
     */
    User selectBySsoId(@Param("ssoId") String ssoId);

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    User selectByEmail(@Param("email") String email);

    /**
     * 根据条件查询用户列表（分页）
     *
     * @param department 部门
     * @param isActive 是否活跃
     * @param search 搜索关键词
     * @return 用户列表
     */
    List<User> selectByCondition(@Param("department") String department, @Param("isActive") Boolean isActive, @Param("search") String search);

    /**
     * 根据用户实体条件查询用户列表（分页）
     *
     * @param user 查询条件
     * @return 用户列表
     */
    List<User> selectByUserCondition(@Param("user") User user);

    /**
     * 根据用户ID列表查询用户
     *
     * @param userIds 用户ID列表
     * @return 用户列表
     */
    List<User> selectByIds(@Param("userIds") List<Long> userIds);

    // ==================== 状态管理 ====================

    /**
     * 启用/禁用用户
     *
     * @param id 用户ID
     * @param isActive 是否启用
     * @return 影响行数
     */
    int toggleUserStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);

    /**
     * 更新用户最后登录时间
     *
     * @param id 用户ID
     * @param lastLoginAt 最后登录时间
     * @return 影响行数
     */
    int updateLastLoginTime(@Param("id") Long id, @Param("lastLoginAt") LocalDateTime lastLoginAt);

    // ==================== 批量操作 ====================

    /**
     * 批量插入用户
     *
     * @param users 用户列表
     * @return 影响行数
     */
    int batchInsert(@Param("users") List<User> users);

    /**
     * 批量更新用户状态
     *
     * @param userIds 用户ID列表
     * @param isActive 是否启用
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("userIds") List<Long> userIds, @Param("isActive") Boolean isActive);

    // ==================== 统计查询 ====================

    /**
     * 统计用户总数
     *
     * @param department 部门
     * @param isActive 是否活跃
     * @param search 搜索关键词
     * @return 用户总数
     */
    int countByCondition(@Param("department") String department, @Param("isActive") Boolean isActive, @Param("search") String search);

    /**
     * 统计活跃用户数
     *
     * @return 活跃用户数
     */
    int countActiveUsers();

    /**
     * 根据部门统计用户数
     *
     * @param department 部门
     * @return 用户数
     */
    int countByDepartment(@Param("department") String department);

    // ==================== 搜索功能 ====================

    /**
     * 搜索用户（根据用户名、显示名称、邮箱模糊匹配）
     *
     * @param keyword 搜索关键词
     * @param isActive 是否只查询活跃用户
     * @return 用户列表
     */
    List<User> searchUsers(@Param("keyword") String keyword, @Param("isActive") Boolean isActive);

    // ==================== 验证方法 ====================

    /**
     * 检查SSO ID是否存在
     *
     * @param ssoId SSO唯一标识
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsBySsoId(@Param("ssoId") String ssoId, @Param("excludeId") Long excludeId);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByUsername(@Param("username") String username, @Param("excludeId") Long excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByEmail(@Param("email") String email, @Param("excludeId") Long excludeId);
}
