package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.KnowledgeTagRelation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 知识-标签关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface KnowledgeTagRelationMapper {

    /**
     * 插入一条知识-标签关联记录
     *
     * @param knowledgeTagRelation 知识-标签关联实体
     * @return 影响的行数
     */
    int insert(KnowledgeTagRelation knowledgeTagRelation);

    /**
     * 根据ID更新知识-标签关联记录
     *
     * @param knowledgeTagRelation 知识-标签关联实体
     * @return 影响的行数
     */
    int updateById(KnowledgeTagRelation knowledgeTagRelation);

    /**
     * 根据ID删除知识-标签关联记录
     *
     * @param id 知识-标签关联ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询知识-标签关联记录
     *
     * @param id 知识-标签关联ID
     * @return 知识-标签关联实体
     */
    KnowledgeTagRelation selectById(@Param("id") Long id);

    /**
     * 根据知识ID查询关联的标签记录
     *
     * @param knowledgeId 知识ID
     * @return 知识-标签关联实体列表
     */
    List<KnowledgeTagRelation> selectByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 根据标签ID查询关联的知识记录
     *
     * @param tagId 标签ID
     * @return 知识-标签关联实体列表
     */
    List<KnowledgeTagRelation> selectByTagId(@Param("tagId") Long tagId);

    /**
     * 批量插入知识-标签关联记录
     *
     * @param knowledgeTagRelationList 知识-标签关联实体列表
     * @return 影响的行数
     */
    int batchInsert(@Param("list") List<KnowledgeTagRelation> knowledgeTagRelationList);

    /**
     * 根据知识ID删除所有关联记录
     *
     * @param knowledgeId 知识ID
     * @return 影响的行数
     */
    int deleteByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 根据标签ID删除所有关联记录
     *
     * @param tagId 标签ID
     * @return 影响的行数
     */
    int deleteByTagId(@Param("tagId") Long tagId);
}
