package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.FileStorage;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface FileStorageMapper {
    int insert(FileStorage record);
    int updateByPrimaryKey(FileStorage record);
    int deleteByPrimaryKey(Long id);
    FileStorage selectByPrimaryKey(Long id);
    List<FileStorage> selectAll();
}
