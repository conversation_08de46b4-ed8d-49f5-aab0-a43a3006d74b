package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 内容标签关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@TableName("content_tag_relation")
public class ContentTagRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 内容ID
     */
    private Long contentId;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
