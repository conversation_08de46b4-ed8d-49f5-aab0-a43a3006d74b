package com.jdl.aic.core.service.dao.entity.primary;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
public class FileStorage {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String fileName;
    private String fileType;
    private String filePath;
    private Long fileSize;
    private String md5;
    private Byte storageType;
    private String bucketName;
    private String objectKey;
    private Long ownerId;
    private String ownerName;
    private Byte status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime deletedAt;

    // Getters and setters
    // ... (省略getter和setter方法，实际使用时需要添加)
}
