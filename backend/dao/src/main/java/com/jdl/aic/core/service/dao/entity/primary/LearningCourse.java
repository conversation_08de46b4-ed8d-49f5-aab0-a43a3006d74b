package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 学习课程表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("learning_course")
public class LearningCourse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 课程名称
     */
    private String name;

    /**
     * 课程描述
     */
    private String description;

    /**
     * 课程分类
     */
    private String category;

    /**
     * 难度级别（BEGINNER:初级, INTERMEDIATE:中级, ADVANCED:高级, EXPERT:专家级）
     */
    private String difficultyLevel;

    /**
     * 预估总学习时长（小时）
     */
    private BigDecimal totalHours;

    /**
     * 包含资源数量
     */
    private Integer resourceCount;

    /**
     * 报名学习人数
     */
    private Integer enrolledCount;

    /**
     * 完成人数
     */
    private Integer completionCount;

    /**
     * 平均完成率
     */
    private BigDecimal completionRate;

    /**
     * 前置知识要求
     */
    private String prerequisites;

    /**
     * 学习目标
     */
    private String learningGoals;

    /**
     * 创建者ID（跨库引用，无外键约束）
     */
    private String creatorId;

    /**
     * 创建者姓名
     */
    private String creatorName;

    /**
     * 课程状态（DRAFT:草稿, PUBLISHED:已发布, ARCHIVED:已归档）
     */
    private String status;

    /**
     * 是否官方课程（0:否, 1:是）
     */
    private Boolean isOfficial;

    /**
     * 封面图片URL
     */
    private String coverImageUrl;

    /**
     * 标签列表，逗号分隔
     */
    private String tags;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 最后更新用户ID
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;
}
