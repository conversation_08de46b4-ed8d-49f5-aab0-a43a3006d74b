package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 分享选项配置表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("share_option_config")
public class ShareOptionConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内容类型编码
     */
    private String contentType;

    /**
     * 分享类型：internal,wechat,email,link_copy,teams,slack
     */
    private String shareType;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 图标URL
     */
    private String iconUrl;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 分享配置JSON
     */
    private String configJson;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建人（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 更新人（跨库引用，无外键约束）
     */
    private String updatedBy;
}
