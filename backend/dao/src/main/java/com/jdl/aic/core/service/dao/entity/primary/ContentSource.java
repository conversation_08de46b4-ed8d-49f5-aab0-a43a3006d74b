package com.jdl.aic.core.service.dao.entity.primary;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ContentSource {
    private Long id;
    private String name;
    private String description;
    private String url;
    private Byte type;
    private Long ownerId;
    private String ownerName;
    private Byte status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime deletedAt;
}
