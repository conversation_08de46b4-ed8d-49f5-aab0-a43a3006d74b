package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentFavorite;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容收藏表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentFavoriteMapper {

    /**
     * 插入一条内容收藏记录
     *
     * @param contentFavorite 内容收藏实体
     * @return 影响的行数
     */
    int insert(ContentFavorite contentFavorite);

    /**
     * 根据ID删除内容收藏记录
     *
     * @param id 内容收藏ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据内容ID和用户ID删除内容收藏记录
     *
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteByContentIdAndUserId(@Param("contentId") Long contentId, @Param("userId") Long userId);

    /**
     * 更新内容收藏记录
     *
     * @param contentFavorite 内容收藏实体
     * @return 影响的行数
     */
    int update(ContentFavorite contentFavorite);

    /**
     * 根据ID查询内容收藏
     *
     * @param id 内容收藏ID
     * @return 内容收藏实体
     */
    ContentFavorite selectById(@Param("id") Long id);

    /**
     * 根据内容ID查询内容收藏列表
     *
     * @param contentId 内容ID
     * @return 内容收藏实体列表
     */
    List<ContentFavorite> selectByContentId(@Param("contentId") Long contentId);

    /**
     * 根据用户ID查询内容收藏列表
     *
     * @param userId 用户ID
     * @return 内容收藏实体列表
     */
    List<ContentFavorite> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询所有内容收藏
     *
     * @return 内容收藏实体列表
     */
    List<ContentFavorite> selectAll();

    /**
     * 统计内容的收藏数
     *
     * @param contentId 内容ID
     * @return 收藏数
     */
    int countByContentId(@Param("contentId") Long contentId);

    /**
     * 检查用户是否已收藏内容
     *
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 收藏记录数（0或1）
     */
    int checkUserFavorited(@Param("contentId") Long contentId, @Param("userId") Long userId);
}
