package com.jdl.aic.core.service.dao.entity.primary;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 知识类型表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("knowledge_type")
public class KnowledgeType implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 知识类型名称，如"提示词"
     */
    private String name;

    /**
     * 知识类型唯一编码，如"PROMPT"
     */
    private String code;

    /**
     * 知识类型描述
     */
    private String description;

    /**
     * 知识类型图标 URL
     */
    private String iconUrl;

    /**
     * 是否启用此知识类型（0:否, 1:是）
     */
    private Boolean isActive;

    /**
     * 社区配置JSON，存储社区相关的配置信息
     */
    private String communityConfigJson;

    /**
     * 渲染配置JSON，存储前端渲染相关的配置信息
     */
    private String renderConfigJson;

    /**
     * 元数据JSON Schema定义，用于定义该知识类型的元数据结构
     */
    private String metadataSchema;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 最后更新用户ID（跨库引用，无外键约束）
     */
    private String updatedBy;
}
