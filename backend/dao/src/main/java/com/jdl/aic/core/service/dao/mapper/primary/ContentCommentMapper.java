package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentComment;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容评论表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentCommentMapper {

    /**
     * 插入一条内容评论记录
     *
     * @param contentComment 内容评论实体
     * @return 影响的行数
     */
    int insert(ContentComment contentComment);

    /**
     * 根据ID删除内容评论记录
     *
     * @param id 内容评论ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 更新内容评论记录
     *
     * @param contentComment 内容评论实体
     * @return 影响的行数
     */
    int update(ContentComment contentComment);

    /**
     * 根据ID查询内容评论
     *
     * @param id 内容评论ID
     * @return 内容评论实体
     */
    ContentComment selectById(@Param("id") Long id);

    /**
     * 根据内容ID查询内容评论列表
     *
     * @param contentId 内容ID
     * @return 内容评论实体列表
     */
    List<ContentComment> selectByContentId(@Param("contentId") Long contentId);

    /**
     * 根据用户ID查询内容评论列表
     *
     * @param userId 用户ID
     * @return 内容评论实体列表
     */
    List<ContentComment> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询所有内容评论
     *
     * @return 内容评论实体列表
     */
    List<ContentComment> selectAll();

    /**
     * 统计内容的评论数
     *
     * @param contentId 内容ID
     * @return 评论数
     */
    int countByContentId(@Param("contentId") Long contentId);

    /**
     * 根据父评论ID查询子评论列表
     *
     * @param parentId 父评论ID
     * @return 内容评论实体列表
     */
    List<ContentComment> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 更新评论点赞数
     *
     * @param id 评论ID
     * @param likeCount 新的点赞数
     * @return 影响的行数
     */
    int updateLikeCount(@Param("id") Long id, @Param("likeCount") Integer likeCount);
}
