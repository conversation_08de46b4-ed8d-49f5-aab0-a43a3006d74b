package com.jdl.aic.core.service.dao.entity.primary;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 内容分类关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
*/

@Data
public class ContentCategoryRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 内容ID
     */
    private Long contentId;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 内容类型（knowledge, solution, news_feed, learning_resource等）
     */
    private String contentType;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
