package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容分类关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentCategoryRelationMapper {

    /**
     * 插入一条内容分类关系记录
     *
     * @param contentCategoryRelation 内容分类关系实体
     * @return 影响的行数
     */
    int insert(ContentCategoryRelation contentCategoryRelation);

    /**
     * 根据ID删除内容分类关系记录
     *
     * @param id 内容分类关系ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据内容ID和分类ID删除内容分类关系记录
     *
     * @param contentId 内容ID
     * @param categoryId 分类ID
     * @return 影响的行数
     */
    int deleteByContentIdAndCategoryId(@Param("contentId") Long contentId, @Param("categoryId") Long categoryId);

    /**
     * 更新内容分类关系记录
     *
     * @param contentCategoryRelation 内容分类关系实体
     * @return 影响的行数
     */
    int update(ContentCategoryRelation contentCategoryRelation);

    /**
     * 根据ID查询内容分类关系
     *
     * @param id 内容分类关系ID
     * @return 内容分类关系实体
     */
    ContentCategoryRelation selectById(@Param("id") Long id);

    /**
     * 根据内容ID查询内容分类关系列表
     *
     * @param contentId 内容ID
     * @return 内容分类关系实体列表
     */
    List<ContentCategoryRelation> selectByContentId(@Param("contentId") Long contentId);

    /**
     * 根据分类ID查询内容分类关系列表
     *
     * @param categoryId 分类ID
     * @return 内容分类关系实体列表
     */
    List<ContentCategoryRelation> selectByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 查询所有内容分类关系
     *
     * @return 内容分类关系实体列表
     */
    List<ContentCategoryRelation> selectAll();

    /**
     * 批量插入内容分类关系记录
     *
     * @param contentCategoryRelations 内容分类关系实体列表
     * @return 影响的行数
     */
    int batchInsert(@Param("list") List<ContentCategoryRelation> contentCategoryRelations);

    /**
     * 批量删除内容分类关系记录
     *
     * @param ids ID列表
     * @return 影响的行数
     */
    int batchDelete(@Param("list") List<Long> ids);

    /**
     * 根据内容ID和内容类型查询分类关系列表
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 内容分类关系实体列表
     */
    List<ContentCategoryRelation> selectByContentIdAndType(@Param("contentId") Long contentId, @Param("contentType") String contentType);

    /**
     * 根据内容ID和内容类型删除分类关系记录
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 影响的行数
     */
    int deleteByContentIdAndType(@Param("contentId") Long contentId, @Param("contentType") String contentType);
}
