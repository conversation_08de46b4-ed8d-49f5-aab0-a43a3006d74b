package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.RssSource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * RSS源表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-19
 */
@Mapper
public interface RssSourceMapper {

    /**
     * 插入RSS源
     *
     * @param rssSource RSS源信息
     * @return 影响行数
     */
    int insert(RssSource rssSource);

    /**
     * 根据ID删除RSS源（软删除）
     *
     * @param id RSS源ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新RSS源信息
     *
     * @param rssSource RSS源信息
     * @return 影响行数
     */
    int updateById(RssSource rssSource);

    /**
     * 根据ID查询RSS源
     *
     * @param id RSS源ID
     * @return RSS源信息
     */
    RssSource selectById(Long id);

    /**
     * 根据条件查询RSS源列表
     *
     * @param rssSource 查询条件
     * @return RSS源列表
     */
    List<RssSource> selectByCondition(RssSource rssSource);

    /**
     * 根据分类查询RSS源列表
     *
     * @param category 分类
     * @param status 状态过滤
     * @param type 类型过滤
     * @return RSS源列表
     */
    List<RssSource> selectByCategory(@Param("category") String category,
                                   @Param("status") Integer status,
                                   @Param("type") Integer type);

    /**
     * 根据所有者ID查询RSS源列表
     *
     * @param ownerId 所有者ID
     * @param status 状态过滤
     * @return RSS源列表
     */
    List<RssSource> selectByOwnerId(@Param("ownerId") String ownerId,
                                  @Param("status") Integer status);

    /**
     * 根据状态查询RSS源列表
     *
     * @param status 状态
     * @return RSS源列表
     */
    List<RssSource> selectByStatus(@Param("status") Integer status);

    /**
     * 根据类型查询RSS源列表
     *
     * @param type 类型
     * @param status 状态过滤
     * @return RSS源列表
     */
    List<RssSource> selectByType(@Param("type") Integer type,
                                @Param("status") Integer status);

    /**
     * 根据RSS订阅地址查询RSS源
     *
     * @param feedUrl RSS订阅地址
     * @param excludeId 排除的ID（用于更新时检查重复）
     * @return RSS源信息
     */
    RssSource selectByFeedUrl(@Param("feedUrl") String feedUrl,
                            @Param("excludeId") Long excludeId);

    /**
     * 更新RSS源状态
     *
     * @param id RSS源ID
     * @param status 新状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新RSS源最后抓取时间
     *
     * @param id RSS源ID
     * @param lastFetchedAt 最后抓取时间
     * @return 影响行数
     */
    int updateLastFetchedTime(@Param("id") Long id,
                            @Param("lastFetchedAt") LocalDateTime lastFetchedAt);

    /**
     * 批量更新RSS源状态
     *
     * @param ids RSS源ID列表
     * @param status 新状态
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 搜索RSS源（根据名称和描述）
     *
     * @param keyword 搜索关键词
     * @param category 分类过滤
     * @param type 类型过滤
     * @param status 状态过滤
     * @return RSS源列表
     */
    List<RssSource> searchRssSources(@Param("keyword") String keyword,
                                   @Param("category") String category,
                                   @Param("type") Integer type,
                                   @Param("status") Integer status);

    /**
     * 统计RSS源数量（按状态）
     *
     * @return 统计结果Map，key为状态，value为数量
     */
    List<StatusCount> countByStatus();

    /**
     * 统计RSS源数量（按类型）
     *
     * @return 统计结果Map，key为类型，value为数量
     */
    List<TypeCount> countByType();

    /**
     * 获取总数量
     *
     * @return 总数量
     */
    Long countTotal();

    /**
     * 状态统计结果类
     */
    class StatusCount {
        private Integer status;
        private Long count;

        public StatusCount() {}

        public StatusCount(Integer status, Long count) {
            this.status = status;
            this.count = count;
        }

        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }

        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }
    }

    /**
     * 类型统计结果类
     */
    class TypeCount {
        private Integer type;
        private Long count;

        public TypeCount() {}

        public TypeCount(Integer type, Long count) {
            this.type = type;
            this.count = count;
        }

        public Integer getType() { return type; }
        public void setType(Integer type) { this.type = type; }

        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }
    }
}
