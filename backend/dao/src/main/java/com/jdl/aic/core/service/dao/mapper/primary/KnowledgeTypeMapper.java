package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.KnowledgeType;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 知识类型表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface KnowledgeTypeMapper {

    /**
     * 插入一条知识类型记录
     *
     * @param knowledgeType 知识类型实体
     * @return 影响的行数
     */
    int insert(KnowledgeType knowledgeType);

    /**
     * 根据ID更新知识类型记录
     *
     * @param knowledgeType 知识类型实体
     * @return 影响的行数
     */
    int updateById(KnowledgeType knowledgeType);

    /**
     * 根据ID删除知识类型记录
     *
     * @param id 知识类型ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询知识类型记录
     *
     * @param id 知识类型ID
     * @return 知识类型实体
     */
    KnowledgeType selectById(@Param("id") Long id);

    /**
     * 查询所有知识类型记录
     *
     * @return 知识类型实体列表
     */
    List<KnowledgeType> selectAll();

    /**
     * 根据条件查询知识类型记录
     *
     * @param knowledgeType 查询条件
     * @return 知识类型实体列表
     */
    List<KnowledgeType> selectByCondition(KnowledgeType knowledgeType);

    /**
     * 根据名称查询知识类型记录
     *
     * @param name 知识类型名称
     * @return 知识类型实体
     */
    KnowledgeType selectByName(@Param("name") String name);

    /**
     * 根据编码查询知识类型记录
     *
     * @param code 知识类型编码
     * @return 知识类型实体
     */
    KnowledgeType selectByCode(@Param("code") String code);
}
