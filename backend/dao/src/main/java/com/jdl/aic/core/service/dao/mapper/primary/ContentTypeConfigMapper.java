package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentTypeConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 内容类型配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-18
 */
@Mapper
public interface ContentTypeConfigMapper {

    /**
     * 插入内容类型配置
     *
     * @param contentTypeConfig 内容类型配置信息
     * @return 影响行数
     */
    int insert(ContentTypeConfig contentTypeConfig);

    /**
     * 根据ID删除内容类型配置
     *
     * @param id 配置ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新内容类型配置信息
     *
     * @param contentTypeConfig 内容类型配置信息
     * @return 影响行数
     */
    int updateById(ContentTypeConfig contentTypeConfig);

    /**
     * 根据ID查询内容类型配置
     *
     * @param id 配置ID
     * @return 内容类型配置信息
     */
    ContentTypeConfig selectById(Long id);

    /**
     * 根据编码查询内容类型配置
     *
     * @param code 内容类型编码
     * @return 内容类型配置信息
     */
    ContentTypeConfig selectByCode(@Param("code") String code);

    /**
     * 根据条件查询内容类型配置列表
     *
     * @param contentTypeConfig 查询条件
     * @return 内容类型配置列表
     */
    List<ContentTypeConfig> selectByCondition(ContentTypeConfig contentTypeConfig);

    /**
     * 查询所有启用的内容类型配置
     *
     * @return 内容类型配置列表
     */
    List<ContentTypeConfig> selectAllActive();

    /**
     * 查询门户模块配置
     *
     * @param isPortalModule 是否为门户模块
     * @param isActive 是否启用
     * @return 内容类型配置列表
     */
    List<ContentTypeConfig> selectByPortalModule(@Param("isPortalModule") Boolean isPortalModule,
                                               @Param("isActive") Boolean isActive);

    /**
     * 更新内容类型配置状态
     *
     * @param id 配置ID
     * @param isActive 是否启用
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);

    /**
     * 更新内容类型配置排序
     *
     * @param id 配置ID
     * @param sortOrder 排序值
     * @return 影响行数
     */
    int updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);

    /**
     * 根据表名查询内容类型配置
     *
     * @param tableName 表名
     * @return 内容类型配置信息
     */
    ContentTypeConfig selectByTableName(@Param("tableName") String tableName);

    /**
     * 搜索内容类型配置（根据名称和描述）
     *
     * @param keyword 搜索关键词
     * @param isActive 是否启用
     * @return 内容类型配置列表
     */
    List<ContentTypeConfig> searchConfigs(@Param("keyword") String keyword,
                                        @Param("isActive") Boolean isActive);
}