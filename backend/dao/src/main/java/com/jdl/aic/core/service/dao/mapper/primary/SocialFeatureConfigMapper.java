package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.SocialFeatureConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 社交功能配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-18
 */
@Mapper
public interface SocialFeatureConfigMapper {

    /**
     * 插入社交功能配置
     *
     * @param socialFeatureConfig 社交功能配置信息
     * @return 影响行数
     */
    int insert(SocialFeatureConfig socialFeatureConfig);

    /**
     * 根据ID删除社交功能配置
     *
     * @param id 配置ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新社交功能配置信息
     *
     * @param socialFeatureConfig 社交功能配置信息
     * @return 影响行数
     */
    int updateById(SocialFeatureConfig socialFeatureConfig);

    /**
     * 根据ID查询社交功能配置
     *
     * @param id 配置ID
     * @return 社交功能配置信息
     */
    SocialFeatureConfig selectById(Long id);

    /**
     * 根据内容类型和功能类型查询配置
     *
     * @param contentType 内容类型
     * @param featureType 功能类型
     * @return 社交功能配置信息
     */
    SocialFeatureConfig selectByContentAndFeature(@Param("contentType") String contentType, 
                                                 @Param("featureType") String featureType);

    /**
     * 根据内容类型查询所有功能配置
     *
     * @param contentType 内容类型
     * @param isEnabled 是否启用
     * @return 社交功能配置列表
     */
    List<SocialFeatureConfig> selectByContentType(@Param("contentType") String contentType,
                                                 @Param("isEnabled") Boolean isEnabled);

    /**
     * 根据功能类型查询所有配置
     *
     * @param featureType 功能类型
     * @param isEnabled 是否启用
     * @return 社交功能配置列表
     */
    List<SocialFeatureConfig> selectByFeatureType(@Param("featureType") String featureType,
                                                 @Param("isEnabled") Boolean isEnabled);

    /**
     * 根据条件查询社交功能配置列表
     *
     * @param socialFeatureConfig 查询条件
     * @return 社交功能配置列表
     */
    List<SocialFeatureConfig> selectByCondition(SocialFeatureConfig socialFeatureConfig);

    /**
     * 查询所有启用的社交功能配置
     *
     * @return 社交功能配置列表
     */
    List<SocialFeatureConfig> selectAllEnabled();

    /**
     * 更新社交功能配置状态
     *
     * @param id 配置ID
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("isEnabled") Boolean isEnabled);

    /**
     * 批量更新内容类型的功能状态
     *
     * @param contentType 内容类型
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    int updateStatusByContentType(@Param("contentType") String contentType, 
                                @Param("isEnabled") Boolean isEnabled);

    /**
     * 批量更新功能类型的状态
     *
     * @param featureType 功能类型
     * @param isEnabled 是否启用
     * @return 影响行数
     */
    int updateStatusByFeatureType(@Param("featureType") String featureType, 
                                @Param("isEnabled") Boolean isEnabled);

    /**
     * 检查内容类型和功能类型组合是否存在
     *
     * @param contentType 内容类型
     * @param featureType 功能类型
     * @return 存在数量
     */
    int countByContentAndFeature(@Param("contentType") String contentType, 
                               @Param("featureType") String featureType);
}
