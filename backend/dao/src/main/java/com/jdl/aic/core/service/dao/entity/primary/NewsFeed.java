package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 资讯表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("news_feed")
public class NewsFeed implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资讯标题
     */
    private String title;

    /**
     * 资讯作者
     */
    private String author;

    /**
     * 资讯原文链接
     */
    private String sourceUrl;

    /**
     * 资讯发布时间（原文时间）
     */
    private LocalDateTime publishedAt;

    /**
     * 资讯摘要（AI生成或人工编辑）
     */
    private String contentSummary;

    /**
     * 资讯内容 HTML 格式（全文抓取）
     */
    private String contentHtml;

    /**
     * 资讯封面图
     */
    private String coverImageUrl;

    /**
     * 外键关联 rss_source.id，标识来源 RSS
     */
    private Long rssSourceId;

    /**
     * 来源名称（如"新华网"，当 rss_source_id 为空时）
     */
    private String sourceName;

    /**
     * 资讯类型（0:采集, 1:官方发布）
     */
    private Integer type;

    /**
     * 状态（0:待审核, 1:已发布, 2:已下线）
     */
    private Integer status;

    /**
     * AI 审核状态（0:未审, 1:通过, 2:拒绝, 3:人工复审）
     */
    private Integer aiReviewStatus;

    /**
     * AI 推荐的标签列表
     */
    private String aiTagsJson;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 最后更新用户ID（跨库引用，无外键约束）
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;

    // 常量定义

    /**
     * 资讯类型常量
     */
    public static class Type {
        public static final int COLLECTED = 0;  // 采集
        public static final int OFFICIAL = 1;   // 官方发布
    }

    /**
     * 状态常量
     */
    public static class Status {
        public static final int PENDING_REVIEW = 0;  // 待审核
        public static final int PUBLISHED = 1;       // 已发布
        public static final int OFFLINE = 2;         // 已下线
    }

    /**
     * AI审核状态常量
     */
    public static class AiReviewStatus {
        public static final int NOT_REVIEWED = 0;    // 未审
        public static final int APPROVED = 1;        // 通过
        public static final int REJECTED = 2;        // 拒绝
        public static final int MANUAL_REVIEW = 3;   // 人工复审
    }
}
