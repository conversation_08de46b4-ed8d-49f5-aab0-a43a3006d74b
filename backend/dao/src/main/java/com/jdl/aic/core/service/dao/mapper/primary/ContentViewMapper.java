package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentView;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容浏览表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentViewMapper {

    /**
     * 插入一条内容浏览记录
     *
     * @param contentView 内容浏览实体
     * @return 影响的行数
     */
    int insert(ContentView contentView);

    /**
     * 根据ID删除内容浏览记录
     *
     * @param id 内容浏览ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 更新内容浏览记录
     *
     * @param contentView 内容浏览实体
     * @return 影响的行数
     */
    int update(ContentView contentView);

    /**
     * 根据ID查询内容浏览
     *
     * @param id 内容浏览ID
     * @return 内容浏览实体
     */
    ContentView selectById(@Param("id") Long id);

    /**
     * 根据内容ID查询内容浏览列表
     *
     * @param contentId 内容ID
     * @return 内容浏览实体列表
     */
    List<ContentView> selectByContentId(@Param("contentId") Long contentId);

    /**
     * 根据用户ID查询内容浏览列表
     *
     * @param userId 用户ID
     * @return 内容浏览实体列表
     */
    List<ContentView> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询所有内容浏览
     *
     * @return 内容浏览实体列表
     */
    List<ContentView> selectAll();

    /**
     * 统计内容的浏览次数
     *
     * @param contentId 内容ID
     * @return 浏览次数
     */
    int countByContentId(@Param("contentId") Long contentId);

    /**
     * 获取内容的总浏览时长
     *
     * @param contentId 内容ID
     * @return 总浏览时长（秒）
     */
    int getTotalViewDuration(@Param("contentId") Long contentId);

    /**
     * 更新浏览时长
     *
     * @param id 浏览记录ID
     * @param viewDuration 新的浏览时长
     * @return 影响的行数
     */
    int updateViewDuration(@Param("id") Long id, @Param("viewDuration") Integer viewDuration);
}
