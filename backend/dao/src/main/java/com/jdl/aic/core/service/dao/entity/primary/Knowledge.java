package com.jdl.aic.core.service.dao.entity.primary;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 知识表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("knowledge")
public class Knowledge implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 知识标题
     */
    private String title;

    /**
     * 知识的简短描述或摘要
     */
    private String description;

    /**
     * 知识的主体内容，如代码、长文本、Markdown 等
     */
    private String content;

    /**
     * 外键关联 knowledge_type.id，知识类型
     */
    private Long knowledgeTypeId;

    /**
     * 关联 user.id，知识作者（跨库引用，无外键约束）
     */
    private String authorId;

    /**
     * 作者姓名（冗余字段，减少跨库查询）
     */
    private String authorName;

    /**
     * 知识状态（0:草稿, 1:待审核, 2:已发布, 3:已下线, 4:已拒绝）
     */
    private Integer status;

    /**
     * 可见性（0:私有, 1:团队可见, 2:公开）
     */
    private Integer visibility;

    /**
     * 如果 visibility 为团队可见，关联 team.id（跨库引用，无外键约束）
     */
    private Long teamId;

    /**
     * 团队名称（冗余字段，减少跨库查询）
     */
    private String teamName;

    /**
     * 知识版本号，如 v1.0.0
     */
    private String version;

    /**
     * 阅读次数
     */
    private Integer readCount;

    /**
     * 点赞次数
     */
    private Integer likeCount;

    /**
     * 评论次数
     */
    private Integer commentCount;

    /**
     * Fork 次数
     */
    private Integer forkCount;

    /**
     * 收藏次数
     */
    private Integer favoriteCount;

    /**
     * 分享次数
     */
    private Integer shareCount;

    /**
     * 社交热度分数
     */
    private java.math.BigDecimal socialScore;

    /**
     * 最后社交活动时间
     */
    private LocalDateTime lastSocialActivityAt;

    /**
     * 知识的封面图片 URL
     */
    private String coverImageUrl;

    /**
     * 核心字段，存储不同知识类型特有的结构化数据
     */
    private String metadataJson;

    /**
     * AI 审核状态（0:未审, 1:通过, 2:拒绝, 3:人工复审）
     */
    private Integer aiReviewStatus;

    /**
     * AI 推荐的标签列表，["tag1", "tag2"]
     */
    private String aiTagsJson;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 最后更新用户ID（跨库引用，无外键约束）
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;
}
