package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户关注表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_follow")
public class UserFollow implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 外键关联 user.id
     */
    private Long userId;

    /**
     * 被关注人ID
     */
    private Long followedId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    private String followedUsername;
    private String followedAvatar;
    private String followedBio;
    private String followedEmail;
    private String followedPhone;
    private String followedDepartment;
    private String followedTags;
    private String followedIsActive;
    private String followedDisplayName;
    private String followedAvatarUrl;
    private String followedTitle;
}
