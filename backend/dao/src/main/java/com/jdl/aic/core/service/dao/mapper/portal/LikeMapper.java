package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.Like;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 点赞数据访问接口
 * 
 * <p>提供点赞相关的数据库操作，包括：
 * <ul>
 *   <li>基本CRUD操作</li>
 *   <li>用户点赞查询</li>
 *   <li>内容点赞统计</li>
 *   <li>点赞状态管理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface LikeMapper {

    // ==================== 基本CRUD操作 ====================

    /**
     * 插入点赞记录
     *
     * @param like 点赞信息
     * @return 影响行数
     */
    int insert(Like like);

    /**
     * 根据ID删除点赞记录（物理删除）
     *
     * @param id 点赞ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 软删除点赞记录（标记为已删除）
     *
     * @param id 点赞ID
     * @return 影响行数
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 根据用户ID和内容信息删除点赞记录
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 影响行数
     */
    int deleteByUserAndContent(@Param("userId") Long userId, 
                              @Param("contentType") Integer contentType, 
                              @Param("contentId") Long contentId);

    /**
     * 根据ID查询点赞记录
     *
     * @param id 点赞ID
     * @return 点赞信息
     */
    Like selectById(@Param("id") Long id);

    // ==================== 条件查询 ====================

    /**
     * 根据用户ID查询点赞列表
     *
     * @param userId 用户ID
     * @return 点赞列表
     */
    List<Like> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和内容类型查询点赞列表
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @return 点赞列表
     */
    List<Like> selectByUserIdAndContentType(@Param("userId") Long userId, 
                                           @Param("contentType") Integer contentType);

    /**
     * 根据内容信息查询点赞列表
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 点赞列表
     */
    List<Like> selectByContent(@Param("contentType") Integer contentType, 
                              @Param("contentId") Long contentId);

    /**
     * 检查用户是否已点赞指定内容
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 点赞记录（如果存在）
     */
    Like selectByUserAndContent(@Param("userId") Long userId, 
                               @Param("contentType") Integer contentType, 
                               @Param("contentId") Long contentId);

    /**
     * 根据条件查询点赞列表
     *
     * @param userId 用户ID（可选）
     * @param contentType 内容类型（可选）
     * @param relatedKnowledgeTypeId 关联知识类型ID（可选）
     * @return 点赞列表
     */
    List<Like> selectByCondition(@Param("userId") Long userId,
                                @Param("contentType") Integer contentType,
                                @Param("relatedKnowledgeTypeId") Long relatedKnowledgeTypeId);

    // ==================== 统计查询 ====================

    /**
     * 统计用户点赞总数
     *
     * @param userId 用户ID
     * @return 点赞总数
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定类型内容的点赞数
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @return 点赞数
     */
    int countByUserIdAndContentType(@Param("userId") Long userId, 
                                   @Param("contentType") Integer contentType);

    /**
     * 统计指定内容的点赞数
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 点赞数
     */
    int countByContent(@Param("contentType") Integer contentType, 
                      @Param("contentId") Long contentId);

    /**
     * 统计指定内容列表的点赞数
     *
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 点赞统计列表（包含contentId和count）
     */
    List<Object> countByContentIds(@Param("contentType") Integer contentType, 
                                  @Param("contentIds") List<Long> contentIds);

    // ==================== 批量操作 ====================

    /**
     * 批量插入点赞记录
     *
     * @param likes 点赞列表
     * @return 影响行数
     */
    int batchInsert(@Param("likes") List<Like> likes);

    /**
     * 批量删除点赞记录
     *
     * @param ids 点赞ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 批量软删除点赞记录
     *
     * @param ids 点赞ID列表
     * @return 影响行数
     */
    int batchSoftDeleteByIds(@Param("ids") List<Long> ids);

    // ==================== 状态管理 ====================

    /**
     * 恢复已删除的点赞记录
     *
     * @param id 点赞ID
     * @return 影响行数
     */
    int restoreById(@Param("id") Long id);

    /**
     * 清理指定天数前的已删除点赞记录
     *
     * @param days 天数
     * @return 影响行数
     */
    int cleanupDeletedRecords(@Param("days") Integer days);

    // ==================== 高级查询 ====================

    /**
     * 获取用户最近点赞的内容
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 点赞列表
     */
    List<Like> selectRecentByUserId(@Param("userId") Long userId, 
                                   @Param("limit") Integer limit);

    /**
     * 获取热门点赞内容（按点赞数排序）
     *
     * @param contentType 内容类型
     * @param limit 限制数量
     * @param days 统计天数（可选）
     * @return 热门内容列表
     */
    List<Object> selectPopularContent(@Param("contentType") Integer contentType, 
                                     @Param("limit") Integer limit,
                                     @Param("days") Integer days);

    /**
     * 检查点赞记录是否存在（包括已删除的）
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return true表示存在，false表示不存在
     */
    boolean existsByUserAndContent(@Param("userId") Long userId, 
                                  @Param("contentType") Integer contentType, 
                                  @Param("contentId") Long contentId);

    // ==================== 点赞状态切换 ====================

    /**
     * 切换点赞状态（如果已点赞则取消，如果未点赞则添加）
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param relatedKnowledgeTypeId 关联知识类型ID（可选）
     * @return 操作结果：1表示添加点赞，-1表示取消点赞，0表示无变化
     */
    int toggleLike(@Param("userId") Long userId, 
                   @Param("contentType") Integer contentType, 
                   @Param("contentId") Long contentId,
                   @Param("relatedKnowledgeTypeId") Long relatedKnowledgeTypeId);

    /**
     * 获取用户对指定内容列表的点赞状态
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 点赞状态列表（包含contentId和isLiked）
     */
    List<Object> selectLikeStatusByContentIds(@Param("userId") Long userId,
                                             @Param("contentType") Integer contentType,
                                             @Param("contentIds") List<Long> contentIds);
}
