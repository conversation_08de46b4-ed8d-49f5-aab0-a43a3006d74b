package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.SolutionKnowledge;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 解决方案-知识关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface SolutionKnowledgeMapper {

    /**
     * 插入一条解决方案-知识关联记录
     *
     * @param solutionKnowledge 解决方案-知识关联实体
     * @return 影响的行数
     */
    int insert(SolutionKnowledge solutionKnowledge);

    /**
     * 根据ID更新解决方案-知识关联记录
     *
     * @param solutionKnowledge 解决方案-知识关联实体
     * @return 影响的行数
     */
    int updateById(SolutionKnowledge solutionKnowledge);

    /**
     * 根据ID删除解决方案-知识关联记录
     *
     * @param id 解决方案-知识关联ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询解决方案-知识关联记录
     *
     * @param id 解决方案-知识关联ID
     * @return 解决方案-知识关联实体
     */
    SolutionKnowledge selectById(@Param("id") Long id);

    /**
     * 根据解决方案ID查询关联的知识记录
     *
     * @param solutionId 解决方案ID
     * @return 解决方案-知识关联实体列表
     */
    List<SolutionKnowledge> selectBySolutionId(@Param("solutionId") Long solutionId);

    /**
     * 根据知识ID查询关联的解决方案记录
     *
     * @param knowledgeId 知识ID
     * @return 解决方案-知识关联实体列表
     */
    List<SolutionKnowledge> selectByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 批量插入解决方案-知识关联记录
     *
     * @param solutionKnowledgeList 解决方案-知识关联实体列表
     * @return 影响的行数
     */
    int batchInsert(@Param("list") List<SolutionKnowledge> solutionKnowledgeList);

    /**
     * 根据解决方案ID删除所有关联记录
     *
     * @param solutionId 解决方案ID
     * @return 影响的行数
     */
    int deleteBySolutionId(@Param("solutionId") Long solutionId);

    /**
     * 更新解决方案-知识关联的排序
     *
     * @param id 解决方案-知识关联ID
     * @param sortOrder 新的排序值
     * @return 影响的行数
     */
    int updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);
}
