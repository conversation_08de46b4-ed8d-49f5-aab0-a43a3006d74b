package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 团队收藏表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("team_favorite")
public class TeamFavorite implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 外键关联 user.id
     */
    private Long userId;

    /**
     * 关联对应内容ID（team.id）
     */
    private Long teamId;

    /**
     * 收藏时间
     */
    private LocalDateTime createdAt;

    /**
     * 软删除标记（取消收藏）
     */
    private LocalDateTime deletedAt;
}
