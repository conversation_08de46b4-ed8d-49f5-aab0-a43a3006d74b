package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.UserCourseEnrollment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户课程报名表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-22
 */
@Mapper
public interface UserCourseEnrollmentMapper {

    /**
     * 插入报名记录
     *
     * @param enrollment 报名信息
     * @return 影响行数
     */
    int insert(UserCourseEnrollment enrollment);

    /**
     * 根据ID删除报名记录
     *
     * @param id 报名ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新报名信息
     *
     * @param enrollment 报名信息
     * @return 影响行数
     */
    int updateById(UserCourseEnrollment enrollment);

    /**
     * 根据ID查询报名记录
     *
     * @param id 报名ID
     * @return 报名信息
     */
    UserCourseEnrollment selectById(Long id);

    /**
     * 根据条件查询报名列表
     *
     * @param enrollment 查询条件
     * @return 报名列表
     */
    List<UserCourseEnrollment> selectByCondition(UserCourseEnrollment enrollment);

    /**
     * 根据用户ID查询报名列表
     *
     * @param userId 用户ID
     * @return 报名列表
     */
    List<UserCourseEnrollment> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据课程ID查询报名列表
     *
     * @param courseId 课程ID
     * @return 报名列表
     */
    List<UserCourseEnrollment> selectByCourseId(@Param("courseId") Long courseId);

    /**
     * 根据用户ID和课程ID查询报名记录
     *
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 报名信息
     */
    UserCourseEnrollment selectByUserIdAndCourseId(@Param("userId") Long userId, @Param("courseId") Long courseId);

    /**
     * 根据用户ID和状态查询报名列表
     *
     * @param userId 用户ID
     * @param enrollmentStatus 报名状态
     * @return 报名列表
     */
    List<UserCourseEnrollment> selectByUserIdAndStatus(@Param("userId") Long userId, 
                                                      @Param("enrollmentStatus") String enrollmentStatus);

    /**
     * 根据课程ID和状态查询报名列表
     *
     * @param courseId 课程ID
     * @param enrollmentStatus 报名状态
     * @return 报名列表
     */
    List<UserCourseEnrollment> selectByCourseIdAndStatus(@Param("courseId") Long courseId, 
                                                        @Param("enrollmentStatus") String enrollmentStatus);

    /**
     * 更新报名状态
     *
     * @param id 报名ID
     * @param enrollmentStatus 报名状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("enrollmentStatus") String enrollmentStatus);

    /**
     * 更新学习进度
     *
     * @param id 报名ID
     * @param progressPercentage 进度百分比
     * @param completedStages 已完成阶段数
     * @param totalStages 总阶段数
     * @param studyHours 学习时长
     * @return 影响行数
     */
    int updateProgress(@Param("id") Long id, 
                      @Param("progressPercentage") BigDecimal progressPercentage,
                      @Param("completedStages") Integer completedStages,
                      @Param("totalStages") Integer totalStages,
                      @Param("studyHours") BigDecimal studyHours);

    /**
     * 更新最后学习时间
     *
     * @param id 报名ID
     * @return 影响行数
     */
    int updateLastStudyTime(@Param("id") Long id);

    /**
     * 统计用户报名课程数量
     *
     * @param userId 用户ID
     * @param enrollmentStatus 报名状态（可选）
     * @return 报名数量
     */
    int countByUserId(@Param("userId") Long userId, @Param("enrollmentStatus") String enrollmentStatus);

    /**
     * 统计课程报名人数
     *
     * @param courseId 课程ID
     * @param enrollmentStatus 报名状态（可选）
     * @return 报名人数
     */
    int countByCourseId(@Param("courseId") Long courseId, @Param("enrollmentStatus") String enrollmentStatus);

    /**
     * 搜索报名记录（根据课程名称）
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     * @param enrollmentStatus 报名状态
     * @return 报名列表
     */
    List<UserCourseEnrollment> searchEnrollments(@Param("userId") Long userId,
                                                @Param("keyword") String keyword,
                                                @Param("enrollmentStatus") String enrollmentStatus);

    /**
     * 批量更新学习时长
     *
     * @param enrollmentIds 报名ID列表
     * @param additionalHours 增加的学习时长
     * @return 影响行数
     */
    int batchUpdateStudyHours(@Param("enrollmentIds") List<Long> enrollmentIds, 
                             @Param("additionalHours") BigDecimal additionalHours);

    /**
     * 获取用户学习统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    UserCourseEnrollment getUserLearningStats(@Param("userId") Long userId);

    /**
     * 获取课程报名统计信息
     *
     * @param courseId 课程ID
     * @return 统计信息
     */
    UserCourseEnrollment getCourseEnrollmentStats(@Param("courseId") Long courseId);

    /**
     * 软删除报名记录
     *
     * @param id 报名ID
     * @return 影响行数
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 根据时间范围查询报名列表
     *
     * @param userId 用户ID
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 报名列表
     */
    List<UserCourseEnrollment> selectByDateRange(@Param("userId") Long userId,
                                               @Param("startDate") String startDate,
                                               @Param("endDate") String endDate);
}
