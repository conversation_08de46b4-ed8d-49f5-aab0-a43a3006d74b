package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 字典表，存储系统配置参数、枚举值、常量等键值对数据
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dictionary")
public class Dictionary implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 字典键，用于标识配置项
     */
    private String key;

    /**
     * 字典值，存储配置的具体内容
     */
    private String value;

    /**
     * 字典类型，用于分类管理（如：system_config, enum_value, constant等）
     */
    private String type;

    /**
     * 字典项描述说明
     */
    private String description;

    /**
     * 排序权重，数值越小越靠前
     */
    private Integer sortOrder;

    /**
     * 是否启用（0:否, 1:是）
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 最后更新用户ID
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;
}
