package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentLike;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容点赞表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentLikeMapper {

    /**
     * 插入一条内容点赞记录
     *
     * @param contentLike 内容点赞实体
     * @return 影响的行数
     */
    int insert(ContentLike contentLike);

    /**
     * 根据ID删除内容点赞记录
     *
     * @param id 内容点赞ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据内容ID和用户ID删除内容点赞记录
     *
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteByContentIdAndUserId(@Param("contentId") Long contentId, @Param("userId") Long userId);

    /**
     * 更新内容点赞记录
     *
     * @param contentLike 内容点赞实体
     * @return 影响的行数
     */
    int update(ContentLike contentLike);

    /**
     * 根据ID查询内容点赞
     *
     * @param id 内容点赞ID
     * @return 内容点赞实体
     */
    ContentLike selectById(@Param("id") Long id);

    /**
     * 根据内容ID查询内容点赞列表
     *
     * @param contentId 内容ID
     * @return 内容点赞实体列表
     */
    List<ContentLike> selectByContentId(@Param("contentId") Long contentId);

    /**
     * 根据用户ID查询内容点赞列表
     *
     * @param userId 用户ID
     * @return 内容点赞实体列表
     */
    List<ContentLike> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询所有内容点赞
     *
     * @return 内容点赞实体列表
     */
    List<ContentLike> selectAll();

    /**
     * 统计内容的点赞数
     *
     * @param contentId 内容ID
     * @return 点赞数
     */
    int countByContentId(@Param("contentId") Long contentId);

    /**
     * 检查用户是否已点赞内容
     *
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 点赞记录数（0或1）
     */
    int checkUserLiked(@Param("contentId") Long contentId, @Param("userId") Long userId);
}
