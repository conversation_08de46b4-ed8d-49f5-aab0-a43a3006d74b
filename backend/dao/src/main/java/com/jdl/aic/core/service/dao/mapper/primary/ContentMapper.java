package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.Content;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentMapper {

    /**
     * 插入一条内容记录
     *
     * @param content 内容实体
     * @return 影响的行数
     */
    int insert(Content content);

    /**
     * 根据ID删除内容记录
     *
     * @param id 内容ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 更新内容记录
     *
     * @param content 内容实体
     * @return 影响的行数
     */
    int update(Content content);

    /**
     * 根据ID查询内容
     *
     * @param id 内容ID
     * @return 内容实体
     */
    Content selectById(@Param("id") Long id);

    /**
     * 查询所有内容
     *
     * @return 内容实体列表
     */
    List<Content> selectAll();

    /**
     * 根据作者ID查询内容
     *
     * @param authorId 作者ID
     * @return 内容实体列表
     */
    List<Content> selectByAuthorId(@Param("authorId") Long authorId);

    /**
     * 根据状态查询内容
     *
     * @param status 内容状态
     * @return 内容实体列表
     */
    List<Content> selectByStatus(@Param("status") Integer status);

    /**
     * 根据类型查询内容
     *
     * @param type 内容类型
     * @return 内容实体列表
     */
    List<Content> selectByType(@Param("type") Integer type);

    /**
     * 更新内容状态
     *
     * @param id 内容ID
     * @param status 新状态
     * @return 影响的行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 增加浏览次数
     *
     * @param id 内容ID
     * @param increment 增加的次数
     * @return 影响的行数
     */
    int incrementViewCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 增加点赞次数
     *
     * @param id 内容ID
     * @param increment 增加的次数
     * @return 影响的行数
     */
    int incrementLikeCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 增加评论次数
     *
     * @param id 内容ID
     * @param increment 增加的次数
     * @return 影响的行数
     */
    int incrementCommentCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 增加收藏次数
     *
     * @param id 内容ID
     * @param increment 增加的次数
     * @return 影响的行数
     */
    int incrementFavoriteCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 增加分享次数
     *
     * @param id 内容ID
     * @param increment 增加的次数
     * @return 影响的行数
     */
    int incrementShareCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 查询热门内容
     *
     * @param limit 限制数量
     * @return 内容实体列表
     */
    List<Content> selectHotContents(@Param("limit") Integer limit);
}
