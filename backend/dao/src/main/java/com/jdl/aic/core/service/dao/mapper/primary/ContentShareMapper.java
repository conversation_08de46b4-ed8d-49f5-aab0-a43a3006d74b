package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.ContentShare;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 内容分享表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface ContentShareMapper {

    /**
     * 插入一条内容分享记录
     *
     * @param contentShare 内容分享实体
     * @return 影响的行数
     */
    int insert(ContentShare contentShare);

    /**
     * 根据ID删除内容分享记录
     *
     * @param id 内容分享ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 更新内容分享记录
     *
     * @param contentShare 内容分享实体
     * @return 影响的行数
     */
    int update(ContentShare contentShare);

    /**
     * 根据ID查询内容分享
     *
     * @param id 内容分享ID
     * @return 内容分享实体
     */
    ContentShare selectById(@Param("id") Long id);

    /**
     * 根据内容ID查询内容分享列表
     *
     * @param contentId 内容ID
     * @return 内容分享实体列表
     */
    List<ContentShare> selectByContentId(@Param("contentId") Long contentId);

    /**
     * 根据用户ID查询内容分享列表
     *
     * @param userId 用户ID
     * @return 内容分享实体列表
     */
    List<ContentShare> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据内容ID和用户ID查询内容分享
     *
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 内容分享实体
     */
    ContentShare selectByContentIdAndUserId(@Param("contentId") Long contentId, @Param("userId") Long userId);

    /**
     * 根据内容ID删除内容分享记录
     *
     * @param contentId 内容ID
     * @return 影响的行数
     */
    int deleteByContentId(@Param("contentId") Long contentId);

    /**
     * 根据用户ID删除内容分享记录
     *
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 统计内容的分享次数
     *
     * @param contentId 内容ID
     * @return 分享次数
     */
    int countByContentId(@Param("contentId") Long contentId);

    /**
     * 根据平台统计内容的分享次数
     *
     * @param contentId 内容ID
     * @param platform 分享平台
     * @return 分享次数
     */
    int countByContentIdAndPlatform(@Param("contentId") Long contentId, @Param("platform") String platform);
}
