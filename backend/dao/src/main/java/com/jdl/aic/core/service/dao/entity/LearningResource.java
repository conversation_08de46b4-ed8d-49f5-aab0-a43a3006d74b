package com.jdl.aic.core.service.dao.entity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 学习资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("learning_resource")
public class LearningResource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资源标题
     */
    private String title;

    /**
     * 资源描述
     */
    private String description;

    /**
     * 学习资源详细内容
     */
    private String content;

    /**
     * 学习目标
     */
    private String learningGoals;

    /**
     * 前置知识要求
     */
    private String prerequisites;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 资源链接
     */
    private String sourceUrl;

    /**
     * 来源平台
     */
    private String sourcePlatform;

    /**
     * 难度级别
     */
    private String difficultyLevel;

    /**
     * 预估学习时长
     */
    private BigDecimal estimatedDurationHours;

    /**
     * 资源语言
     */
    private String language;

    /**
     * 是否免费
     */
    private Boolean isFree;

    /**
     * 价格信息
     */
    private String priceInfo;

    /**
     * 平均评分
     */
    private BigDecimal rating;

    /**
     * 评分人数
     */
    private Integer ratingCount;

    /**
     * 查看次数
     */
    private Integer viewCount;

    /**
     * 收藏次数
     */
    private Integer bookmarkCount;

    /**
     * 完成人数
     */
    private Integer completionCount;

    /**
     * 完成率
     */
    private BigDecimal completionRate;

    /**
     * 标签列表，逗号分隔
     */
    private String tags;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 内容配置JSON
     */
    private String contentConfig;

    /**
     * 嵌入配置JSON
     */
    private String embedConfig;

    /**
     * 访问配置JSON
     */
    private String accessConfig;

    /**
     * 媒体元数据JSON
     */
    private String mediaMetadata;

    /**
     * 扩展元数据
     */
    private String metadata;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 最后更新用户ID
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;
}
