package com.jdl.aic.core.service.dao.entity.primary;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 解决方案-知识关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("solution_knowledge")
public class SolutionKnowledge implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 外键关联 solution.id
     */
    private Long solutionId;

    /**
     * 外键关联 knowledge.id
     */
    private Long knowledgeId;

    /**
     * 在解决方案中的排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 最后更新用户ID（跨库引用，无外键约束）
     */
    private String updatedBy;
}
