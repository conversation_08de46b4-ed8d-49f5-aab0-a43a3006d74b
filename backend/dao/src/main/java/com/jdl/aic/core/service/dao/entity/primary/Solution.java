package com.jdl.aic.core.service.dao.entity.primary;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 场景解决方案表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("solution")
public class Solution implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 解决方案标题
     */
    private String title;

    /**
     * 解决方案描述
     */
    private String description;

    /**
     * 解决方案的详细内容（Markdown/富文本）
     */
    private String content;

    /**
     * 关联 user.id，解决方案作者（跨库引用，无外键约束）
     */
    private String authorId;

    /**
     * 作者姓名（冗余字段，减少跨库查询）
     */
    private String authorName;

    /**
     * 状态（0:草稿, 1:待审核, 2:已发布, 3:已下线）
     */
    private Integer status;

    /**
     * 可见性（0:私有, 1:团队可见, 2:公开）
     */
    private Integer visibility;

    /**
     * 如果 visibility 为团队可见，关联 team.id（跨库引用，无外键约束）
     */
    private Long teamId;

    /**
     * 团队名称（冗余字段，减少跨库查询）
     */
    private String teamName;

    /**
     * 阅读次数
     */
    private Integer readCount;

    /**
     * 点赞次数
     */
    private Integer likeCount;

    /**
     * 评论次数
     */
    private Integer commentCount;

    /**
     * 封面图片 URL
     */
    private String coverImageUrl;

    /**
     * AI 审核状态
     */
    private Integer aiReviewStatus;

    /**
     * AI 推荐标签列表
     */
    private String aiTagsJson;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 最后更新用户ID（跨库引用，无外键约束）
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;
}
