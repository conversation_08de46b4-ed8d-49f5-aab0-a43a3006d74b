package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 团队表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("team")
public class Team implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 团队名称
     */
    private String name;

    /**
     * 团队描述
     */
    private String description;

    /**
     * 自关联 team.id，父团队ID，支持团队层级结构
     */
    private Long parentId;

    /**
     * 团队是否活跃
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 最后更新用户ID
     */
    private String updatedBy;

    /**
     * 团队标签列表（JSON格式存储）
     */
    private String tagsJson;

    /**
     * 团队头像URL
     */
    private String avatarUrl;

    /**
     * 团队隐私设置（0:公开, 1:私有）
     */
    private Integer privacy;

    /**
     * 团队邀请设置（0:任何人可邀请, 1:仅管理员可邀请, 2:仅创建者可邀请）
     */
    private Integer inviteSetting;
}
