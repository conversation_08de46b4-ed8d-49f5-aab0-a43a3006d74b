package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.Favorite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收藏数据访问接口
 * 
 * <p>提供收藏相关的数据库操作，包括：
 * <ul>
 *   <li>基本CRUD操作</li>
 *   <li>用户收藏查询</li>
 *   <li>内容收藏统计</li>
 *   <li>收藏状态管理</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface FavoriteMapper {

    // ==================== 基本CRUD操作 ====================

    /**
     * 插入收藏记录
     *
     * @param favorite 收藏信息
     * @return 影响行数
     */
    int insert(Favorite favorite);

    /**
     * 根据ID删除收藏记录（物理删除）
     *
     * @param id 收藏ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 软删除收藏记录（标记为已删除）
     *
     * @param id 收藏ID
     * @return 影响行数
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 根据用户ID和内容信息删除收藏记录
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 影响行数
     */
    int deleteByUserAndContent(@Param("userId") Long userId, 
                              @Param("contentType") Integer contentType, 
                              @Param("contentId") Long contentId);

    /**
     * 根据ID查询收藏记录
     *
     * @param id 收藏ID
     * @return 收藏信息
     */
    Favorite selectById(@Param("id") Long id);

    // ==================== 条件查询 ====================

    /**
     * 根据用户ID查询收藏列表
     *
     * @param userId 用户ID
     * @return 收藏列表
     */
    List<Favorite> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和内容类型查询收藏列表
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @return 收藏列表
     */
    List<Favorite> selectByUserIdAndContentType(@Param("userId") Long userId, 
                                               @Param("contentType") Integer contentType);

    /**
     * 根据内容信息查询收藏列表
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 收藏列表
     */
    List<Favorite> selectByContent(@Param("contentType") Integer contentType, 
                                  @Param("contentId") Long contentId);

    /**
     * 检查用户是否已收藏指定内容
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 收藏记录（如果存在）
     */
    Favorite selectByUserAndContent(@Param("userId") Long userId, 
                                   @Param("contentType") Integer contentType, 
                                   @Param("contentId") Long contentId);

    /**
     * 根据条件查询收藏列表
     *
     * @param userId 用户ID（可选）
     * @param contentType 内容类型（可选）
     * @param relatedKnowledgeTypeId 关联知识类型ID（可选）
     * @return 收藏列表
     */
    List<Favorite> selectByCondition(@Param("userId") Long userId,
                                    @Param("contentType") Integer contentType,
                                    @Param("relatedKnowledgeTypeId") Long relatedKnowledgeTypeId);

    // ==================== 统计查询 ====================

    /**
     * 统计用户收藏总数
     *
     * @param userId 用户ID
     * @return 收藏总数
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 统计用户指定类型内容的收藏数
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @return 收藏数
     */
    int countByUserIdAndContentType(@Param("userId") Long userId, 
                                   @Param("contentType") Integer contentType);

    /**
     * 统计指定内容的收藏数
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 收藏数
     */
    int countByContent(@Param("contentType") Integer contentType, 
                      @Param("contentId") Long contentId);

    /**
     * 统计指定内容列表的收藏数
     *
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 收藏统计列表（包含contentId和count）
     */
    List<Object> countByContentIds(@Param("contentType") Integer contentType, 
                                  @Param("contentIds") List<Long> contentIds);

    // ==================== 批量操作 ====================

    /**
     * 批量插入收藏记录
     *
     * @param favorites 收藏列表
     * @return 影响行数
     */
    int batchInsert(@Param("favorites") List<Favorite> favorites);

    /**
     * 批量删除收藏记录
     *
     * @param ids 收藏ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 批量软删除收藏记录
     *
     * @param ids 收藏ID列表
     * @return 影响行数
     */
    int batchSoftDeleteByIds(@Param("ids") List<Long> ids);

    // ==================== 状态管理 ====================

    /**
     * 恢复已删除的收藏记录
     *
     * @param id 收藏ID
     * @return 影响行数
     */
    int restoreById(@Param("id") Long id);

    /**
     * 清理指定天数前的已删除收藏记录
     *
     * @param days 天数
     * @return 影响行数
     */
    int cleanupDeletedRecords(@Param("days") Integer days);

    // ==================== 高级查询 ====================

    /**
     * 获取用户最近收藏的内容
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 收藏列表
     */
    List<Favorite> selectRecentByUserId(@Param("userId") Long userId, 
                                       @Param("limit") Integer limit);

    /**
     * 获取热门收藏内容（按收藏数排序）
     *
     * @param contentType 内容类型
     * @param limit 限制数量
     * @param days 统计天数（可选）
     * @return 热门内容列表
     */
    List<Object> selectPopularContent(@Param("contentType") Integer contentType, 
                                     @Param("limit") Integer limit,
                                     @Param("days") Integer days);

    /**
     * 检查收藏记录是否存在（包括已删除的）
     *
     * @param userId 用户ID
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return true表示存在，false表示不存在
     */
    boolean existsByUserAndContent(@Param("userId") Long userId, 
                                  @Param("contentType") Integer contentType, 
                                  @Param("contentId") Long contentId);
}
