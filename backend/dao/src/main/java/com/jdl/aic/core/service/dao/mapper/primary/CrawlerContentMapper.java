package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.CrawlerContent;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 爬虫内容表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-17
 */
@Mapper
public interface CrawlerContentMapper {

    /**
     * 插入爬虫内容
     *
     * @param crawlerContent 爬虫内容信息
     * @return 影响行数
     */
    int insert(CrawlerContent crawlerContent);

    /**
     * 根据ID删除爬虫内容
     *
     * @param id 内容ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新爬虫内容信息
     *
     * @param crawlerContent 爬虫内容信息
     * @return 影响行数
     */
    int updateById(CrawlerContent crawlerContent);

    /**
     * 根据ID查询爬虫内容
     *
     * @param id 内容ID
     * @return 爬虫内容信息
     */
    CrawlerContent selectById(Long id);

    /**
     * 根据条件查询爬虫内容列表
     *
     * @param crawlerContent 查询条件
     * @return 爬虫内容列表
     */
    List<CrawlerContent> selectByCondition(CrawlerContent crawlerContent);

    /**
     * 根据MD5查询爬虫内容
     *
     * @param contentMd5 内容MD5值
     * @return 爬虫内容信息
     */
    CrawlerContent selectByMd5(@Param("contentMd5") String contentMd5);

    /**
     * 根据链接查询爬虫内容
     *
     * @param link 原始链接
     * @return 爬虫内容信息
     */
    CrawlerContent selectByLink(@Param("link") String link);

    /**
     * 根据状态查询爬虫内容列表
     *
     * @param status 内容状态
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    List<CrawlerContent> selectByStatus(@Param("status") Integer status, 
                                      @Param("limit") Integer limit);

    /**
     * 根据内容类型查询爬虫内容列表
     *
     * @param contentType 内容类型
     * @param isFeatured 是否精品内容
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    List<CrawlerContent> selectByContentType(@Param("contentType") String contentType,
                                           @Param("isFeatured") Boolean isFeatured,
                                           @Param("limit") Integer limit);

    /**
     * 根据语言查询爬虫内容列表
     *
     * @param language 内容语言
     * @param status 内容状态
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    List<CrawlerContent> selectByLanguage(@Param("language") String language,
                                        @Param("status") Integer status,
                                        @Param("limit") Integer limit);

    /**
     * 根据质量评分范围查询爬虫内容列表
     *
     * @param minScore 最小评分
     * @param maxScore 最大评分
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    List<CrawlerContent> selectByQualityScore(@Param("minScore") BigDecimal minScore,
                                            @Param("maxScore") BigDecimal maxScore,
                                            @Param("limit") Integer limit);

    /**
     * 根据发布时间范围查询爬虫内容列表
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param status 内容状态
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    List<CrawlerContent> selectByPubDateRange(@Param("startDate") LocalDateTime startDate,
                                            @Param("endDate") LocalDateTime endDate,
                                            @Param("status") Integer status,
                                            @Param("limit") Integer limit);

    /**
     * 更新内容状态
     *
     * @param id 内容ID
     * @param status 内容状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 更新AI总结
     *
     * @param id 内容ID
     * @param aiSummary AI总结内容
     * @return 影响行数
     */
    int updateAiSummary(@Param("id") Long id, @Param("aiSummary") String aiSummary);

    /**
     * 更新质量评分
     *
     * @param id 内容ID
     * @param qualityScore 质量评分
     * @return 影响行数
     */
    int updateQualityScore(@Param("id") Long id, @Param("qualityScore") BigDecimal qualityScore);

    /**
     * 更新精品状态
     *
     * @param id 内容ID
     * @param isFeatured 是否精品内容
     * @return 影响行数
     */
    int updateFeaturedStatus(@Param("id") Long id, @Param("isFeatured") Boolean isFeatured);

    /**
     * 搜索爬虫内容（根据标题、描述、内容）
     *
     * @param keyword 搜索关键词
     * @param contentType 内容类型过滤
     * @param language 语言过滤
     * @param status 状态过滤
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    List<CrawlerContent> searchContents(@Param("keyword") String keyword,
                                      @Param("contentType") String contentType,
                                      @Param("language") String language,
                                      @Param("status") Integer status,
                                      @Param("limit") Integer limit);

    /**
     * 统计各状态的内容数量
     *
     * @return 状态统计结果
     */
    List<Object> countByStatus();

    /**
     * 统计各内容类型的数量
     *
     * @return 内容类型统计结果
     */
    List<Object> countByContentType();

    /**
     * 批量插入爬虫内容
     *
     * @param contents 爬虫内容列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<CrawlerContent> contents);

    /**
     * 批量更新内容状态
     *
     * @param ids 内容ID列表
     * @param status 内容状态
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    // ==================== 高级条件查询 ====================

    /**
     * 高级条件查询爬虫内容总数
     *
     * @param params 查询参数Map
     * @return 总数
     */
    Long countByAdvancedCondition(@Param("params") Map<String, Object> params);

    /**
     * 根据新增字段查询爬虫内容
     *
     * @param type 内容类型（新字段）
     * @param taskId 任务ID
     * @param status 状态过滤
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    List<CrawlerContent> selectByNewFields(@Param("type") String type,
                                         @Param("taskId") String taskId,
                                         @Param("status") Integer status,
                                         @Param("limit") Integer limit);

    /**
     * 根据多个条件组合查询（支持 IN 查询）
     *
     * @param ids ID列表
     * @param contentTypes 内容类型列表
     * @param languages 语言列表
     * @param statuses 状态列表
     * @param types 新内容类型列表
     * @param taskIds 任务ID列表
     * @param limit 限制数量
     * @return 爬虫内容列表
     */
    List<CrawlerContent> selectByMultipleConditions(@Param("ids") List<Long> ids,
                                                   @Param("contentTypes") List<String> contentTypes,
                                                   @Param("languages") List<String> languages,
                                                   @Param("statuses") List<Integer> statuses,
                                                   @Param("types") List<String> types,
                                                   @Param("taskIds") List<String> taskIds,
                                                   @Param("limit") Integer limit);
                                                   
    /**
     * 根据type查询去重后的taskName和taskId
     *
     * @param type 内容类型
     * @return 去重后的爬虫内容列表
     */
    List<CrawlerContent> selectDistinctTaskInfoByType(@Param("type") String type);
    
    /**
     * 统计各type类型的内容数量
     *
     * @return type类型统计结果，包含type和count字段
     */
    List<Map<String, Object>> countByType();

}
