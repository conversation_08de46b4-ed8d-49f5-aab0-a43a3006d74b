package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.Dictionary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-17
 */
@Mapper
public interface DictionaryMapper {

    /**
     * 插入字典项
     *
     * @param dictionary 字典信息
     * @return 影响行数
     */
    int insert(Dictionary dictionary);

    /**
     * 根据ID删除字典项
     *
     * @param id 字典ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新字典项信息
     *
     * @param dictionary 字典信息
     * @return 影响行数
     */
    int updateById(Dictionary dictionary);

    /**
     * 根据ID查询字典项
     *
     * @param id 字典ID
     * @return 字典信息
     */
    Dictionary selectById(Long id);

    /**
     * 根据条件查询字典项列表
     *
     * @param dictionary 查询条件
     * @return 字典项列表
     */
    List<Dictionary> selectByCondition(Dictionary dictionary);

    /**
     * 根据类型查询字典项列表
     *
     * @param type 字典类型
     * @param isActive 是否启用
     * @return 字典项列表
     */
    List<Dictionary> selectByType(@Param("type") String type, 
                                @Param("isActive") Boolean isActive);

    /**
     * 根据键和类型查询字典项
     *
     * @param key 字典键
     * @param type 字典类型
     * @return 字典信息
     */
    Dictionary selectByKeyAndType(@Param("key") String key, 
                                @Param("type") String type);

    /**
     * 根据键查询字典项列表
     *
     * @param key 字典键
     * @param isActive 是否启用
     * @return 字典项列表
     */
    List<Dictionary> selectByKey(@Param("key") String key, 
                               @Param("isActive") Boolean isActive);

    /**
     * 更新字典项排序权重
     *
     * @param id 字典ID
     * @param sortOrder 排序权重
     * @return 影响行数
     */
    int updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);

    /**
     * 更新字典项状态
     *
     * @param id 字典ID
     * @param isActive 是否启用
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);

    /**
     * 搜索字典项（根据键、值、描述）
     *
     * @param keyword 搜索关键词
     * @param type 字典类型过滤
     * @param isActive 是否启用
     * @return 字典项列表
     */
    List<Dictionary> searchDictionaries(@Param("keyword") String keyword,
                                      @Param("type") String type,
                                      @Param("isActive") Boolean isActive);

    /**
     * 获取所有字典类型
     *
     * @return 字典类型列表
     */
    List<String> selectAllTypes();

    /**
     * 根据类型统计字典项数量
     *
     * @param type 字典类型
     * @param isActive 是否启用
     * @return 字典项数量
     */
    int countByType(@Param("type") String type, @Param("isActive") Boolean isActive);

    /**
     * 批量插入字典项
     *
     * @param dictionaries 字典项列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<Dictionary> dictionaries);

    /**
     * 批量更新字典项状态
     *
     * @param ids 字典ID列表
     * @param isActive 是否启用
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("isActive") Boolean isActive);
}
