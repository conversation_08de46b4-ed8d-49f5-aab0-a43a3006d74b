package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.LearningPathResource;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 学习路径资源关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface LearningPathResourceMapper {

    /**
     * 插入一条学习路径资源关联记录
     *
     * @param learningPathResource 学习路径资源关联实体
     * @return 影响的行数
     */
    int insert(LearningPathResource learningPathResource);

    /**
     * 根据ID删除学习路径资源关联记录
     *
     * @param id 学习路径资源关联ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 更新学习路径资源关联记录
     *
     * @param learningPathResource 学习路径资源关联实体
     * @return 影响的行数
     */
    int update(LearningPathResource learningPathResource);

    /**
     * 根据ID查询学习路径资源关联
     *
     * @param id 学习路径资源关联ID
     * @return 学习路径资源关联实体
     */
    LearningPathResource selectById(@Param("id") Long id);

    /**
     * 根据学习路径ID查询关联的资源
     *
     * @param learningPathId 学习路径ID
     * @return 学习路径资源关联实体列表
     */
    List<LearningPathResource> selectByLearningPathId(@Param("learningPathId") Long learningPathId);

    /**
     * 根据资源ID查询关联的学习路径
     *
     * @param resourceId 资源ID
     * @return 学习路径资源关联实体列表
     */
    List<LearningPathResource> selectByResourceId(@Param("resourceId") Long resourceId);

    /**
     * 查询指定学习路径的资源数量
     *
     * @param learningPathId 学习路径ID
     * @return 资源数量
     */
    int countByLearningPathId(@Param("learningPathId") Long learningPathId);

    /**
     * 批量插入学习路径资源关联记录
     *
     * @param learningPathResources 学习路径资源关联实体列表
     * @return 影响的行数
     */
    int batchInsert(@Param("list") List<LearningPathResource> learningPathResources);

    /**
     * 根据学习路径ID删除所有关联记录
     *
     * @param learningPathId 学习路径ID
     * @return 影响的行数
     */
    int deleteByLearningPathId(@Param("learningPathId") Long learningPathId);

    /**
     * 根据学习路径ID和资源ID删除关联记录
     *
     * @param learningPathId 学习路径ID
     * @param resourceId 资源ID
     * @return 影响的行数
     */
    int deleteByPathAndResource(@Param("learningPathId") Long learningPathId, @Param("resourceId") Long resourceId);

    /**
     * 根据学习路径ID查询关联的资源（按顺序排列）
     *
     * @param learningPathId 学习路径ID
     * @return 学习路径资源关联实体列表
     */
    List<LearningPathResource> selectByLearningPathIdOrderBySequence(@Param("learningPathId") Long learningPathId);

    /**
     * 更新资源在学习路径中的顺序
     *
     * @param learningPathId 学习路径ID
     * @param resourceId 资源ID
     * @param newOrder 新的顺序
     * @return 影响的行数
     */
    int updateSequenceOrder(@Param("learningPathId") Long learningPathId, @Param("resourceId") Long resourceId, @Param("newOrder") Integer newOrder);

    /**
     * 批量更新资源顺序
     *
     * @param updates 更新列表，包含资源ID和新顺序
     * @return 影响的行数
     */
    int batchUpdateSequenceOrder(@Param("updates") List<LearningPathResource> updates);

    /**
     * 检查资源是否已在学习路径中
     *
     * @param learningPathId 学习路径ID
     * @param resourceId 资源ID
     * @return 关联记录数量
     */
    int countByPathAndResource(@Param("learningPathId") Long learningPathId, @Param("resourceId") Long resourceId);

    /**
     * 获取学习路径中的最大顺序号
     *
     * @param learningPathId 学习路径ID
     * @return 最大顺序号
     */
    Integer getMaxSequenceOrder(@Param("learningPathId") Long learningPathId);
}
