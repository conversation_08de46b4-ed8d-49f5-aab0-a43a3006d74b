package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.Team;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 团队表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-20
 */
@Mapper
public interface TeamMapper {

    /**
     * 插入团队
     *
     * @param team 团队信息
     * @return 影响行数
     */
    int insert(Team team);

    /**
     * 根据ID删除团队
     *
     * @param id 团队ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新团队信息
     *
     * @param team 团队信息
     * @return 影响行数
     */
    int updateById(Team team);

    /**
     * 根据ID查询团队
     *
     * @param id 团队ID
     * @return 团队信息
     */
    Team selectById(Long id);

    /**
     * 根据条件查询团队列表
     *
     * @param team 查询条件
     * @return 团队列表
     */
    List<Team> selectByCondition(Team team);

    /**
     * 根据父团队ID查询子团队列表
     *
     * @param parentId 父团队ID
     * @return 子团队列表
     */
    List<Team> selectByParentId(@Param("parentId") Long parentId);

    /**
     * 查询根团队列表（parentId为null）
     *
     * @param isActive 是否启用
     * @return 根团队列表
     */
    List<Team> selectRootTeams(@Param("isActive") Boolean isActive);

    /**
     * 根据名称查询团队
     *
     * @param name 团队名称
     * @param parentId 父团队ID
     * @return 团队信息
     */
    Team selectByNameAndParent(@Param("name") String name, 
                              @Param("parentId") Long parentId);

    /**
     * 查询团队的子团队数量
     *
     * @param parentId 父团队ID
     * @return 子团队数量
     */
    int countByParentId(@Param("parentId") Long parentId);

    /**
     * 更新团队状态
     *
     * @param id 团队ID
     * @param isActive 是否启用
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("isActive") Boolean isActive);

    /**
     * 更新团队父级
     *
     * @param id 团队ID
     * @param newParentId 新父团队ID
     * @return 影响行数
     */
    int updateParent(@Param("id") Long id, @Param("newParentId") Long newParentId);

    /**
     * 搜索团队（根据名称和描述）
     *
     * @param keyword 搜索关键词
     * @param isActive 是否启用
     * @return 团队列表
     */
    List<Team> searchTeams(@Param("keyword") String keyword,
                          @Param("isActive") Boolean isActive);

    /**
     * 获取所有活跃团队
     *
     * @return 活跃团队列表
     */
    List<Team> selectActiveTeams();

    /**
     * 根据名称模糊查询团队
     *
     * @param name 团队名称关键词
     * @return 匹配的团队列表
     */
    List<Team> selectByNameLike(@Param("name") String name);
}
