package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.UserTeam;
import com.jdl.aic.core.service.dao.entity.portal.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户团队关联数据访问接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface UserTeamMapper {

    // ==================== 基本CRUD操作 ====================

    /**
     * 插入用户团队关联
     *
     * @param userTeam 用户团队关联信息
     * @return 影响行数
     */
    int insert(UserTeam userTeam);

    /**
     * 根据ID删除用户团队关联
     *
     * @param id 关联ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据用户ID和团队ID删除关联
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteByUserIdAndTeamId(@Param("userId") Long userId, @Param("teamId") Long teamId);

    /**
     * 更新用户团队关联信息
     *
     * @param userTeam 用户团队关联信息
     * @return 影响行数
     */
    int updateById(UserTeam userTeam);

    /**
     * 根据ID查询用户团队关联
     *
     * @param id 关联ID
     * @return 用户团队关联信息
     */
    UserTeam selectById(@Param("id") Long id);

    // ==================== 条件查询 ====================

    /**
     * 根据用户ID和团队ID查询关联
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 用户团队关联信息
     */
    UserTeam selectByUserIdAndTeamId(@Param("userId") Long userId, @Param("teamId") Long teamId);

    /**
     * 根据用户ID查询所属团队ID列表
     *
     * @param userId 用户ID
     * @return 团队ID列表
     */
    List<Long> selectTeamIdsByUserId(@Param("userId") Long userId);

    /**
     * 根据团队ID查询成员用户ID列表
     *
     * @param teamId 团队ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据团队ID查询成员用户列表（带分页）
     *
     * @param teamId 团队ID
     * @return 用户列表
     */
    List<User> selectUsersByTeamId(@Param("teamId") Long teamId);

    /**
     * 根据用户ID查询团队关联列表
     *
     * @param userId 用户ID
     * @return 用户团队关联列表
     */
    List<UserTeam> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据团队ID查询用户关联列表
     *
     * @param teamId 团队ID
     * @return 用户团队关联列表
     */
    List<UserTeam> selectByTeamId(@Param("teamId") Long teamId);

    // ==================== 角色管理 ====================

    /**
     * 获取用户在团队中的角色
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 角色（0:成员, 1:管理员, 2:创建者）
     */
    Integer selectUserTeamRole(@Param("userId") Long userId, @Param("teamId") Long teamId);

    /**
     * 更新用户在团队中的角色
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param role 新角色
     * @return 影响行数
     */
    int updateUserTeamRole(@Param("userId") Long userId, @Param("teamId") Long teamId, @Param("role") Integer role);

    /**
     * 根据角色查询团队成员
     *
     * @param teamId 团队ID
     * @param role 角色
     * @return 用户团队关联列表
     */
    List<UserTeam> selectByTeamIdAndRole(@Param("teamId") Long teamId, @Param("role") Integer role);

    // ==================== 批量操作 ====================

    /**
     * 批量添加用户到团队
     *
     * @param userTeams 用户团队关联列表
     * @return 影响行数
     */
    int batchInsert(@Param("userTeams") List<UserTeam> userTeams);

    /**
     * 批量删除用户团队关联
     *
     * @param ids 关联ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 根据用户ID列表和团队ID删除关联
     *
     * @param userIds 用户ID列表
     * @param teamId 团队ID
     * @return 影响行数
     */
    int batchDeleteByUserIdsAndTeamId(@Param("userIds") List<Long> userIds, @Param("teamId") Long teamId);

    /**
     * 根据用户ID删除所有团队关联
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteAllByUserId(@Param("userId") Long userId);

    /**
     * 根据团队ID删除所有用户关联
     *
     * @param teamId 团队ID
     * @return 影响行数
     */
    int deleteAllByTeamId(@Param("teamId") Long teamId);

    // ==================== 统计查询 ====================

    /**
     * 统计团队成员数量
     *
     * @param teamId 团队ID
     * @return 成员数量
     */
    int countMembersByTeamId(@Param("teamId") Long teamId);

    /**
     * 统计用户所属团队数量
     *
     * @param userId 用户ID
     * @return 团队数量
     */
    int countTeamsByUserId(@Param("userId") Long userId);

    /**
     * 根据角色统计团队成员数量
     *
     * @param teamId 团队ID
     * @param role 角色
     * @return 成员数量
     */
    int countMembersByTeamIdAndRole(@Param("teamId") Long teamId, @Param("role") Integer role);

    // ==================== 验证方法 ====================

    /**
     * 检查用户是否在团队中
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 是否存在关联
     */
    boolean existsByUserIdAndTeamId(@Param("userId") Long userId, @Param("teamId") Long teamId);

    /**
     * 检查用户是否为团队管理员或创建者
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 是否为管理员或创建者
     */
    boolean isUserTeamAdminOrCreator(@Param("userId") Long userId, @Param("teamId") Long teamId);
}
