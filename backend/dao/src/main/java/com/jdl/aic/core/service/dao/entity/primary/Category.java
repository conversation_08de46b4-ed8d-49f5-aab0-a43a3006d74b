package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 分类表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("category")
public class Category implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 父分类ID，支持层级结构
     */
    private Long parentId;

    /**
     * 所属内容类别（knowledge, solution, news_feed, learning_resource, general）
     */
    private String contentCategory;

    /**
     * 分类图标 URL
     */
    private String iconUrl;

    /**
     * 排序权重
     */
    private Integer sortOrder;

    /**
     * 是否启用（0:否, 1:是）
     */
    private Boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 最后更新用户ID（跨库引用，无外键约束）
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;

    /**
     * 细分类型ID，对于knowledge类型关联knowledge_type.id
     */
    private Long subTypeId;
}
