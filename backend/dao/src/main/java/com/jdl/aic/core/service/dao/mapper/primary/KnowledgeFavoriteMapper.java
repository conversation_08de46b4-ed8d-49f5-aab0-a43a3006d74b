package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.KnowledgeFavorite;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 知识收藏表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface KnowledgeFavoriteMapper {

    /**
     * 插入一条知识收藏记录
     *
     * @param knowledgeFavorite 知识收藏实体
     * @return 影响的行数
     */
    int insert(KnowledgeFavorite knowledgeFavorite);

    /**
     * 根据ID删除知识收藏记录
     *
     * @param id 知识收藏ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据知识ID和用户ID删除收藏记录
     *
     * @param knowledgeId 知识ID
     * @param userId 用户ID
     * @return 影响的行数
     */
    int deleteByKnowledgeIdAndUserId(@Param("knowledgeId") Long knowledgeId, @Param("userId") Long userId);

    /**
     * 根据ID查询知识收藏记录
     *
     * @param id 知识收藏ID
     * @return 知识收藏实体
     */
    KnowledgeFavorite selectById(@Param("id") Long id);

    /**
     * 根据知识ID查询收藏记录
     *
     * @param knowledgeId 知识ID
     * @return 知识收藏实体列表
     */
    List<KnowledgeFavorite> selectByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 根据用户ID查询收藏记录
     *
     * @param userId 用户ID
     * @return 知识收藏实体列表
     */
    List<KnowledgeFavorite> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询指定知识的收藏数量
     *
     * @param knowledgeId 知识ID
     * @return 收藏数量
     */
    int countByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 检查用户是否已经收藏了指定的知识
     *
     * @param knowledgeId 知识ID
     * @param userId 用户ID
     * @return 如果已收藏返回true，否则返回false
     */
    boolean existsByKnowledgeIdAndUserId(@Param("knowledgeId") Long knowledgeId, @Param("userId") Long userId);
}
