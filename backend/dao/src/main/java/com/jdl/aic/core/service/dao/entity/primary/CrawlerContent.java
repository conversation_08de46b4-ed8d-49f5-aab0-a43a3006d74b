package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 爬虫内容表，存储爬虫抓取的网页内容和相关元数据
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("crawler_content")
public class CrawlerContent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内容标题
     */
    private String title;

    /**
     * 原始链接URL
     */
    private String link;

    /**
     * 内容语言（如：zh-CN, en-US, ja-JP等）
     */
    private String language;

    /**
     * 作者信息（JSON格式）
     */
    private String author;

    /**
     * 内容描述或摘要
     */
    private String description;

    /**
     * 内容发布时间
     */
    private LocalDateTime pubDate;

    /**
     * AI智能总结内容
     */
    private String aiSummary;

    /**
     * 是否精品内容（0:否, 1:是）
     */
    private Boolean isFeatured;

    /**
     * 内容MD5值，用于去重和校验
     */
    private String contentMd5;

    /**
     * 爬虫抓取的完整内容
     */
    private String content;

    /**
     * 内容类型（如：article, news, blog, document等）
     */
    private String contentType;

    /**
     * 内容状态（0:待处理, 1:已处理, 2:处理失败, 3:已忽略）
     */
    private Integer status;

    /**
     * 内容质量评分（0.00-5.00）
     */
    private BigDecimal qualityScore;

    /**
     * 内容字数统计
     */
    private Integer wordCount;

    /**
     * 内容标签列表（JSON格式）
     */
    private String tags;

    /**
     * 扩展元数据信息（JSON格式）
     */
    private String metadata;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（系统爬虫时为NULL）
     */
    private String createdBy;

    /**
     * 最后更新用户ID
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;

    /**
     * 内容类型，区分文章、视频、音频、图片
     */
    private String type;

    /**
     * 附件信息，JSON格式
     */
    private String attachments;

    /**
     * 媒体资源信息，JSON格式
     */
    private String media;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务描述
     */
    private String taskDesc;
}
