package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 团队推荐内容实体类
 * 对应ai_community_portal.team_recommendation表
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Data
@TableName("team_recommendation")
public class TeamRecommendation {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 被推荐的团队ID
     */
    private Long team_id;
    
    /**
     * 推荐人用户ID
     */
    private Long user_id;
    
    /**
     * 被推荐内容的ID
     */
    private Long content_id;
    
    /**
     * 被推荐内容的类型
     */
    private String content_type;
    
    /**
     * 推荐理由
     */
    private String reason;
    
    /**
     * 推荐状态（active/deleted）
     */
    private String status;
    
    /**
     * 软删除标志，0-未删除，1-已删除
     */
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private LocalDateTime created_at;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime updated_at;
}
