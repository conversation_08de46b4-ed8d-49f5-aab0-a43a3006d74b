package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评论表实体类
 * 
 * <p>对应portal数据库中的comment表，用于存储用户对知识、资讯、解决方案等内容的评论。
 * 支持嵌套回复和评论状态管理。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("comment")
public class Comment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内容类型（0:知识, 1:资讯, 2:解决方案）
     */
    private Integer contentType;

    /**
     * 关联对应内容ID（跨库引用，无外键约束）
     */
    private Long contentId;

    /**
     * 当content_type为知识时，关联知识类型ID（跨库引用）
     */
    private Long relatedKnowledgeTypeId;

    /**
     * 外键关联 user.id，评论人
     */
    private Long userId;

    /**
     * 外键自关联 comment.id，用于回复评论
     */
    private Long parentCommentId;

    /**
     * 评论内容
     */
    private String commentText;

    /**
     * 评论状态（0:正常, 1:待审核, 2:已删除）
     */
    private Integer status;

    /**
     * 评论点赞数
     */
    private Integer likeCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID
     */
    private String createdBy;

    /**
     * 最后更新用户ID
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;

    /**
     * 默认构造函数
     */
    public Comment() {
        this.status = 0; // 默认状态为正常
        this.likeCount = 0; // 默认点赞数为0
    }

    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param commentText 评论内容
     */
    public Comment(Integer contentType, Long contentId, Long userId, String commentText) {
        this();
        this.contentType = contentType;
        this.contentId = contentId;
        this.userId = userId;
        this.commentText = commentText;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 构造函数（包含知识类型ID）
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param relatedKnowledgeTypeId 关联知识类型ID
     * @param userId 用户ID
     * @param commentText 评论内容
     */
    public Comment(Integer contentType, Long contentId, Long relatedKnowledgeTypeId, Long userId, String commentText, Long parentCommentId) {
        this(contentType, contentId, userId, commentText);
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
        this.parentCommentId = parentCommentId;
    }

    /**
     * 回复评论构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     * @param commentText 评论内容
     * @param parentCommentId 父评论ID
     */
    public Comment(Integer contentType, Long contentId, Long userId, String commentText, Long parentCommentId) {
        this(contentType, contentId, userId, commentText);
        this.parentCommentId = parentCommentId;
    }

    /**
     * 判断是否为回复评论
     * 
     * @return true表示是回复评论，false表示是顶级评论
     */
    public boolean isReply() {
        return parentCommentId != null;
    }

    /**
     * 判断是否已删除
     * 
     * @return true表示已删除，false表示正常
     */
    public boolean isDeleted() {
        return deletedAt != null || status == 2;
    }

    /**
     * 标记为已删除
     */
    public void markAsDeleted() {
        this.status = 2;
        this.deletedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 增加点赞数
     */
    public void incrementLikeCount() {
        this.likeCount = (this.likeCount == null ? 0 : this.likeCount) + 1;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 减少点赞数
     */
    public void decrementLikeCount() {
        this.likeCount = Math.max(0, (this.likeCount == null ? 0 : this.likeCount) - 1);
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Comment{" +
                "id=" + id +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                ", relatedKnowledgeTypeId=" + relatedKnowledgeTypeId +
                ", userId=" + userId +
                ", parentCommentId=" + parentCommentId +
                ", commentText='" + commentText + '\'' +
                ", status=" + status +
                ", likeCount=" + likeCount +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", createdBy='" + createdBy + '\'' +
                ", updatedBy='" + updatedBy + '\'' +
                ", deletedAt=" + deletedAt +
                '}';
    }
}
