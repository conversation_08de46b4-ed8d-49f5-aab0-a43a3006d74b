package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户实体类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * SSO/LDAP唯一标识
     */
    private String ssoId;

    /**
     * 用户名（如工号）
     */
    private String username;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户头像URL
     */
    private String avatarUrl;

    /**
     * 部门信息
     */
    private String department;

    /**
     * 职位
     */
    private String title;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 标签列表（JSON格式存储）
     */
    private String tags;

    /**
     * 用户是否活跃（0:否, 1:是）
     */
    private Boolean isActive;

    /**
     * 最后登录时间
     */
    private LocalDateTime lastLoginAt;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;
}
