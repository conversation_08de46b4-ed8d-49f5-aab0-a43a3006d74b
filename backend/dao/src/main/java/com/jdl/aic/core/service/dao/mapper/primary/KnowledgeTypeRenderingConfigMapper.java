package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.KnowledgeTypeRenderingConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 知识类型渲染配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface KnowledgeTypeRenderingConfigMapper {

    /**
     * 插入一条知识类型渲染配置记录
     *
     * @param config 知识类型渲染配置实体
     * @return 影响的行数
     */
    int insert(KnowledgeTypeRenderingConfig config);

    /**
     * 根据ID更新知识类型渲染配置记录
     *
     * @param config 知识类型渲染配置实体
     * @return 影响的行数
     */
    int updateById(KnowledgeTypeRenderingConfig config);

    /**
     * 根据ID删除知识类型渲染配置记录
     *
     * @param id 知识类型渲染配置ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询知识类型渲染配置记录
     *
     * @param id 知识类型渲染配置ID
     * @return 知识类型渲染配置实体
     */
    KnowledgeTypeRenderingConfig selectById(@Param("id") Long id);

    /**
     * 根据知识类型ID查询知识类型渲染配置记录
     *
     * @param knowledgeTypeId 知识类型ID
     * @return 知识类型渲染配置实体
     */
    KnowledgeTypeRenderingConfig selectByKnowledgeTypeId(@Param("knowledgeTypeId") Long knowledgeTypeId);

    /**
     * 查询所有知识类型渲染配置记录
     *
     * @return 知识类型渲染配置实体列表
     */
    List<KnowledgeTypeRenderingConfig> selectAll();
}
