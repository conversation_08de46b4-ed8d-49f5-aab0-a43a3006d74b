package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.KnowledgeTag;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 知识标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface KnowledgeTagMapper {

    /**
     * 插入一条知识标签记录
     *
     * @param knowledgeTag 知识标签实体
     * @return 影响的行数
     */
    int insert(KnowledgeTag knowledgeTag);

    /**
     * 根据ID更新知识标签记录
     *
     * @param knowledgeTag 知识标签实体
     * @return 影响的行数
     */
    int updateById(KnowledgeTag knowledgeTag);

    /**
     * 根据ID删除知识标签记录
     *
     * @param id 知识标签ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询知识标签记录
     *
     * @param id 知识标签ID
     * @return 知识标签实体
     */
    KnowledgeTag selectById(@Param("id") Long id);

    /**
     * 查询所有知识标签记录
     *
     * @return 知识标签实体列表
     */
    List<KnowledgeTag> selectAll();

    /**
     * 根据名称查询知识标签记录
     *
     * @param name 标签名称
     * @return 知识标签实体
     */
    KnowledgeTag selectByName(@Param("name") String name);

    /**
     * 批量插入知识标签记录
     *
     * @param knowledgeTagList 知识标签实体列表
     * @return 影响的行数
     */
    int batchInsert(@Param("list") List<KnowledgeTag> knowledgeTagList);

    /**
     * 根据名称模糊查询知识标签记录
     *
     * @param name 标签名称（模糊查询）
     * @return 知识标签实体列表
     */
    List<KnowledgeTag> selectLikeName(@Param("name") String name);
}
