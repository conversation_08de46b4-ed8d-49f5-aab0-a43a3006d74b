package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.KnowledgeView;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;
import java.time.LocalDateTime;

/**
 * <p>
 * 知识浏览记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface KnowledgeViewMapper {

    /**
     * 插入一条知识浏览记录
     *
     * @param knowledgeView 知识浏览记录实体
     * @return 影响的行数
     */
    int insert(KnowledgeView knowledgeView);

    /**
     * 根据ID删除知识浏览记录
     *
     * @param id 知识浏览记录ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询知识浏览记录
     *
     * @param id 知识浏览记录ID
     * @return 知识浏览记录实体
     */
    KnowledgeView selectById(@Param("id") Long id);

    /**
     * 根据知识ID查询浏览记录
     *
     * @param knowledgeId 知识ID
     * @return 知识浏览记录实体列表
     */
    List<KnowledgeView> selectByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 根据用户ID查询浏览记录
     *
     * @param userId 用户ID
     * @return 知识浏览记录实体列表
     */
    List<KnowledgeView> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询指定知识的浏览次数
     *
     * @param knowledgeId 知识ID
     * @return 浏览次数
     */
    int countByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 查询指定时间范围内的浏览记录
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 知识浏览记录实体列表
     */
    List<KnowledgeView> selectByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户是否浏览过指定知识
     *
     * @param knowledgeId 知识ID
     * @param userId 用户ID
     * @return 如果浏览过返回true，否则返回false
     */
    boolean existsByKnowledgeIdAndUserId(@Param("knowledgeId") Long knowledgeId, @Param("userId") Long userId);

    /**
     * 查询指定知识的最近浏览记录
     *
     * @param knowledgeId 知识ID
     * @param limit 限制返回的记录数
     * @return 知识浏览记录实体列表
     */
    List<KnowledgeView> selectRecentViewsByKnowledgeId(@Param("knowledgeId") Long knowledgeId, @Param("limit") int limit);
}
