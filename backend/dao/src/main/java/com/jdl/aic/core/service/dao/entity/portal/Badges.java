package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 徽章实体类
 * 对应ai_community_portal.badges表
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Data
@TableName("badges")
public class Badges {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 徽章名称
     */
    private String name;
    
    /**
     * 徽章描述
     */
    private String description;
    
    /**
     * 徽章图标URL
     */
    private String icon_url;
    
    /**
     * 获得条件
     */
    private String award_condition;
    
    /**
     * 创建时间
     */
    private LocalDateTime created_at;
    
    /**
     * 更新时间
     */
    private LocalDateTime updated_at;
    
    /**
     * 创建人ID
     */
    private Long created_by;
    
    /**
     * 软删除标志，0-未删除，1-已删除
     */
    private Integer deleted;
}
