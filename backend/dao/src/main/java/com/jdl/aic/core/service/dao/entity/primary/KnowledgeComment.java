package com.jdl.aic.core.service.dao.entity.primary;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 知识评论表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("knowledge_comment")
public class KnowledgeComment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 外键关联 knowledge.id
     */
    private Long knowledgeId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 父评论ID，用于回复功能
     */
    private Long parentId;

    /**
     * 评论作者ID（跨库引用，无外键约束）
     */
    private Long authorId;

    /**
     * 作者姓名（冗余字段，减少跨库查询）
     */
    private String authorName;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 最后更新用户ID（跨库引用，无外键约束）
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;
}
