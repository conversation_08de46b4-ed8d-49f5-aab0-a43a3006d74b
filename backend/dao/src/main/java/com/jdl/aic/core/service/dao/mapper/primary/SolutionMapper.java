package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.Solution;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 场景解决方案表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Service
public interface SolutionMapper {

    /**
     * 插入一条场景解决方案记录
     *
     * @param solution 场景解决方案实体
     * @return 影响的行数
     */
    int insert(Solution solution);

    /**
     * 根据ID更新场景解决方案记录
     *
     * @param solution 场景解决方案实体
     * @return 影响的行数
     */
    int updateById(Solution solution);

    /**
     * 根据ID删除场景解决方案记录（软删除）
     *
     * @param id 场景解决方案ID
     * @return 影响的行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID查询场景解决方案记录
     *
     * @param id 场景解决方案ID
     * @return 场景解决方案实体
     */
    Solution selectById(@Param("id") Long id);

    /**
     * 查询所有场景解决方案记录
     *
     * @return 场景解决方案实体列表
     */
    List<Solution> selectAll();

    /**
     * 根据条件查询场景解决方案记录
     *
     * @param solution 查询条件
     * @return 场景解决方案实体列表
     */
    List<Solution> selectByCondition(Solution solution);

    /**
     * 根据作者ID查询场景解决方案记录
     *
     * @param authorId 作者ID
     * @return 场景解决方案实体列表
     */
    List<Solution> selectByAuthorId(@Param("authorId") Long authorId);

    /**
     * 根据状态查询场景解决方案记录
     *
     * @param status 场景解决方案状态
     * @return 场景解决方案实体列表
     */
    List<Solution> selectByStatus(@Param("status") Integer status);

    /**
     * 更新场景解决方案的阅读次数
     *
     * @param id 场景解决方案ID
     * @return 影响的行数
     */
    int incrementReadCount(@Param("id") Long id);

    /**
     * 更新场景解决方案的点赞次数
     *
     * @param id 场景解决方案ID
     * @return 影响的行数
     */
    int incrementLikeCount(@Param("id") Long id);

    /**
     * 更新场景解决方案的评论次数
     *
     * @param id 场景解决方案ID
     * @return 影响的行数
     */
    int incrementCommentCount(@Param("id") Long id);

    /**
     * 根据复杂条件查询解决方案记录
     *
     * @param status 状态
     * @param authorId 作者ID
     * @param visibility 可见性
     * @param teamId 团队ID
     * @param aiReviewStatus AI审核状态
     * @param categoryId 分类ID
     * @param search 搜索关键词
     * @return 解决方案实体列表
     */
    List<Solution> selectByComplexCondition(@Param("status") Integer status,
                                          @Param("authorId") String authorId,
                                          @Param("visibility") Integer visibility,
                                          @Param("teamId") Long teamId,
                                          @Param("aiReviewStatus") Integer aiReviewStatus,
                                          @Param("categoryId") Long categoryId,
                                          @Param("search") String search);
}
