package com.jdl.aic.core.service.dao.entity.portal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 收藏表实体类
 * 
 * <p>对应portal数据库中的favorite表，用于存储用户对各类内容的收藏行为。
 * 支持对知识、资讯、解决方案等内容的收藏。
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("favorite")
public class Favorite implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内容类型（0:知识, 1:资讯, 2:解决方案）
     */
    private Integer contentType;

    /**
     * 关联对应内容ID（跨库引用，无外键约束）
     */
    private Long contentId;

    /**
     * 当content_type为知识时，关联知识类型ID（跨库引用）
     */
    private Long relatedKnowledgeTypeId;

    /**
     * 外键关联 user.id
     */
    private Long userId;

    /**
     * 收藏时间
     */
    private LocalDateTime createdAt;

    /**
     * 软删除标记（取消收藏）
     */
    private LocalDateTime deletedAt;

    /**
     * 默认构造函数
     */
    public Favorite() {
    }

    /**
     * 构造函数
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param userId 用户ID
     */
    public Favorite(Integer contentType, Long contentId, Long userId) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.userId = userId;
        this.createdAt = LocalDateTime.now();
    }

    /**
     * 构造函数（包含知识类型ID）
     * 
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param relatedKnowledgeTypeId 关联知识类型ID
     * @param userId 用户ID
     */
    public Favorite(Integer contentType, Long contentId, Long relatedKnowledgeTypeId, Long userId) {
        this.contentType = contentType;
        this.contentId = contentId;
        this.relatedKnowledgeTypeId = relatedKnowledgeTypeId;
        this.userId = userId;
        this.createdAt = LocalDateTime.now();
    }

    /**
     * 判断是否已删除（取消收藏）
     * 
     * @return true表示已取消收藏，false表示仍在收藏中
     */
    public boolean isDeleted() {
        return deletedAt != null;
    }

    /**
     * 标记为已删除（取消收藏）
     */
    public void markAsDeleted() {
        this.deletedAt = LocalDateTime.now();
    }

    /**
     * 恢复收藏（取消删除标记）
     */
    public void restore() {
        this.deletedAt = null;
    }

    @Override
    public String toString() {
        return "Favorite{" +
                "id=" + id +
                ", contentType=" + contentType +
                ", contentId=" + contentId +
                ", relatedKnowledgeTypeId=" + relatedKnowledgeTypeId +
                ", userId=" + userId +
                ", createdAt=" + createdAt +
                ", deletedAt=" + deletedAt +
                '}';
    }
}
