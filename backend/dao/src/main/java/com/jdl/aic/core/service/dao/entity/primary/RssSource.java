package com.jdl.aic.core.service.dao.entity.primary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * RSS源表
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("rss_source")
public class RssSource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * RSS源名称
     */
    private String name;

    /**
     * RSS订阅地址
     */
    private String feedUrl;

    /**
     * 源描述
     */
    private String description;

    /**
     * RSS源的分类
     */
    private String category;

    /**
     * 源类型（0:官方, 1:用户订阅）
     */
    private Integer type;

    /**
     * 如果是用户订阅，关联 user.id（跨库引用，无外键约束）
     */
    private String ownerId;

    /**
     * 所有者姓名（冗余字段，减少跨库查询）
     */
    private String ownerName;

    /**
     * 上次成功抓取时间
     */
    private LocalDateTime lastFetchedAt;

    /**
     * 源状态（0:活跃, 1:暂停, 2:失败）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 创建用户ID（跨库引用，无外键约束）
     */
    private String createdBy;

    /**
     * 最后更新用户ID（跨库引用，无外键约束）
     */
    private String updatedBy;

    /**
     * 软删除时间戳
     */
    private LocalDateTime deletedAt;

    // 常量定义

    /**
     * 源类型常量
     */
    public static class Type {
        public static final int OFFICIAL = 0;  // 官方
        public static final int USER_SUBSCRIBED = 1;   // 用户订阅
    }

    /**
     * 源状态常量
     */
    public static class Status {
        public static final int ACTIVE = 0;  // 活跃
        public static final int PAUSED = 1;  // 暂停
        public static final int FAILED = 2;  // 失败
    }
}
