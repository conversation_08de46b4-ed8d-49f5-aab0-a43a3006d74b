package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.Comment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评论数据访问接口
 * 
 * <p>提供评论相关的数据库操作，包括：
 * <ul>
 *   <li>基本CRUD操作</li>
 *   <li>评论查询和分页</li>
 *   <li>评论统计</li>
 *   <li>评论状态管理</li>
 *   <li>嵌套回复支持</li>
 * </ul>
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface CommentMapper {

    // ==================== 基本CRUD操作 ====================

    /**
     * 插入评论
     *
     * @param comment 评论信息
     * @return 影响行数
     */
    int insert(Comment comment);

    /**
     * 根据ID删除评论（物理删除）
     *
     * @param id 评论ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 软删除评论（标记为已删除）
     *
     * @param id 评论ID
     * @return 影响行数
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 更新评论信息
     *
     * @param comment 评论信息
     * @return 影响行数
     */
    int updateById(Comment comment);

    /**
     * 根据ID查询评论
     *
     * @param id 评论ID
     * @return 评论信息
     */
    Comment selectById(@Param("id") Long id);

    // ==================== 条件查询 ====================

    /**
     * 根据内容信息查询评论列表（分页）
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 评论列表
     */
    List<Comment> selectByContent(@Param("contentType") Integer contentType, 
                                 @Param("contentId") Long contentId);

    /**
     * 根据用户ID查询评论列表
     *
     * @param userId 用户ID
     * @return 评论列表
     */
    List<Comment> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据父评论ID查询回复列表
     *
     * @param parentCommentId 父评论ID
     * @return 回复列表
     */
    List<Comment> selectByParentId(@Param("parentCommentId") Long parentCommentId);

    /**
     * 根据条件查询评论列表
     *
     * @param contentType 内容类型（可选）
     * @param contentId 内容ID（可选）
     * @param userId 用户ID（可选）
     * @param status 评论状态（可选）
     * @param parentCommentId 父评论ID（可选）
     * @return 评论列表
     */
    List<Comment> selectByCondition(@Param("contentType") Integer contentType,
                                   @Param("contentId") Long contentId,
                                   @Param("userId") Long userId,
                                   @Param("status") Integer status,
                                   @Param("parentCommentId") Long parentCommentId);

    /**
     * 查询顶级评论列表（不包括回复）
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param status 评论状态（可选）
     * @return 顶级评论列表
     */
    List<Comment> selectTopLevelComments(@Param("contentType") Integer contentType,
                                        @Param("contentId") Long contentId,
                                        @Param("status") Integer status);

    // ==================== 统计查询 ====================

    /**
     * 统计指定内容的评论数
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @return 评论数
     */
    int countByContent(@Param("contentType") Integer contentType, 
                      @Param("contentId") Long contentId);

    /**
     * 统计用户评论总数
     *
     * @param userId 用户ID
     * @return 评论总数
     */
    int countByUserId(@Param("userId") Long userId);

    /**
     * 统计指定评论的回复数
     *
     * @param parentCommentId 父评论ID
     * @return 回复数
     */
    int countRepliesByParentId(@Param("parentCommentId") Long parentCommentId);

    /**
     * 统计指定内容列表的评论数
     *
     * @param contentType 内容类型
     * @param contentIds 内容ID列表
     * @return 评论统计列表（包含contentId和count）
     */
    List<Object> countByContentIds(@Param("contentType") Integer contentType, 
                                  @Param("contentIds") List<Long> contentIds);

    /**
     * 统计各状态评论数量
     *
     * @param contentType 内容类型（可选）
     * @param contentId 内容ID（可选）
     * @return 状态统计列表
     */
    List<Object> countByStatus(@Param("contentType") Integer contentType,
                              @Param("contentId") Long contentId);

    // ==================== 状态管理 ====================

    /**
     * 更新评论状态
     *
     * @param id 评论ID
     * @param status 新状态
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 批量更新评论状态
     *
     * @param ids 评论ID列表
     * @param status 新状态
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 恢复已删除的评论
     *
     * @param id 评论ID
     * @return 影响行数
     */
    int restoreById(@Param("id") Long id);

    // ==================== 点赞管理 ====================

    /**
     * 增加评论点赞数
     *
     * @param id 评论ID
     * @return 影响行数
     */
    int incrementLikeCount(@Param("id") Long id);

    /**
     * 减少评论点赞数
     *
     * @param id 评论ID
     * @return 影响行数
     */
    int decrementLikeCount(@Param("id") Long id);

    /**
     * 更新评论点赞数
     *
     * @param id 评论ID
     * @param likeCount 新的点赞数
     * @return 影响行数
     */
    int updateLikeCount(@Param("id") Long id, @Param("likeCount") Integer likeCount);

    // ==================== 批量操作 ====================

    /**
     * 批量插入评论
     *
     * @param comments 评论列表
     * @return 影响行数
     */
    int batchInsert(@Param("comments") List<Comment> comments);

    /**
     * 批量删除评论
     *
     * @param ids 评论ID列表
     * @return 影响行数
     */
    int batchDeleteByIds(@Param("ids") List<Long> ids);

    /**
     * 批量软删除评论
     *
     * @param ids 评论ID列表
     * @return 影响行数
     */
    int batchSoftDeleteByIds(@Param("ids") List<Long> ids);

    // ==================== 高级查询 ====================

    /**
     * 获取用户最近评论
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 评论列表
     */
    List<Comment> selectRecentByUserId(@Param("userId") Long userId, 
                                      @Param("limit") Integer limit);

    /**
     * 获取热门评论（按点赞数排序）
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param limit 限制数量
     * @return 热门评论列表
     */
    List<Comment> selectPopularComments(@Param("contentType") Integer contentType,
                                       @Param("contentId") Long contentId,
                                       @Param("limit") Integer limit);

    /**
     * 搜索评论内容
     *
     * @param keyword 关键词
     * @param contentType 内容类型（可选）
     * @param userId 用户ID（可选）
     * @return 评论列表
     */
    List<Comment> searchComments(@Param("keyword") String keyword,
                                @Param("contentType") Integer contentType,
                                @Param("userId") Long userId);

    /**
     * 获取评论树结构（包含回复）
     *
     * @param contentType 内容类型
     * @param contentId 内容ID
     * @param maxDepth 最大深度
     * @return 评论树列表
     */
    List<Comment> selectCommentTree(@Param("contentType") Integer contentType,
                                   @Param("contentId") Long contentId,
                                   @Param("maxDepth") Integer maxDepth);

    /**
     * 清理指定天数前的已删除评论记录
     *
     * @param days 天数
     * @return 影响行数
     */
    int cleanupDeletedRecords(@Param("days") Integer days);

    /**
     * 检查评论是否存在
     *
     * @param id 评论ID
     * @return true表示存在，false表示不存在
     */
    boolean existsById(@Param("id") Long id);
}
