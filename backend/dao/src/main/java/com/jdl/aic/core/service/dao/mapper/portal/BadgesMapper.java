package com.jdl.aic.core.service.dao.mapper.portal;

import com.jdl.aic.core.service.dao.entity.portal.Badges;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 徽章数据访问接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Mapper
public interface BadgesMapper {

    /**
     * 根据ID查询徽章
     */
    Badges selectById(@Param("id") Long id);

    /**
     * 根据名称查询徽章
     */
    Badges selectByName(@Param("name") String name);

    /**
     * 查询所有徽章
     */
    List<Badges> selectAll();

    /**
     * 查询未删除的徽章
     */
    List<Badges> selectActive();

    /**
     * 插入徽章
     */
    int insert(Badges badges);

    /**
     * 更新徽章
     */
    int updateById(Badges badges);

    /**
     * 根据ID删除徽章（物理删除）
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据ID软删除徽章
     */
    int softDeleteById(@Param("id") Long id);

    /**
     * 统计徽章数量
     */
    int count();

    /**
     * 统计活跃徽章数量
     */
    int countActive();

    /**
     * 批量插入徽章
     */
    int batchInsert(@Param("badges") List<Badges> badges);

    /**
     * 批量软删除徽章
     */
    int batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 搜索徽章（根据名称、描述）
     */
    List<Badges> searchBadges(@Param("keyword") String keyword, @Param("limit") Integer limit);

    /**
     * 根据创建人查询徽章
     */
    List<Badges> selectByCreatedBy(@Param("createdBy") Long createdBy);
}
