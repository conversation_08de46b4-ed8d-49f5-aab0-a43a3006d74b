package com.jdl.aic.core.service.dao.mapper.primary;

import com.jdl.aic.core.service.dao.entity.primary.LearningCourse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 学习课程表 Mapper 接口
 * </p>
 *
 * <AUTHOR> Community Development Team
 * @since 2025-07-19
 */
@Mapper
public interface LearningCourseMapper {

    /**
     * 插入学习课程
     *
     * @param course 课程信息
     * @return 影响行数
     */
    int insert(LearningCourse course);

    /**
     * 根据ID删除学习课程（软删除）
     *
     * @param id 课程ID
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 更新学习课程信息
     *
     * @param course 课程信息
     * @return 影响行数
     */
    int updateById(LearningCourse course);

    /**
     * 根据ID查询学习课程
     *
     * @param id 课程ID
     * @return 课程信息
     */
    LearningCourse selectById(Long id);

    /**
     * 根据条件查询学习课程列表
     *
     * @param category 课程分类过滤
     * @param difficultyLevel 难度级别过滤
     * @param status 状态过滤
     * @param isOfficial 是否官方课程过滤
     * @param creatorId 创建者ID过滤
     * @param search 搜索关键词
     * @return 课程列表
     */
    List<LearningCourse> selectByConditions(
            @Param("category") String category,
            @Param("difficultyLevel") String difficultyLevel,
            @Param("status") String status,
            @Param("isOfficial") Boolean isOfficial,
            @Param("creatorId") String creatorId,
            @Param("search") String search);

    /**
     * 根据分类查询学习课程列表
     *
     * @param category 课程分类
     * @param limit 限制数量
     * @return 课程列表
     */
    List<LearningCourse> selectByCategory(@Param("category") String category, @Param("limit") Integer limit);

    /**
     * 根据难度级别查询学习课程列表
     *
     * @param difficultyLevel 难度级别
     * @param limit 限制数量
     * @return 课程列表
     */
    List<LearningCourse> selectByDifficulty(@Param("difficultyLevel") String difficultyLevel, @Param("limit") Integer limit);

    /**
     * 根据创建者查询学习课程列表
     *
     * @param creatorId 创建者ID
     * @param limit 限制数量
     * @return 课程列表
     */
    List<LearningCourse> selectByCreator(@Param("creatorId") String creatorId, @Param("limit") Integer limit);

    /**
     * 根据状态查询学习课程列表
     *
     * @param status 课程状态
     * @param limit 限制数量
     * @return 课程列表
     */
    List<LearningCourse> selectByStatus(@Param("status") String status, @Param("limit") Integer limit);

    /**
     * 搜索学习课程
     *
     * @param keyword 搜索关键词
     * @param categories 分类过滤
     * @param difficultyLevels 难度级别过滤
     * @param tags 标签过滤
     * @param isOfficial 是否官方课程过滤
     * @param minHours 最小学习时长
     * @param maxHours 最大学习时长
     * @return 课程列表
     */
    List<LearningCourse> searchCourses(
            @Param("keyword") String keyword,
            @Param("categories") List<String> categories,
            @Param("difficultyLevels") List<String> difficultyLevels,
            @Param("tags") List<String> tags,
            @Param("isOfficial") Boolean isOfficial,
            @Param("minHours") Integer minHours,
            @Param("maxHours") Integer maxHours);

    /**
     * 获取热门课程列表（按报名人数排序）
     *
     * @param limit 限制数量
     * @return 课程列表
     */
    List<LearningCourse> selectPopularCourses(@Param("limit") Integer limit);

    /**
     * 获取最新课程列表（按创建时间排序）
     *
     * @param limit 限制数量
     * @return 课程列表
     */
    List<LearningCourse> selectLatestCourses(@Param("limit") Integer limit);

    /**
     * 获取官方课程列表
     *
     * @param limit 限制数量
     * @return 课程列表
     */
    List<LearningCourse> selectOfficialCourses(@Param("limit") Integer limit);

    /**
     * 更新课程状态
     *
     * @param id 课程ID
     * @param status 新状态
     * @param updatedBy 更新用户
     * @return 影响行数
     */
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("updatedBy") String updatedBy);

    /**
     * 增加课程报名人数
     *
     * @param id 课程ID
     * @return 影响行数
     */
    int incrementEnrolledCount(@Param("id") Long id);

    /**
     * 增加课程完成人数
     *
     * @param id 课程ID
     * @return 影响行数
     */
    int incrementCompletionCount(@Param("id") Long id);

    /**
     * 更新课程资源数量
     *
     * @param id 课程ID
     * @param resourceCount 资源数量
     * @return 影响行数
     */
    int updateResourceCount(@Param("id") Long id, @Param("resourceCount") Integer resourceCount);

    /**
     * 更新课程完成率
     *
     * @param id 课程ID
     * @param completionRate 完成率
     * @return 影响行数
     */
    int updateCompletionRate(@Param("id") Long id, @Param("completionRate") java.math.BigDecimal completionRate);

    /**
     * 批量更新课程状态
     *
     * @param ids 课程ID列表
     * @param status 新状态
     * @param updatedBy 更新用户
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") String status, @Param("updatedBy") String updatedBy);

    /**
     * 批量删除课程（软删除）
     *
     * @param ids 课程ID列表
     * @return 影响行数
     */
    int batchDelete(@Param("ids") List<Long> ids);

    /**
     * 根据名称查询课程（用于重复检查）
     *
     * @param name 课程名称
     * @param excludeId 排除的课程ID
     * @return 课程信息
     */
    LearningCourse selectByName(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 统计课程总数
     *
     * @param status 状态过滤
     * @return 课程总数
     */
    int countByStatus(@Param("status") String status);

    /**
     * 根据ID列表查询课程
     *
     * @param ids 课程ID列表
     * @return 课程列表
     */
    List<LearningCourse> selectByIds(@Param("ids") List<Long> ids);
}
