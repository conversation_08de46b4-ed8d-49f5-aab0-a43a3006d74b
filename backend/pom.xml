<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jdl.aic.core.service</groupId>
    <artifactId>aic-core-service-backend</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <name>aic-core-service-backend</name>
    <description>AI社区核心服务系统</description>

    <!-- 子模块 -->
    <modules>
        <module>common</module>
        <module>dao</module>
        <module>service</module>
        <module>web-api</module>
        <module>client</module>
    </modules>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.boot.version>2.7.18</spring.boot.version>
        <mysql.version>8.0.33</mysql.version>
        <mybatis.spring.boot.version>2.3.1</mybatis.spring.boot.version>
        <druid.version>1.2.18</druid.version>
        <fastjson.version>1.2.83-jdsec.rc1</fastjson.version>
        <jwt.version>0.11.5</jwt.version>
        <spring.security.oauth2.version>2.7.18</spring.security.oauth2.version>
        <lombok.version>1.18.26</lombok.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
        <aspectj.version>1.9.7</aspectj.version>
        <jsf.version>1.7.8-HOTFIX-T3</jsf.version>
        <eclp.core.version>0.0.1-SNAPSHOT</eclp.core.version>
        <maven.test.skip>false</maven.test.skip>
        <eclp.core.version>0.0.1-SNAPSHOT</eclp.core.version>
        <!-- 移除旧的Spring版本定义 -->
        <log4j.version>2.17.1-jdsec.rc1</log4j.version>
        <ump.version>20240630</ump.version>
    </properties>

    <!-- 依赖管理 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.jdl.aic.core</groupId>
                <artifactId>aic-client</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>
            <!-- Spring Boot BOM -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- MySQL驱动 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <!-- MyBatis Spring Boot Starter -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.spring.boot.version}</version>
            </dependency>

            <!-- Druid连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- FastJSON -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Spring Security OAuth2 Client -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-oauth2-client</artifactId>
                <version>${spring.security.oauth2.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jwt.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <optional>true</optional>
            </dependency>

            <!-- MapStruct -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- AspectJ dependencies -->
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjrt</artifactId>
                <version>${aspectj.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectj.version}</version>
            </dependency>

            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.jdl.aic.core.service</groupId>
                <artifactId>common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.aic.core.service</groupId>
                <artifactId>dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdl.aic.core.service</groupId>
                <artifactId>service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.5</version>
            </dependency>
            <dependency>
                <groupId>com.jd.stream</groupId>
                <artifactId>server-sdk</artifactId>
                <version>1.1.9</version>
            </dependency>
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>2.2</version>
            </dependency>
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.eclp</groupId>
                <artifactId>core-jsf</artifactId>
                <version>${eclp.core.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jsf</artifactId>
                        <groupId>com.jd</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>profiler</artifactId>
                        <groupId>com.jd.ump</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.jd.eclp</groupId>-->
<!--                <artifactId>core-dao</artifactId>-->
<!--                <version>${eclp.core.version}</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <artifactId>profiler</artifactId>-->
<!--                        <groupId>com.jd.ump</groupId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.apache.commons</groupId>-->
<!--                <artifactId>commons-dbcp2</artifactId>-->
<!--                <version>2.9.0</version>-->
<!--            </dependency>-->
            <!-- 移除单独的Spring Context依赖，让Spring Boot管理版本 -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <!-- 注释掉log4j-slf4j-impl，避免与Spring Boot的log4j-to-slf4j冲突 -->
            <!--
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-1.2-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.ump</groupId>
                <artifactId>profiler</artifactId>
                <version>${ump.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <activeProfile>test</activeProfile>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>

        <!-- 生产环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <activeProfile>prod</activeProfile>
            </properties>
        </profile>
    </profiles>

    <build>
        <sourceDirectory>src/main/java</sourceDirectory>
        <testSourceDirectory>src/test/java</testSourceDirectory>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>

        <defaultGoal>package</defaultGoal>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                    <configuration>
                        <source>1.8</source>
                        <target>1.8</target>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
