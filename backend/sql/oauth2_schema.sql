-- OAuth2账号体系数据库表结构

-- 使用数据库
USE ai;

-- 修改用户表，添加OAuth2相关字段
ALTER TABLE `sys_user` 
ADD COLUMN `provider` varchar(20) DEFAULT 'local' COMMENT '登录提供商（local/google/github）',
ADD COLUMN `provider_id` varchar(100) DEFAULT NULL COMMENT '第三方平台用户ID',
ADD COLUMN `is_oauth_user` tinyint(1) DEFAULT 0 COMMENT '是否为OAuth用户（0否 1是）',
ADD COLUMN `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
ADD COLUMN `login_count` int DEFAULT 0 COMMENT '登录次数';

-- 创建OAuth2账号绑定表
DROP TABLE IF EXISTS `sys_user_oauth`;
CREATE TABLE `sys_user_oauth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `provider` varchar(20) NOT NULL COMMENT '第三方平台（google/github）',
  `provider_id` varchar(100) NOT NULL COMMENT '第三方平台用户ID',
  `provider_username` varchar(100) DEFAULT NULL COMMENT '第三方平台用户名',
  `provider_email` varchar(100) DEFAULT NULL COMMENT '第三方平台邮箱',
  `provider_avatar` varchar(500) DEFAULT NULL COMMENT '第三方平台头像',
  `access_token` text DEFAULT NULL COMMENT '访问令牌',
  `refresh_token` text DEFAULT NULL COMMENT '刷新令牌',
  `expires_at` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `raw_user_info` json DEFAULT NULL COMMENT '原始用户信息JSON',
  `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否为主要登录方式',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_provider_id` (`provider`, `provider_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_provider` (`provider`),
  CONSTRAINT `fk_user_oauth_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户OAuth2绑定表';

-- 创建JWT令牌管理表
DROP TABLE IF EXISTS `sys_jwt_token`;
CREATE TABLE `sys_jwt_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `token_id` varchar(100) NOT NULL COMMENT '令牌唯一标识',
  `access_token` text NOT NULL COMMENT '访问令牌',
  `refresh_token` text DEFAULT NULL COMMENT '刷新令牌',
  `token_type` varchar(20) DEFAULT 'Bearer' COMMENT '令牌类型',
  `expires_at` datetime NOT NULL COMMENT '访问令牌过期时间',
  `refresh_expires_at` datetime DEFAULT NULL COMMENT '刷新令牌过期时间',
  `device_info` varchar(500) DEFAULT NULL COMMENT '设备信息',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `is_revoked` tinyint(1) DEFAULT 0 COMMENT '是否已撤销（0否 1是）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_token_id` (`token_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_revoked` (`is_revoked`),
  CONSTRAINT `fk_jwt_token_user_id` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='JWT令牌管理表';

-- 创建登录日志表
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `login_type` varchar(20) NOT NULL COMMENT '登录类型（local/google/github）',
  `login_status` varchar(20) NOT NULL COMMENT '登录状态（success/failed）',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `device_info` varchar(500) DEFAULT NULL COMMENT '设备信息',
  `location` varchar(200) DEFAULT NULL COMMENT '登录地点',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_login_status` (`login_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

-- 插入OAuth2测试数据
-- 为现有用户添加OAuth2绑定示例
INSERT INTO `sys_user_oauth` (`user_id`, `provider`, `provider_id`, `provider_username`, `provider_email`, `provider_avatar`, `is_primary`) VALUES
(1, 'google', 'google_123456789', '<EMAIL>', '<EMAIL>', 'https://lh3.googleusercontent.com/a/default-user', 0),
(2, 'github', 'github_987654321', 'testuser', '<EMAIL>', 'https://avatars.githubusercontent.com/u/123456', 0);

-- 更新现有用户的登录统计
UPDATE `sys_user` SET `login_count` = 5, `last_login_time` = NOW() WHERE `id` = 1;
UPDATE `sys_user` SET `login_count` = 3, `last_login_time` = NOW() WHERE `id` = 2;

-- 创建索引优化查询性能
CREATE INDEX `idx_user_provider` ON `sys_user` (`provider`);
CREATE INDEX `idx_user_provider_id` ON `sys_user` (`provider_id`);
CREATE INDEX `idx_user_oauth_user` ON `sys_user_oauth` (`user_id`, `provider`);
CREATE INDEX `idx_login_log_user_time` ON `sys_login_log` (`user_id`, `login_time`);
