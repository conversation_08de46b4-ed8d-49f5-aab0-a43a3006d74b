-- =====================================================
-- AI Community Portal Database DDL Script
-- =====================================================
-- 数据库：ai_community_portal
-- 用途：存储用户个性化和社区互动数据
-- 访问者：仅由Portal后端服务独家访问
-- 创建时间：2025-01-12
-- 最后更新：2025-01-15
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `ai_community_portal`
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE `ai_community_portal`;
-- ai_community_admin.admin_permission definition

CREATE TABLE `admin_permission` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100)  NOT NULL COMMENT '权限点名称（如"knowledge:create"）',
  `description` text  COMMENT '权限点描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `idx_admin_permission_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理员权限表';


-- ai_community_admin.admin_role definition

CREATE TABLE `admin_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100)  NOT NULL COMMENT '角色名称',
  `description` text  COMMENT '角色描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `idx_admin_role_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理员角色表';


-- ai_community_admin.admin_user definition

CREATE TABLE `admin_user` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sso_id` varchar(100)  NOT NULL COMMENT 'SSO/LDAP 唯一标识',
  `username` varchar(100)  NOT NULL COMMENT '用户名（如工号）',
  `display_name` varchar(100)  NOT NULL COMMENT '显示名称',
  `email` varchar(255)  DEFAULT NULL COMMENT '邮箱',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '管理员账号是否活跃',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `sso_id` (`sso_id`),
  UNIQUE KEY `idx_admin_user_sso_id` (`sso_id`),
  KEY `idx_admin_user_username` (`username`),
  KEY `idx_admin_user_email` (`email`),
  KEY `idx_admin_user_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台管理员用户表';


-- ai_community_admin.audit_trail definition

CREATE TABLE `audit_trail` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `actor_type` tinyint(4) NOT NULL COMMENT '操作者类型（0:User, 1:AdminUser）',
  `actor_id` bigint(20) unsigned NOT NULL COMMENT '操作者 ID（可能跨库引用）',
  `action_type` varchar(100)  NOT NULL COMMENT '操作类型（如"KNOWLEDGE_CREATE", "USER_UPDATE_ROLE"）',
  `target_type` varchar(100)  NOT NULL COMMENT '操作目标类型（如"Knowledge", "User"）',
  `target_id` bigint(20) unsigned NOT NULL COMMENT '操作目标 ID（可能跨库引用）',
  `old_value_json` json DEFAULT NULL COMMENT '操作前数据快照',
  `new_value_json` json DEFAULT NULL COMMENT '操作后数据快照',
  `ip_address` varchar(45)  DEFAULT NULL COMMENT '操作 IP 地址',
  `user_agent` varchar(512)  DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_audit_actor` (`actor_type`,`actor_id`),
  KEY `idx_audit_target` (`target_type`,`target_id`),
  KEY `idx_audit_action_type` (`action_type`),
  KEY `idx_audit_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计追踪表';


-- ai_community_admin.sys_login_log definition

CREATE TABLE `sys_login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `login_type` varchar(20) NOT NULL COMMENT '登录类型（local/google/github）',
  `login_status` varchar(20) NOT NULL COMMENT '登录状态（success/failed）',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `device_info` varchar(500) DEFAULT NULL COMMENT '设备信息',
  `location` varchar(200) DEFAULT NULL COMMENT '登录地点',
  `error_message` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_login_status` (`login_status`),
  KEY `idx_login_log_user_time` (`user_id`,`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';


-- ai_community_admin.sys_role definition

CREATE TABLE `sys_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int(11) NOT NULL COMMENT '显示顺序',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色信息表';


-- ai_community_admin.sys_user definition

CREATE TABLE `sys_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(30) NOT NULL COMMENT '用户账号',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `nickname` varchar(30) NOT NULL COMMENT '用户昵称',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phone` varchar(11) DEFAULT '' COMMENT '手机号码',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `status` char(1) DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `provider` varchar(20) DEFAULT 'local' COMMENT '登录提供商（local/google/github）',
  `provider_id` varchar(100) DEFAULT NULL COMMENT '第三方平台用户ID',
  `is_oauth_user` tinyint(1) DEFAULT '0' COMMENT '是否为OAuth用户（0否 1是）',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `login_count` int(11) DEFAULT '0' COMMENT '登录次数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_user_provider` (`provider`),
  KEY `idx_user_provider_id` (`provider_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';


-- ai_community_admin.sys_user_role definition

CREATE TABLE `sys_user_role` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户和角色关联表';


-- ai_community_admin.admin_role_permission definition

CREATE TABLE `admin_role_permission` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_role_id` bigint(20) unsigned NOT NULL COMMENT '外键关联 admin_role.id',
  `admin_permission_id` bigint(20) unsigned NOT NULL COMMENT '外键关联 admin_permission.id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admin_role_permission_unique` (`admin_role_id`,`admin_permission_id`),
  KEY `idx_admin_role_permission_role_id` (`admin_role_id`),
  KEY `idx_admin_role_permission_permission_id` (`admin_permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员角色权限关联表';


-- ai_community_admin.admin_user_role definition

CREATE TABLE `admin_user_role` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `admin_user_id` bigint(20) unsigned NOT NULL COMMENT '外键关联 admin_user.id',
  `admin_role_id` bigint(20) unsigned NOT NULL COMMENT '外键关联 admin_role.id',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admin_user_role_unique` (`admin_user_id`,`admin_role_id`),
  KEY `idx_admin_user_role_user_id` (`admin_user_id`),
  KEY `idx_admin_user_role_role_id` (`admin_role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户角色关联表';


-- ai_community_admin.content_review_status definition

CREATE TABLE `content_review_status` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content_type` tinyint(4) NOT NULL COMMENT '内容类型（0:知识, 1:资讯, 2:解决方案）',
  `content_id` bigint(20) unsigned NOT NULL COMMENT '关联对应内容ID（跨库引用，无外键约束）',
  `status` tinyint(4) NOT NULL COMMENT '当前审核状态（0:待审, 1:通过, 2:拒绝, 3:复审中）',
  `reviewer_id` bigint(20) unsigned DEFAULT NULL COMMENT '外键关联 admin_user.id，审核人',
  `review_comment` text  COMMENT '审核意见',
  `ai_suggestion_json` json DEFAULT NULL COMMENT 'AI 审核建议，如 {"sentiment": "negative", "keywords": ["xxx"]}',
  `reviewed_at` datetime DEFAULT NULL COMMENT '审核时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_content_review_unique` (`content_type`,`content_id`),
  KEY `idx_content_review_reviewer_id` (`reviewer_id`),
  KEY `idx_content_review_status` (`status`),
  KEY `idx_content_review_content_type` (`content_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容审核状态表';


-- ai_community_admin.notification_template definition

CREATE TABLE `notification_template` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_code` varchar(100)  NOT NULL COMMENT '模板唯一编码（如"COMMENT_REPLY_EMAIL"）',
  `template_type` tinyint(4) NOT NULL COMMENT '模板类型（0:邮件, 1:站内信, 2:短信）',
  `language` varchar(10)  NOT NULL DEFAULT 'zh-CN' COMMENT '模板语言（如"zh-CN", "en-US"）',
  `title_template` varchar(255)  NOT NULL COMMENT '通知标题模板',
  `content_template` text  COMMENT '通知内容模板，包含占位符',
  `description` text  COMMENT '模板描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` bigint(20) unsigned DEFAULT NULL COMMENT '创建用户ID (关联 admin_user.id)',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '最后更新用户ID (关联 admin_user.id)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_template_code_type_lang_unique` (`template_code`,`template_type`,`language`),
  KEY `idx_notification_template_type` (`template_type`),
  KEY `idx_notification_template_language` (`language`),
  KEY `idx_notification_template_created_by` (`created_by`),
  KEY `idx_notification_template_updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知模板表';


-- ai_community_admin.sys_jwt_token definition

CREATE TABLE `sys_jwt_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `token_id` varchar(100) NOT NULL COMMENT '令牌唯一标识',
  `access_token` text COMMENT '访问令牌',
  `refresh_token` text COMMENT '刷新令牌',
  `token_type` varchar(20) DEFAULT 'Bearer' COMMENT '令牌类型',
  `expires_at` datetime NOT NULL COMMENT '访问令牌过期时间',
  `refresh_expires_at` datetime DEFAULT NULL COMMENT '刷新令牌过期时间',
  `device_info` varchar(500) DEFAULT NULL COMMENT '设备信息',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `is_revoked` tinyint(1) DEFAULT '0' COMMENT '是否已撤销（0否 1是）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_token_id` (`token_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  KEY `idx_is_revoked` (`is_revoked`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='JWT令牌管理表';


-- ai_community_admin.sys_user_oauth definition

CREATE TABLE `sys_user_oauth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `provider` varchar(20) NOT NULL COMMENT '第三方平台（google/github）',
  `provider_id` varchar(100) NOT NULL COMMENT '第三方平台用户ID',
  `provider_username` varchar(100) DEFAULT NULL COMMENT '第三方平台用户名',
  `provider_email` varchar(100) DEFAULT NULL COMMENT '第三方平台邮箱',
  `provider_avatar` varchar(500) DEFAULT NULL COMMENT '第三方平台头像',
  `access_token` text COMMENT '访问令牌',
  `refresh_token` text COMMENT '刷新令牌',
  `expires_at` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `raw_user_info` json DEFAULT NULL COMMENT '原始用户信息JSON',
  `is_primary` tinyint(1) DEFAULT '0' COMMENT '是否为主要登录方式',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_provider_id` (`provider`,`provider_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_provider` (`provider`),
  KEY `idx_user_oauth_user` (`user_id`,`provider`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户OAuth2绑定表';


-- ai_community_admin.system_config definition

CREATE TABLE `system_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `key_name` varchar(100)  NOT NULL COMMENT '配置项名称（键）',
  `key_value` text  COMMENT '配置项值（值）',
  `description` text  COMMENT '配置项描述',
  `is_sensitive` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为敏感配置（如API Key，0:否, 1:是）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `updated_by` bigint(20) unsigned DEFAULT NULL COMMENT '最后更新用户ID (关联 admin_user.id)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `key_name` (`key_name`),
  UNIQUE KEY `idx_system_config_key_name` (`key_name`),
  KEY `idx_system_config_is_sensitive` (`is_sensitive`),
  KEY `idx_system_config_updated_by` (`updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';