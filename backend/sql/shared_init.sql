-- =====================================================
-- AI Community Shared Business Database DDL Script
-- =====================================================
-- 数据库：ai_community_shared
-- 用途：存储核心业务数据和内容
-- 访问者：仅由后端核心服务层独家访问
-- 创建时间：2025-01-12
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `ai_community_shared`
DEFAULT CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE `ai_community_shared`;
-- ai_community_shared.access_log definition

CREATE TABLE `access_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned DEFAULT NULL COMMENT '访问用户ID（跨库引用，无外键约束，匿名访问时为NULL）',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户姓名（冗余字段）',
  `session_id` varchar(100) DEFAULT NULL COMMENT '会话ID',
  `ip_address` varchar(45) NOT NULL COMMENT '访问IP地址',
  `user_agent` varchar(512) DEFAULT NULL COMMENT '用户代理信息',
  `request_method` varchar(10) NOT NULL COMMENT 'HTTP请求方法',
  `request_url` varchar(512) NOT NULL COMMENT '请求URL',
  `request_params` text COMMENT '请求参数（脱敏后）',
  `response_status` smallint(6) NOT NULL COMMENT 'HTTP响应状态码',
  `response_time_ms` int(10) unsigned NOT NULL COMMENT '响应时间（毫秒）',
  `referer` varchar(512) DEFAULT NULL COMMENT '来源页面',
  `module_name` varchar(50) DEFAULT NULL COMMENT '访问的功能模块',
  `action_name` varchar(50) DEFAULT NULL COMMENT '具体操作名称',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_access_log_user_id` (`user_id`),
  KEY `idx_access_log_ip_address` (`ip_address`),
  KEY `idx_access_log_module_action` (`module_name`,`action_name`),
  KEY `idx_access_log_created_at` (`created_at`),
  KEY `idx_access_log_response_status` (`response_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='访问日志表';


-- ai_community_shared.ai_usage_log definition

CREATE TABLE `ai_usage_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '使用AI服务的用户ID（跨库引用，无外键约束）',
  `user_name` varchar(100) NOT NULL COMMENT '用户姓名（冗余字段）',
  `service_type` tinyint(4) NOT NULL COMMENT 'AI服务类型（0:内容生成, 1:内容审核, 2:标签推荐, 3:搜索优化）',
  `model_name` varchar(100) NOT NULL COMMENT '使用的AI模型名称',
  `related_content_type` tinyint(4) DEFAULT NULL COMMENT '关联内容类型（0:知识, 1:资讯, 2:解决方案）',
  `related_content_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联内容ID（跨库引用，无外键约束）',
  `input_tokens` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '输入token数量',
  `output_tokens` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '输出token数量',
  `cost_amount` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '调用成本（元）',
  `response_time_ms` int(10) unsigned NOT NULL COMMENT '响应时间（毫秒）',
  `status` tinyint(4) NOT NULL COMMENT '调用状态（0:成功, 1:失败, 2:超时）',
  `error_message` text COMMENT '错误信息（如果失败）',
  `request_data_json` json DEFAULT NULL COMMENT '请求数据（脱敏后）',
  `response_data_json` json DEFAULT NULL COMMENT '响应数据（脱敏后）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_ai_usage_log_user_id` (`user_id`),
  KEY `idx_ai_usage_log_service_type` (`service_type`),
  KEY `idx_ai_usage_log_related_content` (`related_content_type`,`related_content_id`),
  KEY `idx_ai_usage_log_status` (`status`),
  KEY `idx_ai_usage_log_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI使用日志表';


-- ai_community_shared.content_source definition

CREATE TABLE `content_source` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_name` varchar(200) NOT NULL COMMENT '来源名称（如"公司内部wiki"、"GitHub开源项目X"）',
  `source_type` tinyint(4) NOT NULL COMMENT '来源类型（0:内部wiki, 1:Git仓库, 2:外部网站, 3:运营导入）',
  `source_url` varchar(512) DEFAULT NULL COMMENT '来源的URL（如wiki首页，Git仓库地址）',
  `last_sync_at` datetime DEFAULT NULL COMMENT '上次同步时间',
  `sync_strategy_json` json DEFAULT NULL COMMENT '自动化同步策略配置，如 git repo 信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_content_source_type` (`source_type`),
  KEY `idx_content_source_name` (`source_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容来源表';


-- ai_community_shared.content_type_config definition

CREATE TABLE `content_type_config` (
  `id` bigint(20) unsigned NOT NULL auto_increment COMMENT '主键',
  `code` varchar(50) NOT NULL COMMENT '内容类型编码',
  `name` varchar(100) NOT NULL COMMENT '内容类型名称',
  `description` text COMMENT '内容类型描述',
  `table_name` varchar(100) NOT NULL COMMENT '对应的主表名',
  `is_portal_module` tinyint(1) DEFAULT '1' COMMENT '是否为门户功能板块（1:是, 0:否）',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '图标URL',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建人（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '更新人（跨库引用，无外键约束）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_code` (`code`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_is_portal_module` (`is_portal_module`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容类型配置表';


-- ai_community_shared.crawler_content definition

CREATE TABLE `crawler_content` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(500) NOT NULL COMMENT '内容标题',
  `link` varchar(500) NOT NULL COMMENT '原始链接URL',
  `language` varchar(10) NOT NULL DEFAULT 'zh-CN' COMMENT '内容语言（如：zh-CN, en-US, ja-JP等）',
  `author` varchar(100) DEFAULT NULL COMMENT '作者',
  `description` text COMMENT '内容描述或摘要',
  `pub_date` datetime DEFAULT NULL COMMENT '内容发布时间',
  `ai_summary` text COMMENT 'AI智能总结内容',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否精品内容（0:否, 1:是）',
  `content_md5` varchar(32) NOT NULL COMMENT '内容MD5值，用于去重和校验',
  `content` longtext COMMENT '爬虫抓取的完整内容',
  `content_type` varchar(50) DEFAULT NULL COMMENT '内容类型（如：article, news, blog, document等）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '内容状态（0:待处理, 1:已处理, 2:处理失败, 3:已忽略）',
  `quality_score` decimal(3,2) DEFAULT NULL COMMENT '内容质量评分（0.00-5.00）',
  `word_count` int(10) unsigned DEFAULT NULL COMMENT '内容字数统计',
  `tags` json DEFAULT NULL COMMENT '内容标签列表',
  `metadata` json DEFAULT NULL COMMENT '扩展元数据信息',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（系统爬虫时为NULL）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  `type` enum('article','video','audio','image') NOT NULL DEFAULT 'article' COMMENT '内容类型，区分文章、视频、音频、图片',
  `attachments` json DEFAULT NULL COMMENT '附件信息，JSON格式',
   task_desc varchar(400) NULL COMMENT '任务描述',
  `media` json DEFAULT NULL COMMENT '媒体资源信息，JSON格式',
  `task_id` varchar(64) DEFAULT NULL COMMENT '任务ID',
  `task_name` varchar(200) DEFAULT NULL COMMENT '任务名称',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_crawler_content_md5` (`content_md5`) COMMENT '内容MD5唯一，防止重复抓取',
  UNIQUE KEY `uk_crawler_content_link` (`link`,`deleted_at`) COMMENT '链接唯一性约束',
  KEY `idx_crawler_content_title` (`title`),
  KEY `idx_crawler_content_language` (`language`),
  KEY `idx_crawler_content_pub_date` (`pub_date`),
  KEY `idx_crawler_content_is_featured` (`is_featured`),
  KEY `idx_crawler_content_status` (`status`),
  KEY `idx_crawler_content_quality_score` (`quality_score`),
  KEY `idx_crawler_content_created_at` (`created_at`),
  KEY `idx_crawler_content_deleted_at` (`deleted_at`),
  KEY `idx1_crawler_content_type` (`type`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='爬虫内容表，存储爬虫抓取的网页内容和相关元数据';


-- ai_community_shared.dictionary definition

CREATE TABLE `dictionary` (
  `id` bigint(20) unsigned NOT NULL  COMMENT '主键ID',
  `key` varchar(100) NOT NULL COMMENT '字典键，用于标识配置项',
  `value` varchar(1000) NOT NULL COMMENT '字典值，存储配置的具体内容',
  `type` varchar(50) NOT NULL COMMENT '字典类型，用于分类管理（如：system_config, enum_value, constant等）',
  `description` varchar(255) DEFAULT NULL COMMENT '字典项描述说明',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重，数值越小越靠前',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（0:否, 1:是）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dictionary_key_type` (`key`,`type`,`deleted_at`) COMMENT '同一类型下的key唯一',
  KEY `idx_dictionary_type` (`type`),
  KEY `idx_dictionary_is_active` (`is_active`),
  KEY `idx_dictionary_sort_order` (`sort_order`),
  KEY `idx_dictionary_created_at` (`created_at`),
  KEY `idx_dictionary_deleted_at` (`deleted_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='字典表，存储系统配置参数、枚举值、常量等键值对数据';


-- ai_community_shared.file_storage definition

CREATE TABLE `file_storage` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `original_filename` varchar(255) NOT NULL COMMENT '原始文件名',
  `stored_filename` varchar(255) NOT NULL COMMENT '存储文件名（通常是UUID）',
  `file_path` varchar(512) NOT NULL COMMENT '文件存储路径',
  `file_size` bigint(20) unsigned NOT NULL COMMENT '文件大小（字节）',
  `mime_type` varchar(100) NOT NULL COMMENT '文件MIME类型',
  `file_hash` varchar(64) DEFAULT NULL COMMENT '文件哈希值（用于去重）',
  `uploaded_by` bigint(20) unsigned NOT NULL COMMENT '上传用户ID（可能跨库引用，无外键约束）',
  `uploader_name` varchar(100) NOT NULL COMMENT '上传者姓名（冗余字段）',
  `related_content_type` tinyint(4) DEFAULT NULL COMMENT '关联内容类型（0:知识封面, 1:评论附件, 2:用户头像, 3:其他）',
  `related_content_id` bigint(20) unsigned DEFAULT NULL COMMENT '关联内容ID（可能跨库引用，无外键约束）',
  `storage_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '存储类型（0:本地, 1:OSS, 2:CDN）',
  `access_url` varchar(512) DEFAULT NULL COMMENT '文件访问URL',
  `is_public` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否公开访问（0:否, 1:是）',
  `download_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '下载次数',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_file_storage_uploaded_by` (`uploaded_by`),
  KEY `idx_file_storage_related_content` (`related_content_type`,`related_content_id`),
  KEY `idx_file_storage_file_hash` (`file_hash`),
  KEY `idx_file_storage_mime_type` (`mime_type`),
  KEY `idx_file_storage_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件存储表';


-- ai_community_shared.knowledge_type definition

CREATE TABLE `knowledge_type` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '知识类型名称，如"提示词"',
  `code` varchar(50) NOT NULL COMMENT '知识类型唯一编码，如"PROMPT"',
  `description` text COMMENT '知识类型描述',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '知识类型图标 URL',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用此知识类型（0:否, 1:是）',
  `community_config_json` json DEFAULT NULL COMMENT '定义社区互动行为的 JSON 配置',
  `render_config_json` json DEFAULT NULL COMMENT '定义前端如何渲染此类型知识的 JSON Schema 及 UI 配置',
  `metadata_schema` json DEFAULT NULL COMMENT 'metadata_schema',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `idx_knowledge_type_name` (`name`),
  UNIQUE KEY `idx_knowledge_type_code` (`code`),
  KEY `idx_knowledge_type_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识类型表';


-- ai_community_shared.learning_course definition

CREATE TABLE `learning_course` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '课程名称',
  `description` text COMMENT '课程描述',
  `category` varchar(100) DEFAULT NULL COMMENT '课程分类',
  `difficulty_level` enum('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT') DEFAULT NULL COMMENT '难度级别',
  `total_hours` decimal(6,1) DEFAULT NULL COMMENT '预估总学习时长（小时）',
  `resource_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '包含资源数量',
  `enrolled_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '报名学习人数',
  `completion_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成人数',
  `completion_rate` decimal(5,2) DEFAULT '0.00' COMMENT '平均完成率',
  `prerequisites` text COMMENT '前置知识要求',
  `learning_goals` text COMMENT '学习目标',
  `creator_id` varchar(200) NOT NULL COMMENT '创建者ID（跨库引用，无外键约束）',
  `creator_name` varchar(100) DEFAULT NULL COMMENT '创建者姓名',
  `status` enum('DRAFT','PUBLISHED','ARCHIVED') NOT NULL DEFAULT 'DRAFT' COMMENT '课程状态',
  `is_official` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否官方课程',
  `cover_image_url` varchar(255) DEFAULT NULL COMMENT '封面图片URL',
  `tags` varchar(500) DEFAULT NULL COMMENT '标签列表，逗号分隔',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_learning_course_category` (`category`),
  KEY `idx_learning_course_difficulty` (`difficulty_level`),
  KEY `idx_learning_course_creator` (`creator_id`),
  KEY `idx_learning_course_status` (`status`),
  KEY `idx_learning_course_is_official` (`is_official`),
  KEY `idx_learning_course_completion_rate` (`completion_rate`),
  KEY `idx_learning_course_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习课程表';


-- ai_community_shared.learning_resource definition

CREATE TABLE `learning_resource` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) NOT NULL COMMENT '资源标题',
  `description` text COMMENT '资源描述',
  `content` longtext COMMENT '学习资源详细内容',
  `learning_goals` text COMMENT '学习目标',
  `prerequisites` text COMMENT '前置知识要求',
  `resource_type` enum('COURSE','VIDEO','DOCUMENT','PROJECT','TOOL_GUIDE','LEARNING_PATH','ARTICLE') NOT NULL COMMENT '资源类型',
  `source_type` enum('EXTERNAL','INTERNAL','USER_RECOMMENDED') NOT NULL COMMENT '来源类型',
  `source_url` varchar(1000) DEFAULT NULL COMMENT '资源链接',
  `source_platform` varchar(100) DEFAULT NULL COMMENT '来源平台：Coursera、YouTube、内部等',
  `difficulty_level` enum('BEGINNER','INTERMEDIATE','ADVANCED','EXPERT') DEFAULT NULL COMMENT '难度级别',
  `estimated_duration_hours` decimal(5,1) DEFAULT NULL COMMENT '预估学习时长',
  `language` varchar(10) NOT NULL DEFAULT 'zh-CN' COMMENT '资源语言',
  `is_free` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否免费',
  `price_info` varchar(100) DEFAULT NULL COMMENT '价格信息',
  `rating` decimal(3,2) DEFAULT NULL COMMENT '平均评分(1-5)',
  `rating_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '评分人数',
  `view_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '查看次数',
  `bookmark_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '收藏次数',
  `completion_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成人数',
  `completion_rate` decimal(5,2) DEFAULT '0.00' COMMENT '平均完成率',
  `tags` varchar(1000) DEFAULT NULL COMMENT '标签列表 ["tag1", "tag2"]',
  `metadata` json DEFAULT NULL COMMENT '扩展元数据',
  `status` enum('ACTIVE','INACTIVE','PENDING_REVIEW') NOT NULL DEFAULT 'ACTIVE' COMMENT '状态',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  `content_type` varchar(50) DEFAULT NULL COMMENT '内容类型：video,pdf,article,external_link,tool,interactive',
  `content_config` json DEFAULT NULL COMMENT '内容配置信息',
  `embed_config` json DEFAULT NULL COMMENT '嵌入配置',
  `access_config` json DEFAULT NULL COMMENT '访问配置',
  `media_metadata` json DEFAULT NULL COMMENT '媒体元数据',
  PRIMARY KEY (`id`),
  KEY `idx_learning_resource_type` (`resource_type`),
  KEY `idx_learning_resource_source` (`source_type`),
  KEY `idx_learning_resource_platform` (`source_platform`),
  KEY `idx_learning_resource_difficulty` (`difficulty_level`),
  KEY `idx_learning_resource_rating` (`rating`),
  KEY `idx_learning_resource_status` (`status`),
  KEY `idx_learning_resource_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习资源表';


-- ai_community_shared.metrics_data definition

CREATE TABLE `metrics_data` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `metric_name` varchar(100) NOT NULL COMMENT '指标名称（如"daily_active_users"）',
  `metric_type` tinyint(4) NOT NULL COMMENT '指标类型（0:计数, 1:求和, 2:平均值, 3:比率）',
  `metric_value` decimal(15,4) NOT NULL COMMENT '指标值',
  `dimension_json` json DEFAULT NULL COMMENT '指标维度信息，如 {"department": "tech", "date": "2023-01-01"}',
  `date_key` date NOT NULL COMMENT '数据日期（用于时间序列分析）',
  `hour_key` tinyint(4) DEFAULT NULL COMMENT '数据小时（0-23，用于小时级分析）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_metrics_data_metric_name` (`metric_name`),
  KEY `idx_metrics_data_date_key` (`date_key`),
  KEY `idx_metrics_data_metric_type` (`metric_type`),
  KEY `idx_metrics_data_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='度量指标数据表';


-- ai_community_shared.rss_source definition

CREATE TABLE `rss_source` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT 'RSS 源名称',
  `feed_url` varchar(512) NOT NULL COMMENT 'RSS 订阅地址',
  `description` text COMMENT '源描述',
  `category` varchar(50) DEFAULT NULL COMMENT 'RSS 源的分类',
  `type` tinyint(4) NOT NULL COMMENT '源类型（0:官方, 1:用户订阅）',
  `owner_id` varchar(200) DEFAULT NULL COMMENT '如果是用户订阅，关联 user.id（跨库引用，无外键约束）',
  `owner_name` varchar(100) DEFAULT NULL COMMENT '所有者姓名（冗余字段，减少跨库查询）',
  `last_fetched_at` datetime DEFAULT NULL COMMENT '上次成功抓取时间',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '源状态（0:活跃, 1:暂停, 2:失败）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `feed_url` (`feed_url`),
  UNIQUE KEY `idx_rss_source_feed_url` (`feed_url`),
  KEY `idx_rss_source_type` (`type`),
  KEY `idx_rss_source_owner_id` (`owner_id`),
  KEY `idx_rss_source_status` (`status`),
  KEY `idx_rss_source_category` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='RSS 源表';


-- ai_community_shared.search_index_log definition

CREATE TABLE `search_index_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `index_name` varchar(100) NOT NULL COMMENT '索引名称',
  `operation_type` tinyint(4) NOT NULL COMMENT '操作类型（0:创建, 1:更新, 2:删除, 3:重建）',
  `content_type` tinyint(4) NOT NULL COMMENT '内容类型（0:知识, 1:资讯, 2:解决方案）',
  `content_id` bigint(20) unsigned NOT NULL COMMENT '内容ID',
  `status` tinyint(4) NOT NULL COMMENT '操作状态（0:成功, 1:失败, 2:进行中）',
  `error_message` text COMMENT '错误信息（如果失败）',
  `processing_time_ms` int(10) unsigned DEFAULT NULL COMMENT '处理时间（毫秒）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_search_index_log_index_name` (`index_name`),
  KEY `idx_search_index_log_content` (`content_type`,`content_id`),
  KEY `idx_search_index_log_status` (`status`),
  KEY `idx_search_index_log_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='搜索索引日志表';


-- ai_community_shared.share_option_config definition

CREATE TABLE `share_option_config` (
  `id` bigint(20) unsigned NOT NULL auto_increment COMMENT '主键ID',
  `content_type` varchar(50) NOT NULL COMMENT '内容类型编码',
  `share_type` varchar(50) NOT NULL COMMENT '分享类型：internal,wechat,email,link_copy,teams,slack',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `display_name` varchar(100) NOT NULL COMMENT '显示名称',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '图标URL',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `config_json` json DEFAULT NULL COMMENT '分享配置JSON',
`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建人（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '更新人（跨库引用，无外键约束）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_content_share` (`content_type`,`share_type`),
  KEY `idx_content_type` (`content_type`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_is_enabled` (`is_enabled`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分享选项配置表';


-- ai_community_shared.social_feature_config definition

CREATE TABLE `social_feature_config` (
  `id` bigint(20) unsigned NOT NULL auto_increment COMMENT '主键ID',
  `content_type` varchar(50) NOT NULL COMMENT '内容类型编码',
  `feature_type` varchar(50) NOT NULL COMMENT '功能类型：like,favorite,share,comment,read_track',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `config_json` json DEFAULT NULL COMMENT '功能配置JSON',
`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建人（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '更新人（跨库引用，无外键约束）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_content_feature` (`content_type`,`feature_type`),
  KEY `idx_content_type` (`content_type`),
  KEY `idx_feature_type` (`feature_type`),
  KEY `idx_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社交功能配置表';


-- ai_community_shared.solution definition

CREATE TABLE `solution` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) NOT NULL COMMENT '解决方案标题',
  `description` text COMMENT '解决方案描述',
  `content` longtext COMMENT '解决方案的详细内容（Markdown/富文本）',
  `author_id` varchar(200) DEFAULT NULL COMMENT '关联 user.id（跨库引用，无外键约束）',
  `author_name` varchar(100) DEFAULT NULL COMMENT '作者姓名（冗余字段，减少跨库查询）',
  `status` tinyint(4) NOT NULL COMMENT '状态（0:草稿, 1:待审核, 2:已发布, 3:已下线）',
  `visibility` tinyint(4) NOT NULL COMMENT '可见性（0:私有, 1:团队可见, 2:公开）',
  `team_id` bigint(20) unsigned DEFAULT NULL COMMENT '如果 visibility 为团队可见，关联 team.id（跨库引用，无外键约束）',
  `team_name` varchar(100) DEFAULT NULL COMMENT '团队名称（冗余字段，减少跨库查询）',
  `read_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '阅读次数',
  `like_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `comment_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '评论次数',
  `cover_image_url` varchar(255) DEFAULT NULL COMMENT '封面图片 URL',
  `ai_review_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'AI 审核状态',
  `ai_tags_json` json DEFAULT NULL COMMENT 'AI 推荐标签列表',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_solution_author_id` (`author_id`),
  KEY `idx_solution_status` (`status`),
  KEY `idx_solution_visibility` (`visibility`),
  KEY `idx_solution_team_id` (`team_id`),
  KEY `idx_solution_title` (`title`),
  KEY `idx_solution_created_at` (`created_at`),
  KEY `idx_solution_read_count` (`read_count`),
  KEY `idx_solution_like_count` (`like_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='场景解决方案表';


-- ai_community_shared.category definition

CREATE TABLE `category` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `parent_id` bigint(20) unsigned DEFAULT NULL COMMENT '自关联 category.id，父分类ID，支持层级结构',
  `content_category` enum('knowledge','solution','news_feed','learning_resource','learning_course','general') NOT NULL DEFAULT 'general' COMMENT '所属内容类别',
  `sub_type_id` bigint(20) unsigned DEFAULT NULL COMMENT '细分类型ID，对于knowledge类型关联knowledge_type.id',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '分类图标 URL',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序权重',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（0:否, 1:是）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_category_name_content` (`name`,`content_category`,`parent_id`,`deleted_at`),
  KEY `idx_category_parent_id` (`parent_id`),
  KEY `idx_category_content_category` (`content_category`),
  KEY `idx_category_sub_type` (`content_category`,`sub_type_id`),
  KEY `idx_category_is_active` (`is_active`),
  KEY `idx_category_sort_order` (`sort_order`),
  KEY `fk_category_sub_type_knowledge` (`sub_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';


-- ai_community_shared.content_category_relation definition

CREATE TABLE `content_category_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content_type` enum('knowledge','solution','news_feed','learning_resource','learning_course') NOT NULL COMMENT '内容类型',
  `content_id` bigint(20) unsigned NOT NULL COMMENT '内容ID',
  `category_id` bigint(20) unsigned NOT NULL COMMENT '分类ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_content_category_unique` (`content_type`,`content_id`,`category_id`),
  KEY `idx_content_category_content` (`content_type`,`content_id`),
  KEY `idx_content_category_category` (`category_id`),
  KEY `idx_content_category_type` (`content_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容分类关联表';


-- ai_community_shared.knowledge definition

CREATE TABLE `knowledge` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) NOT NULL COMMENT '知识标题',
  `description` text COMMENT '知识的简短描述或摘要',
  `content` longtext COMMENT '知识的主体内容，如代码、长文本、Markdown 等',
  `logo_url` varchar(255) DEFAULT NULL COMMENT 'logo图片',
  `knowledge_type_id` bigint(20) unsigned NOT NULL COMMENT '外键关联 knowledge_type.id，知识类型',
  `author_id` varchar(200) DEFAULT NULL COMMENT '关联 user.id，知识作者（跨库引用，无外键约束）',
  `author_name` varchar(100) DEFAULT NULL COMMENT '作者姓名（冗余字段，减少跨库查询）',
  `status` tinyint(4) NOT NULL COMMENT '知识状态（0:草稿, 1:待审核, 2:已发布, 3:已下线, 4:已拒绝）',
  `visibility` tinyint(4) NOT NULL COMMENT '可见性（0:私有, 1:团队可见, 2:公开）',
  `team_id` bigint(20) unsigned DEFAULT NULL COMMENT '如果 visibility 为团队可见，关联 team.id（跨库引用，无外键约束）',
  `team_name` varchar(100) DEFAULT NULL COMMENT '团队名称（冗余字段，减少跨库查询）',
  `version` varchar(50) DEFAULT NULL COMMENT '知识版本号，如 v1.0.0',
  `read_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '阅读次数',
  `like_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点赞次数',
  `comment_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '评论次数',
  `fork_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'Fork 次数',
  `cover_image_url` varchar(255) DEFAULT NULL COMMENT '知识的封面图片 URL',
  `metadata_json` json DEFAULT NULL COMMENT '核心字段，存储不同知识类型特有的结构化数据',
  `ai_review_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'AI 审核状态（0:未审, 1:通过, 2:拒绝, 3:人工复审）',
  `ai_tags_json` json DEFAULT NULL COMMENT 'AI 推荐的标签列表，["tag1", "tag2"]',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  `favorite_count` int(10) unsigned DEFAULT '0' COMMENT '收藏次数',
  `share_count` int(10) unsigned DEFAULT '0' COMMENT '分享次数',
  `social_score` decimal(10,2) DEFAULT '0.00' COMMENT '社交热度分数',
  `last_social_activity_at` datetime DEFAULT NULL COMMENT '最后社交活动时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_type_id` (`knowledge_type_id`),
  KEY `idx_knowledge_author_id` (`author_id`),
  KEY `idx_knowledge_status` (`status`),
  KEY `idx_knowledge_visibility` (`visibility`),
  KEY `idx_knowledge_team_id` (`team_id`),
  KEY `idx_knowledge_title` (`title`),
  KEY `idx_knowledge_created_at` (`created_at`),
  KEY `idx_knowledge_read_count` (`read_count`),
  KEY `idx_knowledge_like_count` (`like_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识表';


-- ai_community_shared.learning_path_resource definition

CREATE TABLE `learning_path_resource` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `learning_path_id` bigint(20) unsigned NOT NULL COMMENT '学习路径资源ID',
  `resource_id` bigint(20) unsigned NOT NULL COMMENT '关联的学习资源ID',
  `sequence_order` int(10) unsigned NOT NULL COMMENT '在路径中的顺序',
  `stage_name` varchar(100) DEFAULT NULL COMMENT '阶段名称（如：第一阶段、第二阶段）',
  `estimated_hours` decimal(5,1) DEFAULT NULL COMMENT '预估学习时长',
  `is_optional` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可选',
  `notes` text COMMENT '学习建议',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_path_resource_unique` (`learning_path_id`,`resource_id`),
  KEY `idx_path_resource_path` (`learning_path_id`),
  KEY `idx_path_resource_resource` (`resource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习路径资源关联表';


-- ai_community_shared.news_feed definition

CREATE TABLE `news_feed` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) NOT NULL COMMENT '资讯标题',
  `author` varchar(100) DEFAULT NULL COMMENT '资讯作者',
  `source_url` varchar(512) NOT NULL COMMENT '资讯原文链接',
  `published_at` datetime NOT NULL COMMENT '资讯发布时间（原文时间）',
  `content_summary` text COMMENT '资讯摘要（AI生成或人工编辑）',
  `content_html` longtext COMMENT '资讯内容 HTML 格式（全文抓取）',
  `cover_image_url` varchar(255) DEFAULT NULL COMMENT '资讯封面图',
  `rss_source_id` bigint(20) unsigned DEFAULT NULL COMMENT '外键关联 rss_source.id，标识来源 RSS',
  `source_name` varchar(100) DEFAULT NULL COMMENT '来源名称（如"新华网"，当 rss_source_id 为空时）',
  `type` tinyint(4) NOT NULL COMMENT '资讯类型（0:采集, 1:官方发布）',
  `status` tinyint(4) NOT NULL COMMENT '状态（0:待审核, 1:已发布, 2:已下线）',
  `ai_review_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'AI 审核状态',
  `ai_tags_json` json DEFAULT NULL COMMENT 'AI 推荐的标签列表',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_news_feed_rss_source_id` (`rss_source_id`),
  KEY `idx_news_feed_status` (`status`),
  KEY `idx_news_feed_type` (`type`),
  KEY `idx_news_feed_published_at` (`published_at`),
  KEY `idx_news_feed_title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资讯表';


-- ai_community_shared.resource_recommendation definition

CREATE TABLE `resource_recommendation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID（跨库引用，无外键约束）',
  `resource_id` bigint(20) unsigned NOT NULL COMMENT '学习资源ID',
  `recommendation_type` enum('INTEREST_BASED','PATH_BASED','SKILL_GAP','TRENDING','SIMILAR_USER') NOT NULL COMMENT '推荐类型',
  `recommendation_score` decimal(5,4) DEFAULT NULL COMMENT '推荐分数',
  `recommendation_reason` json DEFAULT NULL COMMENT '推荐原因',
  `is_clicked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否点击',
  `is_bookmarked` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否收藏',
  `feedback_score` tinyint(4) DEFAULT NULL COMMENT '用户反馈分数(1-5)',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `clicked_at` datetime DEFAULT NULL COMMENT '点击时间',
  PRIMARY KEY (`id`),
  KEY `idx_resource_recommendation_user` (`user_id`),
  KEY `idx_resource_recommendation_resource` (`resource_id`),
  KEY `idx_resource_recommendation_type` (`recommendation_type`),
  KEY `idx_resource_recommendation_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源推荐记录表';


-- ai_community_shared.resource_similarity definition

CREATE TABLE `resource_similarity` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `resource_a_id` bigint(20) unsigned NOT NULL COMMENT '资源A的ID',
  `resource_b_id` bigint(20) unsigned NOT NULL COMMENT '资源B的ID',
  `similarity_score` decimal(5,4) NOT NULL COMMENT '相似度分数',
  `similarity_type` enum('CONTENT','USER_BEHAVIOR','TOPIC','DIFFICULTY') NOT NULL COMMENT '相似度类型',
  `calculated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_resource_similarity_pair` (`resource_a_id`,`resource_b_id`,`similarity_type`),
  KEY `idx_resource_similarity_a` (`resource_a_id`),
  KEY `idx_resource_similarity_b` (`resource_b_id`),
  KEY `idx_resource_similarity_score` (`similarity_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习资源关联表';


-- ai_community_shared.tag definition

CREATE TABLE `tag` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `content_category` enum('knowledge','solution','news_feed','learning_resource','general') NOT NULL DEFAULT 'general' COMMENT '所属内容类别',
  `sub_type_id` bigint(20) unsigned DEFAULT NULL COMMENT '细分类型ID，对于knowledge类型关联knowledge_type.id',
  `tag_category` varchar(50) DEFAULT NULL COMMENT '标签分组，如：技术栈、难度级别、应用场景等',
  `description` text COMMENT '标签描述',
  `color` varchar(7) DEFAULT NULL COMMENT '标签颜色（十六进制，如 #FF5733）',
  `usage_count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '使用次数统计',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用（0:否, 1:是）',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  `updated_by` varchar(200) DEFAULT NULL COMMENT '最后更新用户ID（跨库引用，无外键约束）',
  `deleted_at` datetime DEFAULT NULL COMMENT '软删除时间戳',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tag_name_content` (`name`,`content_category`,`deleted_at`),
  KEY `idx_tag_content_category` (`content_category`),
  KEY `idx_tag_sub_type` (`content_category`,`sub_type_id`),
  KEY `idx_tag_category` (`tag_category`),
  KEY `idx_tag_usage_count` (`usage_count`),
  KEY `idx_tag_is_active` (`is_active`),
  KEY `fk_tag_sub_type_knowledge` (`sub_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';


-- ai_community_shared.content_tag_relation definition

CREATE TABLE `content_tag_relation` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content_type` enum('knowledge','solution','news_feed','learning_resource') NOT NULL COMMENT '内容类型',
  `content_id` bigint(20) unsigned NOT NULL COMMENT '内容ID',
  `tag_id` bigint(20) unsigned NOT NULL COMMENT '标签ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `created_by` varchar(200) DEFAULT NULL COMMENT '创建用户ID（跨库引用，无外键约束）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_content_tag_unique` (`content_type`,`content_id`,`tag_id`),
  KEY `idx_content_tag_content` (`content_type`,`content_id`),
  KEY `idx_content_tag_tag` (`tag_id`),
  KEY `idx_content_tag_type` (`content_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容标签关联表';