# OAuth2 配置指南

## Google OAuth2 设置

### 1. Google Cloud Console 配置

1. 访问 https://console.cloud.google.com/
2. 创建新项目或选择现有项目
3. 启用 Google+ API：
   - 导航到 "API和服务" → "库"
   - 搜索 "Google+ API" 并启用

4. 创建 OAuth2 凭据：
   - 导航到 "API和服务" → "凭据"
   - 点击 "创建凭据" → "OAuth 2.0 客户端ID"
   - 应用类型：Web应用
   - 授权的重定向URI：
     ```
     http://localhost:8080/login/oauth2/code/google
     ```

### 2. 环境变量配置

将获取的客户端ID和密钥配置为环境变量：

```bash
export GOOGLE_CLIENT_ID="your-actual-google-client-id"
export GOOGLE_CLIENT_SECRET="your-actual-google-client-secret"
```

或者直接在 application.yml 中替换：

```yaml
spring:
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: "your-actual-google-client-id"
            client-secret: "your-actual-google-client-secret"
```

## GitHub OAuth2 设置

### 1. GitHub 应用配置

1. 访问 https://github.com/settings/developers
2. 点击 "New OAuth App"
3. 填写应用信息：
   - Application name: Template Admin
   - Homepage URL: http://localhost:3000
   - Authorization callback URL: http://localhost:8080/login/oauth2/code/github

### 2. 环境变量配置

```bash
export GITHUB_CLIENT_ID="your-github-client-id"
export GITHUB_CLIENT_SECRET="your-github-client-secret"
```

## 测试配置

配置完成后重启后端服务，然后测试OAuth2登录功能。
