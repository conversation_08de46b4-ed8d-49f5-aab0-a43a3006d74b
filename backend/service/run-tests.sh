#!/bin/bash

# KnowledgeService 测试运行脚本
# 
# 使用方法:
#   ./run-tests.sh [test-type]
#
# test-type 可选值:
#   unit        - 只运行单元测试
#   integration - 只运行集成测试
#   performance - 只运行性能测试
#   all         - 运行所有测试 (默认)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查 Maven 是否安装
check_maven() {
    if ! command -v mvn &> /dev/null; then
        print_message $RED "错误: Maven 未安装或不在 PATH 中"
        exit 1
    fi
}

# 运行单元测试
run_unit_tests() {
    print_message $BLUE "🧪 运行单元测试..."
    mvn test -Dtest=KnowledgeServiceImplTest -Dspring.profiles.active=test
}

# 运行集成测试
run_integration_tests() {
    print_message $BLUE "🔗 运行集成测试..."
    mvn test -Dtest=KnowledgeServiceIntegrationTest -Dspring.profiles.active=test
}

# 运行性能测试
run_performance_tests() {
    print_message $BLUE "⚡ 运行性能测试..."
    mvn test -Dtest=KnowledgeServicePerformanceTest -Dspring.profiles.active=test
}

# 运行所有测试
run_all_tests() {
    print_message $BLUE "🚀 运行所有测试..."
    mvn test -Dtest=*KnowledgeService*Test -Dspring.profiles.active=test
}

# 生成测试报告
generate_report() {
    print_message $BLUE "📊 生成测试报告..."
    mvn surefire-report:report
    
    if [ -f "target/site/surefire-report.html" ]; then
        print_message $GREEN "✅ 测试报告已生成: target/site/surefire-report.html"
    fi
}

# 清理测试环境
cleanup() {
    print_message $YELLOW "🧹 清理测试环境..."
    mvn clean
}

# 主函数
main() {
    local test_type=${1:-all}
    
    print_message $GREEN "🎯 KnowledgeService 测试运行器"
    print_message $YELLOW "测试类型: $test_type"
    echo
    
    # 检查环境
    check_maven
    
    # 进入项目目录
    cd "$(dirname "$0")"
    
    # 根据参数运行相应的测试
    case $test_type in
        unit)
            run_unit_tests
            ;;
        integration)
            run_integration_tests
            ;;
        performance)
            run_performance_tests
            ;;
        all)
            run_all_tests
            ;;
        clean)
            cleanup
            exit 0
            ;;
        report)
            generate_report
            exit 0
            ;;
        *)
            print_message $RED "❌ 未知的测试类型: $test_type"
            echo
            echo "支持的测试类型:"
            echo "  unit        - 单元测试"
            echo "  integration - 集成测试"
            echo "  performance - 性能测试"
            echo "  all         - 所有测试"
            echo "  clean       - 清理环境"
            echo "  report      - 生成报告"
            exit 1
            ;;
    esac
    
    # 检查测试结果
    if [ $? -eq 0 ]; then
        print_message $GREEN "✅ 测试执行成功!"
        
        # 自动生成报告
        if [ "$test_type" != "clean" ] && [ "$test_type" != "report" ]; then
            echo
            generate_report
        fi
    else
        print_message $RED "❌ 测试执行失败!"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "KnowledgeService 测试运行脚本"
    echo
    echo "使用方法:"
    echo "  $0 [选项]"
    echo
    echo "选项:"
    echo "  unit        运行单元测试"
    echo "  integration 运行集成测试"
    echo "  performance 运行性能测试"
    echo "  all         运行所有测试 (默认)"
    echo "  clean       清理测试环境"
    echo "  report      生成测试报告"
    echo "  help        显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0 unit                # 只运行单元测试"
    echo "  $0 integration         # 只运行集成测试"
    echo "  $0 performance         # 只运行性能测试"
    echo "  $0                     # 运行所有测试"
}

# 处理命令行参数
if [ "$1" = "help" ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 运行主函数
main "$@"
