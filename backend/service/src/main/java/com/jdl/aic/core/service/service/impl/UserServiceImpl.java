package com.jdl.aic.core.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.dao.mapper.portal.UserFollowMapper;
import com.jdl.aic.core.service.dao.mapper.primary.KnowledgeMapper;
import org.apache.ibatis.annotations.Select;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.dto.request.user.GetUserListRequest;
import com.jdl.aic.core.service.dao.entity.portal.User;
import com.jdl.aic.core.service.dao.entity.portal.UserTeam;
import com.jdl.aic.core.service.dao.mapper.portal.UserMapper;
import com.jdl.aic.core.service.dao.mapper.portal.UserTeamMapper;
import com.jdl.aic.core.service.portal.client.UserDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("userService")
public class UserServiceImpl implements UserDataService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserTeamMapper userTeamMapper;

    @Autowired
    private UserFollowMapper userFollowMapper;

    @Autowired
    private KnowledgeMapper knowledgeMapper;

    // ==================== 用户基本信息管理 ====================

    @Override
    public Result<PageResult<UserDTO>> getUserList(PageRequest pageRequest, GetUserListRequest request) {
        log.info("开始获取用户列表 - 分页参数: {}, 查询条件: {}", pageRequest, request);

        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 查询用户列表
            List<User> users;
            String department = request != null ? request.getDepartment() : null;
            Boolean isActive = request != null ? request.getIsActive() : null;
            String search = request != null ? request.getSearch() : null;

            if (StringUtils.hasText(search)) {
                log.debug("使用搜索模式查询用户 - search: {}", search);
                users = userMapper.searchUsers(search, isActive);
            } else {
                log.debug("使用条件查询用户");
                users = userMapper.selectByCondition(department, isActive, null);
            }

            log.info("数据库查询完成，查询到 {} 条用户记录", users.size());
            PageInfo<User> pageInfo = new PageInfo<>(users);

            // 转换为DTO
            List<UserDTO> userDTOs = users.stream()
                    .map(this::convertToUserDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<UserDTO> pageResult = PageResult.of(
                    userDTOs,
                    pageInfo.getTotal(),
                    pageRequest.getPage(),
                    pageRequest.getSize());

            log.info("获取用户列表成功 - 总数: {}, 当前页: {}", pageInfo.getTotal(), pageRequest.getPage());
            return Result.success("获取用户列表成功", pageResult);

        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.errorResult("USER_LIST_ERROR", "获取用户列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<UserDTO> getUserById(Long id) {
        log.info("开始根据ID获取用户详情 - id: {}", id);
        
        try {
            if (id == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            User user = userMapper.selectById(id);
            if (user == null) {
                log.warn("用户不存在 - id: {}", id);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            UserDTO userDTO = convertToUserDTO(user);
            log.info("获取用户详情成功 - id: {}, username: {}", id, user.getUsername());
            return Result.success("获取用户详情成功", userDTO);

        } catch (Exception e) {
            log.error("获取用户详情失败 - id: {}", id, e);
            return Result.errorResult("USER_GET_ERROR", "获取用户详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<UserDTO> getUserBySsoId(String ssoId) {
        log.info("开始根据SSO ID获取用户详情 - ssoId: {}", ssoId);
        
        try {
            if (!StringUtils.hasText(ssoId)) {
                log.warn("参数校验失败 - SSO ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "SSO ID不能为空");
            }

            User user = userMapper.selectBySsoId(ssoId);
            if (user == null) {
                log.warn("用户不存在 - ssoId: {}", ssoId);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            UserDTO userDTO = convertToUserDTO(user);
            log.info("根据SSO ID获取用户详情成功 - ssoId: {}, username: {}", ssoId, user.getUsername());
            return Result.success("获取用户详情成功", userDTO);

        } catch (Exception e) {
            log.error("根据SSO ID获取用户详情失败 - ssoId: {}", ssoId, e);
            return Result.errorResult("USER_GET_ERROR", "获取用户详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<UserDTO> getUserByUsername(String username) {
        log.info("开始根据用户名获取用户详情 - username: {}", username);
        
        try {
            if (!StringUtils.hasText(username)) {
                log.warn("参数校验失败 - 用户名不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户名不能为空");
            }

            User user = userMapper.selectByUsername(username);
            if (user == null) {
                log.warn("用户不存在 - username: {}", username);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            UserDTO userDTO = convertToUserDTO(user);
            log.info("根据用户名获取用户详情成功 - username: {}", username);
            return Result.success("获取用户详情成功", userDTO);

        } catch (Exception e) {
            log.error("根据用户名获取用户详情失败 - username: {}", username, e);
            return Result.errorResult("USER_GET_ERROR", "获取用户详情失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<UserDTO> createUser(UserDTO user) {
        log.info("开始创建用户 - 入参: id={}, username={}, ssoId={}",
            user != null ? user.getId() : null,
            user != null ? user.getUsername() : null, 
            user != null ? user.getSsoId() : null);
        
        try {
            if (user == null) {
                log.warn("参数校验失败 - 用户信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户信息不能为空");
            }

            // 验证必填字段
            if (user.getId() == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            if (!StringUtils.hasText(user.getUsername())) {
                log.warn("参数校验失败 - 用户名不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户名不能为空");
            }

            if (!StringUtils.hasText(user.getSsoId())) {
                log.warn("参数校验失败 - SSO ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "SSO ID不能为空");
            }

            if (!StringUtils.hasText(user.getDisplayName())) {
                log.warn("参数校验失败 - 显示名称不能为空");
                return Result.errorResult("INVALID_PARAMETER", "显示名称不能为空");
            }

            // 检查SSO ID是否已存在
            if (userMapper.existsBySsoId(user.getSsoId(), null)) {
                log.warn("SSO ID已存在 - ssoId: {}", user.getSsoId());
                return Result.errorResult("SSO_ID_EXISTS", "SSO ID已存在");
            }

            // 检查用户名是否已存在
            if (userMapper.existsByUsername(user.getUsername(), null)) {
                log.warn("用户名已存在 - username: {}", user.getUsername());
                return Result.errorResult("USERNAME_EXISTS", "用户名已存在");
            }

            // 检查邮箱是否已存在（如果提供了邮箱）
            if (StringUtils.hasText(user.getEmail()) && userMapper.existsByEmail(user.getEmail(), null)) {
                log.warn("邮箱已存在 - email: {}", user.getEmail());
                return Result.errorResult("EMAIL_EXISTS", "邮箱已存在");
            }

            // 转换为实体对象
            User entity = convertToUserEntity(user);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            
            // 设置默认值
            if (entity.getIsActive() == null) {
                entity.setIsActive(true);
            }

            // 插入数据库
            int result = userMapper.insert(entity);
            if (result > 0) {
                log.info("用户创建成功 - id: {}, username: {}, ssoId: {}", 
                    entity.getId(), entity.getUsername(), entity.getSsoId());
                UserDTO resultDto = convertToUserDTO(entity);
                return Result.success("用户创建成功", resultDto);
            } else {
                log.error("用户创建失败 - 数据库插入返回结果: {}, username: {}", result, entity.getUsername());
                return Result.errorResult("USER_CREATE_FAILED", "用户创建失败");
            }

        } catch (Exception e) {
            log.error("创建用户失败", e);
            return Result.errorResult("USER_CREATE_ERROR", "创建用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<UserDTO> updateUser(Long id, UserDTO user) {
        log.info("开始更新用户 - id: {}, 入参: username={}", id, user != null ? user.getUsername() : null);
        
        try {
            if (id == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            if (user == null) {
                log.warn("参数校验失败 - 用户信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户信息不能为空");
            }

            // 检查用户是否存在
            User existingUser = userMapper.selectById(id);
            if (existingUser == null) {
                log.warn("用户不存在 - id: {}", id);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            // 检查SSO ID是否已被其他用户使用
            if (StringUtils.hasText(user.getSsoId()) && userMapper.existsBySsoId(user.getSsoId(), id)) {
                log.warn("SSO ID已被其他用户使用 - ssoId: {}", user.getSsoId());
                return Result.errorResult("SSO_ID_EXISTS", "SSO ID已被其他用户使用");
            }

            // 检查用户名是否已被其他用户使用
            if (StringUtils.hasText(user.getUsername()) && userMapper.existsByUsername(user.getUsername(), id)) {
                log.warn("用户名已被其他用户使用 - username: {}", user.getUsername());
                return Result.errorResult("USERNAME_EXISTS", "用户名已被其他用户使用");
            }

            // 检查邮箱是否已被其他用户使用
            if (StringUtils.hasText(user.getEmail()) && userMapper.existsByEmail(user.getEmail(), id)) {
                log.warn("邮箱已被其他用户使用 - email: {}", user.getEmail());
                return Result.errorResult("EMAIL_EXISTS", "邮箱已被其他用户使用");
            }

            // 更新用户信息
            User updateEntity = new User();
            updateEntity.setId(id);
            updateEntity.setUpdatedAt(LocalDateTime.now());
            
            // 只更新非空字段
            if (StringUtils.hasText(user.getSsoId())) {
                updateEntity.setSsoId(user.getSsoId());
            }
            if (StringUtils.hasText(user.getUsername())) {
                updateEntity.setUsername(user.getUsername());
            }
            if (StringUtils.hasText(user.getDisplayName())) {
                updateEntity.setDisplayName(user.getDisplayName());
            }
            if (StringUtils.hasText(user.getEmail())) {
                updateEntity.setEmail(user.getEmail());
            }
            if (StringUtils.hasText(user.getAvatarUrl())) {
                updateEntity.setAvatarUrl(user.getAvatarUrl());
            }
            if (StringUtils.hasText(user.getDepartment())) {
                updateEntity.setDepartment(user.getDepartment());
            }
            if (StringUtils.hasText(user.getTitle())) {
                updateEntity.setTitle(user.getTitle());
            }
            if (StringUtils.hasText(user.getBio())) {
                updateEntity.setBio(user.getBio());
            }
            if (user.getTags() != null && !user.getTags().isEmpty()) {
                updateEntity.setTags(JSON.toJSONString(user.getTags()));
            }
            if (user.getIsActive() != null) {
                updateEntity.setIsActive(user.getIsActive());
            }

            int result = userMapper.updateById(updateEntity);
            if (result > 0) {
                // 查询更新后的用户信息
                User updatedUser = userMapper.selectById(id);
                UserDTO resultDto = convertToUserDTO(updatedUser);
                log.info("用户更新成功 - id: {}, username: {}", id, resultDto.getUsername());
                return Result.success("用户更新成功", resultDto);
            } else {
                log.error("用户更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("USER_UPDATE_FAILED", "用户更新失败");
            }

        } catch (Exception e) {
            log.error("更新用户失败 - id: {}", id, e);
            return Result.errorResult("USER_UPDATE_ERROR", "更新用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteUser(Long id) {
        log.info("开始删除用户 - id: {}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 检查用户是否存在
            User existingUser = userMapper.selectById(id);
            if (existingUser == null) {
                log.warn("用户不存在 - id: {}", id);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            // 删除用户的所有团队关联
            userTeamMapper.deleteAllByUserId(id);

            // 删除用户
            int result = userMapper.deleteById(id);
            if (result > 0) {
                log.info("用户删除成功 - id: {}, username: {}", id, existingUser.getUsername());
                return Result.success();
            } else {
                log.error("用户删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("USER_DELETE_FAILED", "用户删除失败");
            }

        } catch (Exception e) {
            log.error("删除用户失败 - id: {}", id, e);
            return Result.errorResult("USER_DELETE_ERROR", "删除用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> toggleUserStatus(Long id, Boolean isActive) {
        log.info("开始切换用户状态 - id: {}, isActive: {}", id, isActive);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            if (isActive == null) {
                log.warn("参数校验失败 - 状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "状态不能为空");
            }

            // 检查用户是否存在
            User existingUser = userMapper.selectById(id);
            if (existingUser == null) {
                log.warn("用户不存在 - id: {}", id);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            int result = userMapper.toggleUserStatus(id, isActive);
            if (result > 0) {
                log.info("用户状态切换成功 - id: {}, isActive: {}", id, isActive);
                return Result.success();
            } else {
                log.error("用户状态切换失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("USER_STATUS_UPDATE_FAILED", "用户状态切换失败");
            }

        } catch (Exception e) {
            log.error("切换用户状态失败 - id: {}, isActive: {}", id, isActive, e);
            return Result.errorResult("USER_STATUS_UPDATE_ERROR", "切换用户状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<List<UserDTO>> batchImportUsers(List<UserDTO> users) {
        log.info("开始批量导入用户 - 数量: {}", users != null ? users.size() : 0);

        try {
            if (users == null || users.isEmpty()) {
                log.warn("参数校验失败 - 用户列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户列表不能为空");
            }

            List<UserDTO> successUsers = new ArrayList<>();
            List<String> errors = new ArrayList<>();

            for (UserDTO user : users) {
                try {
                    Result<UserDTO> createResult = createUser(user);
                    if (createResult.isSuccess()) {
                        successUsers.add(createResult.getData());
                    } else {
                        errors.add("用户 " + user.getUsername() + ": " + createResult.getMessage());
                    }
                } catch (Exception e) {
                    errors.add("用户 " + user.getUsername() + ": " + e.getMessage());
                }
            }

            if (!errors.isEmpty()) {
                log.warn("批量导入用户部分失败 - 成功: {}, 失败: {}, 错误: {}",
                    successUsers.size(), errors.size(), errors);
            }

            log.info("批量导入用户完成 - 成功: {}, 失败: {}", successUsers.size(), errors.size());
            return Result.success("批量导入用户完成，成功: " + successUsers.size() + "，失败: " + errors.size(), successUsers);

        } catch (Exception e) {
            log.error("批量导入用户失败", e);
            return Result.errorResult("USER_BATCH_IMPORT_ERROR", "批量导入用户失败: " + e.getMessage());
        }
    }

    // ==================== 用户认证相关 ====================

    @Override
    public Result<UserDTO> login(String ssoId) {
        log.info("开始用户登录 - ssoId: {}", ssoId);

        try {
            if (!StringUtils.hasText(ssoId)) {
                log.warn("参数校验失败 - SSO ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "SSO ID不能为空");
            }

            User user = userMapper.selectBySsoId(ssoId);
            if (user == null) {
                log.warn("用户不存在 - ssoId: {}", ssoId);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            if (!Boolean.TRUE.equals(user.getIsActive())) {
                log.warn("用户已被禁用 - ssoId: {}", ssoId);
                return Result.errorResult("USER_DISABLED", "用户已被禁用");
            }

            UserDTO userDTO = convertToUserDTO(user);
            log.info("用户登录成功 - ssoId: {}, username: {}", ssoId, user.getUsername());
            return Result.success("用户登录成功", userDTO);

        } catch (Exception e) {
            log.error("用户登录失败 - ssoId: {}", ssoId, e);
            return Result.errorResult("USER_LOGIN_ERROR", "用户登录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateLastLoginTime(Long id) {
        log.info("开始更新用户最后登录时间 - id: {}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            int result = userMapper.updateLastLoginTime(id, LocalDateTime.now());
            if (result > 0) {
                log.info("更新用户最后登录时间成功 - id: {}", id);
                return Result.success();
            } else {
                log.error("更新用户最后登录时间失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("UPDATE_LOGIN_TIME_FAILED", "更新用户最后登录时间失败");
            }

        } catch (Exception e) {
            log.error("更新用户最后登录时间失败 - id: {}", id, e);
            return Result.errorResult("UPDATE_LOGIN_TIME_ERROR", "更新用户最后登录时间失败: " + e.getMessage());
        }
    }

    @Override
    public Result<UserDTO> getCurrentUser(Long userId) {
        log.info("开始获取当前用户信息 - userId: {}", userId);
        return getUserById(userId);
    }

    @Override
    @Transactional
    public Result<UserDTO> updateCurrentUserProfile(Long userId, UserDTO user) {
        log.info("开始更新当前用户个人信息 - userId: {}", userId);

        // 限制只能更新部分字段
        UserDTO limitedUser = new UserDTO();
        limitedUser.setDisplayName(user.getDisplayName());
        limitedUser.setEmail(user.getEmail());
        limitedUser.setAvatarUrl(user.getAvatarUrl());
        limitedUser.setBio(user.getBio());
        limitedUser.setTags(user.getTags());

        return updateUser(userId, limitedUser);
    }

    // ==================== 用户统计和偏好设置 ====================

    @Override
    public Result<UserDataService.UserStatsDTO> getUserStats(Long userId) {
        log.info("开始获取用户统计信息 - userId: {}", userId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在 - userId: {}", userId);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            // 创建统计信息（这里可以根据实际需求查询相关统计数据）
            UserDataService.UserStatsDTO stats = new UserDataService.UserStatsDTO();
            stats.setCreatedKnowledgeCount(0); // TODO: 实现实际统计逻辑
            stats.setFavoriteKnowledgeCount(0);
            stats.setTotalReadCount(0);
            stats.setTotalLikeCount(0);
            stats.setCommentCount(0);

            log.info("获取用户统计信息成功 - userId: {}", userId);
            return Result.success("获取用户统计信息成功", stats);

        } catch (Exception e) {
            log.error("获取用户统计信息失败 - userId: {}", userId, e);
            return Result.errorResult("USER_STATS_ERROR", "获取用户统计信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateUserPreferences(Long userId, Object preferences) {
        log.info("开始更新用户偏好设置 - userId: {}", userId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在 - userId: {}", userId);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            // TODO: 实现偏好设置更新逻辑
            // 这里可以将偏好设置存储到用户表的某个字段或单独的偏好设置表中

            log.info("更新用户偏好设置成功 - userId: {}", userId);
            return Result.success();

        } catch (Exception e) {
            log.error("更新用户偏好设置失败 - userId: {}", userId, e);
            return Result.errorResult("USER_PREFERENCES_UPDATE_ERROR", "更新用户偏好设置失败: " + e.getMessage());
        }
    }

    // ==================== 用户团队管理 ====================

    @Override
    public Result<List<Long>> getUserTeams(Long userId) {
        log.info("开始获取用户所属团队列表 - userId: {}", userId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在 - userId: {}", userId);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            List<Long> teamIds = userTeamMapper.selectTeamIdsByUserId(userId);
            log.info("获取用户所属团队列表成功 - userId: {}, 团队数量: {}", userId, teamIds.size());
            return Result.success("获取用户所属团队列表成功", teamIds);

        } catch (Exception e) {
            log.error("获取用户所属团队列表失败 - userId: {}", userId, e);
            return Result.errorResult("USER_TEAMS_ERROR", "获取用户所属团队列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> addUserToTeam(Long userId, Long teamId, Integer role) {
        log.info("开始将用户添加到团队 - userId: {}, teamId: {}, role: {}", userId, teamId, role);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            if (role == null) {
                role = 0; // 默认为成员
            }

            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在 - userId: {}", userId);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            // 检查是否已存在关联
            if (userTeamMapper.existsByUserIdAndTeamId(userId, teamId)) {
                log.warn("用户已在团队中 - userId: {}, teamId: {}", userId, teamId);
                return Result.errorResult("USER_ALREADY_IN_TEAM", "用户已在团队中");
            }

            // 创建用户团队关联
            UserTeam userTeam = new UserTeam();
            userTeam.setUserId(userId);
            userTeam.setTeamId(teamId);
            userTeam.setRole(role);
            userTeam.setJoinedAt(LocalDateTime.now());
            userTeam.setCreatedAt(LocalDateTime.now());
            userTeam.setUpdatedAt(LocalDateTime.now());

            int result = userTeamMapper.insert(userTeam);
            if (result > 0) {
                log.info("将用户添加到团队成功 - userId: {}, teamId: {}, role: {}", userId, teamId, role);
                return Result.success();
            } else {
                log.error("将用户添加到团队失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("ADD_USER_TO_TEAM_FAILED", "将用户添加到团队失败");
            }

        } catch (Exception e) {
            log.error("将用户添加到团队失败 - userId: {}, teamId: {}, role: {}", userId, teamId, role, e);
            return Result.errorResult("ADD_USER_TO_TEAM_ERROR", "将用户添加到团队失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> removeUserFromTeam(Long userId, Long teamId) {
        log.info("开始将用户从团队中移除 - userId: {}, teamId: {}", userId, teamId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            // 检查关联是否存在
            if (!userTeamMapper.existsByUserIdAndTeamId(userId, teamId)) {
                log.warn("用户不在团队中 - userId: {}, teamId: {}", userId, teamId);
                return Result.errorResult("USER_NOT_IN_TEAM", "用户不在团队中");
            }

            int result = userTeamMapper.deleteByUserIdAndTeamId(userId, teamId);
            if (result > 0) {
                log.info("将用户从团队中移除成功 - userId: {}, teamId: {}", userId, teamId);
                return Result.success();
            } else {
                log.error("将用户从团队中移除失败 - 数据库删除返回结果: {}", result);
                return Result.errorResult("REMOVE_USER_FROM_TEAM_FAILED", "将用户从团队中移除失败");
            }

        } catch (Exception e) {
            log.error("将用户从团队中移除失败 - userId: {}, teamId: {}", userId, teamId, e);
            return Result.errorResult("REMOVE_USER_FROM_TEAM_ERROR", "将用户从团队中移除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateUserTeamRole(Long userId, Long teamId, Integer role) {
        log.info("开始更新用户在团队中的角色 - userId: {}, teamId: {}, role: {}", userId, teamId, role);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            if (role == null) {
                log.warn("参数校验失败 - 角色不能为空");
                return Result.errorResult("INVALID_PARAMETER", "角色不能为空");
            }

            // 检查关联是否存在
            if (!userTeamMapper.existsByUserIdAndTeamId(userId, teamId)) {
                log.warn("用户不在团队中 - userId: {}, teamId: {}", userId, teamId);
                return Result.errorResult("USER_NOT_IN_TEAM", "用户不在团队中");
            }

            int result = userTeamMapper.updateUserTeamRole(userId, teamId, role);
            if (result > 0) {
                log.info("更新用户在团队中的角色成功 - userId: {}, teamId: {}, role: {}", userId, teamId, role);
                return Result.success();
            } else {
                log.error("更新用户在团队中的角色失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("UPDATE_USER_TEAM_ROLE_FAILED", "更新用户在团队中的角色失败");
            }

        } catch (Exception e) {
            log.error("更新用户在团队中的角色失败 - userId: {}, teamId: {}, role: {}", userId, teamId, role, e);
            return Result.errorResult("UPDATE_USER_TEAM_ROLE_ERROR", "更新用户在团队中的角色失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<UserDTO>> getTeamMembers(Long teamId, PageRequest pageRequest) {
        log.info("开始获取团队成员列表 - teamId: {}, 分页参数: {}", teamId, pageRequest);

        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 查询团队成员
            List<User> users = userTeamMapper.selectUsersByTeamId(teamId);
            PageInfo<User> pageInfo = new PageInfo<>(users);

            // 转换为DTO
            List<UserDTO> userDTOs = users.stream()
                    .map(this::convertToUserDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<UserDTO> pageResult = PageResult.of(
                    userDTOs,
                    pageInfo.getTotal(),
                    pageRequest.getPage(),
                    pageRequest.getSize());

            log.info("获取团队成员列表成功 - teamId: {}, 总数: {}", teamId, pageInfo.getTotal());
            return Result.success("获取团队成员列表成功", pageResult);

        } catch (Exception e) {
            log.error("获取团队成员列表失败 - teamId: {}", teamId, e);
            return Result.errorResult("TEAM_MEMBERS_ERROR", "获取团队成员列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getUserTeamRole(Long userId, Long teamId) {
        log.info("开始获取用户在团队中的角色 - userId: {}, teamId: {}", userId, teamId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            Integer role = userTeamMapper.selectUserTeamRole(userId, teamId);
            if (role == null) {
                log.warn("用户不在团队中 - userId: {}, teamId: {}", userId, teamId);
                return Result.errorResult("USER_NOT_IN_TEAM", "用户不在团队中");
            }

            log.info("获取用户在团队中的角色成功 - userId: {}, teamId: {}, role: {}", userId, teamId, role);
            return Result.success("获取用户在团队中的角色成功", role);

        } catch (Exception e) {
            log.error("获取用户在团队中的角色失败 - userId: {}, teamId: {}", userId, teamId, e);
            return Result.errorResult("USER_TEAM_ROLE_ERROR", "获取用户在团队中的角色失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<List<Object>> batchAddUsersToTeam(List<Long> userIds, Long teamId, Integer role) {
        log.info("开始批量添加用户到团队 - userIds: {}, teamId: {}, role: {}", userIds, teamId, role);

        try {
            if (userIds == null || userIds.isEmpty()) {
                log.warn("参数校验失败 - 用户ID列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID列表不能为空");
            }

            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            if (role == null) {
                role = 0; // 默认为成员
            }

            List<Object> results = new ArrayList<>();
            List<UserTeam> userTeams = new ArrayList<>();

            for (Long userId : userIds) {
                try {
                    // 检查用户是否存在
                    User user = userMapper.selectById(userId);
                    if (user == null) {
                        results.add("用户不存在: " + userId);
                        continue;
                    }

                    // 检查是否已存在关联
                    if (userTeamMapper.existsByUserIdAndTeamId(userId, teamId)) {
                        results.add("用户已在团队中: " + userId);
                        continue;
                    }

                    // 创建用户团队关联
                    UserTeam userTeam = new UserTeam();
                    userTeam.setUserId(userId);
                    userTeam.setTeamId(teamId);
                    userTeam.setRole(role);
                    userTeam.setJoinedAt(LocalDateTime.now());
                    userTeam.setCreatedAt(LocalDateTime.now());
                    userTeam.setUpdatedAt(LocalDateTime.now());
                    userTeams.add(userTeam);

                    results.add("成功: " + userId);

                } catch (Exception e) {
                    results.add("失败: " + userId + " - " + e.getMessage());
                }
            }

            // 批量插入
            if (!userTeams.isEmpty()) {
                userTeamMapper.batchInsert(userTeams);
            }

            log.info("批量添加用户到团队完成 - teamId: {}, 处理数量: {}", teamId, userIds.size());
            return Result.success("批量添加用户到团队完成", results);

        } catch (Exception e) {
            log.error("批量添加用户到团队失败 - userIds: {}, teamId: {}, role: {}", userIds, teamId, role, e);
            return Result.errorResult("BATCH_ADD_USERS_TO_TEAM_ERROR", "批量添加用户到团队失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> batchRemoveUsersFromTeam(List<Long> userIds, Long teamId) {
        return null;
    }

    @Override
    public Result<UserSocialStatsDTO> getUserSocialStats(Long userId) {
        log.info("开始获取用户社交统计信息 - userId: {}", userId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 检查用户是否存在
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在 - userId: {}", userId);
                return Result.errorResult("USER_NOT_FOUND", "用户不存在");
            }

            // 创建社交统计信息
            UserSocialStatsDTO stats = new UserSocialStatsDTO();

            // 统计关注数量
            int followingCount = userFollowMapper.countFollowingByUserId(userId);
            stats.setFollowingCount(followingCount);

            // 统计粉丝数量
            int followersCount = userFollowMapper.countFollowersByFollowedId(userId);
            stats.setFollowersCount(followersCount);

            // 统计已发布文章数量（status = 2 表示已发布状态）
            int publishedArticleCount = knowledgeMapper.countByAuthorIdAndStatus(userId.toString(), 2);
            stats.setPublishedArticleCount(publishedArticleCount);

            log.info("获取用户社交统计信息成功 - userId: {}, followingCount: {}, followersCount: {}, publishedArticleCount: {}",
                    userId, followingCount, followersCount, publishedArticleCount);
            return Result.success("获取用户社交统计信息成功", stats);

        } catch (Exception e) {
            log.error("获取用户社交统计信息失败 - userId: {}", userId, e);
            return Result.errorResult("USER_SOCIAL_STATS_ERROR", "获取用户社交统计信息失败: " + e.getMessage());
        }
    }

    // ==================== 转换工具方法 ====================

    /**
     * 将User实体转换为UserDTO
     *
     * @param user User实体
     * @return UserDTO
     */
    private UserDTO convertToUserDTO(User user) {
        if (user == null) {
            return null;
        }

        UserDTO dto = new UserDTO();
        BeanUtils.copyProperties(user, dto, "tags"); // 排除tags字段，单独处理

        // 处理tags字段的JSON转换
        if (StringUtils.hasText(user.getTags())) {
            try {
                List<String> tags = JSON.parseObject(user.getTags(), new TypeReference<List<String>>() {});
                dto.setTags(tags);
            } catch (Exception e) {
                log.warn("解析用户标签JSON失败 - userId: {}, tags: {}", user.getId(), user.getTags(), e);
                dto.setTags(new ArrayList<>());
            }
        }

        return dto;
    }

    /**
     * 将UserDTO转换为User实体
     *
     * @param userDTO UserDTO
     * @return User实体
     */
    private User convertToUserEntity(UserDTO userDTO) {
        if (userDTO == null) {
            return null;
        }

        User entity = new User();
        BeanUtils.copyProperties(userDTO, entity, "tags"); // 排除tags字段，单独处理

        // 处理tags字段的JSON转换
        if (userDTO.getTags() != null && !userDTO.getTags().isEmpty()) {
            try {
                entity.setTags(JSON.toJSONString(userDTO.getTags()));
            } catch (Exception e) {
                log.warn("转换用户标签为JSON失败 - tags: {}", userDTO.getTags(), e);
            }
        }

        return entity;
    }
}
