package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.rss.RssSourceDTO;
import com.jdl.aic.core.service.client.dto.request.rss.GetRssSourceListRequest;
import com.jdl.aic.core.service.client.dto.request.rss.UpdateRssSourceStatusRequest;
import com.jdl.aic.core.service.client.dto.request.rss.GetRssSourcesByCategoryRequest;
import com.jdl.aic.core.service.client.service.RssSourceService;
import com.jdl.aic.core.service.dao.entity.primary.RssSource;
import com.jdl.aic.core.service.dao.mapper.primary.RssSourceMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * RSS源管理服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("rssSourceService")
public class RssSourceServiceImpl implements RssSourceService {

    @Resource
    private RssSourceMapper rssSourceMapper;

    @Override
    public Result<PageResult<RssSourceDTO>> getRssSourceList(PageRequest pageRequest, GetRssSourceListRequest request) {
        log.info("调用getRssSourceList方法 - 参数: pageRequest={}, request={}", pageRequest, request);
        
        try {
            // 设置分页参数 (PageHelper使用1开始的页码，而PageRequest使用0开始的页码)
            PageHelper.startPage(pageRequest.getPage() + 1, pageRequest.getSize());
            log.debug("设置分页参数 - page: {}, size: {}", pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            String search = request != null ? request.getSearch() : null;
            String category = request != null ? request.getCategory() : null;
            Integer type = request != null ? request.getType() : null;
            Integer status = request != null ? request.getStatus() : null;
            String ownerId = request != null ? request.getOwnerId() : null;
            Boolean activeOnly = request != null ? request.getActiveOnly() : null;

            // 如果activeOnly为true，强制设置status为活跃状态
            if (Boolean.TRUE.equals(activeOnly)) {
                status = RssSource.Status.ACTIVE;
            }

            // 查询RSS源列表
            List<RssSource> rssSources;
            if (StringUtils.hasText(search)) {
                log.debug("使用搜索模式查询RSS源 - search: {}", search);
                rssSources = rssSourceMapper.searchRssSources(search, category, type, status);
            } else {
                log.debug("使用条件查询RSS源");
                RssSource queryCondition = new RssSource();
                queryCondition.setCategory(category);
                queryCondition.setType(type);
                queryCondition.setStatus(status);
                queryCondition.setOwnerId(ownerId);
                rssSources = rssSourceMapper.selectByCondition(queryCondition);
            }
            log.info("数据库查询完成，查询到 {} 条RSS源记录", rssSources.size());

            // 使用PageInfo获取分页信息
            PageInfo<RssSource> pageInfo = new PageInfo<>(rssSources);

            // 转换为DTO
            List<RssSourceDTO> rssSourceDTOs = rssSources.stream()
                    .map(this::convertToRssSourceDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<RssSourceDTO> pageResult = PageResult.of(
                rssSourceDTOs,
                pageInfo.getTotal(),
                pageRequest.getPage(),
                pageRequest.getSize()
            );

            log.info("RSS源列表查询成功 - 总记录数: {}, 当前页: {}, 页大小: {}",
                pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());
            return Result.success("RSS源列表查询成功", pageResult);

        } catch (Exception e) {
            log.error("RSS源列表查询失败", e);
            return Result.errorResult("RSS_SOURCE_LIST_QUERY_FAILED", "RSS源列表查询失败: " + e.getMessage());
        }
    }

    @Override
    public Result<RssSourceDTO> getRssSourceById(Long id) {
        log.info("调用getRssSourceById方法 - 参数: id={}", id);
        
        if (id == null) {
            log.warn("RSS源ID不能为空");
            return Result.errorResult("INVALID_PARAMETER", "RSS源ID不能为空");
        }

        try {
            RssSource rssSource = rssSourceMapper.selectById(id);
            if (rssSource == null) {
                log.warn("RSS源不存在 - id: {}", id);
                return Result.errorResult("RSS_SOURCE_NOT_FOUND", "RSS源不存在");
            }

            RssSourceDTO rssSourceDTO = convertToRssSourceDTO(rssSource);
            log.info("RSS源查询成功 - id: {}, name: {}", id, rssSource.getName());
            return Result.success("RSS源查询成功", rssSourceDTO);

        } catch (Exception e) {
            log.error("RSS源查询失败 - id: {}", id, e);
            return Result.errorResult("RSS_SOURCE_QUERY_FAILED", "RSS源查询失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<RssSourceDTO> createRssSource(RssSourceDTO rssSource) {
        log.info("调用createRssSource方法 - 参数: rssSource={}", rssSource);
        
        if (rssSource == null) {
            log.warn("RSS源信息不能为空");
            return Result.errorResult("INVALID_PARAMETER", "RSS源信息不能为空");
        }

        try {
            // 检查RSS订阅地址是否已存在
            RssSource existingRssSource = rssSourceMapper.selectByFeedUrl(rssSource.getFeedUrl(), null);
            if (existingRssSource != null) {
                log.warn("RSS订阅地址已存在 - feedUrl: {}", rssSource.getFeedUrl());
                return Result.errorResult("RSS_FEED_URL_EXISTS", "RSS订阅地址已存在");
            }

            // 转换为实体对象
            RssSource entity = convertToRssSourceEntity(rssSource);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());

            // 设置默认状态
            if (entity.getStatus() == null) {
                entity.setStatus(RssSource.Status.ACTIVE);
            }

            // 插入数据库
            int result = rssSourceMapper.insert(entity);
            if (result > 0) {
                log.info("RSS源创建成功 - id: {}, name: {}, feedUrl: {}", 
                    entity.getId(), entity.getName(), entity.getFeedUrl());
                RssSourceDTO resultDto = convertToRssSourceDTO(entity);
                return Result.success("RSS源创建成功", resultDto);
            } else {
                log.error("RSS源创建失败 - 数据库插入返回结果: {}, name: {}", result, entity.getName());
                return Result.errorResult("RSS_SOURCE_CREATE_FAILED", "RSS源创建失败");
            }

        } catch (Exception e) {
            log.error("RSS源创建失败 - name: {}", rssSource.getName(), e);
            return Result.errorResult("RSS_SOURCE_CREATE_FAILED", "RSS源创建失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<RssSourceDTO> updateRssSource(Long id, RssSourceDTO rssSource) {
        log.info("调用updateRssSource方法 - 参数: id={}, rssSource={}", id, rssSource);
        
        if (id == null || rssSource == null) {
            log.warn("RSS源ID和RSS源信息不能为空");
            return Result.errorResult("INVALID_PARAMETER", "RSS源ID和RSS源信息不能为空");
        }

        try {
            // 检查RSS源是否存在
            RssSource existingRssSource = rssSourceMapper.selectById(id);
            if (existingRssSource == null) {
                log.warn("RSS源不存在 - id: {}", id);
                return Result.errorResult("RSS_SOURCE_NOT_FOUND", "RSS源不存在");
            }

            // 检查RSS订阅地址是否已被其他RSS源使用
            if (StringUtils.hasText(rssSource.getFeedUrl()) && 
                !rssSource.getFeedUrl().equals(existingRssSource.getFeedUrl())) {
                RssSource duplicateRssSource = rssSourceMapper.selectByFeedUrl(rssSource.getFeedUrl(), id);
                if (duplicateRssSource != null) {
                    log.warn("RSS订阅地址已被其他RSS源使用 - feedUrl: {}", rssSource.getFeedUrl());
                    return Result.errorResult("RSS_FEED_URL_EXISTS", "RSS订阅地址已被其他RSS源使用");
                }
            }

            // 转换为实体对象并设置ID
            RssSource entity = convertToRssSourceEntity(rssSource);
            entity.setId(id);
            entity.setUpdatedAt(LocalDateTime.now());

            // 更新数据库
            int result = rssSourceMapper.updateById(entity);
            if (result > 0) {
                // 查询更新后的数据
                RssSource updatedRssSource = rssSourceMapper.selectById(id);
                RssSourceDTO resultDto = convertToRssSourceDTO(updatedRssSource);
                log.info("RSS源更新成功 - id: {}, name: {}", id, updatedRssSource.getName());
                return Result.success("RSS源更新成功", resultDto);
            } else {
                log.error("RSS源更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("RSS_SOURCE_UPDATE_FAILED", "RSS源更新失败");
            }

        } catch (Exception e) {
            log.error("RSS源更新失败 - id: {}", id, e);
            return Result.errorResult("RSS_SOURCE_UPDATE_FAILED", "RSS源更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteRssSource(Long id) {
        log.info("调用deleteRssSource方法 - 参数: id={}", id);
        
        if (id == null) {
            log.warn("RSS源ID不能为空");
            return Result.errorResult("INVALID_PARAMETER", "RSS源ID不能为空");
        }

        try {
            // 检查RSS源是否存在
            RssSource existingRssSource = rssSourceMapper.selectById(id);
            if (existingRssSource == null) {
                log.warn("RSS源不存在 - id: {}", id);
                return Result.errorResult("RSS_SOURCE_NOT_FOUND", "RSS源不存在");
            }

            // 软删除RSS源
            int result = rssSourceMapper.deleteById(id);
            if (result > 0) {
                log.info("RSS源删除成功 - id: {}, name: {}", id, existingRssSource.getName());
                return Result.success();
            } else {
                log.error("RSS源删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("RSS_SOURCE_DELETE_FAILED", "RSS源删除失败");
            }

        } catch (Exception e) {
            log.error("RSS源删除失败 - id: {}", id, e);
            return Result.errorResult("RSS_SOURCE_DELETE_FAILED", "RSS源删除失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateRssSourceStatus(UpdateRssSourceStatusRequest request) {
        log.info("调用updateRssSourceStatus方法 - 参数: request={}", request);

        if (request == null || request.getId() == null || request.getStatus() == null) {
            log.warn("请求参数不能为空");
            return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
        }

        try {
            // 检查RSS源是否存在
            RssSource existingRssSource = rssSourceMapper.selectById(request.getId());
            if (existingRssSource == null) {
                log.warn("RSS源不存在 - id: {}", request.getId());
                return Result.errorResult("RSS_SOURCE_NOT_FOUND", "RSS源不存在");
            }

            // 更新状态
            int result = rssSourceMapper.updateStatus(request.getId(), request.getStatus());
            if (result > 0) {
                log.info("RSS源状态更新成功 - id: {}, status: {} -> {}",
                    request.getId(), existingRssSource.getStatus(), request.getStatus());
                return Result.success();
            } else {
                log.error("RSS源状态更新失败 - 数据库更新返回结果: {}, id: {}", result, request.getId());
                return Result.errorResult("RSS_SOURCE_STATUS_UPDATE_FAILED", "RSS源状态更新失败");
            }

        } catch (Exception e) {
            log.error("RSS源状态更新失败 - id: {}", request.getId(), e);
            return Result.errorResult("RSS_SOURCE_STATUS_UPDATE_FAILED", "RSS源状态更新失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<RssSourceDTO>> getRssSourcesByCategory(GetRssSourcesByCategoryRequest request) {
        log.info("调用getRssSourcesByCategory方法 - 参数: request={}", request);

        try {
            String category = request != null ? request.getCategory() : null;
            Boolean activeOnly = request != null ? request.getActiveOnly() : null;
            Integer type = request != null ? request.getType() : null;

            // 如果activeOnly为true，设置status为活跃状态
            Integer status = Boolean.TRUE.equals(activeOnly) ? RssSource.Status.ACTIVE : null;

            List<RssSource> rssSources = rssSourceMapper.selectByCategory(category, status, type);
            List<RssSourceDTO> rssSourceDTOs = rssSources.stream()
                    .map(this::convertToRssSourceDTO)
                    .collect(Collectors.toList());

            log.info("根据分类查询RSS源成功 - category: {}, 查询到 {} 条记录", category, rssSourceDTOs.size());
            return Result.success("根据分类查询RSS源成功", rssSourceDTOs);

        } catch (Exception e) {
            log.error("根据分类查询RSS源失败", e);
            return Result.errorResult("RSS_SOURCE_CATEGORY_QUERY_FAILED", "根据分类查询RSS源失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<RssSourceDTO>> getActiveRssSources() {
        log.info("调用getActiveRssSources方法");

        try {
            List<RssSource> rssSources = rssSourceMapper.selectByStatus(RssSource.Status.ACTIVE);
            List<RssSourceDTO> rssSourceDTOs = rssSources.stream()
                    .map(this::convertToRssSourceDTO)
                    .collect(Collectors.toList());

            log.info("查询活跃RSS源成功 - 查询到 {} 条记录", rssSourceDTOs.size());
            return Result.success("查询活跃RSS源成功", rssSourceDTOs);

        } catch (Exception e) {
            log.error("查询活跃RSS源失败", e);
            return Result.errorResult("ACTIVE_RSS_SOURCE_QUERY_FAILED", "查询活跃RSS源失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<RssSourceDTO>> getUserRssSources(String ownerId) {
        log.info("调用getUserRssSources方法 - 参数: ownerId={}", ownerId);

        if (!StringUtils.hasText(ownerId)) {
            log.warn("用户ID不能为空");
            return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
        }

        try {
            List<RssSource> rssSources = rssSourceMapper.selectByOwnerId(ownerId, null);
            List<RssSourceDTO> rssSourceDTOs = rssSources.stream()
                    .map(this::convertToRssSourceDTO)
                    .collect(Collectors.toList());

            log.info("查询用户RSS源成功 - ownerId: {}, 查询到 {} 条记录", ownerId, rssSourceDTOs.size());
            return Result.success("查询用户RSS源成功", rssSourceDTOs);

        } catch (Exception e) {
            log.error("查询用户RSS源失败 - ownerId: {}", ownerId, e);
            return Result.errorResult("USER_RSS_SOURCE_QUERY_FAILED", "查询用户RSS源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateLastFetchedTime(Long id) {
        log.info("调用updateLastFetchedTime方法 - 参数: id={}", id);

        if (id == null) {
            log.warn("RSS源ID不能为空");
            return Result.errorResult("INVALID_PARAMETER", "RSS源ID不能为空");
        }

        try {
            // 检查RSS源是否存在
            RssSource existingRssSource = rssSourceMapper.selectById(id);
            if (existingRssSource == null) {
                log.warn("RSS源不存在 - id: {}", id);
                return Result.errorResult("RSS_SOURCE_NOT_FOUND", "RSS源不存在");
            }

            // 更新最后抓取时间
            int result = rssSourceMapper.updateLastFetchedTime(id, LocalDateTime.now());
            if (result > 0) {
                log.info("RSS源最后抓取时间更新成功 - id: {}", id);
                return Result.success();
            } else {
                log.error("RSS源最后抓取时间更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("RSS_SOURCE_FETCH_TIME_UPDATE_FAILED", "RSS源最后抓取时间更新失败");
            }

        } catch (Exception e) {
            log.error("RSS源最后抓取时间更新失败 - id: {}", id, e);
            return Result.errorResult("RSS_SOURCE_FETCH_TIME_UPDATE_FAILED", "RSS源最后抓取时间更新失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchUpdateStatus(List<Long> ids, Integer status) {
        log.info("调用batchUpdateStatus方法 - 参数: ids={}, status={}", ids, status);

        if (ids == null || ids.isEmpty() || status == null) {
            log.warn("RSS源ID列表和状态不能为空");
            return Result.errorResult("INVALID_PARAMETER", "RSS源ID列表和状态不能为空");
        }

        try {
            int result = rssSourceMapper.batchUpdateStatus(ids, status);
            if (result > 0) {
                log.info("批量更新RSS源状态成功 - 更新了 {} 条记录, status: {}", result, status);
                return Result.success();
            } else {
                log.error("批量更新RSS源状态失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("RSS_SOURCE_BATCH_UPDATE_FAILED", "批量更新RSS源状态失败");
            }

        } catch (Exception e) {
            log.error("批量更新RSS源状态失败", e);
            return Result.errorResult("RSS_SOURCE_BATCH_UPDATE_FAILED", "批量更新RSS源状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> checkFeedUrlExists(String feedUrl, Long excludeId) {
        log.info("调用checkFeedUrlExists方法 - 参数: feedUrl={}, excludeId={}", feedUrl, excludeId);

        if (!StringUtils.hasText(feedUrl)) {
            log.warn("RSS订阅地址不能为空");
            return Result.errorResult("INVALID_PARAMETER", "RSS订阅地址不能为空");
        }

        try {
            RssSource existingRssSource = rssSourceMapper.selectByFeedUrl(feedUrl, excludeId);
            boolean exists = existingRssSource != null;

            log.info("RSS订阅地址存在性检查完成 - feedUrl: {}, exists: {}", feedUrl, exists);
            return Result.success("RSS订阅地址存在性检查完成", exists);

        } catch (Exception e) {
            log.error("RSS订阅地址存在性检查失败 - feedUrl: {}", feedUrl, e);
            return Result.errorResult("RSS_FEED_URL_CHECK_FAILED", "RSS订阅地址存在性检查失败: " + e.getMessage());
        }
    }

    @Override
    public Result<RssSourceStatistics> getRssSourceStatistics() {
        log.info("调用getRssSourceStatistics方法");

        try {
            // 获取总数量
            Long totalCount = rssSourceMapper.countTotal();

            // 获取按状态统计
            List<RssSourceMapper.StatusCount> statusCounts = rssSourceMapper.countByStatus();
            Long activeCount = 0L;
            Long pausedCount = 0L;
            Long failedCount = 0L;

            for (RssSourceMapper.StatusCount statusCount : statusCounts) {
                switch (statusCount.getStatus()) {
                    case 0: // ACTIVE
                        activeCount = statusCount.getCount();
                        break;
                    case 1: // PAUSED
                        pausedCount = statusCount.getCount();
                        break;
                    case 2: // FAILED
                        failedCount = statusCount.getCount();
                        break;
                }
            }

            // 获取按类型统计
            List<RssSourceMapper.TypeCount> typeCounts = rssSourceMapper.countByType();
            Long officialCount = 0L;
            Long userSubscribedCount = 0L;

            for (RssSourceMapper.TypeCount typeCount : typeCounts) {
                switch (typeCount.getType()) {
                    case 0: // OFFICIAL
                        officialCount = typeCount.getCount();
                        break;
                    case 1: // USER_SUBSCRIBED
                        userSubscribedCount = typeCount.getCount();
                        break;
                }
            }

            RssSourceStatistics statistics = new RssSourceStatistics(
                totalCount, activeCount, pausedCount, failedCount, officialCount, userSubscribedCount);

            log.info("RSS源统计信息查询成功 - statistics: {}", statistics);
            return Result.success("RSS源统计信息查询成功", statistics);

        } catch (Exception e) {
            log.error("RSS源统计信息查询失败", e);
            return Result.errorResult("RSS_SOURCE_STATISTICS_QUERY_FAILED", "RSS源统计信息查询失败: " + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 将RssSource实体转换为RssSourceDTO
     *
     * @param rssSource RSS源实体
     * @return RSS源DTO
     */
    private RssSourceDTO convertToRssSourceDTO(RssSource rssSource) {
        if (rssSource == null) {
            return null;
        }

        RssSourceDTO dto = new RssSourceDTO();
        BeanUtils.copyProperties(rssSource, dto);
        return dto;
    }

    /**
     * 将RssSourceDTO转换为RssSource实体
     *
     * @param rssSourceDTO RSS源DTO
     * @return RSS源实体
     */
    private RssSource convertToRssSourceEntity(RssSourceDTO rssSourceDTO) {
        if (rssSourceDTO == null) {
            return null;
        }

        RssSource entity = new RssSource();
        BeanUtils.copyProperties(rssSourceDTO, entity);
        return entity;
    }
}
