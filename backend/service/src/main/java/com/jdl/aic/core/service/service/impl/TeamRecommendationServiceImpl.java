package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.recommendation.TeamRecommendationDTO;
import com.jdl.aic.core.service.client.service.TeamRecommendationService;
import com.jdl.aic.core.service.dao.entity.portal.TeamRecommendation;
import com.jdl.aic.core.service.dao.mapper.portal.TeamRecommendationMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 团队推荐内容服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("teamRecommendationService")
public class TeamRecommendationServiceImpl implements TeamRecommendationService {

    @Resource
    private TeamRecommendationMapper teamRecommendationMapper;

    // 状态常量
    private static final String STATUS_ACTIVE = "active";
    @Override
    public Result<TeamRecommendationDTO> getTeamRecommendationById(Long id) {
        log.info("开始根据ID获取团队推荐详情 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 推荐ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐ID不能为空");
            }

            TeamRecommendation recommendation = teamRecommendationMapper.selectById(id);
            if (recommendation == null) {
                log.warn("团队推荐不存在 - id: {}", id);
                return Result.errorResult("TEAM_RECOMMENDATION_NOT_FOUND", "团队推荐不存在");
            }

            log.info("查询团队推荐成功 - id: {}, teamId: {}, contentType: {}",
                    id, recommendation.getTeam_id(), recommendation.getContent_type());
            TeamRecommendationDTO recommendationDTO = convertToTeamRecommendationDTO(recommendation);
            return Result.success(recommendationDTO);

        } catch (Exception e) {
            log.error("根据ID获取团队推荐详情失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取团队推荐详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<TeamRecommendationDTO>> getTeamRecommendationList(
            PageRequest pageRequest,
            Long teamId,
            Long userId,
            String contentType,
            String status,
            String search) {
        log.info("开始获取团队推荐列表 - 入参: pageRequest={}, teamId={}, userId={}, contentType={}, status={}, search={}",
                pageRequest, teamId, userId, contentType, status, search);
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            Map<String, Object> params = new HashMap<>();
            if (teamId != null) {
                params.put("teamId", teamId);
            }
            if (userId != null) {
                params.put("userId", userId);
            }
            if (StringUtils.hasText(contentType)) {
                params.put("contentType", contentType);
            }
            if (StringUtils.hasText(status)) {
                params.put("status", status);
            }
            if (StringUtils.hasText(search)) {
                params.put("search", search);
            }
            log.debug("构建查询条件完成: teamId={}, userId={}, contentType={}, status={}",
                    teamId, userId, contentType, status);

            // 查询团队推荐列表
            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByCondition(
                    teamId, userId, contentType, status);
            log.info("数据库查询完成，查询到 {} 条团队推荐记录", recommendations.size());

            // 使用PageInfo获取分页信息
            PageInfo<TeamRecommendation> pageInfo = new PageInfo<>(recommendations);

            // 转换为DTO
            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            PageResult<TeamRecommendationDTO> pageResult = PageResult.of(
                    recommendationDTOs, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());
            log.info("获取团队推荐列表成功 - 返回 {} 条记录，总数: {}", recommendationDTOs.size(), pageInfo.getTotal());

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取团队推荐列表失败 - 入参: teamId={}, userId={}, contentType={}, status={}, search={}, 错误信息: {}",
                    teamId, userId, contentType, status, search, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_LIST_QUERY_ERROR", "获取团队推荐列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<TeamRecommendationDTO> createTeamRecommendation(TeamRecommendationDTO teamRecommendation) {
        log.info("开始创建团队推荐 - 入参: teamId={}, userId={}, contentId={}, contentType={}",
                teamRecommendation != null ? teamRecommendation.getTeamId() : null,
                teamRecommendation != null ? teamRecommendation.getUserId() : null,
                teamRecommendation != null ? teamRecommendation.getContentId() : null,
                teamRecommendation != null ? teamRecommendation.getContentType() : null);
        try {
            if (teamRecommendation == null) {
                log.warn("参数校验失败 - 团队推荐信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队推荐信息不能为空");
            }

            // 验证必填字段
            if (teamRecommendation.getTeamId() == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }
            if (teamRecommendation.getContentId() == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }
            if (!StringUtils.hasText(teamRecommendation.getContentType())) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            // 检查推荐记录是否已存在
            boolean exists = teamRecommendationMapper.existsByTeamIdAndContentIdAndType(
                    teamRecommendation.getTeamId(), teamRecommendation.getContentId(), teamRecommendation.getContentType());
            if (exists) {
                log.warn("相同推荐记录已存在 - teamId: {}, contentId: {}, contentType: {}",
                        teamRecommendation.getTeamId(), teamRecommendation.getContentId(), teamRecommendation.getContentType());
                return Result.errorResult("TEAM_RECOMMENDATION_EXISTS", "相同推荐记录已存在");
            }
            log.debug("推荐记录唯一性校验通过 - teamId: {}, contentId: {}, contentType: {}",
                    teamRecommendation.getTeamId(), teamRecommendation.getContentId(), teamRecommendation.getContentType());

            // 转换为实体对象
            TeamRecommendation recommendationEntity = convertToTeamRecommendation(teamRecommendation);
            recommendationEntity.setCreated_at(LocalDateTime.now());
            recommendationEntity.setUpdated_at(LocalDateTime.now());

            // 设置默认值
            if (!StringUtils.hasText(recommendationEntity.getStatus())) {
                recommendationEntity.setStatus(STATUS_ACTIVE);
            }
            if (recommendationEntity.getDeleted() == null) {
                recommendationEntity.setDeleted(0);
            }
            log.debug("实体转换和默认值设置完成 - teamId: {}, contentId: {}, contentType: {}, status: {}",
                    recommendationEntity.getTeam_id(), recommendationEntity.getContent_id(),
                    recommendationEntity.getContent_type(), recommendationEntity.getStatus());

            // 插入数据库
            int result = teamRecommendationMapper.insert(recommendationEntity);
            if (result <= 0) {
                log.error("团队推荐创建失败 - 数据库插入返回结果: {}, teamId: {}", result, recommendationEntity.getTeam_id());
                return Result.errorResult("TEAM_RECOMMENDATION_CREATE_FAILED", "创建团队推荐失败");
            }

            log.info("团队推荐创建成功 - id: {}, teamId: {}, contentId: {}, contentType: {}",
                    recommendationEntity.getId(), recommendationEntity.getTeam_id(),
                    recommendationEntity.getContent_id(), recommendationEntity.getContent_type());
            TeamRecommendationDTO resultDTO = convertToTeamRecommendationDTO(recommendationEntity);
            return Result.success(resultDTO);

        } catch (Exception e) {
            log.error("创建团队推荐失败 - 入参: teamId={}, userId={}, contentId={}, contentType={}, 错误信息: {}",
                    teamRecommendation != null ? teamRecommendation.getTeamId() : null,
                    teamRecommendation != null ? teamRecommendation.getUserId() : null,
                    teamRecommendation != null ? teamRecommendation.getContentId() : null,
                    teamRecommendation != null ? teamRecommendation.getContentType() : null,
                    e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_CREATE_ERROR", "创建团队推荐失败: " + e.getMessage());
        }
    }
    private static final String STATUS_INACTIVE = "inactive";
    private static final String STATUS_DELETED = "deleted";
    @Override
    @Transactional
    public Result<TeamRecommendationDTO> recommendContentToTeam(Long teamId, Long userId, Long contentId, String contentType, String reason) {
        log.info("开始推荐内容到团队 - 入参: teamId={}, userId={}, contentId={}, contentType={}, reason={}",
                teamId, userId, contentId, contentType, reason);
        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }
            if (contentId == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }
            if (!StringUtils.hasText(contentType)) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            // 检查推荐记录是否已存在
            boolean exists = teamRecommendationMapper.existsByTeamIdAndContentIdAndType(teamId, contentId, contentType);
            if (exists) {
                log.warn("相同推荐记录已存在 - teamId: {}, contentId: {}, contentType: {}", teamId, contentId, contentType);
                return Result.errorResult("TEAM_RECOMMENDATION_EXISTS", "相同推荐记录已存在");
            }
            log.debug("推荐记录唯一性校验通过 - teamId: {}, contentId: {}, contentType: {}", teamId, contentId, contentType);

            // 创建推荐实体
            TeamRecommendation recommendation = new TeamRecommendation();
            recommendation.setTeam_id(teamId);
            recommendation.setUser_id(userId);
            recommendation.setContent_id(contentId);
            recommendation.setContent_type(contentType);
            recommendation.setReason(reason);
            recommendation.setStatus(STATUS_ACTIVE);
            recommendation.setDeleted(0);
            recommendation.setCreated_at(LocalDateTime.now());
            recommendation.setUpdated_at(LocalDateTime.now());

            // 插入数据库
            int result = teamRecommendationMapper.insert(recommendation);
            if (result <= 0) {
                log.error("推荐内容到团队失败 - 数据库插入返回结果: {}, teamId: {}", result, teamId);
                return Result.errorResult("TEAM_RECOMMENDATION_CREATE_FAILED", "推荐内容到团队失败");
            }

            log.info("推荐内容到团队成功 - id: {}, teamId: {}, contentId: {}, contentType: {}",
                    recommendation.getId(), recommendation.getTeam_id(),
                    recommendation.getContent_id(), recommendation.getContent_type());
            TeamRecommendationDTO resultDTO = convertToTeamRecommendationDTO(recommendation);
            return Result.success(resultDTO);

        } catch (Exception e) {
            log.error("推荐内容到团队失败 - 入参: teamId={}, userId={}, contentId={}, contentType={}, 错误信息: {}",
                    teamId, userId, contentId, contentType, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_CREATE_ERROR", "推荐内容到团队失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<TeamRecommendationDTO> updateTeamRecommendation(Long id, TeamRecommendationDTO teamRecommendation) {
        log.info("开始更新团队推荐 - 入参: id={}, teamId={}, contentId={}, contentType={}",
                id,
                teamRecommendation != null ? teamRecommendation.getTeamId() : null,
                teamRecommendation != null ? teamRecommendation.getContentId() : null,
                teamRecommendation != null ? teamRecommendation.getContentType() : null);
        try {
            if (id == null || teamRecommendation == null) {
                log.warn("参数校验失败 - 推荐ID和信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐ID和信息不能为空");
            }

            // 检查团队推荐是否存在
            TeamRecommendation existingRecommendation = teamRecommendationMapper.selectById(id);
            if (existingRecommendation == null) {
                log.warn("团队推荐不存在 - id: {}", id);
                return Result.errorResult("TEAM_RECOMMENDATION_NOT_FOUND", "团队推荐不存在");
            }
            log.debug("查询到待更新的团队推荐 - id: {}, teamId: {}, contentType: {}",
                    id, existingRecommendation.getTeam_id(), existingRecommendation.getContent_type());

            // 如果修改了团队ID、内容ID或内容类型，检查新的值是否已存在
            if ((teamRecommendation.getTeamId() != null && !teamRecommendation.getTeamId().equals(existingRecommendation.getTeam_id())) ||
                    (teamRecommendation.getContentId() != null && !teamRecommendation.getContentId().equals(existingRecommendation.getContent_id())) ||
                    (StringUtils.hasText(teamRecommendation.getContentType()) && !teamRecommendation.getContentType().equals(existingRecommendation.getContent_type()))) {

                Long newTeamId = teamRecommendation.getTeamId() != null ? teamRecommendation.getTeamId() : existingRecommendation.getTeam_id();
                Long newContentId = teamRecommendation.getContentId() != null ? teamRecommendation.getContentId() : existingRecommendation.getContent_id();
                String newContentType = StringUtils.hasText(teamRecommendation.getContentType()) ? teamRecommendation.getContentType() : existingRecommendation.getContent_type();

                boolean exists = teamRecommendationMapper.existsByTeamIdAndContentIdAndType(newTeamId, newContentId, newContentType);
                if (exists) {
                    log.warn("相同推荐记录已存在 - teamId: {}, contentId: {}, contentType: {}", newTeamId, newContentId, newContentType);
                    return Result.errorResult("TEAM_RECOMMENDATION_EXISTS", "相同推荐记录已存在");
                }
                log.debug("推荐记录唯一性校验通过 - teamId: {}, contentId: {}, contentType: {}", newTeamId, newContentId, newContentType);
            }

            // 更新团队推荐信息
            TeamRecommendation recommendationEntity = convertToTeamRecommendation(teamRecommendation);
            recommendationEntity.setId(id);
            recommendationEntity.setUpdated_at(LocalDateTime.now());

            int result = teamRecommendationMapper.updateById(recommendationEntity);
            if (result <= 0) {
                log.error("团队推荐更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("TEAM_RECOMMENDATION_UPDATE_FAILED", "更新团队推荐失败");
            }

            // 查询更新后的团队推荐
            TeamRecommendation updatedRecommendation = teamRecommendationMapper.selectById(id);
            log.info("团队推荐更新成功 - id: {}, teamId: {}, contentId: {}, contentType: {}",
                    updatedRecommendation.getId(), updatedRecommendation.getTeam_id(),
                    updatedRecommendation.getContent_id(), updatedRecommendation.getContent_type());
            TeamRecommendationDTO resultDTO = convertToTeamRecommendationDTO(updatedRecommendation);
            return Result.success(resultDTO);

        } catch (Exception e) {
            log.error("更新团队推荐失败 - 入参: id={}, teamId={}, contentId={}, contentType={}, 错误信息: {}",
                    id,
                    teamRecommendation != null ? teamRecommendation.getTeamId() : null,
                    teamRecommendation != null ? teamRecommendation.getContentId() : null,
                    teamRecommendation != null ? teamRecommendation.getContentType() : null,
                    e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_UPDATE_ERROR", "更新团队推荐失败: " + e.getMessage());
        }
    }
    @Override
    @Transactional
    public Result<Void> deleteTeamRecommendation(Long id) {
        log.info("开始删除团队推荐 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 推荐ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐ID不能为空");
            }

            // 检查团队推荐是否存在
            TeamRecommendation existingRecommendation = teamRecommendationMapper.selectById(id);
            if (existingRecommendation == null) {
                log.warn("团队推荐不存在 - id: {}", id);
                return Result.errorResult("TEAM_RECOMMENDATION_NOT_FOUND", "团队推荐不存在");
            }
            log.debug("查询到待删除的团队推荐 - id: {}, teamId: {}, contentType: {}",
                    id, existingRecommendation.getTeam_id(), existingRecommendation.getContent_type());

            int result = teamRecommendationMapper.deleteById(id);
            if (result <= 0) {
                log.error("团队推荐删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("TEAM_RECOMMENDATION_DELETE_FAILED", "删除团队推荐失败");
            }

            log.info("团队推荐删除成功 - id: {}, teamId: {}, contentType: {}",
                    id, existingRecommendation.getTeam_id(), existingRecommendation.getContent_type());
            return Result.success();

        } catch (Exception e) {
            log.error("删除团队推荐失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_DELETE_ERROR", "删除团队推荐失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> softDeleteTeamRecommendation(Long id) {
        log.info("开始软删除团队推荐 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 推荐ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐ID不能为空");
            }

            // 检查团队推荐是否存在
            TeamRecommendation existingRecommendation = teamRecommendationMapper.selectById(id);
            if (existingRecommendation == null) {
                log.warn("团队推荐不存在 - id: {}", id);
                return Result.errorResult("TEAM_RECOMMENDATION_NOT_FOUND", "团队推荐不存在");
            }
            log.debug("查询到待软删除的团队推荐 - id: {}, teamId: {}, contentType: {}",
                    id, existingRecommendation.getTeam_id(), existingRecommendation.getContent_type());

            int result = teamRecommendationMapper.softDeleteById(id);
            if (result <= 0) {
                log.error("团队推荐软删除失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("TEAM_RECOMMENDATION_SOFT_DELETE_FAILED", "软删除团队推荐失败");
            }

            log.info("团队推荐软删除成功 - id: {}, teamId: {}, contentType: {}",
                    id, existingRecommendation.getTeam_id(), existingRecommendation.getContent_type());
            return Result.success();

        } catch (Exception e) {
            log.error("软删除团队推荐失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_SOFT_DELETE_ERROR", "软删除团队推荐失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamRecommendationDTO>> getTeamRecommendationsByTeamId(Long teamId, Integer limit) {
        log.info("开始根据团队ID获取推荐内容列表 - 入参: teamId={}, limit={}", teamId, limit);
        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByTeamId(teamId);
            log.info("根据团队ID查询完成，查询到 {} 条记录", recommendations.size());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && recommendations.size() > limit) {
                recommendations = recommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("根据团队ID获取推荐内容列表成功 - teamId: {}, 返回 {} 条记录", teamId, recommendationDTOs.size());
            return Result.success(recommendationDTOs);

        } catch (Exception e) {
            log.error("根据团队ID获取推荐内容列表失败 - 入参: teamId={}, limit={}, 错误信息: {}",
                    teamId, limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取推荐内容列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamRecommendationDTO>> getTeamRecommendationsByUserId(Long userId, Integer limit) {
        log.info("开始根据用户ID获取推荐记录列表 - 入参: userId={}, limit={}", userId, limit);
        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByUserId(userId);
            log.info("根据用户ID查询完成，查询到 {} 条记录", recommendations.size());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && recommendations.size() > limit) {
                recommendations = recommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("根据用户ID获取推荐记录列表成功 - userId: {}, 返回 {} 条记录", userId, recommendationDTOs.size());
            return Result.success(recommendationDTOs);

        } catch (Exception e) {
            log.error("根据用户ID获取推荐记录列表失败 - 入参: userId={}, limit={}, 错误信息: {}",
                    userId, limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取推荐记录列表失败: " + e.getMessage());
        }
    }
    @Override
    public Result<List<TeamRecommendationDTO>> getTeamRecommendationsByContentIdAndType(Long contentId, String contentType, Integer limit) {
        log.info("开始根据内容ID和类型获取推荐记录列表 - 入参: contentId={}, contentType={}, limit={}",
                contentId, contentType, limit);
        try {
            if (contentId == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }
            if (!StringUtils.hasText(contentType)) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByContentIdAndType(contentId, contentType);
            log.info("根据内容ID和类型查询完成，查询到 {} 条记录", recommendations.size());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && recommendations.size() > limit) {
                recommendations = recommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("根据内容ID和类型获取推荐记录列表成功 - contentId: {}, contentType: {}, 返回 {} 条记录",
                    contentId, contentType, recommendationDTOs.size());
            return Result.success(recommendationDTOs);

        } catch (Exception e) {
            log.error("根据内容ID和类型获取推荐记录列表失败 - 入参: contentId={}, contentType={}, limit={}, 错误信息: {}",
                    contentId, contentType, limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取推荐记录列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamRecommendationDTO>> getTeamRecommendationsByTeamIdAndContentType(Long teamId, String contentType, Integer limit) {
        log.info("开始根据团队ID和内容类型获取推荐列表 - 入参: teamId={}, contentType={}, limit={}",
                teamId, contentType, limit);
        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }
            if (!StringUtils.hasText(contentType)) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByTeamIdAndContentType(teamId, contentType);
            log.info("根据团队ID和内容类型查询完成，查询到 {} 条记录", recommendations.size());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && recommendations.size() > limit) {
                recommendations = recommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("根据团队ID和内容类型获取推荐列表成功 - teamId: {}, contentType: {}, 返回 {} 条记录",
                    teamId, contentType, recommendationDTOs.size());
            return Result.success(recommendationDTOs);

        } catch (Exception e) {
            log.error("根据团队ID和内容类型获取推荐列表失败 - 入参: teamId={}, contentType={}, limit={}, 错误信息: {}",
                    teamId, contentType, limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取推荐列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamRecommendationDTO>> getTeamRecommendationsByStatus(String status, Integer limit) {
        log.info("开始根据状态获取推荐记录列表 - 入参: status={}, limit={}", status, limit);
        try {
            if (!StringUtils.hasText(status)) {
                log.warn("参数校验失败 - 推荐状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐状态不能为空");
            }

            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByStatus(status);
            log.info("根据状态查询完成，查询到 {} 条记录", recommendations.size());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && recommendations.size() > limit) {
                recommendations = recommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("根据状态获取推荐记录列表成功 - status: {}, 返回 {} 条记录", status, recommendationDTOs.size());
            return Result.success(recommendationDTOs);

        } catch (Exception e) {
            log.error("根据状态获取推荐记录列表失败 - 入参: status={}, limit={}, 错误信息: {}",
                    status, limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取推荐记录列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamRecommendationDTO>> getAllTeamRecommendations(Integer limit) {
        log.info("开始获取所有推荐记录列表 - 入参: limit={}", limit);
        try {
            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectAll();
            log.info("查询所有推荐记录完成，查询到 {} 条记录", recommendations.size());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && recommendations.size() > limit) {
                recommendations = recommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("获取所有推荐记录列表成功 - 返回 {} 条记录", recommendationDTOs.size());
            return Result.success(recommendationDTOs);

        } catch (Exception e) {
            log.error("获取所有推荐记录列表失败 - 入参: limit={}, 错误信息: {}", limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取推荐记录列表失败: " + e.getMessage());
        }
    }
    @Override
    public Result<List<TeamRecommendationDTO>> getActiveTeamRecommendations(Integer limit) {
        log.info("开始获取活跃的推荐记录列表 - 入参: limit={}", limit);
        try {
            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectActive();
            log.info("查询活跃推荐记录完成，查询到 {} 条记录", recommendations.size());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && recommendations.size() > limit) {
                recommendations = recommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("获取活跃推荐记录列表成功 - 返回 {} 条记录", recommendationDTOs.size());
            return Result.success(recommendationDTOs);

        } catch (Exception e) {
            log.error("获取活跃推荐记录列表失败 - 入参: limit={}, 错误信息: {}", limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取活跃推荐记录列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamRecommendationDTO>> getTeamRecommendationsByCondition(Long teamId, Long userId, String contentType, String status, Integer limit) {
        log.info("开始根据条件查询推荐记录列表 - 入参: teamId={}, userId={}, contentType={}, status={}, limit={}",
                teamId, userId, contentType, status, limit);
        try {
            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByCondition(teamId, userId, contentType, status);
            log.info("根据条件查询完成，查询到 {} 条记录", recommendations.size());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && recommendations.size() > limit) {
                recommendations = recommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = recommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("根据条件查询推荐记录列表成功 - teamId: {}, userId: {}, contentType: {}, status: {}, 返回 {} 条记录",
                    teamId, userId, contentType, status, recommendationDTOs.size());
            return Result.success(recommendationDTOs);

        } catch (Exception e) {
            log.error("根据条件查询推荐记录列表失败 - 入参: teamId={}, userId={}, contentType={}, status={}, limit={}, 错误信息: {}",
                    teamId, userId, contentType, status, limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "根据条件查询推荐记录列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateRecommendationStatus(Long id, String status) {
        log.info("开始更新推荐状态 - 入参: id={}, status={}", id, status);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 推荐ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐ID不能为空");
            }
            if (!StringUtils.hasText(status)) {
                log.warn("参数校验失败 - 推荐状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐状态不能为空");
            }

            // 检查团队推荐是否存在
            TeamRecommendation existingRecommendation = teamRecommendationMapper.selectById(id);
            if (existingRecommendation == null) {
                log.warn("团队推荐不存在 - id: {}", id);
                return Result.errorResult("TEAM_RECOMMENDATION_NOT_FOUND", "团队推荐不存在");
            }
            log.debug("查询到待更新状态的团队推荐 - id: {}, teamId: {}, contentType: {}, 当前状态: {}",
                    id, existingRecommendation.getTeam_id(), existingRecommendation.getContent_type(), existingRecommendation.getStatus());

            int result = teamRecommendationMapper.updateStatus(id, status);
            if (result <= 0) {
                log.error("推荐状态更新失败 - 数据库更新返回结果: {}, id: {}, status: {}", result, id, status);
                return Result.errorResult("TEAM_RECOMMENDATION_STATUS_UPDATE_FAILED", "更新推荐状态失败");
            }

            log.info("推荐状态更新成功 - id: {}, teamId: {}, contentType: {}, 新状态: {}",
                    id, existingRecommendation.getTeam_id(), existingRecommendation.getContent_type(), status);
            return Result.success();

        } catch (Exception e) {
            log.error("更新推荐状态失败 - 入参: id={}, status={}, 错误信息: {}", id, status, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_STATUS_UPDATE_ERROR", "更新推荐状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> activateRecommendation(Long id) {
        log.info("开始激活推荐 - 入参: id={}", id);
        try {
            return updateRecommendationStatus(id, STATUS_ACTIVE);
        } catch (Exception e) {
            log.error("激活推荐失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_ACTIVATE_ERROR", "激活推荐失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deactivateRecommendation(Long id) {
        log.info("开始停用推荐 - 入参: id={}", id);
        try {
            return updateRecommendationStatus(id, STATUS_INACTIVE);
        } catch (Exception e) {
            log.error("停用推荐失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_DEACTIVATE_ERROR", "停用推荐失败: " + e.getMessage());
        }
    }
    @Override
    @Transactional
    public Result<List<TeamRecommendationDTO>> batchCreateTeamRecommendations(List<TeamRecommendationDTO> teamRecommendations) {
        log.info("开始批量创建团队推荐 - 入参: 推荐数量={}", teamRecommendations != null ? teamRecommendations.size() : 0);
        try {
            if (teamRecommendations == null || teamRecommendations.isEmpty()) {
                log.warn("参数校验失败 - 团队推荐列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队推荐列表不能为空");
            }

            List<TeamRecommendation> recommendationEntities = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (TeamRecommendationDTO dto : teamRecommendations) {
                // 验证必填字段
                if (dto.getTeamId() == null || dto.getContentId() == null || !StringUtils.hasText(dto.getContentType())) {
                    log.warn("参数校验失败 - 团队ID、内容ID和内容类型不能为空");
                    return Result.errorResult("INVALID_PARAMETER", "团队ID、内容ID和内容类型不能为空");
                }

                // 检查推荐记录是否已存在
                boolean exists = teamRecommendationMapper.existsByTeamIdAndContentIdAndType(
                        dto.getTeamId(), dto.getContentId(), dto.getContentType());
                if (exists) {
                    log.warn("相同推荐记录已存在 - teamId: {}, contentId: {}, contentType: {}",
                            dto.getTeamId(), dto.getContentId(), dto.getContentType());
                    return Result.errorResult("TEAM_RECOMMENDATION_EXISTS",
                            "团队ID " + dto.getTeamId() + "、内容ID " + dto.getContentId() +
                                    "、内容类型 " + dto.getContentType() + " 的推荐记录已存在");
                }

                TeamRecommendation entity = convertToTeamRecommendation(dto);
                entity.setCreated_at(now);
                entity.setUpdated_at(now);
                if (!StringUtils.hasText(entity.getStatus())) {
                    entity.setStatus(STATUS_ACTIVE);
                }
                if (entity.getDeleted() == null) {
                    entity.setDeleted(0);
                }
                recommendationEntities.add(entity);
            }

            int result = teamRecommendationMapper.batchInsert(recommendationEntities);
            if (result <= 0) {
                log.error("批量创建团队推荐失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("TEAM_RECOMMENDATION_BATCH_CREATE_FAILED", "批量创建团队推荐失败");
            }

            List<TeamRecommendationDTO> resultDTOs = recommendationEntities.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("批量创建团队推荐成功 - 创建数量: {}", resultDTOs.size());
            return Result.success(resultDTOs);

        } catch (Exception e) {
            log.error("批量创建团队推荐失败 - 错误信息: {}", e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_BATCH_CREATE_ERROR", "批量创建团队推荐失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchUpdateRecommendationStatus(List<Long> ids, String status) {
        log.info("开始批量更新推荐状态 - 入参: ids数量={}, status={}", ids != null ? ids.size() : 0, status);
        try {
            if (ids == null || ids.isEmpty()) {
                log.warn("参数校验失败 - 推荐ID列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐ID列表不能为空");
            }
            if (!StringUtils.hasText(status)) {
                log.warn("参数校验失败 - 推荐状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐状态不能为空");
            }

            int result = teamRecommendationMapper.batchUpdateStatus(ids, status);
            if (result <= 0) {
                log.error("批量更新推荐状态失败 - 数据库更新返回结果: {}, ids数量: {}, status: {}", result, ids.size(), status);
                return Result.errorResult("TEAM_RECOMMENDATION_BATCH_STATUS_UPDATE_FAILED", "批量更新推荐状态失败");
            }

            log.info("批量更新推荐状态成功 - ids数量: {}, status: {}, 更新记录数: {}", ids.size(), status, result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新推荐状态失败 - 入参: ids数量={}, status={}, 错误信息: {}", ids != null ? ids.size() : 0, status, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_BATCH_STATUS_UPDATE_ERROR", "批量更新推荐状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchSoftDeleteTeamRecommendations(List<Long> ids) {
        log.info("开始批量软删除推荐记录 - 入参: ids数量={}", ids != null ? ids.size() : 0);
        try {
            if (ids == null || ids.isEmpty()) {
                log.warn("参数校验失败 - 推荐ID列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "推荐ID列表不能为空");
            }

            int result = teamRecommendationMapper.batchSoftDelete(ids);
            if (result <= 0) {
                log.error("批量软删除推荐记录失败 - 数据库更新返回结果: {}, ids数量: {}", result, ids.size());
                return Result.errorResult("TEAM_RECOMMENDATION_BATCH_SOFT_DELETE_FAILED", "批量软删除推荐记录失败");
            }

            log.info("批量软删除推荐记录成功 - ids数量: {}, 删除记录数: {}", ids.size(), result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量软删除推荐记录失败 - 入参: ids数量={}, 错误信息: {}", ids != null ? ids.size() : 0, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_BATCH_SOFT_DELETE_ERROR", "批量软删除推荐记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> isRecommendationExists(Long teamId, Long contentId, String contentType) {
        log.info("开始检查推荐记录是否存在 - 入参: teamId={}, contentId={}, contentType={}", teamId, contentId, contentType);
        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }
            if (contentId == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }
            if (!StringUtils.hasText(contentType)) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            boolean exists = teamRecommendationMapper.existsByTeamIdAndContentIdAndType(teamId, contentId, contentType);
            log.info("检查推荐记录是否存在完成 - teamId: {}, contentId: {}, contentType: {}, 结果: {}",
                    teamId, contentId, contentType, exists);
            return Result.success(exists);

        } catch (Exception e) {
            log.error("检查推荐记录是否存在失败 - 入参: teamId={}, contentId={}, contentType={}, 错误信息: {}",
                    teamId, contentId, contentType, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_CHECK_ERROR", "检查推荐记录是否存在失败: " + e.getMessage());
        }
    }
    @Override
    public Result<Integer> getTeamRecommendationCount() {
        log.info("开始统计推荐记录数量");
        try {
            int count = teamRecommendationMapper.count();
            log.info("统计推荐记录数量完成 - 总数: {}", count);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计推荐记录数量失败 - 错误信息: {}", e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_COUNT_ERROR", "统计推荐记录数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getActiveTeamRecommendationCount() {
        log.info("开始统计活跃推荐记录数量");
        try {
            int count = teamRecommendationMapper.countActive();
            log.info("统计活跃推荐记录数量完成 - 总数: {}", count);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计活跃推荐记录数量失败 - 错误信息: {}", e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_COUNT_ERROR", "统计活跃推荐记录数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getTeamRecommendationCountByTeamId(Long teamId) {
        log.info("开始统计团队的推荐数量 - 入参: teamId={}", teamId);
        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            int count = teamRecommendationMapper.countByTeamId(teamId);
            log.info("统计团队的推荐数量完成 - teamId: {}, 总数: {}", teamId, count);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计团队的推荐数量失败 - 入参: teamId={}, 错误信息: {}", teamId, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_COUNT_ERROR", "统计团队的推荐数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getTeamRecommendationCountByUserId(Long userId) {
        log.info("开始统计用户的推荐数量 - 入参: userId={}", userId);
        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            int count = teamRecommendationMapper.countByUserId(userId);
            log.info("统计用户的推荐数量完成 - userId: {}, 总数: {}", userId, count);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计用户的推荐数量失败 - 入参: userId={}, 错误信息: {}", userId, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_COUNT_ERROR", "统计用户的推荐数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getTeamRecommendationCountByContentIdAndType(Long contentId, String contentType) {
        log.info("开始统计内容的推荐数量 - 入参: contentId={}, contentType={}", contentId, contentType);
        try {
            if (contentId == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }
            if (!StringUtils.hasText(contentType)) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            int count = teamRecommendationMapper.countByContentIdAndType(contentId, contentType);
            log.info("统计内容的推荐数量完成 - contentId: {}, contentType: {}, 总数: {}", contentId, contentType, count);
            return Result.success(count);
        } catch (Exception e) {
            log.error("统计内容的推荐数量失败 - 入参: contentId={}, contentType={}, 错误信息: {}",
                    contentId, contentType, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_COUNT_ERROR", "统计内容的推荐数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getTeamRecommendationStatistics(Long teamId) {
        log.info("开始获取团队推荐统计信息 - 入参: teamId={}", teamId);
        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            List<Object> statistics = teamRecommendationMapper.getTeamRecommendationStatistics(teamId);
            if (statistics == null || statistics.isEmpty()) {
                log.info("团队推荐统计信息为空 - teamId: {}", teamId);
                return Result.success(new HashMap<>());
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> result = (Map<String, Object>) statistics.get(0);
            log.info("获取团队推荐统计信息成功 - teamId: {}", teamId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取团队推荐统计信息失败 - 入参: teamId={}, 错误信息: {}", teamId, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_STATISTICS_ERROR", "获取团队推荐统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<String>> getPopularContentTypes() {
        log.info("开始获取热门推荐内容类型");
        try {
            List<TeamRecommendation> allRecommendations = teamRecommendationMapper.selectActive();
            log.info("查询活跃推荐记录完成，查询到 {} 条记录", allRecommendations.size());

            List<String> popularContentTypes = allRecommendations.stream()
                    .collect(Collectors.groupingBy(TeamRecommendation::getContent_type, Collectors.counting()))
                    .entrySet().stream()
                    .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            log.info("获取热门推荐内容类型成功 - 返回 {} 种类型", popularContentTypes.size());
            return Result.success(popularContentTypes);
        } catch (Exception e) {
            log.error("获取热门推荐内容类型失败 - 错误信息: {}", e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取热门推荐内容类型失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamRecommendationDTO>> getUserRecentRecommendations(Long userId, Integer limit) {
        log.info("开始获取用户最近的推荐记录 - 入参: userId={}, limit={}", userId, limit);
        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByUserId(userId);
            log.info("查询用户推荐记录完成，查询到 {} 条记录", recommendations.size());

            // 按创建时间降序排序
            List<TeamRecommendation> sortedRecommendations = recommendations.stream()
                    .sorted((r1, r2) -> r2.getCreated_at().compareTo(r1.getCreated_at()))
                    .collect(Collectors.toList());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && sortedRecommendations.size() > limit) {
                sortedRecommendations = sortedRecommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = sortedRecommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("获取用户最近的推荐记录成功 - userId: {}, 返回 {} 条记录", userId, recommendationDTOs.size());
            return Result.success(recommendationDTOs);
        } catch (Exception e) {
            log.error("获取用户最近的推荐记录失败 - 入参: userId={}, limit={}, 错误信息: {}",
                    userId, limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取用户最近的推荐记录失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamRecommendationDTO>> getTeamRecentRecommendations(Long teamId, Integer limit) {
        log.info("开始获取团队最近的推荐记录 - 入参: teamId={}, limit={}", teamId, limit);
        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            List<TeamRecommendation> recommendations = teamRecommendationMapper.selectByTeamId(teamId);
            log.info("查询团队推荐记录完成，查询到 {} 条记录", recommendations.size());

            // 按创建时间降序排序
            List<TeamRecommendation> sortedRecommendations = recommendations.stream()
                    .sorted((r1, r2) -> r2.getCreated_at().compareTo(r1.getCreated_at()))
                    .collect(Collectors.toList());

            // 如果有限制数量，进行截取
            if (limit != null && limit > 0 && sortedRecommendations.size() > limit) {
                sortedRecommendations = sortedRecommendations.subList(0, limit);
            }

            List<TeamRecommendationDTO> recommendationDTOs = sortedRecommendations.stream()
                    .map(this::convertToTeamRecommendationDTO)
                    .collect(Collectors.toList());

            log.info("获取团队最近的推荐记录成功 - teamId: {}, 返回 {} 条记录", teamId, recommendationDTOs.size());
            return Result.success(recommendationDTOs);
        } catch (Exception e) {
            log.error("获取团队最近的推荐记录失败 - 入参: teamId={}, limit={}, 错误信息: {}",
                    teamId, limit, e.getMessage(), e);
            return Result.errorResult("TEAM_RECOMMENDATION_QUERY_ERROR", "获取团队最近的推荐记录失败: " + e.getMessage());
        }
    }
    

    // ==================== 私有方法 ====================

    /**
     * 将TeamRecommendation实体转换为TeamRecommendationDTO
     *
     * @param recommendation TeamRecommendation实体
     * @return TeamRecommendationDTO
     */
    private TeamRecommendationDTO convertToTeamRecommendationDTO(TeamRecommendation recommendation) {
        if (recommendation == null) {
            return null;
        }

        TeamRecommendationDTO dto = new TeamRecommendationDTO();
        dto.setId(recommendation.getId());
        dto.setTeamId(recommendation.getTeam_id());
        dto.setUserId(recommendation.getUser_id());
        dto.setContentId(recommendation.getContent_id());
        dto.setContentType(recommendation.getContent_type());
        dto.setReason(recommendation.getReason());
        dto.setStatus(recommendation.getStatus());
        dto.setDeleted(recommendation.getDeleted());
        dto.setCreatedAt(recommendation.getCreated_at());
        dto.setUpdatedAt(recommendation.getUpdated_at());

        return dto;
    }

    /**
     * 将TeamRecommendationDTO转换为TeamRecommendation实体
     *
     * @param dto TeamRecommendationDTO
     * @return TeamRecommendation实体
     */
    private TeamRecommendation convertToTeamRecommendation(TeamRecommendationDTO dto) {
        if (dto == null) {
            return null;
        }

        TeamRecommendation recommendation = new TeamRecommendation();
        recommendation.setId(dto.getId());
        recommendation.setTeam_id(dto.getTeamId());
        recommendation.setUser_id(dto.getUserId());
        recommendation.setContent_id(dto.getContentId());
        recommendation.setContent_type(dto.getContentType());
        recommendation.setReason(dto.getReason());
        recommendation.setStatus(dto.getStatus());
        recommendation.setDeleted(dto.getDeleted());
        recommendation.setCreated_at(dto.getCreatedAt());
        recommendation.setUpdated_at(dto.getUpdatedAt());

        return recommendation;
    }
}