package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.ShareOptionConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;
import com.jdl.aic.core.service.client.service.ShareOptionConfigService;
import com.jdl.aic.core.service.dao.entity.primary.ShareOptionConfig;
import com.jdl.aic.core.service.dao.mapper.primary.ShareOptionConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分享选项配置管理服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("shareOptionConfigService")
public class ShareOptionConfigServiceImpl implements ShareOptionConfigService {

    @Autowired
    private ShareOptionConfigMapper shareOptionConfigMapper;

    // 支持的分享类型
    private static final List<String> SUPPORTED_SHARE_TYPES = Arrays.asList(
            "internal", "wechat", "email", "link_copy", "teams", "slack"
    );

    @Override
    public Result<PageResult<ShareOptionConfigDTO>> getConfigList(
            PageRequest pageRequest, GetShareOptionConfigListRequest request) {
        log.info("开始获取分享选项配置列表 - 入参: pageRequest={}, request={}",
                pageRequest, request);
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            ShareOptionConfig queryCondition = new ShareOptionConfig();
            if (request != null) {
                if (StringUtils.hasText(request.getContentType())) {
                    queryCondition.setContentType(request.getContentType());
                }
                if (StringUtils.hasText(request.getShareType())) {
                    queryCondition.setShareType(request.getShareType());
                }
                if (request.getIsEnabled() != null) {
                    queryCondition.setIsEnabled(request.getIsEnabled());
                }
            }

            // 查询配置列表
            List<ShareOptionConfig> configs = shareOptionConfigMapper.selectByCondition(queryCondition);
            PageInfo<ShareOptionConfig> pageInfo = new PageInfo<>(configs);

            // 转换为DTO
            List<ShareOptionConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            PageResult<ShareOptionConfigDTO> pageResult = PageResult.of(configDTOs, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());

            log.info("获取分享选项配置列表成功 - 总数: {}, 当前页: {}", pageInfo.getTotal(), pageInfo.getPageNum());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取分享选项配置列表失败", e);
            return Result.errorResult("CONFIG_LIST_QUERY_ERROR", "获取分享选项配置列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ShareOptionConfigDTO> getConfigById(Long id) {
        log.info("开始根据ID获取分享选项配置 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 配置ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID不能为空");
            }

            ShareOptionConfig config = shareOptionConfigMapper.selectById(id);
            if (config == null) {
                log.warn("分享选项配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "分享选项配置不存在");
            }

            ShareOptionConfigDTO configDTO = convertToDTO(config);
            log.info("根据ID获取分享选项配置成功 - id: {}, contentType: {}, shareType: {}", 
                    id, config.getContentType(), config.getShareType());
            return Result.success(configDTO);

        } catch (Exception e) {
            log.error("根据ID获取分享选项配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_QUERY_ERROR", "获取分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ShareOptionConfigDTO> getConfigByContentAndShare(GetShareOptionConfigByContentAndShareRequest request) {
        log.info("开始根据内容类型和分享类型获取配置 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getContentType()) || !StringUtils.hasText(request.getShareType())) {
                log.warn("参数校验失败 - 请求参数、内容类型和分享类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、内容类型和分享类型不能为空");
            }

            ShareOptionConfig config = shareOptionConfigMapper.selectByContentAndShare(request.getContentType(), request.getShareType());
            if (config == null) {
                log.warn("分享选项配置不存在 - contentType: {}, shareType: {}", request.getContentType(), request.getShareType());
                return Result.errorResult("CONFIG_NOT_FOUND", "分享选项配置不存在");
            }

            ShareOptionConfigDTO configDTO = convertToDTO(config);
            log.info("根据内容类型和分享类型获取配置成功 - contentType: {}, shareType: {}, id: {}",
                    request.getContentType(), request.getShareType(), config.getId());
            return Result.success(configDTO);

        } catch (Exception e) {
            log.error("根据内容类型和分享类型获取配置失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_QUERY_ERROR", "获取分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ShareOptionConfigDTO> createConfig(ShareOptionConfigDTO config) {
        log.info("开始创建分享选项配置 - 入参: config={}", config);
        try {
            if (config == null) {
                log.warn("参数校验失败 - 配置信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置信息不能为空");
            }

            // 检查内容类型和分享类型组合是否已存在
            if (StringUtils.hasText(config.getContentType()) && StringUtils.hasText(config.getShareType())) {
                int count = shareOptionConfigMapper.countByContentAndShare(config.getContentType(), config.getShareType());
                if (count > 0) {
                    log.warn("分享选项配置已存在 - contentType: {}, shareType: {}", 
                            config.getContentType(), config.getShareType());
                    return Result.errorResult("CONFIG_ALREADY_EXISTS", "该内容类型和分享类型的配置已存在");
                }
            }

            // 转换为实体并设置默认值
            ShareOptionConfig entity = convertToEntity(config);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            if (entity.getIsEnabled() == null) {
                entity.setIsEnabled(true);
            }
            if (entity.getSortOrder() == null) {
                entity.setSortOrder(0);
            }

            // 插入数据库
            int result = shareOptionConfigMapper.insert(entity);
            if (result > 0) {
                log.info("分享选项配置创建成功 - id: {}, contentType: {}, shareType: {}", 
                        entity.getId(), entity.getContentType(), entity.getShareType());
                ShareOptionConfigDTO resultDto = convertToDTO(entity);
                return Result.success("分享选项配置创建成功", resultDto);
            } else {
                log.error("分享选项配置创建失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("CONFIG_CREATE_FAILED", "分享选项配置创建失败");
            }

        } catch (Exception e) {
            log.error("创建分享选项配置失败", e);
            return Result.errorResult("CONFIG_CREATE_ERROR", "创建分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ShareOptionConfigDTO> updateConfig(Long id, ShareOptionConfigDTO config) {
        log.info("开始更新分享选项配置 - 入参: id={}, config={}", id, config);
        try {
            if (id == null || config == null) {
                log.warn("参数校验失败 - 配置ID和配置信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID和配置信息不能为空");
            }

            // 检查配置是否存在
            ShareOptionConfig existingConfig = shareOptionConfigMapper.selectById(id);
            if (existingConfig == null) {
                log.warn("分享选项配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "分享选项配置不存在");
            }

            // 检查内容类型和分享类型组合是否已存在（排除当前记录）
            if (StringUtils.hasText(config.getContentType()) && StringUtils.hasText(config.getShareType())) {
                if (!config.getContentType().equals(existingConfig.getContentType()) || 
                    !config.getShareType().equals(existingConfig.getShareType())) {
                    int count = shareOptionConfigMapper.countByContentAndShare(config.getContentType(), config.getShareType());
                    if (count > 0) {
                        log.warn("分享选项配置已存在 - contentType: {}, shareType: {}", 
                                config.getContentType(), config.getShareType());
                        return Result.errorResult("CONFIG_ALREADY_EXISTS", "该内容类型和分享类型的配置已存在");
                    }
                }
            }

            // 更新实体信息
            ShareOptionConfig entity = convertToEntity(config);
            entity.setId(id);
            entity.setUpdatedAt(LocalDateTime.now());
            entity.setCreatedAt(existingConfig.getCreatedAt()); // 保持创建时间不变

            int result = shareOptionConfigMapper.updateById(entity);
            if (result > 0) {
                log.info("分享选项配置更新成功 - id: {}, contentType: {}, shareType: {}", 
                        id, entity.getContentType(), entity.getShareType());
                ShareOptionConfigDTO resultDto = convertToDTO(entity);
                return Result.success("分享选项配置更新成功", resultDto);
            } else {
                log.error("分享选项配置更新失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("CONFIG_UPDATE_FAILED", "分享选项配置更新失败");
            }

        } catch (Exception e) {
            log.error("更新分享选项配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_UPDATE_ERROR", "更新分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteConfig(Long id) {
        log.info("开始删除分享选项配置 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 配置ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID不能为空");
            }

            // 检查配置是否存在
            ShareOptionConfig existingConfig = shareOptionConfigMapper.selectById(id);
            if (existingConfig == null) {
                log.warn("分享选项配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "分享选项配置不存在");
            }

            int result = shareOptionConfigMapper.deleteById(id);
            if (result > 0) {
                log.info("分享选项配置删除成功 - id: {}, contentType: {}, shareType: {}", 
                        id, existingConfig.getContentType(), existingConfig.getShareType());
                return Result.success();
            } else {
                log.error("分享选项配置删除失败 - 数据库删除返回结果: {}", result);
                return Result.errorResult("CONFIG_DELETE_FAILED", "分享选项配置删除失败");
            }

        } catch (Exception e) {
            log.error("删除分享选项配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_DELETE_ERROR", "删除分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> toggleConfigStatus(ToggleShareOptionConfigStatusRequest request) {
        log.info("开始切换分享选项配置状态 - 入参: request={}", request);
        try {
            if (request == null || request.getId() == null || request.getIsEnabled() == null) {
                log.warn("参数校验失败 - 请求参数、配置ID和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、配置ID和状态不能为空");
            }

            // 检查配置是否存在
            ShareOptionConfig existingConfig = shareOptionConfigMapper.selectById(request.getId());
            if (existingConfig == null) {
                log.warn("分享选项配置不存在 - id: {}", request.getId());
                return Result.errorResult("CONFIG_NOT_FOUND", "分享选项配置不存在");
            }

            int result = shareOptionConfigMapper.updateStatus(request.getId(), request.getIsEnabled());
            if (result > 0) {
                log.info("分享选项配置状态切换成功 - id: {}, isEnabled: {}", request.getId(), request.getIsEnabled());
                return Result.success();
            } else {
                log.error("分享选项配置状态切换失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("CONFIG_STATUS_UPDATE_FAILED", "分享选项配置状态切换失败");
            }

        } catch (Exception e) {
            log.error("切换分享选项配置状态失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_STATUS_UPDATE_ERROR", "切换分享选项配置状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> updateConfigSortOrder(UpdateShareOptionConfigSortOrderRequest request) {
        log.info("开始更新分享选项配置排序 - 入参: request={}", request);
        try {
            if (request == null || request.getId() == null || request.getSortOrder() == null) {
                log.warn("参数校验失败 - 请求参数、配置ID和排序值不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、配置ID和排序值不能为空");
            }

            // 检查配置是否存在
            ShareOptionConfig existingConfig = shareOptionConfigMapper.selectById(request.getId());
            if (existingConfig == null) {
                log.warn("分享选项配置不存在 - id: {}", request.getId());
                return Result.errorResult("CONFIG_NOT_FOUND", "分享选项配置不存在");
            }

            int result = shareOptionConfigMapper.updateSortOrder(request.getId(), request.getSortOrder());
            if (result > 0) {
                log.info("分享选项配置排序更新成功 - id: {}, sortOrder: {}", request.getId(), request.getSortOrder());
                return Result.success();
            } else {
                log.error("分享选项配置排序更新失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("CONFIG_SORT_UPDATE_FAILED", "分享选项配置排序更新失败");
            }

        } catch (Exception e) {
            log.error("更新分享选项配置排序失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_SORT_UPDATE_ERROR", "更新分享选项配置排序失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ShareOptionConfigDTO>> getConfigsByContentType(GetConfigsByContentTypeRequest request) {
        log.info("开始根据内容类型获取分享选项配置 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getContentType())) {
                log.warn("参数校验失败 - 请求参数和内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数和内容类型不能为空");
            }

            List<ShareOptionConfig> configs = shareOptionConfigMapper.selectByContentType(request.getContentType(), request.getIsEnabled());
            List<ShareOptionConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("根据内容类型获取分享选项配置成功 - contentType: {}, 数量: {}", request.getContentType(), configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("根据内容类型获取分享选项配置失败 - request: {}", request, e);
            return Result.errorResult("CONFIGS_QUERY_ERROR", "获取分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ShareOptionConfigDTO>> getConfigsByShareType(GetShareOptionConfigsByShareTypeRequest request) {
        log.info("开始根据分享类型获取分享选项配置 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getShareType())) {
                log.warn("参数校验失败 - 请求参数和分享类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数和分享类型不能为空");
            }

            List<ShareOptionConfig> configs = shareOptionConfigMapper.selectByShareType(request.getShareType(), request.getIsEnabled());
            List<ShareOptionConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("根据分享类型获取分享选项配置成功 - shareType: {}, 数量: {}", request.getShareType(), configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("根据分享类型获取分享选项配置失败 - request: {}", request, e);
            return Result.errorResult("CONFIGS_QUERY_ERROR", "获取分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ShareOptionConfigDTO>> getAllEnabledConfigs() {
        log.info("开始获取所有启用的分享选项配置");
        try {
            List<ShareOptionConfig> configs = shareOptionConfigMapper.selectAllEnabled();
            List<ShareOptionConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("获取所有启用的分享选项配置成功 - 数量: {}", configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("获取所有启用的分享选项配置失败", e);
            return Result.errorResult("ENABLED_CONFIGS_QUERY_ERROR", "获取所有启用的分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchUpdateStatusByContentType(BatchUpdateStatusByContentTypeRequest request) {
        log.info("开始批量更新内容类型的分享选项状态 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getContentType()) || request.getIsEnabled() == null) {
                log.warn("参数校验失败 - 请求参数、内容类型和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、内容类型和状态不能为空");
            }

            int result = shareOptionConfigMapper.updateStatusByContentType(request.getContentType(), request.getIsEnabled());
            log.info("批量更新内容类型的分享选项状态成功 - contentType: {}, isEnabled: {}, 影响行数: {}",
                    request.getContentType(), request.getIsEnabled(), result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新内容类型的分享选项状态失败 - request: {}", request, e);
            return Result.errorResult("BATCH_UPDATE_ERROR", "批量更新内容类型的分享选项状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchUpdateStatusByShareType(BatchUpdateStatusByShareTypeRequest request) {
        log.info("开始批量更新分享类型的状态 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getShareType()) || request.getIsEnabled() == null) {
                log.warn("参数校验失败 - 请求参数、分享类型和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、分享类型和状态不能为空");
            }

            int result = shareOptionConfigMapper.updateStatusByShareType(request.getShareType(), request.getIsEnabled());
            log.info("批量更新分享类型的状态成功 - shareType: {}, isEnabled: {}, 影响行数: {}",
                    request.getShareType(), request.getIsEnabled(), result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新分享类型的状态失败 - request: {}", request, e);
            return Result.errorResult("BATCH_UPDATE_ERROR", "批量更新分享类型的状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchUpdateSortOrder(BatchUpdateSortOrderRequest request) {
        log.info("开始批量更新分享选项配置排序 - 入参: request={}", request);
        try {
            if (request == null || request.getConfigIds() == null || request.getSortOrders() == null
                || request.getConfigIds().size() != request.getSortOrders().size()) {
                log.warn("参数校验失败 - 请求参数、配置ID列表和排序值列表不能为空且长度必须一致");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、配置ID列表和排序值列表不能为空且长度必须一致");
            }

            for (int i = 0; i < request.getConfigIds().size(); i++) {
                Long configId = request.getConfigIds().get(i);
                Integer sortOrder = request.getSortOrders().get(i);
                shareOptionConfigMapper.updateSortOrder(configId, sortOrder);
            }

            log.info("批量更新分享选项配置排序成功 - 更新数量: {}", request.getConfigIds().size());
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新分享选项配置排序失败 - request: {}", request, e);
            return Result.errorResult("BATCH_SORT_UPDATE_ERROR", "批量更新分享选项配置排序失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> checkConfigExists(CheckShareOptionConfigExistsRequest request) {
        log.info("开始检查分享选项配置是否存在 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getContentType()) || !StringUtils.hasText(request.getShareType())) {
                log.warn("参数校验失败 - 请求参数、内容类型和分享类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、内容类型和分享类型不能为空");
            }

            ShareOptionConfig config = shareOptionConfigMapper.selectByContentAndShare(request.getContentType(), request.getShareType());
            boolean exists = config != null && (request.getExcludeId() == null || !config.getId().equals(request.getExcludeId()));

            log.info("检查分享选项配置是否存在完成 - contentType: {}, shareType: {}, exists: {}",
                    request.getContentType(), request.getShareType(), exists);
            return Result.success(exists);

        } catch (Exception e) {
            log.error("检查分享选项配置是否存在失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_CHECK_ERROR", "检查分享选项配置是否存在失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ShareOptionConfigDTO>> searchConfigs(SearchConfigsRequest request) {
        log.info("开始搜索分享选项配置 - 入参: request={}", request);
        try {
            String keyword = request != null ? request.getKeyword() : null;
            Boolean isEnabled = request != null ? request.getIsEnabled() : null;

            List<ShareOptionConfig> configs = shareOptionConfigMapper.searchConfigs(keyword, isEnabled);
            List<ShareOptionConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("搜索分享选项配置成功 - 关键词: {}, 结果数量: {}", keyword, configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("搜索分享选项配置失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_SEARCH_ERROR", "搜索分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ShareOptionConfigDTO>> initDefaultConfigs(String contentType) {
        log.info("开始为内容类型初始化默认分享选项配置 - 入参: contentType={}", contentType);
        try {
            if (!StringUtils.hasText(contentType)) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            List<ShareOptionConfigDTO> createdConfigs = new java.util.ArrayList<>();

            // 为每个支持的分享类型创建默认配置
            for (int i = 0; i < SUPPORTED_SHARE_TYPES.size(); i++) {
                String shareType = SUPPORTED_SHARE_TYPES.get(i);
                // 检查是否已存在
                int count = shareOptionConfigMapper.countByContentAndShare(contentType, shareType);
                if (count == 0) {
                    ShareOptionConfigDTO config = new ShareOptionConfigDTO(contentType, shareType, getDefaultDisplayName(shareType));
                    config.setIsEnabled(true);
                    config.setSortOrder(i + 1);

                    Result<ShareOptionConfigDTO> createResult = createConfig(config);
                    if (createResult.isSuccess()) {
                        createdConfigs.add(createResult.getData());
                    }
                }
            }

            log.info("为内容类型初始化默认分享选项配置成功 - contentType: {}, 创建数量: {}",
                    contentType, createdConfigs.size());
            return Result.success(createdConfigs);

        } catch (Exception e) {
            log.error("为内容类型初始化默认分享选项配置失败 - contentType: {}", contentType, e);
            return Result.errorResult("INIT_DEFAULT_CONFIGS_ERROR", "初始化默认分享选项配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<String>> getSupportedShareTypes() {
        log.info("获取支持的分享类型列表");
        return Result.success(SUPPORTED_SHARE_TYPES);
    }

    @Override
    public Result<List<ShareOptionConfigDTO>> copyConfigsToContentType(CopyConfigsToContentTypeRequest request) {
        log.info("开始复制配置到新的内容类型 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getSourceContentType()) || !StringUtils.hasText(request.getTargetContentType())) {
                log.warn("参数校验失败 - 请求参数、源内容类型和目标内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、源内容类型和目标内容类型不能为空");
            }

            // 获取源内容类型的所有配置
            List<ShareOptionConfig> sourceConfigs = shareOptionConfigMapper.selectByContentType(request.getSourceContentType(), null);
            if (sourceConfigs.isEmpty()) {
                log.warn("源内容类型没有配置 - sourceContentType: {}", request.getSourceContentType());
                return Result.errorResult("SOURCE_CONFIGS_NOT_FOUND", "源内容类型没有配置");
            }

            List<ShareOptionConfigDTO> copiedConfigs = new java.util.ArrayList<>();

            // 复制每个配置到目标内容类型
            for (ShareOptionConfig sourceConfig : sourceConfigs) {
                // 检查目标内容类型是否已有该分享类型的配置
                int count = shareOptionConfigMapper.countByContentAndShare(request.getTargetContentType(), sourceConfig.getShareType());
                if (count == 0) {
                    ShareOptionConfigDTO newConfig = new ShareOptionConfigDTO(request.getTargetContentType(), sourceConfig.getShareType(), sourceConfig.getDisplayName());
                    newConfig.setIsEnabled(sourceConfig.getIsEnabled());
                    newConfig.setIconUrl(sourceConfig.getIconUrl());
                    newConfig.setSortOrder(sourceConfig.getSortOrder());
                    newConfig.setConfigJson(sourceConfig.getConfigJson());

                    Result<ShareOptionConfigDTO> createResult = createConfig(newConfig);
                    if (createResult.isSuccess()) {
                        copiedConfigs.add(createResult.getData());
                    }
                }
            }

            log.info("复制配置到新的内容类型成功 - sourceContentType: {}, targetContentType: {}, 复制数量: {}",
                    request.getSourceContentType(), request.getTargetContentType(), copiedConfigs.size());
            return Result.success(copiedConfigs);

        } catch (Exception e) {
            log.error("复制配置到新的内容类型失败 - request: {}", request, e);
            return Result.errorResult("COPY_CONFIGS_ERROR", "复制配置到新的内容类型失败: " + e.getMessage());
        }
    }

    /**
     * 获取默认显示名称
     */
    private String getDefaultDisplayName(String shareType) {
        switch (shareType) {
            case "internal": return "内部分享";
            case "wechat": return "微信分享";
            case "email": return "邮件分享";
            case "link_copy": return "复制链接";
            case "teams": return "Teams分享";
            case "slack": return "Slack分享";
            default: return shareType;
        }
    }

    /**
     * 实体转DTO
     */
    private ShareOptionConfigDTO convertToDTO(ShareOptionConfig entity) {
        if (entity == null) {
            return null;
        }
        ShareOptionConfigDTO dto = new ShareOptionConfigDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * DTO转实体
     */
    private ShareOptionConfig convertToEntity(ShareOptionConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ShareOptionConfig entity = new ShareOptionConfig();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }
}
