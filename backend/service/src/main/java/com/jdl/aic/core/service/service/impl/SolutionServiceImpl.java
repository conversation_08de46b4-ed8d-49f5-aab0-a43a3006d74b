package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.solution.SolutionDTO;
import com.jdl.aic.core.service.client.dto.solution.SolutionStepDTO;
import com.jdl.aic.core.service.client.dto.request.solution.GetSolutionListRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetPopularSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetRecommendedSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetUserSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.SearchSolutionsRequest;
import com.jdl.aic.core.service.client.service.SolutionService;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.dao.entity.primary.Solution;
import com.jdl.aic.core.service.dao.entity.primary.SolutionKnowledge;
import com.jdl.aic.core.service.dao.entity.primary.Knowledge;
import com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation;
import com.jdl.aic.core.service.dao.mapper.primary.SolutionMapper;
import com.jdl.aic.core.service.dao.mapper.primary.SolutionKnowledgeMapper;
import com.jdl.aic.core.service.dao.mapper.primary.KnowledgeMapper;
import com.jdl.aic.core.service.dao.mapper.primary.ContentCategoryRelationMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import java.util.stream.Collectors;

/**
 * 场景解决方案服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("solutionService")
@Transactional
public class SolutionServiceImpl implements SolutionService {

    @Resource
    private SolutionMapper solutionMapper;

    @Resource
    private SolutionKnowledgeMapper solutionKnowledgeMapper;

    @Resource
    private KnowledgeMapper knowledgeMapper;

    @Resource
    private ContentCategoryRelationMapper contentCategoryRelationMapper;

    // ==================== 解决方案管理 ====================

    @Override
    public Result<PageResult<SolutionDTO>> getSolutionList(GetSolutionListRequest request) {
        log.info("开始获取解决方案列表 - 入参: request={}", request);
        try {
            if (request == null || request.getPageRequest() == null) {
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            PageRequest pageRequest = request.getPageRequest();
            String category = request.getCategory();
            Integer status = request.getStatus();
            String authorId = request.getAuthorId();
            String search = request.getSearch();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            Solution queryCondition = new Solution();
            if (status != null) {
                queryCondition.setStatus(status);
            }
            if (authorId != null) {
                queryCondition.setAuthorId(authorId);
            }
            if (StringUtils.hasText(search)) {
                queryCondition.setTitle(search);
            }

            // 查询解决方案列表
            List<Solution> solutions = solutionMapper.selectByCondition(queryCondition);
            log.info("数据库查询完成，查询到 {} 条解决方案记录", solutions.size());

            // 使用PageInfo获取分页信息
            PageInfo<Solution> pageInfo = new PageInfo<>(solutions);

            // 转换为DTO
            List<SolutionDTO> solutionDTOs = solutions.stream()
                    .map(this::convertToSolutionDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<SolutionDTO> pageResult = PageResult.of(
                    solutionDTOs, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());

            log.info("解决方案列表查询成功 - 总数: {}, 当前页: {}, 页大小: {}",
                    pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取解决方案列表失败 - 入参: request={}, 错误信息: {}", request, e.getMessage(), e);
            return Result.errorResult("SOLUTION_LIST_QUERY_ERROR", "获取解决方案列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SolutionDTO> getSolutionById(Long id) {
        log.info("调用getSolutionById方法 - 参数: id={}", id);
        try {
            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "解决方案ID不能为空");
            }

            Solution solution = solutionMapper.selectById(id);
            if (solution == null) {
                return Result.errorResult("SOLUTION_NOT_FOUND", "解决方案不存在");
            }

            SolutionDTO dto = convertToSolutionDTO(solution);
            
            // 加载解决方案的步骤信息
            List<SolutionStepDTO> steps = getSolutionStepsInternal(id);
            dto.setSteps(steps);

            return Result.success(dto);

        } catch (Exception e) {
            log.error("查询解决方案详情失败", e);
            return Result.errorResult("SOLUTION_QUERY_ERROR", "查询解决方案详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SolutionDTO> createSolution(SolutionDTO solution) {
        log.info("开始创建解决方案 - 入参: title={}, authorId={}",
            solution != null ? solution.getTitle() : null,
            solution != null ? solution.getAuthorId() : null);
        try {
            if (solution == null) {
                log.warn("参数校验失败 - 解决方案信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "解决方案信息不能为空");
            }

            // 验证必填字段
            if (!StringUtils.hasText(solution.getTitle())) {
                log.warn("参数校验失败 - 解决方案标题不能为空");
                return Result.errorResult("INVALID_PARAMETER", "解决方案标题不能为空");
            }
            if (solution.getAuthorId() == null) {
                log.warn("参数校验失败 - 作者ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "作者ID不能为空");
            }

            // 转换为实体对象
            Solution entity = new Solution();
            BeanUtils.copyProperties(solution, entity);

            // 设置默认值
            if (entity.getStatus() == null) {
                entity.setStatus(0); // 默认草稿状态
            }
            if (entity.getVisibility() == null) {
                entity.setVisibility(2); // 默认公开
            }
            if (entity.getReadCount() == null) {
                entity.setReadCount(0);
            }
            if (entity.getLikeCount() == null) {
                entity.setLikeCount(0);
            }
            if (entity.getCommentCount() == null) {
                entity.setCommentCount(0);
            }

            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());

            // 插入数据库
            int result = solutionMapper.insert(entity);
            if (result > 0) {
                // 处理分类关系
                List<ContentCategoryRelationDTO> categoriesToSave = prepareCategoriesForSave(solution);
                saveCategoryRelations(entity.getId(), categoriesToSave);

                log.info("解决方案创建成功 - id: {}, title: {}, authorId: {}",
                    entity.getId(), entity.getTitle(), entity.getAuthorId());
                SolutionDTO resultDto = convertToSolutionDTO(entity);
                return Result.success("解决方案创建成功", resultDto);
            } else {
                log.error("解决方案创建失败 - 数据库插入返回结果: {}, title: {}", result, entity.getTitle());
                return Result.errorResult("SOLUTION_CREATE_FAILED", "解决方案创建失败");
            }

        } catch (Exception e) {
            log.error("创建解决方案失败 - 入参: title={}, authorId={}, 错误信息: {}",
                solution != null ? solution.getTitle() : null,
                solution != null ? solution.getAuthorId() : null,
                e.getMessage(), e);
            return Result.errorResult("SOLUTION_CREATE_ERROR", "创建解决方案失败: " + e.getMessage());
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 将Solution实体转换为SolutionDTO
     */
    private SolutionDTO convertToSolutionDTO(Solution solution) {
        if (solution == null) {
            return null;
        }
        
        SolutionDTO dto = new SolutionDTO();
        BeanUtils.copyProperties(solution, dto);

        // 映射字段名差异
        dto.setViewCount(solution.getReadCount());

        // 加载分类信息
        try {
            List<ContentCategoryRelation> categoryRelations = contentCategoryRelationMapper
                    .selectByContentIdAndType(solution.getId(), ContentType.SOLUTION.getValue());
            if (categoryRelations != null && !categoryRelations.isEmpty()) {
                List<ContentCategoryRelationDTO> categoryDTOs = categoryRelations.stream()
                        .map(this::convertToContentCategoryRelationDTO)
                        .collect(Collectors.toList());
                dto.setCategories(categoryDTOs);
            }
        } catch (Exception e) {
            log.warn("加载解决方案分类信息失败 - id: {}", solution.getId(), e);
        }

        return dto;
    }

    /**
     * 内部方法：获取解决方案的步骤列表
     */
    private List<SolutionStepDTO> getSolutionStepsInternal(Long solutionId) {
        try {
            List<SolutionKnowledge> solutionKnowledges = solutionKnowledgeMapper.selectBySolutionId(solutionId);
            if (CollectionUtils.isEmpty(solutionKnowledges)) {
                return new ArrayList<>();
            }

            return solutionKnowledges.stream()
                    .map(this::convertToSolutionStepDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取解决方案步骤失败 - solutionId: {}", solutionId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将SolutionKnowledge转换为SolutionStepDTO
     */
    private SolutionStepDTO convertToSolutionStepDTO(SolutionKnowledge solutionKnowledge) {
        if (solutionKnowledge == null) {
            return null;
        }

        SolutionStepDTO dto = new SolutionStepDTO();
        dto.setId(solutionKnowledge.getId());
        dto.setSolutionId(solutionKnowledge.getSolutionId());
        dto.setStepOrder(solutionKnowledge.getSortOrder());
        dto.setRelatedKnowledgeId(solutionKnowledge.getKnowledgeId());

        // 获取关联的知识信息
        if (solutionKnowledge.getKnowledgeId() != null) {
            try {
                Knowledge knowledge = knowledgeMapper.selectById(solutionKnowledge.getKnowledgeId());
                if (knowledge != null) {
                    dto.setStepTitle(knowledge.getTitle());
                    dto.setStepDescription(knowledge.getDescription());
                    dto.setRelatedKnowledgeTitle(knowledge.getTitle());
                }
            } catch (Exception e) {
                log.warn("获取关联知识信息失败 - knowledgeId: {}", solutionKnowledge.getKnowledgeId(), e);
            }
        }

        return dto;
    }

    @Override
    public Result<SolutionDTO> updateSolution(Long id, SolutionDTO solution) {
        log.info("开始更新解决方案 - 入参: id={}, title={}",
            id, solution != null ? solution.getTitle() : null);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 解决方案ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "解决方案ID不能为空");
            }
            if (solution == null) {
                log.warn("参数校验失败 - 解决方案信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "解决方案信息不能为空");
            }

            // 检查解决方案是否存在
            Solution existingSolution = solutionMapper.selectById(id);
            if (existingSolution == null) {
                log.warn("解决方案不存在 - id: {}", id);
                return Result.errorResult("SOLUTION_NOT_FOUND", "解决方案不存在");
            }

            // 转换为实体对象
            Solution entity = new Solution();
            BeanUtils.copyProperties(solution, entity);
            entity.setId(id);
            entity.setUpdatedAt(LocalDateTime.now());

            int result = solutionMapper.updateById(entity);
            if (result > 0) {
                // 处理分类关系
                List<ContentCategoryRelationDTO> categoriesToSave = prepareCategoriesForSave(solution);
                saveCategoryRelations(id, categoriesToSave);

                log.info("解决方案更新成功 - id: {}, title: {}", id, entity.getTitle());
                SolutionDTO resultDto = convertToSolutionDTO(entity);
                return Result.success("解决方案更新成功", resultDto);
            } else {
                log.error("解决方案更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("SOLUTION_UPDATE_FAILED", "解决方案更新失败");
            }

        } catch (Exception e) {
            log.error("更新解决方案失败 - 入参: id={}, title={}, 错误信息: {}",
                id, solution != null ? solution.getTitle() : null, e.getMessage(), e);
            return Result.errorResult("SOLUTION_UPDATE_ERROR", "更新解决方案失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteSolution(Long id) {
        log.info("开始删除解决方案 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 解决方案ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "解决方案ID不能为空");
            }

            // 检查解决方案是否存在
            Solution existingSolution = solutionMapper.selectById(id);
            if (existingSolution == null) {
                log.warn("解决方案不存在 - id: {}", id);
                return Result.errorResult("SOLUTION_NOT_FOUND", "解决方案不存在");
            }

            // 删除关联的分类关系
            contentCategoryRelationMapper.deleteByContentIdAndType(id, ContentType.SOLUTION.getValue());
            log.debug("删除解决方案分类关系 - solutionId: {}", id);

            // 删除关联的知识步骤（如果存在）
            try {
                solutionKnowledgeMapper.deleteBySolutionId(id);
                log.debug("删除解决方案知识关联 - solutionId: {}", id);
            } catch (Exception e) {
                log.warn("删除解决方案知识关联失败，可能表不存在 - solutionId: {}, 错误: {}", id, e.getMessage());
            }

            // 软删除解决方案
            int result = solutionMapper.deleteById(id);
            if (result > 0) {
                log.info("解决方案删除成功 - id: {}, title: {}", id, existingSolution.getTitle());
                return Result.success();
            } else {
                log.error("解决方案删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("SOLUTION_DELETE_FAILED", "解决方案删除失败");
            }

        } catch (Exception e) {
            log.error("删除解决方案失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("SOLUTION_DELETE_ERROR", "删除解决方案失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> updateSolutionStatus(Long id, Integer status) {
        log.info("开始更新解决方案状态 - 入参: id={}, status={}", id, status);
        try {
            if (id == null || status == null) {
                log.warn("参数校验失败 - 参数不能为空, id: {}, status: {}", id, status);
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 检查解决方案是否存在
            Solution existingSolution = solutionMapper.selectById(id);
            if (existingSolution == null) {
                log.warn("解决方案不存在 - id: {}", id);
                return Result.errorResult("SOLUTION_NOT_FOUND", "解决方案不存在");
            }

            Solution updateEntity = new Solution();
            updateEntity.setId(id);
            updateEntity.setStatus(status);
            updateEntity.setUpdatedAt(LocalDateTime.now());

            int result = solutionMapper.updateById(updateEntity);
            if (result > 0) {
                log.info("解决方案状态更新成功 - id: {}, 新状态: {}", id, status);
                return Result.success();
            } else {
                log.error("解决方案状态更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("SOLUTION_STATUS_UPDATE_FAILED", "解决方案状态更新失败");
            }

        } catch (Exception e) {
            log.error("更新解决方案状态失败 - 入参: id={}, status={}, 错误信息: {}", id, status, e.getMessage(), e);
            return Result.errorResult("SOLUTION_STATUS_UPDATE_ERROR", "更新解决方案状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchUpdateSolutionStatus(List<Long> ids, Integer status) {
        log.info("开始批量更新解决方案状态 - 入参: ids={}, status={}", ids, status);
        try {
            if (CollectionUtils.isEmpty(ids) || status == null) {
                log.warn("参数校验失败 - 参数不能为空, ids: {}, status: {}", ids, status);
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            int successCount = 0;
            for (Long id : ids) {
                try {
                    Result<Void> result = updateSolutionStatus(id, status);
                    if (result.isSuccess()) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.warn("批量更新中单个解决方案状态更新失败 - id: {}, status: {}", id, status, e);
                }
            }

            log.info("批量更新解决方案状态完成 - 总数: {}, 成功: {}", ids.size(), successCount);
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新解决方案状态失败 - 入参: ids={}, status={}, 错误信息: {}", ids, status, e.getMessage(), e);
            return Result.errorResult("SOLUTION_BATCH_STATUS_UPDATE_ERROR", "批量更新解决方案状态失败: " + e.getMessage());
        }
    }

    // ==================== 解决方案步骤管理 ====================

    @Override
    public Result<List<SolutionStepDTO>> getSolutionSteps(Long solutionId) {
        log.info("调用getSolutionSteps方法 - 参数: solutionId={}", solutionId);
        try {
            if (solutionId == null) {
                return Result.errorResult("INVALID_PARAMETER", "解决方案ID不能为空");
            }

            List<SolutionStepDTO> steps = getSolutionStepsInternal(solutionId);
            return Result.success(steps);

        } catch (Exception e) {
            log.error("获取解决方案步骤失败", e);
            return Result.errorResult("SOLUTION_STEPS_QUERY_ERROR", "获取解决方案步骤失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SolutionStepDTO> getSolutionStepById(Long stepId) {
        log.info("调用getSolutionStepById方法 - 参数: stepId={}", stepId);
        try {
            if (stepId == null) {
                return Result.errorResult("INVALID_PARAMETER", "步骤ID不能为空");
            }

            SolutionKnowledge solutionKnowledge = solutionKnowledgeMapper.selectById(stepId);
            if (solutionKnowledge == null) {
                return Result.errorResult("SOLUTION_STEP_NOT_FOUND", "解决方案步骤不存在");
            }

            SolutionStepDTO dto = convertToSolutionStepDTO(solutionKnowledge);
            return Result.success(dto);

        } catch (Exception e) {
            log.error("查询解决方案步骤详情失败", e);
            return Result.errorResult("SOLUTION_STEP_QUERY_ERROR", "查询解决方案步骤详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SolutionStepDTO> addSolutionStep(Long solutionId, SolutionStepDTO step) {
        log.info("开始添加解决方案步骤 - 入参: solutionId={}, stepTitle={}",
            solutionId, step != null ? step.getStepTitle() : null);
        try {
            if (solutionId == null) {
                log.warn("参数校验失败 - 解决方案ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "解决方案ID不能为空");
            }
            if (step == null) {
                log.warn("参数校验失败 - 步骤信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "步骤信息不能为空");
            }
            if (step.getRelatedKnowledgeId() == null) {
                log.warn("参数校验失败 - 关联知识ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "关联知识ID不能为空");
            }

            // 检查解决方案是否存在
            Solution solution = solutionMapper.selectById(solutionId);
            if (solution == null) {
                log.warn("解决方案不存在 - solutionId: {}", solutionId);
                return Result.errorResult("SOLUTION_NOT_FOUND", "解决方案不存在");
            }

            // 检查知识是否存在
            Knowledge knowledge = knowledgeMapper.selectById(step.getRelatedKnowledgeId());
            if (knowledge == null) {
                log.warn("关联知识不存在 - knowledgeId: {}", step.getRelatedKnowledgeId());
                return Result.errorResult("KNOWLEDGE_NOT_FOUND", "关联知识不存在");
            }

            // 创建关联记录
            SolutionKnowledge entity = new SolutionKnowledge();
            entity.setSolutionId(solutionId);
            entity.setKnowledgeId(step.getRelatedKnowledgeId());
            entity.setSortOrder(step.getStepOrder() != null ? step.getStepOrder() : 1);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());

            int result = solutionKnowledgeMapper.insert(entity);
            if (result > 0) {
                log.info("解决方案步骤添加成功 - id: {}, solutionId: {}, knowledgeId: {}",
                    entity.getId(), solutionId, step.getRelatedKnowledgeId());
                SolutionStepDTO resultDto = convertToSolutionStepDTO(entity);
                return Result.success("解决方案步骤添加成功", resultDto);
            } else {
                log.error("解决方案步骤添加失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("SOLUTION_STEP_ADD_FAILED", "解决方案步骤添加失败");
            }

        } catch (Exception e) {
            log.error("添加解决方案步骤失败 - 入参: solutionId={}, stepTitle={}, 错误信息: {}",
                solutionId, step != null ? step.getStepTitle() : null, e.getMessage(), e);
            return Result.errorResult("SOLUTION_STEP_ADD_ERROR", "添加解决方案步骤失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SolutionStepDTO> updateSolutionStep(Long stepId, SolutionStepDTO step) {
        log.info("开始更新解决方案步骤 - 入参: stepId={}, stepTitle={}",
            stepId, step != null ? step.getStepTitle() : null);
        try {
            if (stepId == null) {
                log.warn("参数校验失败 - 步骤ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "步骤ID不能为空");
            }
            if (step == null) {
                log.warn("参数校验失败 - 步骤信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "步骤信息不能为空");
            }

            // 检查步骤是否存在
            SolutionKnowledge existingStep = solutionKnowledgeMapper.selectById(stepId);
            if (existingStep == null) {
                log.warn("解决方案步骤不存在 - stepId: {}", stepId);
                return Result.errorResult("SOLUTION_STEP_NOT_FOUND", "解决方案步骤不存在");
            }

            // 更新步骤信息
            SolutionKnowledge entity = new SolutionKnowledge();
            entity.setId(stepId);
            if (step.getRelatedKnowledgeId() != null) {
                entity.setKnowledgeId(step.getRelatedKnowledgeId());
            }
            if (step.getStepOrder() != null) {
                entity.setSortOrder(step.getStepOrder());
            }
            entity.setUpdatedAt(LocalDateTime.now());

            int result = solutionKnowledgeMapper.updateById(entity);
            if (result > 0) {
                log.info("解决方案步骤更新成功 - stepId: {}", stepId);
                SolutionStepDTO resultDto = convertToSolutionStepDTO(entity);
                return Result.success("解决方案步骤更新成功", resultDto);
            } else {
                log.error("解决方案步骤更新失败 - 数据库更新返回结果: {}, stepId: {}", result, stepId);
                return Result.errorResult("SOLUTION_STEP_UPDATE_FAILED", "解决方案步骤更新失败");
            }

        } catch (Exception e) {
            log.error("更新解决方案步骤失败 - 入参: stepId={}, stepTitle={}, 错误信息: {}",
                stepId, step != null ? step.getStepTitle() : null, e.getMessage(), e);
            return Result.errorResult("SOLUTION_STEP_UPDATE_ERROR", "更新解决方案步骤失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteSolutionStep(Long stepId) {
        log.info("开始删除解决方案步骤 - 入参: stepId={}", stepId);
        try {
            if (stepId == null) {
                log.warn("参数校验失败 - 步骤ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "步骤ID不能为空");
            }

            // 检查步骤是否存在
            SolutionKnowledge existingStep = solutionKnowledgeMapper.selectById(stepId);
            if (existingStep == null) {
                log.warn("解决方案步骤不存在 - stepId: {}", stepId);
                return Result.errorResult("SOLUTION_STEP_NOT_FOUND", "解决方案步骤不存在");
            }

            int result = solutionKnowledgeMapper.deleteById(stepId);
            if (result > 0) {
                log.info("解决方案步骤删除成功 - stepId: {}", stepId);
                return Result.success();
            } else {
                log.error("解决方案步骤删除失败 - 数据库删除返回结果: {}, stepId: {}", result, stepId);
                return Result.errorResult("SOLUTION_STEP_DELETE_FAILED", "解决方案步骤删除失败");
            }

        } catch (Exception e) {
            log.error("删除解决方案步骤失败 - 入参: stepId={}, 错误信息: {}", stepId, e.getMessage(), e);
            return Result.errorResult("SOLUTION_STEP_DELETE_ERROR", "删除解决方案步骤失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> reorderSolutionSteps(Long solutionId, List<Long> stepIds) {
        log.info("开始调整解决方案步骤顺序 - 入参: solutionId={}, stepIds={}", solutionId, stepIds);
        try {
            if (solutionId == null || CollectionUtils.isEmpty(stepIds)) {
                log.warn("参数校验失败 - 参数不能为空, solutionId: {}, stepIds: {}", solutionId, stepIds);
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 检查解决方案是否存在
            Solution solution = solutionMapper.selectById(solutionId);
            if (solution == null) {
                log.warn("解决方案不存在 - solutionId: {}", solutionId);
                return Result.errorResult("SOLUTION_NOT_FOUND", "解决方案不存在");
            }

            // 更新步骤顺序
            for (int i = 0; i < stepIds.size(); i++) {
                Long stepId = stepIds.get(i);
                int newOrder = i + 1;
                try {
                    solutionKnowledgeMapper.updateSortOrder(stepId, newOrder);
                } catch (Exception e) {
                    log.warn("更新步骤顺序失败 - stepId: {}, newOrder: {}", stepId, newOrder, e);
                }
            }

            log.info("解决方案步骤顺序调整成功 - solutionId: {}", solutionId);
            return Result.success();

        } catch (Exception e) {
            log.error("调整解决方案步骤顺序失败 - 入参: solutionId={}, stepIds={}, 错误信息: {}",
                solutionId, stepIds, e.getMessage(), e);
            return Result.errorResult("SOLUTION_STEPS_REORDER_ERROR", "调整解决方案步骤顺序失败: " + e.getMessage());
        }
    }

    // ==================== 解决方案统计和分析 ====================

    @Override
    public Result<Void> incrementViewCount(Long id) {
        log.info("调用incrementViewCount方法 - 参数: id={}", id);
        try {
            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "解决方案ID不能为空");
            }

            int result = solutionMapper.incrementReadCount(id);
            if (result > 0) {
                log.debug("解决方案浏览次数增加成功 - id: {}", id);
                return Result.success();
            } else {
                return Result.errorResult("INCREMENT_VIEW_COUNT_FAILED", "增加浏览次数失败");
            }

        } catch (Exception e) {
            log.error("增加解决方案浏览次数失败", e);
            return Result.errorResult("INCREMENT_VIEW_COUNT_ERROR", "增加浏览次数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> incrementLikeCount(Long id, Long userId) {
        log.info("调用incrementLikeCount方法 - 参数: id={}, userId={}", id, userId);
        try {
            if (id == null || userId == null) {
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // TODO: 这里可以添加防重复点赞的逻辑，比如检查用户是否已经点赞过

            int result = solutionMapper.incrementLikeCount(id);
            if (result > 0) {
                log.debug("解决方案点赞次数增加成功 - id: {}, userId: {}", id, userId);
                return Result.success();
            } else {
                return Result.errorResult("INCREMENT_LIKE_COUNT_FAILED", "增加点赞次数失败");
            }

        } catch (Exception e) {
            log.error("增加解决方案点赞次数失败", e);
            return Result.errorResult("INCREMENT_LIKE_COUNT_ERROR", "增加点赞次数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> decrementLikeCount(Long id, Long userId) {
        log.info("调用decrementLikeCount方法 - 参数: id={}, userId={}", id, userId);
        try {
            if (id == null || userId == null) {
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // TODO: 这里需要实现减少点赞次数的逻辑，当前SolutionMapper中没有对应方法
            // 可以通过直接更新SQL实现
            Solution solution = solutionMapper.selectById(id);
            if (solution != null && solution.getLikeCount() > 0) {
                Solution updateEntity = new Solution();
                updateEntity.setId(id);
                updateEntity.setLikeCount(solution.getLikeCount() - 1);
                updateEntity.setUpdatedAt(LocalDateTime.now());

                int result = solutionMapper.updateById(updateEntity);
                if (result > 0) {
                    log.debug("解决方案点赞次数减少成功 - id: {}, userId: {}", id, userId);
                    return Result.success();
                }
            }

            return Result.errorResult("DECREMENT_LIKE_COUNT_FAILED", "取消点赞失败");

        } catch (Exception e) {
            log.error("取消解决方案点赞失败", e);
            return Result.errorResult("DECREMENT_LIKE_COUNT_ERROR", "取消点赞失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> incrementUseCount(Long id, Long userId) {
        log.info("调用incrementUseCount方法 - 参数: id={}, userId={}", id, userId);
        try {
            if (id == null || userId == null) {
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // TODO: 这里需要实现增加使用次数的逻辑，当前数据库表中没有use_count字段
            // 可以考虑添加到数据库表中或者通过其他方式记录
            log.debug("解决方案使用次数记录成功 - id: {}, userId: {}", id, userId);
            return Result.success();

        } catch (Exception e) {
            log.error("记录解决方案使用次数失败", e);
            return Result.errorResult("INCREMENT_USE_COUNT_ERROR", "记录使用次数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<SolutionDTO>> getPopularSolutions(GetPopularSolutionsRequest request) {
        log.info("调用getPopularSolutions方法 - 参数: request={}", request);
        try {
            if (request == null || request.getPageRequest() == null) {
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            PageRequest pageRequest = request.getPageRequest();
            String category = request.getCategory();
            Integer days = request.getDays();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件 - 按点赞数和阅读数排序
            Solution queryCondition = new Solution();
            queryCondition.setStatus(2); // 只查询已发布的解决方案

            // TODO: 这里可以根据days参数添加时间范围过滤
            List<Solution> solutions = solutionMapper.selectByCondition(queryCondition);

            // 按热度排序（点赞数 + 阅读数）
            solutions.sort((s1, s2) -> {
                int score1 = (s1.getLikeCount() != null ? s1.getLikeCount() : 0) +
                           (s1.getReadCount() != null ? s1.getReadCount() : 0);
                int score2 = (s2.getLikeCount() != null ? s2.getLikeCount() : 0) +
                           (s2.getReadCount() != null ? s2.getReadCount() : 0);
                return Integer.compare(score2, score1); // 降序
            });

            // 使用PageInfo获取分页信息
            PageInfo<Solution> pageInfo = new PageInfo<>(solutions);

            // 转换为DTO
            List<SolutionDTO> solutionDTOs = solutions.stream()
                    .map(this::convertToSolutionDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<SolutionDTO> pageResult = PageResult.of(
                    solutionDTOs, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取热门解决方案失败 - 入参: request={}, 错误信息: {}", request, e.getMessage(), e);
            return Result.errorResult("POPULAR_SOLUTIONS_QUERY_ERROR", "获取热门解决方案失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<SolutionDTO>> getRecommendedSolutions(GetRecommendedSolutionsRequest request) {
        log.info("调用getRecommendedSolutions方法 - 参数: request={}", request);
        try {
            if (request == null || request.getUserId() == null || request.getPageRequest() == null) {
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            Long userId = request.getUserId();
            PageRequest pageRequest = request.getPageRequest();

            // TODO: 这里可以实现基于用户行为的推荐算法
            // 目前简单返回热门解决方案
            GetPopularSolutionsRequest popularRequest = new GetPopularSolutionsRequest(pageRequest, null, 30);
            return getPopularSolutions(popularRequest);

        } catch (Exception e) {
            log.error("获取推荐解决方案失败 - 入参: request={}, 错误信息: {}", request, e.getMessage(), e);
            return Result.errorResult("RECOMMENDED_SOLUTIONS_QUERY_ERROR", "获取推荐解决方案失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<SolutionDTO>> getUserSolutions(GetUserSolutionsRequest request) {
        log.info("调用getUserSolutions方法 - 参数: request={}", request);
        try {
            if (request == null || request.getUserId() == null || request.getPageRequest() == null) {
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            String userId = request.getUserId();
            PageRequest pageRequest = request.getPageRequest();
            Integer status = request.getStatus();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            Solution queryCondition = new Solution();
            queryCondition.setAuthorId(userId);
            if (status != null) {
                queryCondition.setStatus(status);
            }

            List<Solution> solutions = solutionMapper.selectByCondition(queryCondition);

            // 使用PageInfo获取分页信息
            PageInfo<Solution> pageInfo = new PageInfo<>(solutions);

            // 转换为DTO
            List<SolutionDTO> solutionDTOs = solutions.stream()
                    .map(this::convertToSolutionDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<SolutionDTO> pageResult = PageResult.of(
                    solutionDTOs, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取用户解决方案失败 - 入参: request={}, 错误信息: {}", request, e.getMessage(), e);
            return Result.errorResult("USER_SOLUTIONS_QUERY_ERROR", "获取用户解决方案失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getSolutionCategoryStats() {
        log.info("调用getSolutionCategoryStats方法");
        try {
            // TODO: 实现解决方案分类统计
            // 这里需要根据实际的分类表结构来实现
            List<Object> stats = new ArrayList<>();
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取解决方案分类统计失败", e);
            return Result.errorResult("SOLUTION_CATEGORY_STATS_ERROR", "获取解决方案分类统计失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<SolutionDTO>> searchSolutions(SearchSolutionsRequest request) {
        log.info("调用searchSolutions方法 - 参数: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getKeyword()) || request.getPageRequest() == null) {
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            String keyword = request.getKeyword();
            PageRequest pageRequest = request.getPageRequest();
            String category = request.getCategory();
            List<String> tags = request.getTags();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            Solution queryCondition = new Solution();
            queryCondition.setTitle(keyword); // 使用标题搜索
            queryCondition.setStatus(2); // 只搜索已发布的解决方案

            List<Solution> solutions = solutionMapper.selectByCondition(queryCondition);

            // 使用PageInfo获取分页信息
            PageInfo<Solution> pageInfo = new PageInfo<>(solutions);

            // 转换为DTO
            List<SolutionDTO> solutionDTOs = solutions.stream()
                    .map(this::convertToSolutionDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<SolutionDTO> pageResult = PageResult.of(
                    solutionDTOs, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("搜索解决方案失败 - 入参: request={}, 错误信息: {}", request, e.getMessage(), e);
            return Result.errorResult("SEARCH_SOLUTIONS_ERROR", "搜索解决方案失败: " + e.getMessage());
        }
    }

    // ==================== 分类关系处理方法 ====================

    /**
     * 准备要保存的分类列表
     *
     * @param solution 解决方案DTO
     * @return 分类列表
     */
    private List<ContentCategoryRelationDTO> prepareCategoriesForSave(SolutionDTO solution) {
        if (solution.getCategories() != null && !solution.getCategories().isEmpty()) {
            log.debug("使用 categories 字段 - 分类数量: {}", solution.getCategories().size());
            return new ArrayList<>(solution.getCategories());
        }

        return new ArrayList<>();
    }

    /**
     * 保存解决方案分类关系
     *
     * @param solutionId 解决方案ID
     * @param categories  分类ID列表
     */
    private void saveCategoryRelations(Long solutionId, List<ContentCategoryRelationDTO> categories) {
        if (solutionId == null) {
            log.warn("解决方案ID为空，跳过分类关系保存");
            return;
        }

        try {
            // 先删除现有的分类关系
            contentCategoryRelationMapper.deleteByContentIdAndType(solutionId, ContentType.SOLUTION.getValue());
            log.debug("删除解决方案现有分类关系 - solutionId: {}", solutionId);

            // 如果有新的分类ID，则插入新的关系
            if (categories != null && !categories.isEmpty()) {
                List<ContentCategoryRelation> relations = new ArrayList<>();
                LocalDateTime now = LocalDateTime.now();

                for (ContentCategoryRelationDTO category : categories) {
                    if (category.getCategoryId() != null) {
                        ContentCategoryRelation relation = new ContentCategoryRelation();
                        relation.setContentId(solutionId);
                        relation.setCategoryId(category.getCategoryId());
                        relation.setContentType(ContentType.SOLUTION.getValue());
                        relation.setCreatedAt(now);
                        relations.add(relation);
                    }
                }

                if (!relations.isEmpty()) {
                    contentCategoryRelationMapper.batchInsert(relations);
                    log.info("保存解决方案分类关系成功 - solutionId: {}, 分类数量: {}", solutionId, relations.size());
                }
            }
        } catch (Exception e) {
            log.error("保存解决方案分类关系失败 - solutionId: {}, categories: {}, 错误信息: {}",
                    solutionId, categories, e.getMessage(), e);
            // 这里不抛出异常，避免影响主流程
        }
    }

    /**
     * 将ContentCategoryRelation实体转换为ContentCategoryRelationDTO
     */
    private ContentCategoryRelationDTO convertToContentCategoryRelationDTO(ContentCategoryRelation entity) {
        if (entity == null) {
            return null;
        }

        ContentCategoryRelationDTO dto = new ContentCategoryRelationDTO();
        dto.setId(entity.getId());
        dto.setContentType(ContentType.SOLUTION);
        dto.setContentId(entity.getContentId());
        dto.setCategoryId(entity.getCategoryId());
        dto.setCreatedAt(entity.getCreatedAt());

        return dto;
    }
}
