package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.CrawlerContentDTO;
import com.jdl.aic.core.service.client.service.CrawlerContentService;
import com.jdl.aic.core.service.dao.entity.primary.CrawlerContent;
import com.jdl.aic.core.service.dao.mapper.primary.CrawlerContentMapper;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 爬虫内容管理服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("crawlerContentService")
public class CrawlerContentServiceImpl implements CrawlerContentService {

    @Resource
    private CrawlerContentMapper crawlerContentMapper;

    @Override
    public Result<List<Map<String, Object>>> countContentByType() {
        log.info("开始统计各type类型的内容数量");
        try {
            List<Map<String, Object>> result = crawlerContentMapper.countByType();
            log.info("统计各type类型的内容数量成功 - 返回 {} 条记录", result.size());
            return Result.success(result);
        } catch (Exception e) {
            log.error("统计各type类型的内容数量失败，错误信息: {}", e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_COUNT_ERROR", "统计各type类型的内容数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CrawlerContentDTO>> getDistinctTaskInfoByType(String type) {
        log.info("开始根据type查询去重后的taskName和taskId - 入参: type={}", type);
        try {
            if (type == null) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            // 查询去重后的taskName和taskId
            List<CrawlerContent> contents = crawlerContentMapper.selectDistinctTaskInfoByType(type);
            log.info("根据type查询去重后的taskName和taskId完成，查询到 {} 条记录", contents.size());

            // 转换为DTO，保留taskId、taskName和taskDesc字段
            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(content -> {
                        CrawlerContentDTO dto = new CrawlerContentDTO();
                        dto.setTaskId(content.getTaskId());
                        dto.setTaskName(content.getTaskName());
                        dto.setTaskDesc(content.getTaskDesc());
                        return dto;
                    })
                    .collect(Collectors.toList());

            log.info("根据type查询去重后的taskName和taskId成功 - type: {}, 返回 {} 条记录", type, contentDTOs.size());
            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("根据type查询去重后的taskName和taskId失败 - 入参: type={}, 错误信息: {}", type, e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "查询失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<CrawlerContentDTO>> getCrawlerContentList(
            PageRequest pageRequest,
            String contentType,
            String language,
            Integer status,
            Boolean isFeatured,
            String search,
            String type,
            String taskId,
            String taskName) {
        log.info("开始获取爬虫内容列表 - 入参: pageRequest={}, contentType={}, language={}, status={}, isFeatured={}, search={}",
            pageRequest, contentType, language, status, isFeatured, search);
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            CrawlerContent queryCondition = new CrawlerContent();
            if (StringUtils.hasText(contentType)) {
                queryCondition.setContentType(contentType);
            }
            if (StringUtils.hasText(language)) {
                queryCondition.setLanguage(language);
            }
            if (status != null) {
                queryCondition.setStatus(status);
            }
            if (isFeatured != null) {
                queryCondition.setIsFeatured(isFeatured);
            }
            if (type != null) {
                queryCondition.setType(type);
            }
            if (StringUtils.hasText(taskId)) {
                queryCondition.setTaskId(taskId);
            }
            if(StringUtils.hasText(taskName)) {
                queryCondition.setTaskName(taskName);
            }
            log.debug("构建查询条件完成: contentType={}, language={}, status={}, isFeatured={}",
                contentType, language, status, isFeatured);

            // 查询爬虫内容列表
            List<CrawlerContent> contents;
            if (StringUtils.hasText(search)) {
                log.debug("使用搜索模式查询爬虫内容 - search: {}", search);
                contents = crawlerContentMapper.searchContents(search, contentType, language, status, null);
            } else {
                log.debug("使用条件查询爬虫内容");
                contents = crawlerContentMapper.selectByCondition(queryCondition);
            }
            log.info("数据库查询完成，查询到 {} 条爬虫内容记录", contents.size());

            // 使用PageInfo获取分页信息
            PageInfo<CrawlerContent> pageInfo = new PageInfo<>(contents);

            // 转换为DTO
            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            PageResult<CrawlerContentDTO> pageResult = PageResult.of(
                    contentDTOs, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());
            log.info("获取爬虫内容列表成功 - 返回 {} 条记录，总数: {}", contentDTOs.size(), pageInfo.getTotal());

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取爬虫内容列表失败 - 入参: contentType={}, language={}, status={}, isFeatured={}, search={}, 错误信息: {}",
                contentType, language, status, isFeatured, search, e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_LIST_QUERY_ERROR", "获取爬虫内容列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CrawlerContentDTO> getCrawlerContentById(Long id) {
        log.info("开始根据ID获取爬虫内容详情 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }

            CrawlerContent content = crawlerContentMapper.selectById(id);
            if (content == null) {
                log.warn("爬虫内容不存在 - id: {}", id);
                return Result.errorResult("CRAWLER_CONTENT_NOT_FOUND", "爬虫内容不存在");
            }

            log.info("查询爬虫内容成功 - id: {}, title: {}, contentType: {}, status: {}",
                id, content.getTitle(), content.getContentType(), content.getStatus());
            CrawlerContentDTO contentDTO = convertToCrawlerContentDTO(content);
            return Result.success(contentDTO);

        } catch (Exception e) {
            log.error("根据ID获取爬虫内容详情失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "获取爬虫内容详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CrawlerContentDTO> getCrawlerContentByMd5(String contentMd5) {
        try {
            if (!StringUtils.hasText(contentMd5)) {
                return Result.errorResult("INVALID_PARAMETER", "内容MD5值不能为空");
            }

            CrawlerContent content = crawlerContentMapper.selectByMd5(contentMd5);
            if (content == null) {
                return Result.errorResult("CRAWLER_CONTENT_NOT_FOUND", "爬虫内容不存在");
            }

            CrawlerContentDTO contentDTO = convertToCrawlerContentDTO(content);
            return Result.success(contentDTO);

        } catch (Exception e) {
            log.error("根据MD5获取爬虫内容失败，MD5: {}", contentMd5, e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "获取爬虫内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CrawlerContentDTO> getCrawlerContentByLink(String link) {
        log.info("开始根据链接获取爬虫内容 - 入参: link={}", link);
        try {
            if (!StringUtils.hasText(link)) {
                log.warn("参数校验失败 - 链接不能为空");
                return Result.errorResult("INVALID_PARAMETER", "链接不能为空");
            }

            CrawlerContent content = crawlerContentMapper.selectByLink(link);
            if (content == null) {
                log.warn("爬虫内容不存在 - link: {}", link);
                return Result.errorResult("CRAWLER_CONTENT_NOT_FOUND", "爬虫内容不存在");
            }

            log.info("根据链接查询爬虫内容成功 - link: {}, id: {}, title: {}", link, content.getId(), content.getTitle());
            CrawlerContentDTO contentDTO = convertToCrawlerContentDTO(content);
            return Result.success(contentDTO);

        } catch (Exception e) {
            log.error("根据链接获取爬虫内容失败 - 入参: link={}, 错误信息: {}", link, e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "获取爬虫内容失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<CrawlerContentDTO> createCrawlerContent(CrawlerContentDTO crawlerContent) {
        log.info("开始创建爬虫内容 - 入参: title={}, link={}, contentType={}, contentMd5={}",
            crawlerContent != null ? crawlerContent.getTitle() : null,
            crawlerContent != null ? crawlerContent.getLink() : null,
            crawlerContent != null ? crawlerContent.getContentType() : null,
            crawlerContent != null ? crawlerContent.getContentMd5() : null);
        try {
            if (crawlerContent == null) {
                log.warn("参数校验失败 - 爬虫内容信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "爬虫内容信息不能为空");
            }

            // 验证必填字段
            if (!StringUtils.hasText(crawlerContent.getTitle())) {
                log.warn("参数校验失败 - 内容标题不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容标题不能为空");
            }
            if (!StringUtils.hasText(crawlerContent.getLink())) {
                log.warn("参数校验失败 - 原始链接不能为空");
                return Result.errorResult("INVALID_PARAMETER", "原始链接不能为空");
            }
            if (!StringUtils.hasText(crawlerContent.getContentMd5())) {
                log.warn("参数校验失败 - 内容MD5值不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容MD5值不能为空");
            }

            // 检查MD5是否已存在
            CrawlerContent existingContentByMd5 = crawlerContentMapper.selectByMd5(crawlerContent.getContentMd5());
            if (existingContentByMd5 != null) {
                log.warn("相同MD5的内容已存在 - md5: {}, existingId: {}", crawlerContent.getContentMd5(), existingContentByMd5.getId());
                return Result.errorResult("CRAWLER_CONTENT_MD5_EXISTS", "相同MD5的内容已存在");
            }
            log.debug("MD5唯一性校验通过 - md5: {}", crawlerContent.getContentMd5());

            // 检查链接是否已存在
            CrawlerContent existingContentByLink = crawlerContentMapper.selectByLink(crawlerContent.getLink());
            if (existingContentByLink != null) {
                log.warn("相同链接的内容已存在 - link: {}, existingId: {}", crawlerContent.getLink(), existingContentByLink.getId());
                return Result.errorResult("CRAWLER_CONTENT_LINK_EXISTS", "相同链接的内容已存在");
            }
            log.debug("链接唯一性校验通过 - link: {}", crawlerContent.getLink());

            // 转换为实体对象
            CrawlerContent contentEntity = convertToCrawlerContent(crawlerContent);
            contentEntity.setCreatedAt(LocalDateTime.now());
            contentEntity.setUpdatedAt(LocalDateTime.now());

            // 设置默认值
            if (contentEntity.getLanguage() == null) {
                contentEntity.setLanguage("zh-CN");
            }
            if (contentEntity.getIsFeatured() == null) {
                contentEntity.setIsFeatured(false);
            }
            if (contentEntity.getStatus() == null) {
                contentEntity.setStatus(0); // 默认为待处理
            }
            log.debug("实体转换和默认值设置完成 - title: {}, contentType: {}, language: {}, status: {}",
                contentEntity.getTitle(), contentEntity.getContentType(), contentEntity.getLanguage(), contentEntity.getStatus());

            // 插入数据库
            int result = crawlerContentMapper.insert(contentEntity);
            if (result <= 0) {
                log.error("爬虫内容创建失败 - 数据库插入返回结果: {}, title: {}", result, contentEntity.getTitle());
                return Result.errorResult("CRAWLER_CONTENT_CREATE_FAILED", "创建爬虫内容失败");
            }

            log.info("爬虫内容创建成功 - id: {}, title: {}, contentType: {}, link: {}",
                contentEntity.getId(), contentEntity.getTitle(), contentEntity.getContentType(), contentEntity.getLink());
            CrawlerContentDTO resultDTO = convertToCrawlerContentDTO(contentEntity);
            return Result.success(resultDTO);

        } catch (Exception e) {
            log.error("创建爬虫内容失败 - 入参: title={}, link={}, contentType={}, 错误信息: {}",
                crawlerContent != null ? crawlerContent.getTitle() : null,
                crawlerContent != null ? crawlerContent.getLink() : null,
                crawlerContent != null ? crawlerContent.getContentType() : null,
                e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_CREATE_ERROR", "创建爬虫内容失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<CrawlerContentDTO> updateCrawlerContent(Long id, CrawlerContentDTO crawlerContent) {
        try {
            if (id == null || crawlerContent == null) {
                return Result.errorResult("INVALID_PARAMETER", "内容ID和信息不能为空");
            }

            // 检查爬虫内容是否存在
            CrawlerContent existingContent = crawlerContentMapper.selectById(id);
            if (existingContent == null) {
                return Result.errorResult("CRAWLER_CONTENT_NOT_FOUND", "爬虫内容不存在");
            }

            // 如果修改了MD5或链接，检查新的值是否已存在
            if (StringUtils.hasText(crawlerContent.getContentMd5()) && 
                !crawlerContent.getContentMd5().equals(existingContent.getContentMd5())) {
                CrawlerContent duplicateContentByMd5 = crawlerContentMapper.selectByMd5(crawlerContent.getContentMd5());
                if (duplicateContentByMd5 != null && !duplicateContentByMd5.getId().equals(id)) {
                    return Result.errorResult("CRAWLER_CONTENT_MD5_EXISTS", "相同MD5的内容已存在");
                }
            }

            if (StringUtils.hasText(crawlerContent.getLink()) && 
                !crawlerContent.getLink().equals(existingContent.getLink())) {
                CrawlerContent duplicateContentByLink = crawlerContentMapper.selectByLink(crawlerContent.getLink());
                if (duplicateContentByLink != null && !duplicateContentByLink.getId().equals(id)) {
                    return Result.errorResult("CRAWLER_CONTENT_LINK_EXISTS", "相同链接的内容已存在");
                }
            }

            // 更新爬虫内容信息
            CrawlerContent contentEntity = convertToCrawlerContent(crawlerContent);
            contentEntity.setId(id);
            contentEntity.setUpdatedAt(LocalDateTime.now());

            int result = crawlerContentMapper.updateById(contentEntity);
            if (result <= 0) {
                return Result.errorResult("CRAWLER_CONTENT_UPDATE_FAILED", "更新爬虫内容失败");
            }

            // 查询更新后的爬虫内容
            CrawlerContent updatedContent = crawlerContentMapper.selectById(id);
            CrawlerContentDTO resultDTO = convertToCrawlerContentDTO(updatedContent);
            return Result.success(resultDTO);

        } catch (Exception e) {
            log.error("更新爬虫内容失败，ID: {}", id, e);
            return Result.errorResult("CRAWLER_CONTENT_UPDATE_ERROR", "更新爬虫内容失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteCrawlerContent(Long id) {
        log.info("开始删除爬虫内容 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }

            // 检查爬虫内容是否存在
            CrawlerContent existingContent = crawlerContentMapper.selectById(id);
            if (existingContent == null) {
                log.warn("爬虫内容不存在 - id: {}", id);
                return Result.errorResult("CRAWLER_CONTENT_NOT_FOUND", "爬虫内容不存在");
            }
            log.debug("查询到待删除的爬虫内容 - id: {}, title: {}, contentType: {}",
                id, existingContent.getTitle(), existingContent.getContentType());

            int result = crawlerContentMapper.deleteById(id);
            if (result <= 0) {
                log.error("爬虫内容删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("CRAWLER_CONTENT_DELETE_FAILED", "删除爬虫内容失败");
            }

            log.info("爬虫内容删除成功 - id: {}, title: {}, contentType: {}",
                id, existingContent.getTitle(), existingContent.getContentType());
            return Result.success();

        } catch (Exception e) {
            log.error("删除爬虫内容失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_DELETE_ERROR", "删除爬虫内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CrawlerContentDTO>> getCrawlerContentsByStatus(Integer status, Integer limit) {
        try {
            if (status == null) {
                return Result.errorResult("INVALID_PARAMETER", "内容状态不能为空");
            }

            List<CrawlerContent> contents = crawlerContentMapper.selectByStatus(status, limit);
            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("根据状态获取爬虫内容列表失败，status: {}", status, e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "获取爬虫内容列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateCrawlerContentStatus(Long id, Integer status) {
        try {
            if (id == null || status == null) {
                return Result.errorResult("INVALID_PARAMETER", "内容ID和状态不能为空");
            }

            int result = crawlerContentMapper.updateStatus(id, status);
            if (result <= 0) {
                return Result.errorResult("CRAWLER_CONTENT_UPDATE_FAILED", "更新爬虫内容状态失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("更新爬虫内容状态失败，ID: {}, status: {}", id, status, e);
            return Result.errorResult("CRAWLER_CONTENT_UPDATE_ERROR", "更新爬虫内容状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchUpdateCrawlerContentStatus(List<Long> ids, Integer status) {
        try {
            if (ids == null || ids.isEmpty() || status == null) {
                return Result.errorResult("INVALID_PARAMETER", "内容ID列表和状态不能为空");
            }

            int result = crawlerContentMapper.batchUpdateStatus(ids, status);
            if (result <= 0) {
                return Result.errorResult("CRAWLER_CONTENT_BATCH_UPDATE_FAILED", "批量更新爬虫内容状态失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("批量更新爬虫内容状态失败", e);
            return Result.errorResult("CRAWLER_CONTENT_BATCH_UPDATE_ERROR", "批量更新爬虫内容状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateAiSummary(Long id, String aiSummary) {
        try {
            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }

            int result = crawlerContentMapper.updateAiSummary(id, aiSummary);
            if (result <= 0) {
                return Result.errorResult("CRAWLER_CONTENT_UPDATE_FAILED", "更新AI总结失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("更新AI总结失败，ID: {}", id, e);
            return Result.errorResult("CRAWLER_CONTENT_UPDATE_ERROR", "更新AI总结失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateQualityScore(Long id, BigDecimal qualityScore) {
        try {
            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }

            int result = crawlerContentMapper.updateQualityScore(id, qualityScore);
            if (result <= 0) {
                return Result.errorResult("CRAWLER_CONTENT_UPDATE_FAILED", "更新质量评分失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("更新质量评分失败，ID: {}, score: {}", id, qualityScore, e);
            return Result.errorResult("CRAWLER_CONTENT_UPDATE_ERROR", "更新质量评分失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateFeaturedStatus(Long id, Boolean isFeatured) {
        try {
            if (id == null || isFeatured == null) {
                return Result.errorResult("INVALID_PARAMETER", "内容ID和精品状态不能为空");
            }

            int result = crawlerContentMapper.updateFeaturedStatus(id, isFeatured);
            if (result <= 0) {
                return Result.errorResult("CRAWLER_CONTENT_UPDATE_FAILED", "更新精品状态失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("更新精品状态失败，ID: {}, isFeatured: {}", id, isFeatured, e);
            return Result.errorResult("CRAWLER_CONTENT_UPDATE_ERROR", "更新精品状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CrawlerContentDTO>> getCrawlerContentsByQualityScore(
            BigDecimal minScore, BigDecimal maxScore, Integer limit) {
        try {
            List<CrawlerContent> contents = crawlerContentMapper.selectByQualityScore(minScore, maxScore, limit);
            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("根据质量评分获取爬虫内容列表失败，minScore: {}, maxScore: {}", minScore, maxScore, e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "获取爬虫内容列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CrawlerContentDTO>> getCrawlerContentsByType(
            String contentType, Boolean isFeatured, Integer limit) {
        log.info("开始根据内容类型获取爬虫内容 - 入参: contentType={}, isFeatured={}, limit={}",
            contentType, isFeatured, limit);
        try {
            if (!StringUtils.hasText(contentType)) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            List<CrawlerContent> contents = crawlerContentMapper.selectByContentType(contentType, isFeatured, limit);
            log.info("根据内容类型查询完成，查询到 {} 条记录", contents.size());

            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            log.info("根据内容类型获取爬虫内容成功 - contentType: {}, 返回 {} 条记录", contentType, contentDTOs.size());
            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("根据内容类型获取爬虫内容列表失败 - 入参: contentType={}, isFeatured={}, limit={}, 错误信息: {}",
                contentType, isFeatured, limit, e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "获取爬虫内容列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CrawlerContentDTO>> getCrawlerContentsByLanguage(
            String language, Integer status, Integer limit) {
        log.info("开始根据语言获取爬虫内容 - 入参: language={}, status={}, limit={}",
            language, status, limit);
        try {
            if (!StringUtils.hasText(language)) {
                log.warn("参数校验失败 - 内容语言不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容语言不能为空");
            }

            List<CrawlerContent> contents = crawlerContentMapper.selectByLanguage(language, status, limit);
            log.info("根据语言查询完成，查询到 {} 条记录", contents.size());

            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            log.info("根据语言获取爬虫内容成功 - language: {}, 返回 {} 条记录", language, contentDTOs.size());
            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("根据语言获取爬虫内容列表失败 - 入参: language={}, status={}, limit={}, 错误信息: {}",
                language, status, limit, e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "获取爬虫内容列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CrawlerContentDTO>> getCrawlerContentsByPubDateRange(
            LocalDateTime startDate, LocalDateTime endDate, Integer status, Integer limit) {
        try {
            List<CrawlerContent> contents = crawlerContentMapper.selectByPubDateRange(startDate, endDate, status, limit);
            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("根据发布时间范围获取爬虫内容列表失败，startDate: {}, endDate: {}", startDate, endDate, e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "获取爬虫内容列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<List<CrawlerContentDTO>> batchCreateCrawlerContents(List<CrawlerContentDTO> contents) {
        try {
            if (contents == null || contents.isEmpty()) {
                return Result.errorResult("INVALID_PARAMETER", "爬虫内容列表不能为空");
            }

            List<CrawlerContent> contentEntities = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (CrawlerContentDTO dto : contents) {
                // 验证必填字段
                if (!StringUtils.hasText(dto.getTitle()) || !StringUtils.hasText(dto.getLink()) ||
                    !StringUtils.hasText(dto.getContentMd5())) {
                    return Result.errorResult("INVALID_PARAMETER", "内容标题、链接和MD5值不能为空");
                }

                // 检查MD5是否已存在
                CrawlerContent existingContentByMd5 = crawlerContentMapper.selectByMd5(dto.getContentMd5());
                if (existingContentByMd5 != null) {
                    return Result.errorResult("CRAWLER_CONTENT_MD5_EXISTS",
                            "MD5值 " + dto.getContentMd5() + " 的内容已存在");
                }

                // 检查链接是否已存在
                CrawlerContent existingContentByLink = crawlerContentMapper.selectByLink(dto.getLink());
                if (existingContentByLink != null) {
                    return Result.errorResult("CRAWLER_CONTENT_LINK_EXISTS",
                            "链接 " + dto.getLink() + " 的内容已存在");
                }

                CrawlerContent entity = convertToCrawlerContent(dto);
                entity.setCreatedAt(now);
                entity.setUpdatedAt(now);
                if (entity.getLanguage() == null) {
                    entity.setLanguage("zh-CN");
                }
                if (entity.getIsFeatured() == null) {
                    entity.setIsFeatured(false);
                }
                if (entity.getStatus() == null) {
                    entity.setStatus(0);
                }
                contentEntities.add(entity);
            }

            int result = crawlerContentMapper.batchInsert(contentEntities);
            if (result <= 0) {
                return Result.errorResult("CRAWLER_CONTENT_BATCH_CREATE_FAILED", "批量创建爬虫内容失败");
            }

            List<CrawlerContentDTO> resultDTOs = contentEntities.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            return Result.success(resultDTOs);

        } catch (Exception e) {
            log.error("批量创建爬虫内容失败", e);
            return Result.errorResult("CRAWLER_CONTENT_BATCH_CREATE_ERROR", "批量创建爬虫内容失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchDeleteCrawlerContents(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.errorResult("INVALID_PARAMETER", "内容ID列表不能为空");
            }

            for (Long id : ids) {
                int result = crawlerContentMapper.deleteById(id);
                if (result <= 0) {
                    log.warn("删除爬虫内容失败，ID: {}", id);
                }
            }

            return Result.success();

        } catch (Exception e) {
            log.error("批量删除爬虫内容失败", e);
            return Result.errorResult("CRAWLER_CONTENT_BATCH_DELETE_ERROR", "批量删除爬虫内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CrawlerContentDTO>> searchCrawlerContents(
            String keyword, String contentType, String language, Integer status, Integer limit) {
        log.info("开始搜索爬虫内容 - 入参: keyword={}, contentType={}, language={}, status={}, limit={}",
            keyword, contentType, language, status, limit);
        try {
            if (!StringUtils.hasText(keyword)) {
                log.warn("参数校验失败 - 搜索关键词不能为空");
                return Result.errorResult("INVALID_PARAMETER", "搜索关键词不能为空");
            }

            List<CrawlerContent> contents = crawlerContentMapper.searchContents(keyword, contentType, language, status, limit);
            log.info("搜索爬虫内容完成，查询到 {} 条记录", contents.size());

            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            log.info("搜索爬虫内容成功 - 关键词: {}, 返回 {} 条记录", keyword, contentDTOs.size());
            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("搜索爬虫内容失败 - 入参: keyword={}, contentType={}, language={}, status={}, limit={}, 错误信息: {}",
                keyword, contentType, language, status, limit, e.getMessage(), e);
            return Result.errorResult("CRAWLER_CONTENT_SEARCH_ERROR", "搜索爬虫内容失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getContentStatusStatistics() {
        try {
            List<Object> statistics = crawlerContentMapper.countByStatus();
            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取内容状态统计失败", e);
            return Result.errorResult("CRAWLER_CONTENT_STATISTICS_ERROR", "获取内容状态统计失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getContentTypeStatistics() {
        try {
            List<Object> statistics = crawlerContentMapper.countByContentType();
            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取内容类型统计失败", e);
            return Result.errorResult("CRAWLER_CONTENT_STATISTICS_ERROR", "获取内容类型统计失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> isContentExists(String contentMd5) {
        try {
            if (!StringUtils.hasText(contentMd5)) {
                return Result.errorResult("INVALID_PARAMETER", "内容MD5值不能为空");
            }

            CrawlerContent content = crawlerContentMapper.selectByMd5(contentMd5);
            return Result.success(content != null);

        } catch (Exception e) {
            log.error("检查内容是否存在失败，MD5: {}", contentMd5, e);
            return Result.errorResult("CRAWLER_CONTENT_CHECK_ERROR", "检查内容是否存在失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> isLinkExists(String link) {
        try {
            if (!StringUtils.hasText(link)) {
                return Result.errorResult("INVALID_PARAMETER", "链接不能为空");
            }

            CrawlerContent content = crawlerContentMapper.selectByLink(link);
            return Result.success(content != null);

        } catch (Exception e) {
            log.error("检查链接是否存在失败，link: {}", link, e);
            return Result.errorResult("CRAWLER_CONTENT_CHECK_ERROR", "检查链接是否存在失败: " + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 将CrawlerContent实体转换为CrawlerContentDTO
     *
     * @param content CrawlerContent实体
     * @return CrawlerContentDTO
     */
    private CrawlerContentDTO convertToCrawlerContentDTO(CrawlerContent content) {
        if (content == null) {
            return null;
        }

        CrawlerContentDTO dto = new CrawlerContentDTO();
        BeanUtils.copyProperties(content, dto);

        // 处理JSON字段
        if (StringUtils.hasText(content.getTags())) {
            try {
                List<String> tagList = JSON.parseArray(content.getTags(), String.class);
                dto.setTags(tagList);
            } catch (Exception e) {
                log.warn("解析tags JSON失败: {}", content.getTags(), e);
            }
        }

        // 处理attachments JSON字段
        if (StringUtils.hasText(content.getAttachments())) {
            try {
                List<Object> attachmentList = JSON.parseArray(content.getAttachments(), Object.class);
                dto.setAttachments(attachmentList);
            } catch (Exception e) {
                log.warn("解析attachments JSON失败: {}", content.getAttachments(), e);
            }
        }

        // 处理media JSON字段
        if (StringUtils.hasText(content.getMedia())) {
            try {
                List<Object> mediaList = JSON.parseArray(content.getMedia(), Object.class);
                dto.setMedia(mediaList);
            } catch (Exception e) {
                log.warn("解析media JSON失败: {}", content.getMedia(), e);
            }
        }

        return dto;
    }

    /**
     * 将CrawlerContentDTO转换为CrawlerContent实体
     *
     * @param contentDTO CrawlerContentDTO
     * @return CrawlerContent实体
     */
    private CrawlerContent convertToCrawlerContent(CrawlerContentDTO contentDTO) {
        if (contentDTO == null) {
            return null;
        }

        CrawlerContent content = new CrawlerContent();
        BeanUtils.copyProperties(contentDTO, content);

        // 处理JSON字段
        if (contentDTO.getTags() != null && !contentDTO.getTags().isEmpty()) {
            try {
                String tagsJson = JSON.toJSONString(contentDTO.getTags());
                content.setTags(tagsJson);
            } catch (Exception e) {
                log.warn("序列化tags JSON失败: {}", contentDTO.getTags(), e);
            }
        }

        // 处理attachments JSON字段
        if (contentDTO.getAttachments() != null && !contentDTO.getAttachments().isEmpty()) {
            try {
                String attachmentsJson = JSON.toJSONString(contentDTO.getAttachments());
                content.setAttachments(attachmentsJson);
            } catch (Exception e) {
                log.warn("序列化attachments JSON失败: {}", contentDTO.getAttachments(), e);
            }
        }

        // 处理media JSON字段
        if (contentDTO.getMedia() != null && !contentDTO.getMedia().isEmpty()) {
            try {
                String mediaJson = JSON.toJSONString(contentDTO.getMedia());
                content.setMedia(mediaJson);
            } catch (Exception e) {
                log.warn("序列化media JSON失败: {}", contentDTO.getMedia(), e);
            }
        }

        return content;
    }

    // ==================== 高级条件查询方法实现 ====================

    @Override
    public Result<List<CrawlerContentDTO>> selectByNewFields(String type, String taskId, Integer status, Integer limit) {
        try {
            log.info("开始根据新增字段查询爬虫内容 - type: {}, taskId: {}, status: {}, limit: {}",
                    type, taskId, status, limit);

            List<CrawlerContent> contents = crawlerContentMapper.selectByNewFields(type, taskId, status, limit);
            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            log.info("根据新增字段查询爬虫内容成功 - 查询到 {} 条记录", contentDTOs.size());
            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("根据新增字段查询爬虫内容失败", e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "查询失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CrawlerContentDTO>> selectByMultipleConditions(
            List<Long> ids, List<String> contentTypes, List<String> languages,
            List<Integer> statuses, List<String> types, List<String> taskIds, Integer limit) {
        try {
            log.info("开始多条件组合查询爬虫内容 - ids: {}, contentTypes: {}, languages: {}, statuses: {}, types: {}, taskIds: {}, limit: {}",
                    ids, contentTypes, languages, statuses, types, taskIds, limit);

            List<CrawlerContent> contents = crawlerContentMapper.selectByMultipleConditions(
                    ids, contentTypes, languages, statuses, types, taskIds, limit);
            List<CrawlerContentDTO> contentDTOs = contents.stream()
                    .map(this::convertToCrawlerContentDTO)
                    .collect(Collectors.toList());

            log.info("多条件组合查询爬虫内容成功 - 查询到 {} 条记录", contentDTOs.size());
            return Result.success(contentDTOs);

        } catch (Exception e) {
            log.error("多条件组合查询爬虫内容失败", e);
            return Result.errorResult("CRAWLER_CONTENT_QUERY_ERROR", "查询失败: " + e.getMessage());
        }
    }

}
