package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.UserFollowDTO;
import com.jdl.aic.core.service.client.dto.community.request.CheckFollowStatusRequest;
import com.jdl.aic.core.service.client.dto.community.request.FollowUserRequest;
import com.jdl.aic.core.service.client.dto.community.request.GetUserFollowListRequest;
import com.jdl.aic.core.service.client.dto.community.request.UnfollowUserRequest;
import com.jdl.aic.core.service.dao.entity.portal.UserFollow;
import com.jdl.aic.core.service.dao.mapper.portal.UserFollowMapper;
import com.jdl.aic.core.service.portal.client.UserFollowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户关注服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("userFollowService")
public class UserFollowServiceImpl implements UserFollowService {

    @Resource
    private UserFollowMapper userFollowMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<UserFollowDTO> followUser(FollowUserRequest request) {
        try {
            log.info("关注用户，请求参数：{}", request);
            
            // 参数校验
            if (request.getUserId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (request.getFollowedId() == null) {
                return Result.errorResult("INVALID_PARAM", "被关注人ID不能为空");
            }
            if (request.getUserId().equals(request.getFollowedId())) {
                return Result.errorResult("INVALID_PARAM", "不能关注自己");
            }
            
            // 检查是否已经关注
            UserFollow existingFollow = userFollowMapper
                    .selectByUserIdAndFollowedId(request.getUserId(), request.getFollowedId());
            if (existingFollow != null) {
                return Result.errorResult("ALREADY_FOLLOWED", "用户已关注该用户");
            }
            
            // 创建关注记录
            UserFollow userFollow = new UserFollow();
            userFollow.setUserId(request.getUserId());
            userFollow.setFollowedId(request.getFollowedId());
            userFollow.setCreatedAt(LocalDateTime.now());
            userFollow.setUpdatedAt(LocalDateTime.now());
            
            int result = userFollowMapper.insert(userFollow);
            if (result > 0) {
                UserFollowDTO followDTO = convertToDTO(userFollow);
                log.info("关注用户成功，ID：{}", userFollow.getId());
                return Result.success("关注用户成功", followDTO);
            } else {
                return Result.errorResult("FOLLOW_ADD_ERROR", "关注用户失败");
            }
            
        } catch (Exception e) {
            log.error("关注用户失败", e);
            return Result.errorResult("FOLLOW_ADD_ERROR", "关注用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> unfollowUser(UnfollowUserRequest request) {
        try {
            log.info("取消关注用户，请求参数：{}", request);
            
            // 参数校验
            if (request.getUserId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (request.getFollowedId() == null) {
                return Result.errorResult("INVALID_PARAM", "被关注人ID不能为空");
            }
            
            // 检查关注记录是否存在
            UserFollow existingFollow = userFollowMapper
                    .selectByUserIdAndFollowedId(request.getUserId(), request.getFollowedId());
            if (existingFollow == null) {
                return Result.errorResult("FOLLOW_NOT_FOUND", "关注记录不存在");
            }
            
            // 删除关注记录
            int result = userFollowMapper.deleteByUserIdAndFollowedId(request.getUserId(), request.getFollowedId());
            if (result > 0) {
                log.info("取消关注用户成功，用户ID：{}，被关注人ID：{}", request.getUserId(), request.getFollowedId());
                return Result.success();
            } else {
                return Result.errorResult("FOLLOW_REMOVE_ERROR", "取消关注用户失败");
            }
            
        } catch (Exception e) {
            log.error("取消关注用户失败", e);
            return Result.errorResult("FOLLOW_REMOVE_ERROR", "取消关注用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> toggleFollowStatus(Long userId, Long followedId) {
        try {
            log.info("切换关注状态，用户ID：{}，被关注人ID：{}", userId, followedId);
            
            // 参数校验
            if (userId == null || followedId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID和被关注人ID不能为空");
            }
            if (userId.equals(followedId)) {
                return Result.errorResult("INVALID_PARAM", "不能关注自己");
            }
            
            // 检查当前关注状态
            UserFollow existingFollow = userFollowMapper.selectByUserIdAndFollowedId(userId, followedId);
            
            if (existingFollow != null) {
                // 已关注，取消关注
                UnfollowUserRequest unfollowRequest = new UnfollowUserRequest(userId, followedId);
                Result<Void> unfollowResult = unfollowUser(unfollowRequest);
                if (unfollowResult.isSuccess()) {
                    return Result.success("切换关注状态成功", false);
                } else {
                    return Result.errorResult(unfollowResult.getCode(), unfollowResult.getMessage());
                }
            } else {
                // 未关注，添加关注
                FollowUserRequest followRequest = new FollowUserRequest(userId, followedId);
                Result<UserFollowDTO> followResult = followUser(followRequest);
                if (followResult.isSuccess()) {
                    return Result.success("切换关注状态成功", true);
                } else {
                    return Result.errorResult(followResult.getCode(), followResult.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("切换关注状态失败", e);
            return Result.errorResult("FOLLOW_TOGGLE_ERROR", "切换关注状态失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> isUserFollowed(CheckFollowStatusRequest request) {
        try {
            log.info("检查关注状态，请求参数：{}", request);
            
            // 参数校验
            if (request.getUserId() == null || request.getFollowedId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID和被关注人ID不能为空");
            }
            
            boolean isFollowed = userFollowMapper.existsByUserIdAndFollowedId(request.getUserId(), request.getFollowedId());
            return Result.success("检查关注状态成功", isFollowed);
            
        } catch (Exception e) {
            log.error("检查关注状态失败", e);
            return Result.errorResult("FOLLOW_CHECK_ERROR", "检查关注状态失败：" + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<UserFollowDTO>> getUserFollowList(GetUserFollowListRequest request) {
        try {
            log.info("获取用户关注列表，请求参数：{}", request);
            
            // 参数校验
            if (request.getUserId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            
            // 设置分页
            PageHelper.startPage(request.getPage(), request.getSize());
            
            // 查询关注列表
            List<UserFollow> userFollows = userFollowMapper.selectByCondition(
                    request.getUserId(),
                    request.getFollowType(),
                    request.getFollowedUsername(),
                    request.getFollowedDisplayName(),
                    request.getFollowedDepartment(),
                    request.getFollowedIsActive(),
                    request.getStartDate(),
                    request.getEndDate()
            );
            
            PageInfo<UserFollow> pageInfo = new PageInfo<>(userFollows);
            
            // 转换为DTO
            List<UserFollowDTO> followDTOs = userFollows.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            // 使用静态方法创建PageResult
            PageResult<UserFollowDTO> pageResult = PageResult.of(
                    followDTOs, 
                    pageInfo.getTotal(), 
                    pageInfo.getPageNum() - 1, // PageResult使用0开始的页码
                    pageInfo.getPageSize()
            );
            
            return Result.success("获取用户关注列表成功", pageResult);
            
        } catch (Exception e) {
            log.error("获取用户关注列表失败", e);
            return Result.errorResult("FOLLOW_LIST_ERROR", "获取用户关注列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<UserFollowDTO>> getUserFollowing(Long userId, Integer limit) {
        try {
            log.info("获取用户关注的人列表，用户ID：{}，限制数量：{}", userId, limit);
            
            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            
            List<UserFollow> userFollows = userFollowMapper.selectRecentFollowingByUserId(userId, limit);
            List<UserFollowDTO> followDTOs = userFollows.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            return Result.success("获取用户关注的人列表成功", followDTOs);
            
        } catch (Exception e) {
            log.error("获取用户关注的人列表失败", e);
            return Result.errorResult("FOLLOW_LIST_ERROR", "获取用户关注的人列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<UserFollowDTO>> getUserFollowers(Long userId, Integer limit) {
        try {
            log.info("获取用户的粉丝列表，用户ID：{}，限制数量：{}", userId, limit);
            
            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            
            List<UserFollow> userFollows = userFollowMapper.selectRecentFollowersByFollowedId(userId, limit);
            List<UserFollowDTO> followDTOs = userFollows.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            return Result.success("获取用户的粉丝列表成功", followDTOs);
            
        } catch (Exception e) {
            log.error("获取用户的粉丝列表失败", e);
            return Result.errorResult("FOLLOW_LIST_ERROR", "获取用户的粉丝列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<UserFollowDTO>> getRecentFollowing(Long userId, Integer limit) {
        try {
            log.info("获取最近关注的用户，用户ID：{}，限制数量：{}", userId, limit);
            
            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            
            List<UserFollow> userFollows = userFollowMapper.selectRecentFollowingByUserId(userId, limit);
            List<UserFollowDTO> followDTOs = userFollows.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            return Result.success("获取最近关注的用户成功", followDTOs);
            
        } catch (Exception e) {
            log.error("获取最近关注的用户失败", e);
            return Result.errorResult("FOLLOW_LIST_ERROR", "获取最近关注的用户失败：" + e.getMessage());
        }
    }

    @Override
    public Result<UserFollowDTO> getFollowById(Long followId) {
        try {
            log.info("根据关注ID获取关注详情，关注ID：{}", followId);
            
            if (followId == null) {
                return Result.errorResult("INVALID_PARAM", "关注ID不能为空");
            }
            
            UserFollow userFollow = userFollowMapper.selectById(followId);
            if (userFollow == null) {
                return Result.errorResult("FOLLOW_NOT_FOUND", "关注记录不存在");
            }
            
            UserFollowDTO followDTO = convertToDTO(userFollow);
            return Result.success("获取关注详情成功", followDTO);
            
        } catch (Exception e) {
            log.error("获取关注详情失败", e);
            return Result.errorResult("FOLLOW_GET_ERROR", "获取关注详情失败：" + e.getMessage());
        }
    }

    /**
     * 将实体转换为DTO
     */
    private UserFollowDTO convertToDTO(UserFollow userFollow) {
        if (userFollow == null) {
            return null;
        }
        
        UserFollowDTO dto = new UserFollowDTO();
        BeanUtils.copyProperties(userFollow, dto);
        return dto;
    }

    /**
     * 将DTO转换为实体
     */
    private UserFollow convertToEntity(UserFollowDTO dto) {
        if (dto == null) {
            return null;
        }

        UserFollow entity = new UserFollow();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    // ==================== 统计方法实现 ====================

    @Override
    public Result<Integer> countUserFollowing(Long userId) {
        try {
            log.info("统计用户关注的人数，用户ID：{}", userId);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }

            int count = userFollowMapper.countFollowingByUserId(userId);
            return Result.success("统计用户关注的人数成功", count);

        } catch (Exception e) {
            log.error("统计用户关注的人数失败", e);
            return Result.errorResult("COUNT_ERROR", "统计用户关注的人数失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Integer> countUserFollowers(Long userId) {
        try {
            log.info("统计用户的粉丝数，用户ID：{}", userId);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }

            int count = userFollowMapper.countFollowersByFollowedId(userId);
            return Result.success("统计用户的粉丝数成功", count);

        } catch (Exception e) {
            log.error("统计用户的粉丝数失败", e);
            return Result.errorResult("COUNT_ERROR", "统计用户的粉丝数失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Object> getUserFollowStats(Long userId) {
        try {
            log.info("获取用户关注统计信息，用户ID：{}", userId);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }

            int followingCount = userFollowMapper.countFollowingByUserId(userId);
            int followersCount = userFollowMapper.countFollowersByFollowedId(userId);

            final int finalFollowingCount = followingCount;
            final int finalFollowersCount = followersCount;
            final Long finalUserId = userId;

            Object stats = new Object() {
                public final Long userId = finalUserId;
                public final int followingCount = finalFollowingCount;
                public final int followersCount = finalFollowersCount;
            };

            return Result.success("获取用户关注统计信息成功", stats);

        } catch (Exception e) {
            log.error("获取用户关注统计信息失败", e);
            return Result.errorResult("STATS_ERROR", "获取用户关注统计信息失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getUserFollowStatsBatch(List<Long> userIds) {
        try {
            log.info("批量获取用户关注统计信息，用户ID列表：{}", userIds);

            if (userIds == null || userIds.isEmpty()) {
                return Result.success("批量获取用户关注统计信息成功", Collections.emptyList());
            }

            List<Object> stats = userFollowMapper.countFollowStatsBatch(userIds);
            return Result.success("批量获取用户关注统计信息成功", stats);

        } catch (Exception e) {
            log.error("批量获取用户关注统计信息失败", e);
            return Result.errorResult("STATS_ERROR", "批量获取用户关注统计信息失败：" + e.getMessage());
        }
    }

    // ==================== 批量操作方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<UserFollowDTO>> batchFollowUsers(Long userId, List<Long> followedIds) {
        try {
            log.info("批量关注用户，用户ID：{}，被关注人ID列表：{}", userId, followedIds);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (followedIds == null || followedIds.isEmpty()) {
                return Result.errorResult("INVALID_PARAM", "被关注人ID列表不能为空");
            }

            List<UserFollow> userFollows = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (Long followedId : followedIds) {
                // 跳过自己
                if (userId.equals(followedId)) {
                    continue;
                }
                // 检查是否已经关注
                if (!userFollowMapper.existsByUserIdAndFollowedId(userId, followedId)) {
                    UserFollow userFollow = new UserFollow();
                    userFollow.setUserId(userId);
                    userFollow.setFollowedId(followedId);
                    userFollow.setCreatedAt(now);
                    userFollow.setUpdatedAt(now);
                    userFollows.add(userFollow);
                }
            }

            if (!userFollows.isEmpty()) {
                int result = userFollowMapper.batchInsert(userFollows);
                if (result > 0) {
                    List<UserFollowDTO> followDTOs = userFollows.stream()
                            .map(this::convertToDTO)
                            .collect(Collectors.toList());
                    log.info("批量关注用户成功，关注数量：{}", result);
                    return Result.success("批量关注用户成功", followDTOs);
                }
            }

            return Result.success("批量关注用户成功", Collections.emptyList());

        } catch (Exception e) {
            log.error("批量关注用户失败", e);
            return Result.errorResult("BATCH_FOLLOW_ERROR", "批量关注用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchUnfollowUsers(Long userId, List<Long> followedIds) {
        try {
            log.info("批量取消关注用户，用户ID：{}，被关注人ID列表：{}", userId, followedIds);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (followedIds == null || followedIds.isEmpty()) {
                return Result.errorResult("INVALID_PARAM", "被关注人ID列表不能为空");
            }

            int result = userFollowMapper.batchDeleteByUserIdAndFollowedIds(userId, followedIds);
            log.info("批量取消关注用户成功，影响记录数：{}", result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量取消关注用户失败", e);
            return Result.errorResult("BATCH_UNFOLLOW_ERROR", "批量取消关注用户失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchUnfollowUsersByIds(Long userId, List<Long> followIds) {
        try {
            log.info("批量取消关注用户（根据关注ID），用户ID：{}，关注ID列表：{}", userId, followIds);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (followIds == null || followIds.isEmpty()) {
                return Result.errorResult("INVALID_PARAM", "关注ID列表不能为空");
            }

            int result = userFollowMapper.batchDeleteByIds(followIds);
            log.info("批量取消关注用户成功，影响记录数：{}", result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量取消关注用户失败", e);
            return Result.errorResult("BATCH_UNFOLLOW_ERROR", "批量取消关注用户失败：" + e.getMessage());
        }
    }

    // ==================== 其他接口方法的简单实现 ====================
    // 注：以下方法为接口完整性提供基础实现，实际项目中需要根据具体需求完善

    @Override
    public Result<List<Object>> getRecommendedUsers(Long userId, Integer limit) {
        try {
            List<Object> recommended = userFollowMapper.selectRecommendedUsersByDepartment(userId, limit);
            return Result.success("获取推荐用户成功", recommended);
        } catch (Exception e) {
            log.error("获取推荐用户失败", e);
            return Result.errorResult("RECOMMEND_ERROR", "获取推荐用户失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getRecommendedUsersByMutualFollows(Long userId, Integer limit) {
        try {
            List<Object> recommended = userFollowMapper.selectRecommendedUsersByMutualFollows(userId, limit);
            return Result.success("基于共同关注推荐用户成功", recommended);
        } catch (Exception e) {
            log.error("基于共同关注推荐用户失败", e);
            return Result.errorResult("RECOMMEND_ERROR", "基于共同关注推荐用户失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getRecommendedUsersByDepartment(Long userId, Integer limit) {
        try {
            List<Object> recommended = userFollowMapper.selectRecommendedUsersByDepartment(userId, limit);
            return Result.success("基于部门推荐用户成功", recommended);
        } catch (Exception e) {
            log.error("基于部门推荐用户失败", e);
            return Result.errorResult("RECOMMEND_ERROR", "基于部门推荐用户失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getPeopleYouMayKnow(Long userId, Integer limit) {
        try {
            List<Object> people = userFollowMapper.selectPeopleYouMayKnow(userId, limit);
            return Result.success("获取可能认识的人成功", people);
        } catch (Exception e) {
            log.error("获取可能认识的人失败", e);
            return Result.errorResult("RECOMMEND_ERROR", "获取可能认识的人失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Object> getUserFollowTrend(Long userId, Integer days) {
        try {
            List<Object> trend = userFollowMapper.selectUserFollowTrend(userId, days);
            return Result.success("获取用户关注趋势成功", trend);
        } catch (Exception e) {
            log.error("获取用户关注趋势失败", e);
            return Result.errorResult("TREND_ERROR", "获取用户关注趋势失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Object> getUserFollowPreference(Long userId) {
        try {
            List<Object> preference = userFollowMapper.selectUserFollowPreference(userId);
            return Result.success("获取用户关注偏好成功", preference);
        } catch (Exception e) {
            log.error("获取用户关注偏好失败", e);
            return Result.errorResult("PREFERENCE_ERROR", "获取用户关注偏好失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<UserFollowDTO>> getMutualFollows(Long userId1, Long userId2) {
        try {
            List<UserFollow> mutualFollows = userFollowMapper.selectMutualFollows(userId1, userId2);
            List<UserFollowDTO> followDTOs = mutualFollows.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return Result.success("获取共同关注的用户成功", followDTOs);
        } catch (Exception e) {
            log.error("获取共同关注的用户失败", e);
            return Result.errorResult("MUTUAL_FOLLOW_ERROR", "获取共同关注的用户失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Object> getFollowNetworkAnalysis(Long userId, Integer depth) {
        return Result.success("获取关注网络分析成功", new Object());
    }

    @Override
    public Result<Integer> cleanupInvalidFollows(Long userId) {
        try {
            int result = userFollowMapper.cleanupInvalidFollows(userId);
            return Result.success("清理无效关注关系成功", result);
        } catch (Exception e) {
            log.error("清理无效关注关系失败", e);
            return Result.errorResult("CLEANUP_ERROR", "清理无效关注关系失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Object> exportUserFollowData(Long userId, String format) {
        return Result.success("导出用户关注数据成功", new Object());
    }

    @Override
    public Result<Object> getFollowActivityStats(Long userId, Integer days) {
        try {
            List<Object> stats = userFollowMapper.selectFollowActivityStats(userId, days);
            return Result.success("获取关注活跃度统计成功", stats);
        } catch (Exception e) {
            log.error("获取关注活跃度统计失败", e);
            return Result.errorResult("ACTIVITY_STATS_ERROR", "获取关注活跃度统计失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getPopularUsers(Integer limit, Integer days) {
        try {
            List<Object> popularUsers = userFollowMapper.selectPopularUsers(limit, days);
            return Result.success("获取热门被关注用户成功", popularUsers);
        } catch (Exception e) {
            log.error("获取热门被关注用户失败", e);
            return Result.errorResult("POPULAR_USERS_ERROR", "获取热门被关注用户失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getFollowNotifications(Long userId, Integer limit) {
        return Result.success("获取关注相关通知成功", Collections.emptyList());
    }

    @Override
    public Result<Void> markFollowNotificationsAsRead(Long userId, List<Long> notificationIds) {
        return Result.success();
    }
}
