package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO;
import com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO;
import com.jdl.aic.core.service.client.dto.learning.LearningPathDTO;
import com.jdl.aic.core.service.client.dto.learning.LearningPathResourceDTO;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningCourseListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.SearchLearningCoursesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.UpdateLearningCourseStatusRequest;
import com.jdl.aic.core.service.client.service.LearningCourseService;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.dao.entity.primary.LearningCourse;
import com.jdl.aic.core.service.dao.entity.primary.LearningPathResource;
import com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation;
import com.jdl.aic.core.service.dao.entity.primary.Category;
import com.jdl.aic.core.service.dao.mapper.primary.LearningCourseMapper;
import com.jdl.aic.core.service.dao.mapper.primary.LearningPathResourceMapper;
import com.jdl.aic.core.service.dao.mapper.primary.LearningResourceMapper;
import com.jdl.aic.core.service.dao.mapper.primary.ContentCategoryRelationMapper;
import com.jdl.aic.core.service.dao.mapper.primary.CategoryMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 学习课程管理服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("learningCourseService")
@Transactional
public class LearningCourseServiceImpl implements LearningCourseService {

    @Resource
    private LearningCourseMapper learningCourseMapper;

    @Resource
    private LearningPathResourceMapper learningPathResourceMapper;

    @Resource
    private LearningResourceMapper learningResourceMapper;

    @Resource
    private ContentCategoryRelationMapper contentCategoryRelationMapper;

    @Resource
    private CategoryMapper categoryMapper;

    // ==================== 课程管理 ====================

    @Override
    public Result<PageResult<LearningCourseDTO>> getLearningCourseList(GetLearningCourseListRequest request) {
        log.info("开始获取学习课程列表 - 入参: {}", request);

        try {
            // 参数校验
            if (request == null || request.getPageRequest() == null) {
                log.warn("参数校验失败 - 请求参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(request.getPageRequest().getPage(), request.getPageRequest().getSize());

            // 查询课程列表
            List<LearningCourse> courses = learningCourseMapper.selectByConditions(
                    request.getCategory(), request.getDifficultyLevel(), request.getStatus(),
                    request.getIsOfficial(), request.getCreatorId(), request.getSearch());

            // 转换为PageInfo
            PageInfo<LearningCourse> pageInfo = new PageInfo<>(courses);

            // 转换为DTO
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<LearningCourseDTO> pageResult = PageResult.of(
                    courseDTOs,
                    pageInfo.getTotal(),
                    pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                    pageInfo.getPageSize()
            );

            log.info("获取学习课程列表成功 - 总数: {}, 当前页: {}", pageInfo.getTotal(), pageInfo.getPageNum());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取学习课程列表失败", e);
            return Result.errorResult("LEARNING_COURSE_LIST_QUERY_ERROR", "获取学习课程列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<LearningCourseDTO> getLearningCourseById(Long id) {
        log.info("开始根据ID获取学习课程 - id: {}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 课程ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID不能为空");
            }

            LearningCourse course = learningCourseMapper.selectById(id);
            if (course == null) {
                log.warn("课程不存在 - id: {}", id);
                return Result.errorResult("LEARNING_COURSE_NOT_FOUND", "课程不存在");
            }

            LearningCourseDTO courseDTO = convertToLearningCourseDTO(course);

            log.info("根据ID获取学习课程成功 - id: {}, name: {}", id, course.getName());
            return Result.success(courseDTO);

        } catch (Exception e) {
            log.error("根据ID获取学习课程失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_COURSE_QUERY_ERROR", "获取学习课程失败: " + e.getMessage());
        }
    }

    @Override
    public Result<LearningCourseDTO> createLearningCourse(LearningCourseDTO course) {
        log.info("开始创建学习课程 - 入参: name={}, creatorId={}",
                course != null ? course.getName() : null,
                course != null ? course.getCreatorId() : null);

        try {
            if (course == null) {
                log.warn("参数校验失败 - 课程信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程信息不能为空");
            }

            // 验证必填字段
            if (!StringUtils.hasText(course.getName())) {
                log.warn("参数校验失败 - 课程名称不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程名称不能为空");
            }
            if (!StringUtils.hasText(course.getCreatorId())) {
                log.warn("参数校验失败 - 创建者ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "创建者ID不能为空");
            }

            // 检查课程名称是否重复
            LearningCourse existingCourse = learningCourseMapper.selectByName(course.getName(), null);
            if (existingCourse != null) {
                log.warn("课程名称已存在 - name: {}", course.getName());
                return Result.errorResult("LEARNING_COURSE_NAME_EXISTS", "课程名称已存在");
            }

            // 转换为实体
            LearningCourse entity = convertToLearningCourse(course);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());

            // 设置默认值
            if (entity.getStatus() == null) {
                entity.setStatus("DRAFT");
            }
            if (entity.getIsOfficial() == null) {
                entity.setIsOfficial(false);
            }
            if (entity.getResourceCount() == null) {
                entity.setResourceCount(0);
            }
            if (entity.getEnrolledCount() == null) {
                entity.setEnrolledCount(0);
            }
            if (entity.getCompletionCount() == null) {
                entity.setCompletionCount(0);
            }
            if (entity.getCompletionRate() == null) {
                entity.setCompletionRate(BigDecimal.ZERO);
            }

            // 插入数据库
            int result = learningCourseMapper.insert(entity);
            if (result > 0) {
                // 处理分类关系
                saveCategoryRelations(entity.getId(), course.getCategories());

                // 处理学习路径关系
                saveLearningPathRelations(entity.getId(), course.getPaths(), course.getCreatorId());

                log.info("学习课程创建成功 - id: {}, name: {}", entity.getId(), entity.getName());
                LearningCourseDTO resultDto = convertToLearningCourseDTO(entity);
                return Result.success("学习课程创建成功", resultDto);
            } else {
                log.error("学习课程创建失败 - 数据库插入返回结果: {}, name: {}", result, entity.getName());
                return Result.errorResult("LEARNING_COURSE_CREATE_FAILED", "学习课程创建失败");
            }

        } catch (Exception e) {
            log.error("创建学习课程失败", e);
            return Result.errorResult("LEARNING_COURSE_CREATE_ERROR", "创建学习课程失败: " + e.getMessage());
        }
    }

    @Override
    public Result<LearningCourseDTO> updateLearningCourse(Long id, LearningCourseDTO course) {
        log.info("开始更新学习课程 - id: {}, name: {}", id, course != null ? course.getName() : null);

        try {
            if (id == null || course == null) {
                log.warn("参数校验失败 - 课程ID和课程信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID和课程信息不能为空");
            }

            // 检查课程是否存在
            LearningCourse existingCourse = learningCourseMapper.selectById(id);
            if (existingCourse == null) {
                log.warn("课程不存在 - id: {}", id);
                return Result.errorResult("LEARNING_COURSE_NOT_FOUND", "课程不存在");
            }

            // 检查课程名称是否重复（排除当前课程）
            if (StringUtils.hasText(course.getName())) {
                LearningCourse duplicateCourse = learningCourseMapper.selectByName(course.getName(), id);
                if (duplicateCourse != null) {
                    log.warn("课程名称已存在 - name: {}", course.getName());
                    return Result.errorResult("LEARNING_COURSE_NAME_EXISTS", "课程名称已存在");
                }
            }

            // 转换为实体并设置ID
            LearningCourse entity = convertToLearningCourse(course);
            entity.setId(id);
            entity.setUpdatedAt(LocalDateTime.now());

            // 更新数据库
            int result = learningCourseMapper.updateById(entity);
            if (result > 0) {
                // 处理分类关系
                saveCategoryRelations(id, course.getCategories());

                // 处理学习路径关系
                saveLearningPathRelations(id, course.getPaths(), course.getCreatorId());

                // 重新查询更新后的数据
                LearningCourse updatedCourse = learningCourseMapper.selectById(id);
                LearningCourseDTO resultDto = convertToLearningCourseDTO(updatedCourse);

                log.info("学习课程更新成功 - id: {}, name: {}", id, updatedCourse.getName());
                return Result.success("学习课程更新成功", resultDto);
            } else {
                log.error("学习课程更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("LEARNING_COURSE_UPDATE_FAILED", "学习课程更新失败");
            }

        } catch (Exception e) {
            log.error("更新学习课程失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_COURSE_UPDATE_ERROR", "更新学习课程失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteLearningCourse(Long id) {
        log.info("开始删除学习课程 - id: {}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 课程ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID不能为空");
            }

            // 检查课程是否存在
            LearningCourse existingCourse = learningCourseMapper.selectById(id);
            if (existingCourse == null) {
                log.warn("课程不存在 - id: {}", id);
                return Result.errorResult("LEARNING_COURSE_NOT_FOUND", "课程不存在");
            }

            // 软删除
            int result = learningCourseMapper.deleteById(id);
            if (result > 0) {
                log.info("学习课程删除成功 - id: {}, name: {}", id, existingCourse.getName());
                return Result.success();
            } else {
                log.error("学习课程删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("LEARNING_COURSE_DELETE_FAILED", "学习课程删除失败");
            }

        } catch (Exception e) {
            log.error("删除学习课程失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_COURSE_DELETE_ERROR", "删除学习课程失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> updateLearningCourseStatus(UpdateLearningCourseStatusRequest request) {
        log.info("开始更新学习课程状态 - 入参: {}", request);

        try {
            if (request == null || request.getId() == null || !StringUtils.hasText(request.getStatus())) {
                log.warn("参数校验失败 - 课程ID和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID和状态不能为空");
            }

            // 检查课程是否存在
            LearningCourse existingCourse = learningCourseMapper.selectById(request.getId());
            if (existingCourse == null) {
                log.warn("课程不存在 - id: {}", request.getId());
                return Result.errorResult("LEARNING_COURSE_NOT_FOUND", "课程不存在");
            }

            // 更新状态
            int result = learningCourseMapper.updateStatus(request.getId(), request.getStatus(), request.getUpdatedBy());
            if (result > 0) {
                log.info("学习课程状态更新成功 - id: {}, status: {}", request.getId(), request.getStatus());
                return Result.success();
            } else {
                log.error("学习课程状态更新失败 - 数据库更新返回结果: {}, id: {}", result, request.getId());
                return Result.errorResult("LEARNING_COURSE_STATUS_UPDATE_FAILED", "学习课程状态更新失败");
            }

        } catch (Exception e) {
            log.error("更新学习课程状态失败", e);
            return Result.errorResult("LEARNING_COURSE_STATUS_UPDATE_ERROR", "更新学习课程状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取学习课程分类统计信息
     *
     * <p>该方法会统计所有启用的学习课程分类及其下的课程数量，支持层级结构。
     * 统计信息包括：
     * <ul>
     *   <li>分类基本信息（ID、名称、图标等）</li>
     *   <li>该分类下的课程数量（不包括已删除的课程）</li>
     *   <li>子分类的统计信息（递归获取）</li>
     * </ul>
     *
     * @return 分类统计信息列表，按排序权重和课程数量排序
     */
    @Override
    public Result<List<CategoryStatisticsDTO>> getCourseCategoryStatistics() {
        log.info("开始获取学习课程分类统计信息");

        try {
            // 获取学习课程相关的所有分类
            List<Category> categories = categoryMapper.selectByContentCategory(
                    ContentType.LEARNING_COURSE.getValue(), true);

            if (categories == null || categories.isEmpty()) {
                log.info("未找到学习课程相关分类");
                return Result.success(new ArrayList<>());
            }

            // 构建分类统计信息
            List<CategoryStatisticsDTO> statisticsList = new ArrayList<>();

            for (Category category : categories) {
                CategoryStatisticsDTO statistics = buildCategoryStatistics(category);
                if (statistics != null) {
                    statisticsList.add(statistics);
                }
            }

            // 按排序权重和课程数量排序
            statisticsList.sort((a, b) -> {
                // 首先按排序权重排序
                int sortOrderCompare = Integer.compare(
                        a.getSortOrder() != null ? a.getSortOrder() : Integer.MAX_VALUE,
                        b.getSortOrder() != null ? b.getSortOrder() : Integer.MAX_VALUE
                );
                if (sortOrderCompare != 0) {
                    return sortOrderCompare;
                }
                // 然后按课程数量降序排序
                return Integer.compare(
                        b.getCourseCount() != null ? b.getCourseCount() : 0,
                        a.getCourseCount() != null ? a.getCourseCount() : 0
                );
            });

            log.info("获取学习课程分类统计信息成功 - 分类数量: {}", statisticsList.size());
            return Result.success(statisticsList);

        } catch (Exception e) {
            log.error("获取学习课程分类统计信息失败", e);
            return Result.errorResult("COURSE_CATEGORY_STATISTICS_ERROR",
                    "获取学习课程分类统计信息失败: " + e.getMessage());
        }
    }

    // ==================== 课程搜索和过滤 ====================

    @Override
    public Result<PageResult<LearningCourseDTO>> searchLearningCourses(SearchLearningCoursesRequest request) {
        log.info("开始搜索学习课程 - 入参: {}", request);

        try {
            if (request == null || request.getPageRequest() == null) {
                log.warn("参数校验失败 - 请求参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(request.getPageRequest().getPage(), request.getPageRequest().getSize());

            // 搜索课程
            List<LearningCourse> courses = learningCourseMapper.searchCourses(
                    request.getKeyword(), request.getCategories(), request.getDifficultyLevels(),
                    request.getTags(), request.getIsOfficial(), request.getMinHours(), request.getMaxHours());

            // 转换为PageInfo
            PageInfo<LearningCourse> pageInfo = new PageInfo<>(courses);

            // 转换为DTO
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<LearningCourseDTO> pageResult = PageResult.of(
                    courseDTOs,
                    pageInfo.getTotal(),
                    pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                    pageInfo.getPageSize()
            );

            log.info("搜索学习课程成功 - 总数: {}, 关键词: {}", pageInfo.getTotal(), request.getKeyword());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("搜索学习课程失败", e);
            return Result.errorResult("LEARNING_COURSE_SEARCH_ERROR", "搜索学习课程失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LearningCourseDTO>> getLearningCoursesByCategory(String category, Integer limit) {
        log.info("开始根据分类获取课程列表 - category: {}, limit: {}", category, limit);

        try {
            if (!StringUtils.hasText(category)) {
                log.warn("参数校验失败 - 分类不能为空");
                return Result.errorResult("INVALID_PARAMETER", "分类不能为空");
            }

            List<LearningCourse> courses = learningCourseMapper.selectByCategory(category, limit);
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            log.info("根据分类获取课程列表成功 - category: {}, count: {}", category, courseDTOs.size());
            return Result.success(courseDTOs);

        } catch (Exception e) {
            log.error("根据分类获取课程列表失败 - category: {}", category, e);
            return Result.errorResult("LEARNING_COURSE_CATEGORY_QUERY_ERROR", "根据分类获取课程列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LearningCourseDTO>> getLearningCoursesByDifficulty(String difficultyLevel, Integer limit) {
        log.info("开始根据难度级别获取课程列表 - difficultyLevel: {}, limit: {}", difficultyLevel, limit);

        try {
            if (!StringUtils.hasText(difficultyLevel)) {
                log.warn("参数校验失败 - 难度级别不能为空");
                return Result.errorResult("INVALID_PARAMETER", "难度级别不能为空");
            }

            List<LearningCourse> courses = learningCourseMapper.selectByDifficulty(difficultyLevel, limit);
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            log.info("根据难度级别获取课程列表成功 - difficultyLevel: {}, count: {}", difficultyLevel, courseDTOs.size());
            return Result.success(courseDTOs);

        } catch (Exception e) {
            log.error("根据难度级别获取课程列表失败 - difficultyLevel: {}", difficultyLevel, e);
            return Result.errorResult("LEARNING_COURSE_DIFFICULTY_QUERY_ERROR", "根据难度级别获取课程列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LearningCourseDTO>> getLearningCoursesByCreator(String creatorId, Integer limit) {
        log.info("开始根据创建者获取课程列表 - creatorId: {}, limit: {}", creatorId, limit);

        try {
            if (!StringUtils.hasText(creatorId)) {
                log.warn("参数校验失败 - 创建者ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "创建者ID不能为空");
            }

            List<LearningCourse> courses = learningCourseMapper.selectByCreator(creatorId, limit);
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            log.info("根据创建者获取课程列表成功 - creatorId: {}, count: {}", creatorId, courseDTOs.size());
            return Result.success(courseDTOs);

        } catch (Exception e) {
            log.error("根据创建者获取课程列表失败 - creatorId: {}", creatorId, e);
            return Result.errorResult("LEARNING_COURSE_CREATOR_QUERY_ERROR", "根据创建者获取课程列表失败: " + e.getMessage());
        }
    }

    // ==================== 课程统计和推荐 ====================

    @Override
    public Result<List<LearningCourseDTO>> getPopularLearningCourses(Integer limit) {
        log.info("开始获取热门课程列表 - limit: {}", limit);

        try {
            List<LearningCourse> courses = learningCourseMapper.selectPopularCourses(limit);
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            log.info("获取热门课程列表成功 - count: {}", courseDTOs.size());
            return Result.success(courseDTOs);

        } catch (Exception e) {
            log.error("获取热门课程列表失败", e);
            return Result.errorResult("POPULAR_COURSES_QUERY_ERROR", "获取热门课程列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LearningCourseDTO>> getRecommendedLearningCourses(String userId, Integer limit) {
        log.info("开始获取推荐课程列表 - userId: {}, limit: {}", userId, limit);

        try {
            // 简单实现：返回热门课程作为推荐
            // 实际项目中可以根据用户行为、兴趣等进行个性化推荐
            List<LearningCourse> courses = learningCourseMapper.selectPopularCourses(limit);
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            log.info("获取推荐课程列表成功 - userId: {}, count: {}", userId, courseDTOs.size());
            return Result.success(courseDTOs);

        } catch (Exception e) {
            log.error("获取推荐课程列表失败 - userId: {}", userId, e);
            return Result.errorResult("RECOMMENDED_COURSES_QUERY_ERROR", "获取推荐课程列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LearningCourseDTO>> getLatestLearningCourses(Integer limit) {
        log.info("开始获取最新课程列表 - limit: {}", limit);

        try {
            List<LearningCourse> courses = learningCourseMapper.selectLatestCourses(limit);
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            log.info("获取最新课程列表成功 - count: {}", courseDTOs.size());
            return Result.success(courseDTOs);

        } catch (Exception e) {
            log.error("获取最新课程列表失败", e);
            return Result.errorResult("LATEST_COURSES_QUERY_ERROR", "获取最新课程列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LearningCourseDTO>> getOfficialLearningCourses(Integer limit) {
        log.info("开始获取官方课程列表 - limit: {}", limit);

        try {
            List<LearningCourse> courses = learningCourseMapper.selectOfficialCourses(limit);
            List<LearningCourseDTO> courseDTOs = courses.stream()
                    .map(this::convertToLearningCourseDTO)
                    .collect(Collectors.toList());

            log.info("获取官方课程列表成功 - count: {}", courseDTOs.size());
            return Result.success(courseDTOs);

        } catch (Exception e) {
            log.error("获取官方课程列表失败", e);
            return Result.errorResult("OFFICIAL_COURSES_QUERY_ERROR", "获取官方课程列表失败: " + e.getMessage());
        }
    }

    // ==================== 课程统计 ====================

    @Override
    public Result<Void> incrementEnrolledCount(Long courseId, String userId) {
        log.info("开始增加课程报名人数 - courseId: {}, userId: {}", courseId, userId);

        try {
            if (courseId == null) {
                log.warn("参数校验失败 - 课程ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID不能为空");
            }

            // 检查课程是否存在
            LearningCourse existingCourse = learningCourseMapper.selectById(courseId);
            if (existingCourse == null) {
                log.warn("课程不存在 - courseId: {}", courseId);
                return Result.errorResult("LEARNING_COURSE_NOT_FOUND", "课程不存在");
            }

            // 增加报名人数
            int result = learningCourseMapper.incrementEnrolledCount(courseId);
            if (result > 0) {
                log.info("课程报名人数增加成功 - courseId: {}, userId: {}", courseId, userId);
                return Result.success();
            } else {
                log.error("课程报名人数增加失败 - courseId: {}", courseId);
                return Result.errorResult("ENROLLED_COUNT_INCREMENT_FAILED", "课程报名人数增加失败");
            }

        } catch (Exception e) {
            log.error("增加课程报名人数失败 - courseId: {}", courseId, e);
            return Result.errorResult("ENROLLED_COUNT_INCREMENT_ERROR", "增加课程报名人数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> incrementCompletionCount(Long courseId, String userId) {
        log.info("开始增加课程完成人数 - courseId: {}, userId: {}", courseId, userId);

        try {
            if (courseId == null) {
                log.warn("参数校验失败 - 课程ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID不能为空");
            }

            // 检查课程是否存在
            LearningCourse existingCourse = learningCourseMapper.selectById(courseId);
            if (existingCourse == null) {
                log.warn("课程不存在 - courseId: {}", courseId);
                return Result.errorResult("LEARNING_COURSE_NOT_FOUND", "课程不存在");
            }

            // 增加完成人数
            int result = learningCourseMapper.incrementCompletionCount(courseId);
            if (result > 0) {
                // 自动更新完成率
                updateCompletionRate(courseId);

                log.info("课程完成人数增加成功 - courseId: {}, userId: {}", courseId, userId);
                return Result.success();
            } else {
                log.error("课程完成人数增加失败 - courseId: {}", courseId);
                return Result.errorResult("COMPLETION_COUNT_INCREMENT_FAILED", "课程完成人数增加失败");
            }

        } catch (Exception e) {
            log.error("增加课程完成人数失败 - courseId: {}", courseId, e);
            return Result.errorResult("COMPLETION_COUNT_INCREMENT_ERROR", "增加课程完成人数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> updateResourceCount(Long courseId, Integer resourceCount) {
        log.info("开始更新课程资源数量 - courseId: {}, resourceCount: {}", courseId, resourceCount);

        try {
            if (courseId == null || resourceCount == null) {
                log.warn("参数校验失败 - 课程ID和资源数量不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID和资源数量不能为空");
            }

            if (resourceCount < 0) {
                log.warn("参数校验失败 - 资源数量不能为负数");
                return Result.errorResult("INVALID_PARAMETER", "资源数量不能为负数");
            }

            // 检查课程是否存在
            LearningCourse existingCourse = learningCourseMapper.selectById(courseId);
            if (existingCourse == null) {
                log.warn("课程不存在 - courseId: {}", courseId);
                return Result.errorResult("LEARNING_COURSE_NOT_FOUND", "课程不存在");
            }

            // 更新资源数量
            int result = learningCourseMapper.updateResourceCount(courseId, resourceCount);
            if (result > 0) {
                log.info("课程资源数量更新成功 - courseId: {}, resourceCount: {}", courseId, resourceCount);
                return Result.success();
            } else {
                log.error("课程资源数量更新失败 - courseId: {}", courseId);
                return Result.errorResult("RESOURCE_COUNT_UPDATE_FAILED", "课程资源数量更新失败");
            }

        } catch (Exception e) {
            log.error("更新课程资源数量失败 - courseId: {}", courseId, e);
            return Result.errorResult("RESOURCE_COUNT_UPDATE_ERROR", "更新课程资源数量失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> updateCompletionRate(Long courseId) {
        log.info("开始计算并更新课程完成率 - courseId: {}", courseId);

        try {
            if (courseId == null) {
                log.warn("参数校验失败 - 课程ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID不能为空");
            }

            // 获取课程信息
            LearningCourse course = learningCourseMapper.selectById(courseId);
            if (course == null) {
                log.warn("课程不存在 - courseId: {}", courseId);
                return Result.errorResult("LEARNING_COURSE_NOT_FOUND", "课程不存在");
            }

            // 计算完成率
            BigDecimal completionRate = BigDecimal.ZERO;
            if (course.getEnrolledCount() != null && course.getEnrolledCount() > 0) {
                int completionCount = course.getCompletionCount() != null ? course.getCompletionCount() : 0;
                completionRate = BigDecimal.valueOf(completionCount)
                        .divide(BigDecimal.valueOf(course.getEnrolledCount()), 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
            }

            // 更新完成率
            int result = learningCourseMapper.updateCompletionRate(courseId, completionRate);
            if (result > 0) {
                log.info("课程完成率更新成功 - courseId: {}, completionRate: {}%", courseId, completionRate);
                return Result.success();
            } else {
                log.error("课程完成率更新失败 - courseId: {}", courseId);
                return Result.errorResult("COMPLETION_RATE_UPDATE_FAILED", "课程完成率更新失败");
            }

        } catch (Exception e) {
            log.error("计算并更新课程完成率失败 - courseId: {}", courseId, e);
            return Result.errorResult("COMPLETION_RATE_UPDATE_ERROR", "计算并更新课程完成率失败: " + e.getMessage());
        }
    }

    // ==================== 批量操作 ====================

    @Override
    public Result<Void> batchUpdateStatus(List<Long> courseIds, String status, String updatedBy) {
        log.info("开始批量更新课程状态 - courseIds: {}, status: {}", courseIds, status);

        try {
            if (courseIds == null || courseIds.isEmpty()) {
                log.warn("参数校验失败 - 课程ID列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID列表不能为空");
            }

            if (!StringUtils.hasText(status)) {
                log.warn("参数校验失败 - 状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "状态不能为空");
            }

            // 批量更新状态
            int result = learningCourseMapper.batchUpdateStatus(courseIds, status, updatedBy);
            if (result > 0) {
                log.info("批量更新课程状态成功 - 更新数量: {}, status: {}", result, status);
                return Result.success();
            } else {
                log.error("批量更新课程状态失败 - courseIds: {}", courseIds);
                return Result.errorResult("BATCH_STATUS_UPDATE_FAILED", "批量更新课程状态失败");
            }

        } catch (Exception e) {
            log.error("批量更新课程状态失败", e);
            return Result.errorResult("BATCH_STATUS_UPDATE_ERROR", "批量更新课程状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchDeleteLearningCourses(List<Long> courseIds) {
        log.info("开始批量删除课程 - courseIds: {}", courseIds);

        try {
            if (courseIds == null || courseIds.isEmpty()) {
                log.warn("参数校验失败 - 课程ID列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "课程ID列表不能为空");
            }

            // 批量软删除
            int result = learningCourseMapper.batchDelete(courseIds);
            if (result > 0) {
                log.info("批量删除课程成功 - 删除数量: {}", result);
                return Result.success();
            } else {
                log.error("批量删除课程失败 - courseIds: {}", courseIds);
                return Result.errorResult("BATCH_DELETE_FAILED", "批量删除课程失败");
            }

        } catch (Exception e) {
            log.error("批量删除课程失败", e);
            return Result.errorResult("BATCH_DELETE_ERROR", "批量删除课程失败: " + e.getMessage());
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 将LearningCourse实体转换为LearningCourseDTO
     */
    private LearningCourseDTO convertToLearningCourseDTO(LearningCourse entity) {
        if (entity == null) {
            return null;
        }

        LearningCourseDTO dto = new LearningCourseDTO();
        BeanUtils.copyProperties(entity, dto);

        // 处理标签字符串转数组
        if (StringUtils.hasText(entity.getTags())) {
            List<String> tagList = Arrays.asList(entity.getTags().split(","));
            dto.setTagList(tagList.stream().map(String::trim).collect(Collectors.toList()));
        }

        // 加载分类信息
        try {
            List<ContentCategoryRelation> categoryRelations = contentCategoryRelationMapper
                    .selectByContentIdAndType(entity.getId(), ContentType.LEARNING_COURSE.getValue());
            if (categoryRelations != null && !categoryRelations.isEmpty()) {
                List<ContentCategoryRelationDTO> categoryDTOs = categoryRelations.stream()
                        .map(this::convertToContentCategoryRelationDTO)
                        .collect(Collectors.toList());
                dto.setCategories(categoryDTOs);
            }
        } catch (Exception e) {
            log.warn("加载学习课程分类信息失败 - id: {}", entity.getId(), e);
        }

        return dto;
    }

    /**
     * 将LearningCourseDTO转换为LearningCourse实体
     */
    private LearningCourse convertToLearningCourse(LearningCourseDTO dto) {
        if (dto == null) {
            return null;
        }

        LearningCourse entity = new LearningCourse();
        BeanUtils.copyProperties(dto, entity);

        // 处理标签数组转字符串
        if (dto.getTagList() != null && !dto.getTagList().isEmpty()) {
            String tags = String.join(",", dto.getTagList());
            entity.setTags(tags);
        }

        return entity;
    }

    // ==================== 私有工具方法 ====================

    /**
     * 保存学习课程分类关系
     *
     * @param learningCourseId 学习课程ID
     * @param categories       分类ID列表
     */
    private void saveCategoryRelations(Long learningCourseId, List<ContentCategoryRelationDTO> categories) {
        if (learningCourseId == null) {
            log.warn("学习课程ID为空，跳过分类关系保存");
            return;
        }

        try {
            // 先删除现有的分类关系
            contentCategoryRelationMapper.deleteByContentIdAndType(learningCourseId, ContentType.LEARNING_COURSE.getValue());
            log.debug("删除学习课程现有分类关系 - learningCourseId: {}", learningCourseId);

            // 如果有新的分类ID，则插入新的关系
            if (categories != null && !categories.isEmpty()) {
                List<ContentCategoryRelation> relations = new ArrayList<>();
                LocalDateTime now = LocalDateTime.now();

                for (ContentCategoryRelationDTO category : categories) {
                    if (category.getCategoryId() != null) {
                        ContentCategoryRelation relation = new ContentCategoryRelation();
                        relation.setContentId(learningCourseId);
                        relation.setCategoryId(category.getCategoryId());
                        relation.setContentType(ContentType.LEARNING_COURSE.getValue());
                        relation.setCreatedAt(now);
                        relations.add(relation);
                    }
                }

                if (!relations.isEmpty()) {
                    contentCategoryRelationMapper.batchInsert(relations);
                    log.info("保存学习课程分类关系成功 - learningCourseId: {}, 分类数量: {}", learningCourseId, relations.size());
                }
            }
        } catch (Exception e) {
            log.error("保存学习课程分类关系失败 - learningCourseId: {}, categories: {}, 错误信息: {}",
                    learningCourseId, categories, e.getMessage(), e);
            // 这里不抛出异常，避免影响主流程
        }
    }

    /**
     * 将ContentCategoryRelation实体转换为ContentCategoryRelationDTO
     */
    private ContentCategoryRelationDTO convertToContentCategoryRelationDTO(ContentCategoryRelation entity) {
        if (entity == null) {
            return null;
        }

        ContentCategoryRelationDTO dto = new ContentCategoryRelationDTO();
        dto.setId(entity.getId());
        dto.setContentType(ContentType.LEARNING_COURSE);
        dto.setContentId(entity.getContentId());
        dto.setCategoryId(entity.getCategoryId());
        dto.setCreatedAt(entity.getCreatedAt());

        return dto;
    }

    /**
     * 构建分类统计信息
     *
     * @param category 分类信息
     * @return 分类统计DTO
     */
    private CategoryStatisticsDTO buildCategoryStatistics(Category category) {
        if (category == null) {
            return null;
        }

        try {
            CategoryStatisticsDTO statistics = new CategoryStatisticsDTO();
            statistics.setCategoryId(category.getId().toString());
            statistics.setCategoryName(category.getName());
            statistics.setParentCategoryId(category.getParentId() != null ?
                    category.getParentId().toString() : null);
            statistics.setIconUrl(category.getIconUrl());
            statistics.setSortOrder(category.getSortOrder());

            // 统计该分类下的课程数量
            int courseCount = countCoursesByCategory(category.getId());
            statistics.setCourseCount(courseCount);

            // 对于学习课程，资源数量设置为0（因为这里统计的是课程，不是资源）
            statistics.setResourceCount(0);

            // 获取子分类统计
            List<Category> childCategories = categoryMapper.selectByParentId(category.getId());
            if (childCategories != null && !childCategories.isEmpty()) {
                List<CategoryStatisticsDTO> children = new ArrayList<>();
                for (Category child : childCategories) {
                    // 只包含启用的子分类
                    if (child.getIsActive() != null && child.getIsActive()) {
                        CategoryStatisticsDTO childStatistics = buildCategoryStatistics(child);
                        if (childStatistics != null) {
                            children.add(childStatistics);
                        }
                    }
                }
                statistics.setChildren(children);
            }

            return statistics;

        } catch (Exception e) {
            log.error("构建分类统计信息失败 - categoryId: {}, categoryName: {}",
                    category.getId(), category.getName(), e);
            return null;
        }
    }

    /**
     * 统计指定分类下的课程数量
     *
     * @param categoryId 分类ID
     * @return 课程数量
     */
    private int countCoursesByCategory(Long categoryId) {
        try {
            // 通过内容分类关系表统计课程数量
            List<ContentCategoryRelation> relations = contentCategoryRelationMapper
                    .selectByCategoryId(categoryId);

            if (relations == null || relations.isEmpty()) {
                return 0;
            }

            // 过滤出学习课程类型的关系，并统计有效课程数量
            int count = 0;
            for (ContentCategoryRelation relation : relations) {
                if (ContentType.LEARNING_COURSE.getValue().equals(relation.getContentType())) {
                    // 检查课程是否存在且状态有效
                    LearningCourse course = learningCourseMapper.selectById(relation.getContentId());
                    if (course != null && !"DELETED".equals(course.getStatus())) {
                        count++;
                    }
                }
            }

            return count;

        } catch (Exception e) {
            log.error("统计分类下课程数量失败 - categoryId: {}", categoryId, e);
            return 0;
        }
    }

    /**
     * 保存学习课程路径关系
     *
     * @param courseId  学习课程ID
     * @param paths     学习路径列表
     * @param creatorId 创建者ID
     */
    private void saveLearningPathRelations(Long courseId, List<LearningPathResourceDTO> paths, String creatorId) {
        if (courseId == null) {
            log.warn("学习课程ID为空，跳过路径关系保存");
            return;
        }

        try {
            // 先删除现有的路径关系
            learningPathResourceMapper.deleteByLearningPathId(courseId);

            if (paths != null && !paths.isEmpty()) {
                List<LearningPathResource> coursePathList = new ArrayList<>();
                LocalDateTime now = LocalDateTime.now();

                for (int i = 0; i < paths.size(); i++) {
                    LearningPathResourceDTO pathDTO = paths.get(i);
                    if (pathDTO != null) {
                        // 创建或更新学习路径
                        LearningPathResource coursePath = new LearningPathResource();
                        BeanUtils.copyProperties(pathDTO, coursePath);
                        coursePath.setLearningPathId(courseId);
                        coursePath.setCreatedAt(now);
                        coursePathList.add(coursePath);
                    }
                }

                // 批量插入路径关系
                if (!coursePathList.isEmpty()) {
                    learningPathResourceMapper.batchInsert(coursePathList);
                    log.info("保存学习课程路径关系成功 - courseId: {}, pathCount: {}", courseId, coursePathList.size());
                }
            }

        } catch (Exception e) {
            log.error("保存学习课程路径关系失败 - courseId: {}", courseId, e);
        }
    }
}
