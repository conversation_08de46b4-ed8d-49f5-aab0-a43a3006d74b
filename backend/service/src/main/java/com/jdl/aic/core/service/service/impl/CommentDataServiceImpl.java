package com.jdl.aic.core.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.CommentDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.dao.entity.portal.Comment;
import com.jdl.aic.core.service.dao.mapper.portal.CommentMapper;
import com.jdl.aic.core.service.portal.client.CommentDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评论数据服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("commentDataService")
public class CommentDataServiceImpl implements CommentDataService {

    @Autowired
    private CommentMapper commentMapper;

    // ==================== 评论基本操作 ====================

    @Override
    @Transactional
    public Result<CommentDTO> createComment(CreateCommentRequest request) {
        log.info("开始发布评论 - userId: {}, contentType: {}, contentId: {}，request: {}",
            request.getUserId(), request.getContentType(), request.getContentId(), JSON.toJSONString(request));

        Long userId = request.getUserId();
        Integer contentType = request.getContentType();
        Long contentId = request.getContentId();
        String commentText = request.getCommentText();
        Long relatedKnowledgeTypeId = request.getRelatedKnowledgeTypeId();
        Long parentCommentId = request.getParentCommentId();

        try {
            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }
            if (contentType == null) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }
            if (contentId == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }
            if (!StringUtils.hasText(commentText)) {
                log.warn("参数校验失败 - 评论内容不能为空");
                return Result.errorResult("INVALID_PARAMETER", "评论内容不能为空");
            }
            if (commentText.length() > 1000) {
                log.warn("参数校验失败 - 评论内容过长");
                return Result.errorResult("INVALID_PARAMETER", "评论内容不能超过1000字符");
            }

            // 创建评论
            Comment comment = new Comment(contentType, contentId, relatedKnowledgeTypeId, userId, commentText, parentCommentId);
            comment.setCreatedBy(userId.toString());
            comment.setUpdatedBy(userId.toString());

            int result = commentMapper.insert(comment);
            
            if (result > 0) {
                log.info("发布评论成功 - commentId: {}, userId: {}, contentType: {}, contentId: {}", 
                    comment.getId(), userId, contentType, contentId);
                return Result.success("发布评论成功", convertToCommentDTO(comment));
            } else {
                log.error("发布评论失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("COMMENT_CREATE_FAILED", "发布评论失败");
            }

        } catch (Exception e) {
            log.error("发布评论失败 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId, e);
            return Result.errorResult("COMMENT_CREATE_ERROR", "发布评论失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<CommentDTO> replyComment(ReplyCommentRequest request) {
        log.info("开始回复评论 - userId: {}, parentCommentId: {}",
            request.getUserId(), request.getParentCommentId());

        Long userId = request.getUserId();
        Long parentCommentId = request.getParentCommentId();
        String commentText = request.getCommentText();

        try {
            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }
            if (parentCommentId == null) {
                log.warn("参数校验失败 - 父评论ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "父评论ID不能为空");
            }
            if (!StringUtils.hasText(commentText)) {
                log.warn("参数校验失败 - 回复内容不能为空");
                return Result.errorResult("INVALID_PARAMETER", "回复内容不能为空");
            }

            // 检查父评论是否存在
            Comment parentComment = commentMapper.selectById(parentCommentId);
            if (parentComment == null || parentComment.isDeleted()) {
                log.warn("父评论不存在 - parentCommentId: {}", parentCommentId);
                return Result.errorResult("PARENT_COMMENT_NOT_FOUND", "父评论不存在");
            }

            // 创建回复评论
            Comment comment = new Comment(parentComment.getContentType(), parentComment.getContentId(), 
                userId, commentText, parentCommentId);
            comment.setRelatedKnowledgeTypeId(parentComment.getRelatedKnowledgeTypeId());
            comment.setCreatedBy(userId.toString());
            comment.setUpdatedBy(userId.toString());

            int result = commentMapper.insert(comment);
            
            if (result > 0) {
                log.info("回复评论成功 - commentId: {}, userId: {}, parentCommentId: {}", 
                    comment.getId(), userId, parentCommentId);
                return Result.success("回复评论成功", convertToCommentDTO(comment));
            } else {
                log.error("回复评论失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("COMMENT_REPLY_FAILED", "回复评论失败");
            }

        } catch (Exception e) {
            log.error("回复评论失败 - userId: {}, parentCommentId: {}", userId, parentCommentId, e);
            return Result.errorResult("COMMENT_REPLY_ERROR", "回复评论失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<CommentDTO> updateComment(UpdateCommentRequest request) {
        log.info("开始编辑评论 - userId: {}, commentId: {}",
            request.getUserId(), request.getCommentId());

        Long userId = request.getUserId();
        Long commentId = request.getCommentId();
        String commentText = request.getCommentText();

        try {
            // 参数校验
            if (userId == null || commentId == null) {
                log.warn("参数校验失败 - 用户ID和评论ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID和评论ID不能为空");
            }
            if (!StringUtils.hasText(commentText)) {
                log.warn("参数校验失败 - 评论内容不能为空");
                return Result.errorResult("INVALID_PARAMETER", "评论内容不能为空");
            }

            // 检查评论是否存在且属于当前用户
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null || comment.isDeleted()) {
                log.warn("评论不存在 - commentId: {}", commentId);
                return Result.errorResult("COMMENT_NOT_FOUND", "评论不存在");
            }
            if (!comment.getUserId().equals(userId)) {
                log.warn("无权限编辑评论 - userId: {}, commentId: {}, commentUserId: {}", 
                    userId, commentId, comment.getUserId());
                return Result.errorResult("NO_PERMISSION", "无权限编辑此评论");
            }

            // 更新评论内容
            comment.setCommentText(commentText);
            comment.setUpdatedBy(userId.toString());

            int result = commentMapper.updateById(comment);
            
            if (result > 0) {
                log.info("编辑评论成功 - commentId: {}, userId: {}", commentId, userId);
                return Result.success("编辑评论成功", convertToCommentDTO(comment));
            } else {
                log.error("编辑评论失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("COMMENT_UPDATE_FAILED", "编辑评论失败");
            }

        } catch (Exception e) {
            log.error("编辑评论失败 - userId: {}, commentId: {}", userId, commentId, e);
            return Result.errorResult("COMMENT_UPDATE_ERROR", "编辑评论失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteComment(Long userId, Long commentId) {
        log.info("开始删除评论 - userId: {}, commentId: {}",
                userId, commentId);

        try {
            // 参数校验
            if (userId == null || commentId == null) {
                log.warn("参数校验失败 - 用户ID和评论ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID和评论ID不能为空");
            }

            // 检查评论是否存在且属于当前用户
            Comment comment = commentMapper.selectById(commentId);
            if (comment == null || comment.isDeleted()) {
                log.warn("评论不存在 - commentId: {}", commentId);
                return Result.errorResult("COMMENT_NOT_FOUND", "评论不存在");
            }
            if (!comment.getUserId().equals(userId)) {
                log.warn("无权限删除评论 - userId: {}, commentId: {}, commentUserId: {}", 
                    userId, commentId, comment.getUserId());
                return Result.errorResult("NO_PERMISSION", "无权限删除此评论");
            }

            // 软删除评论
            int result = commentMapper.deleteById(commentId);
            
            if (result > 0) {
                log.info("删除评论成功 - commentId: {}, userId: {}", commentId, userId);
                return Result.success("删除评论成功", null);
            } else {
                log.error("删除评论失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("COMMENT_DELETE_FAILED", "删除评论失败");
            }

        } catch (Exception e) {
            log.error("删除评论失败 - userId: {}, commentId: {}", userId, commentId, e);
            return Result.errorResult("COMMENT_DELETE_ERROR", "删除评论失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CommentDTO> getCommentById(Long commentId) {
        log.info("开始获取评论详情 - commentId: {}", commentId);

        try {
            if (commentId == null) {
                log.warn("参数校验失败 - 评论ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "评论ID不能为空");
            }

            Comment comment = commentMapper.selectById(commentId);
            if (comment == null) {
                log.warn("评论不存在 - commentId: {}", commentId);
                return Result.errorResult("COMMENT_NOT_FOUND", "评论不存在");
            }

            log.info("获取评论详情成功 - commentId: {}", commentId);
            return Result.success("获取评论详情成功", convertToCommentDTO(comment));

        } catch (Exception e) {
            log.error("获取评论详情失败 - commentId: {}", commentId, e);
            return Result.errorResult("COMMENT_GET_ERROR", "获取评论详情失败: " + e.getMessage());
        }
    }

    // ==================== 评论查询 ====================

    @Override
    public Result<PageResult<CommentDTO>> getCommentsByContent(GetCommentsByContentRequest request) {
        log.info("开始获取内容评论列表 - contentType: {}, contentId: {}, sortBy: {}",
            request.getContentType(), request.getContentId(), request.getSortBy());

        Integer contentType = request.getContentType();
        Long contentId = request.getContentId();
        PageRequest pageRequest = request.getPageRequest();
        String sortBy = request.getSortBy();

        try {
            // 参数校验
            if (contentType == null || contentId == null) {
                log.warn("参数校验失败 - 内容类型和内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型和内容ID不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 查询评论列表
            List<Comment> comments = commentMapper.selectByContent(contentType, contentId);

            log.info("数据库查询完成，查询到 {} 条评论记录", comments.size());
            PageInfo<Comment> pageInfo = new PageInfo<>(comments);

            // 转换为DTO
            List<CommentDTO> commentDTOs = comments.stream()
                    .map(this::convertToCommentDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<CommentDTO> pageResult = PageResult.of(
                commentDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                pageInfo.getPageSize()
            );

            log.info("获取内容评论列表成功 - contentType: {}, contentId: {}, total: {}", 
                contentType, contentId, pageInfo.getTotal());
            return Result.success("获取内容评论列表成功", pageResult);

        } catch (Exception e) {
            log.error("获取内容评论列表失败 - contentType: {}, contentId: {}", contentType, contentId, e);
            return Result.errorResult("COMMENT_LIST_ERROR", "获取内容评论列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<CommentDTO>> getUserComments(Long userId, PageRequest pageRequest, Integer contentType) {
        log.info("开始获取用户评论列表 - userId: {}, contentType: {}", userId, contentType);

        try {
            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 查询用户评论列表
            List<Comment> comments = commentMapper.selectByUserId(userId);

            PageInfo<Comment> pageInfo = new PageInfo<>(comments);

            // 转换为DTO
            List<CommentDTO> commentDTOs = comments.stream()
                    .map(this::convertToCommentDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<CommentDTO> pageResult = PageResult.of(
                commentDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1,
                pageInfo.getPageSize()
            );

            log.info("获取用户评论列表成功 - userId: {}, total: {}", userId, pageInfo.getTotal());
            return Result.success("获取用户评论列表成功", pageResult);

        } catch (Exception e) {
            log.error("获取用户评论列表失败 - userId: {}", userId, e);
            return Result.errorResult("USER_COMMENT_LIST_ERROR", "获取用户评论列表失败: " + e.getMessage());
        }
    }

    // ==================== 评论统计 ====================

    @Override
    public Result<Integer> getCommentCount(GetCommentCountRequest request) {
        if (request == null) {
            log.warn("参数校验失败 - 请求对象不能为空");
            return Result.errorResult("INVALID_PARAMETER", "请求对象不能为空");
        }

        Integer contentType = request.getContentType();
        Long contentId = request.getContentId();

        log.info("开始统计内容评论数 - contentType: {}, contentId: {}", contentType, contentId);

        try {
            if (contentType == null || contentId == null) {
                log.warn("参数校验失败 - 内容类型和内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型和内容ID不能为空");
            }

            int count = commentMapper.countByContent(contentType, contentId);
            log.info("统计内容评论数成功 - contentType: {}, contentId: {}, count: {}", 
                contentType, contentId, count);
            return Result.success("统计内容评论数成功", count);

        } catch (Exception e) {
            log.error("统计内容评论数失败 - contentType: {}, contentId: {}", contentType, contentId, e);
            return Result.errorResult("COMMENT_COUNT_ERROR", "统计内容评论数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getUserCommentCount(Long userId) {
        log.info("开始统计用户评论总数 - userId: {}", userId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            int count = commentMapper.countByUserId(userId);
            log.info("统计用户评论总数成功 - userId: {}, count: {}", userId, count);
            return Result.success("统计用户评论总数成功", count);

        } catch (Exception e) {
            log.error("统计用户评论总数失败 - userId: {}", userId, e);
            return Result.errorResult("USER_COMMENT_COUNT_ERROR", "统计用户评论总数失败: " + e.getMessage());
        }
    }

    // ==================== 数据转换方法 ====================

    /**
     * 将实体对象转换为DTO
     */
    private CommentDTO convertToCommentDTO(Comment comment) {
        if (comment == null) {
            return null;
        }

        CommentDTO dto = new CommentDTO();
        dto.setId(comment.getId());
        dto.setContentType(comment.getContentType());
        dto.setContentId(comment.getContentId());
        dto.setRelatedKnowledgeTypeId(comment.getRelatedKnowledgeTypeId());
        dto.setUserId(comment.getUserId());
        dto.setParentId(comment.getParentCommentId());
        dto.setContent(comment.getCommentText());
        dto.setStatus(comment.getStatus());
        dto.setLikeCount(comment.getLikeCount());
        dto.setCreatedAt(comment.getCreatedAt());
        dto.setUpdatedAt(comment.getUpdatedAt());

        return dto;
    }

    // ==================== 其他接口方法的简单实现 ====================
    // 注：以下方法为接口完整性提供基础实现，实际项目中需要根据具体需求完善

    @Override
    public Result<PageResult<CommentDTO>> getCommentReplies(Long parentCommentId, PageRequest pageRequest) {
        return Result.success("获取评论回复成功", PageResult.of(Collections.emptyList(), 0L, 0, 10));
    }

    @Override
    public Result<PageResult<CommentDTO>> getCommentTree(Integer contentType, Long contentId, Integer maxDepth, PageRequest pageRequest) {
        return Result.success("获取评论树成功", PageResult.of(Collections.emptyList(), 0L, 0, 10));
    }

    @Override
    public Result<List<CommentDTO>> getPopularComments(Integer contentType, Long contentId, Integer limit) {
        return Result.success("获取热门评论成功", Collections.emptyList());
    }

    @Override
    public Result<PageResult<CommentDTO>> searchComments(String keyword, Integer contentType, Long userId, PageRequest pageRequest) {
        return Result.success("搜索评论成功", PageResult.of(Collections.emptyList(), 0L, 0, 10));
    }

    @Override
    public Result<List<Object>> getCommentCountBatch(Integer contentType, List<Long> contentIds) {
        return Result.success("批量统计评论数成功", Collections.emptyList());
    }

    @Override
    public Result<Integer> getCommentReplyCount(Long commentId) {
        return Result.success("统计评论回复数成功", 0);
    }

    @Override
    public Result<List<Object>> getCommentStatusStats(Integer contentType, Long contentId) {
        return Result.success("统计评论状态成功", Collections.emptyList());
    }

    @Override
    public Result<Void> incrementCommentLike(Long commentId) {
        return Result.success("增加评论点赞数成功", null);
    }

    @Override
    public Result<Void> decrementCommentLike(Long commentId) {
        return Result.success("减少评论点赞数成功", null);
    }

    @Override
    public Result<Void> updateCommentLikeCount(Long commentId, Integer likeCount) {
        return Result.success("更新评论点赞数成功", null);
    }

    @Override
    public Result<Void> updateCommentStatus(Long commentId, Integer status) {
        return Result.success("更新评论状态成功", null);
    }

    @Override
    public Result<Void> batchUpdateCommentStatus(List<Long> commentIds, Integer status) {
        return Result.success("批量更新评论状态成功", null);
    }

    @Override
    public Result<CommentDTO> restoreComment(Long commentId) {
        return Result.success("恢复评论成功", new CommentDTO());
    }

    @Override
    public Result<Void> reviewComment(Long commentId, Boolean approved, String reason) {
        return Result.success("审核评论成功", null);
    }

    @Override
    public Result<List<CommentDTO>> batchCreateComments(Long userId, List<CommentDTO> comments) {
        return Result.success("批量发布评论成功", Collections.emptyList());
    }

    @Override
    public Result<Void> batchDeleteComments(Long userId, List<Long> commentIds) {
        return Result.success("批量删除评论成功", null);
    }

    @Override
    public Result<Integer> cleanupDeletedComments(Integer days) {
        return Result.success("清理已删除评论成功", 0);
    }

    @Override
    public Result<Object> getUserCommentTrend(Long userId, Integer days) {
        return Result.success("获取用户评论趋势成功", new Object());
    }

    @Override
    public Result<Object> getContentCommentTrend(Integer contentType, Long contentId, Integer days) {
        return Result.success("获取内容评论趋势成功", new Object());
    }

    @Override
    public Result<Object> getUserCommentActivity(Long userId, Integer days) {
        return Result.success("获取用户评论活跃度成功", new Object());
    }

    @Override
    public Result<Object> getCommentQualityAnalysis(Integer contentType, Long contentId) {
        return Result.success("获取评论质量分析成功", new Object());
    }

    @Override
    public Result<Object> exportUserComments(Long userId, Integer contentType, String format) {
        return Result.success("导出用户评论成功", new Object());
    }

    @Override
    public Result<Object> exportContentComments(Integer contentType, Long contentId, String format) {
        return Result.success("导出内容评论成功", new Object());
    }

    @Override
    public Result<PageResult<CommentDTO>> getPendingComments(PageRequest pageRequest, Integer contentType) {
        return Result.success("获取待审核评论成功", PageResult.of(Collections.emptyList(), 0L, 0, 10));
    }

    @Override
    public Result<List<CommentDTO>> getRecentComments(Long userId, Integer limit) {
        return Result.success("获取最近评论成功", Collections.emptyList());
    }
}
