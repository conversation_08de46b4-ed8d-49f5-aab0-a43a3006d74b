package com.jdl.aic.core.service.service;

import com.jdl.aic.core.service.dao.entity.portal.TeamRecommendation;

import java.util.List;
import java.util.Map;

/**
 * 团队推荐内容服务接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface TeamRecommendationService {

    /**
     * 根据ID查询团队推荐
     */
    TeamRecommendation getTeamRecommendationById(Long id);

    /**
     * 根据团队ID查询推荐内容
     */
    List<TeamRecommendation> getTeamRecommendationsByTeamId(Long teamId);

    /**
     * 根据用户ID查询推荐记录
     */
    List<TeamRecommendation> getTeamRecommendationsByUserId(Long userId);

    /**
     * 根据内容ID和类型查询推荐记录
     */
    List<TeamRecommendation> getTeamRecommendationsByContentIdAndType(Long contentId, String contentType);

    /**
     * 根据团队ID和内容类型查询推荐
     */
    List<TeamRecommendation> getTeamRecommendationsByTeamIdAndContentType(Long teamId, String contentType);

    /**
     * 根据状态查询推荐记录
     */
    List<TeamRecommendation> getTeamRecommendationsByStatus(String status);

    /**
     * 查询所有推荐记录
     */
    List<TeamRecommendation> getAllTeamRecommendations();

    /**
     * 查询活跃的推荐记录
     */
    List<TeamRecommendation> getActiveTeamRecommendations();

    /**
     * 创建团队推荐
     */
    boolean createTeamRecommendation(TeamRecommendation teamRecommendation);

    /**
     * 推荐内容到团队
     */
    boolean recommendContentToTeam(Long teamId, Long userId, Long contentId, String contentType, String reason);

    /**
     * 更新团队推荐
     */
    boolean updateTeamRecommendation(TeamRecommendation teamRecommendation);

    /**
     * 删除团队推荐（物理删除）
     */
    boolean deleteTeamRecommendation(Long id);

    /**
     * 软删除团队推荐
     */
    boolean softDeleteTeamRecommendation(Long id);

    /**
     * 更新推荐状态
     */
    boolean updateRecommendationStatus(Long id, String status);

    /**
     * 激活推荐
     */
    boolean activateRecommendation(Long id);

    /**
     * 停用推荐
     */
    boolean deactivateRecommendation(Long id);

    /**
     * 检查推荐记录是否存在
     */
    boolean isRecommendationExists(Long teamId, Long contentId, String contentType);

    /**
     * 统计推荐记录数量
     */
    int getTeamRecommendationCount();

    /**
     * 统计活跃推荐记录数量
     */
    int getActiveTeamRecommendationCount();

    /**
     * 统计团队的推荐数量
     */
    int getTeamRecommendationCountByTeamId(Long teamId);

    /**
     * 统计用户的推荐数量
     */
    int getTeamRecommendationCountByUserId(Long userId);

    /**
     * 统计内容的推荐数量
     */
    int getTeamRecommendationCountByContentIdAndType(Long contentId, String contentType);

    /**
     * 根据条件查询推荐记录
     */
    List<TeamRecommendation> getTeamRecommendationsByCondition(Long teamId, Long userId, String contentType, String status);

    /**
     * 批量创建团队推荐
     */
    boolean batchCreateTeamRecommendations(List<TeamRecommendation> teamRecommendations);

    /**
     * 批量更新推荐状态
     */
    boolean batchUpdateRecommendationStatus(List<Long> ids, String status);

    /**
     * 批量软删除推荐记录
     */
    boolean batchSoftDeleteTeamRecommendations(List<Long> ids);

    /**
     * 获取团队推荐统计信息
     */
    Map<String, Object> getTeamRecommendationStatistics(Long teamId);

    /**
     * 获取热门推荐内容类型
     */
    List<String> getPopularContentTypes();

    /**
     * 获取用户最近的推荐记录
     */
    List<TeamRecommendation> getUserRecentRecommendations(Long userId, Integer limit);

    /**
     * 获取团队最近的推荐记录
     */
    List<TeamRecommendation> getTeamRecentRecommendations(Long teamId, Integer limit);
}
