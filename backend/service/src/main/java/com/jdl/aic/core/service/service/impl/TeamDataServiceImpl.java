package com.jdl.aic.core.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.team.TeamDTO;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamListRequest;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamTreeRequest;
import com.jdl.aic.core.service.client.dto.request.team.ToggleTeamStatusRequest;
import com.jdl.aic.core.service.client.dto.request.team.MoveTeamToParentRequest;
import com.jdl.aic.core.service.dao.entity.portal.Team;
import com.jdl.aic.core.service.dao.mapper.portal.TeamMapper;

import com.jdl.aic.core.service.dao.mapper.portal.TeamRecommendationMapper;
import com.jdl.aic.core.service.dao.mapper.portal.UserTeamMapper;
import com.jdl.aic.core.service.portal.client.TeamDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import com.jdl.aic.core.service.client.dto.team.TeamStatisticsDTO;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队管理服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("teamService")
public class TeamDataServiceImpl implements TeamDataService {

    @Resource
    private TeamMapper teamMapper;
    @Autowired
    private UserTeamMapper userTeamMapper;
    @Autowired
    private TeamRecommendationMapper teamRecommendationMapper;

    @Override
    public Result<PageResult<TeamDTO>> getTeamList(PageRequest pageRequest, GetTeamListRequest request) {
        log.info("开始获取团队列表 - 分页参数: {}, 查询条件: {}", pageRequest, request);
        
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            Team queryCondition = new Team();
            if (request != null) {
                queryCondition.setParentId(request.getParentId());
                queryCondition.setIsActive(request.getIsActive());
            }

            // 查询团队列表
            List<Team> teams;
            if (request != null && StringUtils.hasText(request.getSearch())) {
                log.debug("使用搜索模式查询团队 - search: {}", request.getSearch());
                teams = teamMapper.searchTeams(request.getSearch(), request.getIsActive());
            } else {
                log.debug("使用条件查询团队");
                teams = teamMapper.selectByCondition(queryCondition);
            }

            // 如果有标签过滤条件，进行标签过滤
            if (request != null && request.getTags() != null && !request.getTags().isEmpty()) {
                log.debug("应用标签过滤 - tags: {}", request.getTags());
                teams = teams.stream()
                        .filter(team -> hasAnyTag(team, request.getTags()))
                        .collect(Collectors.toList());
            }
            log.info("数据库查询完成，查询到 {} 条团队记录", teams.size());

            // 使用PageInfo获取分页信息
            PageInfo<Team> pageInfo = new PageInfo<>(teams);

            // 转换为DTO
            List<TeamDTO> teamDTOs = teams.stream()
                    .map(this::convertToTeamDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<TeamDTO> pageResult = PageResult.of(
                teamDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                pageInfo.getPageSize()
            );

            log.info("团队列表查询成功 - 总记录数: {}, 当前页: {}, 每页大小: {}",
                pageResult.getPagination().getTotalElements(),
                pageResult.getPagination().getCurrentPage(),
                pageResult.getPagination().getPageSize());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取团队列表失败", e);
            return Result.errorResult("TEAM_LIST_QUERY_ERROR", "获取团队列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamDTO>> getTeamTree(GetTeamTreeRequest request) {
        log.info("开始获取团队树形结构 - 查询条件: {}", request);
        
        try {
            Boolean isActive = request != null ? request.getIsActive() : null;
            
            // 查询根团队
            List<Team> rootTeams = teamMapper.selectRootTeams(isActive);
            log.info("查询到 {} 个根团队", rootTeams.size());

            // 构建团队树
            List<TeamDTO> teamTree = rootTeams.stream()
                    .map(this::convertToTeamDTO)
                    .map(dto -> {
                        log.debug("构建团队树节点 - id: {}, name: {}", dto.getId(), dto.getName());
                        return buildTeamTree(dto, isActive);
                    })
                    .collect(Collectors.toList());

            log.info("团队树构建成功 - 根节点数量: {}", teamTree.size());
            return Result.success(teamTree);

        } catch (Exception e) {
            log.error("获取团队树形结构失败", e);
            return Result.errorResult("TEAM_TREE_QUERY_ERROR", "获取团队树形结构失败: " + e.getMessage());
        }
    }

    @Override
    public Result<TeamDTO> getTeamById(Long id) {
        log.info("开始根据ID获取团队详情 - id: {}", id);
        
        try {
            if (id == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            Team team = teamMapper.selectById(id);
            if (team == null) {
                log.warn("团队不存在 - id: {}", id);
                return Result.errorResult("TEAM_NOT_FOUND", "团队不存在");
            }

            TeamDTO teamDTO = convertToTeamDTO(team);
            log.info("团队详情查询成功 - id: {}, name: {}", teamDTO.getId(), teamDTO.getName());
            return Result.success(teamDTO);

        } catch (Exception e) {
            log.error("根据ID获取团队详情失败 - id: {}", id, e);
            return Result.errorResult("TEAM_QUERY_ERROR", "获取团队详情失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<TeamDTO> createTeam(TeamDTO team) {
        log.info("开始创建团队 - 入参: name={}, parentId={}",
            team != null ? team.getName() : null,
            team != null ? team.getParentId() : null);
        
        try {
            if (team == null) {
                log.warn("参数校验失败 - 团队信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队信息不能为空");
            }

            // 验证必填字段
            if (!StringUtils.hasText(team.getName())) {
                log.warn("参数校验失败 - 团队名称不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队名称不能为空");
            }

            // 检查同级别下是否存在同名团队
            Team existingTeam = teamMapper.selectByNameAndParent(team.getName(), team.getParentId());
            if (existingTeam != null) {
                log.warn("团队名称已存在 - name: {}, parentId: {}", team.getName(), team.getParentId());
                return Result.errorResult("TEAM_NAME_EXISTS", "同级别下已存在相同名称的团队");
            }

            // 如果指定了父团队，验证父团队是否存在且活跃
            if (team.getParentId() != null) {
                Team parentTeam = teamMapper.selectById(team.getParentId());
                if (parentTeam == null) {
                    log.warn("父团队不存在 - parentId: {}", team.getParentId());
                    return Result.errorResult("PARENT_TEAM_NOT_FOUND", "父团队不存在");
                }
                if (!Boolean.TRUE.equals(parentTeam.getIsActive())) {
                    log.warn("父团队未启用 - parentId: {}", team.getParentId());
                    return Result.errorResult("PARENT_TEAM_INACTIVE", "父团队未启用");
                }
            }

            // 转换为实体对象
            Team entity = convertToTeamEntity(team);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            
            // 设置默认值
            if (entity.getIsActive() == null) {
                entity.setIsActive(true);
            }

            // 插入数据库
            int result = teamMapper.insert(entity);
            if (result > 0) {
                log.info("团队创建成功 - id: {}, name: {}, parentId: {}",
                    entity.getId(), entity.getName(), entity.getParentId());
                TeamDTO resultDto = convertToTeamDTO(entity);
                return Result.success("团队创建成功", resultDto);
            } else {
                log.error("团队创建失败 - 数据库插入返回结果: {}, name: {}", result, entity.getName());
                return Result.errorResult("TEAM_CREATE_FAILED", "团队创建失败");
            }

        } catch (Exception e) {
            log.error("创建团队失败", e);
            return Result.errorResult("TEAM_CREATE_ERROR", "创建团队失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<TeamDTO> updateTeam(Long id, TeamDTO team) {
        log.info("开始更新团队 - id: {}, 入参: name={}", id, team != null ? team.getName() : null);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }
            if (team == null) {
                log.warn("参数校验失败 - 团队信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队信息不能为空");
            }

            // 检查团队是否存在
            Team existingTeam = teamMapper.selectById(id);
            if (existingTeam == null) {
                log.warn("团队不存在 - id: {}", id);
                return Result.errorResult("TEAM_NOT_FOUND", "团队不存在");
            }

            // 验证必填字段
            if (!StringUtils.hasText(team.getName())) {
                log.warn("参数校验失败 - 团队名称不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队名称不能为空");
            }

            // 检查同级别下是否存在同名团队（排除自己）
            Team duplicateTeam = teamMapper.selectByNameAndParent(team.getName(), team.getParentId());
            if (duplicateTeam != null && !duplicateTeam.getId().equals(id)) {
                log.warn("团队名称已存在 - name: {}, parentId: {}", team.getName(), team.getParentId());
                return Result.errorResult("TEAM_NAME_EXISTS", "同级别下已存在相同名称的团队");
            }

            // 如果指定了父团队，验证父团队是否存在且活跃
            if (team.getParentId() != null) {
                Team parentTeam = teamMapper.selectById(team.getParentId());
                if (parentTeam == null) {
                    log.warn("父团队不存在 - parentId: {}", team.getParentId());
                    return Result.errorResult("PARENT_TEAM_NOT_FOUND", "父团队不存在");
                }
                if (!Boolean.TRUE.equals(parentTeam.getIsActive())) {
                    log.warn("父团队未启用 - parentId: {}", team.getParentId());
                    return Result.errorResult("PARENT_TEAM_INACTIVE", "父团队未启用");
                }

                // 防止循环引用：不能将团队移动到自己的子团队下
                if (isDescendant(id, team.getParentId())) {
                    log.warn("不能将团队移动到自己的子团队下 - teamId: {}, parentId: {}", id, team.getParentId());
                    return Result.errorResult("CIRCULAR_REFERENCE", "不能将团队移动到自己的子团队下");
                }
            }

            // 更新团队信息
            Team updateEntity = new Team();
            updateEntity.setId(id);
            updateEntity.setName(team.getName());
            updateEntity.setDescription(team.getDescription());
            updateEntity.setParentId(team.getParentId());
            updateEntity.setIsActive(team.getIsActive());
            updateEntity.setUpdatedAt(LocalDateTime.now());
            updateEntity.setUpdatedBy(team.getUpdatedBy());

            // 处理tags字段的JSON转换
            if (team.getTags() != null) {
                try {
                    updateEntity.setTagsJson(JSON.toJSONString(team.getTags()));
                } catch (Exception e) {
                    log.warn("转换团队标签为JSON失败 - tags: {}", team.getTags(), e);
                }
            }

            int result = teamMapper.updateById(updateEntity);
            if (result > 0) {
                // 查询更新后的团队信息
                Team updatedTeam = teamMapper.selectById(id);
                TeamDTO resultDto = convertToTeamDTO(updatedTeam);
                log.info("团队更新成功 - id: {}, name: {}", id, resultDto.getName());
                return Result.success("团队更新成功", resultDto);
            } else {
                log.error("团队更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("TEAM_UPDATE_FAILED", "团队更新失败");
            }

        } catch (Exception e) {
            log.error("更新团队失败 - id: {}", id, e);
            return Result.errorResult("TEAM_UPDATE_ERROR", "更新团队失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteTeam(Long id) {
        log.info("开始删除团队 - id: {}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            // 检查团队是否存在
            Team existingTeam = teamMapper.selectById(id);
            if (existingTeam == null) {
                log.warn("团队不存在 - id: {}", id);
                return Result.errorResult("TEAM_NOT_FOUND", "团队不存在");
            }

            // 检查是否有子团队
            int childCount = teamMapper.countByParentId(id);
            if (childCount > 0) {
                log.warn("团队下存在子团队，无法删除 - id: {}, 子团队数量: {}", id, childCount);
                return Result.errorResult("TEAM_HAS_CHILDREN", "该团队下还有子团队，无法删除");
            }

            // 删除团队
            int result = teamMapper.deleteById(id);
            if (result > 0) {
                log.info("团队删除成功 - id: {}, name: {}", id, existingTeam.getName());
                return Result.success();
            } else {
                log.error("团队删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("TEAM_DELETE_FAILED", "团队删除失败");
            }

        } catch (Exception e) {
            log.error("删除团队失败 - id: {}", id, e);
            return Result.errorResult("TEAM_DELETE_ERROR", "删除团队失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> toggleTeamStatus(ToggleTeamStatusRequest request) {
        log.info("开始切换团队状态 - 入参: {}", request);

        try {
            if (request == null || request.getTeamId() == null || request.getIsActive() == null) {
                log.warn("参数校验失败 - 请求参数不完整");
                return Result.errorResult("INVALID_PARAMETER", "请求参数不完整");
            }

            // 检查团队是否存在
            Team existingTeam = teamMapper.selectById(request.getTeamId());
            if (existingTeam == null) {
                log.warn("团队不存在 - id: {}", request.getTeamId());
                return Result.errorResult("TEAM_NOT_FOUND", "团队不存在");
            }

            // 更新团队状态
            int result = teamMapper.updateStatus(request.getTeamId(), request.getIsActive());
            if (result > 0) {
                log.info("团队状态切换成功 - id: {}, isActive: {}", request.getTeamId(), request.getIsActive());
                return Result.success();
            } else {
                log.error("团队状态切换失败 - 数据库更新返回结果: {}, id: {}", result, request.getTeamId());
                return Result.errorResult("TEAM_STATUS_UPDATE_FAILED", "团队状态切换失败");
            }

        } catch (Exception e) {
            log.error("切换团队状态失败", e);
            return Result.errorResult("TEAM_STATUS_UPDATE_ERROR", "切换团队状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> moveTeamToParent(MoveTeamToParentRequest request) {
        log.info("开始移动团队到新父团队 - 入参: {}", request);

        try {
            if (request == null || request.getTeamId() == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            // 检查团队是否存在
            Team existingTeam = teamMapper.selectById(request.getTeamId());
            if (existingTeam == null) {
                log.warn("团队不存在 - id: {}", request.getTeamId());
                return Result.errorResult("TEAM_NOT_FOUND", "团队不存在");
            }

            // 如果指定了新父团队，验证父团队是否存在且活跃
            if (request.getNewParentId() != null) {
                Team parentTeam = teamMapper.selectById(request.getNewParentId());
                if (parentTeam == null) {
                    log.warn("父团队不存在 - parentId: {}", request.getNewParentId());
                    return Result.errorResult("PARENT_TEAM_NOT_FOUND", "父团队不存在");
                }
                if (!Boolean.TRUE.equals(parentTeam.getIsActive())) {
                    log.warn("父团队未启用 - parentId: {}", request.getNewParentId());
                    return Result.errorResult("PARENT_TEAM_INACTIVE", "父团队未启用");
                }

                // 防止循环引用：不能将团队移动到自己的子团队下
                if (isDescendant(request.getTeamId(), request.getNewParentId())) {
                    log.warn("不能将团队移动到自己的子团队下 - teamId: {}, parentId: {}",
                        request.getTeamId(), request.getNewParentId());
                    return Result.errorResult("CIRCULAR_REFERENCE", "不能将团队移动到自己的子团队下");
                }
            }

            // 移动团队
            int result = teamMapper.updateParent(request.getTeamId(), request.getNewParentId());
            if (result > 0) {
                log.info("团队移动成功 - id: {}, newParentId: {}", request.getTeamId(), request.getNewParentId());
                return Result.success();
            } else {
                log.error("团队移动失败 - 数据库更新返回结果: {}, id: {}", result, request.getTeamId());
                return Result.errorResult("TEAM_MOVE_FAILED", "团队移动失败");
            }

        } catch (Exception e) {
            log.error("移动团队失败", e);
            return Result.errorResult("TEAM_MOVE_ERROR", "移动团队失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamDTO>> getActiveTeams() {
        log.info("开始获取所有活跃团队列表");

        try {
            List<Team> teams = teamMapper.selectActiveTeams();
            List<TeamDTO> teamDTOs = teams.stream()
                    .map(this::convertToTeamDTO)
                    .collect(Collectors.toList());

            log.info("获取活跃团队列表成功 - 数量: {}", teamDTOs.size());
            return Result.success(teamDTOs);

        } catch (Exception e) {
            log.error("获取活跃团队列表失败", e);
            return Result.errorResult("ACTIVE_TEAMS_QUERY_ERROR", "获取活跃团队列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamDTO>> searchTeamsByName(String name) {
        log.info("开始根据名称搜索团队 - name: {}", name);

        try {
            if (!StringUtils.hasText(name)) {
                log.warn("参数校验失败 - 搜索关键词不能为空");
                return Result.errorResult("INVALID_PARAMETER", "搜索关键词不能为空");
            }

            List<Team> teams = teamMapper.selectByNameLike(name);
            List<TeamDTO> teamDTOs = teams.stream()
                    .map(this::convertToTeamDTO)
                    .collect(Collectors.toList());

            log.info("根据名称搜索团队成功 - 关键词: {}, 结果数量: {}", name, teamDTOs.size());
            return Result.success(teamDTOs);

        } catch (Exception e) {
            log.error("根据名称搜索团队失败 - name: {}", name, e);
            return Result.errorResult("SEARCH_TEAMS_ERROR", "搜索团队失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamDTO>> getRootTeams(Boolean isActive) {
        log.info("开始获取根团队列表 - isActive: {}", isActive);

        try {
            List<Team> teams = teamMapper.selectRootTeams(isActive);
            List<TeamDTO> teamDTOs = teams.stream()
                    .map(this::convertToTeamDTO)
                    .collect(Collectors.toList());

            log.info("获取根团队列表成功 - 数量: {}", teamDTOs.size());
            return Result.success(teamDTOs);

        } catch (Exception e) {
            log.error("获取根团队列表失败", e);
            return Result.errorResult("ROOT_TEAMS_QUERY_ERROR", "获取根团队列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamDTO>> getChildTeams(Long parentId, Boolean isActive) {
        log.info("开始获取子团队列表 - parentId: {}, isActive: {}", parentId, isActive);

        try {
            if (parentId == null) {
                log.warn("参数校验失败 - 父团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "父团队ID不能为空");
            }

            List<Team> teams = teamMapper.selectByParentId(parentId);
            if (isActive != null) {
                teams = teams.stream()
                        .filter(team -> isActive.equals(team.getIsActive()))
                        .collect(Collectors.toList());
            }

            List<TeamDTO> teamDTOs = teams.stream()
                    .map(this::convertToTeamDTO)
                    .collect(Collectors.toList());

            log.info("获取子团队列表成功 - parentId: {}, 数量: {}", parentId, teamDTOs.size());
            return Result.success(teamDTOs);

        } catch (Exception e) {
            log.error("获取子团队列表失败 - parentId: {}", parentId, e);
            return Result.errorResult("CHILD_TEAMS_QUERY_ERROR", "获取子团队列表失败: " + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 构建团队树
     *
     * @param teamDTO 团队DTO
     * @param isActive 是否只包含活跃团队
     * @return 包含子团队的团队DTO
     */
    private TeamDTO buildTeamTree(TeamDTO teamDTO, Boolean isActive) {
        try {
            List<Team> childTeams = teamMapper.selectByParentId(teamDTO.getId());
            if (isActive != null) {
                childTeams = childTeams.stream()
                        .filter(team -> isActive.equals(team.getIsActive()))
                        .collect(Collectors.toList());
            }

            if (!childTeams.isEmpty()) {
                List<TeamDTO> childDTOs = childTeams.stream()
                        .map(this::convertToTeamDTO)
                        .map(dto -> buildTeamTree(dto, isActive))
                        .collect(Collectors.toList());
                teamDTO.setChildren(childDTOs);
            }

            return teamDTO;
        } catch (Exception e) {
            log.error("构建团队树失败 - teamId: {}", teamDTO.getId(), e);
            return teamDTO;
        }
    }

    /**
     * 检查是否为子孙团队（防止循环引用）
     *
     * @param ancestorId 祖先团队ID
     * @param descendantId 后代团队ID
     * @return 是否为子孙关系
     */
    private boolean isDescendant(Long ancestorId, Long descendantId) {
        if (ancestorId == null || descendantId == null || ancestorId.equals(descendantId)) {
            return false;
        }

        try {
            Team descendant = teamMapper.selectById(descendantId);
            while (descendant != null && descendant.getParentId() != null) {
                if (ancestorId.equals(descendant.getParentId())) {
                    return true;
                }
                descendant = teamMapper.selectById(descendant.getParentId());
            }
            return false;
        } catch (Exception e) {
            log.error("检查团队层级关系失败 - ancestorId: {}, descendantId: {}", ancestorId, descendantId, e);
            return false;
        }
    }

    /**
     * 将Team实体转换为TeamDTO
     *
     * @param team Team实体
     * @return TeamDTO
     */
    private TeamDTO convertToTeamDTO(Team team) {
        if (team == null) {
            return null;
        }

        TeamDTO dto = new TeamDTO();
        BeanUtils.copyProperties(team, dto);

        // 处理tags字段的JSON转换
        if (team.getTagsJson() != null && !team.getTagsJson().trim().isEmpty()) {
            try {
                dto.setTags(JSON.parseArray(team.getTagsJson(), String.class));
            } catch (Exception e) {
                log.warn("解析团队标签JSON失败 - teamId: {}, tagsJson: {}", team.getId(), team.getTagsJson(), e);
            }
        }

        // 新字段 avatarUrl, privacy, inviteSetting 会通过 BeanUtils.copyProperties 自动复制

        return dto;
    }

    /**
     * 将TeamDTO转换为Team实体
     *
     * @param teamDTO TeamDTO
     * @return Team实体
     */
    private Team convertToTeamEntity(TeamDTO teamDTO) {
        if (teamDTO == null) {
            return null;
        }

        Team entity = new Team();
        BeanUtils.copyProperties(teamDTO, entity, "tags"); // 排除tags字段，单独处理

        // 处理tags字段的JSON转换
        if (teamDTO.getTags() != null && !teamDTO.getTags().isEmpty()) {
            try {
                entity.setTagsJson(JSON.toJSONString(teamDTO.getTags()));
            } catch (Exception e) {
                log.warn("转换团队标签为JSON失败 - tags: {}", teamDTO.getTags(), e);
            }
        }

        // 新字段 avatarUrl, privacy, inviteSetting 会通过 BeanUtils.copyProperties 自动复制

        return entity;
    }

    /**
     * 检查团队是否包含任一指定标签
     *
     * @param team 团队实体
     * @param targetTags 目标标签列表
     * @return 是否包含任一标签
     */
    private boolean hasAnyTag(Team team, List<String> targetTags) {
        if (team.getTagsJson() == null || team.getTagsJson().trim().isEmpty() ||
            targetTags == null || targetTags.isEmpty()) {
            return false;
        }

        try {
            List<String> teamTags = JSON.parseArray(team.getTagsJson(), String.class);
            if (teamTags == null || teamTags.isEmpty()) {
                return false;
            }

            // 检查是否有任一标签匹配
            return teamTags.stream().anyMatch(targetTags::contains);
        } catch (Exception e) {
            log.warn("解析团队标签JSON失败 - teamId: {}, tagsJson: {}", team.getId(), team.getTagsJson(), e);
            return false;
        }
    }

    @Override
    public Result<TeamStatisticsDTO> getTeamStatistics(Long teamId) {
        log.info("开始获取团队统计信息 - teamId: {}", teamId);
        
        try {
            if (teamId == null) {
                log.warn("参数校验失败 - 团队ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "团队ID不能为空");
            }

            // 检查团队是否存在
            Team team = teamMapper.selectById(teamId);
            if (team == null) {
                log.warn("团队不存在 - id: {}", teamId);
                return Result.errorResult("TEAM_NOT_FOUND", "团队不存在");
            }

            // 获取团队人数
            int memberCount = userTeamMapper.countMembersByTeamId(teamId);
            
            // 获取团队文章总数
            int articleCount = teamRecommendationMapper.countByTeamId(teamId);
            
            // 构建统计信息DTO
            TeamStatisticsDTO statisticsDTO = new TeamStatisticsDTO();
            statisticsDTO.setTeamId(teamId);
            statisticsDTO.setMemberCount(memberCount);
            statisticsDTO.setArticleCount(articleCount);
            
            log.info("获取团队统计信息成功 - teamId: {}, 团队人数: {}, 团队文章总数: {}",
                    teamId, memberCount, articleCount);
            return Result.success(statisticsDTO);
            
        } catch (Exception e) {
            log.error("获取团队统计信息失败 - teamId: {}", teamId, e);
            return Result.errorResult("TEAM_STATISTICS_QUERY_ERROR", "获取团队统计信息失败: " + e.getMessage());
        }
    }

}
