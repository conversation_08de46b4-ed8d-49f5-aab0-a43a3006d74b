package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.ContentTypeConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;
import com.jdl.aic.core.service.client.service.ContentTypeConfigService;
import com.jdl.aic.core.service.dao.entity.primary.ContentTypeConfig;
import com.jdl.aic.core.service.dao.mapper.primary.ContentTypeConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 内容类型配置管理服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("contentTypeConfigService")
public class ContentTypeConfigServiceImpl implements ContentTypeConfigService {

    @Autowired
    private ContentTypeConfigMapper contentTypeConfigMapper;

    @Override
    public Result<PageResult<ContentTypeConfigDTO>> getConfigList(
            PageRequest pageRequest, GetContentTypeConfigListRequest request) {
        log.info("开始获取内容类型配置列表 - 入参: pageRequest={}, request={}",
                pageRequest, request);
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            ContentTypeConfig queryCondition = new ContentTypeConfig();
            if (request != null) {
                if (StringUtils.hasText(request.getCode())) {
                    queryCondition.setCode(request.getCode());
                }
                if (StringUtils.hasText(request.getName())) {
                    queryCondition.setName(request.getName());
                }
                if (request.getIsPortalModule() != null) {
                    queryCondition.setIsPortalModule(request.getIsPortalModule());
                }
                if (request.getIsActive() != null) {
                    queryCondition.setIsActive(request.getIsActive());
                }
            }

            // 查询配置列表
            List<ContentTypeConfig> configs = contentTypeConfigMapper.selectByCondition(queryCondition);
            PageInfo<ContentTypeConfig> pageInfo = new PageInfo<>(configs);

            // 转换为DTO
            List<ContentTypeConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            PageResult<ContentTypeConfigDTO> pageResult = PageResult.of(configDTOs, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());

            log.info("获取内容类型配置列表成功 - 总数: {}, 当前页: {}", pageInfo.getTotal(), pageInfo.getPageNum());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取内容类型配置列表失败", e);
            return Result.errorResult("CONFIG_LIST_QUERY_ERROR", "获取内容类型配置列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ContentTypeConfigDTO> getConfigById(Long id) {
        log.info("开始根据ID获取内容类型配置 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 配置ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID不能为空");
            }

            ContentTypeConfig config = contentTypeConfigMapper.selectById(id);
            if (config == null) {
                log.warn("内容类型配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "内容类型配置不存在");
            }

            ContentTypeConfigDTO configDTO = convertToDTO(config);
            log.info("根据ID获取内容类型配置成功 - id: {}, code: {}", id, config.getCode());
            return Result.success(configDTO);

        } catch (Exception e) {
            log.error("根据ID获取内容类型配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_QUERY_ERROR", "获取内容类型配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ContentTypeConfigDTO> getConfigByCode(String code) {
        log.info("开始根据编码获取内容类型配置 - 入参: code={}", code);
        try {
            if (!StringUtils.hasText(code)) {
                log.warn("参数校验失败 - 内容类型编码不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型编码不能为空");
            }

            ContentTypeConfig config = contentTypeConfigMapper.selectByCode(code);
            if (config == null) {
                log.warn("内容类型配置不存在 - code: {}", code);
                return Result.errorResult("CONFIG_NOT_FOUND", "内容类型配置不存在");
            }

            ContentTypeConfigDTO configDTO = convertToDTO(config);
            log.info("根据编码获取内容类型配置成功 - code: {}, id: {}", code, config.getId());
            return Result.success(configDTO);

        } catch (Exception e) {
            log.error("根据编码获取内容类型配置失败 - code: {}", code, e);
            return Result.errorResult("CONFIG_QUERY_ERROR", "获取内容类型配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ContentTypeConfigDTO> createConfig(ContentTypeConfigDTO config) {
        log.info("开始创建内容类型配置 - 入参: config={}", config);
        try {
            if (config == null) {
                log.warn("参数校验失败 - 配置信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置信息不能为空");
            }

            // 检查编码是否已存在
            if (StringUtils.hasText(config.getCode())) {
                ContentTypeConfig existingConfig = contentTypeConfigMapper.selectByCode(config.getCode());
                if (existingConfig != null) {
                    log.warn("内容类型编码已存在 - code: {}", config.getCode());
                    return Result.errorResult("CODE_ALREADY_EXISTS", "内容类型编码已存在");
                }
            }

            // 检查表名是否已被使用
            if (StringUtils.hasText(config.getTableName())) {
                ContentTypeConfig existingConfig = contentTypeConfigMapper.selectByTableName(config.getTableName());
                if (existingConfig != null) {
                    log.warn("表名已被使用 - tableName: {}", config.getTableName());
                    return Result.errorResult("TABLE_NAME_ALREADY_EXISTS", "表名已被使用");
                }
            }

            // 转换为实体并设置默认值
            ContentTypeConfig entity = convertToEntity(config);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            if (entity.getIsActive() == null) {
                entity.setIsActive(true);
            }
            if (entity.getIsPortalModule() == null) {
                entity.setIsPortalModule(false);
            }
            if (entity.getSortOrder() == null) {
                entity.setSortOrder(0);
            }

            // 插入数据库
            int result = contentTypeConfigMapper.insert(entity);
            if (result > 0) {
                log.info("内容类型配置创建成功 - id: {}, code: {}", entity.getId(), entity.getCode());
                ContentTypeConfigDTO resultDto = convertToDTO(entity);
                return Result.success("内容类型配置创建成功", resultDto);
            } else {
                log.error("内容类型配置创建失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("CONFIG_CREATE_FAILED", "内容类型配置创建失败");
            }

        } catch (Exception e) {
            log.error("创建内容类型配置失败", e);
            return Result.errorResult("CONFIG_CREATE_ERROR", "创建内容类型配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ContentTypeConfigDTO> updateConfig(Long id, ContentTypeConfigDTO config) {
        log.info("开始更新内容类型配置 - 入参: id={}, config={}", id, config);
        try {
            if (id == null || config == null) {
                log.warn("参数校验失败 - 配置ID和配置信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID和配置信息不能为空");
            }

            // 检查配置是否存在
            ContentTypeConfig existingConfig = contentTypeConfigMapper.selectById(id);
            if (existingConfig == null) {
                log.warn("内容类型配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "内容类型配置不存在");
            }

            // 检查编码是否已存在（排除当前记录）
            if (StringUtils.hasText(config.getCode()) && !config.getCode().equals(existingConfig.getCode())) {
                ContentTypeConfig codeConfig = contentTypeConfigMapper.selectByCode(config.getCode());
                if (codeConfig != null) {
                    log.warn("内容类型编码已存在 - code: {}", config.getCode());
                    return Result.errorResult("CODE_ALREADY_EXISTS", "内容类型编码已存在");
                }
            }

            // 检查表名是否已被使用（排除当前记录）
            if (StringUtils.hasText(config.getTableName()) && !config.getTableName().equals(existingConfig.getTableName())) {
                ContentTypeConfig tableConfig = contentTypeConfigMapper.selectByTableName(config.getTableName());
                if (tableConfig != null) {
                    log.warn("表名已被使用 - tableName: {}", config.getTableName());
                    return Result.errorResult("TABLE_NAME_ALREADY_EXISTS", "表名已被使用");
                }
            }

            // 更新实体信息
            ContentTypeConfig entity = convertToEntity(config);
            entity.setId(id);
            entity.setUpdatedAt(LocalDateTime.now());
            entity.setCreatedAt(existingConfig.getCreatedAt()); // 保持创建时间不变

            int result = contentTypeConfigMapper.updateById(entity);
            if (result > 0) {
                log.info("内容类型配置更新成功 - id: {}, code: {}", id, entity.getCode());
                ContentTypeConfigDTO resultDto = convertToDTO(entity);
                return Result.success("内容类型配置更新成功", resultDto);
            } else {
                log.error("内容类型配置更新失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("CONFIG_UPDATE_FAILED", "内容类型配置更新失败");
            }

        } catch (Exception e) {
            log.error("更新内容类型配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_UPDATE_ERROR", "更新内容类型配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteConfig(Long id) {
        log.info("开始删除内容类型配置 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 配置ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID不能为空");
            }

            // 检查配置是否存在
            ContentTypeConfig existingConfig = contentTypeConfigMapper.selectById(id);
            if (existingConfig == null) {
                log.warn("内容类型配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "内容类型配置不存在");
            }

            int result = contentTypeConfigMapper.deleteById(id);
            if (result > 0) {
                log.info("内容类型配置删除成功 - id: {}, code: {}", id, existingConfig.getCode());
                return Result.success();
            } else {
                log.error("内容类型配置删除失败 - 数据库删除返回结果: {}", result);
                return Result.errorResult("CONFIG_DELETE_FAILED", "内容类型配置删除失败");
            }

        } catch (Exception e) {
            log.error("删除内容类型配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_DELETE_ERROR", "删除内容类型配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> toggleConfigStatus(ToggleContentTypeConfigStatusRequest request) {
        log.info("开始切换内容类型配置状态 - 入参: request={}", request);
        try {
            if (request == null || request.getId() == null || request.getIsActive() == null) {
                log.warn("参数校验失败 - 请求参数、配置ID和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、配置ID和状态不能为空");
            }

            // 检查配置是否存在
            ContentTypeConfig existingConfig = contentTypeConfigMapper.selectById(request.getId());
            if (existingConfig == null) {
                log.warn("内容类型配置不存在 - id: {}", request.getId());
                return Result.errorResult("CONFIG_NOT_FOUND", "内容类型配置不存在");
            }

            int result = contentTypeConfigMapper.updateStatus(request.getId(), request.getIsActive());
            if (result > 0) {
                log.info("内容类型配置状态切换成功 - id: {}, isActive: {}", request.getId(), request.getIsActive());
                return Result.success();
            } else {
                log.error("内容类型配置状态切换失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("CONFIG_STATUS_UPDATE_FAILED", "内容类型配置状态切换失败");
            }

        } catch (Exception e) {
            log.error("切换内容类型配置状态失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_STATUS_UPDATE_ERROR", "切换内容类型配置状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> updateConfigSortOrder(UpdateContentTypeConfigSortOrderRequest request) {
        log.info("开始更新内容类型配置排序 - 入参: request={}", request);
        try {
            if (request == null || request.getId() == null || request.getSortOrder() == null) {
                log.warn("参数校验失败 - 请求参数、配置ID和排序值不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、配置ID和排序值不能为空");
            }

            // 检查配置是否存在
            ContentTypeConfig existingConfig = contentTypeConfigMapper.selectById(request.getId());
            if (existingConfig == null) {
                log.warn("内容类型配置不存在 - id: {}", request.getId());
                return Result.errorResult("CONFIG_NOT_FOUND", "内容类型配置不存在");
            }

            int result = contentTypeConfigMapper.updateSortOrder(request.getId(), request.getSortOrder());
            if (result > 0) {
                log.info("内容类型配置排序更新成功 - id: {}, sortOrder: {}", request.getId(), request.getSortOrder());
                return Result.success();
            } else {
                log.error("内容类型配置排序更新失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("CONFIG_SORT_UPDATE_FAILED", "内容类型配置排序更新失败");
            }

        } catch (Exception e) {
            log.error("更新内容类型配置排序失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_SORT_UPDATE_ERROR", "更新内容类型配置排序失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ContentTypeConfigDTO>> getAllActiveConfigs() {
        log.info("开始获取所有启用的内容类型配置");
        try {
            List<ContentTypeConfig> configs = contentTypeConfigMapper.selectAllActive();
            List<ContentTypeConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("获取所有启用的内容类型配置成功 - 数量: {}", configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("获取所有启用的内容类型配置失败", e);
            return Result.errorResult("ACTIVE_CONFIGS_QUERY_ERROR", "获取所有启用的内容类型配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ContentTypeConfigDTO>> getPortalModuleConfigs(Boolean isActive) {
        log.info("开始获取门户模块配置 - 入参: isActive={}", isActive);
        try {
            List<ContentTypeConfig> configs = contentTypeConfigMapper.selectByPortalModule(true, isActive);
            List<ContentTypeConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("获取门户模块配置成功 - 数量: {}", configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("获取门户模块配置失败", e);
            return Result.errorResult("PORTAL_CONFIGS_QUERY_ERROR", "获取门户模块配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<ContentTypeConfigDTO> getConfigByTableName(String tableName) {
        log.info("开始根据表名获取内容类型配置 - 入参: tableName={}", tableName);
        try {
            if (!StringUtils.hasText(tableName)) {
                log.warn("参数校验失败 - 表名不能为空");
                return Result.errorResult("INVALID_PARAMETER", "表名不能为空");
            }

            ContentTypeConfig config = contentTypeConfigMapper.selectByTableName(tableName);
            if (config == null) {
                log.warn("内容类型配置不存在 - tableName: {}", tableName);
                return Result.errorResult("CONFIG_NOT_FOUND", "内容类型配置不存在");
            }

            ContentTypeConfigDTO configDTO = convertToDTO(config);
            log.info("根据表名获取内容类型配置成功 - tableName: {}, id: {}", tableName, config.getId());
            return Result.success(configDTO);

        } catch (Exception e) {
            log.error("根据表名获取内容类型配置失败 - tableName: {}", tableName, e);
            return Result.errorResult("CONFIG_QUERY_ERROR", "获取内容类型配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<ContentTypeConfigDTO>> searchConfigs(SearchContentTypeConfigsRequest request) {
        log.info("开始搜索内容类型配置 - 入参: request={}", request);
        try {
            String keyword = request != null ? request.getKeyword() : null;
            Boolean isActive = request != null ? request.getIsActive() : null;

            List<ContentTypeConfig> configs = contentTypeConfigMapper.searchConfigs(keyword, isActive);
            List<ContentTypeConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("搜索内容类型配置成功 - 关键词: {}, 结果数量: {}", keyword, configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("搜索内容类型配置失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_SEARCH_ERROR", "搜索内容类型配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchUpdateSortOrder(BatchUpdateContentTypeConfigSortOrderRequest request) {
        log.info("开始批量更新内容类型配置排序 - 入参: request={}", request);
        try {
            if (request == null || request.getConfigIds() == null || request.getSortOrders() == null
                || request.getConfigIds().size() != request.getSortOrders().size()) {
                log.warn("参数校验失败 - 请求参数、配置ID列表和排序值列表不能为空且长度必须一致");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、配置ID列表和排序值列表不能为空且长度必须一致");
            }

            for (int i = 0; i < request.getConfigIds().size(); i++) {
                Long configId = request.getConfigIds().get(i);
                Integer sortOrder = request.getSortOrders().get(i);
                contentTypeConfigMapper.updateSortOrder(configId, sortOrder);
            }

            log.info("批量更新内容类型配置排序成功 - 更新数量: {}", request.getConfigIds().size());
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新内容类型配置排序失败 - request: {}", request, e);
            return Result.errorResult("BATCH_SORT_UPDATE_ERROR", "批量更新内容类型配置排序失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> checkCodeExists(CheckContentTypeCodeExistsRequest request) {
        log.info("开始检查内容类型编码是否存在 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getCode())) {
                log.warn("参数校验失败 - 请求参数和内容类型编码不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数和内容类型编码不能为空");
            }

            ContentTypeConfig config = contentTypeConfigMapper.selectByCode(request.getCode());
            boolean exists = config != null && (request.getExcludeId() == null || !config.getId().equals(request.getExcludeId()));

            log.info("检查内容类型编码是否存在完成 - code: {}, exists: {}", request.getCode(), exists);
            return Result.success(exists);

        } catch (Exception e) {
            log.error("检查内容类型编码是否存在失败 - request: {}", request, e);
            return Result.errorResult("CODE_CHECK_ERROR", "检查内容类型编码是否存在失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> checkTableNameExists(CheckContentTypeTableNameExistsRequest request) {
        log.info("开始检查表名是否已被使用 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getTableName())) {
                log.warn("参数校验失败 - 请求参数和表名不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数和表名不能为空");
            }

            ContentTypeConfig config = contentTypeConfigMapper.selectByTableName(request.getTableName());
            boolean exists = config != null && (request.getExcludeId() == null || !config.getId().equals(request.getExcludeId()));

            log.info("检查表名是否已被使用完成 - tableName: {}, exists: {}", request.getTableName(), exists);
            return Result.success(exists);

        } catch (Exception e) {
            log.error("检查表名是否已被使用失败 - request: {}", request, e);
            return Result.errorResult("TABLE_NAME_CHECK_ERROR", "检查表名是否已被使用失败: " + e.getMessage());
        }
    }

    /**
     * 实体转DTO
     */
    private ContentTypeConfigDTO convertToDTO(ContentTypeConfig entity) {
        if (entity == null) {
            return null;
        }
        ContentTypeConfigDTO dto = new ContentTypeConfigDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * DTO转实体
     */
    private ContentTypeConfig convertToEntity(ContentTypeConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        ContentTypeConfig entity = new ContentTypeConfig();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }
}
