package com.jdl.aic.core.service.service.impl;

import com.jdl.aic.core.service.dao.entity.portal.Badges;
import com.jdl.aic.core.service.dao.mapper.portal.BadgesMapper;
import com.jdl.aic.core.service.service.BadgesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 徽章服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class BadgesServiceImpl implements BadgesService {

    @Autowired
    private BadgesMapper badgesMapper;

    @Override
    public Badges getBadgeById(Long id) {
        if (id == null) {
            return null;
        }
        return badgesMapper.selectById(id);
    }

    @Override
    public Badges getBadgeByName(String name) {
        if (!StringUtils.hasText(name)) {
            return null;
        }
        return badgesMapper.selectByName(name);
    }

    @Override
    public List<Badges> getAllBadges() {
        return badgesMapper.selectAll();
    }

    @Override
    public List<Badges> getActiveBadges() {
        return badgesMapper.selectActive();
    }

    @Override
    public List<Badges> getBadgesByCreatedBy(Long createdBy) {
        if (createdBy == null) {
            return null;
        }
        return badgesMapper.selectByCreatedBy(createdBy);
    }

    @Override
    public boolean createBadge(Badges badge) {
        if (badge == null || !StringUtils.hasText(badge.getName())) {
            return false;
        }
        
        // 检查徽章名称是否已存在
        if (isBadgeNameExists(badge.getName())) {
            return false;
        }
        
        // 设置创建时间和默认值
        LocalDateTime now = LocalDateTime.now();
        badge.setCreated_at(now);
        badge.setUpdated_at(now);
        if (badge.getDeleted() == null) {
            badge.setDeleted(0); // 默认未删除
        }
        
        return badgesMapper.insert(badge) > 0;
    }

    @Override
    public boolean updateBadge(Badges badge) {
        if (badge == null || badge.getId() == null) {
            return false;
        }
        
        badge.setUpdated_at(LocalDateTime.now());
        return badgesMapper.updateById(badge) > 0;
    }

    @Override
    public boolean deleteBadge(Long id) {
        if (id == null) {
            return false;
        }
        return badgesMapper.deleteById(id) > 0;
    }

    @Override
    public boolean softDeleteBadge(Long id) {
        if (id == null) {
            return false;
        }
        return badgesMapper.softDeleteById(id) > 0;
    }

    @Override
    public boolean isBadgeNameExists(String name) {
        if (!StringUtils.hasText(name)) {
            return false;
        }
        return badgesMapper.selectByName(name) != null;
    }

    @Override
    public int getBadgeCount() {
        return badgesMapper.count();
    }

    @Override
    public int getActiveBadgeCount() {
        return badgesMapper.countActive();
    }

    @Override
    public List<Badges> searchBadges(String keyword, Integer limit) {
        if (!StringUtils.hasText(keyword)) {
            return null;
        }
        return badgesMapper.searchBadges(keyword, limit);
    }

    @Override
    public boolean batchCreateBadges(List<Badges> badges) {
        if (badges == null || badges.isEmpty()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        for (Badges badge : badges) {
            if (badge.getCreated_at() == null) {
                badge.setCreated_at(now);
            }
            if (badge.getUpdated_at() == null) {
                badge.setUpdated_at(now);
            }
            if (badge.getDeleted() == null) {
                badge.setDeleted(0);
            }
        }
        
        return badgesMapper.batchInsert(badges) > 0;
    }

    @Override
    public boolean batchSoftDeleteBadges(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return badgesMapper.batchSoftDelete(ids) > 0;
    }
}
