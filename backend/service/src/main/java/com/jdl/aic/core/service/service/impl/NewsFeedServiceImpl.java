package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.newsfeed.NewsFeedDTO;
import com.jdl.aic.core.service.client.dto.request.newsfeed.GetNewsFeedListRequest;
import com.jdl.aic.core.service.client.dto.request.newsfeed.UpdateNewsFeedStatusRequest;
import com.jdl.aic.core.service.client.dto.request.newsfeed.BatchUpdateNewsFeedStatusRequest;
import com.jdl.aic.core.service.client.service.NewsFeedService;
import com.jdl.aic.core.service.dao.entity.primary.NewsFeed;
import com.jdl.aic.core.service.dao.mapper.primary.NewsFeedMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 资讯管理服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("newsFeedService")
public class NewsFeedServiceImpl implements NewsFeedService {

    @Resource
    private NewsFeedMapper newsFeedMapper;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Result<PageResult<NewsFeedDTO>> getNewsFeedList(PageRequest pageRequest, GetNewsFeedListRequest request) {
        log.info("调用getNewsFeedList方法 - 参数: pageRequest={}, request={}", pageRequest, request);
        
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage() + 1, pageRequest.getSize()); // PageHelper从1开始
            
            // 解析时间参数
            LocalDateTime publishedAtStart = null;
            LocalDateTime publishedAtEnd = null;
            if (request != null) {
                if (StringUtils.hasText(request.getPublishedAtStart())) {
                    publishedAtStart = LocalDateTime.parse(request.getPublishedAtStart(), DATE_TIME_FORMATTER);
                }
                if (StringUtils.hasText(request.getPublishedAtEnd())) {
                    publishedAtEnd = LocalDateTime.parse(request.getPublishedAtEnd(), DATE_TIME_FORMATTER);
                }
            }

            // 查询资讯列表
            List<NewsFeed> newsFeeds;
            if (request != null && StringUtils.hasText(request.getSearch())) {
                log.debug("使用搜索模式查询资讯 - search: {}", request.getSearch());
                newsFeeds = newsFeedMapper.searchNewsFeed(request.getSearch(), request.getType(), request.getStatus());
            } else {
                log.debug("使用复合条件查询资讯");
                newsFeeds = newsFeedMapper.selectByComplexCondition(
                    request != null ? request.getType() : null,
                    request != null ? request.getStatus() : null,
                    request != null ? request.getAiReviewStatus() : null,
                    request != null ? request.getRssSourceId() : null,
                    request != null ? request.getSourceName() : null,
                    request != null ? request.getAuthor() : null,
                    request != null ? request.getSearch() : null,
                    publishedAtStart,
                    publishedAtEnd
                );
            }
            log.info("数据库查询完成，查询到 {} 条资讯记录", newsFeeds.size());

            // 使用PageInfo获取分页信息
            PageInfo<NewsFeed> pageInfo = new PageInfo<>(newsFeeds);

            // 转换为DTO
            List<NewsFeedDTO> newsFeedDTOs = new ArrayList<>();
            for (NewsFeed newsFeed : newsFeeds) {
                newsFeedDTOs.add(convertToNewsFeedDTO(newsFeed));
            }

            // 构建分页结果
            PageResult<NewsFeedDTO> pageResult = PageResult.of(
                newsFeedDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                pageInfo.getPageSize()
            );

            log.info("资讯列表查询成功 - 总记录数: {}, 当前页: {}, 每页大小: {}",
                pageResult.getPagination().getTotalElements(),
                pageResult.getPagination().getCurrentPage(),
                pageResult.getPagination().getPageSize());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取资讯列表失败", e);
            return Result.errorResult("NEWS_FEED_LIST_QUERY_ERROR", "获取资讯列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<NewsFeedDTO> getNewsFeedById(Long id) {
        log.info("调用getNewsFeedById方法 - 参数: id={}", id);
        
        try {
            if (id == null) {
                log.warn("资讯ID为空");
                return Result.errorResult("INVALID_PARAMETER", "资讯ID不能为空");
            }

            NewsFeed newsFeed = newsFeedMapper.selectById(id);
            if (newsFeed == null) {
                log.warn("资讯不存在 - id: {}", id);
                return Result.errorResult("NEWS_FEED_NOT_FOUND", "资讯不存在");
            }

            NewsFeedDTO newsFeedDTO = convertToNewsFeedDTO(newsFeed);
            log.info("资讯查询成功 - id: {}, title: {}", id, newsFeed.getTitle());
            return Result.success(newsFeedDTO);

        } catch (Exception e) {
            log.error("获取资讯详情失败 - id: {}", id, e);
            return Result.errorResult("NEWS_FEED_QUERY_ERROR", "获取资讯详情失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<NewsFeedDTO> createNewsFeed(NewsFeedDTO newsFeedDTO) {
        log.info("调用createNewsFeed方法 - 参数: newsFeedDTO={}", newsFeedDTO);
        
        try {
            if (newsFeedDTO == null) {
                log.warn("资讯信息为空");
                return Result.errorResult("INVALID_PARAMETER", "资讯信息不能为空");
            }

            // 检查必填字段
            if (!StringUtils.hasText(newsFeedDTO.getTitle())) {
                return Result.errorResult("INVALID_PARAMETER", "资讯标题不能为空");
            }
            if (!StringUtils.hasText(newsFeedDTO.getSourceUrl())) {
                return Result.errorResult("INVALID_PARAMETER", "资讯原文链接不能为空");
            }
            if (newsFeedDTO.getPublishedAt() == null) {
                return Result.errorResult("INVALID_PARAMETER", "资讯发布时间不能为空");
            }

            // 检查资讯是否已存在（根据原文链接）
            NewsFeed existingNewsFeed = newsFeedMapper.selectBySourceUrl(newsFeedDTO.getSourceUrl());
            if (existingNewsFeed != null) {
                log.warn("资讯已存在 - sourceUrl: {}", newsFeedDTO.getSourceUrl());
                return Result.errorResult("NEWS_FEED_ALREADY_EXISTS", "该资讯已存在");
            }

            // 转换为实体对象
            NewsFeed entity = convertToNewsFeedEntity(newsFeedDTO);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());

            // 设置默认值
            if (entity.getType() == null) {
                entity.setType(NewsFeed.Type.COLLECTED); // 默认为采集类型
            }
            if (entity.getStatus() == null) {
                entity.setStatus(NewsFeed.Status.PENDING_REVIEW); // 默认为待审核状态
            }
            if (entity.getAiReviewStatus() == null) {
                entity.setAiReviewStatus(NewsFeed.AiReviewStatus.NOT_REVIEWED); // 默认为未审核
            }

            // 插入数据库
            int result = newsFeedMapper.insert(entity);
            if (result > 0) {
                log.info("资讯创建成功 - id: {}, title: {}, sourceUrl: {}",
                    entity.getId(), entity.getTitle(), entity.getSourceUrl());
                NewsFeedDTO resultDto = convertToNewsFeedDTO(entity);
                return Result.success("资讯创建成功", resultDto);
            } else {
                log.error("资讯创建失败 - 数据库插入返回结果: {}, title: {}", result, entity.getTitle());
                return Result.errorResult("NEWS_FEED_CREATE_FAILED", "资讯创建失败");
            }

        } catch (Exception e) {
            log.error("创建资讯失败", e);
            return Result.errorResult("NEWS_FEED_CREATE_ERROR", "创建资讯失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<NewsFeedDTO> updateNewsFeed(Long id, NewsFeedDTO newsFeedDTO) {
        log.info("调用updateNewsFeed方法 - 参数: id={}, newsFeedDTO={}", id, newsFeedDTO);
        
        try {
            if (id == null) {
                log.warn("资讯ID为空");
                return Result.errorResult("INVALID_PARAMETER", "资讯ID不能为空");
            }
            if (newsFeedDTO == null) {
                log.warn("资讯信息为空");
                return Result.errorResult("INVALID_PARAMETER", "资讯信息不能为空");
            }

            // 检查资讯是否存在
            NewsFeed existingNewsFeed = newsFeedMapper.selectById(id);
            if (existingNewsFeed == null) {
                log.warn("资讯不存在 - id: {}", id);
                return Result.errorResult("NEWS_FEED_NOT_FOUND", "资讯不存在");
            }

            // 转换为实体对象
            NewsFeed entity = convertToNewsFeedEntity(newsFeedDTO);
            entity.setId(id);
            entity.setUpdatedAt(LocalDateTime.now());

            // 更新数据库
            int result = newsFeedMapper.updateById(entity);
            if (result > 0) {
                log.info("资讯更新成功 - id: {}, title: {}", id, entity.getTitle());
                
                // 查询更新后的数据
                NewsFeed updatedNewsFeed = newsFeedMapper.selectById(id);
                NewsFeedDTO resultDto = convertToNewsFeedDTO(updatedNewsFeed);
                return Result.success("资讯更新成功", resultDto);
            } else {
                log.error("资讯更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("NEWS_FEED_UPDATE_FAILED", "资讯更新失败");
            }

        } catch (Exception e) {
            log.error("更新资讯失败 - id: {}", id, e);
            return Result.errorResult("NEWS_FEED_UPDATE_ERROR", "更新资讯失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteNewsFeed(Long id) {
        log.info("调用deleteNewsFeed方法 - 参数: id={}", id);
        
        try {
            if (id == null) {
                log.warn("资讯ID为空");
                return Result.errorResult("INVALID_PARAMETER", "资讯ID不能为空");
            }

            // 检查资讯是否存在
            NewsFeed existingNewsFeed = newsFeedMapper.selectById(id);
            if (existingNewsFeed == null) {
                log.warn("资讯不存在 - id: {}", id);
                return Result.errorResult("NEWS_FEED_NOT_FOUND", "资讯不存在");
            }

            // 软删除
            int result = newsFeedMapper.deleteById(id);
            if (result > 0) {
                log.info("资讯删除成功 - id: {}, title: {}", id, existingNewsFeed.getTitle());
                return Result.success();
            } else {
                log.error("资讯删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("NEWS_FEED_DELETE_FAILED", "资讯删除失败");
            }

        } catch (Exception e) {
            log.error("删除资讯失败 - id: {}", id, e);
            return Result.errorResult("NEWS_FEED_DELETE_ERROR", "删除资讯失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchDeleteNewsFeed(List<Long> ids) {
        log.info("调用batchDeleteNewsFeed方法 - 参数: ids={}", ids);

        try {
            if (ids == null || ids.isEmpty()) {
                log.warn("资讯ID列表为空");
                return Result.errorResult("INVALID_PARAMETER", "资讯ID列表不能为空");
            }

            // 批量软删除
            int result = newsFeedMapper.batchDeleteByIds(ids);
            if (result > 0) {
                log.info("批量删除资讯成功 - 删除数量: {}, ids: {}", result, ids);
                return Result.success();
            } else {
                log.error("批量删除资讯失败 - 数据库删除返回结果: {}, ids: {}", result, ids);
                return Result.errorResult("NEWS_FEED_BATCH_DELETE_FAILED", "批量删除资讯失败");
            }

        } catch (Exception e) {
            log.error("批量删除资讯失败 - ids: {}", ids, e);
            return Result.errorResult("NEWS_FEED_BATCH_DELETE_ERROR", "批量删除资讯失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateNewsFeedStatus(UpdateNewsFeedStatusRequest request) {
        log.info("调用updateNewsFeedStatus方法 - 参数: request={}", request);

        try {
            if (request == null || request.getId() == null || request.getStatus() == null) {
                log.warn("请求参数无效");
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            // 检查资讯是否存在
            NewsFeed existingNewsFeed = newsFeedMapper.selectById(request.getId());
            if (existingNewsFeed == null) {
                log.warn("资讯不存在 - id: {}", request.getId());
                return Result.errorResult("NEWS_FEED_NOT_FOUND", "资讯不存在");
            }

            // 更新状态
            int result = newsFeedMapper.updateStatus(request.getId(), request.getStatus());
            if (result > 0) {
                log.info("资讯状态更新成功 - id: {}, status: {} -> {}",
                    request.getId(), existingNewsFeed.getStatus(), request.getStatus());
                return Result.success();
            } else {
                log.error("资讯状态更新失败 - 数据库更新返回结果: {}, id: {}", result, request.getId());
                return Result.errorResult("NEWS_FEED_STATUS_UPDATE_FAILED", "资讯状态更新失败");
            }

        } catch (Exception e) {
            log.error("更新资讯状态失败", e);
            return Result.errorResult("NEWS_FEED_STATUS_UPDATE_ERROR", "更新资讯状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchUpdateNewsFeedStatus(BatchUpdateNewsFeedStatusRequest request) {
        log.info("调用batchUpdateNewsFeedStatus方法 - 参数: request={}", request);

        try {
            if (request == null || request.getIds() == null || request.getIds().isEmpty() || request.getStatus() == null) {
                log.warn("请求参数无效");
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            // 批量更新状态
            int result = newsFeedMapper.batchUpdateStatus(request.getIds(), request.getStatus());
            if (result > 0) {
                log.info("批量更新资讯状态成功 - 更新数量: {}, status: {}, ids: {}",
                    result, request.getStatus(), request.getIds());
                return Result.success();
            } else {
                log.error("批量更新资讯状态失败 - 数据库更新返回结果: {}, ids: {}", result, request.getIds());
                return Result.errorResult("NEWS_FEED_BATCH_STATUS_UPDATE_FAILED", "批量更新资讯状态失败");
            }

        } catch (Exception e) {
            log.error("批量更新资讯状态失败", e);
            return Result.errorResult("NEWS_FEED_BATCH_STATUS_UPDATE_ERROR", "批量更新资讯状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> publishNewsFeed(Long id) {
        log.info("调用publishNewsFeed方法 - 参数: id={}", id);

        try {
            if (id == null) {
                log.warn("资讯ID为空");
                return Result.errorResult("INVALID_PARAMETER", "资讯ID不能为空");
            }

            // 检查资讯是否存在
            NewsFeed existingNewsFeed = newsFeedMapper.selectById(id);
            if (existingNewsFeed == null) {
                log.warn("资讯不存在 - id: {}", id);
                return Result.errorResult("NEWS_FEED_NOT_FOUND", "资讯不存在");
            }

            // 发布资讯
            int result = newsFeedMapper.updateStatus(id, NewsFeed.Status.PUBLISHED);
            if (result > 0) {
                log.info("资讯发布成功 - id: {}, title: {}", id, existingNewsFeed.getTitle());
                return Result.success();
            } else {
                log.error("资讯发布失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("NEWS_FEED_PUBLISH_FAILED", "资讯发布失败");
            }

        } catch (Exception e) {
            log.error("发布资讯失败 - id: {}", id, e);
            return Result.errorResult("NEWS_FEED_PUBLISH_ERROR", "发布资讯失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> offlineNewsFeed(Long id) {
        log.info("调用offlineNewsFeed方法 - 参数: id={}", id);

        try {
            if (id == null) {
                log.warn("资讯ID为空");
                return Result.errorResult("INVALID_PARAMETER", "资讯ID不能为空");
            }

            // 检查资讯是否存在
            NewsFeed existingNewsFeed = newsFeedMapper.selectById(id);
            if (existingNewsFeed == null) {
                log.warn("资讯不存在 - id: {}", id);
                return Result.errorResult("NEWS_FEED_NOT_FOUND", "资讯不存在");
            }

            // 下线资讯
            int result = newsFeedMapper.updateStatus(id, NewsFeed.Status.OFFLINE);
            if (result > 0) {
                log.info("资讯下线成功 - id: {}, title: {}", id, existingNewsFeed.getTitle());
                return Result.success();
            } else {
                log.error("资讯下线失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("NEWS_FEED_OFFLINE_FAILED", "资讯下线失败");
            }

        } catch (Exception e) {
            log.error("下线资讯失败 - id: {}", id, e);
            return Result.errorResult("NEWS_FEED_OFFLINE_ERROR", "下线资讯失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateAiReviewStatus(Long id, Integer aiReviewStatus) {
        log.info("调用updateAiReviewStatus方法 - 参数: id={}, aiReviewStatus={}", id, aiReviewStatus);

        try {
            if (id == null || aiReviewStatus == null) {
                log.warn("参数无效");
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 检查资讯是否存在
            NewsFeed existingNewsFeed = newsFeedMapper.selectById(id);
            if (existingNewsFeed == null) {
                log.warn("资讯不存在 - id: {}", id);
                return Result.errorResult("NEWS_FEED_NOT_FOUND", "资讯不存在");
            }

            // 更新AI审核状态
            int result = newsFeedMapper.updateAiReviewStatus(id, aiReviewStatus);
            if (result > 0) {
                log.info("AI审核状态更新成功 - id: {}, aiReviewStatus: {} -> {}",
                    id, existingNewsFeed.getAiReviewStatus(), aiReviewStatus);
                return Result.success();
            } else {
                log.error("AI审核状态更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("AI_REVIEW_STATUS_UPDATE_FAILED", "AI审核状态更新失败");
            }

        } catch (Exception e) {
            log.error("更新AI审核状态失败 - id: {}", id, e);
            return Result.errorResult("AI_REVIEW_STATUS_UPDATE_ERROR", "更新AI审核状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<NewsFeedDTO>> getPendingAiReviewNewsFeed(PageRequest pageRequest) {
        log.info("调用getPendingAiReviewNewsFeed方法 - 参数: pageRequest={}", pageRequest);

        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage() + 1, pageRequest.getSize()); // PageHelper从1开始

            // 查询待AI审核的资讯
            List<NewsFeed> newsFeeds = newsFeedMapper.selectByAiReviewStatus(NewsFeed.AiReviewStatus.NOT_REVIEWED);
            log.info("查询到 {} 条待AI审核的资讯", newsFeeds.size());

            // 使用PageInfo获取分页信息
            PageInfo<NewsFeed> pageInfo = new PageInfo<>(newsFeeds);

            // 转换为DTO
            List<NewsFeedDTO> newsFeedDTOs = new ArrayList<>();
            for (NewsFeed newsFeed : newsFeeds) {
                newsFeedDTOs.add(convertToNewsFeedDTO(newsFeed));
            }

            // 构建分页结果
            PageResult<NewsFeedDTO> pageResult = PageResult.of(
                newsFeedDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                pageInfo.getPageSize()
            );

            log.info("待AI审核资讯查询成功 - 总记录数: {}", pageResult.getPagination().getTotalElements());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取待AI审核资讯失败", e);
            return Result.errorResult("PENDING_AI_REVIEW_QUERY_ERROR", "获取待AI审核资讯失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<NewsFeedDTO>> getNewsFeedByRssSource(Long rssSourceId, PageRequest pageRequest) {
        log.info("调用getNewsFeedByRssSource方法 - 参数: rssSourceId={}, pageRequest={}", rssSourceId, pageRequest);

        try {
            if (rssSourceId == null) {
                log.warn("RSS源ID为空");
                return Result.errorResult("INVALID_PARAMETER", "RSS源ID不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage() + 1, pageRequest.getSize()); // PageHelper从1开始

            // 查询RSS源的资讯
            List<NewsFeed> newsFeeds = newsFeedMapper.selectByRssSourceId(rssSourceId);
            log.info("查询到RSS源 {} 的 {} 条资讯", rssSourceId, newsFeeds.size());

            // 使用PageInfo获取分页信息
            PageInfo<NewsFeed> pageInfo = new PageInfo<>(newsFeeds);

            // 转换为DTO
            List<NewsFeedDTO> newsFeedDTOs = new ArrayList<>();
            for (NewsFeed newsFeed : newsFeeds) {
                newsFeedDTOs.add(convertToNewsFeedDTO(newsFeed));
            }

            // 构建分页结果
            PageResult<NewsFeedDTO> pageResult = PageResult.of(
                newsFeedDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                pageInfo.getPageSize()
            );

            log.info("RSS源资讯查询成功 - RSS源ID: {}, 总记录数: {}", rssSourceId, pageResult.getPagination().getTotalElements());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取RSS源资讯失败 - rssSourceId: {}", rssSourceId, e);
            return Result.errorResult("RSS_SOURCE_NEWS_FEED_QUERY_ERROR", "获取RSS源资讯失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> syncRssSourceNewsFeed(Long rssSourceId) {
        log.info("调用syncRssSourceNewsFeed方法 - 参数: rssSourceId={}", rssSourceId);

        try {
            if (rssSourceId == null) {
                log.warn("RSS源ID为空");
                return Result.errorResult("INVALID_PARAMETER", "RSS源ID不能为空");
            }

            // TODO: 实现RSS源同步逻辑
            // 这里应该调用RSS采集服务来同步资讯
            log.info("RSS源同步功能待实现 - rssSourceId: {}", rssSourceId);

            return Result.success("RSS源同步功能待实现", 0);

        } catch (Exception e) {
            log.error("同步RSS源资讯失败 - rssSourceId: {}", rssSourceId, e);
            return Result.errorResult("RSS_SOURCE_SYNC_ERROR", "同步RSS源资讯失败: " + e.getMessage());
        }
    }

    @Override
    public Result<NewsFeedStatistics> getNewsFeedStatistics() {
        log.info("调用getNewsFeedStatistics方法");

        try {
            // 获取统计信息
            Map<String, Long> statisticsMap = newsFeedMapper.getNewsFeedStatistics();
            Long todayCount = newsFeedMapper.getTodayNewsFeedCount();

            NewsFeedStatistics statistics = new NewsFeedStatistics(
                (Long) statisticsMap.get("totalCount"),
                (Long) statisticsMap.get("publishedCount"),
                (Long) statisticsMap.get("pendingCount"),
                (Long) statisticsMap.get("offlineCount"),
                todayCount
            );

            log.info("资讯统计信息获取成功 - 总数: {}, 已发布: {}, 待审核: {}, 已下线: {}, 今日新增: {}",
                statistics.getTotalCount(), statistics.getPublishedCount(),
                statistics.getPendingCount(), statistics.getOfflineCount(), statistics.getTodayCount());

            return Result.success(statistics);

        } catch (Exception e) {
            log.error("获取资讯统计信息失败", e);
            return Result.errorResult("NEWS_FEED_STATISTICS_ERROR", "获取资讯统计信息失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<NewsFeedDTO>> getPopularNewsFeed(Integer limit) {
        log.info("调用getPopularNewsFeed方法 - 参数: limit={}", limit);

        try {
            if (limit == null || limit <= 0) {
                limit = 10; // 默认10条
            }

            List<NewsFeed> newsFeeds = newsFeedMapper.selectPopularNewsFeed(limit, NewsFeed.Status.PUBLISHED);
            List<NewsFeedDTO> newsFeedDTOs = new ArrayList<>();
            for (NewsFeed newsFeed : newsFeeds) {
                newsFeedDTOs.add(convertToNewsFeedDTO(newsFeed));
            }

            log.info("热门资讯查询成功 - 查询到 {} 条记录", newsFeedDTOs.size());
            return Result.success(newsFeedDTOs);

        } catch (Exception e) {
            log.error("获取热门资讯失败", e);
            return Result.errorResult("POPULAR_NEWS_FEED_QUERY_ERROR", "获取热门资讯失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<NewsFeedDTO>> getLatestNewsFeed(Integer limit) {
        log.info("调用getLatestNewsFeed方法 - 参数: limit={}", limit);

        try {
            if (limit == null || limit <= 0) {
                limit = 10; // 默认10条
            }

            List<NewsFeed> newsFeeds = newsFeedMapper.selectLatestNewsFeed(limit, NewsFeed.Status.PUBLISHED);
            List<NewsFeedDTO> newsFeedDTOs = new ArrayList<>();
            for (NewsFeed newsFeed : newsFeeds) {
                newsFeedDTOs.add(convertToNewsFeedDTO(newsFeed));
            }

            log.info("最新资讯查询成功 - 查询到 {} 条记录", newsFeedDTOs.size());
            return Result.success(newsFeedDTOs);

        } catch (Exception e) {
            log.error("获取最新资讯失败", e);
            return Result.errorResult("LATEST_NEWS_FEED_QUERY_ERROR", "获取最新资讯失败: " + e.getMessage());
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 将NewsFeed实体转换为NewsFeedDTO
     *
     * @param newsFeed 实体对象
     * @return DTO对象
     */
    private NewsFeedDTO convertToNewsFeedDTO(NewsFeed newsFeed) {
        if (newsFeed == null) {
            return null;
        }

        NewsFeedDTO dto = new NewsFeedDTO();
        BeanUtils.copyProperties(newsFeed, dto);

        // 设置状态描述
        dto.setTypeDesc(getTypeDescription(newsFeed.getType()));
        dto.setStatusDesc(getStatusDescription(newsFeed.getStatus()));
        dto.setAiReviewStatusDesc(getAiReviewStatusDescription(newsFeed.getAiReviewStatus()));

        // TODO: 解析AI标签JSON
        // if (StringUtils.hasText(newsFeed.getAiTagsJson())) {
        //     try {
        //         List<String> aiTags = JSON.parseArray(newsFeed.getAiTagsJson(), String.class);
        //         dto.setAiTags(aiTags);
        //     } catch (Exception e) {
        //         log.warn("解析AI标签JSON失败 - id: {}, aiTagsJson: {}", newsFeed.getId(), newsFeed.getAiTagsJson());
        //     }
        // }

        return dto;
    }

    /**
     * 将NewsFeedDTO转换为NewsFeed实体
     *
     * @param dto DTO对象
     * @return 实体对象
     */
    private NewsFeed convertToNewsFeedEntity(NewsFeedDTO dto) {
        if (dto == null) {
            return null;
        }

        NewsFeed entity = new NewsFeed();
        BeanUtils.copyProperties(dto, entity);

        // TODO: 将AI标签列表转换为JSON
        // if (dto.getAiTags() != null && !dto.getAiTags().isEmpty()) {
        //     try {
        //         String aiTagsJson = JSON.toJSONString(dto.getAiTags());
        //         entity.setAiTagsJson(aiTagsJson);
        //     } catch (Exception e) {
        //         log.warn("转换AI标签为JSON失败 - aiTags: {}", dto.getAiTags());
        //     }
        // }

        return entity;
    }

    /**
     * 获取资讯类型描述
     */
    private String getTypeDescription(Integer type) {
        if (type == null) return null;
        switch (type) {
            case 0: return "采集";
            case 1: return "官方发布";
            default: return "未知";
        }
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(Integer status) {
        if (status == null) return null;
        switch (status) {
            case 0: return "待审核";
            case 1: return "已发布";
            case 2: return "已下线";
            default: return "未知";
        }
    }

    /**
     * 获取AI审核状态描述
     */
    private String getAiReviewStatusDescription(Integer aiReviewStatus) {
        if (aiReviewStatus == null) return null;
        switch (aiReviewStatus) {
            case 0: return "未审";
            case 1: return "通过";
            case 2: return "拒绝";
            case 3: return "人工复审";
            default: return "未知";
        }
    }
}
