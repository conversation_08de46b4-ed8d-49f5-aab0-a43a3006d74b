package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.category.CategoryDTO;
import com.jdl.aic.core.service.client.dto.request.category.GetCategoryListRequest;
import com.jdl.aic.core.service.client.dto.request.category.GetCategoryTreeRequest;
import com.jdl.aic.core.service.client.dto.request.category.ToggleCategoryStatusRequest;
import com.jdl.aic.core.service.client.dto.request.category.UpdateCategorySortOrderRequest;
import com.jdl.aic.core.service.client.dto.request.category.MoveCategoryToParentRequest;
import com.jdl.aic.core.service.client.dto.request.category.GetCategoriesBySubTypeRequest;
import com.jdl.aic.core.service.client.service.CategoryService;
import com.jdl.aic.core.service.dao.entity.primary.Category;
import com.jdl.aic.core.service.dao.mapper.primary.CategoryMapper;
import com.jdl.aic.core.service.dao.mapper.primary.KnowledgeTypeMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 分类管理服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("categoryService")
@Transactional
public class CategoryServiceImpl implements CategoryService {

    @Resource
    private CategoryMapper categoryMapper;

    @Resource
    private KnowledgeTypeMapper knowledgeTypeMapper;

    // ==================== 分类管理 ====================

    @Override
    public Result<PageResult<CategoryDTO>> getCategoryList(
            PageRequest pageRequest,
            GetCategoryListRequest request) {
        log.info("开始获取分类列表 - 入参: pageRequest={}, request={}",
                pageRequest, request);
        try {
            // 从请求对象中提取参数
            String contentCategory = request != null ? request.getContentCategory() : null;
            Long subTypeId = request != null ? request.getSubTypeId() : null;
            Long parentId = request != null ? request.getParentId() : null;
            Boolean isActive = request != null ? request.getIsActive() : null;
            String search = request != null ? request.getSearch() : null;

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            Category queryCondition = new Category();
            if (StringUtils.hasText(contentCategory)) {
                queryCondition.setContentCategory(contentCategory);
            }
            if (parentId != null) {
                queryCondition.setParentId(parentId);
            }
            if (isActive != null) {
                queryCondition.setIsActive(isActive);
            }
            if (StringUtils.hasText(search)) {
                queryCondition.setName(search);
            }
            log.debug("构建查询条件完成: contentCategory={}, parentId={}, isActive={}, search={}",
                contentCategory, parentId, isActive, search);

            // 查询分类列表
            List<Category> categories;
            if (StringUtils.hasText(search)) {
                log.debug("使用搜索模式查询分类 - search: {}", search);
                categories = categoryMapper.searchCategories(search, contentCategory, isActive);
            } else {
                log.debug("使用条件查询分类");
                categories = categoryMapper.selectByCondition(queryCondition);
            }
            log.info("数据库查询完成，查询到 {} 条分类记录", categories.size());

            // 使用PageInfo获取分页信息
            PageInfo<Category> pageInfo = new PageInfo<>(categories);

            // 转换为DTO
            List<CategoryDTO> categoryDTOs = categories.stream()
                    .map(this::convertToCategoryDTO)
                    .collect(Collectors.toList());

            // 使用PageInfo的总数创建分页结果
            PageResult<CategoryDTO> pageResult = PageResult.of(categoryDTOs, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());
            log.info("获取分类列表成功 - 返回 {} 条记录，总数: {}", categoryDTOs.size(), pageInfo.getTotal());

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取分类列表失败 - 入参: request={}, 错误信息: {}",
                request, e.getMessage(), e);
            return Result.errorResult("CATEGORY_LIST_QUERY_ERROR", "获取分类列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CategoryDTO>> getCategoryTree(GetCategoryTreeRequest request) {
        log.info("开始获取分类树 - 入参: request={}", request);
        // 从请求对象中提取参数
        String contentCategory = request != null ? request.getContentCategory() : null;
        Long subTypeId = request != null ? request.getSubTypeId() : null;
        Boolean isActive = request != null ? request.getIsActive() : null;
        
        try {
            // 查询根分类
            List<Category> rootCategories = categoryMapper.selectRootCategories(contentCategory, subTypeId,  isActive);
            log.info("查询到 {} 个根分类", rootCategories.size());

            // 构建分类树
            List<CategoryDTO> categoryTree = rootCategories.stream()
                    .map(this::convertToCategoryDTO)
                    .map(dto -> {
                        log.debug("构建分类树节点 - id: {}, name: {}", dto.getId(), dto.getName());
                        return buildCategoryTree(dto, contentCategory, isActive);
                    })
                    .collect(Collectors.toList());

            log.info("分类树构建成功 - 根节点数量: {}", categoryTree.size());
            return Result.success(categoryTree);

        } catch (Exception e) {
            log.error("获取分类树失败 - 入参: contentCategory={}, subTypeId={}, isActive={}, 错误信息: {}",
                contentCategory, subTypeId, isActive, e.getMessage(), e);
            return Result.errorResult("CATEGORY_TREE_QUERY_ERROR", "获取分类树失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CategoryDTO> getCategoryById(Long id) {
        log.info("调用getCategoryById方法 - 参数: id={}", id);
        try {
            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "分类ID不能为空");
            }

            Category category = categoryMapper.selectById(id);
            if (category == null) {
                return Result.errorResult("CATEGORY_NOT_FOUND", "分类不存在");
            }

            CategoryDTO dto = convertToCategoryDTO(category);
            return Result.success(dto);

        } catch (Exception e) {
            log.error("查询分类详情失败", e);
            return Result.errorResult("CATEGORY_QUERY_ERROR", "查询分类详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CategoryDTO> createCategory(CategoryDTO category) {
        log.info("开始创建分类 - 入参: name={}, contentCategory={}, parentId={}",
            category != null ? category.getName() : null,
            category != null ? category.getContentCategory() : null,
            category != null ? category.getParentId() : null);
        try {
            if (category == null) {
                log.warn("参数校验失败 - 分类信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "分类信息不能为空");
            }

            // 验证必填字段
            if (!StringUtils.hasText(category.getName())) {
                log.warn("参数校验失败 - 分类名称不能为空");
                return Result.errorResult("INVALID_PARAMETER", "分类名称不能为空");
            }
            if (!StringUtils.hasText(category.getContentCategory())) {
                log.warn("参数校验失败 - 内容类别不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类别不能为空");
            }

            // 检查同级分类名称是否重复
            Category existingCategory = categoryMapper.selectByNameAndCategory(
                    category.getName(), category.getContentCategory(), category.getParentId());
            if (existingCategory != null) {
                log.warn("同级分类名称已存在 - name: {}, contentCategory: {}, parentId: {}, existingId: {}",
                    category.getName(), category.getContentCategory(), category.getParentId(), existingCategory.getId());
                return Result.errorResult("CATEGORY_NAME_EXISTS", "同级分类名称已存在");
            }
            log.debug("分类名称唯一性校验通过 - name: {}", category.getName());

            // 如果有父分类，验证父分类是否存在
            if (category.getParentId() != null) {
                Category parentCategory = categoryMapper.selectById(category.getParentId());
                if (parentCategory == null) {
                    log.warn("父分类不存在 - parentId: {}", category.getParentId());
                    return Result.errorResult("PARENT_CATEGORY_NOT_FOUND", "父分类不存在");
                }
                // 验证父分类的内容类别是否一致
                if (!parentCategory.getContentCategory().equals(category.getContentCategory())) {
                    log.warn("父分类的内容类别不一致 - parentCategory: {}, currentCategory: {}",
                        parentCategory.getContentCategory(), category.getContentCategory());
                    return Result.errorResult("INVALID_PARENT_CATEGORY", "父分类的内容类别不一致");
                }
                log.debug("父分类校验通过 - parentId: {}, parentName: {}", category.getParentId(), parentCategory.getName());
            }

            // 转换为实体对象
            Category entity = convertToCategory(category);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());

            // 设置默认值
            if (entity.getIsActive() == null) {
                entity.setIsActive(true);
            }
            if (entity.getSortOrder() == null) {
                entity.setSortOrder(0);
            }
            log.debug("实体转换和默认值设置完成 - name: {}, contentCategory: {}, isActive: {}, sortOrder: {}",
                entity.getName(), entity.getContentCategory(), entity.getIsActive(), entity.getSortOrder());

            // 插入数据库
            int result = categoryMapper.insert(entity);
            if (result > 0) {
                log.info("分类创建成功 - id: {}, name: {}, contentCategory: {}, parentId: {}",
                    entity.getId(), entity.getName(), entity.getContentCategory(), entity.getParentId());
                CategoryDTO resultDto = convertToCategoryDTO(entity);
                return Result.success("分类创建成功", resultDto);
            } else {
                log.error("分类创建失败 - 数据库插入返回结果: {}, name: {}", result, entity.getName());
                return Result.errorResult("CATEGORY_CREATE_FAILED", "分类创建失败");
            }

        } catch (Exception e) {
            log.error("创建分类失败 - 入参: name={}, contentCategory={}, parentId={}, 错误信息: {}",
                category != null ? category.getName() : null,
                category != null ? category.getContentCategory() : null,
                category != null ? category.getParentId() : null,
                e.getMessage(), e);
            return Result.errorResult("CATEGORY_CREATE_ERROR", "创建分类失败: " + e.getMessage());
        }
    }

    @Override
    public Result<CategoryDTO> updateCategory(Long id, CategoryDTO category) {
        log.info("调用updateCategory方法 - 参数: id={}, category={}", id, category);
        try {
            if (id == null || category == null) {
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 检查分类是否存在
            Category existingCategory = categoryMapper.selectById(id);
            if (existingCategory == null) {
                return Result.errorResult("CATEGORY_NOT_FOUND", "分类不存在");
            }

            // 如果更新名称，检查同级分类名称是否重复
            if (StringUtils.hasText(category.getName()) && !category.getName().equals(existingCategory.getName())) {
                String contentCategory = StringUtils.hasText(category.getContentCategory()) 
                        ? category.getContentCategory() : existingCategory.getContentCategory();
                Long parentId = category.getParentId() != null ? category.getParentId() : existingCategory.getParentId();
                
                Category duplicateCategory = categoryMapper.selectByNameAndCategory(
                        category.getName(), contentCategory, parentId);
                if (duplicateCategory != null && !duplicateCategory.getId().equals(id)) {
                    return Result.errorResult("CATEGORY_NAME_EXISTS", "同级分类名称已存在");
                }
            }

            // 更新字段
            if (StringUtils.hasText(category.getName())) {
                existingCategory.setName(category.getName());
            }
            if (category.getDescription() != null) {
                existingCategory.setDescription(category.getDescription());
            }
            if (StringUtils.hasText(category.getIconUrl())) {
                existingCategory.setIconUrl(category.getIconUrl());
            }
            if (category.getSortOrder() != null) {
                existingCategory.setSortOrder(category.getSortOrder());
            }
            if (category.getIsActive() != null) {
                existingCategory.setIsActive(category.getIsActive());
            }
            
            existingCategory.setUpdatedAt(LocalDateTime.now());
            if (StringUtils.hasText(category.getUpdatedBy())) {
                existingCategory.setUpdatedBy(category.getUpdatedBy());
            }

            // 更新数据库
            int result = categoryMapper.updateById(existingCategory);
            if (result > 0) {
                CategoryDTO resultDto = convertToCategoryDTO(existingCategory);
                return Result.success("分类更新成功", resultDto);
            } else {
                return Result.errorResult("CATEGORY_UPDATE_FAILED", "分类更新失败");
            }

        } catch (Exception e) {
            log.error("更新分类失败", e);
            return Result.errorResult("CATEGORY_UPDATE_ERROR", "更新分类失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteCategory(Long id) {
        log.info("开始删除分类 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 分类ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "分类ID不能为空");
            }

            // 检查分类是否存在
            Category existingCategory = categoryMapper.selectById(id);
            if (existingCategory == null) {
                log.warn("分类不存在 - id: {}", id);
                return Result.errorResult("CATEGORY_NOT_FOUND", "分类不存在");
            }
            log.debug("查询到待删除的分类 - id: {}, name: {}, contentCategory: {}",
                id, existingCategory.getName(), existingCategory.getContentCategory());

            // 检查是否有子分类
            int childCount = categoryMapper.countByParentId(id);
            if (childCount > 0) {
                log.warn("分类下存在子分类，无法删除 - id: {}, 子分类数量: {}", id, childCount);
                return Result.errorResult("CATEGORY_HAS_CHILDREN", "该分类下还有子分类，无法删除");
            }
            log.debug("子分类检查通过，可以删除 - id: {}", id);

            // TODO: 检查是否有关联的内容（需要根据具体业务需求实现）
            // 这里可以检查knowledge_category、content_category_relation等关联表

            int result = categoryMapper.deleteById(id);
            if (result > 0) {
                log.info("分类删除成功 - id: {}, name: {}, contentCategory: {}",
                    id, existingCategory.getName(), existingCategory.getContentCategory());
                return Result.success();
            } else {
                log.error("分类删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("CATEGORY_DELETE_FAILED", "分类删除失败");
            }

        } catch (Exception e) {
            log.error("删除分类失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("CATEGORY_DELETE_ERROR", "删除分类失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> toggleCategoryStatus(ToggleCategoryStatusRequest request) {
        log.info("开始切换分类状态 - 入参: request={}", request);
        try {
            // 从请求对象中提取参数
            Long id = request != null ? request.getId() : null;
            Boolean isActive = request != null ? request.getIsActive() : null;

            if (id == null || isActive == null) {
                log.warn("参数校验失败 - 参数不能为空, id: {}, isActive: {}", id, isActive);
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 检查分类是否存在
            Category existingCategory = categoryMapper.selectById(id);
            if (existingCategory == null) {
                log.warn("分类不存在 - id: {}", id);
                return Result.errorResult("CATEGORY_NOT_FOUND", "分类不存在");
            }
            log.debug("查询到分类 - id: {}, name: {}, 当前状态: {}, 目标状态: {}",
                id, existingCategory.getName(), existingCategory.getIsActive(), isActive);

            int result = categoryMapper.updateStatus(id, isActive);
            if (result > 0) {
                log.info("分类状态更新成功 - id: {}, name: {}, 新状态: {}", id, existingCategory.getName(), isActive);
                return Result.success();
            } else {
                log.error("分类状态更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("CATEGORY_STATUS_UPDATE_FAILED", "分类状态更新失败");
            }

        } catch (Exception e) {
            log.error("更新分类状态失败 - 入参: request={}, 错误信息: {}", request, e.getMessage(), e);
            return Result.errorResult("CATEGORY_STATUS_UPDATE_ERROR", "更新分类状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> updateCategorySortOrder(UpdateCategorySortOrderRequest request) {
        try {
            // 从请求对象中提取参数
            Long id = request != null ? request.getId() : null;
            Integer sortOrder = request != null ? request.getSortOrder() : null;

            if (id == null || sortOrder == null) {
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 检查分类是否存在
            Category existingCategory = categoryMapper.selectById(id);
            if (existingCategory == null) {
                return Result.errorResult("CATEGORY_NOT_FOUND", "分类不存在");
            }

            int result = categoryMapper.updateSortOrder(id, sortOrder);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.errorResult("CATEGORY_SORT_UPDATE_FAILED", "分类排序更新失败");
            }

        } catch (Exception e) {
            log.error("更新分类排序失败", e);
            return Result.errorResult("CATEGORY_SORT_UPDATE_ERROR", "更新分类排序失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> moveCategoryToParent(MoveCategoryToParentRequest request) {
        log.info("调用moveCategoryToParent方法 - 参数: request={}", request);
        try {
            // 从请求对象中提取参数
            Long id = request != null ? request.getId() : null;
            Long newParentId = request != null ? request.getNewParentId() : null;

            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "分类ID不能为空");
            }

            // 检查分类是否存在
            Category existingCategory = categoryMapper.selectById(id);
            if (existingCategory == null) {
                return Result.errorResult("CATEGORY_NOT_FOUND", "分类不存在");
            }

            // 如果有新父分类，验证新父分类是否存在
            if (newParentId != null) {
                Category newParentCategory = categoryMapper.selectById(newParentId);
                if (newParentCategory == null) {
                    return Result.errorResult("PARENT_CATEGORY_NOT_FOUND", "新父分类不存在");
                }

                // 验证不能移动到自己或自己的子分类下（防止循环引用）
                if (id.equals(newParentId) || isDescendantOf(newParentId, id)) {
                    return Result.errorResult("CIRCULAR_REFERENCE", "不能移动到自己或子分类下");
                }

                // 验证内容类别是否一致
                if (!newParentCategory.getContentCategory().equals(existingCategory.getContentCategory())) {
                    return Result.errorResult("INVALID_PARENT_CATEGORY", "新父分类的内容类别不一致");
                }
            }

            // 检查同级分类名称是否重复
            Category duplicateCategory = categoryMapper.selectByNameAndCategory(
                    existingCategory.getName(), existingCategory.getContentCategory(), newParentId);
            if (duplicateCategory != null && !duplicateCategory.getId().equals(id)) {
                return Result.errorResult("CATEGORY_NAME_EXISTS", "目标位置已存在同名分类");
            }

            int result = categoryMapper.updateParent(id, newParentId);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.errorResult("CATEGORY_MOVE_FAILED", "分类移动失败");
            }

        } catch (Exception e) {
            log.error("移动分类失败", e);
            return Result.errorResult("CATEGORY_MOVE_ERROR", "移动分类失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CategoryDTO>> getAvailableCategoriesForKnowledgeType(Long knowledgeTypeId) {
        log.info("调用getAvailableCategoriesForKnowledgeType方法 - 参数: knowledgeTypeId={}", knowledgeTypeId);
        try {
            if (knowledgeTypeId == null) {
                return Result.errorResult("INVALID_PARAMETER", "知识类型ID不能为空");
            }

            // 注意：当前数据库表中没有sub_type_id字段，这里返回通用知识分类和全局通用分类
            List<Category> categories = new ArrayList<>();

            // 获取通用知识分类（content_category = 'knowledge'）
            List<Category> knowledgeCategories = categoryMapper.selectByContentCategory("knowledge", true);
            categories.addAll(knowledgeCategories);

            // 获取全局通用分类（content_category = 'general'）
            List<Category> generalCategories = categoryMapper.selectByContentCategory("general", true);
            categories.addAll(generalCategories);

            List<CategoryDTO> categoryDTOs = categories.stream()
                    .map(this::convertToCategoryDTO)
                    .collect(Collectors.toList());

            return Result.success(categoryDTOs);

        } catch (Exception e) {
            log.error("获取知识类型可用分类失败", e);
            return Result.errorResult("AVAILABLE_CATEGORIES_QUERY_ERROR", "获取知识类型可用分类失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<CategoryDTO>> getCategoriesBySubType(GetCategoriesBySubTypeRequest request) {
        log.info("调用getCategoriesBySubType方法 - 参数: request={}", request);
        try {
            // 从请求对象中提取参数
            String contentCategory = request != null ? request.getContentCategory() : null;
            Long subTypeId = request != null ? request.getSubTypeId() : null;

            // 注意：当前数据库表中没有sub_type_id字段，这里先按照内容类别查询
            log.warn("当前数据库表不支持sub_type_id字段，使用内容类别查询");
            List<Category> categories = categoryMapper.selectByContentCategory(contentCategory, true);
            List<CategoryDTO> categoryDTOs = categories.stream()
                    .map(this::convertToCategoryDTO)
                    .collect(Collectors.toList());

            return Result.success(categoryDTOs);

        } catch (Exception e) {
            log.error("根据细分类型获取分类失败", e);
            return Result.errorResult("CATEGORIES_BY_SUBTYPE_QUERY_ERROR", "根据细分类型获取分类失败: " + e.getMessage());
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 递归构建分类树
     */
    private CategoryDTO buildCategoryTree(CategoryDTO parent, String contentCategory, Boolean isActive) {
        List<Category> children = categoryMapper.selectByParentId(parent.getId());
        if (!children.isEmpty()) {
            List<CategoryDTO> childrenDTOs = children.stream()
                    .filter(child -> isActive == null || child.getIsActive().equals(isActive))
                    .map(this::convertToCategoryDTO)
                    .map(dto -> buildCategoryTree(dto, contentCategory, isActive))
                    .collect(Collectors.toList());
            parent.setChildren(childrenDTOs);
        }
        return parent;
    }

    /**
     * 检查是否为子孙分类（防止循环引用）
     */
    private boolean isDescendantOf(Long potentialDescendant, Long ancestorId) {
        if (potentialDescendant == null || ancestorId == null) {
            return false;
        }

        Category category = categoryMapper.selectById(potentialDescendant);
        if (category == null || category.getParentId() == null) {
            return false;
        }

        if (category.getParentId().equals(ancestorId)) {
            return true;
        }

        return isDescendantOf(category.getParentId(), ancestorId);
    }

    /**
     * 将Category实体转换为CategoryDTO
     */
    private CategoryDTO convertToCategoryDTO(Category entity) {
        if (entity == null) {
            return null;
        }

        CategoryDTO dto = new CategoryDTO();
        BeanUtils.copyProperties(entity, dto);

        // 如果有父分类，获取父分类名称
        if (entity.getParentId() != null) {
            Category parentCategory = categoryMapper.selectById(entity.getParentId());
            if (parentCategory != null) {
                dto.setParentName(parentCategory.getName());
            }
        }

        return dto;
    }

    /**
     * 将CategoryDTO转换为Category实体
     */
    private Category convertToCategory(CategoryDTO dto) {
        if (dto == null) {
            return null;
        }

        Category entity = new Category();
        BeanUtils.copyProperties(dto, entity);

        return entity;
    }
}
