package com.jdl.aic.core.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO;
import com.jdl.aic.core.service.client.dto.request.knowledge.GetKnowledgeListRequest;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.dao.entity.primary.Knowledge;
import com.jdl.aic.core.service.dao.entity.primary.KnowledgeType;
import com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation;
import com.jdl.aic.core.service.dao.mapper.primary.KnowledgeMapper;
import com.jdl.aic.core.service.dao.mapper.primary.KnowledgeTypeMapper;
import com.jdl.aic.core.service.dao.mapper.primary.CategoryMapper;
import com.jdl.aic.core.service.dao.mapper.primary.ContentCategoryRelationMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service("knowledgeService")
@Transactional
public class KnowledgeServiceImpl implements KnowledgeService {

    @Resource
    private KnowledgeMapper knowledgeMapper;

    @Resource
    private KnowledgeTypeMapper knowledgeTypeMapper;

    @Resource
    private CategoryMapper categoryMapper;

    @Resource
    private ContentCategoryRelationMapper contentCategoryRelationMapper;
    // ==================== 知识类型管理 ====================

    @Override
    public Result<PageResult<KnowledgeTypeDTO>> getKnowledgeTypeList(PageRequest pageRequest, Boolean isActive, String search) {
        log.info("开始获取知识类型列表 - 入参: pageRequest={}, isActive={}, search={}", pageRequest, isActive, search);
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            KnowledgeType queryCondition = new KnowledgeType();
            if (isActive != null) {
                queryCondition.setIsActive(isActive);
            }
            log.debug("构建查询条件完成: {}", queryCondition);

            // 查询知识类型列表
            List<KnowledgeType> knowledgeTypes = knowledgeTypeMapper.selectByCondition(queryCondition);
            log.info("数据库查询完成，查询到 {} 条知识类型记录", knowledgeTypes.size());

            // 使用PageInfo获取分页信息
            PageInfo<KnowledgeType> pageInfo = new PageInfo<>(knowledgeTypes);

            // 转换为DTO
            List<KnowledgeTypeDTO> dtoList = knowledgeTypes.stream()
                    .map(this::convertToKnowledgeTypeDTO)
                    .collect(Collectors.toList());

            PageResult<KnowledgeTypeDTO> pageResult = PageResult.of(dtoList, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());
            log.info("获取知识类型列表成功 - 返回 {} 条记录，总数: {}", dtoList.size(), pageInfo.getTotal());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取知识类型列表失败 - 入参: pageRequest={}, isActive={}, search={}, 错误信息: {}",
                    pageRequest, isActive, search, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_TYPE_QUERY_ERROR", "查询知识类型列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<KnowledgeTypeDTO> getKnowledgeTypeById(Long id) {
        log.info("开始根据ID获取知识类型详情 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 知识类型ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识类型ID不能为空");
            }

            KnowledgeType knowledgeType = knowledgeTypeMapper.selectById(id);
            if (knowledgeType == null) {
                log.warn("知识类型不存在 - id: {}", id);
                return Result.errorResult("KNOWLEDGE_TYPE_NOT_FOUND", "知识类型不存在");
            }

            log.info("查询知识类型成功 - id: {}, name: {}, code: {}", id, knowledgeType.getName(), knowledgeType.getCode());
            KnowledgeTypeDTO dto = convertToKnowledgeTypeDTO(knowledgeType);
            return Result.success(dto);

        } catch (Exception e) {
            log.error("根据ID获取知识类型详情失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_TYPE_QUERY_ERROR", "查询知识类型详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<KnowledgeTypeDTO> getKnowledgeTypeByCode(String code) {
        log.info("开始根据编码获取知识类型详情 - 入参: code={}", code);
        try {
            if (!StringUtils.hasText(code)) {
                log.warn("参数校验失败 - 知识类型编码不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识类型编码不能为空");
            }

            KnowledgeType knowledgeType = knowledgeTypeMapper.selectByCode(code);
            if (knowledgeType == null) {
                log.warn("知识类型不存在 - code: {}", code);
                return Result.errorResult("KNOWLEDGE_TYPE_NOT_FOUND", "知识类型不存在");
            }

            log.info("根据编码查询知识类型成功 - code: {}, id: {}, name: {}", code, knowledgeType.getId(), knowledgeType.getName());
            KnowledgeTypeDTO dto = convertToKnowledgeTypeDTO(knowledgeType);
            return Result.success(dto);

        } catch (Exception e) {
            log.error("根据编码获取知识类型详情失败 - 入参: code={}, 错误信息: {}", code, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_TYPE_QUERY_ERROR", "查询知识类型详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<KnowledgeTypeDTO> createKnowledgeType(KnowledgeTypeDTO knowledgeType) {
        log.info("开始创建知识类型 - 入参: {}", knowledgeType);
        try {
            if (knowledgeType == null) {
                log.warn("参数校验失败 - 知识类型信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识类型信息不能为空");
            }

            // 验证必填字段
            if (!StringUtils.hasText(knowledgeType.getName())) {
                log.warn("参数校验失败 - 知识类型名称不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识类型名称不能为空");
            }
            if (!StringUtils.hasText(knowledgeType.getCode())) {
                log.warn("参数校验失败 - 知识类型编码不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识类型编码不能为空");
            }

            // 检查编码是否已存在
            KnowledgeType existingType = knowledgeTypeMapper.selectByCode(knowledgeType.getCode());
            if (existingType != null) {
                log.warn("知识类型编码已存在 - code: {}, existingId: {}", knowledgeType.getCode(), existingType.getId());
                return Result.errorResult("KNOWLEDGE_TYPE_CODE_EXISTS", "知识类型编码已存在");
            }
            log.debug("编码唯一性校验通过 - code: {}", knowledgeType.getCode());

            // 转换为实体并设置默认值
            KnowledgeType entity = convertToKnowledgeTypeEntity(knowledgeType);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            if (entity.getIsActive() == null) {
                entity.setIsActive(true);
            }
            log.debug("实体转换完成，准备插入数据库 - name: {}, code: {}, isActive: {}",
                    entity.getName(), entity.getCode(), entity.getIsActive());

            // 插入数据库
            int result = knowledgeTypeMapper.insert(entity);
            if (result > 0) {
                log.info("知识类型创建成功 - id: {}, name: {}, code: {}", entity.getId(), entity.getName(), entity.getCode());
                KnowledgeTypeDTO resultDto = convertToKnowledgeTypeDTO(entity);
                return Result.success("知识类型创建成功", resultDto);
            } else {
                log.error("知识类型创建失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("KNOWLEDGE_TYPE_CREATE_FAILED", "知识类型创建失败");
            }

        } catch (Exception e) {
            log.error("创建知识类型失败 - 入参: {}, 错误信息: {}", knowledgeType, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_TYPE_CREATE_ERROR", "创建知识类型失败: " + e.getMessage());
        }
    }

    @Override
    public Result<KnowledgeTypeDTO> updateKnowledgeType(Long id, KnowledgeTypeDTO knowledgeType) {
        log.info("开始更新知识类型 - 入参: id={}, knowledgeType={}", id, knowledgeType);
        try {
            if (id == null || knowledgeType == null) {
                log.warn("参数校验失败 - 参数不能为空, id: {}, knowledgeType: {}", id, knowledgeType);
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 检查知识类型是否存在
            KnowledgeType existingType = knowledgeTypeMapper.selectById(id);
            if (existingType == null) {
                log.warn("知识类型不存在 - id: {}", id);
                return Result.errorResult("KNOWLEDGE_TYPE_NOT_FOUND", "知识类型不存在");
            }
            log.debug("查询到待更新的知识类型 - id: {}, name: {}, code: {}", id, existingType.getName(), existingType.getCode());

            // 如果更新编码，检查新编码是否已被其他记录使用
            if (StringUtils.hasText(knowledgeType.getCode()) && !knowledgeType.getCode().equals(existingType.getCode())) {
                KnowledgeType codeCheck = knowledgeTypeMapper.selectByCode(knowledgeType.getCode());
                if (codeCheck != null && !codeCheck.getId().equals(id)) {
                    log.warn("知识类型编码已存在 - newCode: {}, existingId: {}, currentId: {}",
                            knowledgeType.getCode(), codeCheck.getId(), id);
                    return Result.errorResult("KNOWLEDGE_TYPE_CODE_EXISTS", "知识类型编码已存在");
                }
                log.debug("编码更新校验通过 - oldCode: {}, newCode: {}", existingType.getCode(), knowledgeType.getCode());
            }

            // 更新实体 - 排除JSON字段，单独处理
            BeanUtils.copyProperties(knowledgeType, existingType, "id", "createdAt", "createdBy",
                    "communityConfigJson", "renderConfigJson", "metadataSchema");

            // 处理JSON字段的转换
            if (knowledgeType.getCommunityConfigJson() != null && !knowledgeType.getCommunityConfigJson().isEmpty()) {
                existingType.setCommunityConfigJson(JSON.toJSONString(knowledgeType.getCommunityConfigJson()));
            }
            if (knowledgeType.getRenderConfigJson() != null && !knowledgeType.getRenderConfigJson().isEmpty()) {
                existingType.setRenderConfigJson(JSON.toJSONString(knowledgeType.getRenderConfigJson()));
            }
            if (knowledgeType.getMetadataSchema() != null && !knowledgeType.getMetadataSchema().isEmpty()) {
                existingType.setMetadataSchema(JSON.toJSONString(knowledgeType.getMetadataSchema()));
            }

            existingType.setUpdatedAt(LocalDateTime.now());
            log.debug("实体属性复制完成，准备更新数据库 - id: {}, name: {}, code: {}",
                    existingType.getId(), existingType.getName(), existingType.getCode());

            int result = knowledgeTypeMapper.updateById(existingType);
            if (result > 0) {
                log.info("知识类型更新成功 - id: {}, name: {}, code: {}",
                        existingType.getId(), existingType.getName(), existingType.getCode());
                KnowledgeTypeDTO resultDto = convertToKnowledgeTypeDTO(existingType);
                return Result.success("知识类型更新成功", resultDto);
            } else {
                log.error("知识类型更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("KNOWLEDGE_TYPE_UPDATE_FAILED", "知识类型更新失败");
            }

        } catch (Exception e) {
            log.error("更新知识类型失败 - 入参: id={}, knowledgeType={}, 错误信息: {}", id, knowledgeType, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_TYPE_UPDATE_ERROR", "更新知识类型失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteKnowledgeType(Long id) {
        log.info("开始删除知识类型 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 知识类型ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识类型ID不能为空");
            }

            // 检查知识类型是否存在
            KnowledgeType existingType = knowledgeTypeMapper.selectById(id);
            if (existingType == null) {
                log.warn("知识类型不存在 - id: {}", id);
                return Result.errorResult("KNOWLEDGE_TYPE_NOT_FOUND", "知识类型不存在");
            }
            log.debug("查询到待删除的知识类型 - id: {}, name: {}, code: {}", id, existingType.getName(), existingType.getCode());

            // 检查是否有关联的知识内容
            List<Knowledge> relatedKnowledge = knowledgeMapper.selectByKnowledgeTypeId(id);
            if (!relatedKnowledge.isEmpty()) {
                log.warn("知识类型下存在关联内容，无法删除 - id: {}, 关联内容数量: {}", id, relatedKnowledge.size());
                return Result.errorResult("KNOWLEDGE_TYPE_HAS_CONTENT", "该知识类型下还有知识内容，无法删除");
            }
            log.debug("关联内容检查通过，可以删除 - id: {}", id);

            int result = knowledgeTypeMapper.deleteById(id);
            if (result > 0) {
                log.info("知识类型删除成功 - id: {}, name: {}, code: {}", id, existingType.getName(), existingType.getCode());
                return Result.success();
            } else {
                log.error("知识类型删除失败 - 数据库删除返回结果: {}, id: {}", result, id);
                return Result.errorResult("KNOWLEDGE_TYPE_DELETE_FAILED", "知识类型删除失败");
            }

        } catch (Exception e) {
            log.error("删除知识类型失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_TYPE_DELETE_ERROR", "删除知识类型失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> toggleKnowledgeTypeStatus(Long id, Boolean isActive) {
        log.info("开始切换知识类型状态 - 入参: id={}, isActive={}", id, isActive);
        try {
            if (id == null || isActive == null) {
                log.warn("参数校验失败 - 参数不能为空, id: {}, isActive: {}", id, isActive);
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            KnowledgeType existingType = knowledgeTypeMapper.selectById(id);
            if (existingType == null) {
                log.warn("知识类型不存在 - id: {}", id);
                return Result.errorResult("KNOWLEDGE_TYPE_NOT_FOUND", "知识类型不存在");
            }
            log.debug("查询到知识类型 - id: {}, name: {}, 当前状态: {}, 目标状态: {}",
                    id, existingType.getName(), existingType.getIsActive(), isActive);

            existingType.setIsActive(isActive);
            existingType.setUpdatedAt(LocalDateTime.now());

            int result = knowledgeTypeMapper.updateById(existingType);
            if (result > 0) {
                log.info("知识类型状态更新成功 - id: {}, name: {}, 新状态: {}", id, existingType.getName(), isActive);
                return Result.success();
            } else {
                log.error("知识类型状态更新失败 - 数据库更新返回结果: {}, id: {}", result, id);
                return Result.errorResult("KNOWLEDGE_TYPE_STATUS_UPDATE_FAILED", "知识类型状态更新失败");
            }

        } catch (Exception e) {
            log.error("更新知识类型状态失败 - 入参: id={}, isActive={}, 错误信息: {}", id, isActive, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_TYPE_STATUS_UPDATE_ERROR", "更新知识类型状态失败: " + e.getMessage());
        }
    }

    // ==================== 知识内容管理 ====================

    @Override
    public Result<PageResult<KnowledgeDTO>> getKnowledgeList(PageRequest pageRequest, GetKnowledgeListRequest request) {
        try {
            // 从请求对象中提取参数
            Long knowledgeTypeId = request != null ? request.getKnowledgeTypeId() : null;
            Integer status = request != null ? request.getStatus() : null;
            Integer visibility = request != null ? request.getVisibility() : null;
            String authorId = request != null ? request.getAuthorId() : null;
            Long teamId = request != null ? request.getTeamId() : null;
            Long categoryId = request != null ? request.getCategoryId() : null;
            String search = request != null ? request.getSearch() : null;

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            Knowledge queryCondition = new Knowledge();
            if (status != null) {
                queryCondition.setStatus(status);
            }
            if (authorId != null) {
                queryCondition.setAuthorId(authorId);
            }
            if (teamId != null) {
                queryCondition.setTeamId(teamId);
            }

            // 使用优化的复合条件查询
            List<Knowledge> knowledgeList = knowledgeMapper.selectByComplexCondition(
                    knowledgeTypeId, status, visibility, authorId, teamId, categoryId, search);

            log.info("数据库查询完成，查询到 {} 条知识记录", knowledgeList.size());

            // 使用PageInfo获取分页信息
            PageInfo<Knowledge> pageInfo = new PageInfo<>(knowledgeList);

            // 转换为DTO
            List<KnowledgeDTO> dtoList = knowledgeList.stream()
                    .map(this::convertToKnowledgeDTO)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dtoList)) {
                for (KnowledgeDTO knowledgeDTO : dtoList) {
                    List<ContentCategoryRelation> relations = contentCategoryRelationMapper.selectByContentIdAndType(knowledgeDTO.getId(), "knowledge");
                    if (!CollectionUtils.isEmpty(relations)) {
                        List<ContentCategoryRelationDTO> categoryIds = relations.stream()
                                .map(item -> {
                                    ContentCategoryRelationDTO dto = new ContentCategoryRelationDTO();
                                    dto.setCategoryId(item.getCategoryId());
                                    return dto;
                                })
                                .collect(Collectors.toList());
                        knowledgeDTO.setCategories(categoryIds);
                    }
                }
            }

            PageResult<KnowledgeDTO> pageResult = PageResult.of(dtoList, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());
            return Result.success(pageResult);

        } catch (Exception e) {
            return Result.errorResult("KNOWLEDGE_QUERY_ERROR", "查询知识列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<KnowledgeDTO> getKnowledgeById(Long id) {
        log.info("开始根据ID获取知识详情 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 知识ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识ID不能为空");
            }

            Knowledge knowledge = knowledgeMapper.selectById(id);
            if (knowledge == null) {
                log.warn("知识不存在 - id: {}", id);
                return Result.errorResult("KNOWLEDGE_NOT_FOUND", "知识不存在");
            }

            log.info("查询知识成功 - id: {}, title: {}, status: {}, authorId: {}",
                    id, knowledge.getTitle(), knowledge.getStatus(), knowledge.getAuthorId());
            KnowledgeDTO knowledgeDTO = convertToKnowledgeDTO(knowledge);
            return Result.success(knowledgeDTO);

        } catch (Exception e) {
            log.error("根据ID获取知识详情失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_QUERY_ERROR", "查询知识详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<KnowledgeDTO> createKnowledge(KnowledgeDTO knowledge) {
        log.info("开始创建知识内容 - 入参: title={}, knowledgeTypeId={}, authorId={}",
                knowledge != null ? knowledge.getTitle() : null,
                knowledge != null ? knowledge.getKnowledgeTypeId() : null,
                knowledge != null ? knowledge.getAuthorId() : null);
        try {
            if (knowledge == null) {
                log.warn("参数校验失败 - 知识信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识信息不能为空");
            }

            // 验证必填字段
            if (!StringUtils.hasText(knowledge.getTitle())) {
                log.warn("参数校验失败 - 知识标题不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识标题不能为空");
            }
            if (knowledge.getKnowledgeTypeId() == null) {
                log.warn("参数校验失败 - 知识类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识类型不能为空");
            }

            // 验证知识类型是否存在
            KnowledgeType knowledgeType = knowledgeTypeMapper.selectById(knowledge.getKnowledgeTypeId());
            if (knowledgeType == null) {
                log.warn("知识类型不存在 - knowledgeTypeId: {}", knowledge.getKnowledgeTypeId());
                return Result.errorResult("KNOWLEDGE_TYPE_NOT_FOUND", "知识类型不存在");
            }
            log.debug("知识类型校验通过 - knowledgeTypeId: {}, typeName: {}", knowledge.getKnowledgeTypeId(), knowledgeType.getName());

            // 转换为实体并设置默认值
            Knowledge entity = convertToKnowledgeEntity(knowledge);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            if (entity.getStatus() == null) {
                entity.setStatus(0); // 默认为草稿状态
            }
            if (entity.getVisibility() == null) {
                entity.setVisibility(0); // 默认为私有
            }
            if (entity.getReadCount() == null) {
                entity.setReadCount(0);
            }
            if (entity.getLikeCount() == null) {
                entity.setLikeCount(0);
            }
            if (entity.getCommentCount() == null) {
                entity.setCommentCount(0);
            }
            if (entity.getForkCount() == null) {
                entity.setForkCount(0);
            }
            log.debug("实体转换和默认值设置完成 - title: {}, status: {}, visibility: {}",
                    entity.getTitle(), entity.getStatus(), entity.getVisibility());

            // 插入数据库
            int result = knowledgeMapper.insert(entity);
            if (result > 0) {
                log.info("知识内容创建成功 - id: {}, title: {}, knowledgeTypeId: {}, authorId: {}",
                        entity.getId(), entity.getTitle(), entity.getKnowledgeTypeId(), entity.getAuthorId());

                // 处理分类关系
                saveCategoryRelations(entity.getId(), knowledge.getCategories());

                KnowledgeDTO resultDto = convertToKnowledgeDTO(entity);
                return Result.success("知识创建成功", resultDto);
            } else {
                log.error("知识内容创建失败 - 数据库插入返回结果: {}, title: {}", result, entity.getTitle());
                return Result.errorResult("KNOWLEDGE_CREATE_FAILED", "知识创建失败");
            }

        } catch (Exception e) {
            log.error("创建知识内容失败 - 入参: title={}, knowledgeTypeId={}, 错误信息: {}",
                    knowledge != null ? knowledge.getTitle() : null,
                    knowledge != null ? knowledge.getKnowledgeTypeId() : null,
                    e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_CREATE_ERROR", "创建知识失败: " + e.getMessage());
        }
    }

    @Override
    public Result<KnowledgeDTO> updateKnowledge(Long id, KnowledgeDTO knowledge) {
        try {
            if (id == null || knowledge == null) {
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 检查知识是否存在
            Knowledge existingKnowledge = knowledgeMapper.selectById(id);
            if (existingKnowledge == null) {
                return Result.errorResult("KNOWLEDGE_NOT_FOUND", "知识不存在");
            }

            // 如果更新知识类型，验证新类型是否存在
            if (knowledge.getKnowledgeTypeId() != null && !knowledge.getKnowledgeTypeId().equals(existingKnowledge.getKnowledgeTypeId())) {
                KnowledgeType knowledgeType = knowledgeTypeMapper.selectById(knowledge.getKnowledgeTypeId());
                if (knowledgeType == null) {
                    return Result.errorResult("KNOWLEDGE_TYPE_NOT_FOUND", "知识类型不存在");
                }
            }

            // 更新实体
            BeanUtils.copyProperties(knowledge, existingKnowledge, "id", "createdAt", "createdBy", "readCount", "likeCount", "commentCount", "forkCount", "favoriteCount", "shareCount", "socialScore", "lastSocialActivityAt");
            existingKnowledge.setUpdatedAt(LocalDateTime.now());

            existingKnowledge.setMetadataJson(JSON.toJSONString(knowledge.getMetadataJson()));
            existingKnowledge.setAiTagsJson(JSON.toJSONString(knowledge.getAiTags()));


            int result = knowledgeMapper.updateById(existingKnowledge);
            if (result > 0) {
                // 处理分类关系
                saveCategoryRelations(id, knowledge.getCategories());

                KnowledgeDTO resultDto = convertToKnowledgeDTO(existingKnowledge);
                return Result.success("知识更新成功", resultDto);
            } else {
                return Result.errorResult("KNOWLEDGE_UPDATE_FAILED", "知识更新失败");
            }

        } catch (Exception e) {
            return Result.errorResult("KNOWLEDGE_UPDATE_ERROR", "更新知识失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteKnowledge(Long id) {
        try {
            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "知识ID不能为空");
            }

            // 检查知识是否存在
            Knowledge existingKnowledge = knowledgeMapper.selectById(id);
            if (existingKnowledge == null) {
                return Result.errorResult("KNOWLEDGE_NOT_FOUND", "知识不存在");
            }

            int result = knowledgeMapper.deleteById(id);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.errorResult("KNOWLEDGE_DELETE_FAILED", "知识删除失败");
            }

        } catch (Exception e) {
            return Result.errorResult("KNOWLEDGE_DELETE_ERROR", "删除知识失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> updateKnowledgeStatus(Long id, Integer status) {
        try {
            if (id == null || status == null) {
                return Result.errorResult("INVALID_PARAMETER", "参数不能为空");
            }

            // 验证状态值
            if (status < 0 || status > 4) {
                return Result.errorResult("INVALID_PARAMETER", "状态值无效");
            }

            Knowledge existingKnowledge = knowledgeMapper.selectById(id);
            if (existingKnowledge == null) {
                return Result.errorResult("KNOWLEDGE_NOT_FOUND", "知识不存在");
            }

            existingKnowledge.setStatus(status);
            existingKnowledge.setUpdatedAt(LocalDateTime.now());

            int result = knowledgeMapper.updateById(existingKnowledge);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.errorResult("KNOWLEDGE_STATUS_UPDATE_FAILED", "知识状态更新失败");
            }

        } catch (Exception e) {
            return Result.errorResult("KNOWLEDGE_STATUS_UPDATE_ERROR", "更新知识状态失败: " + e.getMessage());
        }
    }

    // ==================== 搜索和其他功能 ====================

    @Override
    public Result<PageResult<KnowledgeDTO>> searchKnowledge(String keyword, PageRequest pageRequest, String knowledgeTypeCode, Integer status, Integer visibility) {
        log.info("开始搜索知识内容 - 入参: keyword={}, pageRequest={}, knowledgeTypeCode={}, status={}, visibility={}",
                keyword, pageRequest, knowledgeTypeCode, status, visibility);
        try {
            if (!StringUtils.hasText(keyword)) {
                log.warn("参数校验失败 - 搜索关键词不能为空");
                return Result.errorResult("INVALID_PARAMETER", "搜索关键词不能为空");
            }

            // 根据知识类型编码获取知识类型ID
            Long knowledgeTypeId = null;
            if (StringUtils.hasText(knowledgeTypeCode)) {
                KnowledgeType knowledgeType = knowledgeTypeMapper.selectByCode(knowledgeTypeCode);
                if (knowledgeType != null) {
                    knowledgeTypeId = knowledgeType.getId();
                    log.debug("知识类型编码转换完成 - 编码: {}, ID: {}", knowledgeTypeCode, knowledgeTypeId);
                } else {
                    log.warn("知识类型不存在，返回空结果 - knowledgeTypeCode: {}", knowledgeTypeCode);
                    return Result.success(PageResult.of(new ArrayList<>(), 0, pageRequest.getPage(), pageRequest.getSize()));
                }
            }

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 使用复合条件查询，包含搜索关键词
            List<Knowledge> knowledgeList = knowledgeMapper.selectByComplexCondition(
                    knowledgeTypeId, status, visibility, null, null, null, keyword);
            log.info("数据库查询完成，查询到 {} 条知识记录", knowledgeList.size());

            // 使用PageInfo获取分页信息
            PageInfo<Knowledge> pageInfo = new PageInfo<>(knowledgeList);

            // 转换为DTO
            List<KnowledgeDTO> dtoList = knowledgeList.stream()
                    .map(this::convertToKnowledgeDTO)
                    .collect(Collectors.toList());

            PageResult<KnowledgeDTO> pageResult = PageResult.of(dtoList, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());
            log.info("搜索知识内容成功 - 关键词: {}, 返回 {} 条记录，总数: {}", keyword, dtoList.size(), pageInfo.getTotal());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("搜索知识内容失败 - 入参: keyword={}, knowledgeTypeCode={}, status={}, visibility={}, 错误信息: {}",
                    keyword, knowledgeTypeCode, status, visibility, e.getMessage(), e);
            return Result.errorResult("KNOWLEDGE_SEARCH_ERROR", "搜索知识失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> incrementReadCount(Long id) {
        log.info("开始增加知识阅读次数 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 知识ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识ID不能为空");
            }

            Knowledge existingKnowledge = knowledgeMapper.selectById(id);
            if (existingKnowledge == null) {
                log.warn("知识不存在 - id: {}", id);
                return Result.errorResult("KNOWLEDGE_NOT_FOUND", "知识不存在");
            }

            Integer oldReadCount = existingKnowledge.getReadCount();
            int result = knowledgeMapper.incrementReadCount(id);
            if (result > 0) {
                log.info("知识阅读次数更新成功 - id: {}, title: {}, 阅读次数: {} -> {}",
                        id, existingKnowledge.getTitle(), oldReadCount, oldReadCount != null ? oldReadCount + 1 : 1);
                return Result.success();
            } else {
                log.error("知识阅读次数更新失败 - id: {}, 数据库更新返回: {}", id, result);
                return Result.errorResult("READ_COUNT_UPDATE_FAILED", "阅读次数更新失败");
            }

        } catch (Exception e) {
            log.error("更新知识阅读次数失败 - 入参: id={}, 错误信息: {}", id, e.getMessage(), e);
            return Result.errorResult("READ_COUNT_UPDATE_ERROR", "更新阅读次数失败: " + e.getMessage());
        }
    }

    // ==================== 批量操作 ====================

    @Override
    public Result<Void> batchUpdateKnowledgeStatus(List<Long> ids, Integer status) {
        log.info("开始批量更新知识状态 - 入参: ids={}, status={}, 数量: {}", ids, status, ids != null ? ids.size() : 0);
        try {
            if (ids == null || ids.isEmpty()) {
                log.warn("参数校验失败 - 知识ID列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识ID列表不能为空");
            }
            if (status == null) {
                log.warn("参数校验失败 - 状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "状态不能为空");
            }

            // 验证状态值
            if (status < 0 || status > 4) {
                log.warn("参数校验失败 - 状态值无效: {}", status);
                return Result.errorResult("INVALID_PARAMETER", "状态值无效");
            }

            int successCount = 0;
            int failedCount = 0;
            for (Long id : ids) {
                try {
                    Knowledge existingKnowledge = knowledgeMapper.selectById(id);
                    if (existingKnowledge != null) {
                        Integer oldStatus = existingKnowledge.getStatus();
                        existingKnowledge.setStatus(status);
                        existingKnowledge.setUpdatedAt(LocalDateTime.now());
                        int result = knowledgeMapper.updateById(existingKnowledge);
                        if (result > 0) {
                            successCount++;
                            log.debug("知识状态更新成功 - id: {}, title: {}, 状态: {} -> {}",
                                    id, existingKnowledge.getTitle(), oldStatus, status);
                        } else {
                            failedCount++;
                            log.warn("知识状态更新失败 - id: {}, 数据库更新返回: {}", id, result);
                        }
                    } else {
                        failedCount++;
                        log.warn("知识不存在，跳过更新 - id: {}", id);
                    }
                } catch (Exception e) {
                    failedCount++;
                    log.error("更新知识状态失败 - id: {}, 错误信息: {}", id, e.getMessage(), e);
                }
            }

            log.info("批量更新知识状态完成 - 总数: {}, 成功: {}, 失败: {}", ids.size(), successCount, failedCount);
            if (successCount > 0) {
                return Result.success();
            } else {
                return Result.errorResult("BATCH_UPDATE_FAILED", "批量更新失败");
            }

        } catch (Exception e) {
            log.error("批量更新知识状态失败 - 入参: ids={}, status={}, 错误信息: {}", ids, status, e.getMessage(), e);
            return Result.errorResult("BATCH_UPDATE_ERROR", "批量更新知识状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchDeleteKnowledge(List<Long> ids) {
        log.info("开始批量删除知识内容 - 入参: ids={}, 数量: {}", ids, ids != null ? ids.size() : 0);
        try {
            if (ids == null || ids.isEmpty()) {
                log.warn("参数校验失败 - 知识ID列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "知识ID列表不能为空");
            }

            int successCount = 0;
            int failedCount = 0;
            for (Long id : ids) {
                try {
                    Knowledge existingKnowledge = knowledgeMapper.selectById(id);
                    if (existingKnowledge != null) {
                        int result = knowledgeMapper.deleteById(id);
                        if (result > 0) {
                            successCount++;
                            log.debug("知识删除成功 - id: {}, title: {}", id, existingKnowledge.getTitle());
                        } else {
                            failedCount++;
                            log.warn("知识删除失败 - id: {}, 数据库删除返回: {}", id, result);
                        }
                    } else {
                        failedCount++;
                        log.warn("知识不存在，跳过删除 - id: {}", id);
                    }
                } catch (Exception e) {
                    failedCount++;
                    log.error("删除知识失败 - id: {}, 错误信息: {}", id, e.getMessage(), e);
                }
            }

            log.info("批量删除知识内容完成 - 总数: {}, 成功: {}, 失败: {}", ids.size(), successCount, failedCount);
            if (successCount > 0) {
                return Result.success();
            } else {
                return Result.errorResult("BATCH_DELETE_FAILED", "批量删除失败");
            }

        } catch (Exception e) {
            log.error("批量删除知识内容失败 - 入参: ids={}, 错误信息: {}", ids, e.getMessage(), e);
            return Result.errorResult("BATCH_DELETE_ERROR", "批量删除知识失败: " + e.getMessage());
        }
    }

    // ==================== 版本管理 ====================

    @Override
    public Result<PageResult<Object>> getKnowledgeVersionHistory(Long knowledgeId, PageRequest pageRequest) {
        // TODO: 实现版本历史查询功能
        return Result.errorResult("NOT_IMPLEMENTED", "版本历史查询功能暂未实现");
    }

    @Override
    public Result<Object> createKnowledgeVersion(Long knowledgeId, String versionComment) {
        // TODO: 实现版本管理功能
        return Result.errorResult("NOT_IMPLEMENTED", "版本管理功能暂未实现");
    }

    // ==================== 私有工具方法 ====================

    /**
     * 将KnowledgeType实体转换为KnowledgeTypeDTO
     */
    private KnowledgeTypeDTO convertToKnowledgeTypeDTO(KnowledgeType entity) {
        if (entity == null) {
            return null;
        }

        KnowledgeTypeDTO dto = new KnowledgeTypeDTO();
        BeanUtils.copyProperties(entity, dto);

        // 处理JSON字段 - 这里暂时设置为空Map，实际项目中需要解析JSON
        if (dto.getMetadataSchema() == null) {
            dto.setMetadataSchema(new HashMap<>());
        }
        if (dto.getRenderConfigJson() == null) {
            dto.setRenderConfigJson(new HashMap<>());
        }
        if (dto.getCommunityConfigJson() == null) {
            dto.setCommunityConfigJson(new HashMap<>());
        }

        // 处理从实体类中的JSON字符串字段到DTO的Map字段的转换
        // 这里暂时设置为空Map，实际项目中需要解析JSON字符串
        if (entity.getCommunityConfigJson() != null && !entity.getCommunityConfigJson().isEmpty()) {
            dto.setCommunityConfigJson(JSON.parseObject(entity.getCommunityConfigJson(), Map.class));
        }
        if (entity.getRenderConfigJson() != null && !entity.getRenderConfigJson().isEmpty()) {
            dto.setRenderConfigJson(JSON.parseObject(entity.getRenderConfigJson(), Map.class));
        }
        if (entity.getMetadataSchema() != null && !entity.getMetadataSchema().isEmpty()) {
            dto.setMetadataSchema(JSON.parseObject(entity.getMetadataSchema(), Map.class));
        }

        return dto;
    }

    /**
     * 将KnowledgeTypeDTO转换为KnowledgeType实体
     */
    private KnowledgeType convertToKnowledgeTypeEntity(KnowledgeTypeDTO dto) {
        if (dto == null) {
            return null;
        }

        KnowledgeType entity = new KnowledgeType();
        BeanUtils.copyProperties(dto, entity, "id", "communityConfigJson", "renderConfigJson", "metadataSchema");

        // 处理从DTO的Map字段到实体类的JSON字符串字段的转换
        // 这里暂时设置为空字符串，实际项目中需要将Map序列化为JSON字符串
        if (dto.getCommunityConfigJson() != null && !dto.getCommunityConfigJson().isEmpty()) {
            entity.setCommunityConfigJson(JSON.toJSONString(dto.getCommunityConfigJson()));
        }
        if (dto.getRenderConfigJson() != null && !dto.getRenderConfigJson().isEmpty()) {
            entity.setRenderConfigJson(JSON.toJSONString(dto.getRenderConfigJson()));
        }
        if (dto.getMetadataSchema() != null && !dto.getMetadataSchema().isEmpty()) {
            entity.setMetadataSchema(JSON.toJSONString(dto.getMetadataSchema()));
        }

        return entity;
    }

    /**
     * 将Knowledge实体转换为KnowledgeDTO
     */
    private KnowledgeDTO convertToKnowledgeDTO(Knowledge entity) {
        if (entity == null) {
            return null;
        }

        KnowledgeDTO dto = new KnowledgeDTO();
        BeanUtils.copyProperties(entity, dto);

        // 处理JSON字段
        if (dto.getMetadataJson() == null) {
            dto.setMetadataJson(JSON.parseObject(entity.getMetadataJson()));
        }
        if (dto.getAiTags() == null) {
            dto.setAiTags(JSON.parseArray(entity.getAiTagsJson(), String.class));
        }

        // 查询并设置知识类型信息
        try {
            KnowledgeType knowledgeType = knowledgeTypeMapper.selectById(entity.getKnowledgeTypeId());
            if (knowledgeType != null) {
                dto.setKnowledgeTypeCode(knowledgeType.getCode());
                dto.setKnowledgeTypeName(knowledgeType.getName());
            }
            log.debug("查询知识类型成功 - knowledgeId: {}, knowledgeType: {}", entity.getId(), JSON.toJSONString(knowledgeType));
        } catch (Exception e) {
            log.warn("查询知识类型失败 - knowledgeId: {}, 错误信息: {}", entity.getId(), e.getMessage());
        }

        // 查询并设置知识分类信息
        try {
            List<ContentCategoryRelation> categories = knowledgeMapper.selectKnowledgeCategories(entity.getId());
            if (!CollectionUtils.isEmpty(categories)) {
                List<ContentCategoryRelationDTO> _categories = new ArrayList<>();
                dto.setCategories(_categories);
                for (ContentCategoryRelation category : categories) {
                    ContentCategoryRelationDTO categoryDTO = new ContentCategoryRelationDTO();
                    BeanUtils.copyProperties(category, categoryDTO);
                    _categories.add(categoryDTO);
                }
            }
            log.debug("查询知识分类成功 - knowledgeId: {}, 分类数量: {}", entity.getId(), categories.size());
        } catch (Exception e) {
            log.warn("查询知识分类失败 - knowledgeId: {}, 错误信息: {}", entity.getId(), e.getMessage());
            dto.setCategories(new ArrayList<>());
        }

        return dto;
    }

    /**
     * 将KnowledgeDTO转换为Knowledge实体
     */
    private Knowledge convertToKnowledgeEntity(KnowledgeDTO dto) {
        if (dto == null) {
            return null;
        }

        Knowledge entity = new Knowledge();
        BeanUtils.copyProperties(dto, entity, "id");
        entity.setMetadataJson(JSON.toJSONString(dto.getMetadataJson()));
        entity.setAiTagsJson(JSON.toJSONString(dto.getAiTags()));

        return entity;
    }

    /**
     * 保存知识分类关系
     *
     * @param knowledgeId 知识ID
     * @param categories  分类ID列表
     */
    private void saveCategoryRelations(Long knowledgeId, List<ContentCategoryRelationDTO> categories) {
        if (knowledgeId == null) {
            log.warn("知识ID为空，跳过分类关系保存");
            return;
        }

        try {
            // 先删除现有的分类关系
            contentCategoryRelationMapper.deleteByContentIdAndType(knowledgeId, ContentType.KNOWLEDGE.getValue());
            log.debug("删除知识现有分类关系 - knowledgeId: {}", knowledgeId);

            // 如果有新的分类ID，则插入新的关系
            if (categories != null && !categories.isEmpty()) {
                List<ContentCategoryRelation> relations = new ArrayList<>();
                LocalDateTime now = LocalDateTime.now();

                for (ContentCategoryRelationDTO category : categories) {
                    if (category.getCategoryId() != null) {
                        ContentCategoryRelation relation = new ContentCategoryRelation();
                        relation.setContentId(knowledgeId);
                        relation.setCategoryId(category.getCategoryId());
                        relation.setContentType("knowledge");
                        relation.setCreatedAt(now);
                        relations.add(relation);
                    }
                }

                if (!relations.isEmpty()) {
                    contentCategoryRelationMapper.batchInsert(relations);
                    log.info("保存知识分类关系成功 - knowledgeId: {}, 分类数量: {}", knowledgeId, relations.size());
                }
            }
        } catch (Exception e) {
            log.error("保存知识分类关系失败 - knowledgeId: {}, categories: {}, 错误信息: {}",
                    knowledgeId, categories, e.getMessage(), e);
            // 这里不抛出异常，避免影响主流程
        }
    }

}
