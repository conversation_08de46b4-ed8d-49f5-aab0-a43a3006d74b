package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.learning.*;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningResourceListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningPathListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetRecommendedResourcesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetPopularResourcesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.SearchLearningResourcesRequest;
import com.jdl.aic.core.service.client.service.LearningResourceService;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.dao.entity.primary.LearningResource;
import com.jdl.aic.core.service.dao.entity.primary.LearningPathResource;
import com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation;
import com.jdl.aic.core.service.dao.entity.primary.Category;
import com.jdl.aic.core.service.dao.mapper.primary.LearningResourceMapper;
import com.jdl.aic.core.service.dao.mapper.primary.LearningPathResourceMapper;
import com.jdl.aic.core.service.dao.mapper.primary.ContentCategoryRelationMapper;
import com.jdl.aic.core.service.dao.mapper.primary.CategoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;

/**
 * 学习资源服务实现类
 *
 * <p>提供学习资源管理功能的具体实现，包括：
 * <ul>
 *   <li>学习资源的CRUD操作</li>
 *   <li>学习路径管理</li>
 *   <li>学习进度跟踪</li>
 *   <li>学习资源搜索和推荐</li>
 *   <li>学习统计和分析</li>
 * </ul>
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("learningResourceService")
public class LearningResourceServiceImpl implements LearningResourceService {

    @Autowired
    private LearningResourceMapper learningResourceMapper;

    @Autowired
    private LearningPathResourceMapper learningPathResourceMapper;

    @Autowired
    private ContentCategoryRelationMapper contentCategoryRelationMapper;

    @Autowired
    private CategoryMapper categoryMapper;

    // ==================== 学习资源管理 ====================

    @Override
    public Result<PageResult<LearningResourceDTO>> getLearningResourceList(GetLearningResourceListRequest request) {

        log.info("开始获取学习资源列表 - 入参: request={}", request);

        try {
            // 参数校验
            if (request == null || request.getPageRequest() == null) {
                log.warn("参数校验失败 - 请求参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            PageRequest pageRequest = request.getPageRequest();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 查询学习资源列表
            List<LearningResource> resources = learningResourceMapper.selectByConditions(
                    request.getResourceType(), request.getCategory(), request.getDifficulty(),
                    request.getStatus(), request.getSearch());

            // 转换为PageInfo
            PageInfo<LearningResource> pageInfo = new PageInfo<>(resources);

            // 转换为DTO
            List<LearningResourceDTO> resourceDTOs = resources.stream()
                    .map(this::convertToLearningResourceDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<LearningResourceDTO> pageResult = PageResult.of(
                    resourceDTOs,
                    pageInfo.getTotal(),
                    pageRequest.getPage(),
                    pageRequest.getSize());

            log.info("获取学习资源列表成功 - 总数: {}, 当前页: {}", pageInfo.getTotal(), pageRequest.getPage());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取学习资源列表失败", e);
            return Result.errorResult("LEARNING_RESOURCE_LIST_ERROR", "获取学习资源列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<LearningResourceDTO> getLearningResourceById(Long id) {
        log.info("开始根据ID获取学习资源详情 - 入参: id={}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 学习资源ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习资源ID不能为空");
            }

            LearningResource resource = learningResourceMapper.selectById(id);
            if (resource == null) {
                log.warn("学习资源不存在 - id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_NOT_FOUND", "学习资源不存在");
            }

            LearningResourceDTO resourceDTO = convertToLearningResourceDTO(resource);

            log.info("根据ID获取学习资源详情成功 - id: {}, title: {}", id, resource.getTitle());
            return Result.success(resourceDTO);

        } catch (Exception e) {
            log.error("根据ID获取学习资源详情失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_RESOURCE_GET_ERROR", "获取学习资源详情失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<LearningResourceDTO> createLearningResource(LearningResourceDTO resource) {
        log.info("开始创建学习资源 - 入参: resource={}", resource);
        log.info("🔍 创建学习资源 - metadataJson: {}", resource.getMetadataJson());

        try {
            if (resource == null || !StringUtils.hasText(resource.getTitle())) {
                log.warn("参数校验失败 - 学习资源信息和标题不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习资源信息和标题不能为空");
            }

            // 转换为实体
            LearningResource entity = convertToLearningResource(resource);
            log.info("🔍 转换后的实体 metadata 字段: {}", entity.getMetadata());
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());

            // 插入数据库
            int result = learningResourceMapper.insert(entity);
            if (result <= 0) {
                log.error("创建学习资源失败 - 数据库插入失败");
                return Result.errorResult("LEARNING_RESOURCE_CREATE_ERROR", "创建学习资源失败");
            }

            // 处理分类关系
            saveCategoryRelations(entity.getId(), resource.getCategories());

            // 转换为DTO返回
            LearningResourceDTO createdResource = convertToLearningResourceDTO(entity);

            log.info("创建学习资源成功 - id: {}, title: {}", entity.getId(), entity.getTitle());
            return Result.success(createdResource);

        } catch (Exception e) {
            log.error("创建学习资源失败", e);
            return Result.errorResult("LEARNING_RESOURCE_CREATE_ERROR", "创建学习资源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<LearningResourceDTO> updateLearningResource(Long id, LearningResourceDTO resource) {
        log.info("开始更新学习资源 - 入参: id={}, resource={}", id, resource);
        log.info("🔍 更新学习资源 - metadataJson: {}", resource.getMetadataJson());

        try {
            if (id == null || resource == null) {
                log.warn("参数校验失败 - 学习资源ID和资源信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习资源ID和资源信息不能为空");
            }

            // 检查资源是否存在
            LearningResource existingResource = learningResourceMapper.selectById(id);
            if (existingResource == null) {
                log.warn("学习资源不存在 - id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_NOT_FOUND", "学习资源不存在");
            }

            // 转换为实体并设置ID
            LearningResource entity = convertToLearningResource(resource);
            log.info("🔍 更新转换后的实体 metadata 字段: {}", entity.getMetadata());
            entity.setId(id);
            entity.setUpdatedAt(LocalDateTime.now());

            // 更新数据库
            int result = learningResourceMapper.update(entity);
            if (result <= 0) {
                log.error("更新学习资源失败 - 数据库更新失败, id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_UPDATE_ERROR", "更新学习资源失败");
            }

            // 处理分类关系
            saveCategoryRelations(id, resource.getCategories());

            // 查询更新后的资源
            LearningResource updatedResource = learningResourceMapper.selectById(id);
            LearningResourceDTO updatedResourceDTO = convertToLearningResourceDTO(updatedResource);

            log.info("更新学习资源成功 - id: {}, title: {}", id, updatedResource.getTitle());
            return Result.success(updatedResourceDTO);

        } catch (Exception e) {
            log.error("更新学习资源失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_RESOURCE_UPDATE_ERROR", "更新学习资源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteLearningResource(Long id) {
        log.info("开始删除学习资源 - 入参: id={}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 学习资源ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习资源ID不能为空");
            }

            // 检查资源是否存在
            LearningResource existingResource = learningResourceMapper.selectById(id);
            if (existingResource == null) {
                log.warn("学习资源不存在 - id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_NOT_FOUND", "学习资源不存在");
            }

            // 软删除资源
            int result = learningResourceMapper.deleteById(id);
            if (result <= 0) {
                log.error("删除学习资源失败 - 数据库删除失败, id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_DELETE_ERROR", "删除学习资源失败");
            }

            log.info("删除学习资源成功 - id: {}", id);
            return Result.success();

        } catch (Exception e) {
            log.error("删除学习资源失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_RESOURCE_DELETE_ERROR", "删除学习资源失败: " + e.getMessage());
        }
    }

    // ==================== 数据转换方法 ====================

    /**
     * 将LearningResource实体转换为LearningResourceDTO
     */
    private LearningResourceDTO convertToLearningResourceDTO(LearningResource entity) {
        if (entity == null) {
            return null;
        }

        LearningResourceDTO dto = new LearningResourceDTO();
        BeanUtils.copyProperties(entity, dto);

        // 处理特殊字段转换
        if (entity.getEstimatedDurationHours() != null) {
            dto.setDuration(entity.getEstimatedDurationHours().multiply(new BigDecimal(60)).intValue()); // 转换为分钟
        }

        // 处理标签字符串转数组
        if (StringUtils.hasText(entity.getTags())) {
            List<String> tagList = Arrays.asList(entity.getTags().split(","));
            dto.setTags(tagList.stream().map(String::trim).collect(Collectors.toList()));
        }

        // 处理前置要求字符串转数组
        if (StringUtils.hasText(entity.getPrerequisites())) {
            List<String> prerequisitesList = Arrays.asList(entity.getPrerequisites().split(","));
            dto.setPrerequisites(prerequisitesList.stream().map(String::trim).collect(Collectors.toList()));
        }

        // 处理学习目标字符串转数组
        if (StringUtils.hasText(entity.getLearningGoals())) {
            List<String> learningObjectivesList = Arrays.asList(entity.getLearningGoals().split(","));
            dto.setLearningObjectives(learningObjectivesList.stream().map(String::trim).collect(Collectors.toList()));
        }

        // 处理JSON字段
        try {
            if (StringUtils.hasText(entity.getContentConfig())) {
                dto.setContentConfig(JSON.parseObject(entity.getContentConfig(), Map.class));
            }
            if (StringUtils.hasText(entity.getEmbedConfig())) {
                dto.setEmbedConfig(JSON.parseObject(entity.getEmbedConfig(), Map.class));
            }
            if (StringUtils.hasText(entity.getAccessConfig())) {
                dto.setAccessConfig(JSON.parseObject(entity.getAccessConfig(), Map.class));
            }
            if (StringUtils.hasText(entity.getMediaMetadata())) {
                dto.setMediaMetadata(JSON.parseObject(entity.getMediaMetadata(), Map.class));
            }
            // 处理 metadata 字段转换为 metadataJson
            if (StringUtils.hasText(entity.getMetadata())) {
                dto.setMetadataJson(JSON.parseObject(entity.getMetadata(), Map.class));
            }
        } catch (Exception e) {
            log.warn("解析JSON字段失败 - id: {}", entity.getId(), e);
        }

        // 设置其他字段
        dto.setSourceUrl(entity.getSourceUrl());
        dto.setThumbnailUrl(null); // 需要根据实际需求设置
        dto.setLanguage(entity.getLanguage());
        dto.setIsActive("ACTIVE".equals(entity.getStatus()));

        // 加载分类信息
        try {
            List<ContentCategoryRelation> categoryRelations = contentCategoryRelationMapper
                    .selectByContentIdAndType(entity.getId(), ContentType.LEARNING_RESOURCE.getValue());
            if (categoryRelations != null && !categoryRelations.isEmpty()) {
                List<ContentCategoryRelationDTO> categoryDTOs = categoryRelations.stream()
                        .map(this::convertToContentCategoryRelationDTO)
                        .collect(Collectors.toList());
                dto.setCategories(categoryDTOs);
            }
        } catch (Exception e) {
            log.warn("加载学习资源分类信息失败 - id: {}", entity.getId(), e);
        }

        return dto;
    }

    /**
     * 将LearningResourceDTO转换为LearningResource实体
     */
    private LearningResource convertToLearningResource(LearningResourceDTO dto) {
        if (dto == null) {
            return null;
        }

        LearningResource entity = new LearningResource();
        BeanUtils.copyProperties(dto, entity);

        // 处理特殊字段转换
        if (dto.getDuration() != null) {
            entity.setEstimatedDurationHours(new BigDecimal(dto.getDuration()).divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP));
        }

        // 处理标签数组转字符串
        if (dto.getTags() != null && !dto.getTags().isEmpty()) {
            String tags = String.join(",", dto.getTags());
            entity.setTags(tags);
        }

        // 处理前置要求数组转字符串
        if (dto.getPrerequisites() != null && !dto.getPrerequisites().isEmpty()) {
            String prerequisites = String.join(",", dto.getPrerequisites());
            entity.setPrerequisites(prerequisites);
        }

        // 处理学习目标数组转字符串
        if (dto.getLearningObjectives() != null && !dto.getLearningObjectives().isEmpty()) {
            String learningGoals = String.join(",", dto.getLearningObjectives());
            entity.setLearningGoals(learningGoals);
        }

        // 处理JSON字段
        try {
            if (dto.getContentConfig() != null) {
                entity.setContentConfig(JSON.toJSONString(dto.getContentConfig()));
            }
            if (dto.getEmbedConfig() != null) {
                entity.setEmbedConfig(JSON.toJSONString(dto.getEmbedConfig()));
            }
            if (dto.getAccessConfig() != null) {
                entity.setAccessConfig(JSON.toJSONString(dto.getAccessConfig()));
            }
            if (dto.getMediaMetadata() != null) {
                entity.setMediaMetadata(JSON.toJSONString(dto.getMediaMetadata()));
            }
            // 处理 metadataJson 字段
            if (dto.getMetadataJson() != null) {
                String metadataJsonString = JSON.toJSONString(dto.getMetadataJson());
                log.info("🔍 convertToLearningResource - metadataJson 序列化结果: {}", metadataJsonString);
                entity.setMetadata(metadataJsonString);
            } else {
                log.info("🔍 convertToLearningResource - metadataJson 为 null");
            }
        } catch (Exception e) {
            log.warn("序列化JSON字段失败", e);
        }

        // 设置状态
        if (dto.getIsActive() != null) {
            entity.setStatus(dto.getIsActive() ? "ACTIVE" : "INACTIVE");
        }

        return entity;
    }

    @Override
    @Transactional
    public Result<Void> updateLearningResourceStatus(Long id, Integer status) {
        log.info("开始更新学习资源状态 - 入参: id={}, status={}", id, status);

        try {
            if (id == null || status == null) {
                log.warn("参数校验失败 - 学习资源ID和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习资源ID和状态不能为空");
            }

            // 检查资源是否存在
            LearningResource existingResource = learningResourceMapper.selectById(id);
            if (existingResource == null) {
                log.warn("学习资源不存在 - id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_NOT_FOUND", "学习资源不存在");
            }

            // 更新状态
            LearningResource updateEntity = new LearningResource();
            updateEntity.setId(id);
            updateEntity.setStatus(convertStatusToString(status));
            updateEntity.setUpdatedAt(LocalDateTime.now());

            int result = learningResourceMapper.update(updateEntity);
            if (result <= 0) {
                log.error("更新学习资源状态失败 - 数据库更新失败, id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_UPDATE_ERROR", "更新学习资源状态失败");
            }

            log.info("更新学习资源状态成功 - id: {}, status: {}", id, status);
            return Result.success();

        } catch (Exception e) {
            log.error("更新学习资源状态失败 - id: {}, status: {}", id, status, e);
            return Result.errorResult("LEARNING_RESOURCE_UPDATE_ERROR", "更新学习资源状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchUpdateLearningResourceStatus(List<Long> ids, Integer status) {
        log.info("开始批量更新学习资源状态 - 入参: ids={}, status={}", ids, status);

        try {
            if (ids == null || ids.isEmpty() || status == null) {
                log.warn("参数校验失败 - 学习资源ID列表和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习资源ID列表和状态不能为空");
            }

            // 批量更新状态
            int result = learningResourceMapper.batchUpdateStatus(ids, status, null);
            if (result <= 0) {
                log.error("批量更新学习资源状态失败 - 数据库更新失败, ids: {}", ids);
                return Result.errorResult("LEARNING_RESOURCE_BATCH_UPDATE_ERROR", "批量更新学习资源状态失败");
            }

            log.info("批量更新学习资源状态成功 - 更新数量: {}, status: {}", result, status);
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新学习资源状态失败 - ids: {}, status: {}", ids, status, e);
            return Result.errorResult("LEARNING_RESOURCE_BATCH_UPDATE_ERROR", "批量更新学习资源状态失败: " + e.getMessage());
        }
    }

    // ==================== 学习路径管理 ====================

    @Override
    public Result<PageResult<LearningPathDTO>> getLearningPathList(GetLearningPathListRequest request) {

        log.info("开始获取学习路径列表 - 入参: request={}", request);

        try {
            // 参数校验
            if (request == null || request.getPageRequest() == null) {
                log.warn("参数校验失败 - 请求参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数不能为空");
            }

            PageRequest pageRequest = request.getPageRequest();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 查询学习路径（实际上是资源类型为LEARNING_PATH的学习资源）
            List<LearningResource> pathResources = learningResourceMapper.selectByConditions(
                    "LEARNING_PATH", request.getCategory(), request.getDifficulty(),
                    request.getStatus(), request.getSearch());

            // 转换为PageInfo
            PageInfo<LearningResource> pageInfo = new PageInfo<>(pathResources);

            // 转换为DTO
            List<LearningPathDTO> pathDTOs = pathResources.stream()
                    .map(this::convertToLearningPathDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<LearningPathDTO> pageResult = PageResult.of(
                    pathDTOs,
                    pageInfo.getTotal(),
                    pageRequest.getPage(),
                    pageRequest.getSize());

            log.info("获取学习路径列表成功 - 总数: {}, 当前页: {}", pageInfo.getTotal(), pageRequest.getPage());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取学习路径列表失败", e);
            return Result.errorResult("LEARNING_PATH_LIST_ERROR", "获取学习路径列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<LearningPathDTO> getLearningPathById(Long id) {
        log.info("开始根据ID获取学习路径详情 - 入参: id={}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 学习路径ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习路径ID不能为空");
            }

            LearningResource pathResource = learningResourceMapper.selectById(id);
            if (pathResource == null || !"LEARNING_PATH".equals(pathResource.getResourceType())) {
                log.warn("学习路径不存在 - id: {}", id);
                return Result.errorResult("LEARNING_PATH_NOT_FOUND", "学习路径不存在");
            }

            LearningPathDTO pathDTO = convertToLearningPathDTO(pathResource);

            // 获取路径包含的资源
            List<LearningPathResource> pathResources = learningPathResourceMapper.selectByLearningPathIdOrderBySequence(id);
            List<LearningPathResourceDTO> resourceDTOs = pathResources.stream()
                    .map(this::convertToLearningPathResourceDTO)
                    .collect(Collectors.toList());
            pathDTO.setResources(resourceDTOs);

            log.info("根据ID获取学习路径详情成功 - id: {}, title: {}", id, pathResource.getTitle());
            return Result.success(pathDTO);

        } catch (Exception e) {
            log.error("根据ID获取学习路径详情失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_PATH_GET_ERROR", "获取学习路径详情失败: " + e.getMessage());
        }
    }

    /**
     * 将状态整数转换为字符串
     */
    private String convertStatusToString(Integer status) {
        if (status == null) {
            return "ACTIVE";
        }
        switch (status) {
            case 0:
                return "INACTIVE";
            case 1:
                return "ACTIVE";
            case 2:
                return "PENDING_REVIEW";
            default:
                return "ACTIVE";
        }
    }

    /**
     * 将LearningResource实体转换为LearningPathDTO
     */
    private LearningPathDTO convertToLearningPathDTO(LearningResource entity) {
        if (entity == null) {
            return null;
        }

        LearningPathDTO dto = new LearningPathDTO();
        dto.setId(entity.getId());
        dto.setTitle(entity.getTitle());
        dto.setDescription(entity.getDescription());
        dto.setDifficulty(entity.getDifficultyLevel());

        if (entity.getEstimatedDurationHours() != null) {
            dto.setEstimatedHours(entity.getEstimatedDurationHours().intValue());
        }

        dto.setRating(entity.getRating() != null ? entity.getRating().doubleValue() : null);
        dto.setIsActive("ACTIVE".equals(entity.getStatus()));
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setCreatedBy(entity.getCreatedBy());
        dto.setUpdatedBy(entity.getUpdatedBy());

        return dto;
    }

    /**
     * 将LearningPathResource实体转换为LearningPathResourceDTO
     */
    private LearningPathResourceDTO convertToLearningPathResourceDTO(LearningPathResource entity) {
        if (entity == null) {
            return null;
        }

        LearningPathResourceDTO dto = new LearningPathResourceDTO();
        dto.setId(entity.getId());
        dto.setLearningPathId(entity.getLearningPathId());
        dto.setResourceId(entity.getResourceId());
        dto.setSequenceOrder(entity.getSequenceOrder());
        dto.setStageName(entity.getStageName());
        dto.setEstimatedHours(entity.getEstimatedHours());
        dto.setIsOptional(!Boolean.TRUE.equals(entity.getIsOptional()));
        dto.setNotes(entity.getNotes());

        // 获取资源详情
        LearningResource resource = learningResourceMapper.selectById(entity.getResourceId());
        if (resource != null) {
            dto.setResources(Collections.singletonList(convertToPathLearningResourceDTO(resource)));
        }

        return dto;
    }

    private LearningResourceDTO convertToPathLearningResourceDTO(LearningResource entity) {
        if (entity == null) {
            return null;
        }

        LearningResourceDTO dto = new LearningResourceDTO();
        dto.setResourceType(entity.getResourceType());
        dto.setId(entity.getId());
        dto.setTitle(entity.getTitle());
        // 处理特殊字段转换
        if (entity.getEstimatedDurationHours() != null) {
            dto.setDuration(entity.getEstimatedDurationHours().multiply(new BigDecimal(60)).intValue()); // 转换为分钟
        }
        return dto;
    }

    @Override
    @Transactional
    public Result<LearningPathDTO> createLearningPath(LearningPathDTO learningPath) {
        log.info("开始创建学习路径 - 入参: learningPath={}", learningPath);

        try {
            if (learningPath == null || !StringUtils.hasText(learningPath.getTitle())) {
                log.warn("参数校验失败 - 学习路径信息和标题不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习路径信息和标题不能为空");
            }

            // 转换为学习资源实体
            LearningResource entity = new LearningResource();
            entity.setTitle(learningPath.getTitle());
            entity.setDescription(learningPath.getDescription());
            entity.setResourceType("LEARNING_PATH");
            entity.setSourceType("INTERNAL");
            entity.setDifficultyLevel(learningPath.getDifficulty());
            if (learningPath.getEstimatedHours() != null) {
                entity.setEstimatedDurationHours(new BigDecimal(learningPath.getEstimatedHours()));
            }
            entity.setStatus("ACTIVE");
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            entity.setCreatedBy(learningPath.getCreatedBy());

            // 插入数据库
            int result = learningResourceMapper.insert(entity);
            if (result <= 0) {
                log.error("创建学习路径失败 - 数据库插入失败");
                return Result.errorResult("LEARNING_PATH_CREATE_ERROR", "创建学习路径失败");
            }

            // 转换为DTO返回
            LearningPathDTO createdPath = convertToLearningPathDTO(entity);

            log.info("创建学习路径成功 - id: {}, title: {}", entity.getId(), entity.getTitle());
            return Result.success(createdPath);

        } catch (Exception e) {
            log.error("创建学习路径失败", e);
            return Result.errorResult("LEARNING_PATH_CREATE_ERROR", "创建学习路径失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<LearningPathDTO> updateLearningPath(Long id, LearningPathDTO learningPath) {
        log.info("开始更新学习路径 - 入参: id={}, learningPath={}", id, learningPath);

        try {
            if (id == null || learningPath == null) {
                log.warn("参数校验失败 - 学习路径ID和路径信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习路径ID和路径信息不能为空");
            }

            // 检查路径是否存在
            LearningResource existingPath = learningResourceMapper.selectById(id);
            if (existingPath == null || !"LEARNING_PATH".equals(existingPath.getResourceType())) {
                log.warn("学习路径不存在 - id: {}", id);
                return Result.errorResult("LEARNING_PATH_NOT_FOUND", "学习路径不存在");
            }

            // 更新实体
            LearningResource entity = new LearningResource();
            entity.setId(id);
            entity.setTitle(learningPath.getTitle());
            entity.setDescription(learningPath.getDescription());
            entity.setDifficultyLevel(learningPath.getDifficulty());
            if (learningPath.getEstimatedHours() != null) {
                entity.setEstimatedDurationHours(new BigDecimal(learningPath.getEstimatedHours()));
            }
            entity.setUpdatedAt(LocalDateTime.now());
            entity.setUpdatedBy(learningPath.getUpdatedBy());

            // 更新数据库
            int result = learningResourceMapper.update(entity);
            if (result <= 0) {
                log.error("更新学习路径失败 - 数据库更新失败, id: {}", id);
                return Result.errorResult("LEARNING_PATH_UPDATE_ERROR", "更新学习路径失败");
            }

            // 查询更新后的路径
            LearningResource updatedPath = learningResourceMapper.selectById(id);
            LearningPathDTO updatedPathDTO = convertToLearningPathDTO(updatedPath);

            log.info("更新学习路径成功 - id: {}, title: {}", id, updatedPath.getTitle());
            return Result.success(updatedPathDTO);

        } catch (Exception e) {
            log.error("更新学习路径失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_PATH_UPDATE_ERROR", "更新学习路径失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteLearningPath(Long id) {
        log.info("开始删除学习路径 - 入参: id={}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 学习路径ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习路径ID不能为空");
            }

            // 删除路径关联的资源
            learningPathResourceMapper.deleteByLearningPathId(id);

//            // 软删除路径
//            int result = learningResourceMapper.deleteById(id);
//            if (result <= 0) {
//                log.error("删除学习路径失败 - 数据库删除失败, id: {}", id);
//                return Result.errorResult("LEARNING_PATH_DELETE_ERROR", "删除学习路径失败");
//            }

            log.info("删除学习路径成功 - id: {}", id);
            return Result.success();

        } catch (Exception e) {
            log.error("删除学习路径失败 - id: {}", id, e);
            return Result.errorResult("LEARNING_PATH_DELETE_ERROR", "删除学习路径失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LearningPathResourceDTO>> getLearningPathResources(Long pathId) {
        log.info("开始获取学习路径的资源列表 - 入参: pathId={}", pathId);

        try {
            if (pathId == null) {
                log.warn("参数校验失败 - 学习路径ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习路径ID不能为空");
            }

            // 获取路径资源
            List<LearningPathResource> pathResources = learningPathResourceMapper.selectByLearningPathIdOrderBySequence(pathId);

            List<LearningPathResourceDTO> resourceDTOs = pathResources.stream()
                    .map(this::convertToLearningPathResourceDTO)
                    .collect(Collectors.toList());

            log.info("获取学习路径的资源列表成功 - pathId: {}, 资源数量: {}", pathId, resourceDTOs.size());
            return Result.success(resourceDTOs);

        } catch (Exception e) {
            log.error("获取学习路径的资源列表失败 - pathId: {}", pathId, e);
            return Result.errorResult("LEARNING_PATH_RESOURCES_GET_ERROR", "获取学习路径的资源列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<LearningPathResourceDTO> addResourceToPath(Long pathId, Long resourceId, Integer sortOrder) {
        log.info("开始为学习路径添加资源 - 入参: pathId={}, resourceId={}, sortOrder={}", pathId, resourceId, sortOrder);

        try {
            if (pathId == null || resourceId == null) {
                log.warn("参数校验失败 - 学习路径ID和资源ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习路径ID和资源ID不能为空");
            }

            // 检查路径是否存在
            LearningResource existingPath = learningResourceMapper.selectById(pathId);
            if (existingPath == null || !"LEARNING_PATH".equals(existingPath.getResourceType())) {
                log.warn("学习路径不存在 - pathId: {}", pathId);
                return Result.errorResult("LEARNING_PATH_NOT_FOUND", "学习路径不存在");
            }

            // 检查资源是否存在
            LearningResource existingResource = learningResourceMapper.selectById(resourceId);
            if (existingResource == null) {
                log.warn("学习资源不存在 - resourceId: {}", resourceId);
                return Result.errorResult("LEARNING_RESOURCE_NOT_FOUND", "学习资源不存在");
            }

            // 检查资源是否已在路径中
            int existingCount = learningPathResourceMapper.countByPathAndResource(pathId, resourceId);
            if (existingCount > 0) {
                log.warn("资源已在学习路径中 - pathId: {}, resourceId: {}", pathId, resourceId);
                return Result.errorResult("RESOURCE_ALREADY_IN_PATH", "资源已在学习路径中");
            }

            // 如果没有指定排序，则设置为最后
            if (sortOrder == null) {
                Integer maxOrder = learningPathResourceMapper.getMaxSequenceOrder(pathId);
                sortOrder = (maxOrder != null ? maxOrder : 0) + 1;
            }

            // 创建关联记录
            LearningPathResource pathResource = new LearningPathResource();
            pathResource.setLearningPathId(pathId);
            pathResource.setResourceId(resourceId);
            pathResource.setSequenceOrder(sortOrder);
            pathResource.setIsOptional(false);
            pathResource.setCreatedAt(LocalDateTime.now());

            // 插入数据库
            int result = learningPathResourceMapper.insert(pathResource);
            if (result <= 0) {
                log.error("为学习路径添加资源失败 - 数据库插入失败");
                return Result.errorResult("ADD_RESOURCE_TO_PATH_ERROR", "为学习路径添加资源失败");
            }

            // 转换为DTO返回
            LearningPathResourceDTO resourceDTO = convertToLearningPathResourceDTO(pathResource);

            log.info("为学习路径添加资源成功 - pathId: {}, resourceId: {}, sortOrder: {}", pathId, resourceId, sortOrder);
            return Result.success(resourceDTO);

        } catch (Exception e) {
            log.error("为学习路径添加资源失败 - pathId: {}, resourceId: {}", pathId, resourceId, e);
            return Result.errorResult("ADD_RESOURCE_TO_PATH_ERROR", "为学习路径添加资源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> removeResourceFromPath(Long pathId, Long resourceId) {
        log.info("开始从学习路径移除资源 - 入参: pathId={}, resourceId={}", pathId, resourceId);

        try {
            if (pathId == null || resourceId == null) {
                log.warn("参数校验失败 - 学习路径ID和资源ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习路径ID和资源ID不能为空");
            }

            // 检查关联是否存在
            int existingCount = learningPathResourceMapper.countByPathAndResource(pathId, resourceId);
            if (existingCount == 0) {
                log.warn("资源不在学习路径中 - pathId: {}, resourceId: {}", pathId, resourceId);
                return Result.errorResult("RESOURCE_NOT_IN_PATH", "资源不在学习路径中");
            }

            // 删除关联记录
            int result = learningPathResourceMapper.deleteByPathAndResource(pathId, resourceId);
            if (result <= 0) {
                log.error("从学习路径移除资源失败 - 数据库删除失败");
                return Result.errorResult("REMOVE_RESOURCE_FROM_PATH_ERROR", "从学习路径移除资源失败");
            }

            log.info("从学习路径移除资源成功 - pathId: {}, resourceId: {}", pathId, resourceId);
            return Result.success();

        } catch (Exception e) {
            log.error("从学习路径移除资源失败 - pathId: {}, resourceId: {}", pathId, resourceId, e);
            return Result.errorResult("REMOVE_RESOURCE_FROM_PATH_ERROR", "从学习路径移除资源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> reorderPathResources(Long pathId, List<Long> resourceIds) {
        log.info("开始调整学习路径中资源的顺序 - 入参: pathId={}, resourceIds={}", pathId, resourceIds);

        try {
            if (pathId == null || resourceIds == null || resourceIds.isEmpty()) {
                log.warn("参数校验失败 - 学习路径ID和资源ID列表不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习路径ID和资源ID列表不能为空");
            }

            // 检查路径是否存在
            LearningResource existingPath = learningResourceMapper.selectById(pathId);
            if (existingPath == null || !"LEARNING_PATH".equals(existingPath.getResourceType())) {
                log.warn("学习路径不存在 - pathId: {}", pathId);
                return Result.errorResult("LEARNING_PATH_NOT_FOUND", "学习路径不存在");
            }

            // 批量更新顺序
            List<LearningPathResource> updates = new java.util.ArrayList<>();
            for (int i = 0; i < resourceIds.size(); i++) {
                LearningPathResource update = new LearningPathResource();
                update.setLearningPathId(pathId);
                update.setResourceId(resourceIds.get(i));
                update.setSequenceOrder(i + 1);
                updates.add(update);
            }

            int result = learningPathResourceMapper.batchUpdateSequenceOrder(updates);
            if (result <= 0) {
                log.error("调整学习路径中资源的顺序失败 - 数据库更新失败");
                return Result.errorResult("REORDER_PATH_RESOURCES_ERROR", "调整学习路径中资源的顺序失败");
            }

            log.info("调整学习路径中资源的顺序成功 - pathId: {}, 更新数量: {}", pathId, result);
            return Result.success();

        } catch (Exception e) {
            log.error("调整学习路径中资源的顺序失败 - pathId: {}", pathId, e);
            return Result.errorResult("REORDER_PATH_RESOURCES_ERROR", "调整学习路径中资源的顺序失败: " + e.getMessage());
        }
    }

    // ==================== 学习进度跟踪 ====================

    @Override
    public Result<UserLearningProgressDTO> getUserLearningProgress(Long userId, Long resourceId) {
        log.info("开始获取用户学习进度 - 入参: userId={}, resourceId={}", userId, resourceId);

        try {
            if (userId == null || resourceId == null) {
                log.warn("参数校验失败 - 用户ID和资源ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID和资源ID不能为空");
            }

            // 这里应该查询用户学习进度表，但由于表结构中没有定义，我们返回一个模拟的进度
            // 在实际实现中，需要创建用户学习进度表和对应的Mapper
            UserLearningProgressDTO progress = new UserLearningProgressDTO(userId, resourceId);
            progress.setProgress(0);
            progress.setTimeSpent(0);
            progress.setIsCompleted(false);

            // 获取资源信息
            LearningResource resource = learningResourceMapper.selectById(resourceId);
            if (resource != null) {
                progress.setResourceTitle(resource.getTitle());
                progress.setResourceType(resource.getResourceType());
            }

            log.info("获取用户学习进度成功 - userId: {}, resourceId: {}", userId, resourceId);
            return Result.success(progress);

        } catch (Exception e) {
            log.error("获取用户学习进度失败 - userId: {}, resourceId: {}", userId, resourceId, e);
            return Result.errorResult("USER_LEARNING_PROGRESS_GET_ERROR", "获取用户学习进度失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<UserLearningProgressDTO> updateUserLearningProgress(
            Long userId,
            Long resourceId,
            UserLearningProgressDTO progress) {

        log.info("开始更新用户学习进度 - 入参: userId={}, resourceId={}, progress={}", userId, resourceId, progress);

        try {
            if (userId == null || resourceId == null || progress == null) {
                log.warn("参数校验失败 - 用户ID、资源ID和进度信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID、资源ID和进度信息不能为空");
            }

            // 这里应该更新用户学习进度表，但由于表结构中没有定义，我们返回一个模拟的结果
            // 在实际实现中，需要创建用户学习进度表和对应的Mapper
            progress.setUserId(userId);
            progress.setResourceId(resourceId);
            progress.setLastAccessAt(java.time.LocalDateTime.now());

            log.info("更新用户学习进度成功 - userId: {}, resourceId: {}", userId, resourceId);
            return Result.success(progress);

        } catch (Exception e) {
            log.error("更新用户学习进度失败 - userId: {}, resourceId: {}", userId, resourceId, e);
            return Result.errorResult("USER_LEARNING_PROGRESS_UPDATE_ERROR", "更新用户学习进度失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> markResourceAsCompleted(Long userId, Long resourceId) {
        log.info("开始标记学习资源为已完成 - 入参: userId={}, resourceId={}", userId, resourceId);

        try {
            if (userId == null || resourceId == null) {
                log.warn("参数校验失败 - 用户ID和资源ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID和资源ID不能为空");
            }

            // 检查资源是否存在
            LearningResource resource = learningResourceMapper.selectById(resourceId);
            if (resource == null) {
                log.warn("学习资源不存在 - resourceId: {}", resourceId);
                return Result.errorResult("LEARNING_RESOURCE_NOT_FOUND", "学习资源不存在");
            }

            // 增加完成次数
            learningResourceMapper.incrementDownloadCount(resourceId);

            log.info("标记学习资源为已完成成功 - userId: {}, resourceId: {}", userId, resourceId);
            return Result.success();

        } catch (Exception e) {
            log.error("标记学习资源为已完成失败 - userId: {}, resourceId: {}", userId, resourceId, e);
            return Result.errorResult("MARK_RESOURCE_COMPLETED_ERROR", "标记学习资源为已完成失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<UserLearningProgressDTO>> getUserPathProgress(Long userId, Long pathId) {
        log.info("开始获取用户学习路径进度 - 入参: userId={}, pathId={}", userId, pathId);

        try {
            if (userId == null || pathId == null) {
                log.warn("参数校验失败 - 用户ID和路径ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID和路径ID不能为空");
            }

            // 获取路径中的所有资源
            List<LearningPathResource> pathResources = learningPathResourceMapper.selectByLearningPathIdOrderBySequence(pathId);

            // 为每个资源创建进度记录（模拟数据）
            List<UserLearningProgressDTO> progressList = pathResources.stream()
                    .map(pathResource -> {
                        UserLearningProgressDTO progress = new UserLearningProgressDTO(userId, pathResource.getResourceId());
                        progress.setProgress(0);
                        progress.setTimeSpent(0);
                        progress.setIsCompleted(false);

                        // 获取资源信息
                        LearningResource resource = learningResourceMapper.selectById(pathResource.getResourceId());
                        if (resource != null) {
                            progress.setResourceTitle(resource.getTitle());
                            progress.setResourceType(resource.getResourceType());
                        }

                        return progress;
                    })
                    .collect(Collectors.toList());

            log.info("获取用户学习路径进度成功 - userId: {}, pathId: {}, 资源数量: {}", userId, pathId, progressList.size());
            return Result.success(progressList);

        } catch (Exception e) {
            log.error("获取用户学习路径进度失败 - userId: {}, pathId: {}", userId, pathId, e);
            return Result.errorResult("USER_PATH_PROGRESS_GET_ERROR", "获取用户学习路径进度失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Object> getUserLearningStats(Long userId) {
        log.info("开始获取用户学习统计 - 入参: userId={}", userId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 这里应该查询用户学习统计数据，但由于表结构中没有定义，我们返回一个模拟的统计
            // 在实际实现中，需要根据用户学习进度表计算统计数据
            java.util.Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("totalResources", 0);
            stats.put("completedResources", 0);
            stats.put("inProgressResources", 0);
            stats.put("totalLearningTime", 0);
            stats.put("completionRate", 0.0);

            log.info("获取用户学习统计成功 - userId: {}", userId);
            return Result.success(stats);

        } catch (Exception e) {
            log.error("获取用户学习统计失败 - userId: {}", userId, e);
            return Result.errorResult("USER_LEARNING_STATS_GET_ERROR", "获取用户学习统计失败: " + e.getMessage());
        }
    }

    /**
     * 获取学习资源分类统计信息
     *
     * <p>该方法会统计所有启用的学习资源分类及其下的资源数量，支持层级结构。
     * 统计信息包括：
     * <ul>
     *   <li>分类基本信息（ID、名称、图标等）</li>
     *   <li>该分类下的资源数量（不包括已删除的资源）</li>
     *   <li>子分类的统计信息（递归获取）</li>
     * </ul>
     *
     * @return 分类统计信息列表，按排序权重和资源数量排序
     */
    @Override
    public Result<List<CategoryStatisticsDTO>> getResourceCategoryStatistics() {
        log.info("开始获取学习资源分类统计信息");

        try {
            // 获取学习资源相关的所有分类
            List<Category> categories = categoryMapper.selectByContentCategory(
                    ContentType.LEARNING_RESOURCE.getValue(), true);

            if (categories == null || categories.isEmpty()) {
                log.info("未找到学习资源相关分类");
                return Result.success(new ArrayList<>());
            }

            // 构建分类统计信息
            List<CategoryStatisticsDTO> statisticsList = new ArrayList<>();

            for (Category category : categories) {
                CategoryStatisticsDTO statistics = buildResourceCategoryStatistics(category);
                if (statistics != null) {
                    statisticsList.add(statistics);
                }
            }

            // 按排序权重和资源数量排序
            statisticsList.sort((a, b) -> {
                // 首先按排序权重排序
                int sortOrderCompare = Integer.compare(
                        a.getSortOrder() != null ? a.getSortOrder() : Integer.MAX_VALUE,
                        b.getSortOrder() != null ? b.getSortOrder() : Integer.MAX_VALUE
                );
                if (sortOrderCompare != 0) {
                    return sortOrderCompare;
                }
                // 然后按资源数量降序排序
                return Integer.compare(
                        b.getResourceCount() != null ? b.getResourceCount() : 0,
                        a.getResourceCount() != null ? a.getResourceCount() : 0
                );
            });

            log.info("获取学习资源分类统计信息成功 - 分类数量: {}", statisticsList.size());
            return Result.success(statisticsList);

        } catch (Exception e) {
            log.error("获取学习资源分类统计信息失败", e);
            return Result.errorResult("RESOURCE_CATEGORY_STATISTICS_ERROR",
                    "获取学习资源分类统计信息失败: " + e.getMessage());
        }
    }

    // ==================== 学习资源推荐和搜索 ====================

    @Override
    public Result<PageResult<LearningResourceDTO>> getRecommendedResources(GetRecommendedResourcesRequest request) {

        log.info("开始获取推荐学习资源 - 入参: request={}", request);

        try {
            // 参数校验
            if (request == null || request.getUserId() == null || request.getPageRequest() == null) {
                log.warn("参数校验失败 - 用户ID和分页请求不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID和分页请求不能为空");
            }

            PageRequest pageRequest = request.getPageRequest();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 获取推荐资源
            List<LearningResource> resources = learningResourceMapper.selectRecommendedResources(
                    request.getUserId(), request.getCategory(), pageRequest.getSize());

            // 转换为PageInfo
            PageInfo<LearningResource> pageInfo = new PageInfo<>(resources);

            // 转换为DTO
            List<LearningResourceDTO> resourceDTOs = resources.stream()
                    .map(this::convertToLearningResourceDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<LearningResourceDTO> pageResult = PageResult.of(
                    resourceDTOs,
                    pageInfo.getTotal(),
                    pageRequest.getPage(),
                    pageRequest.getSize());

            log.info("获取推荐学习资源成功 - userId: {}, 总数: {}", request.getUserId(), pageInfo.getTotal());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取推荐学习资源失败 - userId: {}", request != null ? request.getUserId() : "null", e);
            return Result.errorResult("RECOMMENDED_RESOURCES_GET_ERROR", "获取推荐学习资源失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<LearningResourceDTO>> getPopularResources(GetPopularResourcesRequest request) {

        log.info("开始获取热门学习资源 - 入参: request={}", request);

        try {
            // 参数校验
            if (request == null || request.getPageRequest() == null) {
                log.warn("参数校验失败 - 分页请求不能为空");
                return Result.errorResult("INVALID_PARAMETER", "分页请求不能为空");
            }

            PageRequest pageRequest = request.getPageRequest();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 获取热门资源
            List<LearningResource> resources = learningResourceMapper.selectPopularResources(
                    request.getCategory(), request.getDays(), pageRequest.getSize());

            // 转换为PageInfo
            PageInfo<LearningResource> pageInfo = new PageInfo<>(resources);

            // 转换为DTO
            List<LearningResourceDTO> resourceDTOs = resources.stream()
                    .map(this::convertToLearningResourceDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<LearningResourceDTO> pageResult = PageResult.of(
                    resourceDTOs,
                    pageInfo.getTotal(),
                    pageRequest.getPage(),
                    pageRequest.getSize());

            log.info("获取热门学习资源成功 - 总数: {}, 统计天数: {}", pageInfo.getTotal(), request.getDays());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取热门学习资源失败", e);
            return Result.errorResult("POPULAR_RESOURCES_GET_ERROR", "获取热门学习资源失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<LearningResourceDTO>> searchLearningResources(SearchLearningResourcesRequest request) {

        log.info("开始搜索学习资源 - 入参: request={}", request);

        try {
            // 参数校验
            if (request == null || !StringUtils.hasText(request.getKeyword()) || request.getPageRequest() == null) {
                log.warn("参数校验失败 - 搜索关键词和分页请求不能为空");
                return Result.errorResult("INVALID_PARAMETER", "搜索关键词和分页请求不能为空");
            }

            PageRequest pageRequest = request.getPageRequest();

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 高级搜索
            List<LearningResource> resources = learningResourceMapper.advancedSearch(request.getKeyword(), request.getFilters());

            // 转换为PageInfo
            PageInfo<LearningResource> pageInfo = new PageInfo<>(resources);

            // 转换为DTO
            List<LearningResourceDTO> resourceDTOs = resources.stream()
                    .map(this::convertToLearningResourceDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<LearningResourceDTO> pageResult = PageResult.of(
                    resourceDTOs,
                    pageInfo.getTotal(),
                    pageRequest.getPage(),
                    pageRequest.getSize());

            log.info("搜索学习资源成功 - 关键词: {}, 总数: {}", request.getKeyword(), pageInfo.getTotal());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("搜索学习资源失败 - keyword: {}", request != null ? request.getKeyword() : "null", e);
            return Result.errorResult("SEARCH_LEARNING_RESOURCES_ERROR", "搜索学习资源失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> incrementViewCount(Long id) {
        log.info("开始增加学习资源浏览次数 - 入参: id={}", id);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 学习资源ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习资源ID不能为空");
            }

            // 检查资源是否存在
            LearningResource resource = learningResourceMapper.selectById(id);
            if (resource == null) {
                log.warn("学习资源不存在 - id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_NOT_FOUND", "学习资源不存在");
            }

            // 增加浏览次数
            int result = learningResourceMapper.incrementViewCount(id);
            if (result <= 0) {
                log.error("增加学习资源浏览次数失败 - 数据库更新失败, id: {}", id);
                return Result.errorResult("INCREMENT_VIEW_COUNT_ERROR", "增加学习资源浏览次数失败");
            }

            log.info("增加学习资源浏览次数成功 - id: {}", id);
            return Result.success();

        } catch (Exception e) {
            log.error("增加学习资源浏览次数失败 - id: {}", id, e);
            return Result.errorResult("INCREMENT_VIEW_COUNT_ERROR", "增加学习资源浏览次数失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> incrementDownloadCount(Long id, Long userId) {
        log.info("开始增加学习资源下载次数 - 入参: id={}, userId={}", id, userId);

        try {
            if (id == null) {
                log.warn("参数校验失败 - 学习资源ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "学习资源ID不能为空");
            }

            // 检查资源是否存在
            LearningResource resource = learningResourceMapper.selectById(id);
            if (resource == null) {
                log.warn("学习资源不存在 - id: {}", id);
                return Result.errorResult("LEARNING_RESOURCE_NOT_FOUND", "学习资源不存在");
            }

            // 增加下载次数
            int result = learningResourceMapper.incrementDownloadCount(id);
            if (result <= 0) {
                log.error("增加学习资源下载次数失败 - 数据库更新失败, id: {}", id);
                return Result.errorResult("INCREMENT_DOWNLOAD_COUNT_ERROR", "增加学习资源下载次数失败");
            }

            log.info("增加学习资源下载次数成功 - id: {}, userId: {}", id, userId);
            return Result.success();

        } catch (Exception e) {
            log.error("增加学习资源下载次数失败 - id: {}, userId: {}", id, userId, e);
            return Result.errorResult("INCREMENT_DOWNLOAD_COUNT_ERROR", "增加学习资源下载次数失败: " + e.getMessage());
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 保存学习资源分类关系
     *
     * @param learningResourceId 学习资源ID
     * @param categories         分类ID列表
     */
    private void saveCategoryRelations(Long learningResourceId, List<ContentCategoryRelationDTO> categories) {
        if (learningResourceId == null) {
            log.warn("学习资源ID为空，跳过分类关系保存");
            return;
        }

        try {
            // 先删除现有的分类关系
            contentCategoryRelationMapper.deleteByContentIdAndType(learningResourceId, ContentType.LEARNING_RESOURCE.getValue());
            log.debug("删除学习资源现有分类关系 - learningResourceId: {}", learningResourceId);

            // 如果有新的分类ID，则插入新的关系
            if (categories != null && !categories.isEmpty()) {
                List<ContentCategoryRelation> relations = new ArrayList<>();
                LocalDateTime now = LocalDateTime.now();

                for (ContentCategoryRelationDTO category : categories) {
                    if (category.getCategoryId() != null) {
                        ContentCategoryRelation relation = new ContentCategoryRelation();
                        relation.setContentId(learningResourceId);
                        relation.setCategoryId(category.getCategoryId());
                        relation.setContentType(ContentType.LEARNING_RESOURCE.getValue());
                        relation.setCreatedAt(now);
                        relations.add(relation);
                    }
                }

                if (!relations.isEmpty()) {
                    contentCategoryRelationMapper.batchInsert(relations);
                    log.info("保存学习资源分类关系成功 - learningResourceId: {}, 分类数量: {}", learningResourceId, relations.size());
                }
            }
        } catch (Exception e) {
            log.error("保存学习资源分类关系失败 - learningResourceId: {}, categories: {}, 错误信息: {}",
                    learningResourceId, categories, e.getMessage(), e);
            // 这里不抛出异常，避免影响主流程
        }
    }

    /**
     * 将ContentCategoryRelation实体转换为ContentCategoryRelationDTO
     */
    private ContentCategoryRelationDTO convertToContentCategoryRelationDTO(ContentCategoryRelation entity) {
        if (entity == null) {
            return null;
        }

        ContentCategoryRelationDTO dto = new ContentCategoryRelationDTO();
        dto.setId(entity.getId());
        dto.setContentType(ContentType.LEARNING_RESOURCE);
        dto.setContentId(entity.getContentId());
        dto.setCategoryId(entity.getCategoryId());
        dto.setCreatedAt(entity.getCreatedAt());

        return dto;
    }

    /**
     * 构建资源分类统计信息
     *
     * @param category 分类信息
     * @return 分类统计DTO
     */
    private CategoryStatisticsDTO buildResourceCategoryStatistics(Category category) {
        if (category == null) {
            return null;
        }

        try {
            CategoryStatisticsDTO statistics = new CategoryStatisticsDTO();
            statistics.setCategoryId(category.getId().toString());
            statistics.setCategoryName(category.getName());
            statistics.setParentCategoryId(category.getParentId() != null ?
                    category.getParentId().toString() : null);
            statistics.setIconUrl(category.getIconUrl());
            statistics.setSortOrder(category.getSortOrder());

            // 统计该分类下的资源数量
            int resourceCount = countResourcesByCategory(category.getId());
            statistics.setResourceCount(resourceCount);

            // 对于学习资源，课程数量设置为0（因为这里统计的是资源，不是课程）
            statistics.setCourseCount(0);

            // 获取子分类统计
            List<Category> childCategories = categoryMapper.selectByParentId(category.getId());
            if (childCategories != null && !childCategories.isEmpty()) {
                List<CategoryStatisticsDTO> children = new ArrayList<>();
                for (Category child : childCategories) {
                    // 只包含启用的子分类
                    if (child.getIsActive() != null && child.getIsActive()) {
                        CategoryStatisticsDTO childStatistics = buildResourceCategoryStatistics(child);
                        if (childStatistics != null) {
                            children.add(childStatistics);
                        }
                    }
                }
                statistics.setChildren(children);
            }

            return statistics;

        } catch (Exception e) {
            log.error("构建资源分类统计信息失败 - categoryId: {}, categoryName: {}",
                    category.getId(), category.getName(), e);
            return null;
        }
    }

    /**
     * 统计指定分类下的资源数量
     *
     * @param categoryId 分类ID
     * @return 资源数量
     */
    private int countResourcesByCategory(Long categoryId) {
        try {
            // 通过内容分类关系表统计资源数量
            List<ContentCategoryRelation> relations = contentCategoryRelationMapper
                    .selectByCategoryId(categoryId);

            if (relations == null || relations.isEmpty()) {
                return 0;
            }

            // 过滤出学习资源类型的关系，并统计有效资源数量
            int count = 0;
            for (ContentCategoryRelation relation : relations) {
                if (ContentType.LEARNING_RESOURCE.getValue().equals(relation.getContentType())) {
                    // 检查资源是否存在且状态有效
                    LearningResource resource = learningResourceMapper.selectById(relation.getContentId());
                    if (resource != null && !"DELETED".equals(resource.getStatus())) {
                        count++;
                    }
                }
            }

            return count;

        } catch (Exception e) {
            log.error("统计分类下资源数量失败 - categoryId: {}", categoryId, e);
            return 0;
        }
    }
}
