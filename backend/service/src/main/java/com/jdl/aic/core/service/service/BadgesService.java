package com.jdl.aic.core.service.service;

import com.jdl.aic.core.service.dao.entity.portal.Badges;

import java.util.List;

/**
 * 徽章服务接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface BadgesService {

    /**
     * 根据ID查询徽章
     */
    Badges getBadgeById(Long id);

    /**
     * 根据名称查询徽章
     */
    Badges getBadgeByName(String name);

    /**
     * 查询所有徽章
     */
    List<Badges> getAllBadges();

    /**
     * 查询活跃徽章
     */
    List<Badges> getActiveBadges();

    /**
     * 根据创建人查询徽章
     */
    List<Badges> getBadgesByCreatedBy(Long createdBy);

    /**
     * 创建徽章
     */
    boolean createBadge(Badges badge);

    /**
     * 更新徽章
     */
    boolean updateBadge(Badges badge);

    /**
     * 删除徽章（物理删除）
     */
    boolean deleteBadge(Long id);

    /**
     * 软删除徽章
     */
    boolean softDeleteBadge(Long id);

    /**
     * 检查徽章名称是否存在
     */
    boolean isBadgeNameExists(String name);

    /**
     * 获取徽章总数
     */
    int getBadgeCount();

    /**
     * 获取活跃徽章数
     */
    int getActiveBadgeCount();

    /**
     * 搜索徽章
     */
    List<Badges> searchBadges(String keyword, Integer limit);

    /**
     * 批量创建徽章
     */
    boolean batchCreateBadges(List<Badges> badges);

    /**
     * 批量软删除徽章
     */
    boolean batchSoftDeleteBadges(List<Long> ids);
}
