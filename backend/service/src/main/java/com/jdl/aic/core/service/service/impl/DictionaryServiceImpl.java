package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.DictionaryDTO;
import com.jdl.aic.core.service.client.service.DictionaryService;
import com.jdl.aic.core.service.dao.entity.primary.Dictionary;
import com.jdl.aic.core.service.dao.mapper.primary.DictionaryMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典管理服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("dictionaryService")
public class DictionaryServiceImpl implements DictionaryService {

    @Resource
    private DictionaryMapper dictionaryMapper;

    @Override
    public Result<PageResult<DictionaryDTO>> getDictionaryList(
            PageRequest pageRequest,
            String type,
            Boolean isActive,
            String search) {
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            Dictionary queryCondition = new Dictionary();
            if (StringUtils.hasText(type)) {
                queryCondition.setType(type);
            }
            if (isActive != null) {
                queryCondition.setIsActive(isActive);
            }

            // 查询字典项列表
            List<Dictionary> dictionaries;
            if (StringUtils.hasText(search)) {
                dictionaries = dictionaryMapper.searchDictionaries(search, type, isActive);
            } else {
                dictionaries = dictionaryMapper.selectByCondition(queryCondition);
            }

            // 使用PageInfo获取分页信息
            PageInfo<Dictionary> pageInfo = new PageInfo<>(dictionaries);

            // 转换为DTO
            List<DictionaryDTO> dictionaryDTOs = dictionaries.stream()
                    .map(this::convertToDictionaryDTO)
                    .collect(Collectors.toList());

            PageResult<DictionaryDTO> pageResult = PageResult.of(
                    dictionaryDTOs, pageInfo.getTotal(), pageRequest.getPage(), pageRequest.getSize());

            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取字典项列表失败", e);
            return Result.errorResult("DICTIONARY_LIST_QUERY_ERROR", "获取字典项列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<DictionaryDTO> getDictionaryById(Long id) {
        try {
            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "字典项ID不能为空");
            }

            Dictionary dictionary = dictionaryMapper.selectById(id);
            if (dictionary == null) {
                return Result.errorResult("DICTIONARY_NOT_FOUND", "字典项不存在");
            }

            DictionaryDTO dictionaryDTO = convertToDictionaryDTO(dictionary);
            return Result.success(dictionaryDTO);

        } catch (Exception e) {
            log.error("获取字典项详情失败，ID: {}", id, e);
            return Result.errorResult("DICTIONARY_QUERY_ERROR", "获取字典项详情失败: " + e.getMessage());
        }
    }

    @Override
    public Result<DictionaryDTO> getDictionaryByKeyAndType(String key, String type) {
        try {
            if (!StringUtils.hasText(key) || !StringUtils.hasText(type)) {
                return Result.errorResult("INVALID_PARAMETER", "字典键和类型不能为空");
            }

            Dictionary dictionary = dictionaryMapper.selectByKeyAndType(key, type);
            if (dictionary == null) {
                return Result.errorResult("DICTIONARY_NOT_FOUND", "字典项不存在");
            }

            DictionaryDTO dictionaryDTO = convertToDictionaryDTO(dictionary);
            return Result.success(dictionaryDTO);

        } catch (Exception e) {
            log.error("根据键和类型获取字典项失败，key: {}, type: {}", key, type, e);
            return Result.errorResult("DICTIONARY_QUERY_ERROR", "获取字典项失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<DictionaryDTO>> getDictionariesByKey(String key, Boolean isActive) {
        try {
            if (!StringUtils.hasText(key)) {
                return Result.errorResult("INVALID_PARAMETER", "字典键不能为空");
            }

            List<Dictionary> dictionaries = dictionaryMapper.selectByKey(key, isActive);
            List<DictionaryDTO> dictionaryDTOs = dictionaries.stream()
                    .map(this::convertToDictionaryDTO)
                    .collect(Collectors.toList());

            return Result.success(dictionaryDTOs);

        } catch (Exception e) {
            log.error("根据键获取字典项列表失败，key: {}", key, e);
            return Result.errorResult("DICTIONARY_QUERY_ERROR", "获取字典项列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<DictionaryDTO>> getDictionariesByType(String type, Boolean isActive) {
        try {
            if (!StringUtils.hasText(type)) {
                return Result.errorResult("INVALID_PARAMETER", "字典类型不能为空");
            }

            List<Dictionary> dictionaries = dictionaryMapper.selectByType(type, isActive);
            List<DictionaryDTO> dictionaryDTOs = dictionaries.stream()
                    .map(this::convertToDictionaryDTO)
                    .collect(Collectors.toList());

            return Result.success(dictionaryDTOs);

        } catch (Exception e) {
            log.error("根据类型获取字典项列表失败，type: {}", type, e);
            return Result.errorResult("DICTIONARY_QUERY_ERROR", "获取字典项列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<DictionaryDTO> createDictionary(DictionaryDTO dictionary) {
        try {
            if (dictionary == null) {
                return Result.errorResult("INVALID_PARAMETER", "字典项信息不能为空");
            }

            // 验证必填字段
            if (!StringUtils.hasText(dictionary.getKey())) {
                return Result.errorResult("INVALID_PARAMETER", "字典键不能为空");
            }
            if (!StringUtils.hasText(dictionary.getValue())) {
                return Result.errorResult("INVALID_PARAMETER", "字典值不能为空");
            }
            if (!StringUtils.hasText(dictionary.getType())) {
                return Result.errorResult("INVALID_PARAMETER", "字典类型不能为空");
            }

            // 检查键和类型组合是否已存在
            Dictionary existingDictionary = dictionaryMapper.selectByKeyAndType(
                    dictionary.getKey(), dictionary.getType());
            if (existingDictionary != null) {
                return Result.errorResult("DICTIONARY_KEY_EXISTS", "相同类型下的字典键已存在");
            }

            // 转换为实体对象
            Dictionary dictionaryEntity = convertToDictionary(dictionary);
            dictionaryEntity.setCreatedAt(LocalDateTime.now());
            dictionaryEntity.setUpdatedAt(LocalDateTime.now());
            
            // 设置默认值
            if (dictionaryEntity.getIsActive() == null) {
                dictionaryEntity.setIsActive(true);
            }
            if (dictionaryEntity.getSortOrder() == null) {
                dictionaryEntity.setSortOrder(0);
            }

            // 插入数据库
            int result = dictionaryMapper.insert(dictionaryEntity);
            if (result <= 0) {
                return Result.errorResult("DICTIONARY_CREATE_FAILED", "创建字典项失败");
            }

            DictionaryDTO resultDTO = convertToDictionaryDTO(dictionaryEntity);
            return Result.success(resultDTO);

        } catch (Exception e) {
            log.error("创建字典项失败", e);
            return Result.errorResult("DICTIONARY_CREATE_ERROR", "创建字典项失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<DictionaryDTO> updateDictionary(Long id, DictionaryDTO dictionary) {
        try {
            if (id == null || dictionary == null) {
                return Result.errorResult("INVALID_PARAMETER", "字典项ID和信息不能为空");
            }

            // 检查字典项是否存在
            Dictionary existingDictionary = dictionaryMapper.selectById(id);
            if (existingDictionary == null) {
                return Result.errorResult("DICTIONARY_NOT_FOUND", "字典项不存在");
            }

            // 如果修改了键或类型，检查新的键和类型组合是否已存在
            if (StringUtils.hasText(dictionary.getKey()) && StringUtils.hasText(dictionary.getType())) {
                if (!dictionary.getKey().equals(existingDictionary.getKey()) ||
                    !dictionary.getType().equals(existingDictionary.getType())) {
                    Dictionary duplicateDictionary = dictionaryMapper.selectByKeyAndType(
                            dictionary.getKey(), dictionary.getType());
                    if (duplicateDictionary != null && !duplicateDictionary.getId().equals(id)) {
                        return Result.errorResult("DICTIONARY_KEY_EXISTS", "相同类型下的字典键已存在");
                    }
                }
            }

            // 更新字典项信息
            Dictionary dictionaryEntity = convertToDictionary(dictionary);
            dictionaryEntity.setId(id);
            dictionaryEntity.setUpdatedAt(LocalDateTime.now());

            int result = dictionaryMapper.updateById(dictionaryEntity);
            if (result <= 0) {
                return Result.errorResult("DICTIONARY_UPDATE_FAILED", "更新字典项失败");
            }

            // 查询更新后的字典项
            Dictionary updatedDictionary = dictionaryMapper.selectById(id);
            DictionaryDTO resultDTO = convertToDictionaryDTO(updatedDictionary);
            return Result.success(resultDTO);

        } catch (Exception e) {
            log.error("更新字典项失败，ID: {}", id, e);
            return Result.errorResult("DICTIONARY_UPDATE_ERROR", "更新字典项失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> deleteDictionary(Long id) {
        try {
            if (id == null) {
                return Result.errorResult("INVALID_PARAMETER", "字典项ID不能为空");
            }

            // 检查字典项是否存在
            Dictionary existingDictionary = dictionaryMapper.selectById(id);
            if (existingDictionary == null) {
                return Result.errorResult("DICTIONARY_NOT_FOUND", "字典项不存在");
            }

            int result = dictionaryMapper.deleteById(id);
            if (result <= 0) {
                return Result.errorResult("DICTIONARY_DELETE_FAILED", "删除字典项失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("删除字典项失败，ID: {}", id, e);
            return Result.errorResult("DICTIONARY_DELETE_ERROR", "删除字典项失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateDictionarySortOrder(Long id, Integer sortOrder) {
        try {
            if (id == null || sortOrder == null) {
                return Result.errorResult("INVALID_PARAMETER", "字典项ID和排序权重不能为空");
            }

            int result = dictionaryMapper.updateSortOrder(id, sortOrder);
            if (result <= 0) {
                return Result.errorResult("DICTIONARY_UPDATE_FAILED", "更新字典项排序权重失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("更新字典项排序权重失败，ID: {}, sortOrder: {}", id, sortOrder, e);
            return Result.errorResult("DICTIONARY_UPDATE_ERROR", "更新字典项排序权重失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> updateDictionaryStatus(Long id, Boolean isActive) {
        try {
            if (id == null || isActive == null) {
                return Result.errorResult("INVALID_PARAMETER", "字典项ID和状态不能为空");
            }

            int result = dictionaryMapper.updateStatus(id, isActive);
            if (result <= 0) {
                return Result.errorResult("DICTIONARY_UPDATE_FAILED", "更新字典项状态失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("更新字典项状态失败，ID: {}, isActive: {}", id, isActive, e);
            return Result.errorResult("DICTIONARY_UPDATE_ERROR", "更新字典项状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<String>> getAllDictionaryTypes() {
        try {
            List<String> types = dictionaryMapper.selectAllTypes();
            return Result.success(types);

        } catch (Exception e) {
            log.error("获取所有字典类型失败", e);
            return Result.errorResult("DICTIONARY_TYPES_QUERY_ERROR", "获取所有字典类型失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> countDictionariesByType(String type, Boolean isActive) {
        try {
            if (!StringUtils.hasText(type)) {
                return Result.errorResult("INVALID_PARAMETER", "字典类型不能为空");
            }

            int count = dictionaryMapper.countByType(type, isActive);
            return Result.success(count);

        } catch (Exception e) {
            log.error("统计字典项数量失败，type: {}", type, e);
            return Result.errorResult("DICTIONARY_COUNT_ERROR", "统计字典项数量失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<List<DictionaryDTO>> batchCreateDictionaries(List<DictionaryDTO> dictionaries) {
        try {
            if (dictionaries == null || dictionaries.isEmpty()) {
                return Result.errorResult("INVALID_PARAMETER", "字典项列表不能为空");
            }

            List<Dictionary> dictionaryEntities = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (DictionaryDTO dto : dictionaries) {
                // 验证必填字段
                if (!StringUtils.hasText(dto.getKey()) || !StringUtils.hasText(dto.getValue()) ||
                    !StringUtils.hasText(dto.getType())) {
                    return Result.errorResult("INVALID_PARAMETER", "字典键、值和类型不能为空");
                }

                // 检查键和类型组合是否已存在
                Dictionary existingDictionary = dictionaryMapper.selectByKeyAndType(dto.getKey(), dto.getType());
                if (existingDictionary != null) {
                    return Result.errorResult("DICTIONARY_KEY_EXISTS",
                            "字典键 " + dto.getKey() + " 在类型 " + dto.getType() + " 下已存在");
                }

                Dictionary entity = convertToDictionary(dto);
                entity.setCreatedAt(now);
                entity.setUpdatedAt(now);
                if (entity.getIsActive() == null) {
                    entity.setIsActive(true);
                }
                if (entity.getSortOrder() == null) {
                    entity.setSortOrder(0);
                }
                dictionaryEntities.add(entity);
            }

            int result = dictionaryMapper.batchInsert(dictionaryEntities);
            if (result <= 0) {
                return Result.errorResult("DICTIONARY_BATCH_CREATE_FAILED", "批量创建字典项失败");
            }

            List<DictionaryDTO> resultDTOs = dictionaryEntities.stream()
                    .map(this::convertToDictionaryDTO)
                    .collect(Collectors.toList());

            return Result.success(resultDTOs);

        } catch (Exception e) {
            log.error("批量创建字典项失败", e);
            return Result.errorResult("DICTIONARY_BATCH_CREATE_ERROR", "批量创建字典项失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchUpdateDictionaryStatus(List<Long> ids, Boolean isActive) {
        try {
            if (ids == null || ids.isEmpty() || isActive == null) {
                return Result.errorResult("INVALID_PARAMETER", "字典项ID列表和状态不能为空");
            }

            int result = dictionaryMapper.batchUpdateStatus(ids, isActive);
            if (result <= 0) {
                return Result.errorResult("DICTIONARY_BATCH_UPDATE_FAILED", "批量更新字典项状态失败");
            }

            return Result.success();

        } catch (Exception e) {
            log.error("批量更新字典项状态失败", e);
            return Result.errorResult("DICTIONARY_BATCH_UPDATE_ERROR", "批量更新字典项状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> batchDeleteDictionaries(List<Long> ids) {
        try {
            if (ids == null || ids.isEmpty()) {
                return Result.errorResult("INVALID_PARAMETER", "字典项ID列表不能为空");
            }

            for (Long id : ids) {
                int result = dictionaryMapper.deleteById(id);
                if (result <= 0) {
                    log.warn("删除字典项失败，ID: {}", id);
                }
            }

            return Result.success();

        } catch (Exception e) {
            log.error("批量删除字典项失败", e);
            return Result.errorResult("DICTIONARY_BATCH_DELETE_ERROR", "批量删除字典项失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<DictionaryDTO>> searchDictionaries(String keyword, String type, Boolean isActive) {
        try {
            if (!StringUtils.hasText(keyword)) {
                return Result.errorResult("INVALID_PARAMETER", "搜索关键词不能为空");
            }

            List<Dictionary> dictionaries = dictionaryMapper.searchDictionaries(keyword, type, isActive);
            List<DictionaryDTO> dictionaryDTOs = dictionaries.stream()
                    .map(this::convertToDictionaryDTO)
                    .collect(Collectors.toList());

            return Result.success(dictionaryDTOs);

        } catch (Exception e) {
            log.error("搜索字典项失败，keyword: {}", keyword, e);
            return Result.errorResult("DICTIONARY_SEARCH_ERROR", "搜索字典项失败: " + e.getMessage());
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 将Dictionary实体转换为DictionaryDTO
     *
     * @param dictionary Dictionary实体
     * @return DictionaryDTO
     */
    private DictionaryDTO convertToDictionaryDTO(Dictionary dictionary) {
        if (dictionary == null) {
            return null;
        }

        DictionaryDTO dto = new DictionaryDTO();
        BeanUtils.copyProperties(dictionary, dto);
        return dto;
    }

    /**
     * 将DictionaryDTO转换为Dictionary实体
     *
     * @param dictionaryDTO DictionaryDTO
     * @return Dictionary实体
     */
    private Dictionary convertToDictionary(DictionaryDTO dictionaryDTO) {
        if (dictionaryDTO == null) {
            return null;
        }

        Dictionary dictionary = new Dictionary();
        BeanUtils.copyProperties(dictionaryDTO, dictionary);
        return dictionary;
    }
}
