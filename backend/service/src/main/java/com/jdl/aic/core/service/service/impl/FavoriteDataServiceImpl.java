package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.dao.entity.portal.Favorite;
import com.jdl.aic.core.service.dao.mapper.portal.FavoriteMapper;
import com.jdl.aic.core.service.portal.client.FavoriteDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收藏数据服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("favoriteDataService")
public class FavoriteDataServiceImpl implements FavoriteDataService {

    @Autowired
    private FavoriteMapper favoriteMapper;

    // ==================== 收藏基本操作 ====================

    @Override
    @Transactional
    public Result<FavoriteDTO> addFavorite(AddFavoriteRequest request) {
        log.info("开始添加收藏 - userId: {}, contentType: {}, contentId: {}",
            request.getUserId(), request.getContentType(), request.getContentId());

        Long userId = request.getUserId();
        Integer contentType = request.getContentType();
        Long contentId = request.getContentId();
        Long relatedKnowledgeTypeId = request.getRelatedKnowledgeTypeId();

        try {
            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }
            if (contentType == null) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }
            if (contentId == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }

            // 检查是否已收藏
            Favorite existingFavorite = favoriteMapper.selectByUserAndContent(userId, contentType, contentId);
            if (existingFavorite != null) {
                if (!existingFavorite.isDeleted()) {
                    log.warn("内容已收藏 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId);
                    return Result.errorResult("ALREADY_FAVORITED", "内容已收藏");
                } else {
                    // 恢复已删除的收藏
                    favoriteMapper.restoreById(existingFavorite.getId());
                    log.info("恢复收藏成功 - favoriteId: {}", existingFavorite.getId());
                    return Result.success("添加收藏成功", convertToFavoriteDTO(existingFavorite));
                }
            }

            // 创建新收藏
            Favorite favorite = new Favorite(contentType, contentId, relatedKnowledgeTypeId, userId);
            int result = favoriteMapper.insert(favorite);
            
            if (result > 0) {
                log.info("添加收藏成功 - favoriteId: {}, userId: {}, contentType: {}, contentId: {}", 
                    favorite.getId(), userId, contentType, contentId);
                return Result.success("添加收藏成功", convertToFavoriteDTO(favorite));
            } else {
                log.error("添加收藏失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("FAVORITE_ADD_FAILED", "添加收藏失败");
            }

        } catch (Exception e) {
            log.error("添加收藏失败 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId, e);
            return Result.errorResult("FAVORITE_ADD_ERROR", "添加收藏失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> removeFavorite(RemoveFavoriteRequest request) {
        log.info("开始取消收藏 - userId: {}, contentType: {}, contentId: {}",
            request.getUserId(), request.getContentType(), request.getContentId());

        Long userId = request.getUserId();
        Integer contentType = request.getContentType();
        Long contentId = request.getContentId();

        try {
            // 参数校验
            if (userId == null || contentType == null || contentId == null) {
                log.warn("参数校验失败 - 必要参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "必要参数不能为空");
            }

            // 查找收藏记录
            Favorite favorite = favoriteMapper.selectByUserAndContent(userId, contentType, contentId);
            if (favorite == null || favorite.isDeleted()) {
                log.warn("收藏记录不存在 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId);
                return Result.errorResult("FAVORITE_NOT_FOUND", "收藏记录不存在");
            }

            // 软删除收藏
            int result = favoriteMapper.deleteById(favorite.getId());
            if (result > 0) {
                log.info("取消收藏成功 - favoriteId: {}", favorite.getId());
                return Result.success("取消收藏成功", null);
            } else {
                log.error("取消收藏失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("FAVORITE_REMOVE_FAILED", "取消收藏失败");
            }

        } catch (Exception e) {
            log.error("取消收藏失败 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId, e);
            return Result.errorResult("FAVORITE_REMOVE_ERROR", "取消收藏失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Boolean> toggleFavorite(ToggleFavoriteRequest request) {
        log.info("开始切换收藏状态 - userId: {}, contentType: {}, contentId: {}",
            request.getUserId(), request.getContentType(), request.getContentId());

        try {
            Long userId = request.getUserId();
            Integer contentType = request.getContentType();
            Long contentId = request.getContentId();
            Long relatedKnowledgeTypeId = request.getRelatedKnowledgeTypeId();

            // 检查当前收藏状态
            Favorite favorite = favoriteMapper.selectByUserAndContent(userId, contentType, contentId);

            if (favorite == null || favorite.isDeleted()) {
                // 添加收藏
                AddFavoriteRequest addRequest = new AddFavoriteRequest(userId, contentType, contentId, relatedKnowledgeTypeId);
                Result<FavoriteDTO> addResult = addFavorite(addRequest);
                if (addResult.isSuccess()) {
                    return Result.success("添加收藏成功", true);
                } else {
                    return Result.errorResult(addResult.getCode(), addResult.getMessage());
                }
            } else {
                // 取消收藏
                RemoveFavoriteRequest removeRequest = new RemoveFavoriteRequest(userId, contentType, contentId);
                Result<Void> removeResult = removeFavorite(removeRequest);
                if (removeResult.isSuccess()) {
                    return Result.success("取消收藏成功", false);
                } else {
                    return Result.errorResult(removeResult.getCode(), removeResult.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("切换收藏状态失败 - userId: {}, contentType: {}, contentId: {}",
                request.getUserId(), request.getContentType(), request.getContentId(), e);
            return Result.errorResult("FAVORITE_TOGGLE_ERROR", "切换收藏状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> isFavorited(CheckFavoriteRequest request) {
        log.info("开始检查收藏状态 - userId: {}, contentType: {}, contentId: {}",
            request.getUserId(), request.getContentType(), request.getContentId());

        try {
            Long userId = request.getUserId();
            Integer contentType = request.getContentType();
            Long contentId = request.getContentId();

            // 参数校验
            if (userId == null || contentType == null || contentId == null) {
                log.warn("参数校验失败 - 必要参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "必要参数不能为空");
            }

            Favorite favorite = favoriteMapper.selectByUserAndContent(userId, contentType, contentId);
            boolean isFavorited = favorite != null && !favorite.isDeleted();

            log.info("检查收藏状态完成 - userId: {}, contentType: {}, contentId: {}, isFavorited: {}",
                userId, contentType, contentId, isFavorited);
            return Result.success("检查收藏状态成功", isFavorited);

        } catch (Exception e) {
            log.error("检查收藏状态失败 - userId: {}, contentType: {}, contentId: {}",
                request.getUserId(), request.getContentType(), request.getContentId(), e);
            return Result.errorResult("FAVORITE_CHECK_ERROR", "检查收藏状态失败: " + e.getMessage());
        }
    }

    // ==================== 收藏查询 ====================

    @Override
    public Result<PageResult<FavoriteDTO>> getUserFavorites(GetUserFavoritesRequest request) {
        log.info("开始获取用户收藏列表 - userId: {}, contentType: {}, page: {}, size: {}",
            request.getUserId(), request.getContentType(),
            request.getPageRequest().getPage(), request.getPageRequest().getSize());

        try {
            Long userId = request.getUserId();
            PageRequest pageRequest = request.getPageRequest();
            Integer contentType = request.getContentType();

            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 查询收藏列表
            List<Favorite> favorites;
            if (contentType != null) {
                favorites = favoriteMapper.selectByUserIdAndContentType(userId, contentType);
            } else {
                favorites = favoriteMapper.selectByUserId(userId);
            }

            log.info("数据库查询完成，查询到 {} 条收藏记录", favorites.size());
            PageInfo<Favorite> pageInfo = new PageInfo<>(favorites);

            // 转换为DTO
            List<FavoriteDTO> favoriteDTOs = favorites.stream()
                    .map(this::convertToFavoriteDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<FavoriteDTO> pageResult = PageResult.of(
                favoriteDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                pageInfo.getPageSize()
            );

            log.info("获取用户收藏列表成功 - userId: {}, total: {}", userId, pageInfo.getTotal());
            return Result.success("获取用户收藏列表成功", pageResult);

        } catch (Exception e) {
            log.error("获取用户收藏列表失败 - userId: {}", request.getUserId(), e);
            return Result.errorResult("FAVORITE_LIST_ERROR", "获取用户收藏列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<FavoriteDTO>> getRecentFavorites(Long userId, Integer limit) {
        log.info("开始获取用户最近收藏 - userId: {}, limit: {}", userId, limit);

        try {
            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            List<Favorite> favorites = favoriteMapper.selectRecentByUserId(userId, limit);
            List<FavoriteDTO> favoriteDTOs = favorites.stream()
                    .map(this::convertToFavoriteDTO)
                    .collect(Collectors.toList());

            log.info("获取用户最近收藏成功 - userId: {}, count: {}", userId, favoriteDTOs.size());
            return Result.success("获取用户最近收藏成功", favoriteDTOs);

        } catch (Exception e) {
            log.error("获取用户最近收藏失败 - userId: {}", userId, e);
            return Result.errorResult("RECENT_FAVORITES_ERROR", "获取用户最近收藏失败: " + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<FavoriteDTO>> getFavoritesByContent(GetFavoritesByContentRequest request) {
        Integer contentType = request.getContentType();
        Long contentId = request.getContentId();
        PageRequest pageRequest = request.getPageRequest();
        log.info("开始获取内容收藏列表 - contentType: {}, contentId: {}", contentType, contentId);

        try {
            // 参数校验
            if (contentType == null || contentId == null) {
                log.warn("参数校验失败 - 内容类型和内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型和内容ID不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            List<Favorite> favorites = favoriteMapper.selectByContent(contentType, contentId);
            PageInfo<Favorite> pageInfo = new PageInfo<>(favorites);

            List<FavoriteDTO> favoriteDTOs = favorites.stream()
                    .map(this::convertToFavoriteDTO)
                    .collect(Collectors.toList());

            PageResult<FavoriteDTO> pageResult = PageResult.of(
                favoriteDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                pageInfo.getPageSize()
            );

            log.info("获取内容收藏列表成功 - contentType: {}, contentId: {}, total: {}",
                contentType, contentId, pageInfo.getTotal());
            return Result.success("获取内容收藏列表成功", pageResult);

        } catch (Exception e) {
            log.error("获取内容收藏列表失败 - contentType: {}, contentId: {}", contentType, contentId, e);
            return Result.errorResult("CONTENT_FAVORITES_ERROR", "获取内容收藏列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getFavoriteStatusBatch(Long userId, Integer contentType, List<Long> contentIds) {
        log.info("开始批量获取收藏状态 - userId: {}, contentType: {}, contentIds size: {}",
            userId, contentType, contentIds != null ? contentIds.size() : 0);

        try {
            // 参数校验
            if (userId == null || contentType == null || CollectionUtils.isEmpty(contentIds)) {
                log.warn("参数校验失败 - 必要参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "必要参数不能为空");
            }

            // TODO: 实现批量查询收藏状态的逻辑
            // 这里需要根据实际需求实现批量查询
            log.info("批量获取收藏状态成功 - userId: {}", userId);
            return Result.success("批量获取收藏状态成功", Collections.emptyList());

        } catch (Exception e) {
            log.error("批量获取收藏状态失败 - userId: {}", userId, e);
            return Result.errorResult("BATCH_STATUS_ERROR", "批量获取收藏状态失败: " + e.getMessage());
        }
    }

    // ==================== 收藏统计 ====================

    @Override
    public Result<Integer> getUserFavoriteCount(Long userId) {
        log.info("开始统计用户收藏总数 - userId: {}", userId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            int count = favoriteMapper.countByUserId(userId);
            log.info("统计用户收藏总数成功 - userId: {}, count: {}", userId, count);
            return Result.success("统计用户收藏总数成功", count);

        } catch (Exception e) {
            log.error("统计用户收藏总数失败 - userId: {}", userId, e);
            return Result.errorResult("COUNT_ERROR", "统计用户收藏总数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getUserFavoriteCountByType(Long userId) {
        log.info("开始统计用户各类型收藏数 - userId: {}", userId);

        try {
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // TODO: 实现按类型统计的逻辑
            log.info("统计用户各类型收藏数成功 - userId: {}", userId);
            return Result.success("统计用户各类型收藏数成功", Collections.emptyList());

        } catch (Exception e) {
            log.error("统计用户各类型收藏数失败 - userId: {}", userId, e);
            return Result.errorResult("COUNT_BY_TYPE_ERROR", "统计用户各类型收藏数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getContentFavoriteCount(GetContentFavoriteCountRequest request) {
        log.info("开始统计内容收藏数 - contentType: {}, contentId: {}",
            request.getContentType(), request.getContentId());

        try {
            Integer contentType = request.getContentType();
            Long contentId = request.getContentId();

            if (contentType == null || contentId == null) {
                log.warn("参数校验失败 - 内容类型和内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型和内容ID不能为空");
            }

            int count = favoriteMapper.countByContent(contentType, contentId);
            log.info("统计内容收藏数成功 - contentType: {}, contentId: {}, count: {}",
                contentType, contentId, count);
            return Result.success("统计内容收藏数成功", count);

        } catch (Exception e) {
            log.error("统计内容收藏数失败 - contentType: {}, contentId: {}",
                request.getContentType(), request.getContentId(), e);
            return Result.errorResult("CONTENT_COUNT_ERROR", "统计内容收藏数失败: " + e.getMessage());
        }
    }

    // ==================== 数据转换方法 ====================

    /**
     * 将实体对象转换为DTO
     */
    private FavoriteDTO convertToFavoriteDTO(Favorite favorite) {
        if (favorite == null) {
            return null;
        }

        FavoriteDTO dto = new FavoriteDTO();
        dto.setId(favorite.getId());
        dto.setUserId(favorite.getUserId());
        dto.setContentType(favorite.getContentType());
        dto.setContentId(favorite.getContentId());
        dto.setRelatedKnowledgeTypeId(favorite.getRelatedKnowledgeTypeId());
        dto.setCreatedAt(favorite.getCreatedAt());

        return dto;
    }

    /**
     * 将DTO转换为实体对象
     */
    private Favorite convertToFavoriteEntity(FavoriteDTO dto) {
        if (dto == null) {
            return null;
        }

        Favorite favorite = new Favorite();
        favorite.setId(dto.getId());
        favorite.setUserId(dto.getUserId());
        favorite.setContentType(dto.getContentType());
        favorite.setContentId(dto.getContentId());
        favorite.setRelatedKnowledgeTypeId(dto.getRelatedKnowledgeTypeId());
        favorite.setCreatedAt(dto.getCreatedAt());

        return favorite;
    }

    // ==================== 其他接口方法的简单实现 ====================
    // 注：以下方法为接口完整性提供基础实现，实际项目中需要根据具体需求完善

    @Override
    public Result<List<Object>> getContentFavoriteCountBatch(Integer contentType, List<Long> contentIds) {
        return Result.success("批量统计内容收藏数成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getPopularFavoriteContent(Integer contentType, Integer limit, Integer days) {
        return Result.success("获取热门收藏内容成功", Collections.emptyList());
    }

    @Override
    public Result<List<FavoriteDTO>> batchAddFavorites(Long userId, List<FavoriteDTO> favorites) {
        return Result.success("批量添加收藏成功", Collections.emptyList());
    }

    @Override
    public Result<Void> batchRemoveFavorites(Long userId, List<Long> favoriteIds) {
        return Result.success("批量取消收藏成功", null);
    }

    @Override
    public Result<Void> batchRemoveFavoritesByContent(Long userId, Integer contentType, List<Long> contentIds) {
        return Result.success("批量取消收藏成功", null);
    }

    @Override
    public Result<Integer> cleanupDeletedFavorites(Long userId, Integer days) {
        return Result.success("清理已删除收藏成功", 0);
    }

    @Override
    public Result<FavoriteDTO> restoreFavorite(Long userId, Long favoriteId) {
        return Result.success("恢复收藏成功", new FavoriteDTO());
    }

    @Override
    public Result<Object> exportUserFavorites(Long userId, Integer contentType, String format) {
        return Result.success("导出用户收藏成功", new Object());
    }

    @Override
    public Result<Object> getUserFavoriteTrend(Long userId, Integer days) {
        return Result.success("获取用户收藏趋势成功", new Object());
    }

    @Override
    public Result<Object> getUserFavoritePreference(Long userId) {
        return Result.success("获取用户收藏偏好成功", new Object());
    }

    @Override
    public Result<Object> getContentFavoriteTrend(Integer contentType, Long contentId, Integer days) {
        return Result.success("获取内容收藏趋势成功", new Object());
    }

    @Override
    public Result<List<Object>> getRecommendedContent(Long userId, Integer contentType, Integer limit) {
        return Result.success("获取推荐内容成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getSimilarUsers(Long userId, Integer limit) {
        return Result.success("获取相似用户成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getRecommendedFavorites(Long userId, Integer contentType, Integer limit) {
        return Result.success("获取推荐收藏成功", Collections.emptyList());
    }
}
