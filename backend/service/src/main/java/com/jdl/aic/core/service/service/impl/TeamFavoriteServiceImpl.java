package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.TeamFavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.request.AddTeamFavoriteRequest;
import com.jdl.aic.core.service.client.dto.community.request.CheckTeamFavoriteRequest;
import com.jdl.aic.core.service.client.dto.community.request.GetUserTeamFavoritesRequest;
import com.jdl.aic.core.service.client.dto.community.request.RemoveTeamFavoriteRequest;
import com.jdl.aic.core.service.dao.entity.portal.TeamFavorite;
import com.jdl.aic.core.service.dao.mapper.portal.TeamFavoriteMapper;
import com.jdl.aic.core.service.portal.client.TeamFavoriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 团队收藏服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("teamFavoriteService")
public class TeamFavoriteServiceImpl implements TeamFavoriteService {

    @Resource
    private TeamFavoriteMapper teamFavoriteMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<TeamFavoriteDTO> addTeamFavorite(AddTeamFavoriteRequest request) {
        try {
            log.info("添加团队收藏，请求参数：{}", request);
            
            // 参数校验
            if (request.getUserId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (request.getTeamId() == null) {
                return Result.errorResult("INVALID_PARAM", "团队ID不能为空");
            }
            
            // 检查是否已经收藏
            TeamFavorite existingFavorite = teamFavoriteMapper
                    .selectByUserIdAndTeamId(request.getUserId(), request.getTeamId());
            if (existingFavorite != null) {
                return Result.errorResult("ALREADY_FAVORITED", "用户已收藏该团队");
            }
            
            // 创建收藏记录
            TeamFavorite teamFavorite = new TeamFavorite();
            teamFavorite.setUserId(request.getUserId());
            teamFavorite.setTeamId(request.getTeamId());
            teamFavorite.setCreatedAt(LocalDateTime.now());
            
            int result = teamFavoriteMapper.insert(teamFavorite);
            if (result > 0) {
                TeamFavoriteDTO favoriteDTO = convertToDTO(teamFavorite);
                log.info("添加团队收藏成功，ID：{}", teamFavorite.getId());
                return Result.success("添加团队收藏成功", favoriteDTO);
            } else {
                return Result.errorResult("FAVORITE_ADD_ERROR", "添加团队收藏失败");
            }
            
        } catch (Exception e) {
            log.error("添加团队收藏失败", e);
            return Result.errorResult("FAVORITE_ADD_ERROR", "添加团队收藏失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> removeTeamFavorite(RemoveTeamFavoriteRequest request) {
        try {
            log.info("取消团队收藏，请求参数：{}", request);
            
            // 参数校验
            if (request.getUserId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (request.getTeamId() == null) {
                return Result.errorResult("INVALID_PARAM", "团队ID不能为空");
            }
            
            // 检查收藏记录是否存在
            TeamFavorite existingFavorite = teamFavoriteMapper
                    .selectByUserIdAndTeamId(request.getUserId(), request.getTeamId());
            if (existingFavorite == null) {
                return Result.errorResult("FAVORITE_NOT_FOUND", "收藏记录不存在");
            }
            
            // 软删除收藏记录
            int result = teamFavoriteMapper.softDeleteByUserIdAndTeamId(request.getUserId(), request.getTeamId());
            if (result > 0) {
                log.info("取消团队收藏成功，用户ID：{}，团队ID：{}", request.getUserId(), request.getTeamId());
                return Result.success();
            } else {
                return Result.errorResult("FAVORITE_REMOVE_ERROR", "取消团队收藏失败");
            }
            
        } catch (Exception e) {
            log.error("取消团队收藏失败", e);
            return Result.errorResult("FAVORITE_REMOVE_ERROR", "取消团队收藏失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> toggleTeamFavorite(Long userId, Long teamId) {
        try {
            log.info("切换团队收藏状态，用户ID：{}，团队ID：{}", userId, teamId);
            
            // 参数校验
            if (userId == null || teamId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID和团队ID不能为空");
            }
            
            // 检查当前收藏状态
            TeamFavorite existingFavorite = teamFavoriteMapper.selectByUserIdAndTeamId(userId, teamId);
            
            if (existingFavorite != null) {
                // 已收藏，取消收藏
                RemoveTeamFavoriteRequest removeRequest = new RemoveTeamFavoriteRequest(userId, teamId);
                Result<Void> removeResult = removeTeamFavorite(removeRequest);
                if (removeResult.isSuccess()) {
                    return Result.success("切换团队收藏状态成功", false);
                } else {
                    return Result.errorResult(removeResult.getCode(), removeResult.getMessage());
                }
            } else {
                // 未收藏，添加收藏
                AddTeamFavoriteRequest addRequest = new AddTeamFavoriteRequest(userId, teamId);
                Result<TeamFavoriteDTO> addResult = addTeamFavorite(addRequest);
                if (addResult.isSuccess()) {
                    return Result.success("切换团队收藏状态成功", true);
                } else {
                    return Result.errorResult(addResult.getCode(), addResult.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("切换团队收藏状态失败", e);
            return Result.errorResult("FAVORITE_TOGGLE_ERROR", "切换团队收藏状态失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> isTeamFavorited(CheckTeamFavoriteRequest request) {
        try {
            log.info("检查团队收藏状态，请求参数：{}", request);
            
            // 参数校验
            if (request.getUserId() == null || request.getTeamId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID和团队ID不能为空");
            }
            
            boolean isFavorited = teamFavoriteMapper.existsByUserIdAndTeamId(request.getUserId(), request.getTeamId());
            return Result.success("检查团队收藏状态成功", isFavorited);
            
        } catch (Exception e) {
            log.error("检查团队收藏状态失败", e);
            return Result.errorResult("FAVORITE_CHECK_ERROR", "检查团队收藏状态失败：" + e.getMessage());
        }
    }

    @Override
    public Result<PageResult<TeamFavoriteDTO>> getUserTeamFavorites(GetUserTeamFavoritesRequest request) {
        try {
            log.info("获取用户团队收藏列表，请求参数：{}", request);
            
            // 参数校验
            if (request.getUserId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            
            // 设置分页
            PageHelper.startPage(request.getPage(), request.getSize());
            
            // 查询收藏列表
            List<TeamFavorite> teamFavorites = teamFavoriteMapper.selectByCondition(
                    request.getUserId(),
                    request.getTeamName(),
                    request.getTeamPrivacy(),
                    request.getTeamIsActive(),
                    request.getStartDate(),
                    request.getEndDate()
            );
            
            PageInfo<TeamFavorite> pageInfo = new PageInfo<>(teamFavorites);
            
            // 转换为DTO
            List<TeamFavoriteDTO> favoriteDTOs = teamFavorites.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            // 使用静态方法创建PageResult
            PageResult<TeamFavoriteDTO> pageResult = PageResult.of(
                    favoriteDTOs, 
                    pageInfo.getTotal(), 
                    pageInfo.getPageNum() - 1, // PageResult使用0开始的页码
                    pageInfo.getPageSize()
            );
            
            return Result.success("获取用户团队收藏列表成功", pageResult);
            
        } catch (Exception e) {
            log.error("获取用户团队收藏列表失败", e);
            return Result.errorResult("FAVORITE_LIST_ERROR", "获取用户团队收藏列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamFavoriteDTO>> getRecentTeamFavorites(Long userId, Integer limit) {
        try {
            log.info("获取用户最近收藏的团队，用户ID：{}，限制数量：{}", userId, limit);
            
            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            
            List<TeamFavorite> teamFavorites = teamFavoriteMapper.selectRecentByUserId(userId, limit);
            List<TeamFavoriteDTO> favoriteDTOs = teamFavorites.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            return Result.success("获取用户最近收藏的团队成功", favoriteDTOs);
            
        } catch (Exception e) {
            log.error("获取用户最近收藏的团队失败", e);
            return Result.errorResult("FAVORITE_LIST_ERROR", "获取用户最近收藏的团队失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<TeamFavoriteDTO>> getTeamFavoriteUsers(Long teamId, Integer limit) {
        try {
            log.info("获取团队的收藏用户列表，团队ID：{}，限制数量：{}", teamId, limit);
            
            if (teamId == null) {
                return Result.errorResult("INVALID_PARAM", "团队ID不能为空");
            }
            
            List<TeamFavorite> teamFavorites = teamFavoriteMapper.selectByTeamIdWithLimit(teamId, limit);
            List<TeamFavoriteDTO> favoriteDTOs = teamFavorites.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            
            return Result.success("获取团队的收藏用户列表成功", favoriteDTOs);
            
        } catch (Exception e) {
            log.error("获取团队的收藏用户列表失败", e);
            return Result.errorResult("FAVORITE_LIST_ERROR", "获取团队的收藏用户列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<TeamFavoriteDTO> getTeamFavoriteById(Long favoriteId) {
        try {
            log.info("根据收藏ID获取团队收藏详情，收藏ID：{}", favoriteId);
            
            if (favoriteId == null) {
                return Result.errorResult("INVALID_PARAM", "收藏ID不能为空");
            }
            
            TeamFavorite teamFavorite = teamFavoriteMapper.selectById(favoriteId);
            if (teamFavorite == null) {
                return Result.errorResult("FAVORITE_NOT_FOUND", "团队收藏记录不存在");
            }
            
            TeamFavoriteDTO favoriteDTO = convertToDTO(teamFavorite);
            return Result.success("获取团队收藏详情成功", favoriteDTO);
            
        } catch (Exception e) {
            log.error("获取团队收藏详情失败", e);
            return Result.errorResult("FAVORITE_GET_ERROR", "获取团队收藏详情失败：" + e.getMessage());
        }
    }

    /**
     * 将实体转换为DTO
     */
    private TeamFavoriteDTO convertToDTO(TeamFavorite teamFavorite) {
        if (teamFavorite == null) {
            return null;
        }
        
        TeamFavoriteDTO dto = new TeamFavoriteDTO();
        BeanUtils.copyProperties(teamFavorite, dto);
        return dto;
    }

    /**
     * 将DTO转换为实体
     */
    private TeamFavorite convertToEntity(TeamFavoriteDTO dto) {
        if (dto == null) {
            return null;
        }

        TeamFavorite entity = new TeamFavorite();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    // ==================== 统计方法实现 ====================

    @Override
    public Result<Integer> countUserTeamFavorites(Long userId) {
        try {
            log.info("统计用户收藏的团队数量，用户ID：{}", userId);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }

            int count = teamFavoriteMapper.countByUserId(userId);
            return Result.success("统计用户收藏的团队数量成功", count);

        } catch (Exception e) {
            log.error("统计用户收藏的团队数量失败", e);
            return Result.errorResult("COUNT_ERROR", "统计用户收藏的团队数量失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Integer> countTeamFavorites(Long teamId) {
        try {
            log.info("统计团队被收藏的次数，团队ID：{}", teamId);

            if (teamId == null) {
                return Result.errorResult("INVALID_PARAM", "团队ID不能为空");
            }

            int count = teamFavoriteMapper.countByTeamId(teamId);
            return Result.success("统计团队被收藏的次数成功", count);

        } catch (Exception e) {
            log.error("统计团队被收藏的次数失败", e);
            return Result.errorResult("COUNT_ERROR", "统计团队被收藏的次数失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getTeamFavoriteCountBatch(List<Long> teamIds) {
        try {
            log.info("批量统计团队被收藏次数，团队ID列表：{}", teamIds);

            if (teamIds == null || teamIds.isEmpty()) {
                return Result.success("批量统计团队被收藏次数成功", Collections.emptyList());
            }

            List<Object> counts = teamFavoriteMapper.countByTeamIds(teamIds);
            return Result.success("批量统计团队被收藏次数成功", counts);

        } catch (Exception e) {
            log.error("批量统计团队被收藏次数失败", e);
            return Result.errorResult("COUNT_ERROR", "批量统计团队被收藏次数失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<Object>> getPopularTeams(Integer limit, Integer days) {
        try {
            log.info("获取热门收藏团队列表，限制数量：{}，统计天数：{}", limit, days);

            List<Object> popularTeams = teamFavoriteMapper.selectPopularTeams(limit, days);
            return Result.success("获取热门收藏团队列表成功", popularTeams);

        } catch (Exception e) {
            log.error("获取热门收藏团队列表失败", e);
            return Result.errorResult("POPULAR_TEAMS_ERROR", "获取热门收藏团队列表失败：" + e.getMessage());
        }
    }

    // ==================== 批量操作方法实现 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<List<TeamFavoriteDTO>> batchAddTeamFavorites(Long userId, List<Long> teamIds) {
        try {
            log.info("批量添加团队收藏，用户ID：{}，团队ID列表：{}", userId, teamIds);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (teamIds == null || teamIds.isEmpty()) {
                return Result.errorResult("INVALID_PARAM", "团队ID列表不能为空");
            }

            List<TeamFavorite> teamFavorites = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();

            for (Long teamId : teamIds) {
                // 检查是否已经收藏
                if (!teamFavoriteMapper.existsByUserIdAndTeamId(userId, teamId)) {
                    TeamFavorite teamFavorite = new TeamFavorite();
                    teamFavorite.setUserId(userId);
                    teamFavorite.setTeamId(teamId);
                    teamFavorite.setCreatedAt(now);
                    teamFavorites.add(teamFavorite);
                }
            }

            if (!teamFavorites.isEmpty()) {
                int result = teamFavoriteMapper.batchInsert(teamFavorites);
                if (result > 0) {
                    List<TeamFavoriteDTO> favoriteDTOs = teamFavorites.stream()
                            .map(this::convertToDTO)
                            .collect(Collectors.toList());
                    log.info("批量添加团队收藏成功，添加数量：{}", result);
                    return Result.success("批量添加团队收藏成功", favoriteDTOs);
                }
            }

            return Result.success("批量添加团队收藏成功", Collections.emptyList());

        } catch (Exception e) {
            log.error("批量添加团队收藏失败", e);
            return Result.errorResult("BATCH_ADD_ERROR", "批量添加团队收藏失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchRemoveTeamFavorites(Long userId, List<Long> teamIds) {
        try {
            log.info("批量取消团队收藏，用户ID：{}，团队ID列表：{}", userId, teamIds);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (teamIds == null || teamIds.isEmpty()) {
                return Result.errorResult("INVALID_PARAM", "团队ID列表不能为空");
            }

            int result = teamFavoriteMapper.batchSoftDeleteByUserIdAndTeamIds(userId, teamIds);
            log.info("批量取消团队收藏成功，影响记录数：{}", result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量取消团队收藏失败", e);
            return Result.errorResult("BATCH_REMOVE_ERROR", "批量取消团队收藏失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchRemoveTeamFavoritesByIds(Long userId, List<Long> favoriteIds) {
        try {
            log.info("批量取消团队收藏（根据收藏ID），用户ID：{}，收藏ID列表：{}", userId, favoriteIds);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (favoriteIds == null || favoriteIds.isEmpty()) {
                return Result.errorResult("INVALID_PARAM", "收藏ID列表不能为空");
            }

            int result = teamFavoriteMapper.batchSoftDeleteByIds(favoriteIds);
            log.info("批量取消团队收藏成功，影响记录数：{}", result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量取消团队收藏失败", e);
            return Result.errorResult("BATCH_REMOVE_ERROR", "批量取消团队收藏失败：" + e.getMessage());
        }
    }

    // ==================== 其他接口方法的简单实现 ====================
    // 注：以下方法为接口完整性提供基础实现，实际项目中需要根据具体需求完善

    @Override
    public Result<Integer> cleanupDeletedTeamFavorites(Long userId, Integer days) {
        try {
            int result = teamFavoriteMapper.cleanupUserDeletedRecords(userId, days);
            return Result.success("清理已删除团队收藏成功", result);
        } catch (Exception e) {
            log.error("清理已删除团队收藏失败", e);
            return Result.errorResult("CLEANUP_ERROR", "清理已删除团队收藏失败：" + e.getMessage());
        }
    }

    @Override
    public Result<TeamFavoriteDTO> restoreTeamFavorite(Long userId, Long favoriteId) {
        try {
            int result = teamFavoriteMapper.restoreById(favoriteId);
            if (result > 0) {
                TeamFavorite teamFavorite = teamFavoriteMapper.selectById(favoriteId);
                return Result.success("恢复团队收藏成功", convertToDTO(teamFavorite));
            }
            return Result.errorResult("RESTORE_ERROR", "恢复团队收藏失败");
        } catch (Exception e) {
            log.error("恢复团队收藏失败", e);
            return Result.errorResult("RESTORE_ERROR", "恢复团队收藏失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Object> getUserTeamFavoriteTrend(Long userId, Integer days) {
        try {
            List<Object> trend = teamFavoriteMapper.selectUserTeamFavoriteTrend(userId, days);
            return Result.success("获取用户团队收藏趋势成功", trend);
        } catch (Exception e) {
            log.error("获取用户团队收藏趋势失败", e);
            return Result.errorResult("TREND_ERROR", "获取用户团队收藏趋势失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Object> getUserTeamFavoritePreference(Long userId) {
        try {
            List<Object> preference = teamFavoriteMapper.selectUserTeamFavoritePreference(userId);
            return Result.success("获取用户团队收藏偏好成功", preference);
        } catch (Exception e) {
            log.error("获取用户团队收藏偏好失败", e);
            return Result.errorResult("PREFERENCE_ERROR", "获取用户团队收藏偏好失败：" + e.getMessage());
        }
    }

    @Override
    public Result<Object> exportUserTeamFavorites(Long userId, String format) {
        return Result.success("导出用户团队收藏成功", new Object());
    }

    @Override
    public Result<List<Object>> getRecommendedTeams(Long userId, Integer limit) {
        return Result.success("获取推荐团队成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getSimilarUsers(Long userId, Integer limit) {
        return Result.success("获取相似用户成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getRecommendedTeamFavorites(Long userId, Integer limit) {
        return Result.success("获取推荐团队收藏成功", Collections.emptyList());
    }
}
