package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.LikeDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.dao.entity.portal.Like;
import com.jdl.aic.core.service.dao.mapper.portal.LikeMapper;
import com.jdl.aic.core.service.portal.client.LikeDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 点赞数据服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("likeDataService")
public class LikeDataServiceImpl implements LikeDataService {

    @Autowired
    private LikeMapper likeMapper;

    // ==================== 点赞基本操作 ====================

    @Override
    @Transactional
    public Result<LikeDTO> addLike(AddLikeRequest request) {
        log.info("开始添加点赞 - userId: {}, contentType: {}, contentId: {}",
            request.getUserId(), request.getContentType(), request.getContentId());

        Long userId = request.getUserId();
        Integer contentType = request.getContentType();
        Long contentId = request.getContentId();
        Long relatedKnowledgeTypeId = request.getRelatedKnowledgeTypeId();

        try {
            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }
            if (contentType == null) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }
            if (contentId == null) {
                log.warn("参数校验失败 - 内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容ID不能为空");
            }

            // 检查是否已点赞
            Like existingLike = likeMapper.selectByUserAndContent(userId, contentType, contentId);
            if (existingLike != null) {
                if (!existingLike.isDeleted()) {
                    log.warn("内容已点赞 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId);
                    return Result.errorResult("ALREADY_LIKED", "内容已点赞");
                } else {
                    // 恢复已删除的点赞
                    likeMapper.restoreById(existingLike.getId());
                    log.info("恢复点赞成功 - likeId: {}", existingLike.getId());
                    return Result.success("添加点赞成功", convertToLikeDTO(existingLike));
                }
            }

            // 创建新点赞
            Like like = new Like(contentType, contentId, relatedKnowledgeTypeId, userId);
            int result = likeMapper.insert(like);
            
            if (result > 0) {
                log.info("添加点赞成功 - likeId: {}, userId: {}, contentType: {}, contentId: {}", 
                    like.getId(), userId, contentType, contentId);
                return Result.success("添加点赞成功", convertToLikeDTO(like));
            } else {
                log.error("添加点赞失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("LIKE_ADD_FAILED", "添加点赞失败");
            }

        } catch (Exception e) {
            log.error("添加点赞失败 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId, e);
            return Result.errorResult("LIKE_ADD_ERROR", "添加点赞失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Void> removeLike(RemoveLikeRequest request) {
        log.info("开始取消点赞 - userId: {}, contentType: {}, contentId: {}",
            request.getUserId(), request.getContentType(), request.getContentId());

        Long userId = request.getUserId();
        Integer contentType = request.getContentType();
        Long contentId = request.getContentId();

        try {
            // 参数校验
            if (userId == null || contentType == null || contentId == null) {
                log.warn("参数校验失败 - 必要参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "必要参数不能为空");
            }

            // 查找点赞记录
            Like like = likeMapper.selectByUserAndContent(userId, contentType, contentId);
            if (like == null || like.isDeleted()) {
                log.warn("点赞记录不存在 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId);
                return Result.errorResult("LIKE_NOT_FOUND", "点赞记录不存在");
            }

            // 软删除点赞
            int result = likeMapper.deleteById(like.getId());
            if (result > 0) {
                log.info("取消点赞成功 - likeId: {}", like.getId());
                return Result.success("取消点赞成功", null);
            } else {
                log.error("取消点赞失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("LIKE_REMOVE_FAILED", "取消点赞失败");
            }

        } catch (Exception e) {
            log.error("取消点赞失败 - userId: {}, contentType: {}, contentId: {}", userId, contentType, contentId, e);
            return Result.errorResult("LIKE_REMOVE_ERROR", "取消点赞失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Boolean> toggleLike(ToggleLikeRequest request) {
        log.info("开始切换点赞状态 - userId: {}, contentType: {}, contentId: {}",
            request.getUserId(), request.getContentType(), request.getContentId());

        try {
            Long userId = request.getUserId();
            Integer contentType = request.getContentType();
            Long contentId = request.getContentId();
            Long relatedKnowledgeTypeId = request.getRelatedKnowledgeTypeId();

            // 检查当前点赞状态
            Like like = likeMapper.selectByUserAndContent(userId, contentType, contentId);

            if (like == null || like.isDeleted()) {
                // 添加点赞
                AddLikeRequest addRequest = new AddLikeRequest(userId, contentType, contentId, relatedKnowledgeTypeId);
                Result<LikeDTO> addResult = addLike(addRequest);
                if (addResult.isSuccess()) {
                    return Result.success("添加点赞成功", true);
                } else {
                    return Result.errorResult(addResult.getCode(), addResult.getMessage());
                }
            } else {
                // 取消点赞
                RemoveLikeRequest removeRequest = new RemoveLikeRequest(userId, contentType, contentId);
                Result<Void> removeResult = removeLike(removeRequest);
                if (removeResult.isSuccess()) {
                    return Result.success("取消点赞成功", false);
                } else {
                    return Result.errorResult(removeResult.getCode(), removeResult.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("切换点赞状态失败 - userId: {}, contentType: {}, contentId: {}",
                request.getUserId(), request.getContentType(), request.getContentId(), e);
            return Result.errorResult("LIKE_TOGGLE_ERROR", "切换点赞状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> isLiked(CheckLikeRequest request) {
        log.info("开始检查点赞状态 - userId: {}, contentType: {}, contentId: {}",
            request.getUserId(), request.getContentType(), request.getContentId());

        try {
            Long userId = request.getUserId();
            Integer contentType = request.getContentType();
            Long contentId = request.getContentId();

            // 参数校验
            if (userId == null || contentType == null || contentId == null) {
                log.warn("参数校验失败 - 必要参数不能为空");
                return Result.errorResult("INVALID_PARAMETER", "必要参数不能为空");
            }

            Like like = likeMapper.selectByUserAndContent(userId, contentType, contentId);
            boolean isLiked = like != null && !like.isDeleted();

            log.info("检查点赞状态完成 - userId: {}, contentType: {}, contentId: {}, isLiked: {}",
                userId, contentType, contentId, isLiked);
            return Result.success("检查点赞状态成功", isLiked);

        } catch (Exception e) {
            log.error("检查点赞状态失败 - userId: {}, contentType: {}, contentId: {}",
                request.getUserId(), request.getContentType(), request.getContentId(), e);
            return Result.errorResult("LIKE_CHECK_ERROR", "检查点赞状态失败: " + e.getMessage());
        }
    }

    // ==================== 点赞查询 ====================

    @Override
    public Result<PageResult<LikeDTO>> getUserLikes(GetUserLikesRequest request) {
        log.info("开始获取用户点赞列表 - userId: {}, contentType: {}, page: {}, size: {}",
            request.getUserId(), request.getContentType(),
            request.getPageRequest().getPage(), request.getPageRequest().getSize());

        try {
            Long userId = request.getUserId();
            PageRequest pageRequest = request.getPageRequest();
            Integer contentType = request.getContentType();

            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 查询点赞列表
            List<Like> likes;
            if (contentType != null) {
                likes = likeMapper.selectByUserIdAndContentType(userId, contentType);
            } else {
                likes = likeMapper.selectByUserId(userId);
            }

            log.info("数据库查询完成，查询到 {} 条点赞记录", likes.size());
            PageInfo<Like> pageInfo = new PageInfo<>(likes);

            // 转换为DTO
            List<LikeDTO> likeDTOs = likes.stream()
                    .map(this::convertToLikeDTO)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<LikeDTO> pageResult = PageResult.of(
                likeDTOs,
                pageInfo.getTotal(),
                pageInfo.getPageNum() - 1, // PageHelper从1开始，PageResult从0开始
                pageInfo.getPageSize()
            );

            log.info("获取用户点赞列表成功 - userId: {}, total: {}", userId, pageInfo.getTotal());
            return Result.success("获取用户点赞列表成功", pageResult);

        } catch (Exception e) {
            log.error("获取用户点赞列表失败 - userId: {}", request.getUserId(), e);
            return Result.errorResult("LIKE_LIST_ERROR", "获取用户点赞列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<LikeDTO>> getRecentLikes(GetUserLikesRequest request) {
        log.info("开始获取用户最近点赞 - userId: {}", request.getUserId());

        try {
            Long userId = request.getUserId();
            // 从分页请求中获取limit，如果没有设置则使用默认值10
            Integer limit = request.getPageRequest() != null ? request.getPageRequest().getSize() : 10;

            // 参数校验
            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            List<Like> likes = likeMapper.selectRecentByUserId(userId, limit);
            List<LikeDTO> likeDTOs = likes.stream()
                    .map(this::convertToLikeDTO)
                    .collect(Collectors.toList());

            log.info("获取用户最近点赞成功 - userId: {}, count: {}", userId, likeDTOs.size());
            return Result.success("获取用户最近点赞成功", likeDTOs);

        } catch (Exception e) {
            log.error("获取用户最近点赞失败 - userId: {}", request.getUserId(), e);
            return Result.errorResult("RECENT_LIKES_ERROR", "获取用户最近点赞失败: " + e.getMessage());
        }
    }

    // ==================== 点赞统计 ====================

    @Override
    public Result<Integer> getUserLikeCount(GetUserLikeCountRequest request) {
        log.info("开始统计用户点赞总数 - userId: {}", request.getUserId());

        try {
            Long userId = request.getUserId();

            if (userId == null) {
                log.warn("参数校验失败 - 用户ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "用户ID不能为空");
            }

            int count = likeMapper.countByUserId(userId);
            log.info("统计用户点赞总数成功 - userId: {}, count: {}", userId, count);
            return Result.success("统计用户点赞总数成功", count);

        } catch (Exception e) {
            log.error("统计用户点赞总数失败 - userId: {}", request.getUserId(), e);
            return Result.errorResult("COUNT_ERROR", "统计用户点赞总数失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Integer> getContentLikeCount(GetContentLikeCountRequest request) {
        log.info("开始统计内容点赞数 - contentType: {}, contentId: {}",
            request.getContentType(), request.getContentId());

        try {
            Integer contentType = request.getContentType();
            Long contentId = request.getContentId();

            if (contentType == null || contentId == null) {
                log.warn("参数校验失败 - 内容类型和内容ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型和内容ID不能为空");
            }

            int count = likeMapper.countByContent(contentType, contentId);
            log.info("统计内容点赞数成功 - contentType: {}, contentId: {}, count: {}",
                contentType, contentId, count);
            return Result.success("统计内容点赞数成功", count);

        } catch (Exception e) {
            log.error("统计内容点赞数失败 - contentType: {}, contentId: {}",
                request.getContentType(), request.getContentId(), e);
            return Result.errorResult("CONTENT_COUNT_ERROR", "统计内容点赞数失败: " + e.getMessage());
        }
    }

    // ==================== 数据转换方法 ====================

    /**
     * 将实体对象转换为DTO
     */
    private LikeDTO convertToLikeDTO(Like like) {
        if (like == null) {
            return null;
        }

        LikeDTO dto = new LikeDTO();
        dto.setId(like.getId());
        dto.setUserId(like.getUserId());
        dto.setContentType(like.getContentType());
        dto.setContentId(like.getContentId());
        dto.setRelatedKnowledgeTypeId(like.getRelatedKnowledgeTypeId());
        dto.setCreatedAt(like.getCreatedAt());
        dto.setIsLiked(true);

        return dto;
    }

    // ==================== 其他接口方法的简单实现 ====================
    // 注：以下方法为接口完整性提供基础实现，实际项目中需要根据具体需求完善

    @Override
    public Result<PageResult<LikeDTO>> getLikesByContent(GetLikesByContentRequest request) {
        log.info("开始获取内容点赞列表 - contentType: {}, contentId: {}",
            request.getContentType(), request.getContentId());

        // 基础实现，返回空列表
        PageResult<LikeDTO> pageResult = PageResult.of(Collections.emptyList(), 0L, 0,
            request.getPageRequest().getSize());
        return Result.success("获取内容点赞列表成功", pageResult);
    }

    @Override
    public Result<List<Object>> getLikeStatusBatch(Long userId, Integer contentType, List<Long> contentIds) {
        return Result.success("批量获取点赞状态成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getUserLikeCountByType(GetUserLikeCountRequest request) {
        log.info("开始统计用户各类型点赞数 - userId: {}", request.getUserId());

        // 基础实现，返回空列表
        return Result.success("统计用户各类型点赞数成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getContentLikeCountBatch(Integer contentType, List<Long> contentIds) {
        return Result.success("批量统计内容点赞数成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getPopularLikedContent(Integer contentType, Integer limit, Integer days) {
        return Result.success("获取热门点赞内容成功", Collections.emptyList());
    }

    @Override
    public Result<List<LikeDTO>> batchAddLikes(Long userId, List<LikeDTO> likes) {
        return Result.success("批量添加点赞成功", Collections.emptyList());
    }

    @Override
    public Result<Void> batchRemoveLikes(Long userId, List<Long> likeIds) {
        return Result.success("批量取消点赞成功", null);
    }

    @Override
    public Result<Void> batchRemoveLikesByContent(Long userId, Integer contentType, List<Long> contentIds) {
        return Result.success("批量取消点赞成功", null);
    }

    @Override
    public Result<Integer> cleanupDeletedLikes(Long userId, Integer days) {
        return Result.success("清理已删除点赞成功", 0);
    }

    @Override
    public Result<LikeDTO> restoreLike(Long userId, Long likeId) {
        return Result.success("恢复点赞成功", new LikeDTO());
    }

    @Override
    public Result<Object> exportUserLikes(Long userId, Integer contentType, String format) {
        return Result.success("导出用户点赞成功", new Object());
    }

    @Override
    public Result<Object> getUserLikeTrend(Long userId, Integer days) {
        return Result.success("获取用户点赞趋势成功", new Object());
    }

    @Override
    public Result<Object> getUserLikePreference(Long userId) {
        return Result.success("获取用户点赞偏好成功", new Object());
    }

    @Override
    public Result<Object> getContentLikeTrend(Integer contentType, Long contentId, Integer days) {
        return Result.success("获取内容点赞趋势成功", new Object());
    }

    @Override
    public Result<Object> getUserLikeActivity(Long userId, Integer days) {
        return Result.success("获取用户点赞活跃度成功", new Object());
    }

    @Override
    public Result<List<Object>> getRecommendedContent(Long userId, Integer contentType, Integer limit) {
        return Result.success("获取推荐内容成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getSimilarUsers(Long userId, Integer limit) {
        return Result.success("获取相似用户成功", Collections.emptyList());
    }

    @Override
    public Result<List<Object>> getRecommendedLikes(Long userId, Integer contentType, Integer limit) {
        return Result.success("获取推荐点赞成功", Collections.emptyList());
    }

    @Override
    public Result<PageResult<Object>> getUserLikeInteractions(Long userId, PageRequest pageRequest) {
        return Result.success("获取用户点赞互动成功", PageResult.of(Collections.emptyList(), 0L, 0, 10));
    }

    @Override
    public Result<Object> getUserLikeInfluence(Long userId) {
        return Result.success("获取用户点赞影响力成功", new Object());
    }

    @Override
    public Result<Object> getContentLikeNetwork(Integer contentType, Long contentId) {
        return Result.success("获取内容点赞网络成功", new Object());
    }
}
