package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.SocialFeatureConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;
import com.jdl.aic.core.service.client.service.SocialFeatureConfigService;
import com.jdl.aic.core.service.dao.entity.primary.SocialFeatureConfig;
import com.jdl.aic.core.service.dao.mapper.primary.SocialFeatureConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 社交功能配置管理服务实现类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("socialFeatureConfigService")
public class SocialFeatureConfigServiceImpl implements SocialFeatureConfigService {

    @Autowired
    private SocialFeatureConfigMapper socialFeatureConfigMapper;

    // 支持的功能类型
    private static final List<String> SUPPORTED_FEATURE_TYPES = Arrays.asList(
            "like", "favorite", "share", "comment", "read_track"
    );

    @Override
    public Result<PageResult<SocialFeatureConfigDTO>> getConfigList(
            PageRequest pageRequest, GetSocialFeatureConfigListRequest request) {
        log.info("开始获取社交功能配置列表 - 入参: pageRequest={}, request={}",
                pageRequest, request);
        try {
            // 设置分页参数
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());

            // 构建查询条件
            SocialFeatureConfig queryCondition = new SocialFeatureConfig();
            if (request != null) {
                if (StringUtils.hasText(request.getContentType())) {
                    queryCondition.setContentType(request.getContentType());
                }
                if (StringUtils.hasText(request.getFeatureType())) {
                    queryCondition.setFeatureType(request.getFeatureType());
                }
                if (request.getIsEnabled() != null) {
                    queryCondition.setIsEnabled(request.getIsEnabled());
                }
            }

            // 查询配置列表
            List<SocialFeatureConfig> configs = socialFeatureConfigMapper.selectByCondition(queryCondition);
            PageInfo<SocialFeatureConfig> pageInfo = new PageInfo<>(configs);

            // 转换为DTO
            List<SocialFeatureConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            PageResult<SocialFeatureConfigDTO> pageResult = PageResult.of(configDTOs, pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize());

            log.info("获取社交功能配置列表成功 - 总数: {}, 当前页: {}", pageInfo.getTotal(), pageInfo.getPageNum());
            return Result.success(pageResult);

        } catch (Exception e) {
            log.error("获取社交功能配置列表失败", e);
            return Result.errorResult("CONFIG_LIST_QUERY_ERROR", "获取社交功能配置列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SocialFeatureConfigDTO> getConfigById(Long id) {
        log.info("开始根据ID获取社交功能配置 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 配置ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID不能为空");
            }

            SocialFeatureConfig config = socialFeatureConfigMapper.selectById(id);
            if (config == null) {
                log.warn("社交功能配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "社交功能配置不存在");
            }

            SocialFeatureConfigDTO configDTO = convertToDTO(config);
            log.info("根据ID获取社交功能配置成功 - id: {}, contentType: {}, featureType: {}", 
                    id, config.getContentType(), config.getFeatureType());
            return Result.success(configDTO);

        } catch (Exception e) {
            log.error("根据ID获取社交功能配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_QUERY_ERROR", "获取社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SocialFeatureConfigDTO> getConfigByContentAndFeature(GetSocialFeatureConfigByContentAndFeatureRequest request) {
        log.info("开始根据内容类型和功能类型获取配置 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getContentType()) || !StringUtils.hasText(request.getFeatureType())) {
                log.warn("参数校验失败 - 请求参数、内容类型和功能类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、内容类型和功能类型不能为空");
            }

            SocialFeatureConfig config = socialFeatureConfigMapper.selectByContentAndFeature(request.getContentType(), request.getFeatureType());
            if (config == null) {
                log.warn("社交功能配置不存在 - contentType: {}, featureType: {}", request.getContentType(), request.getFeatureType());
                return Result.errorResult("CONFIG_NOT_FOUND", "社交功能配置不存在");
            }

            SocialFeatureConfigDTO configDTO = convertToDTO(config);
            log.info("根据内容类型和功能类型获取配置成功 - contentType: {}, featureType: {}, id: {}",
                    request.getContentType(), request.getFeatureType(), config.getId());
            return Result.success(configDTO);

        } catch (Exception e) {
            log.error("根据内容类型和功能类型获取配置失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_QUERY_ERROR", "获取社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SocialFeatureConfigDTO> createConfig(SocialFeatureConfigDTO config) {
        log.info("开始创建社交功能配置 - 入参: config={}", config);
        try {
            if (config == null) {
                log.warn("参数校验失败 - 配置信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置信息不能为空");
            }

            // 检查内容类型和功能类型组合是否已存在
            if (StringUtils.hasText(config.getContentType()) && StringUtils.hasText(config.getFeatureType())) {
                int count = socialFeatureConfigMapper.countByContentAndFeature(config.getContentType(), config.getFeatureType());
                if (count > 0) {
                    log.warn("社交功能配置已存在 - contentType: {}, featureType: {}", 
                            config.getContentType(), config.getFeatureType());
                    return Result.errorResult("CONFIG_ALREADY_EXISTS", "该内容类型和功能类型的配置已存在");
                }
            }

            // 转换为实体并设置默认值
            SocialFeatureConfig entity = convertToEntity(config);
            entity.setCreatedAt(LocalDateTime.now());
            entity.setUpdatedAt(LocalDateTime.now());
            if (entity.getIsEnabled() == null) {
                entity.setIsEnabled(true);
            }

            // 插入数据库
            int result = socialFeatureConfigMapper.insert(entity);
            if (result > 0) {
                log.info("社交功能配置创建成功 - id: {}, contentType: {}, featureType: {}", 
                        entity.getId(), entity.getContentType(), entity.getFeatureType());
                SocialFeatureConfigDTO resultDto = convertToDTO(entity);
                return Result.success("社交功能配置创建成功", resultDto);
            } else {
                log.error("社交功能配置创建失败 - 数据库插入返回结果: {}", result);
                return Result.errorResult("CONFIG_CREATE_FAILED", "社交功能配置创建失败");
            }

        } catch (Exception e) {
            log.error("创建社交功能配置失败", e);
            return Result.errorResult("CONFIG_CREATE_ERROR", "创建社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<SocialFeatureConfigDTO> updateConfig(Long id, SocialFeatureConfigDTO config) {
        log.info("开始更新社交功能配置 - 入参: id={}, config={}", id, config);
        try {
            if (id == null || config == null) {
                log.warn("参数校验失败 - 配置ID和配置信息不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID和配置信息不能为空");
            }

            // 检查配置是否存在
            SocialFeatureConfig existingConfig = socialFeatureConfigMapper.selectById(id);
            if (existingConfig == null) {
                log.warn("社交功能配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "社交功能配置不存在");
            }

            // 检查内容类型和功能类型组合是否已存在（排除当前记录）
            if (StringUtils.hasText(config.getContentType()) && StringUtils.hasText(config.getFeatureType())) {
                if (!config.getContentType().equals(existingConfig.getContentType()) || 
                    !config.getFeatureType().equals(existingConfig.getFeatureType())) {
                    int count = socialFeatureConfigMapper.countByContentAndFeature(config.getContentType(), config.getFeatureType());
                    if (count > 0) {
                        log.warn("社交功能配置已存在 - contentType: {}, featureType: {}", 
                                config.getContentType(), config.getFeatureType());
                        return Result.errorResult("CONFIG_ALREADY_EXISTS", "该内容类型和功能类型的配置已存在");
                    }
                }
            }

            // 更新实体信息
            SocialFeatureConfig entity = convertToEntity(config);
            entity.setId(id);
            entity.setUpdatedAt(LocalDateTime.now());
            entity.setCreatedAt(existingConfig.getCreatedAt()); // 保持创建时间不变

            int result = socialFeatureConfigMapper.updateById(entity);
            if (result > 0) {
                log.info("社交功能配置更新成功 - id: {}, contentType: {}, featureType: {}", 
                        id, entity.getContentType(), entity.getFeatureType());
                SocialFeatureConfigDTO resultDto = convertToDTO(entity);
                return Result.success("社交功能配置更新成功", resultDto);
            } else {
                log.error("社交功能配置更新失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("CONFIG_UPDATE_FAILED", "社交功能配置更新失败");
            }

        } catch (Exception e) {
            log.error("更新社交功能配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_UPDATE_ERROR", "更新社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> deleteConfig(Long id) {
        log.info("开始删除社交功能配置 - 入参: id={}", id);
        try {
            if (id == null) {
                log.warn("参数校验失败 - 配置ID不能为空");
                return Result.errorResult("INVALID_PARAMETER", "配置ID不能为空");
            }

            // 检查配置是否存在
            SocialFeatureConfig existingConfig = socialFeatureConfigMapper.selectById(id);
            if (existingConfig == null) {
                log.warn("社交功能配置不存在 - id: {}", id);
                return Result.errorResult("CONFIG_NOT_FOUND", "社交功能配置不存在");
            }

            int result = socialFeatureConfigMapper.deleteById(id);
            if (result > 0) {
                log.info("社交功能配置删除成功 - id: {}, contentType: {}, featureType: {}", 
                        id, existingConfig.getContentType(), existingConfig.getFeatureType());
                return Result.success();
            } else {
                log.error("社交功能配置删除失败 - 数据库删除返回结果: {}", result);
                return Result.errorResult("CONFIG_DELETE_FAILED", "社交功能配置删除失败");
            }

        } catch (Exception e) {
            log.error("删除社交功能配置失败 - id: {}", id, e);
            return Result.errorResult("CONFIG_DELETE_ERROR", "删除社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> toggleConfigStatus(ToggleSocialFeatureConfigStatusRequest request) {
        log.info("开始切换社交功能配置状态 - 入参: request={}", request);
        try {
            if (request == null || request.getId() == null || request.getIsEnabled() == null) {
                log.warn("参数校验失败 - 请求参数、配置ID和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数、配置ID和状态不能为空");
            }

            // 检查配置是否存在
            SocialFeatureConfig existingConfig = socialFeatureConfigMapper.selectById(request.getId());
            if (existingConfig == null) {
                log.warn("社交功能配置不存在 - id: {}", request.getId());
                return Result.errorResult("CONFIG_NOT_FOUND", "社交功能配置不存在");
            }

            int result = socialFeatureConfigMapper.updateStatus(request.getId(), request.getIsEnabled());
            if (result > 0) {
                log.info("社交功能配置状态切换成功 - id: {}, isEnabled: {}", request.getId(), request.getIsEnabled());
                return Result.success();
            } else {
                log.error("社交功能配置状态切换失败 - 数据库更新返回结果: {}", result);
                return Result.errorResult("CONFIG_STATUS_UPDATE_FAILED", "社交功能配置状态切换失败");
            }

        } catch (Exception e) {
            log.error("切换社交功能配置状态失败 - request: {}", request, e);
            return Result.errorResult("CONFIG_STATUS_UPDATE_ERROR", "切换社交功能配置状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<SocialFeatureConfigDTO>> getConfigsByContentType(GetConfigsByContentTypeRequest request) {
        log.info("开始根据内容类型获取社交功能配置 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getContentType())) {
                log.warn("参数校验失败 - 请求参数和内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数和内容类型不能为空");
            }

            List<SocialFeatureConfig> configs = socialFeatureConfigMapper.selectByContentType(request.getContentType(), request.getIsEnabled());
            List<SocialFeatureConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("根据内容类型获取社交功能配置成功 - contentType: {}, 数量: {}", request.getContentType(), configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("根据内容类型获取社交功能配置失败 - request: {}", request, e);
            return Result.errorResult("CONFIGS_QUERY_ERROR", "获取社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<SocialFeatureConfigDTO>> getConfigsByFeatureType(GetSocialFeatureConfigsByFeatureTypeRequest request) {
        log.info("开始根据功能类型获取社交功能配置 - 入参: request={}", request);
        try {
            if (request == null || !StringUtils.hasText(request.getFeatureType())) {
                log.warn("参数校验失败 - 请求参数和功能类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "请求参数和功能类型不能为空");
            }

            List<SocialFeatureConfig> configs = socialFeatureConfigMapper.selectByFeatureType(request.getFeatureType(), request.getIsEnabled());
            List<SocialFeatureConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("根据功能类型获取社交功能配置成功 - featureType: {}, 数量: {}", request.getFeatureType(), configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("根据功能类型获取社交功能配置失败 - request: {}", request, e);
            return Result.errorResult("CONFIGS_QUERY_ERROR", "获取社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<SocialFeatureConfigDTO>> getAllEnabledConfigs() {
        log.info("开始获取所有启用的社交功能配置");
        try {
            List<SocialFeatureConfig> configs = socialFeatureConfigMapper.selectAllEnabled();
            List<SocialFeatureConfigDTO> configDTOs = configs.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            log.info("获取所有启用的社交功能配置成功 - 数量: {}", configDTOs.size());
            return Result.success(configDTOs);

        } catch (Exception e) {
            log.error("获取所有启用的社交功能配置失败", e);
            return Result.errorResult("ENABLED_CONFIGS_QUERY_ERROR", "获取所有启用的社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchUpdateStatusByContentType(String contentType, Boolean isEnabled) {
        log.info("开始批量更新内容类型的功能状态 - 入参: contentType={}, isEnabled={}", contentType, isEnabled);
        try {
            if (!StringUtils.hasText(contentType) || isEnabled == null) {
                log.warn("参数校验失败 - 内容类型和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型和状态不能为空");
            }

            int result = socialFeatureConfigMapper.updateStatusByContentType(contentType, isEnabled);
            log.info("批量更新内容类型的功能状态成功 - contentType: {}, isEnabled: {}, 影响行数: {}",
                    contentType, isEnabled, result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新内容类型的功能状态失败 - contentType: {}", contentType, e);
            return Result.errorResult("BATCH_UPDATE_ERROR", "批量更新内容类型的功能状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> batchUpdateStatusByFeatureType(String featureType, Boolean isEnabled) {
        log.info("开始批量更新功能类型的状态 - 入参: featureType={}, isEnabled={}", featureType, isEnabled);
        try {
            if (!StringUtils.hasText(featureType) || isEnabled == null) {
                log.warn("参数校验失败 - 功能类型和状态不能为空");
                return Result.errorResult("INVALID_PARAMETER", "功能类型和状态不能为空");
            }

            int result = socialFeatureConfigMapper.updateStatusByFeatureType(featureType, isEnabled);
            log.info("批量更新功能类型的状态成功 - featureType: {}, isEnabled: {}, 影响行数: {}",
                    featureType, isEnabled, result);
            return Result.success();

        } catch (Exception e) {
            log.error("批量更新功能类型的状态失败 - featureType: {}", featureType, e);
            return Result.errorResult("BATCH_UPDATE_ERROR", "批量更新功能类型的状态失败: " + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> checkConfigExists(String contentType, String featureType, Long excludeId) {
        log.info("开始检查社交功能配置是否存在 - 入参: contentType={}, featureType={}, excludeId={}",
                contentType, featureType, excludeId);
        try {
            if (!StringUtils.hasText(contentType) || !StringUtils.hasText(featureType)) {
                log.warn("参数校验失败 - 内容类型和功能类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型和功能类型不能为空");
            }

            SocialFeatureConfig config = socialFeatureConfigMapper.selectByContentAndFeature(contentType, featureType);
            boolean exists = config != null && (excludeId == null || !config.getId().equals(excludeId));

            log.info("检查社交功能配置是否存在完成 - contentType: {}, featureType: {}, exists: {}",
                    contentType, featureType, exists);
            return Result.success(exists);

        } catch (Exception e) {
            log.error("检查社交功能配置是否存在失败 - contentType: {}, featureType: {}", contentType, featureType, e);
            return Result.errorResult("CONFIG_CHECK_ERROR", "检查社交功能配置是否存在失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<SocialFeatureConfigDTO>> initDefaultConfigs(String contentType) {
        log.info("开始为内容类型初始化默认社交功能配置 - 入参: contentType={}", contentType);
        try {
            if (!StringUtils.hasText(contentType)) {
                log.warn("参数校验失败 - 内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "内容类型不能为空");
            }

            List<SocialFeatureConfigDTO> createdConfigs = new java.util.ArrayList<>();

            // 为每个支持的功能类型创建默认配置
            for (String featureType : SUPPORTED_FEATURE_TYPES) {
                // 检查是否已存在
                int count = socialFeatureConfigMapper.countByContentAndFeature(contentType, featureType);
                if (count == 0) {
                    SocialFeatureConfigDTO config = new SocialFeatureConfigDTO(contentType, featureType);
                    config.setIsEnabled(true);

                    Result<SocialFeatureConfigDTO> createResult = createConfig(config);
                    if (createResult.isSuccess()) {
                        createdConfigs.add(createResult.getData());
                    }
                }
            }

            log.info("为内容类型初始化默认社交功能配置成功 - contentType: {}, 创建数量: {}",
                    contentType, createdConfigs.size());
            return Result.success(createdConfigs);

        } catch (Exception e) {
            log.error("为内容类型初始化默认社交功能配置失败 - contentType: {}", contentType, e);
            return Result.errorResult("INIT_DEFAULT_CONFIGS_ERROR", "初始化默认社交功能配置失败: " + e.getMessage());
        }
    }

    @Override
    public Result<List<String>> getSupportedFeatureTypes() {
        log.info("获取支持的功能类型列表");
        return Result.success(SUPPORTED_FEATURE_TYPES);
    }

    @Override
    public Result<List<SocialFeatureConfigDTO>> copyConfigsToContentType(String sourceContentType, String targetContentType) {
        log.info("开始复制配置到新的内容类型 - 入参: sourceContentType={}, targetContentType={}",
                sourceContentType, targetContentType);
        try {
            if (!StringUtils.hasText(sourceContentType) || !StringUtils.hasText(targetContentType)) {
                log.warn("参数校验失败 - 源内容类型和目标内容类型不能为空");
                return Result.errorResult("INVALID_PARAMETER", "源内容类型和目标内容类型不能为空");
            }

            // 获取源内容类型的所有配置
            List<SocialFeatureConfig> sourceConfigs = socialFeatureConfigMapper.selectByContentType(sourceContentType, null);
            if (sourceConfigs.isEmpty()) {
                log.warn("源内容类型没有配置 - sourceContentType: {}", sourceContentType);
                return Result.errorResult("SOURCE_CONFIGS_NOT_FOUND", "源内容类型没有配置");
            }

            List<SocialFeatureConfigDTO> copiedConfigs = new java.util.ArrayList<>();

            // 复制每个配置到目标内容类型
            for (SocialFeatureConfig sourceConfig : sourceConfigs) {
                // 检查目标内容类型是否已有该功能类型的配置
                int count = socialFeatureConfigMapper.countByContentAndFeature(targetContentType, sourceConfig.getFeatureType());
                if (count == 0) {
                    SocialFeatureConfigDTO newConfig = new SocialFeatureConfigDTO(targetContentType, sourceConfig.getFeatureType());
                    newConfig.setIsEnabled(sourceConfig.getIsEnabled());
                    newConfig.setConfigJson(sourceConfig.getConfigJson());

                    Result<SocialFeatureConfigDTO> createResult = createConfig(newConfig);
                    if (createResult.isSuccess()) {
                        copiedConfigs.add(createResult.getData());
                    }
                }
            }

            log.info("复制配置到新的内容类型成功 - sourceContentType: {}, targetContentType: {}, 复制数量: {}",
                    sourceContentType, targetContentType, copiedConfigs.size());
            return Result.success(copiedConfigs);

        } catch (Exception e) {
            log.error("复制配置到新的内容类型失败 - sourceContentType: {}, targetContentType: {}",
                    sourceContentType, targetContentType, e);
            return Result.errorResult("COPY_CONFIGS_ERROR", "复制配置到新的内容类型失败: " + e.getMessage());
        }
    }

    /**
     * 实体转DTO
     */
    private SocialFeatureConfigDTO convertToDTO(SocialFeatureConfig entity) {
        if (entity == null) {
            return null;
        }
        SocialFeatureConfigDTO dto = new SocialFeatureConfigDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * DTO转实体
     */
    private SocialFeatureConfig convertToEntity(SocialFeatureConfigDTO dto) {
        if (dto == null) {
            return null;
        }
        SocialFeatureConfig entity = new SocialFeatureConfig();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }
}
