package com.jdl.aic.core.service.service;

import com.jdl.aic.core.service.dao.entity.portal.UserBadges;

import java.util.List;
import java.util.Map;

/**
 * 用户徽章关联服务接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface UserBadgesService {

    /**
     * 根据ID查询用户徽章关联
     */
    UserBadges getUserBadgeById(Long id);

    /**
     * 根据用户ID查询用户的所有徽章
     */
    List<UserBadges> getUserBadgesByUserId(Long userId);

    /**
     * 根据徽章ID查询拥有该徽章的所有用户
     */
    List<UserBadges> getUserBadgesByBadgeId(Long badgeId);

    /**
     * 根据用户ID和徽章ID查询关联记录
     */
    UserBadges getUserBadgeByUserIdAndBadgeId(Long userId, Long badgeId);

    /**
     * 查询所有用户徽章关联
     */
    List<UserBadges> getAllUserBadges();

    /**
     * 查询活跃的用户徽章关联
     */
    List<UserBadges> getActiveUserBadges();

    /**
     * 为用户授予徽章
     */
    boolean awardBadgeToUser(Long userId, Long badgeId, Long awardedBy);

    /**
     * 创建用户徽章关联
     */
    boolean createUserBadge(UserBadges userBadge);

    /**
     * 更新用户徽章关联
     */
    boolean updateUserBadge(UserBadges userBadge);

    /**
     * 删除用户徽章关联（物理删除）
     */
    boolean deleteUserBadge(Long id);

    /**
     * 软删除用户徽章关联
     */
    boolean softDeleteUserBadge(Long id);

    /**
     * 撤销用户的徽章
     */
    boolean revokeBadgeFromUser(Long userId, Long badgeId);

    /**
     * 检查用户是否拥有指定徽章
     */
    boolean hasUserBadge(Long userId, Long badgeId);

    /**
     * 统计用户徽章关联数量
     */
    int getUserBadgeCount();

    /**
     * 统计用户的徽章数量
     */
    int getUserBadgeCountByUserId(Long userId);

    /**
     * 统计徽章的用户数量
     */
    int getUserBadgeCountByBadgeId(Long badgeId);

    /**
     * 批量为用户授予徽章
     */
    boolean batchAwardBadgesToUser(Long userId, List<Long> badgeIds, Long awardedBy);

    /**
     * 批量撤销用户的徽章
     */
    boolean batchRevokeBadgesFromUser(Long userId, List<Long> badgeIds);

    /**
     * 批量创建用户徽章关联
     */
    boolean batchCreateUserBadges(List<UserBadges> userBadges);

    /**
     * 获取用户徽章统计信息
     */
    Map<String, Object> getUserBadgeStatistics(Long userId);
}
