package com.jdl.aic.core.service.service;

import com.jdl.aic.core.service.dao.entity.portal.Team;

import java.util.List;

/**
 * 团队服务接口
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
public interface TeamService {

    /**
     * 根据ID查询团队
     */
    Team getTeamById(Long id);

    /**
     * 根据名称查询团队
     */
    Team getTeamByName(String name);

    /**
     * 查询所有团队
     */
    List<Team> getAllTeams();

    /**
     * 查询活跃团队
     */
    List<Team> getActiveTeams();

    /**
     * 根据父团队ID查询子团队
     */
    List<Team> getTeamsByParentId(Long parentId);

    /**
     * 根据隐私设置查询团队
     */
    List<Team> getTeamsByPrivacy(String privacy);

    /**
     * 根据创建人查询团队
     */
    List<Team> getTeamsByCreatedBy(Long createdBy);

    /**
     * 创建团队
     */
    boolean createTeam(Team team);

    /**
     * 更新团队
     */
    boolean updateTeam(Team team);

    /**
     * 删除团队
     */
    boolean deleteTeam(Long id);

    /**
     * 更新团队活跃状态
     */
    boolean updateTeamActiveStatus(Long id, Integer isActive);

    /**
     * 检查团队名称是否存在
     */
    boolean isTeamNameExists(String name);

    /**
     * 获取团队总数
     */
    int getTeamCount();

    /**
     * 获取活跃团队数
     */
    int getActiveTeamCount();

    /**
     * 获取公开团队数
     */
    int getPublicTeamCount();

    /**
     * 搜索团队
     */
    List<Team> searchTeams(String keyword, Integer limit);

    /**
     * 根据条件查询团队
     */
    List<Team> getTeamsByCondition(String privacy, Integer isActive, Long parentId, String keyword);

    /**
     * 查询团队层级结构
     */
    List<Team> getTeamHierarchy(Long rootId);

    /**
     * 批量创建团队
     */
    boolean batchCreateTeams(List<Team> teams);

    /**
     * 批量更新团队状态
     */
    boolean batchUpdateTeamStatus(List<Long> ids, Integer isActive);

    /**
     * 获取根团队列表（没有父团队的团队）
     */
    List<Team> getRootTeams();

    /**
     * 检查团队是否为公开团队
     */
    boolean isPublicTeam(Long teamId);

    /**
     * 检查团队是否为私有团队
     */
    boolean isPrivateTeam(Long teamId);
}
