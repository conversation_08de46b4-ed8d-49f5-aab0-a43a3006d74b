package com.jdl.aic.core.service.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.enrollment.UserCourseEnrollmentDTO;
import com.jdl.aic.core.service.client.dto.request.enrollment.GetUserEnrollmentListRequest;
import com.jdl.aic.core.service.client.dto.request.enrollment.UpdateEnrollmentProgressRequest;
import com.jdl.aic.core.service.client.dto.request.enrollment.UpdateEnrollmentStatusRequest;
import com.jdl.aic.core.service.dao.entity.primary.LearningCourse;
import com.jdl.aic.core.service.dao.mapper.primary.LearningCourseMapper;
import com.jdl.aic.core.service.portal.client.UserCourseEnrollmentService;
import com.jdl.aic.core.service.dao.entity.portal.UserCourseEnrollment;
import com.jdl.aic.core.service.dao.mapper.portal.UserCourseEnrollmentMapper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户课程报名服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@Service("userCourseEnrollmentService")
public class UserCourseEnrollmentServiceImpl implements UserCourseEnrollmentService {

    @Resource
    private UserCourseEnrollmentMapper userCourseEnrollmentMapper;

    @Resource
    private LearningCourseMapper learningCourseMapper;

    @Override
    public Result<PageResult<UserCourseEnrollmentDTO>> getUserEnrollmentList(
            PageRequest pageRequest, GetUserEnrollmentListRequest request) {
        try {
            log.info("获取用户报名列表，请求参数：{}", request);
            
            // 设置分页
            PageHelper.startPage(pageRequest.getPage(), pageRequest.getSize());
            
            // 构建查询条件
            UserCourseEnrollment queryCondition = new UserCourseEnrollment();
            if (request.getUserId() != null) {
                queryCondition.setUserId(request.getUserId());
            }
            if (request.getCourseId() != null) {
                queryCondition.setCourseId(request.getCourseId());
            }
            if (StringUtils.hasText(request.getEnrollmentStatus())) {
                queryCondition.setEnrollmentStatus(request.getEnrollmentStatus());
            }
            if (StringUtils.hasText(request.getEnrollmentSource())) {
                queryCondition.setEnrollmentSource(request.getEnrollmentSource());
            }
            
            List<UserCourseEnrollment> enrollments;
            
            // 如果有搜索关键词，使用搜索方法
            if (StringUtils.hasText(request.getSearch())) {
                enrollments = userCourseEnrollmentMapper.searchEnrollments(
                    request.getUserId(), request.getSearch(), request.getEnrollmentStatus());
            } else if (StringUtils.hasText(request.getStartDate()) || StringUtils.hasText(request.getEndDate())) {
                // 如果有时间范围，使用时间范围查询
                enrollments = userCourseEnrollmentMapper.selectByDateRange(
                    request.getUserId(), request.getStartDate(), request.getEndDate());
            } else {
                // 普通条件查询
                enrollments = userCourseEnrollmentMapper.selectByCondition(queryCondition);
            }
            
            PageInfo<UserCourseEnrollment> pageInfo = new PageInfo<>(enrollments);
            
            // 转换为DTO
            List<UserCourseEnrollmentDTO> enrollmentDTOs = enrollments.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            // 使用静态方法创建PageResult
            PageResult<UserCourseEnrollmentDTO> pageResult = PageResult.of(
                    enrollmentDTOs,
                    pageInfo.getTotal(),
                    pageInfo.getPageNum() - 1, // PageResult使用0开始的页码
                    pageInfo.getPageSize()
            );
            
            return Result.success(pageResult);
            
        } catch (Exception e) {
            log.error("获取用户报名列表失败", e);
            return Result.errorResult("ENROLLMENT_LIST_ERROR", "获取用户报名列表失败：" + e.getMessage());
        }
    }

    @Override
    public Result<UserCourseEnrollmentDTO> getEnrollmentById(Long id) {
        try {
            log.info("根据ID获取报名详情，ID：{}", id);
            
            if (id == null) {
                return Result.errorResult("INVALID_PARAM", "报名ID不能为空");
            }

            UserCourseEnrollment enrollment = userCourseEnrollmentMapper.selectById(id);
            if (enrollment == null) {
                return Result.errorResult("ENROLLMENT_NOT_FOUND", "报名记录不存在");
            }

            UserCourseEnrollmentDTO enrollmentDTO = convertToDTO(enrollment);
            return Result.success(enrollmentDTO);

        } catch (Exception e) {
            log.error("根据ID获取报名详情失败，ID：{}", id, e);
            return Result.errorResult("ENROLLMENT_GET_ERROR", "获取报名详情失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<UserCourseEnrollmentDTO> createEnrollment(UserCourseEnrollmentDTO enrollmentDTO) {
        try {
            log.info("创建课程报名，参数：{}", enrollmentDTO);
            
            // 参数校验
            if (enrollmentDTO.getUserId() == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }
            if (enrollmentDTO.getCourseId() == null) {
                return Result.errorResult("INVALID_PARAM", "课程ID不能为空");
            }

            // 检查是否已经报名
            UserCourseEnrollment existingEnrollment = userCourseEnrollmentMapper
                    .selectByUserIdAndCourseId(enrollmentDTO.getUserId(), enrollmentDTO.getCourseId());
            if (existingEnrollment != null) {
                return Result.errorResult("ALREADY_ENROLLED", "用户已报名该课程");
            }
            
            // 转换为实体
            UserCourseEnrollment enrollment = convertToEntity(enrollmentDTO);
            enrollment.setEnrolledAt(LocalDateTime.now());
            enrollment.setCreatedAt(LocalDateTime.now());
            enrollment.setUpdatedAt(LocalDateTime.now());
            
            // 设置默认值
            if (enrollment.getEnrollmentStatus() == null) {
                enrollment.setEnrollmentStatus("ENROLLED");
            }
            if (enrollment.getProgressPercentage() == null) {
                enrollment.setProgressPercentage(BigDecimal.ZERO);
            }
            if (enrollment.getCompletedStages() == null) {
                enrollment.setCompletedStages(0);
            }
            if (enrollment.getStudyHours() == null) {
                enrollment.setStudyHours(BigDecimal.ZERO);
            }
            if (enrollment.getEnrollmentSource() == null) {
                enrollment.setEnrollmentSource("WEB");
            }
            
            int result = userCourseEnrollmentMapper.insert(enrollment);
            if (result > 0) {
                UserCourseEnrollmentDTO resultDTO = convertToDTO(enrollment);
                log.info("创建课程报名成功，ID：{}", enrollment.getId());
                return Result.success(resultDTO);
            } else {
                return Result.errorResult("ENROLLMENT_CREATE_ERROR", "创建课程报名失败");
            }

        } catch (Exception e) {
            log.error("创建课程报名失败", e);
            return Result.errorResult("ENROLLMENT_CREATE_ERROR", "创建课程报名失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<UserCourseEnrollmentDTO> updateEnrollment(Long id, UserCourseEnrollmentDTO enrollmentDTO) {
        try {
            log.info("更新报名信息，ID：{}，参数：{}", id, enrollmentDTO);
            
            if (id == null) {
                return Result.errorResult("INVALID_PARAM", "报名ID不能为空");
            }

            // 检查记录是否存在
            UserCourseEnrollment existingEnrollment = userCourseEnrollmentMapper.selectById(id);
            if (existingEnrollment == null) {
                return Result.errorResult("ENROLLMENT_NOT_FOUND", "报名记录不存在");
            }
            
            // 转换为实体并设置ID
            UserCourseEnrollment enrollment = convertToEntity(enrollmentDTO);
            enrollment.setId(id);
            enrollment.setUpdatedAt(LocalDateTime.now());
            
            int result = userCourseEnrollmentMapper.updateById(enrollment);
            if (result > 0) {
                UserCourseEnrollment updatedEnrollment = userCourseEnrollmentMapper.selectById(id);
                UserCourseEnrollmentDTO resultDTO = convertToDTO(updatedEnrollment);
                log.info("更新报名信息成功，ID：{}", id);
                return Result.success(resultDTO);
            } else {
                return Result.errorResult("ENROLLMENT_UPDATE_ERROR", "更新报名信息失败");
            }

        } catch (Exception e) {
            log.error("更新报名信息失败，ID：{}", id, e);
            return Result.errorResult("ENROLLMENT_UPDATE_ERROR", "更新报名信息失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> deleteEnrollment(Long id) {
        try {
            log.info("删除报名记录，ID：{}", id);
            
            if (id == null) {
                return Result.errorResult("INVALID_PARAM", "报名ID不能为空");
            }

            // 检查记录是否存在
            UserCourseEnrollment existingEnrollment = userCourseEnrollmentMapper.selectById(id);
            if (existingEnrollment == null) {
                return Result.errorResult("ENROLLMENT_NOT_FOUND", "报名记录不存在");
            }

            // 使用软删除
            int result = userCourseEnrollmentMapper.softDeleteById(id);
            if (result > 0) {
                log.info("删除报名记录成功，ID：{}", id);
                return Result.success();
            } else {
                return Result.errorResult("ENROLLMENT_DELETE_ERROR", "删除报名记录失败");
            }

        } catch (Exception e) {
            log.error("删除报名记录失败，ID：{}", id, e);
            return Result.errorResult("ENROLLMENT_DELETE_ERROR", "删除报名记录失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateEnrollmentStatus(UpdateEnrollmentStatusRequest request) {
        try {
            log.info("更新报名状态，请求参数：{}", request);
            
            if (request.getId() == null) {
                return Result.errorResult("INVALID_PARAM", "报名ID不能为空");
            }
            if (!StringUtils.hasText(request.getEnrollmentStatus())) {
                return Result.errorResult("INVALID_PARAM", "报名状态不能为空");
            }

            // 检查记录是否存在
            UserCourseEnrollment existingEnrollment = userCourseEnrollmentMapper.selectById(request.getId());
            if (existingEnrollment == null) {
                return Result.errorResult("ENROLLMENT_NOT_FOUND", "报名记录不存在");
            }

            int result = userCourseEnrollmentMapper.updateStatus(request.getId(), request.getEnrollmentStatus());
            if (result > 0) {
                log.info("更新报名状态成功，ID：{}，状态：{}", request.getId(), request.getEnrollmentStatus());
                return Result.success();
            } else {
                return Result.errorResult("ENROLLMENT_UPDATE_ERROR", "更新报名状态失败");
            }

        } catch (Exception e) {
            log.error("更新报名状态失败", e);
            return Result.errorResult("ENROLLMENT_UPDATE_ERROR", "更新报名状态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> updateEnrollmentProgress(UpdateEnrollmentProgressRequest request) {
        try {
            log.info("更新学习进度，请求参数：{}", request);
            
            if (request.getId() == null) {
                return Result.errorResult("INVALID_PARAM", "报名ID不能为空");
            }

            // 检查记录是否存在
            UserCourseEnrollment existingEnrollment = userCourseEnrollmentMapper.selectById(request.getId());
            if (existingEnrollment == null) {
                return Result.errorResult("ENROLLMENT_NOT_FOUND", "报名记录不存在");
            }

            int result = userCourseEnrollmentMapper.updateProgress(
                    request.getId(),
                    request.getProgressPercentage(),
                    request.getCompletedStages(),
                    request.getTotalStages(),
                    request.getStudyHours()
            );

            if (result > 0) {
                log.info("更新学习进度成功，ID：{}", request.getId());
                return Result.success();
            } else {
                return Result.errorResult("ENROLLMENT_UPDATE_ERROR", "更新学习进度失败");
            }

        } catch (Exception e) {
            log.error("更新学习进度失败", e);
            return Result.errorResult("ENROLLMENT_UPDATE_ERROR", "更新学习进度失败：" + e.getMessage());
        }
    }

    /**
     * 实体转DTO
     */
    private UserCourseEnrollmentDTO convertToDTO(UserCourseEnrollment enrollment) {
        if (enrollment == null) {
            return null;
        }
        
        UserCourseEnrollmentDTO dto = new UserCourseEnrollmentDTO();
        BeanUtils.copyProperties(enrollment, dto);

        Long courseId = enrollment.getCourseId();
        if (courseId != null) {
            LearningCourse course = learningCourseMapper.selectById(courseId);
            if (course != null) {
                dto.setCourseName(course.getName());
            }
        }

        return dto;
    }

    /**
     * DTO转实体
     */
    private UserCourseEnrollment convertToEntity(UserCourseEnrollmentDTO dto) {
        if (dto == null) {
            return null;
        }

        UserCourseEnrollment entity = new UserCourseEnrollment();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    @Override
    public Result<Boolean> isUserEnrolledInCourse(Long userId, Long courseId) {
        try {
            log.info("检查用户是否已报名指定课程，用户ID：{}，课程ID：{}", userId, courseId);

            if (userId == null || courseId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID和课程ID不能为空");
            }

            UserCourseEnrollment enrollment = userCourseEnrollmentMapper
                    .selectByUserIdAndCourseId(userId, courseId);

            boolean isEnrolled = enrollment != null;
            return Result.success(isEnrolled);

        } catch (Exception e) {
            log.error("检查用户是否已报名指定课程失败，用户ID：{}，课程ID：{}", userId, courseId, e);
            return Result.errorResult("ENROLLMENT_CHECK_ERROR", "检查报名状态失败：" + e.getMessage());
        }
    }

    @Override
    public Result<UserCourseEnrollmentDTO> getUserCourseEnrollment(Long userId, Long courseId) {
        try {
            log.info("获取用户在指定课程的报名信息，用户ID：{}，课程ID：{}", userId, courseId);

            if (userId == null || courseId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID和课程ID不能为空");
            }

            UserCourseEnrollment enrollment = userCourseEnrollmentMapper
                    .selectByUserIdAndCourseId(userId, courseId);

            if (enrollment == null) {
                return Result.errorResult("ENROLLMENT_NOT_FOUND", "用户未报名该课程");
            }

            UserCourseEnrollmentDTO enrollmentDTO = convertToDTO(enrollment);
            return Result.success(enrollmentDTO);

        } catch (Exception e) {
            log.error("获取用户课程报名信息失败，用户ID：{}，课程ID：{}", userId, courseId, e);
            return Result.errorResult("ENROLLMENT_GET_ERROR", "获取报名信息失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<UserCourseEnrollmentDTO>> getUserEnrollments(Long userId) {
        try {
            log.info("获取用户的所有报名课程，用户ID：{}", userId);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }

            List<UserCourseEnrollment> enrollments = userCourseEnrollmentMapper.selectByUserId(userId);
            List<UserCourseEnrollmentDTO> enrollmentDTOs = enrollments.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            return Result.success(enrollmentDTOs);

        } catch (Exception e) {
            log.error("获取用户的所有报名课程失败，用户ID：{}", userId, e);
            return Result.errorResult("ENROLLMENT_LIST_ERROR", "获取用户报名课程失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<UserCourseEnrollmentDTO>> getCourseEnrollments(Long courseId) {
        try {
            log.info("获取课程的所有报名用户，课程ID：{}", courseId);

            if (courseId == null) {
                return Result.errorResult("INVALID_PARAM", "课程ID不能为空");
            }

            List<UserCourseEnrollment> enrollments = userCourseEnrollmentMapper.selectByCourseId(courseId);
            List<UserCourseEnrollmentDTO> enrollmentDTOs = enrollments.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            return Result.success(enrollmentDTOs);

        } catch (Exception e) {
            log.error("获取课程的所有报名用户失败，课程ID：{}", courseId, e);
            return Result.errorResult("ENROLLMENT_LIST_ERROR", "获取课程报名用户失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<UserCourseEnrollmentDTO>> getUserInProgressCourses(Long userId) {
        try {
            log.info("获取用户正在学习的课程，用户ID：{}", userId);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }

            List<UserCourseEnrollment> enrollments = userCourseEnrollmentMapper
                    .selectByUserIdAndStatus(userId, "IN_PROGRESS");
            List<UserCourseEnrollmentDTO> enrollmentDTOs = enrollments.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            return Result.success(enrollmentDTOs);

        } catch (Exception e) {
            log.error("获取用户正在学习的课程失败，用户ID：{}", userId, e);
            return Result.errorResult("ENROLLMENT_LIST_ERROR", "获取正在学习的课程失败：" + e.getMessage());
        }
    }

    @Override
    public Result<List<UserCourseEnrollmentDTO>> getUserCompletedCourses(Long userId) {
        try {
            log.info("获取用户已完成的课程，用户ID：{}", userId);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }

            List<UserCourseEnrollment> enrollments = userCourseEnrollmentMapper
                    .selectByUserIdAndStatus(userId, "COMPLETED");
            List<UserCourseEnrollmentDTO> enrollmentDTOs = enrollments.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            return Result.success(enrollmentDTOs);

        } catch (Exception e) {
            log.error("获取用户已完成的课程失败，用户ID：{}", userId, e);
            return Result.errorResult("ENROLLMENT_LIST_ERROR", "获取已完成的课程失败：" + e.getMessage());
        }
    }

    @Override
    public Result<UserCourseEnrollmentDTO> getUserLearningStats(Long userId) {
        try {
            log.info("获取用户学习统计信息，用户ID：{}", userId);

            if (userId == null) {
                return Result.errorResult("INVALID_PARAM", "用户ID不能为空");
            }

            UserCourseEnrollment stats = userCourseEnrollmentMapper.getUserLearningStats(userId);
            UserCourseEnrollmentDTO statsDTO = convertToDTO(stats);

            return Result.success(statsDTO);

        } catch (Exception e) {
            log.error("获取用户学习统计信息失败，用户ID：{}", userId, e);
            return Result.errorResult("STATS_GET_ERROR", "获取学习统计信息失败：" + e.getMessage());
        }
    }

    @Override
    public Result<UserCourseEnrollmentDTO> getCourseEnrollmentStats(Long courseId) {
        try {
            log.info("获取课程报名统计信息，课程ID：{}", courseId);

            if (courseId == null) {
                return Result.errorResult("INVALID_PARAM", "课程ID不能为空");
            }

            UserCourseEnrollment stats = userCourseEnrollmentMapper.getCourseEnrollmentStats(courseId);
            UserCourseEnrollmentDTO statsDTO = convertToDTO(stats);

            return Result.success(statsDTO);

        } catch (Exception e) {
            log.error("获取课程报名统计信息失败，课程ID：{}", courseId, e);
            return Result.errorResult("STATS_GET_ERROR", "获取课程报名统计信息失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<Void> batchUpdateStudyHours(List<Long> enrollmentIds, Double additionalHours) {
        try {
            log.info("批量更新学习时长，报名ID列表：{}，增加时长：{}", enrollmentIds, additionalHours);

            if (enrollmentIds == null || enrollmentIds.isEmpty()) {
                return Result.errorResult("INVALID_PARAM", "报名ID列表不能为空");
            }
            if (additionalHours == null || additionalHours < 0) {
                return Result.errorResult("INVALID_PARAM", "增加的学习时长不能为空或小于0");
            }

            BigDecimal additionalHoursBD = BigDecimal.valueOf(additionalHours);
            int result = userCourseEnrollmentMapper.batchUpdateStudyHours(enrollmentIds, additionalHoursBD);

            if (result > 0) {
                log.info("批量更新学习时长成功，影响记录数：{}", result);
                return Result.success();
            } else {
                return Result.errorResult("ENROLLMENT_UPDATE_ERROR", "批量更新学习时长失败");
            }

        } catch (Exception e) {
            log.error("批量更新学习时长失败", e);
            return Result.errorResult("ENROLLMENT_UPDATE_ERROR", "批量更新学习时长失败：" + e.getMessage());
        }
    }
}
