package com.jdl.aic.core.service.service.impl;

import com.jdl.aic.core.service.dao.entity.portal.UserBadges;
import com.jdl.aic.core.service.dao.mapper.portal.UserBadgesMapper;
import com.jdl.aic.core.service.service.UserBadgesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 用户徽章关联服务实现类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Service
public class UserBadgesServiceImpl implements UserBadgesService {

    @Autowired
    private UserBadgesMapper userBadgesMapper;

    @Override
    public UserBadges getUserBadgeById(Long id) {
        if (id == null) {
            return null;
        }
        return userBadgesMapper.selectById(id);
    }

    @Override
    public List<UserBadges> getUserBadgesByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        return userBadgesMapper.selectByUserId(userId);
    }

    @Override
    public List<UserBadges> getUserBadgesByBadgeId(Long badgeId) {
        if (badgeId == null) {
            return null;
        }
        return userBadgesMapper.selectByBadgeId(badgeId);
    }

    @Override
    public UserBadges getUserBadgeByUserIdAndBadgeId(Long userId, Long badgeId) {
        if (userId == null || badgeId == null) {
            return null;
        }
        return userBadgesMapper.selectByUserIdAndBadgeId(userId, badgeId);
    }

    @Override
    public List<UserBadges> getAllUserBadges() {
        return userBadgesMapper.selectAll();
    }

    @Override
    public List<UserBadges> getActiveUserBadges() {
        return userBadgesMapper.selectActive();
    }

    @Override
    public boolean awardBadgeToUser(Long userId, Long badgeId, Long awardedBy) {
        if (userId == null || badgeId == null) {
            return false;
        }
        
        // 检查用户是否已经拥有该徽章
        if (hasUserBadge(userId, badgeId)) {
            return false; // 已经拥有，不重复授予
        }
        
        UserBadges userBadge = new UserBadges();
        userBadge.setUser_id(userId);
        userBadge.setBadge_id(badgeId);
        userBadge.setEarned_at(LocalDateTime.now());
        userBadge.setCreated_at(LocalDateTime.now());
        userBadge.setUpdated_at(LocalDateTime.now());
        userBadge.setCreated_by(awardedBy);
        userBadge.setDeleted(0);
        
        return userBadgesMapper.insert(userBadge) > 0;
    }

    @Override
    public boolean createUserBadge(UserBadges userBadge) {
        if (userBadge == null || userBadge.getUser_id() == null || userBadge.getBadge_id() == null) {
            return false;
        }
        
        // 设置创建时间和默认值
        LocalDateTime now = LocalDateTime.now();
        if (userBadge.getEarned_at() == null) {
            userBadge.setEarned_at(now);
        }
        userBadge.setCreated_at(now);
        userBadge.setUpdated_at(now);
        if (userBadge.getDeleted() == null) {
            userBadge.setDeleted(0);
        }
        
        return userBadgesMapper.insert(userBadge) > 0;
    }

    @Override
    public boolean updateUserBadge(UserBadges userBadge) {
        if (userBadge == null || userBadge.getId() == null) {
            return false;
        }
        
        userBadge.setUpdated_at(LocalDateTime.now());
        return userBadgesMapper.updateById(userBadge) > 0;
    }

    @Override
    public boolean deleteUserBadge(Long id) {
        if (id == null) {
            return false;
        }
        return userBadgesMapper.deleteById(id) > 0;
    }

    @Override
    public boolean softDeleteUserBadge(Long id) {
        if (id == null) {
            return false;
        }
        return userBadgesMapper.softDeleteById(id) > 0;
    }

    @Override
    public boolean revokeBadgeFromUser(Long userId, Long badgeId) {
        if (userId == null || badgeId == null) {
            return false;
        }
        return userBadgesMapper.deleteByUserIdAndBadgeId(userId, badgeId) > 0;
    }

    @Override
    public boolean hasUserBadge(Long userId, Long badgeId) {
        if (userId == null || badgeId == null) {
            return false;
        }
        return userBadgesMapper.existsByUserIdAndBadgeId(userId, badgeId);
    }

    @Override
    public int getUserBadgeCount() {
        return userBadgesMapper.count();
    }

    @Override
    public int getUserBadgeCountByUserId(Long userId) {
        if (userId == null) {
            return 0;
        }
        return userBadgesMapper.countByUserId(userId);
    }

    @Override
    public int getUserBadgeCountByBadgeId(Long badgeId) {
        if (badgeId == null) {
            return 0;
        }
        return userBadgesMapper.countByBadgeId(badgeId);
    }

    @Override
    public boolean batchAwardBadgesToUser(Long userId, List<Long> badgeIds, Long awardedBy) {
        if (userId == null || badgeIds == null || badgeIds.isEmpty()) {
            return false;
        }
        
        List<UserBadges> userBadges = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (Long badgeId : badgeIds) {
            // 检查是否已经拥有该徽章
            if (!hasUserBadge(userId, badgeId)) {
                UserBadges userBadge = new UserBadges();
                userBadge.setUser_id(userId);
                userBadge.setBadge_id(badgeId);
                userBadge.setEarned_at(now);
                userBadge.setCreated_at(now);
                userBadge.setUpdated_at(now);
                userBadge.setCreated_by(awardedBy);
                userBadge.setDeleted(0);
                userBadges.add(userBadge);
            }
        }
        
        if (userBadges.isEmpty()) {
            return false; // 没有新的徽章需要授予
        }
        
        return userBadgesMapper.batchInsert(userBadges) > 0;
    }

    @Override
    public boolean batchRevokeBadgesFromUser(Long userId, List<Long> badgeIds) {
        if (userId == null || badgeIds == null || badgeIds.isEmpty()) {
            return false;
        }
        return userBadgesMapper.batchDeleteByUserId(userId, badgeIds) > 0;
    }

    @Override
    public boolean batchCreateUserBadges(List<UserBadges> userBadges) {
        if (userBadges == null || userBadges.isEmpty()) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        for (UserBadges userBadge : userBadges) {
            if (userBadge.getEarned_at() == null) {
                userBadge.setEarned_at(now);
            }
            if (userBadge.getCreated_at() == null) {
                userBadge.setCreated_at(now);
            }
            if (userBadge.getUpdated_at() == null) {
                userBadge.setUpdated_at(now);
            }
            if (userBadge.getDeleted() == null) {
                userBadge.setDeleted(0);
            }
        }
        
        return userBadgesMapper.batchInsert(userBadges) > 0;
    }

    @Override
    public Map<String, Object> getUserBadgeStatistics(Long userId) {
        if (userId == null) {
            return null;
        }
        List<Object> statistics = userBadgesMapper.getUserBadgeStatistics(userId);
        if (statistics != null && !statistics.isEmpty()) {
            return (Map<String, Object>) statistics.get(0);
        }
        return null;
    }
}
