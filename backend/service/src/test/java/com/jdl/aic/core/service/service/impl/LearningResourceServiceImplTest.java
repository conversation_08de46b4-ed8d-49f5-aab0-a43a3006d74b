package com.jdl.aic.core.service.service.impl;

import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.dao.entity.primary.Category;
import com.jdl.aic.core.service.dao.entity.primary.ContentCategoryRelation;
import com.jdl.aic.core.service.dao.entity.primary.LearningResource;
import com.jdl.aic.core.service.dao.mapper.primary.CategoryMapper;
import com.jdl.aic.core.service.dao.mapper.primary.ContentCategoryRelationMapper;
import com.jdl.aic.core.service.dao.mapper.primary.LearningResourceMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * LearningResourceServiceImpl 测试类
 */
@ExtendWith(MockitoExtension.class)
class LearningResourceServiceImplTest {

    @Mock
    private CategoryMapper categoryMapper;

    @Mock
    private ContentCategoryRelationMapper contentCategoryRelationMapper;

    @Mock
    private LearningResourceMapper learningResourceMapper;

    @InjectMocks
    private LearningResourceServiceImpl learningResourceService;

    private Category testCategory1;
    private Category testCategory2;
    private ContentCategoryRelation testRelation1;
    private ContentCategoryRelation testRelation2;
    private LearningResource testResource1;
    private LearningResource testResource2;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testCategory1 = new Category();
        testCategory1.setId(1L);
        testCategory1.setName("编程语言");
        testCategory1.setParentId(null);
        testCategory1.setContentCategory(ContentType.LEARNING_RESOURCE.getValue());
        testCategory1.setIconUrl("http://example.com/programming.png");
        testCategory1.setSortOrder(1);
        testCategory1.setIsActive(true);

        testCategory2 = new Category();
        testCategory2.setId(2L);
        testCategory2.setName("Java教程");
        testCategory2.setParentId(1L);
        testCategory2.setContentCategory(ContentType.LEARNING_RESOURCE.getValue());
        testCategory2.setIconUrl("http://example.com/java.png");
        testCategory2.setSortOrder(2);
        testCategory2.setIsActive(true);

        testRelation1 = new ContentCategoryRelation();
        testRelation1.setId(1L);
        testRelation1.setContentId(101L);
        testRelation1.setCategoryId(1L);
        testRelation1.setContentType(ContentType.LEARNING_RESOURCE.getValue());
        testRelation1.setCreatedAt(LocalDateTime.now());

        testRelation2 = new ContentCategoryRelation();
        testRelation2.setId(2L);
        testRelation2.setContentId(102L);
        testRelation2.setCategoryId(1L);
        testRelation2.setContentType(ContentType.LEARNING_RESOURCE.getValue());
        testRelation2.setCreatedAt(LocalDateTime.now());

        testResource1 = new LearningResource();
        testResource1.setId(101L);
        testResource1.setTitle("Java基础教程");
        testResource1.setStatus("PUBLISHED");

        testResource2 = new LearningResource();
        testResource2.setId(102L);
        testResource2.setTitle("Java高级特性");
        testResource2.setStatus("PUBLISHED");
    }

    @Test
    void testGetResourceCategoryStatistics_Success() {
        // 准备Mock数据
        when(categoryMapper.selectByContentCategory(ContentType.LEARNING_RESOURCE.getValue(), true))
                .thenReturn(Arrays.asList(testCategory1, testCategory2));
        
        when(categoryMapper.selectByParentId(1L))
                .thenReturn(Arrays.asList(testCategory2));
        
        when(categoryMapper.selectByParentId(2L))
                .thenReturn(Arrays.asList());
        
        when(contentCategoryRelationMapper.selectByCategoryId(1L))
                .thenReturn(Arrays.asList(testRelation1, testRelation2));
        
        when(contentCategoryRelationMapper.selectByCategoryId(2L))
                .thenReturn(Arrays.asList());
        
        when(learningResourceMapper.selectById(101L))
                .thenReturn(testResource1);
        
        when(learningResourceMapper.selectById(102L))
                .thenReturn(testResource2);

        // 执行测试
        Result<List<CategoryStatisticsDTO>> result = learningResourceService.getResourceCategoryStatistics();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(2, result.getData().size());

        CategoryStatisticsDTO category1Stats = result.getData().get(0);
        assertEquals("1", category1Stats.getCategoryId());
        assertEquals("编程语言", category1Stats.getCategoryName());
        assertEquals(2, category1Stats.getResourceCount());
        assertEquals(0, category1Stats.getCourseCount());
        assertNotNull(category1Stats.getChildren());
        assertEquals(1, category1Stats.getChildren().size());

        CategoryStatisticsDTO category2Stats = result.getData().get(1);
        assertEquals("2", category2Stats.getCategoryId());
        assertEquals("Java教程", category2Stats.getCategoryName());
        assertEquals(0, category2Stats.getResourceCount());
        assertEquals("1", category2Stats.getParentCategoryId());
    }

    @Test
    void testGetResourceCategoryStatistics_EmptyCategories() {
        // 准备Mock数据 - 空分类列表
        when(categoryMapper.selectByContentCategory(ContentType.LEARNING_RESOURCE.getValue(), true))
                .thenReturn(Arrays.asList());

        // 执行测试
        Result<List<CategoryStatisticsDTO>> result = learningResourceService.getResourceCategoryStatistics();

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(0, result.getData().size());
    }

    @Test
    void testGetResourceCategoryStatistics_Exception() {
        // 准备Mock数据 - 抛出异常
        when(categoryMapper.selectByContentCategory(ContentType.LEARNING_RESOURCE.getValue(), true))
                .thenThrow(new RuntimeException("Database error"));

        // 执行测试
        Result<List<CategoryStatisticsDTO>> result = learningResourceService.getResourceCategoryStatistics();

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("RESOURCE_CATEGORY_STATISTICS_ERROR", result.getCode());
        assertTrue(result.getMessage().contains("获取学习资源分类统计信息失败"));
    }

    @Test
    void testGetResourceCategoryStatistics_WithDeletedResource() {
        // 准备测试数据 - 包含已删除的资源
        LearningResource deletedResource = new LearningResource();
        deletedResource.setId(103L);
        deletedResource.setTitle("已删除资源");
        deletedResource.setStatus("DELETED");

        ContentCategoryRelation deletedRelation = new ContentCategoryRelation();
        deletedRelation.setId(3L);
        deletedRelation.setContentId(103L);
        deletedRelation.setCategoryId(1L);
        deletedRelation.setContentType(ContentType.LEARNING_RESOURCE.getValue());

        // 准备Mock数据
        when(categoryMapper.selectByContentCategory(ContentType.LEARNING_RESOURCE.getValue(), true))
                .thenReturn(Arrays.asList(testCategory1));
        
        when(categoryMapper.selectByParentId(1L))
                .thenReturn(Arrays.asList());
        
        when(contentCategoryRelationMapper.selectByCategoryId(1L))
                .thenReturn(Arrays.asList(testRelation1, deletedRelation));
        
        when(learningResourceMapper.selectById(101L))
                .thenReturn(testResource1);
        
        when(learningResourceMapper.selectById(103L))
                .thenReturn(deletedResource);

        // 执行测试
        Result<List<CategoryStatisticsDTO>> result = learningResourceService.getResourceCategoryStatistics();

        // 验证结果 - 已删除的资源不应该被统计
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getData().size());
        assertEquals(1, result.getData().get(0).getResourceCount()); // 只统计有效资源
    }
}
