# 测试环境配置文件
spring:
  profiles:
    active: test
  
  # 数据源配置 - 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
    
  # H2 数据库控制台配置（仅测试环境）
  h2:
    console:
      enabled: true
      path: /h2-console
      
  # JPA 配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 测试时自动创建和删除表
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
        
  # MyBatis 配置
  mybatis:
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: com.jdl.aic.core.service.dao.entity
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
      
# 日志配置
logging:
  level:
    com.jdl.aic.core.service: DEBUG
    org.springframework.transaction: DEBUG
    org.mybatis: DEBUG
    
# 测试相关配置
test:
  # 是否启用测试数据初始化
  data:
    init: true
  # 测试超时时间（秒）
  timeout: 30
