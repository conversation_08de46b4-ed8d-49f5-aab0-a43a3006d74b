# AIC Core Service Backend

<div align="center">

![Java](https://img.shields.io/badge/Java-1.8-orange.svg)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-green.svg)
![MySQL](https://img.shields.io/badge/MySQL-8.0-blue.svg)
![MyBatis](https://img.shields.io/badge/MyBatis-2.3.1-red.svg)
![Maven](https://img.shields.io/badge/Maven-3.6+-blue.svg)

企业级管理后台系统后端服务，基于Spring Boot多模块架构

[前端项目](../frontend/README.md) | [项目文档](../README.md) | [OAuth2配置](../OAuth2-解决方案.md)

</div>

## ✨ 特性

### 🎯 核心功能
- **用户管理** - 用户CRUD、登录认证、权限控制
- **OAuth2认证** - Google、GitHub社交登录支持
- **JWT令牌** - 无状态认证、令牌管理、自动刷新
- **服务管理** - 微服务配置、状态监控、版本控制
- **数据安全** - SQL注入防护、XSS防护、CORS跨域
- **监控运维** - Druid监控、健康检查、日志管理

### 🛠️ 技术特性
- **模块化架构** - Maven多模块，职责分离
- **数据库优化** - 连接池、事务管理、缓存机制
- **安全防护** - Spring Security、OAuth2、JWT
- **API设计** - RESTful API、统一返回格式
- **开发友好** - 热重载、Mock数据、调试工具

## 🛠️ 技术栈

### 核心框架
- **Java 1.8** - 编程语言
- **Spring Boot 2.7.18** - 应用框架
- **Spring Security** - 安全框架
- **Spring Security OAuth2** - OAuth2认证
- **Maven 3.6+** - 构建工具

### 数据层
- **MySQL 8.0** - 关系型数据库
- **MyBatis 2.3.1** - ORM框架
- **Druid 1.2.18** - 数据库连接池

### 工具库
- **FastJSON 1.2.83** - JSON处理
- **JJWT 0.11.5** - JWT令牌处理

## 🚀 快速开始

### 环境要求
- **JDK**: 1.8+
- **Maven**: 3.6+
- **MySQL**: 8.0+

### 安装和运行

```bash
# 1. 进入后端目录
cd backend

# 2. 编译项目
mvn clean compile

# 3. 打包项目
mvn clean package

# 4. 运行项目
cd web-api
mvn spring-boot:run

# 或者运行打包后的jar文件
java -jar web-api/target/web-api-1.0.0.jar
```

### 数据库配置

```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE ai_community_shared CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 导入初始数据
mysql -u root -p ai_community_shared < sql/shared_init.sql
```

### 访问地址
- **API服务**: http://localhost:8080/api
- **健康检查**: http://localhost:8080/api/health
- **Druid监控**: http://localhost:8080/druid (admin/123456)

### 默认账号
- **用户名**: admin
- **密码**: 123456

## 📁 项目结构

```
backend/
├── common/                 # 公共模块
│   ├── src/main/java/com/example/common/
│   │   ├── entity/         # 基础实体类
│   │   ├── result/         # 统一返回结果
│   │   └── util/           # 工具类
│   └── pom.xml
├── dao/                    # 数据访问层
│   ├── src/main/java/com/example/dao/
│   │   ├── entity/         # 实体类
│   │   └── mapper/         # MyBatis Mapper接口
│   ├── src/main/resources/mapper/  # MyBatis XML映射文件
│   └── pom.xml
├── service/                # 业务逻辑层
│   ├── src/main/java/com/example/service/
│   │   ├── impl/           # Service实现类
│   │   ├── JwtTokenService.java
│   │   ├── OAuth2UserService.java
│   │   └── UserService.java
│   └── pom.xml
├── web-api/                # Web API层
│   ├── src/main/java/com/example/
│   │   ├── controller/     # 控制器
│   │   ├── config/         # 配置类
│   │   └── WebApiApplication.java
│   ├── src/main/resources/
│   │   ├── application.yml # 主配置文件
│   │   └── mapper/         # MyBatis映射文件
│   └── pom.xml
├── sql/                    # 数据库脚本
│   └── init.sql           # 初始化脚本
├── pom.xml                 # 父级POM文件
└── README.md              # 项目说明
```

## 🔧 核心功能

### 认证授权
- **JWT认证** - 无状态令牌认证，支持访问令牌和刷新令牌
- **OAuth2登录** - Google、GitHub社交登录集成
- **用户管理** - 用户CRUD操作、角色权限管理
- **令牌管理** - 令牌生成、验证、刷新、撤销

### API接口
- **用户接口** - 登录、注册、用户信息管理
- **认证接口** - JWT令牌操作、OAuth2回调处理
- **系统接口** - 健康检查、系统信息、监控数据
- **统一响应** - 标准化API响应格式

### 数据安全
- **SQL注入防护** - MyBatis参数化查询
- **XSS防护** - 输入数据过滤和转义
- **CORS跨域** - 跨域请求配置
- **密码加密** - BCrypt密码哈希

### 监控运维
- **Druid监控** - 数据库连接池监控
- **健康检查** - 应用状态检查接口
- **日志管理** - 结构化日志输出
- **性能监控** - SQL执行监控

## 📚 API接口

### 认证接口
```
POST /api/auth/login              # 用户登录
POST /api/auth/refresh            # 刷新令牌
POST /api/auth/logout             # 用户登出
GET  /api/auth/me                 # 获取当前用户信息
```

### OAuth2接口
```
GET  /login/oauth2/code/google    # Google OAuth2回调
GET  /login/oauth2/code/github    # GitHub OAuth2回调
POST /api/auth/oauth2/bind        # 绑定OAuth2账号
POST /api/auth/oauth2/unbind      # 解绑OAuth2账号
```

### 用户管理接口
```
GET    /api/user/list             # 获取用户列表
GET    /api/user/{id}             # 根据ID获取用户
POST   /api/user/create           # 创建用户
PUT    /api/user/update           # 更新用户信息
DELETE /api/user/{id}             # 删除用户
GET    /api/user/count            # 获取用户数量
```

### JWT令牌管理
```
GET    /api/jwt/tokens            # 获取用户令牌列表
POST   /api/jwt/revoke            # 撤销指定令牌
POST   /api/jwt/revoke-all        # 撤销所有令牌
GET    /api/jwt/verify            # 验证令牌有效性
```

### 系统接口
```
GET /api/health                   # 健康检查
GET /api/welcome                  # 欢迎信息
```

### 响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

## ⚙️ 配置说明

### 数据库配置
```yaml
# application.yml
spring:
  datasource:
    url: *********************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
```

### OAuth2配置
```yaml
spring:
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ${GOOGLE_CLIENT_ID:your-google-client-id}
            client-secret: ${GOOGLE_CLIENT_SECRET:your-google-client-secret}
          github:
            client-id: ${GITHUB_CLIENT_ID:your-github-client-id}
            client-secret: ${GITHUB_CLIENT_SECRET:your-github-client-secret}
```

### JWT配置
```yaml
jwt:
  secret: your-jwt-secret-key
  access-token-expiration: 3600000    # 1小时
  refresh-token-expiration: 604800000 # 7天
```

### Druid监控配置
```yaml
spring:
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        login-username: admin
        login-password: 123456
```

## 🛠️ 开发指南

### 添加新接口

1. **创建实体类**
```java
// dao/src/main/java/com/example/dao/entity/
@Entity
public class YourEntity {
    private Long id;
    private String name;
    // getter/setter方法
}
```

2. **创建Mapper接口**
```java
// dao/src/main/java/com/example/dao/mapper/
@Mapper
public interface YourMapper {
    List<YourEntity> findAll();
    YourEntity findById(Long id);
}
```

3. **创建Service**
```java
// service/src/main/java/com/example/service/
public interface YourService {
    List<YourEntity> getAllEntities();
}

@Service
public class YourServiceImpl implements YourService {
    @Autowired
    private YourMapper yourMapper;

    public List<YourEntity> getAllEntities() {
        return yourMapper.findAll();
    }
}
```

4. **创建Controller**
```java
// web-api/src/main/java/com/example/controller/
@RestController
@RequestMapping("/api/your-resource")
public class YourController {
    @Autowired
    private YourService yourService;

    @GetMapping("/list")
    public Result<List<YourEntity>> getAll() {
        return Result.success(yourService.getAllEntities());
    }
}
```

### OAuth2配置步骤

1. **获取OAuth2凭据**
   - Google: https://console.cloud.google.com/
   - GitHub: https://github.com/settings/developers

2. **配置环境变量**
```bash
export GOOGLE_CLIENT_ID=your-google-client-id
export GOOGLE_CLIENT_SECRET=your-google-client-secret
export GITHUB_CLIENT_ID=your-github-client-id
export GITHUB_CLIENT_SECRET=your-github-client-secret
```

3. **重启应用**
```bash
mvn spring-boot:run
```

## ⚠️ 注意事项

- **不使用Lombok** - 所有getter/setter方法需要手动生成
- **数据库安全** - 生产环境请修改默认数据库密码
- **JWT密钥** - 生产环境请使用强密钥
- **OAuth2配置** - 确保回调URL配置正确
- **日志管理** - 日志文件保存在logs目录

## 🔗 相关链接

- [项目主页](../README.md) - 项目整体介绍和快速开始
- [前端文档](../frontend/README.md) - 前端项目详细说明
- [OAuth2配置指南](../OAuth2-解决方案.md) - OAuth2详细配置步骤
- [数据库脚本](./sql/shared_init.sql) - 数据库初始化脚本

## 🤝 贡献指南

### 开发流程
1. Fork 项目
2. 创建功能分支: `git checkout -b feature/your-feature`
3. 提交更改: `git commit -am 'Add some feature'`
4. 推送分支: `git push origin feature/your-feature`
5. 创建 Pull Request

### 代码规范
- 遵循Java编码规范
- 使用Spring Boot最佳实践
- 不使用Lombok，手动生成getter/setter
- 添加适当的注释和文档

### 提交规范
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../LICENSE) 文件了解详情

## 💬 支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 📧 邮箱: <EMAIL>
- 💬 QQ群: 123456789
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-repo/issues)

---

<div align="center">
Made with ❤️ by AIC Core Service Team
</div>
