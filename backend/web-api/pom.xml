<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.jdl.aic.core.service</groupId>
        <artifactId>aic-core-service-backend</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>web-api</artifactId>
    <packaging>jar</packaging>

    <name>web-api</name>
    <description>Web API Module</description>

    <dependencies>
        <!-- Service模块 -->
        <dependency>
            <groupId>com.jdl.aic.core.service</groupId>
            <artifactId>service</artifactId>
        </dependency>

        <!-- DAO模块 -->
        <dependency>
            <groupId>com.jdl.aic.core.service</groupId>
            <artifactId>dao</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-spring</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jd.stream</groupId>
            <artifactId>server-sdk</artifactId>
        </dependency>

        <!-- Common模块 -->
        <dependency>
            <groupId>com.jdl.aic.core.service</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <!-- 排除log4j-slf4j-impl，避免与Spring Boot的log4j-to-slf4j冲突 -->
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Spring Boot Web Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <!-- 排除log4j-slf4j-impl，避免与log4j-to-slf4j冲突 -->
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
        </dependency>

        <!-- MyBatis Spring Boot Starter -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>

        <!-- MySQL驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>

        <!-- Druid连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!-- Spring Security OAuth2 Client -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>

        <!-- JWT -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- AspectJ dependencies -->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        
        <!-- Spring AOP -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jsf</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jd.eclp</groupId>
            <artifactId>core-jsf</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsf</artifactId>
                    <groupId>com.jd</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>profiler</artifactId>
                    <groupId>com.jd.ump</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>../init-config</directory>
                <includes>
                    <include>aicore_test.properties</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.jdl.aic.core.service.Application</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>2.2.1</version>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven</groupId>
                        <artifactId>maven-core</artifactId>
                        <version>2.2.1</version>
                        <exclusions>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-file</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-provider-api</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-http-lightweight</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-http-shared</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-provider-api</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-http</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-webdav-jackrabbit</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-ssh-external</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.wagon</groupId>
                                <artifactId>wagon-ssh</artifactId>
                            </exclusion>
                            <exclusion>
                                <groupId>org.apache.maven.reporting</groupId>
                                <artifactId>maven-reporting-api</artifactId>
                            </exclusion>
                        </exclusions>
                    </dependency>
                </dependencies>
                <configuration>
                    <!-- not append assembly id in release file name -->
                    <finalName>ai-core-service</finalName>
                    <appendAssemblyId>false</appendAssemblyId>
                    <descriptors>
                        <descriptor>src/main/assemble/package.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
