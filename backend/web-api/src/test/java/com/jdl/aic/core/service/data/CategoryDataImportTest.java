package com.jdl.aic.core.service.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdl.aic.core.service.client.dto.category.CategoryDTO;
import com.jdl.aic.core.service.client.service.CategoryService;
import com.jdl.aic.core.service.client.common.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 分类数据导入测试类
 * 解析categorys.json文件并将数据插入category表
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CategoryDataImportTest {

    @Autowired
    private CategoryService categoryService;

    /**
     * 测试从categorys.json文件导入分类数据
     */
    @Test
    public void testImportCategoriesFromJson() {
        try {
            // 读取JSON文件 - 使用多种路径尝试策略
            String jsonContent = readJsonFileWithFallback();
            
            // 解析JSON数组
            JSONArray categoryArray = JSON.parseArray(jsonContent);
            
            System.out.println("开始导入分类数据，共 " + categoryArray.size() + " 条记录");
            
            int successCount = 0;
            int failCount = 0;
            
            // 遍历每个分类条目
            for (int i = 0; i < categoryArray.size(); i++) {
                JSONObject jsonCategory = categoryArray.getJSONObject(i);
                
                try {
                    // 转换为CategoryDTO
                    CategoryDTO categoryDTO = convertJsonToCategoryDTO(jsonCategory);
                    
                    // 检查是否已存在相同名称的分类（可选的重复检查）
                    // 这里可以根据需要添加重复检查逻辑
                    
                    // 创建分类
                    Result<CategoryDTO> result = categoryService.createCategory(categoryDTO);
                    
                    if (result.isSuccess()) {
                        successCount++;
                        System.out.println("成功导入分类: " + categoryDTO.getName() + " (ID: " + jsonCategory.getInteger("id") + ")");
                    } else {
                        failCount++;
                        System.err.println("导入分类失败: " + categoryDTO.getName() + " - " + result.getMessage());
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    System.err.println("处理分类数据时出错 (ID: " + jsonCategory.getInteger("id") + "): " + e.getMessage());
                    e.printStackTrace();
                }
            }
            
            System.out.println("数据导入完成！成功: " + successCount + " 条，失败: " + failCount + " 条");
            
            // 验证导入结果
            Assert.assertTrue("至少应该成功导入一条数据", successCount > 0);
            
        } catch (Exception e) {
            System.err.println("导入过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            Assert.fail("导入过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 将JSON对象转换为CategoryDTO
     */
    private CategoryDTO convertJsonToCategoryDTO(JSONObject jsonObject) {
        CategoryDTO dto = new CategoryDTO();

        dto.setId(jsonObject.getLong("id"));
        // 基本字段映射
        dto.setName(jsonObject.getString("name"));
        dto.setDescription(jsonObject.getString("description"));
        
        // 父分类ID处理
        Object parentIdObj = jsonObject.get("parentId");
        if (parentIdObj != null && !"null".equals(parentIdObj.toString())) {
            dto.setParentId(jsonObject.getLong("parentId"));
        }
        
        // 内容类别
        dto.setContentCategory(jsonObject.getString("contentCategory"));
        
        // 细分类型ID
        Object subTypeIdObj = jsonObject.get("subTypeId");
        if (subTypeIdObj != null && !"null".equals(subTypeIdObj.toString())) {
            dto.setSubTypeId(jsonObject.getLong("subTypeId"));
        }
        
        // 图标URL
        dto.setIconUrl(jsonObject.getString("iconUrl"));
        
        // 排序权重
        dto.setSortOrder(jsonObject.getInteger("sortOrder"));
        
        // 是否启用
        dto.setIsActive(jsonObject.getBoolean("isActive"));
        
        // 处理时间字段
        String createdAtStr = jsonObject.getString("createdAt");
        if (createdAtStr != null) {
            try {
                dto.setCreatedAt(LocalDateTime.parse(createdAtStr, DateTimeFormatter.ISO_DATE_TIME));
            } catch (Exception e) {
                System.out.println("解析创建时间失败: " + createdAtStr + ", 使用当前时间");
                dto.setCreatedAt(LocalDateTime.now());
            }
        } else {
            dto.setCreatedAt(LocalDateTime.now());
        }
        
        String updatedAtStr = jsonObject.getString("updatedAt");
        if (updatedAtStr != null) {
            try {
                dto.setUpdatedAt(LocalDateTime.parse(updatedAtStr, DateTimeFormatter.ISO_DATE_TIME));
            } catch (Exception e) {
                System.out.println("解析更新时间失败: " + updatedAtStr + ", 使用当前时间");
                dto.setUpdatedAt(LocalDateTime.now());
            }
        } else {
            dto.setUpdatedAt(LocalDateTime.now());
        }
        
        // 设置创建者和更新者（可以根据需要调整）
        dto.setCreatedBy("system");
        dto.setUpdatedBy("system");

        return dto;
    }

    /**
     * 使用多种路径尝试策略读取JSON文件
     */
    private String readJsonFileWithFallback() throws IOException {
        // 定义可能的文件路径
        String[] possiblePaths = {
//            "backend/docs/data/categorys.json",
//            "docs/data/categorys.json",
//            "categorys.json",
            "../docs/data/categorys.json",
//            "../../docs/data/categorys.json",
//            "../../../docs/data/categorys.json",
//            "./backend/docs/data/categorys.json",
//            "./docs/data/categorys.json"
        };

        for (String path : possiblePaths) {
            try {
                Path filePath = Paths.get(path);
                if (Files.exists(filePath)) {
                    System.out.println("成功找到JSON文件: " + filePath.toAbsolutePath());
                    return new String(Files.readAllBytes(filePath));
                }
            } catch (Exception e) {
                // 继续尝试下一个路径
                System.out.println("尝试路径失败: " + path + " - " + e.getMessage());
            }
        }

        // 如果所有路径都失败，打印调试信息
        System.err.println("无法找到JSON文件，当前工作目录: " + System.getProperty("user.dir"));
        System.err.println("尝试过的路径:");
        for (String path : possiblePaths) {
            System.err.println("  - " + path);
        }

        throw new IOException("无法找到categorys.json文件，请检查文件路径");
    }


}
