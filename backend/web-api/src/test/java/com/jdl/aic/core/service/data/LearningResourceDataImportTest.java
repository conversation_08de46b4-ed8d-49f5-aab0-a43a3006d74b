package com.jdl.aic.core.service.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdl.aic.core.service.client.dto.learning.LearningResourceDTO;
import com.jdl.aic.core.service.client.service.LearningResourceService;
import com.jdl.aic.core.service.client.common.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 学习资源数据导入测试类
 * 解析learning_resources.json文件并将数据插入learning_resource表
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class LearningResourceDataImportTest {

    @Autowired
    private LearningResourceService learningResourceService;

    /**
     * 测试从learning_resources.json文件导入学习资源数据
     */
    @Test
    public void testImportLearningResourcesFromJson() {
        try {
            // 读取JSON文件 - 使用多种路径尝试策略
            String jsonContent = readJsonFileWithFallback();
            
            // 解析JSON数组
            JSONArray resourcesArray = JSON.parseArray(jsonContent);
            
            System.out.println("开始导入学习资源数据，共 " + resourcesArray.size() + " 条记录");
            
            int successCount = 0;
            int failCount = 0;
            
            // 遍历每个学习资源条目
            for (int i = 0; i < resourcesArray.size(); i++) {
                JSONObject jsonResource = resourcesArray.getJSONObject(i);
                
                try {
                    // 转换为LearningResourceDTO
                    LearningResourceDTO resourceDTO = convertJsonToLearningResourceDTO(jsonResource);
                    
                    // 检查是否已存在相同标题的资源（可选的重复检查）
                    // 这里可以根据需要添加重复检查逻辑
                    
                    // 创建学习资源
                    Result<LearningResourceDTO> result = learningResourceService.createLearningResource(resourceDTO);
                    
                    if (result.isSuccess()) {
                        successCount++;
                        System.out.println("成功导入学习资源: " + resourceDTO.getTitle());
                    } else {
                        failCount++;
                        System.err.println("导入学习资源失败: " + resourceDTO.getTitle() + " - " + result.getMessage());
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    System.err.println("处理学习资源数据时出错 (标题: " + jsonResource.getString("title") + "): " + e.getMessage());
                    e.printStackTrace();
                }
            }
            
            System.out.println("数据导入完成！成功: " + successCount + " 条，失败: " + failCount + " 条");
            
            // 验证导入结果
            Assert.assertTrue("至少应该成功导入一条数据", successCount > 0);
            
        } catch (Exception e) {
            System.err.println("导入过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            Assert.fail("导入过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 将JSON对象转换为LearningResourceDTO
     */
    private LearningResourceDTO convertJsonToLearningResourceDTO(JSONObject jsonObject) {
        LearningResourceDTO dto = new LearningResourceDTO();

        // 基本字段映射
        dto.setTitle(jsonObject.getString("title"));
        dto.setDescription(jsonObject.getString("description"));
        dto.setContent(jsonObject.getString("content"));

        // 资源类型和来源信息
        dto.setResourceType(jsonObject.getString("resourceType"));
        dto.setSourceUrl(jsonObject.getString("sourceUrl"));

        // 难度级别 - 注意字段名称映射
        String difficultyLevel = jsonObject.getString("difficultyLevel");
        if (StringUtils.hasText(difficultyLevel)) {
            // 将 difficultyLevel 映射到 difficulty 字段
            dto.setDifficulty(difficultyLevel.toLowerCase());
        }

        // 语言
        dto.setLanguage(jsonObject.getString("language"));

        // 时长处理 - 将小时转换为分钟
        BigDecimal estimatedHours = jsonObject.getBigDecimal("estimatedDurationHours");
        if (estimatedHours != null) {
            // 将小时转换为分钟
            dto.setDuration(estimatedHours.multiply(new BigDecimal(60)).intValue());
        }

        // 评分信息 - 注意类型转换
        BigDecimal rating = jsonObject.getBigDecimal("rating");
        if (rating != null) {
            dto.setRating(rating.doubleValue());
        }
        Integer ratingCount = jsonObject.getInteger("ratingCount");
        if (ratingCount != null) {
            // LearningResourceDTO 使用 reviewCount 字段
            dto.setReviewCount(ratingCount);
        }

        // 完成率
        BigDecimal completionRate = jsonObject.getBigDecimal("completionRate");
        if (completionRate != null) {
            dto.setCompletionRate(completionRate);
        }

        // 标签处理
        JSONArray tagsArray = jsonObject.getJSONArray("tags");
        if (tagsArray != null && !tagsArray.isEmpty()) {
            List<String> tagsList = new ArrayList<>();
            for (int i = 0; i < tagsArray.size(); i++) {
                tagsList.add(tagsArray.getString(i));
            }
            dto.setTags(tagsList);
        }

        // 前置要求处理 - 将字符串转换为列表
        String prerequisites = jsonObject.getString("prerequisites");
        if (StringUtils.hasText(prerequisites)) {
            List<String> prerequisitesList = new ArrayList<>();
            prerequisitesList.add(prerequisites);
            dto.setPrerequisites(prerequisitesList);
        }

        // 学习目标处理 - 将字符串转换为列表
        String learningGoals = jsonObject.getString("learningGoals");
        if (StringUtils.hasText(learningGoals)) {
            List<String> learningObjectivesList = new ArrayList<>();
            learningObjectivesList.add(learningGoals);
            dto.setLearningObjectives(learningObjectivesList);
        }

        // 内容类型
        dto.setContentType(jsonObject.getString("contentType"));

        // 缩略图URL处理
        JSONObject metadata = jsonObject.getJSONObject("metadata");
        if (metadata != null) {
            String thumbnailUrl = metadata.getString("thumbnailUrl");
            if (StringUtils.hasText(thumbnailUrl)) {
                dto.setThumbnailUrl(thumbnailUrl);
            }
            // 设置元数据
            @SuppressWarnings("unchecked")
            Map<String, Object> metadataMap = metadata.toJavaObject(Map.class);
            dto.setMetadataJson(metadataMap);
        }

        // JSON配置字段
        JSONObject contentConfig = jsonObject.getJSONObject("contentConfig");
        if (contentConfig != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> contentConfigMap = contentConfig.toJavaObject(Map.class);
            dto.setContentConfig(contentConfigMap);
        }

        JSONObject embedConfig = jsonObject.getJSONObject("embedConfig");
        if (embedConfig != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> embedConfigMap = embedConfig.toJavaObject(Map.class);
            dto.setEmbedConfig(embedConfigMap);
        }

        JSONObject accessConfig = jsonObject.getJSONObject("accessConfig");
        if (accessConfig != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> accessConfigMap = accessConfig.toJavaObject(Map.class);
            dto.setAccessConfig(accessConfigMap);
        }

        JSONObject mediaMetadata = jsonObject.getJSONObject("mediaMetadata");
        if (mediaMetadata != null) {
            @SuppressWarnings("unchecked")
            Map<String, Object> mediaMetadataMap = mediaMetadata.toJavaObject(Map.class);
            dto.setMediaMetadata(mediaMetadataMap);
        }

        // 设置激活状态
        String status = jsonObject.getString("status");
        dto.setIsActive(!"INACTIVE".equals(status));

        // 设置创建者和更新者信息
        dto.setCreatedBy(jsonObject.getString("createdBy"));
        dto.setUpdatedBy(jsonObject.getString("updatedBy"));

        // 设置默认值
        if (!StringUtils.hasText(dto.getCreatedBy())) {
            dto.setCreatedBy("system_import");
        }
        if (!StringUtils.hasText(dto.getUpdatedBy())) {
            dto.setUpdatedBy("system_import");
        }

        return dto;
    }

    /**
     * 使用多种路径尝试策略读取JSON文件
     */
    private String readJsonFileWithFallback() throws IOException {
        // 定义可能的文件路径
        String[] possiblePaths = {
            "../docs/data/learning_resources.json"
        };

        for (String path : possiblePaths) {
            try {
                Path filePath = Paths.get(path);
                if (Files.exists(filePath)) {
                    System.out.println("成功找到JSON文件: " + filePath.toAbsolutePath());
                    return new String(Files.readAllBytes(filePath));
                }
            } catch (Exception e) {
                // 继续尝试下一个路径
                System.out.println("尝试路径失败: " + path + " - " + e.getMessage());
            }
        }

        // 如果所有路径都失败，打印调试信息
        System.err.println("无法找到JSON文件，当前工作目录: " + System.getProperty("user.dir"));
        System.err.println("尝试过的路径:");
        for (String path : possiblePaths) {
            System.err.println("  - " + path);
        }

        throw new IOException("无法找到learning_resources.json文件，请检查文件路径");
    }

}
