package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.solution.SolutionDTO;
import com.jdl.aic.core.service.client.dto.solution.SolutionStepDTO;
import com.jdl.aic.core.service.client.dto.request.solution.GetSolutionListRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetPopularSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetRecommendedSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.GetUserSolutionsRequest;
import com.jdl.aic.core.service.client.dto.request.solution.SearchSolutionsRequest;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.client.service.SolutionService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * SolutionService 集成测试
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class SolutionServiceTest {

    @Resource
    private SolutionService solutionService;

    @Test
    public void testCreateSolution() {
        // 创建测试解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("测试解决方案");
        newSolution.setDescription("这是一个测试解决方案的描述");
        newSolution.setContent("这是解决方案的详细内容，包含了具体的解决步骤和方法。");
        newSolution.setAuthorId("user001");
        newSolution.setAuthorName("测试用户");
        newSolution.setStatus(0); // 草稿状态

        Result<SolutionDTO> result = solutionService.createSolution(newSolution);
        System.out.println("创建解决方案结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("测试解决方案", result.getData().getTitle());
    }

    @Test
    public void testGetSolutionById() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("查询测试解决方案");
        newSolution.setDescription("用于查询测试的解决方案");
        newSolution.setContent("查询测试内容");
        newSolution.setAuthorId("user002");
        newSolution.setAuthorName("测试用户");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 查询解决方案
        Result<SolutionDTO> result = solutionService.getSolutionById(solutionId);
        System.out.println("查询解决方案结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("查询测试解决方案", result.getData().getTitle());
    }

    @Test
    public void testGetSolutionList() {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(10);

        GetSolutionListRequest request = new GetSolutionListRequest(pageRequest, null, null, null, null);
        System.out.println("查询解决方案列表请求：" + JSON.toJSONString(request));
        Result<PageResult<SolutionDTO>> result = solutionService.getSolutionList(request);
        System.out.println("解决方案列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateSolution() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("更新测试解决方案");
        newSolution.setDescription("用于更新测试的解决方案");
        newSolution.setContent("更新测试内容");
        newSolution.setAuthorId("user003");
        newSolution.setAuthorName("测试用户");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 更新解决方案
        SolutionDTO updateSolution = new SolutionDTO();
        updateSolution.setTitle("更新后的解决方案标题");
        updateSolution.setDescription("更新后的描述");
        updateSolution.setContent("更新后的内容");

        Result<SolutionDTO> result = solutionService.updateSolution(solutionId, updateSolution);
        System.out.println("更新解决方案结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("更新后的解决方案标题", result.getData().getTitle());
    }

    @Test
    public void testUpdateSolutionStatus() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("状态测试解决方案");
        newSolution.setDescription("用于状态测试的解决方案");
        newSolution.setContent("状态测试内容");
        newSolution.setAuthorId("user004");
        newSolution.setAuthorName("测试用户");
        newSolution.setStatus(0); // 草稿状态

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 更新状态为已发布
        Result<Void> result = solutionService.updateSolutionStatus(solutionId, 2);
        System.out.println("更新解决方案状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更改
        Result<SolutionDTO> getResult = solutionService.getSolutionById(solutionId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(2), getResult.getData().getStatus());
    }

    @Test
    public void testBatchUpdateSolutionStatus() {
        // 先创建两个解决方案
        SolutionDTO solution1 = new SolutionDTO();
        solution1.setTitle("批量状态测试解决方案1");
        solution1.setDescription("批量测试1");
        solution1.setContent("批量测试内容1");
        solution1.setAuthorId("user005");
        solution1.setStatus(0);

        SolutionDTO solution2 = new SolutionDTO();
        solution2.setTitle("批量状态测试解决方案2");
        solution2.setDescription("批量测试2");
        solution2.setContent("批量测试内容2");
        solution2.setAuthorId("user005");
        solution2.setStatus(0);

        Result<SolutionDTO> createResult1 = solutionService.createSolution(solution1);
        Result<SolutionDTO> createResult2 = solutionService.createSolution(solution2);
        Assert.assertTrue(createResult1.isSuccess());
        Assert.assertTrue(createResult2.isSuccess());

        List<Long> ids = Arrays.asList(createResult1.getData().getId(), createResult2.getData().getId());

        // 批量更新状态
        Result<Void> result = solutionService.batchUpdateSolutionStatus(ids, 2);
        System.out.println("批量更新解决方案状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testIncrementViewCount() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("浏览测试解决方案");
        newSolution.setDescription("用于浏览测试的解决方案");
        newSolution.setContent("浏览测试内容");
        newSolution.setAuthorId("user006");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 增加浏览次数
        Result<Void> result = solutionService.incrementViewCount(solutionId);
        System.out.println("增加浏览次数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testIncrementLikeCount() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("点赞测试解决方案");
        newSolution.setDescription("用于点赞测试的解决方案");
        newSolution.setContent("点赞测试内容");
        newSolution.setAuthorId("user007");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 增加点赞次数
        Result<Void> result = solutionService.incrementLikeCount(solutionId, 2L);
        System.out.println("增加点赞次数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testGetPopularSolutions() {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(5);

        GetPopularSolutionsRequest request = new GetPopularSolutionsRequest(pageRequest, null, 30);
        Result<PageResult<SolutionDTO>> result = solutionService.getPopularSolutions(request);
        System.out.println("热门解决方案结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetUserSolutions() {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(10);

        GetUserSolutionsRequest request = new GetUserSolutionsRequest("user002", pageRequest, null);
        Result<PageResult<SolutionDTO>> result = solutionService.getUserSolutions(request);
        System.out.println("用户解决方案结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testSearchSolutions() {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(10);

        SearchSolutionsRequest request = new SearchSolutionsRequest("测试", pageRequest, null, null);
        Result<PageResult<SolutionDTO>> result = solutionService.searchSolutions(request);
        System.out.println("搜索解决方案结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testDeleteSolution() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("删除测试解决方案");
        newSolution.setDescription("用于删除测试的解决方案");
        newSolution.setContent("删除测试内容");
        newSolution.setAuthorId("user002");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 删除解决方案
        Result<Void> result = solutionService.deleteSolution(solutionId);
        System.out.println("删除解决方案结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    // ==================== 解决方案步骤测试 ====================

    @Test
    public void testAddSolutionStep() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("步骤测试解决方案");
        newSolution.setDescription("用于步骤测试的解决方案");
        newSolution.setContent("步骤测试内容");
        newSolution.setAuthorId("user008");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 添加解决方案步骤
        SolutionStepDTO step = new SolutionStepDTO();
        step.setStepTitle("测试步骤1");
        step.setStepDescription("这是第一个测试步骤");
        step.setStepOrder(1);
        step.setRelatedKnowledgeId(1L); // 假设存在知识ID为1的记录

        Result<SolutionStepDTO> result = solutionService.addSolutionStep(solutionId, step);
        System.out.println("添加解决方案步骤结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        // 注意：这个测试可能会失败，因为可能不存在知识ID为1的记录
        // 在实际测试中，需要先创建知识记录或使用Mock
    }

    @Test
    public void testGetSolutionSteps() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("步骤查询测试解决方案");
        newSolution.setDescription("用于步骤查询测试的解决方案");
        newSolution.setContent("步骤查询测试内容");
        newSolution.setAuthorId("user009");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 查询解决方案步骤
        Result<List<SolutionStepDTO>> result = solutionService.getSolutionSteps(solutionId);
        System.out.println("查询解决方案步骤结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testReorderSolutionSteps() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("步骤排序测试解决方案");
        newSolution.setDescription("用于步骤排序测试的解决方案");
        newSolution.setContent("步骤排序测试内容");
        newSolution.setAuthorId("user010");
        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 假设有步骤ID列表（在实际测试中需要先创建步骤）
        List<Long> stepIds = Arrays.asList(1L, 2L, 3L);

        // 调整步骤顺序
        Result<Void> result = solutionService.reorderSolutionSteps(solutionId, stepIds);
        System.out.println("调整解决方案步骤顺序结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        // 注意：这个测试可能会失败，因为可能不存在对应的步骤记录
    }

    // ==================== 统计和分析测试 ====================

    @Test
    public void testGetRecommendedSolutions() {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(5);

        GetRecommendedSolutionsRequest request = new GetRecommendedSolutionsRequest(1L, pageRequest);
        Result<PageResult<SolutionDTO>> result = solutionService.getRecommendedSolutions(request);
        System.out.println("推荐解决方案结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetSolutionCategoryStats() {
        Result<List<Object>> result = solutionService.getSolutionCategoryStats();
        System.out.println("解决方案分类统计结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testIncrementUseCount() {
        // 先创建一个解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("使用统计测试解决方案");
        newSolution.setDescription("用于使用统计测试的解决方案");
        newSolution.setContent("使用统计测试内容");
        newSolution.setAuthorId("user011");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 记录使用次数
        Result<Void> result = solutionService.incrementUseCount(solutionId, 2L);
        System.out.println("记录使用次数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testDecrementLikeCount() {
        // 先创建一个解决方案并增加点赞
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("取消点赞测试解决方案");
        newSolution.setDescription("用于取消点赞测试的解决方案");
        newSolution.setContent("取消点赞测试内容");
        newSolution.setAuthorId("user002");

        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        Assert.assertTrue(createResult.isSuccess());
        Long solutionId = createResult.getData().getId();

        // 先增加点赞
        solutionService.incrementLikeCount(solutionId, 2L);

        // 取消点赞
        Result<Void> result = solutionService.decrementLikeCount(solutionId, 2L);
        System.out.println("取消点赞结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    // ==================== 参数验证测试 ====================

    @Test
    public void testCreateSolutionWithInvalidParameters() {
        // 测试空参数
        Result<SolutionDTO> result1 = solutionService.createSolution(null);
        Assert.assertFalse(result1.isSuccess());
        Assert.assertEquals("INVALID_PARAMETER", result1.getCode());

        // 测试缺少标题
        SolutionDTO solution = new SolutionDTO();
        solution.setAuthorId("user002");
        Result<SolutionDTO> result2 = solutionService.createSolution(solution);
        Assert.assertFalse(result2.isSuccess());
        Assert.assertEquals("INVALID_PARAMETER", result2.getCode());

        // 测试缺少作者ID
        solution.setTitle("测试标题");
        solution.setAuthorId(null);
        Result<SolutionDTO> result3 = solutionService.createSolution(solution);
        Assert.assertFalse(result3.isSuccess());
        Assert.assertEquals("INVALID_PARAMETER", result3.getCode());
    }

    // ==================== 复杂条件查询测试 ====================

    @Test
    public void testSelectByComplexCondition() {
        // 先创建几个测试解决方案
        SolutionDTO solution1 = new SolutionDTO();
        solution1.setTitle("复杂查询测试解决方案1");
        solution1.setDescription("用于复杂查询测试的解决方案1");
        solution1.setContent("复杂查询测试内容1");
        solution1.setAuthorId("user_complex_1");
        solution1.setAuthorName("复杂查询测试用户1");
        solution1.setStatus(2); // 已发布

        Result<SolutionDTO> createResult1 = solutionService.createSolution(solution1);
        Assert.assertTrue(createResult1.isSuccess());

        SolutionDTO solution2 = new SolutionDTO();
        solution2.setTitle("复杂查询测试解决方案2");
        solution2.setDescription("用于复杂查询测试的解决方案2");
        solution2.setContent("复杂查询测试内容2");
        solution2.setAuthorId("user_complex_2");
        solution2.setAuthorName("复杂查询测试用户2");
        solution2.setStatus(1); // 待审核

        Result<SolutionDTO> createResult2 = solutionService.createSolution(solution2);
        Assert.assertTrue(createResult2.isSuccess());

        // 测试复杂条件查询 - 通过直接调用mapper方法
        // 注意：这里需要注入SolutionMapper来直接测试
        System.out.println("复杂条件查询测试完成 - 创建了测试数据");
        System.out.println("解决方案1 ID: " + createResult1.getData().getId());
        System.out.println("解决方案2 ID: " + createResult2.getData().getId());

        // 实际的复杂查询测试需要在mapper层进行，这里只是验证数据创建成功
        Assert.assertNotNull(createResult1.getData().getId());
        Assert.assertNotNull(createResult2.getData().getId());
    }



    @Test
    public void testCreateSolutionWithCategoriesField() {
        // 测试使用 categories 字段（List类型）创建解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("分类列表测试解决方案");
        newSolution.setDescription("这是一个测试分类列表字段的解决方案");
        newSolution.setContent("测试分类列表字段是否能正确保存到数据库");
        newSolution.setAuthorId("user_categories_test");
        newSolution.setAuthorName("分类列表测试用户");
        newSolution.setStatus(0); // 草稿状态

        // 设置 categories 字段
        ContentCategoryRelationDTO categoryRelation = new ContentCategoryRelationDTO();
        categoryRelation.setCategoryId(2L);
        categoryRelation.setContentType(ContentType.SOLUTION);
        newSolution.setCategories(Arrays.asList(categoryRelation));

        Result<SolutionDTO> result = solutionService.createSolution(newSolution);
        System.out.println("使用categories字段创建解决方案结果：" + JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("分类列表测试解决方案", result.getData().getTitle());

        // 验证分类关系是否正确保存
        Result<SolutionDTO> getResult = solutionService.getSolutionById(result.getData().getId());
        Assert.assertTrue(getResult.isSuccess());
        SolutionDTO savedSolution = getResult.getData();

        // 检查是否有分类信息
        if (savedSolution.getCategories() != null && !savedSolution.getCategories().isEmpty()) {
            System.out.println("分类信息已正确保存：" + JSON.toJSONString(savedSolution.getCategories()));
            Assert.assertEquals(Long.valueOf(2), savedSolution.getCategories().get(0).getCategoryId());
        } else {
            System.out.println("警告：分类信息未保存或查询失败");
        }
    }

    @Test
    public void testDeleteSolutionWithCategories() {
        // 先创建一个带分类的解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("待删除的分类测试解决方案");
        newSolution.setDescription("这是一个用于测试删除功能的解决方案");
        newSolution.setContent("测试删除时是否正确清理分类关联关系");
        newSolution.setAuthorId("user_delete_test");
        newSolution.setAuthorName("删除测试用户");
        newSolution.setStatus(0); // 草稿状态

        // 设置 categories 字段
        ContentCategoryRelationDTO categoryRelation = new ContentCategoryRelationDTO();
        categoryRelation.setCategoryId(3L);
        categoryRelation.setContentType(ContentType.SOLUTION);
        newSolution.setCategories(Arrays.asList(categoryRelation));

        // 创建解决方案
        Result<SolutionDTO> createResult = solutionService.createSolution(newSolution);
        System.out.println("创建带分类的解决方案结果：" + JSON.toJSONString(createResult));

        Assert.assertNotNull(createResult);
        Assert.assertTrue(createResult.isSuccess());
        Assert.assertNotNull(createResult.getData());
        Long solutionId = createResult.getData().getId();

        // 验证分类关系已创建
        Result<SolutionDTO> getResult = solutionService.getSolutionById(solutionId);
        Assert.assertTrue(getResult.isSuccess());
        SolutionDTO savedSolution = getResult.getData();

        if (savedSolution.getCategories() != null && !savedSolution.getCategories().isEmpty()) {
            System.out.println("分类关系已正确创建：" + JSON.toJSONString(savedSolution.getCategories()));
        }

        // 删除解决方案
        Result<Void> deleteResult = solutionService.deleteSolution(solutionId);
        System.out.println("删除解决方案结果：" + JSON.toJSONString(deleteResult));

        Assert.assertNotNull(deleteResult);
        Assert.assertTrue(deleteResult.isSuccess());

        // 验证解决方案已被删除
        Result<SolutionDTO> getAfterDeleteResult = solutionService.getSolutionById(solutionId);
        Assert.assertFalse(getAfterDeleteResult.isSuccess());
        Assert.assertEquals("SOLUTION_NOT_FOUND", getAfterDeleteResult.getCode());

        System.out.println("解决方案删除测试完成，分类关联关系应该已被正确清理");
    }

    @Test
    public void testGetSolutionByIdWithInvalidParameters() {
        // 测试空ID
        Result<SolutionDTO> result = solutionService.getSolutionById(null);
        Assert.assertFalse(result.isSuccess());
        Assert.assertEquals("INVALID_PARAMETER", result.getCode());

        // 测试不存在的ID
        Result<SolutionDTO> result2 = solutionService.getSolutionById(99999L);
        Assert.assertFalse(result2.isSuccess());
        Assert.assertEquals("SOLUTION_NOT_FOUND", result2.getCode());
    }
}
