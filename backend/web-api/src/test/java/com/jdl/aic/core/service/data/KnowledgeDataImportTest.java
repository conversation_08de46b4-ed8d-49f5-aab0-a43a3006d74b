package com.jdl.aic.core.service.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.client.common.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 知识数据导入测试类
 * 解析all-knowledge.json文件并将数据插入knowledge表
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class KnowledgeDataImportTest {

    @Autowired
    private KnowledgeService knowledgeService;

    /**
     * 测试从all-knowledge.json文件导入知识数据
     */
    @Test
    public void testImportKnowledgeFromJson() {
        try {
            // 读取JSON文件 - 使用多种路径尝试策略
            String jsonContent = readJsonFileWithFallback();
            
            // 解析JSON数组
            JSONArray knowledgeArray = JSON.parseArray(jsonContent);
            
            System.out.println("开始导入知识数据，共 " + knowledgeArray.size() + " 条记录");
            
            int successCount = 0;
            int failCount = 0;
            
            // 遍历每个知识条目
            for (int i = 0; i < knowledgeArray.size(); i++) {
                JSONObject jsonKnowledge = knowledgeArray.getJSONObject(i);
                
                try {
                    // 转换为KnowledgeDTO
                    KnowledgeDTO knowledgeDTO = convertJsonToKnowledgeDTO(jsonKnowledge);
                    
                    // 检查是否已存在相同标题的知识（可选的重复检查）
                    // 这里可以根据需要添加重复检查逻辑
                    
                    // 创建知识
                    Result<KnowledgeDTO> result = knowledgeService.createKnowledge(knowledgeDTO);
                    
                    if (result.isSuccess()) {
                        successCount++;
                        System.out.println("成功导入知识: " + knowledgeDTO.getTitle() + " (ID: " + jsonKnowledge.getInteger("id") + ")");
                    } else {
                        failCount++;
                        System.err.println("导入知识失败: " + knowledgeDTO.getTitle() + " - " + result.getMessage());
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    System.err.println("处理知识数据时出错 (ID: " + jsonKnowledge.getInteger("id") + "): " + e.getMessage());
                    e.printStackTrace();
                }
            }
            
            System.out.println("数据导入完成！成功: " + successCount + " 条，失败: " + failCount + " 条");
            
            // 验证导入结果
            Assert.assertTrue("至少应该成功导入一条数据", successCount > 0);
            
        } catch (Exception e) {
            System.err.println("导入过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            Assert.fail("导入过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 将JSON对象转换为KnowledgeDTO
     */
    private KnowledgeDTO convertJsonToKnowledgeDTO(JSONObject jsonObject) {
        KnowledgeDTO dto = new KnowledgeDTO();

        dto.setId(jsonObject.getLong("id"));
        // 基本字段映射
        dto.setTitle(jsonObject.getString("title"));
        dto.setDescription(jsonObject.getString("description"));
        dto.setContent(jsonObject.getString("content"));
        
        // 知识类型相关字段
        dto.setKnowledgeTypeId(jsonObject.getLong("knowledgeTypeId"));
        dto.setKnowledgeTypeCode(jsonObject.getString("knowledgeTypeCode"));
        dto.setKnowledgeTypeName(jsonObject.getString("knowledgeTypeName"));
        
        // 作者信息
        dto.setAuthorId(jsonObject.getString("authorId"));
        dto.setAuthorName(jsonObject.getString("authorName"));
        
        // 状态和可见性
        dto.setStatus(jsonObject.getInteger("status"));
        dto.setVisibility(jsonObject.getInteger("visibility"));
        
        // 版本信息
        dto.setVersion(jsonObject.getString("version"));
        
        // 统计数据
        dto.setReadCount(jsonObject.getInteger("readCount"));
        dto.setLikeCount(jsonObject.getInteger("likeCount"));
        dto.setCommentCount(jsonObject.getInteger("commentCount"));
        dto.setForkCount(jsonObject.getInteger("forkCount"));
        dto.setFavoriteCount(jsonObject.getInteger("favoriteCount"));
        dto.setShareCount(jsonObject.getInteger("shareCount"));

        // 社交热度分数
        if (jsonObject.getBigDecimal("socialScore") != null) {
            dto.setSocialScore(jsonObject.getBigDecimal("socialScore"));
        }

        // 最后社交活动时间
        if (jsonObject.getString("lastSocialActivityAt") != null) {
            // 这里可能需要根据实际的日期格式进行解析
            // dto.setLastSocialActivityAt(LocalDateTime.parse(jsonObject.getString("lastSocialActivityAt")));
        }

        // 封面图片
        dto.setCoverImageUrl(jsonObject.getString("coverImageUrl"));
        
        // 处理metadataJson
        Object metadataObj = jsonObject.get("metadataJson");
        if (metadataObj != null && metadataObj instanceof JSONObject) {
            dto.setMetadataJson(((JSONObject) metadataObj).getInnerMap());
        }
        
        // 处理tags - 转换为AI推荐标签
        Object tagsObj = jsonObject.get("tags");
        if (tagsObj != null) {
            List<String> tags = new ArrayList<>();
            if (tagsObj instanceof JSONArray) {
                JSONArray tagsArray = (JSONArray) tagsObj;
                for (int i = 0; i < tagsArray.size(); i++) {
                    tags.add(tagsArray.getString(i));
                }
            }
            dto.setAiTags(tags);
        }

        // 处理categories - 转换为ContentCategoryRelationDTO列表
        Object categoriesObj = jsonObject.get("categories");
        if (categoriesObj != null) {
            List<ContentCategoryRelationDTO> categories = new ArrayList<>();
            if (categoriesObj instanceof JSONArray) {
                JSONArray categoriesArray = (JSONArray) categoriesObj;
                for (int i = 0; i < categoriesArray.size(); i++) {
                    Object categoryItem = categoriesArray.get(i);
                    Long categoryId = null;

                    // 处理不同的数据类型
                    if (categoryItem instanceof Integer) {
                        categoryId = ((Integer) categoryItem).longValue();
                    } else if (categoryItem instanceof Long) {
                        categoryId = (Long) categoryItem;
                    } else if (categoryItem instanceof String) {
                        try {
                            categoryId = Long.parseLong((String) categoryItem);
                        } catch (NumberFormatException e) {
                            System.out.println("无法解析分类ID: " + categoryItem);
                            continue;
                        }
                    }

                    if (categoryId != null) {
                        ContentCategoryRelationDTO categoryRelation = new ContentCategoryRelationDTO();
                        categoryRelation.setContentType(ContentType.KNOWLEDGE);
                        categoryRelation.setCategoryId(categoryId);
                        // contentId 将在知识创建后设置
                        categories.add(categoryRelation);
                    }
                }
            }
            dto.setCategories(categories);
        }
        
        // 处理时间字段
        String createdAtStr = jsonObject.getString("createdAt");
        if (createdAtStr != null) {
            try {
                dto.setCreatedAt(LocalDateTime.parse(createdAtStr, DateTimeFormatter.ISO_DATE_TIME));
            } catch (Exception e) {
                System.out.println("解析创建时间失败: " + createdAtStr + ", 使用当前时间");
                dto.setCreatedAt(LocalDateTime.now());
            }
        } else {
            dto.setCreatedAt(LocalDateTime.now());
        }
        
        String updatedAtStr = jsonObject.getString("updatedAt");
        if (updatedAtStr != null) {
            try {
                dto.setUpdatedAt(LocalDateTime.parse(updatedAtStr, DateTimeFormatter.ISO_DATE_TIME));
            } catch (Exception e) {
                System.out.println("解析更新时间失败: " + updatedAtStr + ", 使用当前时间");
                dto.setUpdatedAt(LocalDateTime.now());
            }
        } else {
            dto.setUpdatedAt(LocalDateTime.now());
        }
        
        // 设置创建者和更新者信息
        dto.setCreatedBy(jsonObject.getString("createdBy"));
        dto.setUpdatedBy(jsonObject.getString("updatedBy"));


        return dto;
    }

    /**
     * 使用多种路径尝试策略读取JSON文件
     */
    private String readJsonFileWithFallback() throws IOException {
        // 定义可能的文件路径
        String[] possiblePaths = {
//            "backend/docs/data/all-knowledge.json",
//            "docs/data/all-knowledge.json",
//            "all-knowledge.json",
            "../docs/data/knowledges.json"
//            "../../docs/data/all-knowledge.json",
//            "../../../docs/data/all-knowledge.json",
//            "./backend/docs/data/all-knowledge.json",
//            "./docs/data/all-knowledge.json"
        };

        for (String path : possiblePaths) {
            try {
                Path filePath = Paths.get(path);
                if (Files.exists(filePath)) {
                    System.out.println("成功找到JSON文件: " + filePath.toAbsolutePath());
                    return new String(Files.readAllBytes(filePath));
                }
            } catch (Exception e) {
                // 继续尝试下一个路径
                System.out.println("尝试路径失败: " + path + " - " + e.getMessage());
            }
        }

        // 如果所有路径都失败，打印调试信息
        System.err.println("无法找到JSON文件，当前工作目录: " + System.getProperty("user.dir"));
        System.err.println("尝试过的路径:");
        for (String path : possiblePaths) {
            System.err.println("  - " + path);
        }

        throw new IOException("无法找到all-knowledge.json文件，请检查文件路径");
    }

}
