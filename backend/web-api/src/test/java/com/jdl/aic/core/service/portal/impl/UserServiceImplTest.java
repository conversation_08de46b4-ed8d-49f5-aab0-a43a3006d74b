package com.jdl.aic.core.service.portal.impl;

import com.alibaba.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.user.UserDTO;
import com.jdl.aic.core.service.client.dto.request.user.GetUserListRequest;
import com.jdl.aic.core.service.portal.client.UserDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Random;

/**
 * 用户服务测试类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
public class UserServiceImplTest {

    @Resource
    private UserDataService userService;

    @Test
    public void testCreateUser() {
        log.info("开始测试创建用户");
        
        // 创建测试用户
        UserDTO newUser = new UserDTO();
        newUser.setSsoId("test-sso-" + new Random().nextInt(10000));
        newUser.setUsername("testuser" + new Random().nextInt(1000));
        newUser.setDisplayName("测试用户" + new Random().nextInt(1000));
        newUser.setEmail("test" + new Random().nextInt(1000) + "@example.com");
        newUser.setAvatarUrl("http://example.com/avatar.jpg");
        newUser.setDepartment("IT");
        newUser.setTitle("开发工程师");
        newUser.setBio("这是一个测试用户");
        newUser.setTags(Arrays.asList("Java", "Spring", "测试"));
        newUser.setIsActive(true);

        Result<UserDTO> result = userService.createUser(newUser);
        log.info("创建用户结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertNotNull(result.getData().getId());
        assertEquals(newUser.getUsername(), result.getData().getUsername());
        assertEquals(newUser.getDisplayName(), result.getData().getDisplayName());
        assertEquals(newUser.getEmail(), result.getData().getEmail());
        assertEquals(newUser.getTags(), result.getData().getTags());
    }

    @Test
    public void testGetUserById() {
        log.info("开始测试根据ID获取用户");
        
        // 先创建一个用户
        UserDTO newUser = new UserDTO();
        newUser.setSsoId("test-sso-query-" + new Random().nextInt(10000));
        newUser.setUsername("queryuser" + new Random().nextInt(1000));
        newUser.setDisplayName("查询测试用户");
        newUser.setEmail("query" + new Random().nextInt(1000) + "@example.com");
        newUser.setDepartment("IT");
        newUser.setIsActive(true);
        newUser.setTags(Arrays.asList("Java", "Spring", "测试"));


        Result<UserDTO> createResult = userService.createUser(newUser);
        assertTrue(createResult.isSuccess());
        Long userId = createResult.getData().getId();

        // 根据ID查询用户
        Result<UserDTO> result = userService.getUserById(userId);
        log.info("根据ID查询用户结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(userId, result.getData().getId());
        assertEquals(newUser.getUsername(), result.getData().getUsername());
    }

    @Test
    public void testGetUserBySsoId() {
        log.info("开始测试根据SSO ID获取用户");
        
        // 先创建一个用户
        String ssoId = "test-sso-query-sso-" + new Random().nextInt(10000);
        UserDTO newUser = new UserDTO();
        newUser.setSsoId(ssoId);
        newUser.setUsername("ssoqueryuser" + new Random().nextInt(1000));
        newUser.setDisplayName("SSO查询测试用户");
        newUser.setEmail("ssoquery" + new Random().nextInt(1000) + "@example.com");
        newUser.setDepartment("IT");
        newUser.setIsActive(true);

        Result<UserDTO> createResult = userService.createUser(newUser);
        assertTrue(createResult.isSuccess());

        // 根据SSO ID查询用户
        Result<UserDTO> result = userService.getUserBySsoId(ssoId);
        log.info("根据SSO ID查询用户结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(ssoId, result.getData().getSsoId());
        assertEquals(newUser.getUsername(), result.getData().getUsername());
    }

    @Test
    public void testGetUserByUsername() {
        log.info("开始测试根据用户名获取用户");
        
        // 先创建一个用户
        String username = "usernamequery" + new Random().nextInt(10000);
        UserDTO newUser = new UserDTO();
        newUser.setSsoId("test-sso-username-" + new Random().nextInt(10000));
        newUser.setUsername(username);
        newUser.setDisplayName("用户名查询测试用户");
        newUser.setEmail("usernamequery" + new Random().nextInt(1000) + "@example.com");
        newUser.setDepartment("IT");
        newUser.setIsActive(true);

        Result<UserDTO> createResult = userService.createUser(newUser);
        assertTrue(createResult.isSuccess());

        // 根据用户名查询用户
        Result<UserDTO> result = userService.getUserByUsername(username);
        log.info("根据用户名查询用户结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(username, result.getData().getUsername());
    }

    @Test
    public void testUpdateUser() {
        log.info("开始测试更新用户");
        
        // 先创建一个用户
        UserDTO newUser = new UserDTO();
        newUser.setSsoId("test-sso-update-" + new Random().nextInt(10000));
        newUser.setUsername("updateuser" + new Random().nextInt(1000));
        newUser.setDisplayName("更新测试用户");
        newUser.setEmail("update" + new Random().nextInt(1000) + "@example.com");
        newUser.setDepartment("IT");
        newUser.setTitle("初级工程师");
        newUser.setIsActive(true);

        Result<UserDTO> createResult = userService.createUser(newUser);
        assertTrue(createResult.isSuccess());
        Long userId = createResult.getData().getId();

        // 更新用户信息
        UserDTO updateUser = new UserDTO();
        updateUser.setDisplayName("更新后的用户名");
        updateUser.setTitle("高级工程师");
        updateUser.setBio("更新后的个人简介");
        updateUser.setTags(Arrays.asList("Java", "Spring Boot", "微服务"));

        Result<UserDTO> result = userService.updateUser(userId, updateUser);
        log.info("更新用户结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals("更新后的用户名", result.getData().getDisplayName());
        assertEquals("高级工程师", result.getData().getTitle());
        assertEquals("更新后的个人简介", result.getData().getBio());
        assertEquals(3, result.getData().getTags().size());
        assertTrue(result.getData().getTags().contains("微服务"));
    }

    @Test
    public void testGetUserList() {
        log.info("开始测试获取用户列表");
        
        // 创建几个测试用户
        for (int i = 1; i <= 3; i++) {
            UserDTO user = new UserDTO();
            user.setSsoId("test-sso-list-" + i + "-" + new Random().nextInt(1000));
            user.setUsername("listuser" + i + new Random().nextInt(100));
            user.setDisplayName("列表测试用户" + i);
            user.setEmail("listuser" + i + new Random().nextInt(100) + "@example.com");
            user.setDepartment("IT");
            user.setIsActive(true);
            userService.createUser(user);
        }

        // 测试分页查询
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetUserListRequest request = new GetUserListRequest();
        request.setDepartment("IT");
        request.setIsActive(true);

        Result<PageResult<UserDTO>> result = userService.getUserList(pageRequest, request);
        log.info("获取用户列表结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().getPagination().getTotalElements() >= 3);
    }

    @Test
    public void testToggleUserStatus() {
        log.info("开始测试切换用户状态");
        
        // 先创建一个用户
        UserDTO newUser = new UserDTO();
        newUser.setSsoId("test-sso-toggle-" + new Random().nextInt(10000));
        newUser.setUsername("toggleuser" + new Random().nextInt(1000));
        newUser.setDisplayName("状态切换测试用户");
        newUser.setEmail("toggle" + new Random().nextInt(1000) + "@example.com");
        newUser.setDepartment("IT");
        newUser.setIsActive(true);

        Result<UserDTO> createResult = userService.createUser(newUser);
        assertTrue(createResult.isSuccess());
        Long userId = createResult.getData().getId();

        // 切换用户状态为禁用
        Result<Void> result = userService.toggleUserStatus(userId, false);
        log.info("切换用户状态结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证状态是否已切换
        Result<UserDTO> queryResult = userService.getUserById(userId);
        assertTrue(queryResult.isSuccess());
        assertEquals(false, queryResult.getData().getIsActive());
    }

    @Test
    public void testLogin() {
        log.info("开始测试用户登录");
        
        // 先创建一个用户
        String ssoId = "test-sso-login-" + new Random().nextInt(10000);
        UserDTO newUser = new UserDTO();
        newUser.setSsoId(ssoId);
        newUser.setUsername("loginuser" + new Random().nextInt(1000));
        newUser.setDisplayName("登录测试用户");
        newUser.setEmail("login" + new Random().nextInt(1000) + "@example.com");
        newUser.setDepartment("IT");
        newUser.setIsActive(true);

        Result<UserDTO> createResult = userService.createUser(newUser);
        assertTrue(createResult.isSuccess());

        // 测试登录
        Result<UserDTO> result = userService.login(ssoId);
        log.info("用户登录结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(ssoId, result.getData().getSsoId());
    }

    @Test
    public void testDeleteUser() {
        log.info("开始测试删除用户");
        
        // 先创建一个用户
        UserDTO newUser = new UserDTO();
        newUser.setSsoId("test-sso-delete-" + new Random().nextInt(10000));
        newUser.setUsername("deleteuser" + new Random().nextInt(1000));
        newUser.setDisplayName("删除测试用户");
        newUser.setEmail("delete" + new Random().nextInt(1000) + "@example.com");
        newUser.setDepartment("IT");
        newUser.setIsActive(true);

        Result<UserDTO> createResult = userService.createUser(newUser);
        assertTrue(createResult.isSuccess());
        Long userId = createResult.getData().getId();

        // 删除用户
        Result<Void> result = userService.deleteUser(userId);
        log.info("删除用户结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证用户是否已删除
        Result<UserDTO> queryResult = userService.getUserById(userId);
        assertFalse(queryResult.isSuccess());
        assertEquals("USER_NOT_FOUND", queryResult.getCode());
    }
}
