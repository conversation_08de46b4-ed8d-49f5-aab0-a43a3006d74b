package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.CrawlerContentDTO;
import com.jdl.aic.core.service.client.service.CrawlerContentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.List;

/**
 * CrawlerContentService 集成测试
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class CrawlerContentServiceTest {

    @Resource
    private CrawlerContentService crawlerContentService;

    @Test
    public void testCreateCrawlerContent() {
        // 创建测试爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("测试爬虫内容标题");
        newContent.setLink("https://test.example.com/article/2");
        newContent.setContentMd5("test_md5_hash_1234567");
        newContent.setLanguage("zh-CN");
        newContent.setAuthor("张三");
        newContent.setDescription("这是一个测试爬虫内容");
        newContent.setContentType("article");
        newContent.setStatus(0);
        newContent.setIsFeatured(false);
        newContent.setContent("这是测试内容的正文部分");
        newContent.setWordCount(100);

        Result<CrawlerContentDTO> result = crawlerContentService.createCrawlerContent(newContent);
        System.out.println("创建爬虫内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("测试爬虫内容标题", result.getData().getTitle());
    }

    @Test
    public void testGetCrawlerContentById() {
        // 先创建一个爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("查询测试内容");
        newContent.setLink("https://test.example.com/query/1");
        newContent.setContentMd5("query_test_md5_123");
        newContent.setLanguage("zh-CN");
        newContent.setDescription("用于查询测试的爬虫内容");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());
        Long contentId = createResult.getData().getId();

        // 查询爬虫内容
        Result<CrawlerContentDTO> result = crawlerContentService.getCrawlerContentById(contentId);
        System.out.println("查询爬虫内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("查询测试内容", result.getData().getTitle());
    }

    @Test
    public void testGetCrawlerContentByMd5() {
        // 先创建一个爬虫内容
        String testMd5 = "unique_md5_hash_456";
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("MD5查询测试内容");
        newContent.setLink("https://test.example.com/md5/1");
        newContent.setContentMd5(testMd5);
        newContent.setLanguage("zh-CN");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());

        // 根据MD5查询
        Result<CrawlerContentDTO> result = crawlerContentService.getCrawlerContentByMd5(testMd5);
        System.out.println("根据MD5查询结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(testMd5, result.getData().getContentMd5());
    }

    @Test
    public void testGetCrawlerContentByLink() {
        // 先创建一个爬虫内容
        String testLink = "https://test.example.com/unique/link/1";
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("链接查询测试内容");
        newContent.setLink(testLink);
        newContent.setContentMd5("link_test_md5_789");
        newContent.setLanguage("zh-CN");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());

        // 根据链接查询
        Result<CrawlerContentDTO> result = crawlerContentService.getCrawlerContentByLink(testLink);
        System.out.println("根据链接查询结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(testLink, result.getData().getLink());
    }

    @Test
    public void testUpdateCrawlerContent() {
        // 先创建一个爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("更新测试内容");
        newContent.setLink("https://test.example.com/update/1");
        newContent.setContentMd5("update_test_md5_abc");
        newContent.setLanguage("zh-CN");
        newContent.setDescription("原始描述");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());
        Long contentId = createResult.getData().getId();

        // 更新爬虫内容
        CrawlerContentDTO updateContent = new CrawlerContentDTO();
        updateContent.setTitle("更新后的标题");
        updateContent.setDescription("更新后的描述");
        updateContent.setStatus(1);
        updateContent.setQualityScore(new BigDecimal("4.5"));

        Result<CrawlerContentDTO> result = crawlerContentService.updateCrawlerContent(contentId, updateContent);
        System.out.println("更新爬虫内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("更新后的标题", result.getData().getTitle());
        Assert.assertEquals("更新后的描述", result.getData().getDescription());
    }

    @Test
    public void testGetCrawlerContentsByStatus() {
        // 先创建几个不同状态的爬虫内容
        for (int i = 1; i <= 3; i++) {
            CrawlerContentDTO content = new CrawlerContentDTO();
            content.setTitle("状态测试内容" + i);
            content.setLink("https://test.example.com/status/" + i);
            content.setContentMd5("status_test_md5_" + i);
            content.setLanguage("zh-CN");
            content.setContentType("article");
            content.setStatus(1); // 已处理状态
            crawlerContentService.createCrawlerContent(content);
        }

        // 根据状态查询
        Result<List<CrawlerContentDTO>> result = crawlerContentService.getCrawlerContentsByStatus(1, 10);
        System.out.println("根据状态查询结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 3);
    }

    @Test
    public void testUpdateCrawlerContentStatus() {
        // 先创建一个爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("状态更新测试内容");
        newContent.setLink("https://test.example.com/status-update/1");
        newContent.setContentMd5("status_update_md5_def");
        newContent.setLanguage("zh-CN");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());
        Long contentId = createResult.getData().getId();

        // 更新状态
        Result<Void> result = crawlerContentService.updateCrawlerContentStatus(contentId, 1);
        System.out.println("更新爬虫内容状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更改
        Result<CrawlerContentDTO> getResult = crawlerContentService.getCrawlerContentById(contentId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(1), getResult.getData().getStatus());
    }

    @Test
    public void testUpdateAiSummary() {
        // 先创建一个爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("AI总结测试内容");
        newContent.setLink("https://test.example.com/ai-summary/1");
        newContent.setContentMd5("ai_summary_md5_ghi");
        newContent.setLanguage("zh-CN");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());
        Long contentId = createResult.getData().getId();

        // 更新AI总结
        String aiSummary = "这是AI生成的内容总结";
        Result<Void> result = crawlerContentService.updateAiSummary(contentId, aiSummary);
        System.out.println("更新AI总结结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证AI总结已更新
        Result<CrawlerContentDTO> getResult = crawlerContentService.getCrawlerContentById(contentId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(aiSummary, getResult.getData().getAiSummary());
    }

    @Test
    public void testUpdateQualityScore() {
        // 先创建一个爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("质量评分测试内容");
        newContent.setLink("https://test.example.com/quality/1");
        newContent.setContentMd5("quality_test_md5_jkl");
        newContent.setLanguage("zh-CN");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());
        Long contentId = createResult.getData().getId();

        // 更新质量评分
        BigDecimal qualityScore = new BigDecimal("4.8");
        Result<Void> result = crawlerContentService.updateQualityScore(contentId, qualityScore);
        System.out.println("更新质量评分结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证质量评分已更新
        Result<CrawlerContentDTO> getResult = crawlerContentService.getCrawlerContentById(contentId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(qualityScore, getResult.getData().getQualityScore());
    }

    @Test
    public void testUpdateFeaturedStatus() {
        // 先创建一个爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("精品状态测试内容");
        newContent.setLink("https://test.example.com/featured/1");
        newContent.setContentMd5("featured_test_md5_mno");
        newContent.setLanguage("zh-CN");
        newContent.setContentType("article");
        newContent.setStatus(0);
        newContent.setIsFeatured(false);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());
        Long contentId = createResult.getData().getId();

        // 更新精品状态
        Result<Void> result = crawlerContentService.updateFeaturedStatus(contentId, true);
        System.out.println("更新精品状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证精品状态已更新
        Result<CrawlerContentDTO> getResult = crawlerContentService.getCrawlerContentById(contentId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertTrue(getResult.getData().getIsFeatured());
    }

    @Test
    public void testGetCrawlerContentsByQualityScore() {
        // 先创建几个不同质量评分的内容
        for (int i = 1; i <= 3; i++) {
            CrawlerContentDTO content = new CrawlerContentDTO();
            content.setTitle("质量评分内容" + i);
            content.setLink("https://test.example.com/quality-range/" + i);
            content.setContentMd5("quality_range_md5_" + i);
            content.setLanguage("zh-CN");
            content.setContentType("article");
            content.setStatus(1);
            content.setQualityScore(new BigDecimal("4." + i));
            crawlerContentService.createCrawlerContent(content);
        }

        // 根据质量评分范围查询
        Result<List<CrawlerContentDTO>> result = crawlerContentService.getCrawlerContentsByQualityScore(
                new BigDecimal("4.0"), new BigDecimal("5.0"), 10);
        System.out.println("根据质量评分范围查询结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 3);
    }

    @Test
    public void testBatchCreateCrawlerContents() {
        // 批量创建爬虫内容
        CrawlerContentDTO content1 = new CrawlerContentDTO();
        content1.setTitle("批量内容1");
        content1.setLink("https://test.example.com/batch/1");
        content1.setContentMd5("batch_md5_1");
        content1.setLanguage("zh-CN");
        content1.setContentType("article");
        content1.setStatus(0);

        CrawlerContentDTO content2 = new CrawlerContentDTO();
        content2.setTitle("批量内容2");
        content2.setLink("https://test.example.com/batch/2");
        content2.setContentMd5("batch_md5_2");
        content2.setLanguage("zh-CN");
        content2.setContentType("article");
        content2.setStatus(0);

        List<CrawlerContentDTO> contents = Arrays.asList(content1, content2);

        Result<List<CrawlerContentDTO>> result = crawlerContentService.batchCreateCrawlerContents(contents);
        System.out.println("批量创建爬虫内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(2, result.getData().size());
    }

    @Test
    public void testSearchCrawlerContents() {
        // 先创建一些测试数据
        CrawlerContentDTO content = new CrawlerContentDTO();
        content.setTitle("搜索测试标题");
        content.setLink("https://test.example.com/search/1");
        content.setContentMd5("search_test_md5_pqr");
        content.setLanguage("zh-CN");
        content.setDescription("包含搜索关键词的描述");
        content.setContentType("article");
        content.setStatus(1);

        crawlerContentService.createCrawlerContent(content);

        // 搜索爬虫内容
        Result<List<CrawlerContentDTO>> result = crawlerContentService.searchCrawlerContents(
                "搜索", "article", "zh-CN", 1, 10);
        System.out.println("搜索爬虫内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 1);
    }

    @Test
    public void testIsContentExists() {
        // 先创建一个爬虫内容
        String testMd5 = "exists_test_md5_stu";
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("存在性检查测试内容");
        newContent.setLink("https://test.example.com/exists/1");
        newContent.setContentMd5(testMd5);
        newContent.setLanguage("zh-CN");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());

        // 检查内容是否存在
        Result<Boolean> result = crawlerContentService.isContentExists(testMd5);
        System.out.println("检查内容是否存在结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData());

        // 检查不存在的内容
        Result<Boolean> notExistsResult = crawlerContentService.isContentExists("non_exists_md5");
        Assert.assertTrue(notExistsResult.isSuccess());
        Assert.assertFalse(notExistsResult.getData());
    }

    @Test
    public void testDeleteCrawlerContent() {
        // 创建测试爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("删除测试内容");
        newContent.setLink("https://test.example.com/delete/1");
        newContent.setContentMd5("delete_test_md5_vwx");
        newContent.setLanguage("zh-CN");
        newContent.setDescription("用于删除测试的爬虫内容");
        newContent.setContentType("article");
        newContent.setStatus(0);

        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(newContent);
        Assert.assertTrue(createResult.isSuccess());
        Long contentId = createResult.getData().getId();

        // 删除爬虫内容
        Result<Void> result = crawlerContentService.deleteCrawlerContent(contentId);
        System.out.println("删除爬虫内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证爬虫内容已删除
        Result<CrawlerContentDTO> getResult = crawlerContentService.getCrawlerContentById(contentId);
        Assert.assertFalse(getResult.isSuccess());
    }

    @Test
    public void testCreateCrawlerContentWithNewFields() {
        // 创建包含新字段的测试爬虫内容
        CrawlerContentDTO newContent = new CrawlerContentDTO();
        newContent.setTitle("新字段测试内容");
        newContent.setLink("https://test.example.com/new-fields/" + System.currentTimeMillis());
        newContent.setContentMd5("test_md5_" + (System.currentTimeMillis() % 10000));
        newContent.setLanguage("zh-CN");
        newContent.setDescription("测试新增字段的爬虫内容");
        newContent.setContentType("article");
        newContent.setStatus(0);

        // 设置新增字段
        newContent.setType("video");
        newContent.setTaskId("task_" + System.currentTimeMillis());
        newContent.setTaskName("测试任务");

        // 设置附件信息
        List<Object> attachments = new ArrayList<>();
        Map<String, Object> attachment = new HashMap<>();
        attachment.put("name", "test.pdf");
        attachment.put("url", "https://example.com/test.pdf");
        attachment.put("size", 1024);
        attachments.add(attachment);
        newContent.setAttachments(attachments);

        // 设置媒体信息
        List<Object> media = new ArrayList<>();
        Map<String, Object> mediaItem = new HashMap<>();
        mediaItem.put("type", "video");
        mediaItem.put("url", "https://example.com/video.mp4");
        mediaItem.put("duration", 300);
        media.add(mediaItem);
        newContent.setMedia(media);

        Result<CrawlerContentDTO> result = crawlerContentService.createCrawlerContent(newContent);
        System.out.println("创建包含新字段的爬虫内容结果：" + JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("video", result.getData().getType());
        Assert.assertEquals(newContent.getTaskId(), result.getData().getTaskId());
        Assert.assertEquals("测试任务", result.getData().getTaskName());
        Assert.assertNotNull(result.getData().getAttachments());
        Assert.assertNotNull(result.getData().getMedia());
    }
}
