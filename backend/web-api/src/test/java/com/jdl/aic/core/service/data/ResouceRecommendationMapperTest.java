package com.jdl.aic.core.service.data;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.dao.entity.primary.LearningResource;
import com.jdl.aic.core.service.dao.mapper.primary.LearningResourceMapper;
import org.apache.ibatis.annotations.Param;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ResouceRecommendationMapperTest {
    @Autowired
    private LearningResourceMapper learningResourceMapper;

    @Test
    public void testGetResourceRecommendations() {
        // TODO: Implement test
        Long userId = 1L;
        String category = null;
        Integer limit = null;
        List<LearningResource> result = learningResourceMapper.selectRecommendedResources(userId, category, limit);
        System.out.println("结果："+ JSON.toJSONString(result));
    }
}
