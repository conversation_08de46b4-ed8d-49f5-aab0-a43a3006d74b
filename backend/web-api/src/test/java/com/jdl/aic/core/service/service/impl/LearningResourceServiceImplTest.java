package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.learning.*;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.common.ContentType;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningResourceListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningPathListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetRecommendedResourcesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.GetPopularResourcesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.SearchLearningResourcesRequest;
import com.jdl.aic.core.service.client.service.LearningResourceService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LearningResourceService 集成测试
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class LearningResourceServiceImplTest {

    @Resource
    private LearningResourceService learningResourceService;

    @Test
    public void testCreateLearningResource() {
        log.info("开始测试创建学习资源");
        
        // 创建测试学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("深度学习基础教程");
        newResource.setDescription("从零开始学习深度学习的基本概念和实践");
        newResource.setContent("详细的深度学习课程内容，包括理论和实践");
        newResource.setResourceType("video");
        newResource.setDifficulty("beginner");
        newResource.setLanguage("zh-CN");
        newResource.setSourceUrl("https://example.com/deep-learning-course");
        newResource.setDuration(600); // 10小时
        newResource.setIsActive(true);
        newResource.setTags(Arrays.asList("深度学习", "机器学习", "AI"));

        // 设置新字段
        newResource.setTags(Arrays.asList("深度学习", "机器学习", "AI"));
        newResource.setPrerequisites(Arrays.asList("Python基础", "数学基础"));
        newResource.setLearningObjectives(Arrays.asList("掌握深度学习基本概念", "能够构建简单的神经网络"));
        newResource.setContentType("video");

        Map<String, Object> contentConfig = new HashMap<>();
        contentConfig.put("videoQuality", "1080p");
        contentConfig.put("subtitles", true);
        newResource.setContentConfig(contentConfig);

        // 设置分类信息
        List<ContentCategoryRelationDTO> categories = Arrays.asList(
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 1L),
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 2L)
        );
        newResource.setCategories(categories);

        Result<LearningResourceDTO> result = learningResourceService.createLearningResource(newResource);
        log.info("创建学习资源结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("深度学习基础教程", result.getData().getTitle());
        Assert.assertEquals("详细的深度学习课程内容，包括理论和实践", result.getData().getContent());
        Assert.assertNotNull(result.getData().getTags());
        Assert.assertTrue(result.getData().getTags().contains("深度学习"));
        Assert.assertNotNull(result.getData().getPrerequisites());
        Assert.assertTrue(result.getData().getPrerequisites().contains("Python基础"));
        Assert.assertNotNull(result.getData().getLearningObjectives());
        Assert.assertEquals("video", result.getData().getContentType());
        Assert.assertNotNull(result.getData().getContentConfig());

        // 验证分类信息
        Assert.assertNotNull(result.getData().getCategories());
        Assert.assertEquals(2, result.getData().getCategories().size());
        Assert.assertEquals(Long.valueOf(1L), result.getData().getCategories().get(0).getCategoryId());
        Assert.assertEquals(Long.valueOf(2L), result.getData().getCategories().get(1).getCategoryId());
    }

    @Test
    public void testGetLearningResourceById() {
        log.info("开始测试根据ID获取学习资源");
        // 查询学习资源
        Result<LearningResourceDTO> result = learningResourceService.getLearningResourceById(5L);
        log.info("查询学习资源结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testCreateLearningResourceWithNewFields() {
        log.info("开始测试创建包含新字段的学习资源");

        // 创建包含所有新字段的测试学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("AI综合课程");
        newResource.setDescription("全面的AI学习课程");
        newResource.setContent("包含理论、实践、项目的完整AI课程内容");
        newResource.setResourceType("course");
        newResource.setDifficulty("intermediate");
        newResource.setLanguage("zh-CN");
        newResource.setSourceUrl("https://example.com/ai-course");
        newResource.setDuration(1200); // 20小时
        newResource.setIsActive(true);

        // 设置新字段
        newResource.setTags(Arrays.asList("人工智能", "机器学习", "深度学习", "计算机视觉"));
        newResource.setPrerequisites(Arrays.asList("Python编程", "线性代数", "概率统计"));
        newResource.setLearningObjectives(Arrays.asList("掌握AI基本概念", "能够实现机器学习算法", "完成AI项目"));
        newResource.setContentType("mixed");

        // 设置配置信息
        Map<String, Object> contentConfig = new HashMap<>();
        contentConfig.put("hasVideo", true);
        contentConfig.put("hasExercise", true);
        contentConfig.put("hasProject", true);
        newResource.setContentConfig(contentConfig);

        Map<String, Object> accessConfig = new HashMap<>();
        accessConfig.put("requiresLogin", true);
        accessConfig.put("isPaid", false);
        newResource.setAccessConfig(accessConfig);

        Map<String, Object> mediaMetadata = new HashMap<>();
        mediaMetadata.put("totalVideos", 50);
        mediaMetadata.put("totalDuration", "20h");
        newResource.setMediaMetadata(mediaMetadata);

        // 设置分类信息
        List<ContentCategoryRelationDTO> categories = Arrays.asList(
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 3L),
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 4L),
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 5L)
        );
        newResource.setCategories(categories);

        Result<LearningResourceDTO> result = learningResourceService.createLearningResource(newResource);
        log.info("创建包含新字段的学习资源结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("AI综合课程", result.getData().getTitle());
        Assert.assertEquals("包含理论、实践、项目的完整AI课程内容", result.getData().getContent());

        // 验证新字段
        Assert.assertNotNull(result.getData().getTags());
        Assert.assertEquals(4, result.getData().getTags().size());
        Assert.assertTrue(result.getData().getTags().contains("人工智能"));

        Assert.assertNotNull(result.getData().getPrerequisites());
        Assert.assertEquals(3, result.getData().getPrerequisites().size());
        Assert.assertTrue(result.getData().getPrerequisites().contains("Python编程"));

        Assert.assertNotNull(result.getData().getLearningObjectives());
        Assert.assertEquals(3, result.getData().getLearningObjectives().size());
        Assert.assertTrue(result.getData().getLearningObjectives().contains("掌握AI基本概念"));

        Assert.assertEquals("mixed", result.getData().getContentType());
        Assert.assertNotNull(result.getData().getContentConfig());
        Assert.assertNotNull(result.getData().getAccessConfig());
        Assert.assertNotNull(result.getData().getMediaMetadata());

        // 验证分类信息
        Assert.assertNotNull(result.getData().getCategories());
        Assert.assertEquals(3, result.getData().getCategories().size());
        Assert.assertEquals(Long.valueOf(3L), result.getData().getCategories().get(0).getCategoryId());
        Assert.assertEquals(Long.valueOf(4L), result.getData().getCategories().get(1).getCategoryId());
        Assert.assertEquals(Long.valueOf(5L), result.getData().getCategories().get(2).getCategoryId());
    }

    @Test
    public void testGetLearningResourceList() {
        log.info("开始测试获取学习资源列表");

        PageRequest pageRequest = PageRequest.of(1, 10);
        GetLearningResourceListRequest request = new GetLearningResourceListRequest(
                pageRequest, null, null, null, null, null);
        Result<PageResult<LearningResourceDTO>> result = learningResourceService.getLearningResourceList(request);
        log.info("获取学习资源列表结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testUpdateLearningResource() {
        log.info("开始测试更新学习资源");
        
        // 先创建一个学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("Python编程基础");
        newResource.setDescription("Python编程入门教程");
        newResource.setResourceType("course");
        newResource.setDifficulty("beginner");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(createResult.isSuccess());
        Long resourceId = createResult.getData().getId();

        // 更新学习资源
        LearningResourceDTO updateResource = new LearningResourceDTO();
        updateResource.setTitle("Python高级编程");
        updateResource.setDescription("Python高级特性和最佳实践");
        updateResource.setDifficulty("advanced");
        updateResource.setDuration(1200); // 20小时

        // 更新分类信息
        List<ContentCategoryRelationDTO> updatedCategories = Arrays.asList(
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 6L),
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 7L)
        );
        updateResource.setCategories(updatedCategories);

        Result<LearningResourceDTO> result = learningResourceService.updateLearningResource(resourceId, updateResource);
        log.info("更新学习资源结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("Python高级编程", result.getData().getTitle());
        Assert.assertEquals("advanced", result.getData().getDifficulty());

        // 验证更新后的分类信息
        Assert.assertNotNull(result.getData().getCategories());
        Assert.assertEquals(2, result.getData().getCategories().size());
        Assert.assertEquals(Long.valueOf(6L), result.getData().getCategories().get(0).getCategoryId());
        Assert.assertEquals(Long.valueOf(7L), result.getData().getCategories().get(1).getCategoryId());
    }

    @Test
    public void testLearningResourceWithCategories() {
        log.info("开始测试学习资源分类功能");

        // 1. 创建带分类的学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("分类测试资源");
        newResource.setDescription("用于测试分类功能的学习资源");
        newResource.setResourceType("course");
        newResource.setDifficulty("intermediate");
        newResource.setLanguage("zh-CN");
        newResource.setIsActive(true);

        // 设置初始分类
        List<ContentCategoryRelationDTO> initialCategories = Arrays.asList(
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 10L),
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 11L),
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 12L)
        );
        newResource.setCategories(initialCategories);

        // 创建资源
        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        log.info("创建带分类的学习资源结果：{}", JSON.toJSONString(createResult));

        Assert.assertNotNull(createResult);
        Assert.assertTrue(createResult.isSuccess());
        Assert.assertNotNull(createResult.getData());
        Long resourceId = createResult.getData().getId();

        // 验证创建时的分类
        Assert.assertNotNull(createResult.getData().getCategories());
        Assert.assertEquals(3, createResult.getData().getCategories().size());

        // 2. 查询资源，验证分类信息被正确加载
        Result<LearningResourceDTO> getResult = learningResourceService.getLearningResourceById(resourceId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertNotNull(getResult.getData().getCategories());
        Assert.assertEquals(3, getResult.getData().getCategories().size());

        // 验证分类ID
        List<Long> categoryIds = getResult.getData().getCategories().stream()
                .map(ContentCategoryRelationDTO::getCategoryId)
                .collect(java.util.stream.Collectors.toList());
        Assert.assertTrue(categoryIds.contains(10L));
        Assert.assertTrue(categoryIds.contains(11L));
        Assert.assertTrue(categoryIds.contains(12L));

        // 3. 更新资源分类
        LearningResourceDTO updateResource = new LearningResourceDTO();
        updateResource.setTitle("分类测试资源（已更新）");
        updateResource.setDescription("更新后的分类测试资源");

        // 设置新的分类（部分保留，部分新增，部分删除）
        List<ContentCategoryRelationDTO> updatedCategories = Arrays.asList(
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 11L), // 保留
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 13L), // 新增
                new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, 14L)  // 新增
        );
        updateResource.setCategories(updatedCategories);

        Result<LearningResourceDTO> updateResult = learningResourceService.updateLearningResource(resourceId, updateResource);
        log.info("更新学习资源分类结果：{}", JSON.toJSONString(updateResult));

        Assert.assertTrue(updateResult.isSuccess());
        Assert.assertNotNull(updateResult.getData().getCategories());
        Assert.assertEquals(3, updateResult.getData().getCategories().size());

        // 验证更新后的分类ID
        List<Long> updatedCategoryIds = updateResult.getData().getCategories().stream()
                .map(ContentCategoryRelationDTO::getCategoryId)
                .collect(java.util.stream.Collectors.toList());
        Assert.assertTrue(updatedCategoryIds.contains(11L)); // 保留的
        Assert.assertTrue(updatedCategoryIds.contains(13L)); // 新增的
        Assert.assertTrue(updatedCategoryIds.contains(14L)); // 新增的
        Assert.assertFalse(updatedCategoryIds.contains(10L)); // 删除的
        Assert.assertFalse(updatedCategoryIds.contains(12L)); // 删除的

        // 4. 测试清空分类
        LearningResourceDTO clearCategoriesResource = new LearningResourceDTO();
        clearCategoriesResource.setTitle("清空分类测试");
        clearCategoriesResource.setCategories(Arrays.asList()); // 空列表

        Result<LearningResourceDTO> clearResult = learningResourceService.updateLearningResource(resourceId, clearCategoriesResource);
        Assert.assertTrue(clearResult.isSuccess());

        // 验证分类已清空
        Result<LearningResourceDTO> finalGetResult = learningResourceService.getLearningResourceById(resourceId);
        Assert.assertTrue(finalGetResult.isSuccess());
        Assert.assertTrue(finalGetResult.getData().getCategories() == null ||
                         finalGetResult.getData().getCategories().isEmpty());

        log.info("学习资源分类功能测试完成");
    }

    @Test
    public void testUpdateLearningResourceStatus() {
        log.info("开始测试更新学习资源状态");
        
        // 先创建一个学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("状态测试资源");
        newResource.setDescription("用于状态测试的学习资源");
        newResource.setResourceType("video");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(createResult.isSuccess());
        Long resourceId = createResult.getData().getId();

        // 更新状态为下线
        Result<Void> result = learningResourceService.updateLearningResourceStatus(resourceId, 2);
        log.info("更新学习资源状态结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更改
        Result<LearningResourceDTO> getResult = learningResourceService.getLearningResourceById(resourceId);
        Assert.assertTrue(getResult.isSuccess());
        // 注意：这里需要根据实际的状态映射逻辑来验证
    }

    @Test
    public void testBatchUpdateLearningResourceStatus() {
        log.info("开始测试批量更新学习资源状态");
        
        // 先创建多个学习资源
        LearningResourceDTO resource1 = new LearningResourceDTO();
        resource1.setTitle("批量测试资源1");
        resource1.setResourceType("video");
        resource1.setIsActive(true);

        LearningResourceDTO resource2 = new LearningResourceDTO();
        resource2.setTitle("批量测试资源2");
        resource2.setResourceType("document");
        resource2.setIsActive(true);

        Result<LearningResourceDTO> createResult1 = learningResourceService.createLearningResource(resource1);
        Result<LearningResourceDTO> createResult2 = learningResourceService.createLearningResource(resource2);
        
        Assert.assertTrue(createResult1.isSuccess());
        Assert.assertTrue(createResult2.isSuccess());

        List<Long> resourceIds = Arrays.asList(
                createResult1.getData().getId(),
                createResult2.getData().getId()
        );

        // 批量更新状态
        Result<Void> result = learningResourceService.batchUpdateLearningResourceStatus(resourceIds, 0);
        log.info("批量更新学习资源状态结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testDeleteLearningResource() {
        log.info("开始测试删除学习资源");
        
        // 创建测试学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("删除测试资源");
        newResource.setDescription("用于删除测试的学习资源");
        newResource.setResourceType("tutorial");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(createResult.isSuccess());
        Long resourceId = createResult.getData().getId();

        // 删除学习资源
        Result<Void> result = learningResourceService.deleteLearningResource(resourceId);
        log.info("删除学习资源结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证学习资源已删除
        Result<LearningResourceDTO> getResult = learningResourceService.getLearningResourceById(resourceId);
        Assert.assertFalse(getResult.isSuccess());
    }

    @Test
    public void testSearchLearningResources() {
        log.info("开始测试搜索学习资源");
        
        // 先创建一些测试资源
        LearningResourceDTO resource1 = new LearningResourceDTO();
        resource1.setTitle("Java编程入门");
        resource1.setDescription("Java基础语法和面向对象编程");
        resource1.setResourceType("course");
        resource1.setIsActive(true);

        LearningResourceDTO resource2 = new LearningResourceDTO();
        resource2.setTitle("Spring框架详解");
        resource2.setDescription("Spring核心概念和实战应用");
        resource2.setResourceType("video");
        resource2.setIsActive(true);

        learningResourceService.createLearningResource(resource1);
        learningResourceService.createLearningResource(resource2);

        // 搜索包含"Java"的资源
        PageRequest pageRequest = PageRequest.of(0, 10);
        SearchLearningResourcesRequest request = new SearchLearningResourcesRequest(
                "Java", pageRequest, null);
        Result<PageResult<LearningResourceDTO>> result = learningResourceService.searchLearningResources(request);
        log.info("搜索学习资源结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testIncrementViewCount() {
        log.info("开始测试增加浏览次数");
        
        // 先创建一个学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("浏览测试资源");
        newResource.setResourceType("video");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(createResult.isSuccess());
        Long resourceId = createResult.getData().getId();

        // 增加浏览次数
        Result<Void> result = learningResourceService.incrementViewCount(resourceId);
        log.info("增加浏览次数结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testIncrementDownloadCount() {
        log.info("开始测试增加下载次数");
        
        // 先创建一个学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("下载测试资源");
        newResource.setResourceType("document");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(createResult.isSuccess());
        Long resourceId = createResult.getData().getId();

        // 增加下载次数
        Long userId = 1001L;
        Result<Void> result = learningResourceService.incrementDownloadCount(resourceId, userId);
        log.info("增加下载次数结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    // ==================== 学习路径管理测试 ====================

    @Test
    public void testCreateLearningPath() {
        log.info("开始测试创建学习路径");

        // 创建测试学习路径
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("AI工程师学习路径");
        newPath.setDescription("从零基础到AI工程师的完整学习路径");
        newPath.setDifficulty("intermediate");
        newPath.setEstimatedHours(120);
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        Result<LearningPathDTO> result = learningResourceService.createLearningPath(newPath);
        log.info("创建学习路径结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("AI工程师学习路径", result.getData().getTitle());
    }

    @Test
    public void testGetLearningPathById() {
        log.info("开始测试根据ID获取学习路径");

        // 先创建一个学习路径
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("数据科学学习路径");
        newPath.setDescription("数据科学完整学习路径");
        newPath.setDifficulty("advanced");
        newPath.setEstimatedHours(80);
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        Result<LearningPathDTO> createResult = learningResourceService.createLearningPath(newPath);
        Assert.assertTrue(createResult.isSuccess());
        Long pathId = createResult.getData().getId();

        // 查询学习路径
        Result<LearningPathDTO> result = learningResourceService.getLearningPathById(pathId);
        log.info("查询学习路径结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("数据科学学习路径", result.getData().getTitle());
    }

    @Test
    public void testGetLearningPathList() {
        log.info("开始测试获取学习路径列表");

        PageRequest pageRequest = PageRequest.of(0, 10);
        GetLearningPathListRequest request = new GetLearningPathListRequest(
                pageRequest, null, null, null, null);
        Result<PageResult<LearningPathDTO>> result = learningResourceService.getLearningPathList(request);
        log.info("获取学习路径列表结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testUpdateLearningPath() {
        log.info("开始测试更新学习路径");

        // 先创建一个学习路径
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("前端开发路径");
        newPath.setDescription("前端开发技能学习路径");
        newPath.setDifficulty("beginner");
        newPath.setEstimatedHours(60);
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        Result<LearningPathDTO> createResult = learningResourceService.createLearningPath(newPath);
        Assert.assertTrue(createResult.isSuccess());
        Long pathId = createResult.getData().getId();

        // 更新学习路径
        LearningPathDTO updatePath = new LearningPathDTO();
        updatePath.setTitle("全栈开发路径");
        updatePath.setDescription("全栈开发技能完整学习路径");
        updatePath.setDifficulty("intermediate");
        updatePath.setEstimatedHours(100);
        updatePath.setUpdatedBy("admin");

        Result<LearningPathDTO> result = learningResourceService.updateLearningPath(pathId, updatePath);
        log.info("更新学习路径结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("全栈开发路径", result.getData().getTitle());
        Assert.assertEquals("intermediate", result.getData().getDifficulty());
    }

    @Test
    public void testDeleteLearningPath() {
        log.info("开始测试删除学习路径");

        // 创建测试学习路径
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("删除测试路径");
        newPath.setDescription("用于删除测试的学习路径");
        newPath.setDifficulty("beginner");
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        Result<LearningPathDTO> createResult = learningResourceService.createLearningPath(newPath);
        Assert.assertTrue(createResult.isSuccess());
        Long pathId = createResult.getData().getId();

        // 删除学习路径
        Result<Void> result = learningResourceService.deleteLearningPath(pathId);
        log.info("删除学习路径结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证学习路径已删除
        Result<LearningPathDTO> getResult = learningResourceService.getLearningPathById(pathId);
        Assert.assertFalse(getResult.isSuccess());
    }

    @Test
    public void testAddResourceToPath() {
        log.info("开始测试为学习路径添加资源");

        // 先创建一个学习路径
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("测试路径");
        newPath.setDescription("用于测试添加资源的路径");
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        Result<LearningPathDTO> pathResult = learningResourceService.createLearningPath(newPath);
        Assert.assertTrue(pathResult.isSuccess());
        Long pathId = pathResult.getData().getId();

        // 创建一个学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("路径测试资源");
        newResource.setResourceType("video");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> resourceResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(resourceResult.isSuccess());
        Long resourceId = resourceResult.getData().getId();

        // 添加资源到路径
        Result<LearningPathResourceDTO> result = learningResourceService.addResourceToPath(pathId, resourceId, 1);
        log.info("添加资源到路径结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testRemoveResourceFromPath() {
        log.info("开始测试从学习路径移除资源");

        // 先创建路径和资源，并建立关联
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("移除测试路径");
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("移除测试资源");
        newResource.setResourceType("document");
        newResource.setIsActive(true);

        Result<LearningPathDTO> pathResult = learningResourceService.createLearningPath(newPath);
        Result<LearningResourceDTO> resourceResult = learningResourceService.createLearningResource(newResource);

        Assert.assertTrue(pathResult.isSuccess());
        Assert.assertTrue(resourceResult.isSuccess());

        Long pathId = pathResult.getData().getId();
        Long resourceId = resourceResult.getData().getId();

        // 先添加资源到路径
        Result<LearningPathResourceDTO> addResult = learningResourceService.addResourceToPath(pathId, resourceId, 1);
        Assert.assertTrue(addResult.isSuccess());

        // 从路径移除资源
        Result<Void> result = learningResourceService.removeResourceFromPath(pathId, resourceId);
        log.info("从路径移除资源结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testGetLearningPathResources() {
        log.info("开始测试获取学习路径的资源列表");

        // 先创建路径和资源，并建立关联
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("资源列表测试路径");
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        LearningResourceDTO resource1 = new LearningResourceDTO();
        resource1.setTitle("路径资源1");
        resource1.setResourceType("video");
        resource1.setIsActive(true);

        LearningResourceDTO resource2 = new LearningResourceDTO();
        resource2.setTitle("路径资源2");
        resource2.setResourceType("document");
        resource2.setIsActive(true);

        Result<LearningPathDTO> pathResult = learningResourceService.createLearningPath(newPath);
        Result<LearningResourceDTO> resource1Result = learningResourceService.createLearningResource(resource1);
        Result<LearningResourceDTO> resource2Result = learningResourceService.createLearningResource(resource2);

        Assert.assertTrue(pathResult.isSuccess());
        Assert.assertTrue(resource1Result.isSuccess());
        Assert.assertTrue(resource2Result.isSuccess());

        Long pathId = pathResult.getData().getId();
        Long resource1Id = resource1Result.getData().getId();
        Long resource2Id = resource2Result.getData().getId();

        // 添加资源到路径
        learningResourceService.addResourceToPath(pathId, resource1Id, 1);
        learningResourceService.addResourceToPath(pathId, resource2Id, 2);

        // 获取路径资源列表
        Result<List<LearningPathResourceDTO>> result = learningResourceService.getLearningPathResources(pathId);
        log.info("获取学习路径资源列表结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(2, result.getData().size());
    }

    @Test
    public void testReorderPathResources() {
        log.info("开始测试调整学习路径中资源的顺序");

        // 先创建路径和多个资源
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("顺序测试路径");
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        Result<LearningPathDTO> pathResult = learningResourceService.createLearningPath(newPath);
        Assert.assertTrue(pathResult.isSuccess());
        Long pathId = pathResult.getData().getId();

        // 创建多个资源并添加到路径
        LearningResourceDTO resource1 = new LearningResourceDTO();
        resource1.setTitle("顺序资源1");
        resource1.setResourceType("video");
        resource1.setIsActive(true);

        LearningResourceDTO resource2 = new LearningResourceDTO();
        resource2.setTitle("顺序资源2");
        resource2.setResourceType("document");
        resource2.setIsActive(true);

        LearningResourceDTO resource3 = new LearningResourceDTO();
        resource3.setTitle("顺序资源3");
        resource3.setResourceType("course");
        resource3.setIsActive(true);

        Result<LearningResourceDTO> result1 = learningResourceService.createLearningResource(resource1);
        Result<LearningResourceDTO> result2 = learningResourceService.createLearningResource(resource2);
        Result<LearningResourceDTO> result3 = learningResourceService.createLearningResource(resource3);

        Assert.assertTrue(result1.isSuccess());
        Assert.assertTrue(result2.isSuccess());
        Assert.assertTrue(result3.isSuccess());

        Long resource1Id = result1.getData().getId();
        Long resource2Id = result2.getData().getId();
        Long resource3Id = result3.getData().getId();

        // 添加资源到路径
        learningResourceService.addResourceToPath(pathId, resource1Id, 1);
        learningResourceService.addResourceToPath(pathId, resource2Id, 2);
        learningResourceService.addResourceToPath(pathId, resource3Id, 3);

        // 调整顺序：将资源3放到第一位
        List<Long> newOrder = Arrays.asList(resource3Id, resource1Id, resource2Id);
        Result<Void> result = learningResourceService.reorderPathResources(pathId, newOrder);
        log.info("调整学习路径资源顺序结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    // ==================== 学习进度跟踪测试 ====================

    @Test
    public void testGetUserLearningProgress() {
        log.info("开始测试获取用户学习进度");

        // 先创建一个学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("进度测试资源");
        newResource.setResourceType("course");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(createResult.isSuccess());
        Long resourceId = createResult.getData().getId();

        // 获取用户学习进度
        Long userId = 1001L;
        Result<UserLearningProgressDTO> result = learningResourceService.getUserLearningProgress(userId, resourceId);
        log.info("获取用户学习进度结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(userId, result.getData().getUserId());
        Assert.assertEquals(resourceId, result.getData().getResourceId());
    }

    @Test
    public void testUpdateUserLearningProgress() {
        log.info("开始测试更新用户学习进度");

        // 先创建一个学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("进度更新测试资源");
        newResource.setResourceType("video");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(createResult.isSuccess());
        Long resourceId = createResult.getData().getId();

        // 创建进度更新数据
        Long userId = 1002L;
        UserLearningProgressDTO progress = new UserLearningProgressDTO(userId, resourceId);
        progress.setProgress(50);
        progress.setTimeSpent(1800); // 30分钟
        progress.setIsCompleted(false);

        // 更新用户学习进度
        Result<UserLearningProgressDTO> result = learningResourceService.updateUserLearningProgress(
                userId, resourceId, progress);
        log.info("更新用户学习进度结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(Integer.valueOf(50), result.getData().getProgress());
    }

    @Test
    public void testMarkResourceAsCompleted() {
        log.info("开始测试标记学习资源为已完成");

        // 先创建一个学习资源
        LearningResourceDTO newResource = new LearningResourceDTO();
        newResource.setTitle("完成测试资源");
        newResource.setResourceType("tutorial");
        newResource.setIsActive(true);

        Result<LearningResourceDTO> createResult = learningResourceService.createLearningResource(newResource);
        Assert.assertTrue(createResult.isSuccess());
        Long resourceId = createResult.getData().getId();

        // 标记资源为已完成
        Long userId = 1003L;
        Result<Void> result = learningResourceService.markResourceAsCompleted(userId, resourceId);
        log.info("标记学习资源为已完成结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testGetUserPathProgress() {
        log.info("开始测试获取用户学习路径进度");

        // 先创建路径和资源
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("进度测试路径");
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        LearningResourceDTO resource1 = new LearningResourceDTO();
        resource1.setTitle("路径进度资源1");
        resource1.setResourceType("video");
        resource1.setIsActive(true);

        LearningResourceDTO resource2 = new LearningResourceDTO();
        resource2.setTitle("路径进度资源2");
        resource2.setResourceType("document");
        resource2.setIsActive(true);

        Result<LearningPathDTO> pathResult = learningResourceService.createLearningPath(newPath);
        Result<LearningResourceDTO> resource1Result = learningResourceService.createLearningResource(resource1);
        Result<LearningResourceDTO> resource2Result = learningResourceService.createLearningResource(resource2);

        Assert.assertTrue(pathResult.isSuccess());
        Assert.assertTrue(resource1Result.isSuccess());
        Assert.assertTrue(resource2Result.isSuccess());

        Long pathId = pathResult.getData().getId();
        Long resource1Id = resource1Result.getData().getId();
        Long resource2Id = resource2Result.getData().getId();

        // 添加资源到路径
        learningResourceService.addResourceToPath(pathId, resource1Id, 1);
        learningResourceService.addResourceToPath(pathId, resource2Id, 2);

        // 获取用户路径进度
        Long userId = 1004L;
        Result<List<UserLearningProgressDTO>> result = learningResourceService.getUserPathProgress(userId, pathId);
        log.info("获取用户学习路径进度结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(2, result.getData().size());
    }

    @Test
    public void testGetUserLearningStats() {
        log.info("开始测试获取用户学习统计");

        Long userId = 1005L;
        Result<Object> result = learningResourceService.getUserLearningStats(userId);
        log.info("获取用户学习统计结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    // ==================== 推荐和搜索测试 ====================

    @Test
    public void testGetRecommendedResources() {
        log.info("开始测试获取推荐学习资源");

        Long userId = 1006L;
        PageRequest pageRequest = PageRequest.of(0, 5);
        GetRecommendedResourcesRequest request = new GetRecommendedResourcesRequest(
                userId, pageRequest, null);
        Result<PageResult<LearningResourceDTO>> result = learningResourceService.getRecommendedResources(request);
        log.info("获取推荐学习资源结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testGetPopularResources() {
        log.info("开始测试获取热门学习资源");

        PageRequest pageRequest = PageRequest.of(0, 10);
        GetPopularResourcesRequest request = new GetPopularResourcesRequest(
                pageRequest, null, 30);
        Result<PageResult<LearningResourceDTO>> result = learningResourceService.getPopularResources(request);
        log.info("获取热门学习资源结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testCompleteWorkflow() {
        log.info("开始测试完整的学习资源管理工作流");

        // 1. 创建学习路径
        LearningPathDTO newPath = new LearningPathDTO();
        newPath.setTitle("完整工作流测试路径");
        newPath.setDescription("用于测试完整工作流的学习路径");
        newPath.setDifficulty("intermediate");
        newPath.setEstimatedHours(40);
        newPath.setIsActive(true);
        newPath.setCreatedBy("admin");

        Result<LearningPathDTO> pathResult = learningResourceService.createLearningPath(newPath);
        Assert.assertTrue(pathResult.isSuccess());
        Long pathId = pathResult.getData().getId();

        // 2. 创建多个学习资源
        LearningResourceDTO[] resources = new LearningResourceDTO[3];
        Long[] resourceIds = new Long[3];

        for (int i = 0; i < 3; i++) {
            resources[i] = new LearningResourceDTO();
            resources[i].setTitle("工作流资源" + (i + 1));
            resources[i].setDescription("工作流测试资源描述" + (i + 1));
            resources[i].setResourceType(i == 0 ? "video" : i == 1 ? "document" : "course");
            resources[i].setDifficulty("intermediate");
            resources[i].setDuration(60 * (i + 1)); // 1小时、2小时、3小时
            resources[i].setIsActive(true);

            // 为每个资源设置不同的分类
            List<ContentCategoryRelationDTO> categories = Arrays.asList(
                    new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, Long.valueOf(20 + i)),
                    new ContentCategoryRelationDTO(ContentType.LEARNING_RESOURCE, null, Long.valueOf(30 + i))
            );
            resources[i].setCategories(categories);

            Result<LearningResourceDTO> resourceResult = learningResourceService.createLearningResource(resources[i]);
            Assert.assertTrue(resourceResult.isSuccess());
            resourceIds[i] = resourceResult.getData().getId();

            // 验证分类信息
            Assert.assertNotNull(resourceResult.getData().getCategories());
            Assert.assertEquals(2, resourceResult.getData().getCategories().size());
        }

        // 3. 将资源添加到学习路径
        for (int i = 0; i < 3; i++) {
            Result<LearningPathResourceDTO> addResult = learningResourceService.addResourceToPath(
                    pathId, resourceIds[i], i + 1);
            Assert.assertTrue(addResult.isSuccess());
        }

        // 4. 获取学习路径详情
        Result<LearningPathDTO> pathDetailResult = learningResourceService.getLearningPathById(pathId);
        Assert.assertTrue(pathDetailResult.isSuccess());
        Assert.assertEquals(3, pathDetailResult.getData().getResources().size());

        // 5. 模拟用户学习进度
        Long userId = 2001L;

        // 标记第一个资源为已完成
        Result<Void> completeResult = learningResourceService.markResourceAsCompleted(userId, resourceIds[0]);
        Assert.assertTrue(completeResult.isSuccess());

        // 更新第二个资源的学习进度
        UserLearningProgressDTO progress = new UserLearningProgressDTO(userId, resourceIds[1]);
        progress.setProgress(75);
        progress.setTimeSpent(5400); // 1.5小时
        progress.setIsCompleted(false);

        Result<UserLearningProgressDTO> progressResult = learningResourceService.updateUserLearningProgress(
                userId, resourceIds[1], progress);
        Assert.assertTrue(progressResult.isSuccess());

        // 6. 获取用户在该路径的整体进度
        Result<List<UserLearningProgressDTO>> pathProgressResult = learningResourceService.getUserPathProgress(
                userId, pathId);
        Assert.assertTrue(pathProgressResult.isSuccess());
        Assert.assertEquals(3, pathProgressResult.getData().size());

        // 7. 增加资源浏览次数
        for (Long resourceId : resourceIds) {
            Result<Void> viewResult = learningResourceService.incrementViewCount(resourceId);
            Assert.assertTrue(viewResult.isSuccess());
        }

        log.info("完整工作流测试成功完成");
    }

    @Test
    public void testGetResourceCategoryStatistics() {
        Result<List<CategoryStatisticsDTO>> result = learningResourceService.getResourceCategoryStatistics();
        System.out.println(JSON.toJSONString(result));
    }
}
