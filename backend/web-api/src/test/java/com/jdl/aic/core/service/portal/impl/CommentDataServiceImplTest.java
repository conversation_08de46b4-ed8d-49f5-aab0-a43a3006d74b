package com.jdl.aic.core.service.portal.impl;

import com.alibaba.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.CommentDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.client.dto.request.community.DeleteCommentRequest;
import com.jdl.aic.core.service.portal.client.CommentDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;
import java.util.Random;

/**
 * 评论服务测试类 - 参考UserServiceImplTest结构
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
public class CommentDataServiceImplTest {

    @Resource
    private CommentDataService commentDataService;

    @Test
    public void testCreateComment() {
        log.info("开始测试创建评论");
        
        // 创建测试评论
        CreateCommentRequest request = new CreateCommentRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0); // 知识类型
        request.setContentId(100L + new Random().nextInt(100));
        request.setCommentText("这是一条测试评论内容 - " + new Random().nextInt(10000));
        request.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> result = commentDataService.createComment(request);
        log.info("创建评论结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertNotNull(result.getData().getId());
        assertEquals(request.getUserId(), result.getData().getUserId());
        assertEquals(request.getContentType(), result.getData().getContentType());
        assertEquals(request.getContentId(), result.getData().getContentId());
        assertEquals(request.getCommentText(), result.getData().getContent());
    }

    @Test
    public void testGetCommentById() {
        log.info("开始测试根据ID获取评论");
        
        // 先创建一个评论
        CreateCommentRequest createRequest = new CreateCommentRequest();
        createRequest.setUserId(1000L + new Random().nextInt(1000));
        createRequest.setContentType(0);
        createRequest.setContentId(100L + new Random().nextInt(100));
        createRequest.setCommentText("查询测试评论内容");
        createRequest.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> createResult = commentDataService.createComment(createRequest);
        assertTrue(createResult.isSuccess());
        Long commentId = createResult.getData().getId();

        // 根据ID查询评论
        Result<CommentDTO> result = commentDataService.getCommentById(commentId);
        log.info("根据ID查询评论结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(commentId, result.getData().getId());
        assertEquals(createRequest.getCommentText(), result.getData().getContent());
    }

    @Test
    public void testReplyComment() {
        log.info("开始测试回复评论");
        
        // 先创建一个父评论
        CreateCommentRequest createRequest = new CreateCommentRequest();
        createRequest.setUserId(1000L + new Random().nextInt(1000));
        createRequest.setContentType(0);
        createRequest.setContentId(100L + new Random().nextInt(100));
        createRequest.setCommentText("父评论内容");
        createRequest.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> createResult = commentDataService.createComment(createRequest);
        assertTrue(createResult.isSuccess());
        Long parentCommentId = createResult.getData().getId();

        // 回复评论
        ReplyCommentRequest replyRequest = new ReplyCommentRequest();
        replyRequest.setUserId(1000L + new Random().nextInt(1000));
        replyRequest.setParentCommentId(parentCommentId);
        replyRequest.setCommentText("这是一条回复内容");

        Result<CommentDTO> result = commentDataService.replyComment(replyRequest);
        log.info("回复评论结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(replyRequest.getCommentText(), result.getData().getContent());
        assertEquals(parentCommentId, result.getData().getParentId());
    }

    @Test
    public void testUpdateComment() {
        log.info("开始测试更新评论");
        
        // 先创建一个评论
        Long userId = 1000L + new Random().nextInt(1000);
        CreateCommentRequest createRequest = new CreateCommentRequest();
        createRequest.setUserId(userId);
        createRequest.setContentType(0);
        createRequest.setContentId(100L + new Random().nextInt(100));
        createRequest.setCommentText("原始评论内容");
        createRequest.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> createResult = commentDataService.createComment(createRequest);
        assertTrue(createResult.isSuccess());
        Long commentId = createResult.getData().getId();

        // 更新评论内容
        UpdateCommentRequest updateRequest = new UpdateCommentRequest();
        updateRequest.setUserId(userId);
        updateRequest.setCommentId(commentId);
        updateRequest.setCommentText("更新后的评论内容");

        Result<CommentDTO> result = commentDataService.updateComment(updateRequest);
        log.info("更新评论结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals("更新后的评论内容", result.getData().getContent());
    }

    @Test
    public void testGetCommentsByContent() {
        log.info("开始测试获取内容评论列表");
        
        Integer contentType = 0;
        Long contentId = 100L + new Random().nextInt(100);
        
        // 为同一个内容创建多条评论
        for (int i = 1; i <= 3; i++) {
            CreateCommentRequest request = new CreateCommentRequest();
            request.setUserId(1000L + new Random().nextInt(1000));
            request.setContentType(contentType);
            request.setContentId(contentId);
            request.setCommentText("列表测试评论内容" + i);
            request.setRelatedKnowledgeTypeId(1L);
            commentDataService.createComment(request);
        }

        // 测试分页查询
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetCommentsByContentRequest request = new GetCommentsByContentRequest();
        request.setContentType(contentType);
        request.setContentId(contentId);
        request.setPageRequest(pageRequest);
        request.setSortBy("time");

        Result<PageResult<CommentDTO>> result = commentDataService.getCommentsByContent(request);
        log.info("获取内容评论列表结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().getPagination().getTotalElements() >= 3);
    }

    @Test
    public void testGetCommentCount() {
        log.info("开始测试获取评论数量");
        
        Integer contentType = 0;
        Long contentId = 200L + new Random().nextInt(100);
        
        // 为内容创建几条评论
        for (int i = 1; i <= 2; i++) {
            CreateCommentRequest request = new CreateCommentRequest();
            request.setUserId(1000L + new Random().nextInt(1000));
            request.setContentType(contentType);
            request.setContentId(contentId);
            request.setCommentText("统计测试评论" + i);
            request.setRelatedKnowledgeTypeId(1L);
            commentDataService.createComment(request);
        }

        // 统计评论数量
        GetCommentCountRequest request = new GetCommentCountRequest();
        request.setContentType(contentType);
        request.setContentId(contentId);

        Result<Integer> result = commentDataService.getCommentCount(request);
        log.info("获取评论数量结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData() >= 2);
    }

    @Test
    public void testDeleteComment() {
        log.info("开始测试删除评论");
        
        // 先创建一个评论
        Long userId = 1000L + new Random().nextInt(1000);
        CreateCommentRequest createRequest = new CreateCommentRequest();
        createRequest.setUserId(userId);
        createRequest.setContentType(0);
        createRequest.setContentId(100L + new Random().nextInt(100));
        createRequest.setCommentText("待删除的评论内容");
        createRequest.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> createResult = commentDataService.createComment(createRequest);
        assertTrue(createResult.isSuccess());
        Long commentId = createResult.getData().getId();

        Result<Void> result = commentDataService.deleteComment(userId, commentId);
        log.info("删除评论结果：{}", JSON.toJSONString(result));
        
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证评论是否已删除（应该查询不到或状态为已删除）
        Result<CommentDTO> queryResult = commentDataService.getCommentById(commentId);
        // 根据业务逻辑，可能返回失败或者状态为已删除
        if (queryResult.isSuccess()) {
            assertEquals(2, queryResult.getData().getStatus()); // 2表示已删除
        }
    }

    @Test
    public void testGetUserCommentCount() {
        log.info("开始测试获取用户评论数量");

        Long userId = 1000L + new Random().nextInt(1000);

        // 为用户创建几条评论
        for (int i = 1; i <= 2; i++) {
            CreateCommentRequest request = new CreateCommentRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(100L + new Random().nextInt(100));
            request.setCommentText("用户评论统计测试" + i);
            request.setRelatedKnowledgeTypeId(1L);
            commentDataService.createComment(request);
        }

        // 统计用户评论数量
        Result<Integer> result = commentDataService.getUserCommentCount(userId);
        log.info("获取用户评论数量结果：{}", JSON.toJSONString(result));

        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData() >= 2);
    }


    @Test
    public void testCommentHierarchy() {
        log.info("开始测试评论层级结构");

        // 创建父评论
        CreateCommentRequest parentRequest = new CreateCommentRequest();
        parentRequest.setUserId(1000L + new Random().nextInt(1000));
        parentRequest.setContentType(0);
        parentRequest.setContentId(100L + new Random().nextInt(100));
        parentRequest.setCommentText("父评论内容");
        parentRequest.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> parentResult = commentDataService.createComment(parentRequest);
        assertTrue(parentResult.isSuccess());
        Long parentCommentId = parentResult.getData().getId();

        // 创建子评论
        ReplyCommentRequest childRequest = new ReplyCommentRequest();
        childRequest.setUserId(1000L + new Random().nextInt(1000));
        childRequest.setParentCommentId(parentCommentId);
        childRequest.setCommentText("子评论内容");

        Result<CommentDTO> childResult = commentDataService.replyComment(childRequest);
        log.info("创建子评论结果：{}", JSON.toJSONString(childResult));
        assertTrue(childResult.isSuccess());
        assertEquals(parentCommentId, childResult.getData().getParentId());

        // 验证层级关系
        Result<CommentDTO> queryResult = commentDataService.getCommentById(childResult.getData().getId());
        assertTrue(queryResult.isSuccess());
        assertEquals(parentCommentId, queryResult.getData().getParentId());
    }

    @Test
    public void testBatchOperations() {
        log.info("开始测试批量操作");

        Integer contentType = 0;
        Long contentId = 300L + new Random().nextInt(100);

        // 批量创建评论
        for (int i = 1; i <= 10; i++) {
            CreateCommentRequest request = new CreateCommentRequest();
            request.setUserId(1000L + new Random().nextInt(1000));
            request.setContentType(contentType);
            request.setContentId(contentId);
            request.setCommentText("批量测试评论" + i);
            request.setRelatedKnowledgeTypeId(1L);

            Result<CommentDTO> result = commentDataService.createComment(request);
            assertTrue(result.isSuccess());
        }

        // 测试分页查询性能
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(5);

        GetCommentsByContentRequest request = new GetCommentsByContentRequest();
        request.setContentType(contentType);
        request.setContentId(contentId);
        request.setPageRequest(pageRequest);

        long startTime = System.currentTimeMillis();
        Result<PageResult<CommentDTO>> result = commentDataService.getCommentsByContent(request);
        long endTime = System.currentTimeMillis();

        log.info("批量查询耗时：{}ms", endTime - startTime);
        assertTrue(result.isSuccess());
    }

    @Test
    public void testSpecialCharacters() {
        log.info("开始测试特殊字符处理");

        // 测试包含特殊字符的评论
        CreateCommentRequest request1 = new CreateCommentRequest();
        request1.setUserId(1000L + new Random().nextInt(1000));
        request1.setContentType(0);
        request1.setContentId(100L + new Random().nextInt(100));
        request1.setCommentText("包含特殊字符的评论：@#$%^&*()_+{}|:<>?[]\\;'\",./<>?");
        request1.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> result1 = commentDataService.createComment(request1);
        log.info("特殊字符评论结果：{}", JSON.toJSONString(result1));
        assertTrue(result1.isSuccess());
        assertEquals(request1.getCommentText(), result1.getData().getContent());

        // 测试包含表情符号的评论
        CreateCommentRequest request2 = new CreateCommentRequest();
        request2.setUserId(1000L + new Random().nextInt(1000));
        request2.setContentType(0);
        request2.setContentId(100L + new Random().nextInt(100));
        request2.setCommentText("包含表情的评论：😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘");
        request2.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> result2 = commentDataService.createComment(request2);
        log.info("表情符号评论结果：{}", JSON.toJSONString(result2));
        assertTrue(result2.isSuccess());
        assertEquals(request2.getCommentText(), result2.getData().getContent());

        // 测试包含HTML标签的评论（应该被转义或过滤）
        CreateCommentRequest request3 = new CreateCommentRequest();
        request3.setUserId(1000L + new Random().nextInt(1000));
        request3.setContentType(0);
        request3.setContentId(100L + new Random().nextInt(100));
        request3.setCommentText("包含HTML的评论：<script>alert('test')</script><b>粗体</b><i>斜体</i>");
        request3.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> result3 = commentDataService.createComment(request3);
        log.info("HTML标签评论结果：{}", JSON.toJSONString(result3));
        assertTrue(result3.isSuccess());
        // 验证HTML标签是否被正确处理（转义或过滤）
        assertNotNull(result3.getData().getContent());
    }


    @Test
    public void testDataIntegrity() {
        log.info("开始测试数据完整性");

        // 创建评论并验证数据完整性
        CreateCommentRequest request = new CreateCommentRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0);
        request.setContentId(100L + new Random().nextInt(100));
        request.setCommentText("数据完整性测试评论");
        request.setRelatedKnowledgeTypeId(1L);

        Result<CommentDTO> result = commentDataService.createComment(request);
        assertTrue(result.isSuccess());

        CommentDTO comment = result.getData();

        // 验证必要字段不为空
        assertNotNull(comment.getId());
        assertNotNull(comment.getUserId());
        assertNotNull(comment.getContentType());
        assertNotNull(comment.getContentId());
        assertNotNull(comment.getContent());
        assertNotNull(comment.getCreatedAt());

        // 验证数据一致性
        assertEquals(request.getUserId(), comment.getUserId());
        assertEquals(request.getContentType(), comment.getContentType());
        assertEquals(request.getContentId(), comment.getContentId());
        assertEquals(request.getCommentText(), comment.getContent());
        assertEquals(request.getRelatedKnowledgeTypeId(), comment.getRelatedKnowledgeTypeId());

        // 验证默认状态
        assertNotNull(comment.getStatus());
        assertTrue(comment.getStatus() >= 0 && comment.getStatus() <= 2);
    }
}
