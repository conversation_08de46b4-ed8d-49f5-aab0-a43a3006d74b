package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.rss.RssSourceDTO;
import com.jdl.aic.core.service.client.dto.request.rss.GetRssSourceListRequest;
import com.jdl.aic.core.service.client.dto.request.rss.UpdateRssSourceStatusRequest;
import com.jdl.aic.core.service.client.dto.request.rss.GetRssSourcesByCategoryRequest;
import com.jdl.aic.core.service.client.service.RssSourceService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * RSS源服务测试类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class RssSourceServiceTest {

    @Resource
    private RssSourceService rssSourceService;

    @Test
    public void testCreateRssSource() {
        // 创建测试RSS源
        RssSourceDTO newRssSource = new RssSourceDTO();
        newRssSource.setName("测试RSS源");
        newRssSource.setFeedUrl("https://example.com/test-rss.xml");
        newRssSource.setDescription("这是一个测试RSS源");
        newRssSource.setCategory("测试分类");
        newRssSource.setType(RssSourceDTO.Type.OFFICIAL);
        newRssSource.setStatus(RssSourceDTO.Status.ACTIVE);

        Result<RssSourceDTO> result = rssSourceService.createRssSource(newRssSource);
        System.out.println("创建RSS源结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
    }

    @Test
    public void testGetRssSourceById() {
        // 查询RSS源
        Result<RssSourceDTO> result = rssSourceService.getRssSourceById(1L);
        System.out.println("查询RSS源结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetRssSourceList() {
        // 创建分页请求
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0); // 页码从0开始
        pageRequest.setSize(10);

        // 创建查询请求
        GetRssSourceListRequest request = new GetRssSourceListRequest();
        request.setActiveOnly(true);

        Result<PageResult<RssSourceDTO>> result = rssSourceService.getRssSourceList(pageRequest, request);
        System.out.println("RSS源列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testUpdateRssSource() {
        // 先创建一个RSS源
        RssSourceDTO newRssSource = new RssSourceDTO();
        newRssSource.setName("更新测试RSS源");
        newRssSource.setFeedUrl("https://example.com/update-test-rss.xml");
        newRssSource.setDescription("用于更新测试的RSS源");
        newRssSource.setCategory("测试分类");
        newRssSource.setType(RssSourceDTO.Type.OFFICIAL);
        newRssSource.setStatus(RssSourceDTO.Status.ACTIVE);

        Result<RssSourceDTO> createResult = rssSourceService.createRssSource(newRssSource);
        Assert.assertTrue(createResult.isSuccess());
        Long rssSourceId = createResult.getData().getId();

        // 更新RSS源
        RssSourceDTO updateRssSource = new RssSourceDTO();
        updateRssSource.setName("更新后的RSS源名称");
        updateRssSource.setDescription("更新后的描述");
        updateRssSource.setCategory("更新后的分类");

        Result<RssSourceDTO> result = rssSourceService.updateRssSource(rssSourceId, updateRssSource);
        System.out.println("更新RSS源结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("更新后的RSS源名称", result.getData().getName());
    }

    @Test
    public void testUpdateRssSourceStatus() {
        // 先创建一个RSS源
        RssSourceDTO newRssSource = new RssSourceDTO();
        newRssSource.setName("状态测试RSS源");
        newRssSource.setFeedUrl("https://example.com/status-test-rss.xml");
        newRssSource.setDescription("用于状态测试的RSS源");
        newRssSource.setCategory("测试分类");
        newRssSource.setType(RssSourceDTO.Type.OFFICIAL);
        newRssSource.setStatus(RssSourceDTO.Status.ACTIVE);

        Result<RssSourceDTO> createResult = rssSourceService.createRssSource(newRssSource);
        Assert.assertTrue(createResult.isSuccess());
        Long rssSourceId = createResult.getData().getId();

        // 更新RSS源状态
        UpdateRssSourceStatusRequest request = new UpdateRssSourceStatusRequest();
        request.setId(rssSourceId);
        request.setStatus(RssSourceDTO.Status.PAUSED);

        Result<Void> result = rssSourceService.updateRssSourceStatus(request);
        System.out.println("更新RSS源状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更新
        Result<RssSourceDTO> getResult = rssSourceService.getRssSourceById(rssSourceId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals((Integer) RssSourceDTO.Status.PAUSED, getResult.getData().getStatus());
    }

    @Test
    public void testGetRssSourcesByCategory() {
        GetRssSourcesByCategoryRequest request = new GetRssSourcesByCategoryRequest();
        request.setCategory("AI资讯");
        request.setActiveOnly(true);

        Result<List<RssSourceDTO>> result = rssSourceService.getRssSourcesByCategory(request);
        System.out.println("根据分类查询RSS源结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetActiveRssSources() {
        Result<List<RssSourceDTO>> result = rssSourceService.getActiveRssSources();
        System.out.println("查询活跃RSS源结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetUserRssSources() {
        String ownerId = "test-user-001";
        Result<List<RssSourceDTO>> result = rssSourceService.getUserRssSources(ownerId);
        System.out.println("查询用户RSS源结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateLastFetchedTime() {
        // 先创建一个RSS源
        RssSourceDTO newRssSource = new RssSourceDTO();
        newRssSource.setName("抓取时间测试RSS源");
        newRssSource.setFeedUrl("https://example.com/fetch-time-test-rss.xml");
        newRssSource.setDescription("用于抓取时间测试的RSS源");
        newRssSource.setCategory("测试分类");
        newRssSource.setType(RssSourceDTO.Type.OFFICIAL);
        newRssSource.setStatus(RssSourceDTO.Status.ACTIVE);

        Result<RssSourceDTO> createResult = rssSourceService.createRssSource(newRssSource);
        Assert.assertTrue(createResult.isSuccess());
        Long rssSourceId = createResult.getData().getId();

        // 更新最后抓取时间
        Result<Void> result = rssSourceService.updateLastFetchedTime(rssSourceId);
        System.out.println("更新最后抓取时间结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testBatchUpdateStatus() {
        // 先创建两个RSS源
        RssSourceDTO rssSource1 = new RssSourceDTO();
        rssSource1.setName("批量更新测试RSS源1");
        rssSource1.setFeedUrl("https://example.com/batch-test-1-rss.xml");
        rssSource1.setType(RssSourceDTO.Type.OFFICIAL);
        rssSource1.setStatus(RssSourceDTO.Status.ACTIVE);

        RssSourceDTO rssSource2 = new RssSourceDTO();
        rssSource2.setName("批量更新测试RSS源2");
        rssSource2.setFeedUrl("https://example.com/batch-test-2-rss.xml");
        rssSource2.setType(RssSourceDTO.Type.OFFICIAL);
        rssSource2.setStatus(RssSourceDTO.Status.ACTIVE);

        Result<RssSourceDTO> createResult1 = rssSourceService.createRssSource(rssSource1);
        Result<RssSourceDTO> createResult2 = rssSourceService.createRssSource(rssSource2);
        Assert.assertTrue(createResult1.isSuccess());
        Assert.assertTrue(createResult2.isSuccess());

        List<Long> ids = Arrays.asList(createResult1.getData().getId(), createResult2.getData().getId());

        // 批量更新状态
        Result<Void> result = rssSourceService.batchUpdateStatus(ids, RssSourceDTO.Status.PAUSED);
        System.out.println("批量更新状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testCheckFeedUrlExists() {
        String feedUrl = "https://example.com/check-exists-test-rss.xml";
        
        // 检查不存在的URL
        Result<Boolean> result1 = rssSourceService.checkFeedUrlExists(feedUrl, null);
        System.out.println("检查RSS订阅地址存在性结果1：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertTrue(result1.isSuccess());
        Assert.assertFalse(result1.getData());

        // 创建RSS源
        RssSourceDTO newRssSource = new RssSourceDTO();
        newRssSource.setName("存在性检查测试RSS源");
        newRssSource.setFeedUrl(feedUrl);
        newRssSource.setType(RssSourceDTO.Type.OFFICIAL);
        newRssSource.setStatus(RssSourceDTO.Status.ACTIVE);

        Result<RssSourceDTO> createResult = rssSourceService.createRssSource(newRssSource);
        Assert.assertTrue(createResult.isSuccess());

        // 再次检查存在性
        Result<Boolean> result2 = rssSourceService.checkFeedUrlExists(feedUrl, null);
        System.out.println("检查RSS订阅地址存在性结果2：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertTrue(result2.isSuccess());
        Assert.assertTrue(result2.getData());
    }

    @Test
    public void testGetRssSourceStatistics() {
        Result<RssSourceService.RssSourceStatistics> result = rssSourceService.getRssSourceStatistics();
        System.out.println("RSS源统计信息结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getTotalCount());
    }

    @Test
    public void testDeleteRssSource() {
        // 先创建一个RSS源
        RssSourceDTO newRssSource = new RssSourceDTO();
        newRssSource.setName("删除测试RSS源");
        newRssSource.setFeedUrl("https://example.com/delete-test-rss.xml");
        newRssSource.setDescription("用于删除测试的RSS源");
        newRssSource.setCategory("测试分类");
        newRssSource.setType(RssSourceDTO.Type.OFFICIAL);
        newRssSource.setStatus(RssSourceDTO.Status.ACTIVE);

        Result<RssSourceDTO> createResult = rssSourceService.createRssSource(newRssSource);
        Assert.assertTrue(createResult.isSuccess());
        Long rssSourceId = createResult.getData().getId();

        // 删除RSS源
        Result<Void> result = rssSourceService.deleteRssSource(rssSourceId);
        System.out.println("删除RSS源结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证RSS源已删除
        Result<RssSourceDTO> getResult = rssSourceService.getRssSourceById(rssSourceId);
        Assert.assertFalse(getResult.isSuccess());
    }
}
