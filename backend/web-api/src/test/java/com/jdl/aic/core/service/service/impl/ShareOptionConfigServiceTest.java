package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.ShareOptionConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;
import com.jdl.aic.core.service.client.service.ShareOptionConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 分享选项配置服务测试类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ShareOptionConfigServiceTest {

    @Resource
    private ShareOptionConfigService shareOptionConfigService;

    @Test
    public void testCreateConfig() {
        log.info("开始测试创建分享选项配置");
        
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_content");
        config.setShareType("wechat");
        config.setDisplayName("微信分享");
        config.setIsEnabled(true);
        config.setIconUrl("https://example.com/wechat.png");
        config.setSortOrder(1);
        config.setConfigJson("{\"appId\": \"test_app_id\"}");

        Result<ShareOptionConfigDTO> result = shareOptionConfigService.createConfig(config);
        log.info("创建分享选项配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("test_content", result.getData().getContentType());
        Assert.assertEquals("wechat", result.getData().getShareType());
        Assert.assertEquals("微信分享", result.getData().getDisplayName());
    }

    @Test
    public void testGetConfigById() {
        log.info("开始测试根据ID获取分享选项配置");
        
        // 先创建一个配置
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_get_content");
        config.setShareType("email");
        config.setDisplayName("邮件分享");
        config.setIsEnabled(true);
        
        Result<ShareOptionConfigDTO> createResult = shareOptionConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 根据ID获取配置
        Result<ShareOptionConfigDTO> result = shareOptionConfigService.getConfigById(configId);
        log.info("根据ID获取分享选项配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(configId, result.getData().getId());
        Assert.assertEquals("test_get_content", result.getData().getContentType());
        Assert.assertEquals("email", result.getData().getShareType());
    }

    @Test
    public void testGetConfigByContentAndShare() {
        log.info("开始测试根据内容类型和分享类型获取配置");
        
        // 先创建一个配置
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_content_share");
        config.setShareType("link_copy");
        config.setDisplayName("复制链接");
        config.setIsEnabled(true);
        
        Result<ShareOptionConfigDTO> createResult = shareOptionConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());

        // 根据内容类型和分享类型获取配置
        GetShareOptionConfigByContentAndShareRequest request = new GetShareOptionConfigByContentAndShareRequest("test_content_share", "link_copy");
        Result<ShareOptionConfigDTO> result = shareOptionConfigService.getConfigByContentAndShare(request);
        log.info("根据内容类型和分享类型获取配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("test_content_share", result.getData().getContentType());
        Assert.assertEquals("link_copy", result.getData().getShareType());
        Assert.assertEquals("复制链接", result.getData().getDisplayName());
    }

    @Test
    public void testUpdateConfig() {
        log.info("开始测试更新分享选项配置");
        
        // 先创建一个配置
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_update_content");
        config.setShareType("teams");
        config.setDisplayName("Teams分享");
        config.setIsEnabled(true);
        config.setSortOrder(1);
        
        Result<ShareOptionConfigDTO> createResult = shareOptionConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 更新配置
        ShareOptionConfigDTO updateConfig = new ShareOptionConfigDTO();
        updateConfig.setContentType("test_update_content");
        updateConfig.setShareType("teams");
        updateConfig.setDisplayName("Microsoft Teams分享");
        updateConfig.setIsEnabled(false);
        updateConfig.setSortOrder(2);
        updateConfig.setConfigJson("{\"webhookUrl\": \"test_url\"}");

        Result<ShareOptionConfigDTO> result = shareOptionConfigService.updateConfig(configId, updateConfig);
        log.info("更新分享选项配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("Microsoft Teams分享", result.getData().getDisplayName());
        Assert.assertEquals(false, result.getData().getIsEnabled());
        Assert.assertEquals(Integer.valueOf(2), result.getData().getSortOrder());
    }

    @Test
    public void testToggleConfigStatus() {
        log.info("开始测试切换分享选项配置状态");
        
        // 先创建一个配置
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_toggle_content");
        config.setShareType("slack");
        config.setDisplayName("Slack分享");
        config.setIsEnabled(true);
        
        Result<ShareOptionConfigDTO> createResult = shareOptionConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 切换状态为禁用
        ToggleShareOptionConfigStatusRequest request = new ToggleShareOptionConfigStatusRequest(configId, false);
        Result<Void> result = shareOptionConfigService.toggleConfigStatus(request);
        log.info("切换分享选项配置状态结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已切换
        Result<ShareOptionConfigDTO> getResult = shareOptionConfigService.getConfigById(configId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(false, getResult.getData().getIsEnabled());
    }

    @Test
    public void testUpdateConfigSortOrder() {
        log.info("开始测试更新分享选项配置排序");
        
        // 先创建一个配置
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_sort_content");
        config.setShareType("internal");
        config.setDisplayName("内部分享");
        config.setIsEnabled(true);
        config.setSortOrder(1);
        
        Result<ShareOptionConfigDTO> createResult = shareOptionConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        UpdateShareOptionConfigSortOrderRequest request = new UpdateShareOptionConfigSortOrderRequest(configId, 10);
        // 更新排序
        Result<Void> result = shareOptionConfigService.updateConfigSortOrder(request);
        log.info("更新分享选项配置排序结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证排序已更新
        Result<ShareOptionConfigDTO> getResult = shareOptionConfigService.getConfigById(configId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(10), getResult.getData().getSortOrder());
    }

    @Test
    public void testGetConfigList() {
        log.info("开始测试获取分享选项配置列表");
        
        PageRequest pageRequest = PageRequest.of(1, 10);
        GetShareOptionConfigListRequest request = new GetShareOptionConfigListRequest();
        Result<PageResult<ShareOptionConfigDTO>> result = shareOptionConfigService.getConfigList(
                pageRequest, request);
        log.info("获取分享选项配置列表结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetConfigsByContentType() {
        log.info("开始测试根据内容类型获取配置");
        
        // 先创建一个配置
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_content_type");
        config.setShareType("wechat");
        config.setDisplayName("微信分享");
        config.setIsEnabled(true);
        
        Result<ShareOptionConfigDTO> createResult = shareOptionConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());

        GetConfigsByContentTypeRequest request = new GetConfigsByContentTypeRequest("test_content_type", true);
        Result<List<ShareOptionConfigDTO>> result = shareOptionConfigService.getConfigsByContentType(request);
        log.info("根据内容类型获取配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() > 0);
    }

    @Test
    public void testGetConfigsByShareType() {
        log.info("开始测试根据分享类型获取配置");
        
        GetShareOptionConfigsByShareTypeRequest request = new GetShareOptionConfigsByShareTypeRequest("wechat", true);
        Result<List<ShareOptionConfigDTO>> result = shareOptionConfigService.getConfigsByShareType(request);
        log.info("根据分享类型获取配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetAllEnabledConfigs() {
        log.info("开始测试获取所有启用的分享选项配置");
        
        Result<List<ShareOptionConfigDTO>> result = shareOptionConfigService.getAllEnabledConfigs();
        log.info("获取所有启用的分享选项配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testBatchUpdateStatusByContentType() {
        log.info("开始测试批量更新内容类型的分享选项状态");
        
        // 先创建一些配置
        ShareOptionConfigDTO config1 = new ShareOptionConfigDTO();
        config1.setContentType("test_batch_content");
        config1.setShareType("wechat");
        config1.setDisplayName("微信分享");
        config1.setIsEnabled(true);
        
        ShareOptionConfigDTO config2 = new ShareOptionConfigDTO();
        config2.setContentType("test_batch_content");
        config2.setShareType("email");
        config2.setDisplayName("邮件分享");
        config2.setIsEnabled(true);
        
        shareOptionConfigService.createConfig(config1);
        shareOptionConfigService.createConfig(config2);

        // 批量更新状态
        BatchUpdateStatusByContentTypeRequest request = new BatchUpdateStatusByContentTypeRequest("test_batch_content", false);
        Result<Void> result = shareOptionConfigService.batchUpdateStatusByContentType(request);
        log.info("批量更新内容类型的分享选项状态结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testBatchUpdateSortOrder() {
        log.info("开始测试批量更新排序");
        
        // 先创建两个配置
        ShareOptionConfigDTO config1 = new ShareOptionConfigDTO();
        config1.setContentType("test_batch_sort");
        config1.setShareType("wechat");
        config1.setDisplayName("微信分享");
        config1.setIsEnabled(true);
        
        ShareOptionConfigDTO config2 = new ShareOptionConfigDTO();
        config2.setContentType("test_batch_sort");
        config2.setShareType("email");
        config2.setDisplayName("邮件分享");
        config2.setIsEnabled(true);
        
        Result<ShareOptionConfigDTO> createResult1 = shareOptionConfigService.createConfig(config1);
        Result<ShareOptionConfigDTO> createResult2 = shareOptionConfigService.createConfig(config2);
        
        Assert.assertTrue(createResult1.isSuccess());
        Assert.assertTrue(createResult2.isSuccess());

        // 批量更新排序
        List<Long> configIds = Arrays.asList(createResult1.getData().getId(), createResult2.getData().getId());
        List<Integer> sortOrders = Arrays.asList(10, 20);

        BatchUpdateSortOrderRequest request = new BatchUpdateSortOrderRequest(configIds, sortOrders);
        request.setConfigIds(configIds);
        request.setSortOrders(sortOrders);
        Result<Void> result = shareOptionConfigService.batchUpdateSortOrder(request);
        log.info("批量更新排序结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testCheckConfigExists() {
        log.info("开始测试检查配置是否存在");
        
        // 先创建一个配置
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_check_content");
        config.setShareType("wechat");
        config.setDisplayName("微信分享");
        config.setIsEnabled(true);
        
        Result<ShareOptionConfigDTO> createResult = shareOptionConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());

        // 检查配置是否存在
        CheckShareOptionConfigExistsRequest request = new CheckShareOptionConfigExistsRequest("test_check_content", "wechat", null);
        Result<Boolean> result = shareOptionConfigService.checkConfigExists(request);
        log.info("检查配置是否存在结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData());

        // 检查不存在的配置
        CheckShareOptionConfigExistsRequest notExistRequest = new CheckShareOptionConfigExistsRequest("not_exist_content", "not_exist_share", null);
        Result<Boolean> notExistResult = shareOptionConfigService.checkConfigExists(notExistRequest);
        Assert.assertTrue(notExistResult.isSuccess());
        Assert.assertFalse(notExistResult.getData());
    }

    @Test
    public void testSearchConfigs() {
        log.info("开始测试搜索分享选项配置");

        SearchConfigsRequest request = new SearchConfigsRequest("微信", true);
        Result<List<ShareOptionConfigDTO>> result = shareOptionConfigService.searchConfigs(request);
        log.info("搜索分享选项配置结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetSupportedShareTypes() {
        log.info("开始测试获取支持的分享类型列表");
        
        Result<List<String>> result = shareOptionConfigService.getSupportedShareTypes();
        log.info("获取支持的分享类型列表结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() > 0);
        Assert.assertTrue(result.getData().contains("internal"));
        Assert.assertTrue(result.getData().contains("wechat"));
        Assert.assertTrue(result.getData().contains("email"));
        Assert.assertTrue(result.getData().contains("link_copy"));
        Assert.assertTrue(result.getData().contains("teams"));
        Assert.assertTrue(result.getData().contains("slack"));
    }

    @Test
    public void testInitDefaultConfigs() {
        log.info("开始测试初始化默认配置");
        
        Result<List<ShareOptionConfigDTO>> result = shareOptionConfigService.initDefaultConfigs(
                "test_init_content");
        log.info("初始化默认配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() > 0);
    }

    @Test
    public void testDeleteConfig() {
        log.info("开始测试删除分享选项配置");
        
        // 先创建一个配置
        ShareOptionConfigDTO config = new ShareOptionConfigDTO();
        config.setContentType("test_delete_content");
        config.setShareType("wechat");
        config.setDisplayName("微信分享");
        config.setIsEnabled(true);
        
        Result<ShareOptionConfigDTO> createResult = shareOptionConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 删除配置
        Result<Void> result = shareOptionConfigService.deleteConfig(configId);
        log.info("删除分享选项配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证配置已删除
        Result<ShareOptionConfigDTO> getResult = shareOptionConfigService.getConfigById(configId);
        Assert.assertFalse(getResult.isSuccess());
    }
}
