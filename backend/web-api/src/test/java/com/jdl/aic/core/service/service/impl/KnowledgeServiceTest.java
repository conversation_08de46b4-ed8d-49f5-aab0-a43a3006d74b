package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeDTO;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO;
import com.jdl.aic.core.service.client.dto.request.knowledge.GetKnowledgeListRequest;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * KnowledgeService 集成测试
 *
 * <AUTHOR> Community Development Team
 * @version 2.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class KnowledgeServiceTest {

    @Resource
    private KnowledgeService knowledgeService;

    // ==================== 知识类型管理测试 ====================

    @Test
    public void testCreateKnowledgeType() {
        // 创建测试知识类型
        KnowledgeTypeDTO newKnowledgeType = new KnowledgeTypeDTO();
        newKnowledgeType.setCode("TEST_TYPE");
        newKnowledgeType.setName("测试知识类型");
        newKnowledgeType.setDescription("这是一个测试知识类型");
        newKnowledgeType.setIconUrl("https://example.com/icon.png");
        newKnowledgeType.setIsActive(true);

        // 设置JSON配置
        Map<String, Object> metadataSchema = new HashMap<>();
        metadataSchema.put("type", "object");
        metadataSchema.put("properties", new HashMap<>());
        newKnowledgeType.setMetadataSchema(metadataSchema);

        Map<String, Object> renderConfig = new HashMap<>();
        renderConfig.put("template", "default");
        renderConfig.put("layout", "card");
        newKnowledgeType.setRenderConfigJson(renderConfig);

        Map<String, Object> communityConfig = new HashMap<>();
        communityConfig.put("allowComments", true);
        communityConfig.put("allowLikes", true);
        newKnowledgeType.setCommunityConfigJson(communityConfig);

        Result<KnowledgeTypeDTO> result = knowledgeService.createKnowledgeType(newKnowledgeType);
        System.out.println("创建知识类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
    }

    @Test
    public void testGetKnowledgeTypeById() {
        // 查询知识类型
        Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeById(18L);
        System.out.println("查询知识类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
    }

    @Test
    public void testCreateAndGetKnowledgeTypeById() {
        // 先创建一个知识类型
        KnowledgeTypeDTO newKnowledgeType = createTestKnowledgeType("QUERY_TEST_1", "查询测试类型1");
        Result<KnowledgeTypeDTO> createResult = knowledgeService.createKnowledgeType(newKnowledgeType);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeTypeId = createResult.getData().getId();

        // 查询知识类型
        Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeById(knowledgeTypeId);
        System.out.println("查询知识类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("查询测试类型", result.getData().getName());
    }

    @Test
    public void testGetKnowledgeTypeByCode() {
        // 先创建一个知识类型
        KnowledgeTypeDTO newKnowledgeType = createTestKnowledgeType("CODE_QUERY_TEST", "编码查询测试类型");
        Result<KnowledgeTypeDTO> createResult = knowledgeService.createKnowledgeType(newKnowledgeType);
        Assert.assertTrue(createResult.isSuccess());

        // 根据编码查询知识类型
        Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeByCode("CODE_QUERY_TEST");
        System.out.println("根据编码查询知识类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("CODE_QUERY_TEST", result.getData().getCode());
    }

    @Test
    public void testGetKnowledgeTypeList() {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(10);

        Result<PageResult<KnowledgeTypeDTO>> result = knowledgeService.getKnowledgeTypeList(pageRequest, true, null);
        System.out.println("知识类型列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateKnowledgeType() {
        // 先创建一个知识类型
        KnowledgeTypeDTO newKnowledgeType = createTestKnowledgeType("UPDATE_TEST", "更新测试类型");
        Result<KnowledgeTypeDTO> createResult = knowledgeService.createKnowledgeType(newKnowledgeType);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeTypeId = createResult.getData().getId();

        // 更新知识类型
        KnowledgeTypeDTO updateKnowledgeType = new KnowledgeTypeDTO();
        updateKnowledgeType.setName("更新后的类型名称");
        updateKnowledgeType.setDescription("更新后的描述");
        updateKnowledgeType.setIsActive(false);

        Result<KnowledgeTypeDTO> result = knowledgeService.updateKnowledgeType(knowledgeTypeId, updateKnowledgeType);
        System.out.println("更新知识类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("更新后的类型名称", result.getData().getName());
    }

    @Test
    public void testToggleKnowledgeTypeStatus() {
        // 先创建一个知识类型
        KnowledgeTypeDTO newKnowledgeType = createTestKnowledgeType("STATUS_TEST", "状态测试类型");
        Result<KnowledgeTypeDTO> createResult = knowledgeService.createKnowledgeType(newKnowledgeType);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeTypeId = createResult.getData().getId();

        // 禁用知识类型
        Result<Void> result = knowledgeService.toggleKnowledgeTypeStatus(knowledgeTypeId, false);
        System.out.println("切换知识类型状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更改
        Result<KnowledgeTypeDTO> getResult = knowledgeService.getKnowledgeTypeById(knowledgeTypeId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertFalse(getResult.getData().getIsActive());
    }

    @Test
    public void testDeleteKnowledgeType() {
        // 先创建一个知识类型
        KnowledgeTypeDTO newKnowledgeType = createTestKnowledgeType("DELETE_TEST", "删除测试类型");
        Result<KnowledgeTypeDTO> createResult = knowledgeService.createKnowledgeType(newKnowledgeType);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeTypeId = createResult.getData().getId();

        // 删除知识类型
        Result<Void> result = knowledgeService.deleteKnowledgeType(knowledgeTypeId);
        System.out.println("删除知识类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证知识类型已删除
        Result<KnowledgeTypeDTO> getResult = knowledgeService.getKnowledgeTypeById(knowledgeTypeId);
        Assert.assertFalse(getResult.isSuccess());
    }

    // ==================== 知识内容管理测试 ====================

    @Test
    public void testCreateKnowledge() {
        // 创建测试知识内容
        KnowledgeDTO newKnowledge = new KnowledgeDTO();
        newKnowledge.setTitle("测试知识内容");
        newKnowledge.setDescription("这是一个测试知识内容");
        newKnowledge.setContent("# 测试内容\n这是测试内容的详细信息。");
        newKnowledge.setKnowledgeTypeId(1L);
        newKnowledge.setAuthorId("1");
        newKnowledge.setStatus(0); // 草稿状态
        newKnowledge.setVisibility(2); // 公开
        newKnowledge.setVersion("1.0.0");
        newKnowledge.setAiTags(Arrays.asList("测试1", "知识内容1"));
        // 设置新字段的默认值
        newKnowledge.setFavoriteCount(0);
        newKnowledge.setShareCount(0);
        newKnowledge.setSocialScore(new java.math.BigDecimal("0.00"));

        Result<KnowledgeDTO> result = knowledgeService.createKnowledge(newKnowledge);
        System.out.println("创建知识内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("测试知识内容", result.getData().getTitle());
    }

    @Test
    public void testGetKnowledgeById() {
        // 查询知识内容
        Result<KnowledgeDTO> result = knowledgeService.getKnowledgeById(1L);
        System.out.println("查询知识内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetKnowledgeList() {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(10);

        GetKnowledgeListRequest request = new GetKnowledgeListRequest();

        Result<PageResult<KnowledgeDTO>> result = knowledgeService.getKnowledgeList(
                pageRequest, request);
        System.out.println("知识内容列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateKnowledge() {
        // 更新知识内容
        KnowledgeDTO updateKnowledge = new KnowledgeDTO();
        updateKnowledge.setId(16L);
        updateKnowledge.setTitle("更新后的知识标题");
        updateKnowledge.setDescription("更新后的描述");
        updateKnowledge.setContent("# 更新后的内容\n这是更新后的内容。");
        updateKnowledge.setAuthorName("ceshi");
        updateKnowledge.setVisibility(2);
        updateKnowledge.setAiTags(Arrays.asList("测试16", "知识内容16"));
        HashMap<String, Object> map = new HashMap<>();
        map.put("aaa", "bbb");
        updateKnowledge.setMetadataJson(map);

        System.out.println("更新知识内容：" + JSON.toJSONString(updateKnowledge));
        Result<KnowledgeDTO> result = knowledgeService.updateKnowledge(16L, updateKnowledge);
        System.out.println("更新知识内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("更新后的知识标题", result.getData().getTitle());
    }

    @Test
    public void testUpdateKnowledgeStatus() {
        // 先创建知识类型和知识内容
        KnowledgeTypeDTO knowledgeType = createTestKnowledgeType("STATUS_KNOWLEDGE_TEST", "状态知识测试类型");
        Result<KnowledgeTypeDTO> typeResult = knowledgeService.createKnowledgeType(knowledgeType);
        Assert.assertTrue(typeResult.isSuccess());

        KnowledgeDTO newKnowledge = createTestKnowledge("状态测试知识", typeResult.getData().getId());
        Result<KnowledgeDTO> createResult = knowledgeService.createKnowledge(newKnowledge);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeId = createResult.getData().getId();

        // 更新知识状态为已发布
        Result<Void> result = knowledgeService.updateKnowledgeStatus(knowledgeId, 2);
        System.out.println("更新知识状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更改
        Result<KnowledgeDTO> getResult = knowledgeService.getKnowledgeById(knowledgeId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(2), getResult.getData().getStatus());
    }

    @Test
    public void testDeleteKnowledge() {
        // 先创建知识类型和知识内容
        KnowledgeTypeDTO knowledgeType = createTestKnowledgeType("DELETE_KNOWLEDGE_TEST", "删除知识测试类型");
        Result<KnowledgeTypeDTO> typeResult = knowledgeService.createKnowledgeType(knowledgeType);
        Assert.assertTrue(typeResult.isSuccess());

        KnowledgeDTO newKnowledge = createTestKnowledge("删除测试知识", typeResult.getData().getId());
        Result<KnowledgeDTO> createResult = knowledgeService.createKnowledge(newKnowledge);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeId = createResult.getData().getId();

        // 删除知识内容
        Result<Void> result = knowledgeService.deleteKnowledge(knowledgeId);
        System.out.println("删除知识内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证知识内容已删除
        Result<KnowledgeDTO> getResult = knowledgeService.getKnowledgeById(knowledgeId);
        Assert.assertFalse(getResult.isSuccess());
    }

    @Test
    public void testSearchKnowledge() {
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(10);

        Result<PageResult<KnowledgeDTO>> result = knowledgeService.searchKnowledge(
                "测试", pageRequest, null, null, null);
        System.out.println("搜索知识内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testIncrementReadCount() {
        // 先创建知识类型和知识内容
        KnowledgeTypeDTO knowledgeType = createTestKnowledgeType("READ_COUNT_TEST", "阅读计数测试类型");
        Result<KnowledgeTypeDTO> typeResult = knowledgeService.createKnowledgeType(knowledgeType);
        Assert.assertTrue(typeResult.isSuccess());

        KnowledgeDTO newKnowledge = createTestKnowledge("阅读计数测试知识", typeResult.getData().getId());
        Result<KnowledgeDTO> createResult = knowledgeService.createKnowledge(newKnowledge);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeId = createResult.getData().getId();

        // 增加阅读次数
        Result<Void> result = knowledgeService.incrementReadCount(knowledgeId);
        System.out.println("增加阅读次数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    // ==================== 批量操作测试 ====================

    @Test
    public void testBatchUpdateKnowledgeStatus() {
        // 先创建知识类型和多个知识内容
        KnowledgeTypeDTO knowledgeType = createTestKnowledgeType("BATCH_STATUS_TEST", "批量状态测试类型");
        Result<KnowledgeTypeDTO> typeResult = knowledgeService.createKnowledgeType(knowledgeType);
        Assert.assertTrue(typeResult.isSuccess());

        List<Long> knowledgeIds = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            KnowledgeDTO knowledge = createTestKnowledge("批量状态测试知识" + i, typeResult.getData().getId());
            Result<KnowledgeDTO> createResult = knowledgeService.createKnowledge(knowledge);
            Assert.assertTrue(createResult.isSuccess());
            knowledgeIds.add(createResult.getData().getId());
        }

        // 批量更新状态
        Result<Void> result = knowledgeService.batchUpdateKnowledgeStatus(knowledgeIds, 2);
        System.out.println("批量更新知识状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testBatchDeleteKnowledge() {
        // 先创建知识类型和多个知识内容
        KnowledgeTypeDTO knowledgeType = createTestKnowledgeType("BATCH_DELETE_TEST", "批量删除测试类型");
        Result<KnowledgeTypeDTO> typeResult = knowledgeService.createKnowledgeType(knowledgeType);
        Assert.assertTrue(typeResult.isSuccess());

        List<Long> knowledgeIds = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            KnowledgeDTO knowledge = createTestKnowledge("批量删除测试知识" + i, typeResult.getData().getId());
            Result<KnowledgeDTO> createResult = knowledgeService.createKnowledge(knowledge);
            Assert.assertTrue(createResult.isSuccess());
            knowledgeIds.add(createResult.getData().getId());
        }

        // 批量删除
        Result<Void> result = knowledgeService.batchDeleteKnowledge(knowledgeIds);
        System.out.println("批量删除知识内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    // ==================== 版本管理测试 ====================

    @Test
    public void testGetKnowledgeVersionHistory() {
        // 先创建知识类型和知识内容
        KnowledgeTypeDTO knowledgeType = createTestKnowledgeType("VERSION_HISTORY_TEST", "版本历史测试类型");
        Result<KnowledgeTypeDTO> typeResult = knowledgeService.createKnowledgeType(knowledgeType);
        Assert.assertTrue(typeResult.isSuccess());

        KnowledgeDTO newKnowledge = createTestKnowledge("版本历史测试知识", typeResult.getData().getId());
        Result<KnowledgeDTO> createResult = knowledgeService.createKnowledge(newKnowledge);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeId = createResult.getData().getId();

        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(1);
        pageRequest.setSize(10);

        // 获取版本历史
        Result<PageResult<Object>> result = knowledgeService.getKnowledgeVersionHistory(knowledgeId, pageRequest);
        System.out.println("获取知识版本历史结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        // 注意：由于版本管理功能可能未实现，这里只检查结果不为null
    }

    @Test
    public void testCreateKnowledgeVersion() {
        // 先创建知识类型和知识内容
        KnowledgeTypeDTO knowledgeType = createTestKnowledgeType("VERSION_CREATE_TEST", "版本创建测试类型");
        Result<KnowledgeTypeDTO> typeResult = knowledgeService.createKnowledgeType(knowledgeType);
        Assert.assertTrue(typeResult.isSuccess());

        KnowledgeDTO newKnowledge = createTestKnowledge("版本创建测试知识", typeResult.getData().getId());
        Result<KnowledgeDTO> createResult = knowledgeService.createKnowledge(newKnowledge);
        Assert.assertTrue(createResult.isSuccess());
        Long knowledgeId = createResult.getData().getId();

        // 创建知识版本
        Result<Object> result = knowledgeService.createKnowledgeVersion(knowledgeId, "测试版本创建");
        System.out.println("创建知识版本结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        // 注意：由于版本管理功能可能未实现，这里只检查结果不为null
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的知识类型
     */
    private KnowledgeTypeDTO createTestKnowledgeType(String code, String name) {
        KnowledgeTypeDTO knowledgeType = new KnowledgeTypeDTO();
        knowledgeType.setCode(code);
        knowledgeType.setName(name);
        knowledgeType.setDescription("测试用知识类型：" + name);
        knowledgeType.setIconUrl("https://example.com/icon.png");
        knowledgeType.setIsActive(true);

        // 设置JSON配置
        Map<String, Object> metadataSchema = new HashMap<>();
        metadataSchema.put("type", "object");
        metadataSchema.put("properties", new HashMap<>());
        knowledgeType.setMetadataSchema(metadataSchema);

        Map<String, Object> renderConfig = new HashMap<>();
        renderConfig.put("template", "default");
        renderConfig.put("layout", "card");
        knowledgeType.setRenderConfigJson(renderConfig);

        Map<String, Object> communityConfig = new HashMap<>();
        communityConfig.put("allowComments", true);
        communityConfig.put("allowLikes", true);
        knowledgeType.setCommunityConfigJson(communityConfig);

        return knowledgeType;
    }

    /**
     * 创建测试用的知识内容
     */
    private KnowledgeDTO createTestKnowledge(String title, Long knowledgeTypeId) {
        KnowledgeDTO knowledge = new KnowledgeDTO();
        knowledge.setTitle(title);
        knowledge.setDescription("测试用知识内容：" + title);
        knowledge.setContent("# " + title + "\n这是测试用的知识内容详情。");
        knowledge.setKnowledgeTypeId(knowledgeTypeId);
        knowledge.setAuthorId("1");
        knowledge.setStatus(0); // 草稿状态
        knowledge.setVisibility(2); // 公开
        knowledge.setVersion("1.0.0");

        Map<String, Object> metadata = new HashMap<>();
        metadata.put("category", "test");
        metadata.put("difficulty", "easy");
        knowledge.setMetadataJson(metadata);

        return knowledge;
    }

    // ==================== 边界条件和异常测试 ====================

    @Test
    public void testGetKnowledgeTypeByIdWithInvalidId() {
        // 测试无效ID
        Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeById(-1L);
        System.out.println("无效ID查询知识类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isSuccess());
    }

    @Test
    public void testGetKnowledgeTypeByCodeWithInvalidCode() {
        // 测试无效编码
        Result<KnowledgeTypeDTO> result = knowledgeService.getKnowledgeTypeByCode("INVALID_CODE_12345");
        System.out.println("无效编码查询知识类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isSuccess());
    }

    @Test
    public void testGetKnowledgeByIdWithInvalidId() {
        // 测试无效ID
        Result<KnowledgeDTO> result = knowledgeService.getKnowledgeById(-1L);
        System.out.println("无效ID查询知识内容结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertFalse(result.isSuccess());
    }

    @Test
    public void testCreateKnowledgeTypeWithDuplicateCode() {
        // 先创建一个知识类型
        KnowledgeTypeDTO firstType = createTestKnowledgeType("DUPLICATE_TEST", "重复测试类型1");
        Result<KnowledgeTypeDTO> firstResult = knowledgeService.createKnowledgeType(firstType);
        Assert.assertTrue(firstResult.isSuccess());

        // 尝试创建相同编码的知识类型
        KnowledgeTypeDTO secondType = createTestKnowledgeType("DUPLICATE_TEST", "重复测试类型2");
        Result<KnowledgeTypeDTO> secondResult = knowledgeService.createKnowledgeType(secondType);
        System.out.println("重复编码创建知识类型结果：" + JSON.toJSONString(secondResult));
        Assert.assertNotNull(secondResult);
        Assert.assertFalse(secondResult.isSuccess());
    }
}
