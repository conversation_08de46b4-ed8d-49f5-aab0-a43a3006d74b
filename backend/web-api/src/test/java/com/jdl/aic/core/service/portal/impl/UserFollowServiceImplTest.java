package com.jdl.aic.core.service.portal.impl;

import com.alibaba.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.UserFollowDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.portal.client.UserFollowService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * UserFollowService 集成测试 - 参考FavoriteDataServiceImplTest结构
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class UserFollowServiceImplTest {

    @Resource
    private UserFollowService userFollowService;

    @Test
    public void testFollowUser() {
        // 创建测试用户关注
        FollowUserRequest request = new FollowUserRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setFollowedId(2000L + new Random().nextInt(1000)); // 确保不同的用户ID

        Result<UserFollowDTO> result = userFollowService.followUser(request);
        System.out.println("关注用户结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals(request.getUserId(), result.getData().getUserId());
        Assert.assertEquals(request.getFollowedId(), result.getData().getFollowedId());
    }

    @Test
    public void testUnfollowUser() {
        // 先关注一个用户
        FollowUserRequest followRequest = new FollowUserRequest();
        followRequest.setUserId(1000L + new Random().nextInt(1000));
        followRequest.setFollowedId(2000L + new Random().nextInt(1000));

        Result<UserFollowDTO> followResult = userFollowService.followUser(followRequest);
        Assert.assertTrue(followResult.isSuccess());

        // 取消关注用户
        UnfollowUserRequest unfollowRequest = new UnfollowUserRequest();
        unfollowRequest.setUserId(followRequest.getUserId());
        unfollowRequest.setFollowedId(followRequest.getFollowedId());

        Result<Void> unfollowResult = userFollowService.unfollowUser(unfollowRequest);
        System.out.println("取消关注用户结果：" + JSON.toJSONString(unfollowResult));
        Assert.assertNotNull(unfollowResult);
        Assert.assertTrue(unfollowResult.isSuccess());

        // 验证关注已取消
        CheckFollowStatusRequest checkRequest = new CheckFollowStatusRequest();
        checkRequest.setUserId(followRequest.getUserId());
        checkRequest.setFollowedId(followRequest.getFollowedId());

        Result<Boolean> checkResult = userFollowService.isUserFollowed(checkRequest);
        Assert.assertTrue(checkResult.isSuccess());
        Assert.assertFalse(checkResult.getData());
    }

    @Test
    public void testToggleFollowStatus() {
        Long userId = 1000L + new Random().nextInt(1000);
        Long followedId = 2000L + new Random().nextInt(1000);

        // 第一次切换（关注）
        Result<Boolean> result1 = userFollowService.toggleFollowStatus(userId, followedId);
        System.out.println("第一次切换关注状态结果：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertTrue(result1.isSuccess());
        Assert.assertTrue(result1.getData()); // 应该是true，表示已关注

        // 第二次切换（取消关注）
        Result<Boolean> result2 = userFollowService.toggleFollowStatus(userId, followedId);
        System.out.println("第二次切换关注状态结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertTrue(result2.isSuccess());
        Assert.assertFalse(result2.getData()); // 应该是false，表示已取消关注
    }

    @Test
    public void testIsUserFollowed() {
        // 先关注一个用户
        FollowUserRequest followRequest = new FollowUserRequest();
        followRequest.setUserId(1000L + new Random().nextInt(1000));
        followRequest.setFollowedId(2000L + new Random().nextInt(1000));

        Result<UserFollowDTO> followResult = userFollowService.followUser(followRequest);
        Assert.assertTrue(followResult.isSuccess());

        // 检查关注状态
        CheckFollowStatusRequest checkRequest = new CheckFollowStatusRequest();
        checkRequest.setUserId(followRequest.getUserId());
        checkRequest.setFollowedId(followRequest.getFollowedId());

        Result<Boolean> result = userFollowService.isUserFollowed(checkRequest);
        System.out.println("检查关注状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData());

        // 检查未关注的用户
        CheckFollowStatusRequest checkRequest2 = new CheckFollowStatusRequest();
        checkRequest2.setUserId(followRequest.getUserId());
        checkRequest2.setFollowedId(9999L); // 不存在的用户ID

        Result<Boolean> result2 = userFollowService.isUserFollowed(checkRequest2);
        Assert.assertTrue(result2.isSuccess());
        Assert.assertFalse(result2.getData());
    }

    @Test
    public void testGetUserFollowList() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个关注
        for (int i = 1; i <= 3; i++) {
            FollowUserRequest request = new FollowUserRequest();
            request.setUserId(userId);
            request.setFollowedId(2000L + i);
            userFollowService.followUser(request);
        }

        // 获取用户关注列表
        GetUserFollowListRequest request = new GetUserFollowListRequest(0, 10, userId);
        request.setFollowType("following");
        Result<PageResult<UserFollowDTO>> result = userFollowService.getUserFollowList(request);
        System.out.println("获取用户关注列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetUserFollowing() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个关注
        for (int i = 1; i <= 2; i++) {
            FollowUserRequest request = new FollowUserRequest();
            request.setUserId(userId);
            request.setFollowedId(2000L + i + 10);
            userFollowService.followUser(request);
        }

        // 获取用户关注的人列表
        Result<List<UserFollowDTO>> result = userFollowService.getUserFollowing(userId, 5);
        System.out.println("获取用户关注的人列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 2);
    }

    @Test
    public void testGetUserFollowers() {
        Long followedId = 2000L + new Random().nextInt(1000);
        
        // 为用户添加几个粉丝
        for (int i = 1; i <= 2; i++) {
            FollowUserRequest request = new FollowUserRequest();
            request.setUserId(1000L + i + 20);
            request.setFollowedId(followedId);
            userFollowService.followUser(request);
        }

        // 获取用户的粉丝列表
        Result<List<UserFollowDTO>> result = userFollowService.getUserFollowers(followedId, 10);
        System.out.println("获取用户的粉丝列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testCountUserFollowing() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个关注
        for (int i = 1; i <= 3; i++) {
            FollowUserRequest request = new FollowUserRequest();
            request.setUserId(userId);
            request.setFollowedId(2000L + i + 30);
            userFollowService.followUser(request);
        }

        // 统计用户关注的人数
        Result<Integer> result = userFollowService.countUserFollowing(userId);
        System.out.println("统计用户关注的人数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData() >= 3);
    }

    @Test
    public void testCountUserFollowers() {
        Long followedId = 2000L + new Random().nextInt(1000);
        
        // 为用户添加几个粉丝
        for (int i = 1; i <= 2; i++) {
            FollowUserRequest request = new FollowUserRequest();
            request.setUserId(1000L + i + 40);
            request.setFollowedId(followedId);
            userFollowService.followUser(request);
        }

        // 统计用户的粉丝数
        Result<Integer> result = userFollowService.countUserFollowers(followedId);
        System.out.println("统计用户的粉丝数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData() >= 2);
    }

    @Test
    public void testGetUserFollowStats() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加关注和粉丝
        for (int i = 1; i <= 2; i++) {
            // 关注别人
            FollowUserRequest followRequest = new FollowUserRequest();
            followRequest.setUserId(userId);
            followRequest.setFollowedId(2000L + i + 50);
            userFollowService.followUser(followRequest);
            
            // 被别人关注
            FollowUserRequest followerRequest = new FollowUserRequest();
            followerRequest.setUserId(1000L + i + 50);
            followerRequest.setFollowedId(userId);
            userFollowService.followUser(followerRequest);
        }

        // 获取用户关注统计信息
        Result<Object> result = userFollowService.getUserFollowStats(userId);
        System.out.println("获取用户关注统计信息结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testBatchFollowUsers() {
        Long userId = 1000L + new Random().nextInt(1000);
        List<Long> followedIds = Arrays.asList(2001L, 2002L, 2003L);

        // 批量关注用户
        Result<List<UserFollowDTO>> result = userFollowService.batchFollowUsers(userId, followedIds);
        System.out.println("批量关注用户结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testBatchUnfollowUsers() {
        Long userId = 1000L + new Random().nextInt(1000);
        List<Long> followedIds = Arrays.asList(2001L, 2002L);

        // 先批量关注用户
        userFollowService.batchFollowUsers(userId, followedIds);

        // 批量取消关注用户
        Result<Void> result = userFollowService.batchUnfollowUsers(userId, followedIds);
        System.out.println("批量取消关注用户结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testGetRecommendedUsers() {
        Long userId = 1000L + new Random().nextInt(1000);

        // 获取推荐用户
        Result<List<Object>> result = userFollowService.getRecommendedUsers(userId, 5);
        System.out.println("获取推荐用户结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetPopularUsers() {
        // 获取热门被关注用户
        Result<List<Object>> result = userFollowService.getPopularUsers(5, 30);
        System.out.println("获取热门被关注用户结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testInvalidParameters() {
        // 测试空用户ID
        FollowUserRequest request1 = new FollowUserRequest();
        request1.setUserId(null);
        request1.setFollowedId(2000L);
        
        Result<UserFollowDTO> result1 = userFollowService.followUser(request1);
        System.out.println("空用户ID测试结果：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertFalse(result1.isSuccess());
        
        // 测试空被关注人ID
        FollowUserRequest request2 = new FollowUserRequest();
        request2.setUserId(1000L);
        request2.setFollowedId(null);
        
        Result<UserFollowDTO> result2 = userFollowService.followUser(request2);
        System.out.println("空被关注人ID测试结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertFalse(result2.isSuccess());
        
        // 测试关注自己
        FollowUserRequest request3 = new FollowUserRequest();
        request3.setUserId(1000L);
        request3.setFollowedId(1000L);
        
        Result<UserFollowDTO> result3 = userFollowService.followUser(request3);
        System.out.println("关注自己测试结果：" + JSON.toJSONString(result3));
        Assert.assertNotNull(result3);
        Assert.assertFalse(result3.isSuccess());
    }

    @Test
    public void testDuplicateFollow() {
        // 测试重复关注
        FollowUserRequest request = new FollowUserRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setFollowedId(2000L + new Random().nextInt(1000));

        // 第一次关注
        Result<UserFollowDTO> result1 = userFollowService.followUser(request);
        Assert.assertTrue(result1.isSuccess());

        // 第二次关注（应该失败）
        Result<UserFollowDTO> result2 = userFollowService.followUser(request);
        System.out.println("重复关注测试结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertFalse(result2.isSuccess());
    }

    @Test
    public void testDataIntegrity() {
        // 创建用户关注并验证数据完整性
        FollowUserRequest request = new FollowUserRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setFollowedId(2000L + new Random().nextInt(1000));

        Result<UserFollowDTO> result = userFollowService.followUser(request);
        Assert.assertTrue(result.isSuccess());

        UserFollowDTO follow = result.getData();

        // 验证必要字段不为空
        Assert.assertNotNull(follow.getId());
        Assert.assertNotNull(follow.getUserId());
        Assert.assertNotNull(follow.getFollowedId());
        Assert.assertNotNull(follow.getCreatedAt());

        // 验证字段值正确
        Assert.assertEquals(request.getUserId(), follow.getUserId());
        Assert.assertEquals(request.getFollowedId(), follow.getFollowedId());
    }
}
