package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.learning.CategoryStatisticsDTO;
import com.jdl.aic.core.service.client.dto.learning.LearningCourseDTO;
import com.jdl.aic.core.service.client.dto.request.learning.GetLearningCourseListRequest;
import com.jdl.aic.core.service.client.dto.request.learning.SearchLearningCoursesRequest;
import com.jdl.aic.core.service.client.dto.request.learning.UpdateLearningCourseStatusRequest;
import com.jdl.aic.core.service.client.service.LearningCourseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 学习课程服务测试类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@TestPropertySource(locations = "classpath:application-test.yml")
@SpringBootTest
public class LearningCourseServiceTest {

    @Resource
    private LearningCourseService learningCourseService;

    @Test
    public void testCreateLearningCourse() {
        // 创建测试课程
        LearningCourseDTO newCourse = new LearningCourseDTO();
        newCourse.setName("Java基础编程课程");
        newCourse.setDescription("这是一个Java基础编程课程，适合初学者学习");
        newCourse.setCategory("编程语言");
        newCourse.setDifficultyLevel("BEGINNER");
        newCourse.setTotalHours(new BigDecimal("40.0"));
        newCourse.setPrerequisites("无特殊要求");
        newCourse.setLearningGoals("掌握Java基础语法和面向对象编程");
        newCourse.setCreatorId("test_user_001");
        newCourse.setCreatorName("测试用户");
        newCourse.setStatus("DRAFT");
        newCourse.setIsOfficial(false);
        newCourse.setTags("Java,编程,基础");

        Result<LearningCourseDTO> result = learningCourseService.createLearningCourse(newCourse);
        System.out.println("创建课程结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
    }

    @Test
    public void testGetLearningCourseById() {
        // 查询课程
        Result<LearningCourseDTO> result = learningCourseService.getLearningCourseById(1L);
        System.out.println("查询课程结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetLearningCourseList() {
        // 构建查询请求
        GetLearningCourseListRequest request = new GetLearningCourseListRequest();
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);
        request.setPageRequest(pageRequest);
        request.setStatus("PUBLISHED");

        Result<PageResult<LearningCourseDTO>> result = learningCourseService.getLearningCourseList(request);
        System.out.println("课程列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testUpdateLearningCourse() {
        // 先创建一个课程
        LearningCourseDTO newCourse = new LearningCourseDTO();
        newCourse.setName("机器学习入门");
        newCourse.setDescription("机器学习基础概念和算法");
        newCourse.setCategory("人工智能");
        newCourse.setDifficultyLevel("INTERMEDIATE");
        newCourse.setCreatorId("test_user_003");

        Result<LearningCourseDTO> createResult = learningCourseService.createLearningCourse(newCourse);
        Assert.assertTrue(createResult.isSuccess());
        Long courseId = createResult.getData().getId();

        // 更新课程信息
        LearningCourseDTO updateCourse = new LearningCourseDTO();
        updateCourse.setName("机器学习进阶");
        updateCourse.setDescription("深入学习机器学习算法和应用");
        updateCourse.setDifficultyLevel("ADVANCED");
        updateCourse.setTotalHours(new BigDecimal("60.0"));

        Result<LearningCourseDTO> result = learningCourseService.updateLearningCourse(courseId, updateCourse);
        System.out.println("更新课程结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("机器学习进阶", result.getData().getName());
        Assert.assertEquals("ADVANCED", result.getData().getDifficultyLevel());
    }

    @Test
    public void testUpdate() {
        String json = "{\"category\":\"\",\"coverImageUrl\":\"\",\"createdBy\":\"wangwangang\",\"description\":\"123\",\"difficultyLevel\":\"BEGINNER\",\"id\":6,\"isOfficial\":false,\"learningGoals\":\"123\",\"name\":\"测试课程111\",\"paths\":[{\"estimatedHours\":0.50,\"isOptional\":false,\"notes\":\"123\",\"resourceId\":1,\"sequenceOrder\":1,\"stageName\":\"123\"}],\"prerequisites\":\"123\",\"resourceCount\":0,\"status\":\"DRAFT\",\"tagList\":[],\"tags\":\"\",\"updatedAt\":\"2025-07-24T12:15:48.439\",\"updatedBy\":\"wangwangang\"}";
        LearningCourseDTO learningCourseDTO = JSON.parseObject(json, LearningCourseDTO.class);

        Result<LearningCourseDTO> createResult = learningCourseService.updateLearningCourse(6L, learningCourseDTO);
        Assert.assertTrue(createResult.isSuccess());
    }

    @Test
    public void testUpdateLearningCourseStatus() {
        // 先创建一个课程
        LearningCourseDTO newCourse = new LearningCourseDTO();
        newCourse.setName("Web前端开发");
        newCourse.setDescription("学习HTML、CSS、JavaScript");
        newCourse.setCategory("前端开发");
        newCourse.setCreatorId("test_user_004");

        Result<LearningCourseDTO> createResult = learningCourseService.createLearningCourse(newCourse);
        Assert.assertTrue(createResult.isSuccess());
        Long courseId = createResult.getData().getId();

        // 更新课程状态
        UpdateLearningCourseStatusRequest request = new UpdateLearningCourseStatusRequest();
        request.setId(courseId);
        request.setStatus("PUBLISHED");
        request.setUpdatedBy("admin");

        Result<Void> result = learningCourseService.updateLearningCourseStatus(request);
        System.out.println("更新状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testSearchLearningCourses() {
        // 构建搜索请求
        SearchLearningCoursesRequest request = new SearchLearningCoursesRequest();
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);
        request.setPageRequest(pageRequest);
        request.setKeyword("Java");
        request.setCategories(Arrays.asList("编程语言", "软件开发"));
        request.setDifficultyLevels(Arrays.asList("BEGINNER", "INTERMEDIATE"));

        Result<PageResult<LearningCourseDTO>> result = learningCourseService.searchLearningCourses(request);
        System.out.println("搜索课程结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetLearningCoursesByCategory() {
        String category = "编程语言";
        Integer limit = 5;

        Result<List<LearningCourseDTO>> result = learningCourseService.getLearningCoursesByCategory(category, limit);
        System.out.println("按分类查询结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetPopularLearningCourses() {
        Integer limit = 10;

        Result<List<LearningCourseDTO>> result = learningCourseService.getPopularLearningCourses(limit);
        System.out.println("热门课程结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetLatestLearningCourses() {
        Integer limit = 10;

        Result<List<LearningCourseDTO>> result = learningCourseService.getLatestLearningCourses(limit);
        System.out.println("最新课程结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testIncrementEnrolledCount() {
        // 先创建一个课程
        LearningCourseDTO newCourse = new LearningCourseDTO();
        newCourse.setName("数据库设计");
        newCourse.setDescription("学习数据库设计原理和实践");
        newCourse.setCreatorId("test_user_005");

        Result<LearningCourseDTO> createResult = learningCourseService.createLearningCourse(newCourse);
        Assert.assertTrue(createResult.isSuccess());
        Long courseId = createResult.getData().getId();

        // 增加报名人数
        Result<Void> result = learningCourseService.incrementEnrolledCount(courseId, "student_001");
        System.out.println("增加报名人数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testBatchUpdateStatus() {
        // 先创建几个课程
        LearningCourseDTO course1 = new LearningCourseDTO();
        course1.setName("课程1");
        course1.setCreatorId("test_user_006");
        Result<LearningCourseDTO> result1 = learningCourseService.createLearningCourse(course1);

        LearningCourseDTO course2 = new LearningCourseDTO();
        course2.setName("课程2");
        course2.setCreatorId("test_user_006");
        Result<LearningCourseDTO> result2 = learningCourseService.createLearningCourse(course2);

        Assert.assertTrue(result1.isSuccess());
        Assert.assertTrue(result2.isSuccess());

        // 批量更新状态
        List<Long> courseIds = Arrays.asList(result1.getData().getId(), result2.getData().getId());
        Result<Void> result = learningCourseService.batchUpdateStatus(courseIds, "PUBLISHED", "admin");
        System.out.println("批量更新状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testDeleteLearningCourse() {
        // 先创建一个课程
        LearningCourseDTO newCourse = new LearningCourseDTO();
        newCourse.setName("待删除课程");
        newCourse.setDescription("这是一个测试删除的课程");
        newCourse.setCreatorId("test_user_007");

        Result<LearningCourseDTO> createResult = learningCourseService.createLearningCourse(newCourse);
        Assert.assertTrue(createResult.isSuccess());
        Long courseId = createResult.getData().getId();

        // 删除课程
        Result<Void> result = learningCourseService.deleteLearningCourse(courseId);
        System.out.println("删除课程结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证课程已被删除
        Result<LearningCourseDTO> getResult = learningCourseService.getLearningCourseById(courseId);
        Assert.assertFalse(getResult.isSuccess());
    }


    @Test
    public void testGetCourseCategoryStatistics() {
        Result<List<CategoryStatisticsDTO>> result = learningCourseService.getCourseCategoryStatistics();
        System.out.println(JSON.toJSONString(result));
    }
}
