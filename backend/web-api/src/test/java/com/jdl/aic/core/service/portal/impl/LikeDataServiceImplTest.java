package com.jdl.aic.core.service.portal.impl;

import com.alibaba.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.LikeDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.portal.client.LikeDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Random;

/**
 * LikeDataService 集成测试 - 参考CategoryServiceTest结构
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class LikeDataServiceImplTest {

    @Resource
    private LikeDataService likeDataService;

    @Test
    public void testAddLike() {
        // 创建测试点赞
        AddLikeRequest request = new AddLikeRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0); // 知识类型
        request.setContentId(100L + new Random().nextInt(100));
        request.setRelatedKnowledgeTypeId(1L);

        Result<LikeDTO> result = likeDataService.addLike(request);
        System.out.println("添加点赞结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals(request.getUserId(), result.getData().getUserId());
        Assert.assertEquals(request.getContentType(), result.getData().getContentType());
        Assert.assertEquals(request.getContentId(), result.getData().getContentId());
    }

    @Test
    public void testRemoveLike() {
        // 先添加一个点赞
        AddLikeRequest addRequest = new AddLikeRequest();
        addRequest.setUserId(1000L + new Random().nextInt(1000));
        addRequest.setContentType(0);
        addRequest.setContentId(100L + new Random().nextInt(100));
        addRequest.setRelatedKnowledgeTypeId(1L);

        Result<LikeDTO> addResult = likeDataService.addLike(addRequest);
        Assert.assertTrue(addResult.isSuccess());

        // 取消点赞
        RemoveLikeRequest removeRequest = new RemoveLikeRequest();
        removeRequest.setUserId(addRequest.getUserId());
        removeRequest.setContentType(addRequest.getContentType());
        removeRequest.setContentId(addRequest.getContentId());

        Result<Void> result = likeDataService.removeLike(removeRequest);
        System.out.println("取消点赞结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testToggleLike() {
        // 测试切换点赞状态
        ToggleLikeRequest request = new ToggleLikeRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0);
        request.setContentId(100L + new Random().nextInt(100));
        request.setRelatedKnowledgeTypeId(1L);

        // 第一次切换（添加点赞）
        Result<Boolean> result1 = likeDataService.toggleLike(request);
        System.out.println("第一次切换点赞结果：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertTrue(result1.isSuccess());
        Assert.assertTrue(result1.getData()); // 应该返回true表示已点赞

        // 第二次切换（取消点赞）
        Result<Boolean> result2 = likeDataService.toggleLike(request);
        System.out.println("第二次切换点赞结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertTrue(result2.isSuccess());
        Assert.assertFalse(result2.getData()); // 应该返回false表示已取消点赞
    }

    @Test
    public void testIsLiked() {
        // 先添加一个点赞
        AddLikeRequest addRequest = new AddLikeRequest();
        addRequest.setUserId(1000L + new Random().nextInt(1000));
        addRequest.setContentType(0);
        addRequest.setContentId(100L + new Random().nextInt(100));
        addRequest.setRelatedKnowledgeTypeId(1L);

        Result<LikeDTO> addResult = likeDataService.addLike(addRequest);
        Assert.assertTrue(addResult.isSuccess());

        // 检查点赞状态
        CheckLikeRequest checkRequest = new CheckLikeRequest();
        checkRequest.setUserId(addRequest.getUserId());
        checkRequest.setContentType(addRequest.getContentType());
        checkRequest.setContentId(addRequest.getContentId());

        Result<Boolean> result = likeDataService.isLiked(checkRequest);
        System.out.println("检查点赞状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData()); // 应该返回true表示已点赞
    }

    @Test
    public void testGetUserLikes() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个点赞
        for (int i = 1; i <= 3; i++) {
            AddLikeRequest request = new AddLikeRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(100L + i);
            request.setRelatedKnowledgeTypeId(1L);
            likeDataService.addLike(request);
        }

        // 测试分页查询用户点赞列表
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetUserLikesRequest request = new GetUserLikesRequest();
        request.setUserId(userId);
        request.setPageRequest(pageRequest);
        request.setContentType(0); // 只查询知识类型的点赞

        Result<PageResult<LikeDTO>> result = likeDataService.getUserLikes(request);
        System.out.println("获取用户点赞列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().getPagination().getTotalElements() >= 3);
    }

    @Test
    public void testGetRecentLikes() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个点赞
        for (int i = 1; i <= 2; i++) {
            AddLikeRequest request = new AddLikeRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(200L + i);
            request.setRelatedKnowledgeTypeId(1L);
            likeDataService.addLike(request);
        }

        // 获取最近点赞
        GetUserLikesRequest request = new GetUserLikesRequest();
        request.setUserId(userId);
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(5);
        request.setPageRequest(pageRequest);

        Result<List<LikeDTO>> result = likeDataService.getRecentLikes(request);
        System.out.println("获取最近点赞结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 2);
    }

    @Test
    public void testGetContentLikeCount() {
        Integer contentType = 0;
        Long contentId = 300L + new Random().nextInt(100);
        
        // 让多个用户点赞同一个内容
        for (int i = 1; i <= 2; i++) {
            AddLikeRequest request = new AddLikeRequest();
            request.setUserId(1000L + i);
            request.setContentType(contentType);
            request.setContentId(contentId);
            request.setRelatedKnowledgeTypeId(1L);
            likeDataService.addLike(request);
        }

        // 统计内容点赞数
        GetContentLikeCountRequest request = new GetContentLikeCountRequest();
        request.setContentType(contentType);
        request.setContentId(contentId);

        Result<Integer> result = likeDataService.getContentLikeCount(request);
        System.out.println("获取内容点赞数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData() >= 2);
    }

    @Test
    public void testGetUserLikeCount() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个点赞
        for (int i = 1; i <= 2; i++) {
            AddLikeRequest request = new AddLikeRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(400L + i);
            request.setRelatedKnowledgeTypeId(1L);
            likeDataService.addLike(request);
        }

        // 统计用户点赞数
        GetUserLikeCountRequest request = new GetUserLikeCountRequest();
        request.setUserId(userId);

        Result<Integer> result = likeDataService.getUserLikeCount(request);
        System.out.println("获取用户点赞数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData() >= 2);
    }

    @Test
    public void testInvalidParameters() {
        // 测试空用户ID
        AddLikeRequest request1 = new AddLikeRequest();
        request1.setUserId(null);
        request1.setContentType(0);
        request1.setContentId(100L);
        
        Result<LikeDTO> result1 = likeDataService.addLike(request1);
        System.out.println("空用户ID测试结果：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertFalse(result1.isSuccess());
        
        // 测试空内容类型
        AddLikeRequest request2 = new AddLikeRequest();
        request2.setUserId(1000L);
        request2.setContentType(null);
        request2.setContentId(100L);
        
        Result<LikeDTO> result2 = likeDataService.addLike(request2);
        System.out.println("空内容类型测试结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertFalse(result2.isSuccess());
        
        // 测试空内容ID
        AddLikeRequest request3 = new AddLikeRequest();
        request3.setUserId(1000L);
        request3.setContentType(0);
        request3.setContentId(null);
        
        Result<LikeDTO> result3 = likeDataService.addLike(request3);
        System.out.println("空内容ID测试结果：" + JSON.toJSONString(result3));
        Assert.assertNotNull(result3);
        Assert.assertFalse(result3.isSuccess());
    }

    @Test
    public void testDuplicateLike() {
        // 测试重复点赞
        AddLikeRequest request = new AddLikeRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0);
        request.setContentId(500L + new Random().nextInt(100));
        request.setRelatedKnowledgeTypeId(1L);

        // 第一次添加点赞
        Result<LikeDTO> result1 = likeDataService.addLike(request);
        Assert.assertTrue(result1.isSuccess());

        // 第二次添加相同点赞
        Result<LikeDTO> result2 = likeDataService.addLike(request);
        System.out.println("重复点赞测试结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        // 根据业务逻辑，可能返回成功（幂等）或失败（重复点赞）
        if (!result2.isSuccess()) {
            Assert.assertTrue(result2.getCode().contains("ALREADY_LIKED") ||
                            result2.getCode().contains("DUPLICATE"));
        }
    }

    @Test
    public void testBatchOperations() {
        Long userId = 1000L + new Random().nextInt(1000);

        // 批量添加点赞
        for (int i = 1; i <= 5; i++) {
            AddLikeRequest request = new AddLikeRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(600L + i);
            request.setRelatedKnowledgeTypeId(1L);

            Result<LikeDTO> result = likeDataService.addLike(request);
            Assert.assertTrue(result.isSuccess());
        }

        // 测试分页查询性能
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(3);

        GetUserLikesRequest request = new GetUserLikesRequest();
        request.setUserId(userId);
        request.setPageRequest(pageRequest);

        long startTime = System.currentTimeMillis();
        Result<PageResult<LikeDTO>> result = likeDataService.getUserLikes(request);
        long endTime = System.currentTimeMillis();

        System.out.println("批量查询耗时：" + (endTime - startTime) + "ms");
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testRemoveNonExistentLike() {
        // 测试取消不存在的点赞
        RemoveLikeRequest request = new RemoveLikeRequest();
        request.setUserId(9999L);
        request.setContentType(0);
        request.setContentId(9999L);

        Result<Void> result = likeDataService.removeLike(request);
        System.out.println("取消不存在点赞结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        // 根据业务逻辑，可能返回成功（幂等）或失败（点赞不存在）
        if (!result.isSuccess()) {
            Assert.assertTrue(result.getCode().contains("NOT_FOUND") ||
                            result.getCode().contains("NOT_LIKED"));
        }
    }

    @Test
    public void testGetLikesByContent() {
        Integer contentType = 0;
        Long contentId = 700L + new Random().nextInt(100);

        // 让多个用户点赞同一个内容
        for (int i = 1; i <= 3; i++) {
            AddLikeRequest request = new AddLikeRequest();
            request.setUserId(2000L + i);
            request.setContentType(contentType);
            request.setContentId(contentId);
            request.setRelatedKnowledgeTypeId(1L);
            likeDataService.addLike(request);
        }

        // 查询点赞该内容的用户列表
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetLikesByContentRequest request = new GetLikesByContentRequest();
        request.setContentType(contentType);
        request.setContentId(contentId);
        request.setPageRequest(pageRequest);

        Result<PageResult<LikeDTO>> result = likeDataService.getLikesByContent(request);
        System.out.println("根据内容获取点赞列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testDataIntegrity() {
        // 创建点赞并验证数据完整性
        AddLikeRequest request = new AddLikeRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0);
        request.setContentId(800L + new Random().nextInt(100));
        request.setRelatedKnowledgeTypeId(1L);

        Result<LikeDTO> result = likeDataService.addLike(request);
        Assert.assertTrue(result.isSuccess());

        LikeDTO like = result.getData();

        // 验证必要字段不为空
        Assert.assertNotNull(like.getId());
        Assert.assertNotNull(like.getUserId());
        Assert.assertNotNull(like.getContentType());
        Assert.assertNotNull(like.getContentId());
        Assert.assertNotNull(like.getCreatedAt());

        // 验证数据一致性
        Assert.assertEquals(request.getUserId(), like.getUserId());
        Assert.assertEquals(request.getContentType(), like.getContentType());
        Assert.assertEquals(request.getContentId(), like.getContentId());
        Assert.assertEquals(request.getRelatedKnowledgeTypeId(), like.getRelatedKnowledgeTypeId());
    }

    @Test
    public void testConcurrentOperations() {
        // 测试并发点赞操作
        Long userId = 1000L + new Random().nextInt(1000);
        Integer contentType = 0;
        Long contentId = 900L + new Random().nextInt(100);

        // 模拟并发添加点赞
        AddLikeRequest request1 = new AddLikeRequest();
        request1.setUserId(userId);
        request1.setContentType(contentType);
        request1.setContentId(contentId);
        request1.setRelatedKnowledgeTypeId(1L);

        AddLikeRequest request2 = new AddLikeRequest();
        request2.setUserId(userId);
        request2.setContentType(contentType);
        request2.setContentId(contentId);
        request2.setRelatedKnowledgeTypeId(1L);

        // 同时执行两个相同的点赞操作
        Result<LikeDTO> result1 = likeDataService.addLike(request1);
        Result<LikeDTO> result2 = likeDataService.addLike(request2);

        System.out.println("并发操作结果1：" + JSON.toJSONString(result1));
        System.out.println("并发操作结果2：" + JSON.toJSONString(result2));

        // 至少有一个操作成功
        Assert.assertTrue(result1.isSuccess() || result2.isSuccess());

        // 验证最终状态
        CheckLikeRequest checkRequest = new CheckLikeRequest();
        checkRequest.setUserId(userId);
        checkRequest.setContentType(contentType);
        checkRequest.setContentId(contentId);

        Result<Boolean> checkResult = likeDataService.isLiked(checkRequest);
        Assert.assertTrue(checkResult.isSuccess());
        Assert.assertTrue(checkResult.getData());
    }

    @Test
    public void testDifferentContentTypes() {
        Long userId = 1000L + new Random().nextInt(1000);

        // 测试不同内容类型的点赞
        Integer[] contentTypes = {0, 1, 2, 3}; // 知识、资讯、评论、解决方案
        String[] typeNames = {"知识", "资讯", "评论", "解决方案"};

        for (int i = 0; i < contentTypes.length; i++) {
            AddLikeRequest request = new AddLikeRequest();
            request.setUserId(userId);
            request.setContentType(contentTypes[i]);
            request.setContentId(1000L + i);
            if (contentTypes[i] == 0) { // 只有知识类型需要关联知识类型ID
                request.setRelatedKnowledgeTypeId(1L);
            }

            Result<LikeDTO> result = likeDataService.addLike(request);
            System.out.println("点赞" + typeNames[i] + "结果：" + JSON.toJSONString(result));
            Assert.assertTrue(result.isSuccess());
            Assert.assertEquals(contentTypes[i], result.getData().getContentType());
        }

        // 验证用户总点赞数
        GetUserLikeCountRequest countRequest = new GetUserLikeCountRequest();
        countRequest.setUserId(userId);

        Result<Integer> countResult = likeDataService.getUserLikeCount(countRequest);
        Assert.assertTrue(countResult.isSuccess());
        Assert.assertTrue(countResult.getData() >= 4);
    }
}
