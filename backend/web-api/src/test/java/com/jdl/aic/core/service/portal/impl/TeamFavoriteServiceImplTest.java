package com.jdl.aic.core.service.portal.impl;

import com.alibaba.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.TeamFavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.portal.client.TeamFavoriteService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * TeamFavoriteService 集成测试 - 参考FavoriteDataServiceImplTest结构
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class TeamFavoriteServiceImplTest {

    @Resource
    private TeamFavoriteService teamFavoriteService;

    @Test
    public void testAddTeamFavorite() {
        // 创建测试团队收藏
        AddTeamFavoriteRequest request = new AddTeamFavoriteRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setTeamId(1L + new Random().nextInt(6)); // 假设有6个团队

        Result<TeamFavoriteDTO> result = teamFavoriteService.addTeamFavorite(request);
        System.out.println("添加团队收藏结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals(request.getUserId(), result.getData().getUserId());
        Assert.assertEquals(request.getTeamId(), result.getData().getTeamId());
    }

    @Test
    public void testRemoveTeamFavorite() {
        // 先添加一个团队收藏
        AddTeamFavoriteRequest addRequest = new AddTeamFavoriteRequest();
        addRequest.setUserId(1000L + new Random().nextInt(1000));
        addRequest.setTeamId(1L + new Random().nextInt(6));

        Result<TeamFavoriteDTO> addResult = teamFavoriteService.addTeamFavorite(addRequest);
        Assert.assertTrue(addResult.isSuccess());

        // 取消团队收藏
        RemoveTeamFavoriteRequest removeRequest = new RemoveTeamFavoriteRequest();
        removeRequest.setUserId(addRequest.getUserId());
        removeRequest.setTeamId(addRequest.getTeamId());

        Result<Void> removeResult = teamFavoriteService.removeTeamFavorite(removeRequest);
        System.out.println("取消团队收藏结果：" + JSON.toJSONString(removeResult));
        Assert.assertNotNull(removeResult);
        Assert.assertTrue(removeResult.isSuccess());

        // 验证收藏已取消
        CheckTeamFavoriteRequest checkRequest = new CheckTeamFavoriteRequest();
        checkRequest.setUserId(addRequest.getUserId());
        checkRequest.setTeamId(addRequest.getTeamId());

        Result<Boolean> checkResult = teamFavoriteService.isTeamFavorited(checkRequest);
        Assert.assertTrue(checkResult.isSuccess());
        Assert.assertFalse(checkResult.getData());
    }

    @Test
    public void testToggleTeamFavorite() {
        Long userId = 1000L + new Random().nextInt(1000);
        Long teamId = 1L + new Random().nextInt(6);

        // 第一次切换（添加收藏）
        Result<Boolean> result1 = teamFavoriteService.toggleTeamFavorite(userId, teamId);
        System.out.println("第一次切换团队收藏结果：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertTrue(result1.isSuccess());
        Assert.assertTrue(result1.getData()); // 应该是true，表示已收藏

        // 第二次切换（取消收藏）
        Result<Boolean> result2 = teamFavoriteService.toggleTeamFavorite(userId, teamId);
        System.out.println("第二次切换团队收藏结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertTrue(result2.isSuccess());
        Assert.assertFalse(result2.getData()); // 应该是false，表示已取消收藏
    }

    @Test
    public void testIsTeamFavorited() {
        // 先添加一个团队收藏
        AddTeamFavoriteRequest addRequest = new AddTeamFavoriteRequest();
        addRequest.setUserId(1000L + new Random().nextInt(1000));
        addRequest.setTeamId(1L + new Random().nextInt(6));

        Result<TeamFavoriteDTO> addResult = teamFavoriteService.addTeamFavorite(addRequest);
        Assert.assertTrue(addResult.isSuccess());

        // 检查收藏状态
        CheckTeamFavoriteRequest checkRequest = new CheckTeamFavoriteRequest();
        checkRequest.setUserId(addRequest.getUserId());
        checkRequest.setTeamId(addRequest.getTeamId());

        Result<Boolean> result = teamFavoriteService.isTeamFavorited(checkRequest);
        System.out.println("检查团队收藏状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData());

        // 检查未收藏的团队
        CheckTeamFavoriteRequest checkRequest2 = new CheckTeamFavoriteRequest();
        checkRequest2.setUserId(addRequest.getUserId());
        checkRequest2.setTeamId(999L); // 不存在的团队ID

        Result<Boolean> result2 = teamFavoriteService.isTeamFavorited(checkRequest2);
        Assert.assertTrue(result2.isSuccess());
        Assert.assertFalse(result2.getData());
    }

    @Test
    public void testGetUserTeamFavorites() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个团队收藏
        for (int i = 1; i <= 3; i++) {
            AddTeamFavoriteRequest request = new AddTeamFavoriteRequest();
            request.setUserId(userId);
            request.setTeamId((long) i);
            teamFavoriteService.addTeamFavorite(request);
        }

        // 获取用户团队收藏列表
        GetUserTeamFavoritesRequest request = new GetUserTeamFavoritesRequest(0, 10, userId);
        Result<PageResult<TeamFavoriteDTO>> result = teamFavoriteService.getUserTeamFavorites(request);
        System.out.println("获取用户团队收藏列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetRecentTeamFavorites() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个团队收藏
        for (int i = 1; i <= 2; i++) {
            AddTeamFavoriteRequest request = new AddTeamFavoriteRequest();
            request.setUserId(userId);
            request.setTeamId((long) (i + 10));
            teamFavoriteService.addTeamFavorite(request);
        }

        // 获取最近团队收藏
        Result<List<TeamFavoriteDTO>> result = teamFavoriteService.getRecentTeamFavorites(userId, 5);
        System.out.println("获取最近团队收藏结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 2);
    }

    @Test
    public void testGetTeamFavoriteUsers() {
        Long teamId = 1L;
        
        // 为团队添加几个收藏用户
        for (int i = 1; i <= 2; i++) {
            AddTeamFavoriteRequest request = new AddTeamFavoriteRequest();
            request.setUserId(2000L + i);
            request.setTeamId(teamId);
            teamFavoriteService.addTeamFavorite(request);
        }

        // 获取团队的收藏用户列表
        Result<List<TeamFavoriteDTO>> result = teamFavoriteService.getTeamFavoriteUsers(teamId, 10);
        System.out.println("获取团队的收藏用户列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testCountUserTeamFavorites() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个团队收藏
        for (int i = 1; i <= 3; i++) {
            AddTeamFavoriteRequest request = new AddTeamFavoriteRequest();
            request.setUserId(userId);
            request.setTeamId((long) (i + 20));
            teamFavoriteService.addTeamFavorite(request);
        }

        // 统计用户收藏的团队数量
        Result<Integer> result = teamFavoriteService.countUserTeamFavorites(userId);
        System.out.println("统计用户收藏的团队数量结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData() >= 3);
    }

    @Test
    public void testCountTeamFavorites() {
        Long teamId = 2L;
        
        // 为团队添加几个收藏用户
        for (int i = 1; i <= 2; i++) {
            AddTeamFavoriteRequest request = new AddTeamFavoriteRequest();
            request.setUserId(3000L + i);
            request.setTeamId(teamId);
            teamFavoriteService.addTeamFavorite(request);
        }

        // 统计团队被收藏的次数
        Result<Integer> result = teamFavoriteService.countTeamFavorites(teamId);
        System.out.println("统计团队被收藏的次数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData() >= 2);
    }

    @Test
    public void testBatchAddTeamFavorites() {
        Long userId = 1000L + new Random().nextInt(1000);
        List<Long> teamIds = Arrays.asList(1L, 2L, 3L);

        // 批量添加团队收藏
        Result<List<TeamFavoriteDTO>> result = teamFavoriteService.batchAddTeamFavorites(userId, teamIds);
        System.out.println("批量添加团队收藏结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testBatchRemoveTeamFavorites() {
        Long userId = 1000L + new Random().nextInt(1000);
        List<Long> teamIds = Arrays.asList(1L, 2L);

        // 先批量添加团队收藏
        teamFavoriteService.batchAddTeamFavorites(userId, teamIds);

        // 批量取消团队收藏
        Result<Void> result = teamFavoriteService.batchRemoveTeamFavorites(userId, teamIds);
        System.out.println("批量取消团队收藏结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testGetPopularTeams() {
        // 获取热门收藏团队列表
        Result<List<Object>> result = teamFavoriteService.getPopularTeams(5, 30);
        System.out.println("获取热门收藏团队列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testInvalidParameters() {
        // 测试空用户ID
        AddTeamFavoriteRequest request1 = new AddTeamFavoriteRequest();
        request1.setUserId(null);
        request1.setTeamId(1L);
        
        Result<TeamFavoriteDTO> result1 = teamFavoriteService.addTeamFavorite(request1);
        System.out.println("空用户ID测试结果：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertFalse(result1.isSuccess());
        
        // 测试空团队ID
        AddTeamFavoriteRequest request2 = new AddTeamFavoriteRequest();
        request2.setUserId(1000L);
        request2.setTeamId(null);
        
        Result<TeamFavoriteDTO> result2 = teamFavoriteService.addTeamFavorite(request2);
        System.out.println("空团队ID测试结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertFalse(result2.isSuccess());
    }

    @Test
    public void testDuplicateAdd() {
        // 测试重复添加收藏
        AddTeamFavoriteRequest request = new AddTeamFavoriteRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setTeamId(1L);

        // 第一次添加
        Result<TeamFavoriteDTO> result1 = teamFavoriteService.addTeamFavorite(request);
        Assert.assertTrue(result1.isSuccess());

        // 第二次添加（应该失败）
        Result<TeamFavoriteDTO> result2 = teamFavoriteService.addTeamFavorite(request);
        System.out.println("重复添加收藏测试结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertFalse(result2.isSuccess());
    }

    @Test
    public void testDataIntegrity() {
        // 创建团队收藏并验证数据完整性
        AddTeamFavoriteRequest request = new AddTeamFavoriteRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setTeamId(1L + new Random().nextInt(6));

        Result<TeamFavoriteDTO> result = teamFavoriteService.addTeamFavorite(request);
        Assert.assertTrue(result.isSuccess());

        TeamFavoriteDTO favorite = result.getData();

        // 验证必要字段不为空
        Assert.assertNotNull(favorite.getId());
        Assert.assertNotNull(favorite.getUserId());
        Assert.assertNotNull(favorite.getTeamId());
        Assert.assertNotNull(favorite.getCreatedAt());

        // 验证字段值正确
        Assert.assertEquals(request.getUserId(), favorite.getUserId());
        Assert.assertEquals(request.getTeamId(), favorite.getTeamId());
    }
}
