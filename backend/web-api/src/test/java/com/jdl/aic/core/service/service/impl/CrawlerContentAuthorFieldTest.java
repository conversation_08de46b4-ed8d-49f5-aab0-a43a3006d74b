package com.jdl.aic.core.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.CrawlerContentDTO;
import com.jdl.aic.core.service.client.service.CrawlerContentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * CrawlerContent Author 字段测试
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class CrawlerContentAuthorFieldTest {

    @Resource
    private CrawlerContentService crawlerContentService;

    /**
     * 测试创建包含 author 字段的爬虫内容
     */
    @Test
    public void testCreateCrawlerContentWithAuthor() {
        log.info("开始测试创建包含 author 字段的爬虫内容");
        
        // 准备测试数据
        CrawlerContentDTO contentDTO = new CrawlerContentDTO();
        contentDTO.setTitle("测试文章标题 - Author字段测试");
        contentDTO.setLink("https://example.com/test-author-field-" + System.currentTimeMillis());
        contentDTO.setLanguage("zh-CN");
        contentDTO.setAuthor("{\"name\":\"张三\",\"url\":\"https://example.com/author/zhangsan\",\"email\":\"<EMAIL>\"}");
        contentDTO.setDescription("这是一个测试 author 字段的文章描述");
        contentDTO.setContent("这是测试内容，用于验证 author 字段是否正常工作。");
        contentDTO.setContentType("article");
        contentDTO.setContentMd5("test-author-md5-" + System.currentTimeMillis());
        contentDTO.setPubDate(LocalDateTime.now());
        contentDTO.setStatus(1);
        contentDTO.setIsFeatured(false);
        
        // 执行创建操作
        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(contentDTO);
        
        // 验证创建结果
        Assert.assertTrue("创建爬虫内容应该成功", createResult.isSuccess());
        Assert.assertNotNull("创建结果不应为空", createResult.getData());
        
        CrawlerContentDTO createdContent = createResult.getData();
        Assert.assertNotNull("创建的内容ID不应为空", createdContent.getId());
        Assert.assertEquals("标题应该匹配", contentDTO.getTitle(), createdContent.getTitle());
        Assert.assertEquals("作者信息应该匹配", contentDTO.getAuthor(), createdContent.getAuthor());
        
        log.info("创建成功 - ID: {}, 标题: {}, 作者: {}", 
            createdContent.getId(), createdContent.getTitle(), createdContent.getAuthor());
        
        // 通过ID查询验证
        Result<CrawlerContentDTO> queryResult = crawlerContentService.getCrawlerContentById(createdContent.getId());
        log.info("结果"+ JSON.toJSONString(queryResult.getData()));
        Assert.assertTrue("查询爬虫内容应该成功", queryResult.isSuccess());
        
        CrawlerContentDTO queriedContent = queryResult.getData();
        Assert.assertEquals("查询的作者信息应该匹配", contentDTO.getAuthor(), queriedContent.getAuthor());
        
        log.info("查询验证成功 - 作者信息: {}", queriedContent.getAuthor());
    }

    /**
     * 测试更新 author 字段
     */
    @Test
    public void testUpdateCrawlerContentAuthor() {
        log.info("开始测试更新 author 字段");
        
        // 先创建一个内容
        CrawlerContentDTO contentDTO = new CrawlerContentDTO();
        contentDTO.setTitle("测试更新 Author 字段");
        contentDTO.setLink("https://example.com/test-update-author-" + System.currentTimeMillis());
        contentDTO.setLanguage("zh-CN");
        contentDTO.setAuthor("{\"name\":\"原作者\",\"url\":\"https://example.com/author/original\"}");
        contentDTO.setDescription("测试更新 author 字段的文章");
        contentDTO.setContent("测试内容");
        contentDTO.setContentType("article");
        contentDTO.setContentMd5("test-update-author-md5-" + System.currentTimeMillis());
        contentDTO.setPubDate(LocalDateTime.now());
        contentDTO.setStatus(1);
        contentDTO.setIsFeatured(false);
        
        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(contentDTO);
        Assert.assertTrue("创建应该成功", createResult.isSuccess());
        
        Long contentId = createResult.getData().getId();
        log.info("创建成功，ID: {}, 原作者: {}", contentId, createResult.getData().getAuthor());
        
        // 更新 author 字段
        CrawlerContentDTO updateDTO = new CrawlerContentDTO();
        updateDTO.setTitle("测试更新 Author 字段 - 已更新");
        updateDTO.setAuthor("{\"name\":\"新作者\",\"url\":\"https://example.com/author/new\",\"email\":\"<EMAIL>\"}");
        
        Result<CrawlerContentDTO> updateResult = crawlerContentService.updateCrawlerContent(contentId, updateDTO);
        Assert.assertTrue("更新应该成功", updateResult.isSuccess());
        
        CrawlerContentDTO updatedContent = updateResult.getData();
        Assert.assertEquals("更新后的作者信息应该匹配", updateDTO.getAuthor(), updatedContent.getAuthor());
        
        log.info("更新成功 - 新作者: {}", updatedContent.getAuthor());
    }

    /**
     * 测试 author 字段为空的情况
     */
    @Test
    public void testCrawlerContentWithNullAuthor() {
        log.info("开始测试 author 字段为空的情况");
        
        CrawlerContentDTO contentDTO = new CrawlerContentDTO();
        contentDTO.setTitle("测试空 Author 字段");
        contentDTO.setLink("https://example.com/test-null-author-" + System.currentTimeMillis());
        contentDTO.setLanguage("zh-CN");
        contentDTO.setAuthor(null); // 设置为空
        contentDTO.setDescription("测试 author 字段为空的情况");
        contentDTO.setContent("测试内容");
        contentDTO.setContentType("article");
        contentDTO.setContentMd5("test-null-author-md5-" + System.currentTimeMillis());
        contentDTO.setPubDate(LocalDateTime.now());
        contentDTO.setStatus(1);
        contentDTO.setIsFeatured(false);
        
        Result<CrawlerContentDTO> createResult = crawlerContentService.createCrawlerContent(contentDTO);
        Assert.assertTrue("创建应该成功", createResult.isSuccess());
        
        CrawlerContentDTO createdContent = createResult.getData();
        Assert.assertNull("作者字段应该为空", createdContent.getAuthor());
        
        log.info("空 author 字段测试成功 - ID: {}", createdContent.getId());
    }
}
