package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.newsfeed.NewsFeedDTO;
import com.jdl.aic.core.service.client.dto.request.newsfeed.GetNewsFeedListRequest;
import com.jdl.aic.core.service.client.dto.request.newsfeed.UpdateNewsFeedStatusRequest;
import com.jdl.aic.core.service.client.dto.request.newsfeed.BatchUpdateNewsFeedStatusRequest;
import com.jdl.aic.core.service.client.service.NewsFeedService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 资讯管理服务测试类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class NewsFeedServiceTest {

    @Resource
    private NewsFeedService newsFeedService;

    @Test
    public void testCreateNewsFeed() {
        // 创建测试资讯
        NewsFeedDTO newNewsFeed = new NewsFeedDTO();
        newNewsFeed.setTitle("测试资讯标题");
        newNewsFeed.setAuthor("测试作者");
        newNewsFeed.setSourceUrl("https://example.com/test-news-" + System.currentTimeMillis());
        newNewsFeed.setPublishedAt(LocalDateTime.now());
        newNewsFeed.setContentSummary("这是一个测试资讯的摘要内容");
        newNewsFeed.setContentHtml("<p>这是测试资讯的HTML内容</p>");
        newNewsFeed.setCoverImageUrl("https://example.com/test-image.jpg");
        newNewsFeed.setSourceName("测试来源");
        newNewsFeed.setType(0); // 采集类型
        newNewsFeed.setStatus(0); // 待审核状态

        Result<NewsFeedDTO> result = newsFeedService.createNewsFeed(newNewsFeed);
        System.out.println("创建资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
    }

    @Test
    public void testGetNewsFeedById() {
        // 根据ID查询资讯
        Result<NewsFeedDTO> result = newsFeedService.getNewsFeedById(1L);
        System.out.println("查询资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateNewsFeed() {
        // 先创建一个资讯
        NewsFeedDTO newNewsFeed = new NewsFeedDTO();
        newNewsFeed.setTitle("更新测试资讯");
        newNewsFeed.setAuthor("测试作者");
        newNewsFeed.setSourceUrl("https://example.com/update-test-" + System.currentTimeMillis());
        newNewsFeed.setPublishedAt(LocalDateTime.now());
        newNewsFeed.setContentSummary("更新测试资讯摘要");
        newNewsFeed.setType(0);
        newNewsFeed.setStatus(0);

        Result<NewsFeedDTO> createResult = newsFeedService.createNewsFeed(newNewsFeed);
        Assert.assertTrue(createResult.isSuccess());
        Long newsFeedId = createResult.getData().getId();

        // 更新资讯信息
        NewsFeedDTO updateNewsFeed = new NewsFeedDTO();
        updateNewsFeed.setTitle("更新后的资讯标题");
        updateNewsFeed.setAuthor("更新后的作者");
        updateNewsFeed.setContentSummary("更新后的资讯摘要");
        updateNewsFeed.setStatus(1); // 更新为已发布状态

        Result<NewsFeedDTO> result = newsFeedService.updateNewsFeed(newsFeedId, updateNewsFeed);
        System.out.println("更新资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("更新后的资讯标题", result.getData().getTitle());
        Assert.assertEquals("更新后的作者", result.getData().getAuthor());
    }

    @Test
    public void testGetNewsFeedList() {
        // 测试分页查询资讯列表
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetNewsFeedListRequest request = new GetNewsFeedListRequest();
        request.setStatus(1); // 查询已发布的资讯

        Result<PageResult<NewsFeedDTO>> result = newsFeedService.getNewsFeedList(pageRequest, request);
        System.out.println("查询资讯列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testSearchNewsFeed() {
        // 测试搜索功能
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetNewsFeedListRequest request = new GetNewsFeedListRequest();
        request.setSearch("测试"); // 搜索包含"测试"的资讯

        Result<PageResult<NewsFeedDTO>> result = newsFeedService.getNewsFeedList(pageRequest, request);
        System.out.println("搜索资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateNewsFeedStatus() {
        // 先创建一个资讯
        NewsFeedDTO newNewsFeed = new NewsFeedDTO();
        newNewsFeed.setTitle("状态测试资讯");
        newNewsFeed.setAuthor("测试作者");
        newNewsFeed.setSourceUrl("https://example.com/status-test-" + System.currentTimeMillis());
        newNewsFeed.setPublishedAt(LocalDateTime.now());
        newNewsFeed.setContentSummary("状态测试资讯摘要");
        newNewsFeed.setType(0);
        newNewsFeed.setStatus(0); // 待审核状态

        Result<NewsFeedDTO> createResult = newsFeedService.createNewsFeed(newNewsFeed);
        Assert.assertTrue(createResult.isSuccess());
        Long newsFeedId = createResult.getData().getId();

        // 更新资讯状态为已发布
        UpdateNewsFeedStatusRequest request = new UpdateNewsFeedStatusRequest();
        request.setId(newsFeedId);
        request.setStatus(1); // 已发布状态

        Result<Void> result = newsFeedService.updateNewsFeedStatus(request);
        System.out.println("更新资讯状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态更新成功
        Result<NewsFeedDTO> getResult = newsFeedService.getNewsFeedById(newsFeedId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(1), getResult.getData().getStatus());
    }

    @Test
    public void testBatchUpdateNewsFeedStatus() {
        // 先创建两个资讯
        NewsFeedDTO newsFeed1 = new NewsFeedDTO();
        newsFeed1.setTitle("批量状态测试资讯1");
        newsFeed1.setAuthor("测试作者");
        newsFeed1.setSourceUrl("https://example.com/batch-test-1-" + System.currentTimeMillis());
        newsFeed1.setPublishedAt(LocalDateTime.now());
        newsFeed1.setContentSummary("批量状态测试资讯1摘要");
        newsFeed1.setType(0);
        newsFeed1.setStatus(0);

        NewsFeedDTO newsFeed2 = new NewsFeedDTO();
        newsFeed2.setTitle("批量状态测试资讯2");
        newsFeed2.setAuthor("测试作者");
        newsFeed2.setSourceUrl("https://example.com/batch-test-2-" + System.currentTimeMillis());
        newsFeed2.setPublishedAt(LocalDateTime.now());
        newsFeed2.setContentSummary("批量状态测试资讯2摘要");
        newsFeed2.setType(0);
        newsFeed2.setStatus(0);

        Result<NewsFeedDTO> createResult1 = newsFeedService.createNewsFeed(newsFeed1);
        Result<NewsFeedDTO> createResult2 = newsFeedService.createNewsFeed(newsFeed2);
        Assert.assertTrue(createResult1.isSuccess());
        Assert.assertTrue(createResult2.isSuccess());

        List<Long> ids = Arrays.asList(createResult1.getData().getId(), createResult2.getData().getId());

        // 批量更新状态为已发布
        BatchUpdateNewsFeedStatusRequest request = new BatchUpdateNewsFeedStatusRequest();
        request.setIds(ids);
        request.setStatus(1); // 已发布状态

        Result<Void> result = newsFeedService.batchUpdateNewsFeedStatus(request);
        System.out.println("批量更新资讯状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testPublishNewsFeed() {
        // 先创建一个资讯
        NewsFeedDTO newNewsFeed = new NewsFeedDTO();
        newNewsFeed.setTitle("发布测试资讯");
        newNewsFeed.setAuthor("测试作者");
        newNewsFeed.setSourceUrl("https://example.com/publish-test-" + System.currentTimeMillis());
        newNewsFeed.setPublishedAt(LocalDateTime.now());
        newNewsFeed.setContentSummary("发布测试资讯摘要");
        newNewsFeed.setType(0);
        newNewsFeed.setStatus(0); // 待审核状态

        Result<NewsFeedDTO> createResult = newsFeedService.createNewsFeed(newNewsFeed);
        Assert.assertTrue(createResult.isSuccess());
        Long newsFeedId = createResult.getData().getId();

        // 发布资讯
        Result<Void> result = newsFeedService.publishNewsFeed(newsFeedId);
        System.out.println("发布资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证发布成功
        Result<NewsFeedDTO> getResult = newsFeedService.getNewsFeedById(newsFeedId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(1), getResult.getData().getStatus());
    }

    @Test
    public void testOfflineNewsFeed() {
        // 先创建并发布一个资讯
        NewsFeedDTO newNewsFeed = new NewsFeedDTO();
        newNewsFeed.setTitle("下线测试资讯");
        newNewsFeed.setAuthor("测试作者");
        newNewsFeed.setSourceUrl("https://example.com/offline-test-" + System.currentTimeMillis());
        newNewsFeed.setPublishedAt(LocalDateTime.now());
        newNewsFeed.setContentSummary("下线测试资讯摘要");
        newNewsFeed.setType(0);
        newNewsFeed.setStatus(1); // 已发布状态

        Result<NewsFeedDTO> createResult = newsFeedService.createNewsFeed(newNewsFeed);
        Assert.assertTrue(createResult.isSuccess());
        Long newsFeedId = createResult.getData().getId();

        // 下线资讯
        Result<Void> result = newsFeedService.offlineNewsFeed(newsFeedId);
        System.out.println("下线资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证下线成功
        Result<NewsFeedDTO> getResult = newsFeedService.getNewsFeedById(newsFeedId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(2), getResult.getData().getStatus());
    }

    @Test
    public void testDeleteNewsFeed() {
        // 先创建一个资讯
        NewsFeedDTO newNewsFeed = new NewsFeedDTO();
        newNewsFeed.setTitle("删除测试资讯");
        newNewsFeed.setAuthor("测试作者");
        newNewsFeed.setSourceUrl("https://example.com/delete-test-" + System.currentTimeMillis());
        newNewsFeed.setPublishedAt(LocalDateTime.now());
        newNewsFeed.setContentSummary("删除测试资讯摘要");
        newNewsFeed.setType(0);
        newNewsFeed.setStatus(0);

        Result<NewsFeedDTO> createResult = newsFeedService.createNewsFeed(newNewsFeed);
        Assert.assertTrue(createResult.isSuccess());
        Long newsFeedId = createResult.getData().getId();

        // 删除资讯
        Result<Void> result = newsFeedService.deleteNewsFeed(newsFeedId);
        System.out.println("删除资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证删除成功（应该查询不到）
        Result<NewsFeedDTO> getResult = newsFeedService.getNewsFeedById(newsFeedId);
        Assert.assertFalse(getResult.isSuccess());
        Assert.assertEquals("NEWS_FEED_NOT_FOUND", getResult.getCode());
    }

    @Test
    public void testBatchDeleteNewsFeed() {
        // 先创建两个资讯
        NewsFeedDTO newsFeed1 = new NewsFeedDTO();
        newsFeed1.setTitle("批量删除测试资讯1");
        newsFeed1.setAuthor("测试作者");
        newsFeed1.setSourceUrl("https://example.com/batch-delete-1-" + System.currentTimeMillis());
        newsFeed1.setPublishedAt(LocalDateTime.now());
        newsFeed1.setContentSummary("批量删除测试资讯1摘要");
        newsFeed1.setType(0);
        newsFeed1.setStatus(0);

        NewsFeedDTO newsFeed2 = new NewsFeedDTO();
        newsFeed2.setTitle("批量删除测试资讯2");
        newsFeed2.setAuthor("测试作者");
        newsFeed2.setSourceUrl("https://example.com/batch-delete-2-" + System.currentTimeMillis());
        newsFeed2.setPublishedAt(LocalDateTime.now());
        newsFeed2.setContentSummary("批量删除测试资讯2摘要");
        newsFeed2.setType(0);
        newsFeed2.setStatus(0);

        Result<NewsFeedDTO> createResult1 = newsFeedService.createNewsFeed(newsFeed1);
        Result<NewsFeedDTO> createResult2 = newsFeedService.createNewsFeed(newsFeed2);
        Assert.assertTrue(createResult1.isSuccess());
        Assert.assertTrue(createResult2.isSuccess());

        List<Long> ids = Arrays.asList(createResult1.getData().getId(), createResult2.getData().getId());

        // 批量删除资讯
        Result<Void> result = newsFeedService.batchDeleteNewsFeed(ids);
        System.out.println("批量删除资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testUpdateAiReviewStatus() {
        // 先创建一个资讯
        NewsFeedDTO newNewsFeed = new NewsFeedDTO();
        newNewsFeed.setTitle("AI审核测试资讯");
        newNewsFeed.setAuthor("测试作者");
        newNewsFeed.setSourceUrl("https://example.com/ai-review-test-" + System.currentTimeMillis());
        newNewsFeed.setPublishedAt(LocalDateTime.now());
        newNewsFeed.setContentSummary("AI审核测试资讯摘要");
        newNewsFeed.setType(0);
        newNewsFeed.setStatus(0);

        Result<NewsFeedDTO> createResult = newsFeedService.createNewsFeed(newNewsFeed);
        Assert.assertTrue(createResult.isSuccess());
        Long newsFeedId = createResult.getData().getId();

        // 更新AI审核状态为通过
        Result<Void> result = newsFeedService.updateAiReviewStatus(newsFeedId, 1);
        System.out.println("更新AI审核状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证AI审核状态更新成功
        Result<NewsFeedDTO> getResult = newsFeedService.getNewsFeedById(newsFeedId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(1), getResult.getData().getAiReviewStatus());
    }

    @Test
    public void testGetPendingAiReviewNewsFeed() {
        // 测试获取待AI审核的资讯列表
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        Result<PageResult<NewsFeedDTO>> result = newsFeedService.getPendingAiReviewNewsFeed(pageRequest);
        System.out.println("获取待AI审核资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getRecords());
    }

    @Test
    public void testGetNewsFeedStatistics() {
        // 测试获取资讯统计信息
        Result<NewsFeedService.NewsFeedStatistics> result = newsFeedService.getNewsFeedStatistics();
        System.out.println("获取资讯统计信息结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getTotalCount());
    }

    @Test
    public void testGetLatestNewsFeed() {
        // 测试获取最新资讯列表
        Result<List<NewsFeedDTO>> result = newsFeedService.getLatestNewsFeed(5);
        System.out.println("获取最新资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetPopularNewsFeed() {
        // 测试获取热门资讯列表
        Result<List<NewsFeedDTO>> result = newsFeedService.getPopularNewsFeed(5);
        System.out.println("获取热门资讯结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testCreateNewsFeedWithInvalidData() {
        // 测试创建资讯时的数据验证

        // 测试空标题
        NewsFeedDTO newsFeedWithEmptyTitle = new NewsFeedDTO();
        newsFeedWithEmptyTitle.setTitle("");
        newsFeedWithEmptyTitle.setSourceUrl("https://example.com/test");
        newsFeedWithEmptyTitle.setPublishedAt(LocalDateTime.now());

        Result<NewsFeedDTO> result1 = newsFeedService.createNewsFeed(newsFeedWithEmptyTitle);
        Assert.assertFalse(result1.isSuccess());
        Assert.assertEquals("INVALID_PARAMETER", result1.getCode());

        // 测试空原文链接
        NewsFeedDTO newsFeedWithEmptyUrl = new NewsFeedDTO();
        newsFeedWithEmptyUrl.setTitle("测试标题");
        newsFeedWithEmptyUrl.setSourceUrl("");
        newsFeedWithEmptyUrl.setPublishedAt(LocalDateTime.now());

        Result<NewsFeedDTO> result2 = newsFeedService.createNewsFeed(newsFeedWithEmptyUrl);
        Assert.assertFalse(result2.isSuccess());
        Assert.assertEquals("INVALID_PARAMETER", result2.getCode());

        // 测试空发布时间
        NewsFeedDTO newsFeedWithEmptyTime = new NewsFeedDTO();
        newsFeedWithEmptyTime.setTitle("测试标题");
        newsFeedWithEmptyTime.setSourceUrl("https://example.com/test");
        newsFeedWithEmptyTime.setPublishedAt(null);

        Result<NewsFeedDTO> result3 = newsFeedService.createNewsFeed(newsFeedWithEmptyTime);
        Assert.assertFalse(result3.isSuccess());
        Assert.assertEquals("INVALID_PARAMETER", result3.getCode());
    }

    @Test
    public void testCreateDuplicateNewsFeed() {
        // 测试创建重复资讯（相同原文链接）
        String duplicateUrl = "https://example.com/duplicate-test-" + System.currentTimeMillis();

        NewsFeedDTO newsFeed1 = new NewsFeedDTO();
        newsFeed1.setTitle("重复测试资讯1");
        newsFeed1.setAuthor("测试作者");
        newsFeed1.setSourceUrl(duplicateUrl);
        newsFeed1.setPublishedAt(LocalDateTime.now());
        newsFeed1.setContentSummary("重复测试资讯1摘要");
        newsFeed1.setType(0);
        newsFeed1.setStatus(0);

        // 第一次创建应该成功
        Result<NewsFeedDTO> result1 = newsFeedService.createNewsFeed(newsFeed1);
        Assert.assertTrue(result1.isSuccess());

        NewsFeedDTO newsFeed2 = new NewsFeedDTO();
        newsFeed2.setTitle("重复测试资讯2");
        newsFeed2.setAuthor("测试作者");
        newsFeed2.setSourceUrl(duplicateUrl); // 相同的URL
        newsFeed2.setPublishedAt(LocalDateTime.now());
        newsFeed2.setContentSummary("重复测试资讯2摘要");
        newsFeed2.setType(0);
        newsFeed2.setStatus(0);

        // 第二次创建应该失败
        Result<NewsFeedDTO> result2 = newsFeedService.createNewsFeed(newsFeed2);
        Assert.assertFalse(result2.isSuccess());
        Assert.assertEquals("NEWS_FEED_ALREADY_EXISTS", result2.getCode());
    }
}
