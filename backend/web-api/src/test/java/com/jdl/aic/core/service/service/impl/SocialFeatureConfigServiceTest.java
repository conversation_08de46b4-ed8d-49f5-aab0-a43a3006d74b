package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.SocialFeatureConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;
import com.jdl.aic.core.service.client.service.SocialFeatureConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * 社交功能配置服务测试类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class SocialFeatureConfigServiceTest {

    @Resource
    private SocialFeatureConfigService socialFeatureConfigService;

    @Test
    public void testCreateConfig() {
        log.info("开始测试创建社交功能配置");
        
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        config.setContentType("test_content");
        config.setFeatureType("like");
        config.setIsEnabled(true);
        config.setConfigJson("{\"maxLikes\": 1000}");

        Result<SocialFeatureConfigDTO> result = socialFeatureConfigService.createConfig(config);
        log.info("创建社交功能配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("test_content", result.getData().getContentType());
        Assert.assertEquals("like", result.getData().getFeatureType());
    }

    @Test
    public void testGetConfigById() {
        log.info("开始测试根据ID获取社交功能配置");
        
        // 先创建一个配置
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        config.setContentType("test_get_content");
        config.setFeatureType("favorite");
        config.setIsEnabled(true);
        
        Result<SocialFeatureConfigDTO> createResult = socialFeatureConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 根据ID获取配置
        Result<SocialFeatureConfigDTO> result = socialFeatureConfigService.getConfigById(configId);
        log.info("根据ID获取社交功能配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(configId, result.getData().getId());
        Assert.assertEquals("test_get_content", result.getData().getContentType());
        Assert.assertEquals("favorite", result.getData().getFeatureType());
    }

    @Test
    public void testGetConfigByContentAndFeature() {
        log.info("开始测试根据内容类型和功能类型获取配置");
        
        // 先创建一个配置
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        config.setContentType("test_content_feature");
        config.setFeatureType("share");
        config.setIsEnabled(true);
        
        Result<SocialFeatureConfigDTO> createResult = socialFeatureConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());

        // 根据内容类型和功能类型获取配置
        GetSocialFeatureConfigByContentAndFeatureRequest request = new GetSocialFeatureConfigByContentAndFeatureRequest("test_content_feature", "share");
        Result<SocialFeatureConfigDTO> result = socialFeatureConfigService.getConfigByContentAndFeature(request);
        log.info("根据内容类型和功能类型获取配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("test_content_feature", result.getData().getContentType());
        Assert.assertEquals("share", result.getData().getFeatureType());
    }

    @Test
    public void testUpdateConfig() {
        log.info("开始测试更新社交功能配置");
        
        // 先创建一个配置
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        config.setContentType("test_update_content");
        config.setFeatureType("comment");
        config.setIsEnabled(true);
        config.setConfigJson("{\"maxComments\": 100}");
        
        Result<SocialFeatureConfigDTO> createResult = socialFeatureConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 更新配置
        SocialFeatureConfigDTO updateConfig = new SocialFeatureConfigDTO();
        updateConfig.setContentType("test_update_content");
        updateConfig.setFeatureType("comment");
        updateConfig.setIsEnabled(false);
        updateConfig.setConfigJson("{\"maxComments\": 200}");

        Result<SocialFeatureConfigDTO> result = socialFeatureConfigService.updateConfig(configId, updateConfig);
        log.info("更新社交功能配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(false, result.getData().getIsEnabled());
        Assert.assertEquals("{\"maxComments\": 200}", result.getData().getConfigJson());
    }

    @Test
    public void testToggleConfigStatus() {
        log.info("开始测试切换社交功能配置状态");
        
        // 先创建一个配置
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        config.setContentType("test_toggle_content");
        config.setFeatureType("read_track");
        config.setIsEnabled(true);
        
        Result<SocialFeatureConfigDTO> createResult = socialFeatureConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 切换状态为禁用
        ToggleSocialFeatureConfigStatusRequest request = new ToggleSocialFeatureConfigStatusRequest(configId, false);
        Result<Void> result = socialFeatureConfigService.toggleConfigStatus(request);
        log.info("切换社交功能配置状态结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已切换
        Result<SocialFeatureConfigDTO> getResult = socialFeatureConfigService.getConfigById(configId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(false, getResult.getData().getIsEnabled());
    }

    @Test
    public void testGetConfigList() {
        log.info("开始测试获取社交功能配置列表");
        
        PageRequest pageRequest = PageRequest.of(1, 10);
        GetSocialFeatureConfigListRequest request = new GetSocialFeatureConfigListRequest();
        Result<PageResult<SocialFeatureConfigDTO>> result = socialFeatureConfigService.getConfigList(
                pageRequest, request);
        log.info("获取社交功能配置列表结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetConfigsByContentType() {
        log.info("开始测试根据内容类型获取配置");
        
        // 先创建一个配置
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        config.setContentType("test_content_type");
        config.setFeatureType("like");
        config.setIsEnabled(true);
        
        Result<SocialFeatureConfigDTO> createResult = socialFeatureConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());

        GetConfigsByContentTypeRequest request = new GetConfigsByContentTypeRequest("test_content_type", true);
        Result<List<SocialFeatureConfigDTO>> result = socialFeatureConfigService.getConfigsByContentType(request);
        log.info("根据内容类型获取配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() > 0);
    }

    @Test
    public void testGetConfigsByFeatureType() {
        log.info("开始测试根据功能类型获取配置");
        
        GetSocialFeatureConfigsByFeatureTypeRequest request = new GetSocialFeatureConfigsByFeatureTypeRequest("like", true);
        Result<List<SocialFeatureConfigDTO>> result = socialFeatureConfigService.getConfigsByFeatureType(request);
        log.info("根据功能类型获取配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetAllEnabledConfigs() {
        log.info("开始测试获取所有启用的社交功能配置");
        
        Result<List<SocialFeatureConfigDTO>> result = socialFeatureConfigService.getAllEnabledConfigs();
        log.info("获取所有启用的社交功能配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testBatchUpdateStatusByContentType() {
        log.info("开始测试批量更新内容类型的功能状态");
        
        // 先创建一些配置
        SocialFeatureConfigDTO config1 = new SocialFeatureConfigDTO();
        config1.setContentType("test_batch_content");
        config1.setFeatureType("like");
        config1.setIsEnabled(true);
        
        SocialFeatureConfigDTO config2 = new SocialFeatureConfigDTO();
        config2.setContentType("test_batch_content");
        config2.setFeatureType("favorite");
        config2.setIsEnabled(true);
        
        socialFeatureConfigService.createConfig(config1);
        socialFeatureConfigService.createConfig(config2);

        // 批量更新状态
        Result<Void> result = socialFeatureConfigService.batchUpdateStatusByContentType("test_batch_content", false);
        log.info("批量更新内容类型的功能状态结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testCheckConfigExists() {
        log.info("开始测试检查配置是否存在");
        
        // 先创建一个配置
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        config.setContentType("test_check_content");
        config.setFeatureType("like");
        config.setIsEnabled(true);
        
        Result<SocialFeatureConfigDTO> createResult = socialFeatureConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());

        // 检查配置是否存在
        Result<Boolean> result = socialFeatureConfigService.checkConfigExists(
                "test_check_content", "like", null);
        log.info("检查配置是否存在结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData());

        // 检查不存在的配置
        Result<Boolean> notExistResult = socialFeatureConfigService.checkConfigExists(
                "not_exist_content", "not_exist_feature", null);
        Assert.assertTrue(notExistResult.isSuccess());
        Assert.assertFalse(notExistResult.getData());
    }

    @Test
    public void testGetSupportedFeatureTypes() {
        log.info("开始测试获取支持的功能类型列表");
        
        Result<List<String>> result = socialFeatureConfigService.getSupportedFeatureTypes();
        log.info("获取支持的功能类型列表结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() > 0);
        Assert.assertTrue(result.getData().contains("like"));
        Assert.assertTrue(result.getData().contains("favorite"));
        Assert.assertTrue(result.getData().contains("share"));
        Assert.assertTrue(result.getData().contains("comment"));
        Assert.assertTrue(result.getData().contains("read_track"));
    }

    @Test
    public void testInitDefaultConfigs() {
        log.info("开始测试初始化默认配置");
        
        Result<List<SocialFeatureConfigDTO>> result = socialFeatureConfigService.initDefaultConfigs(
                "test_init_content");
        log.info("初始化默认配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() > 0);
    }

    @Test
    public void testDeleteConfig() {
        log.info("开始测试删除社交功能配置");
        
        // 先创建一个配置
        SocialFeatureConfigDTO config = new SocialFeatureConfigDTO();
        config.setContentType("test_delete_content");
        config.setFeatureType("like");
        config.setIsEnabled(true);
        
        Result<SocialFeatureConfigDTO> createResult = socialFeatureConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 删除配置
        Result<Void> result = socialFeatureConfigService.deleteConfig(configId);
        log.info("删除社交功能配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证配置已删除
        Result<SocialFeatureConfigDTO> getResult = socialFeatureConfigService.getConfigById(configId);
        Assert.assertFalse(getResult.isSuccess());
    }
}
