package com.jdl.aic.core.service.consumer;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.category.ContentCategoryRelationDTO;
import com.jdl.aic.core.service.client.dto.request.solution.GetSolutionListRequest;
import com.jdl.aic.core.service.client.dto.solution.SolutionDTO;
import com.jdl.aic.core.service.client.service.SolutionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * FileName: JsfConsumerTest
 * Description:
 * [@author]:   quhua<PERSON>
 * [@date]:     2025/7/2223:28
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"/spring-jsf-testing-env.xml"})
public class JsfConsumerTest {

    @Resource(name = "solutionServiceConsumer")
    private SolutionService solutionService;

    @Test
    public void testSolutionServiceCreate() {
        // 测试创建解决方案
        SolutionDTO newSolution = new SolutionDTO();
        newSolution.setTitle("JSF测试解决方案");
        newSolution.setDescription("这是一个JSF消费者测试解决方案");
        newSolution.setContent("测试JSF服务调用是否正常");
        newSolution.setAuthorId("user_jsf_test");
        newSolution.setAuthorName("JSF测试用户");
        newSolution.setStatus(0); // 草稿状态
        ContentCategoryRelationDTO categoryRelationDTO = new ContentCategoryRelationDTO();
        categoryRelationDTO.setCategoryId(1L);
        newSolution.setCategories(Arrays.asList(categoryRelationDTO));
        Result<SolutionDTO> result = solutionService.createSolution(newSolution);
        System.out.println("JSF创建解决方案结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testSolutionServiceSelect() {
        // 测试查询解决方案
        GetSolutionListRequest request = new GetSolutionListRequest();
        PageRequest pageRequest = new PageRequest();
        pageRequest.setSize(20);
        pageRequest.setPage(1);
        request.setPageRequest(pageRequest);
        Result<PageResult<SolutionDTO>> result = solutionService.getSolutionList(request);
        System.out.println("JSF查询解决方案结果：" + JSON.toJSONString(result));
    }

    @Test
    public void testSolutionServiceDelete() {

        Result<Void> result = solutionService.deleteSolution(10L);
        System.out.println("JSF删除解决方案结果：" + JSON.toJSONString(result));
    }

}
