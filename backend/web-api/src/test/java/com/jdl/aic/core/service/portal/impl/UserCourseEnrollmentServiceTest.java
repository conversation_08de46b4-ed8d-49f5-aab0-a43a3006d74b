package com.jdl.aic.core.service.portal.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.enrollment.UserCourseEnrollmentDTO;
import com.jdl.aic.core.service.client.dto.request.enrollment.GetUserEnrollmentListRequest;
import com.jdl.aic.core.service.client.dto.request.enrollment.UpdateEnrollmentProgressRequest;
import com.jdl.aic.core.service.client.dto.request.enrollment.UpdateEnrollmentStatusRequest;
import com.jdl.aic.core.service.portal.client.UserCourseEnrollmentService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 用户课程报名服务测试类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class UserCourseEnrollmentServiceTest {

    @Resource
    private UserCourseEnrollmentService userCourseEnrollmentService;

    @Test
    public void testCreateEnrollment() {
        // 创建测试报名
        UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
        newEnrollment.setUserId(1L);
        newEnrollment.setCourseId(1L);
        newEnrollment.setEnrollmentStatus("ENROLLED");
        newEnrollment.setEnrollmentSource("WEB");
        newEnrollment.setProgressPercentage(BigDecimal.ZERO);
        newEnrollment.setCompletedStages(0);
        newEnrollment.setTotalStages(10);
        newEnrollment.setStudyHours(BigDecimal.ZERO);

        Result<UserCourseEnrollmentDTO> result = userCourseEnrollmentService.createEnrollment(newEnrollment);
        System.out.println("创建报名结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
    }

    @Test
    public void testGetEnrollmentById() {
        // 先创建一个报名
        UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
        newEnrollment.setUserId(2L);
        newEnrollment.setCourseId(3L);
        newEnrollment.setEnrollmentStatus("ENROLLED");
        newEnrollment.setEnrollmentSource("WEB");

        Result<UserCourseEnrollmentDTO> createResult = userCourseEnrollmentService.createEnrollment(newEnrollment);
        Assert.assertTrue(createResult.isSuccess());
        Long enrollmentId = createResult.getData().getId();

        // 查询报名
        Result<UserCourseEnrollmentDTO> result = userCourseEnrollmentService.getEnrollmentById(enrollmentId);
        System.out.println("查询报名结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(enrollmentId, result.getData().getId());
    }

    @Test
    public void testGetUserEnrollmentList() {
        PageRequest pageRequest = new PageRequest(1, 10);
        GetUserEnrollmentListRequest request = new GetUserEnrollmentListRequest();
        request.setUserId(1L);
        request.setEnrollmentStatus("ENROLLED");

        Result<PageResult<UserCourseEnrollmentDTO>> result = userCourseEnrollmentService
                .getUserEnrollmentList(pageRequest, request);
        System.out.println("用户报名列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateEnrollmentStatus() {
        // 先创建一个报名
        UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
        newEnrollment.setUserId(3L);
        newEnrollment.setCourseId(3L);
        newEnrollment.setEnrollmentStatus("ENROLLED");

        Result<UserCourseEnrollmentDTO> createResult = userCourseEnrollmentService.createEnrollment(newEnrollment);
        Assert.assertTrue(createResult.isSuccess());
        Long enrollmentId = createResult.getData().getId();

        // 更新状态
        UpdateEnrollmentStatusRequest request = new UpdateEnrollmentStatusRequest();
        request.setId(enrollmentId);
        request.setEnrollmentStatus("IN_PROGRESS");

        Result<Void> result = userCourseEnrollmentService.updateEnrollmentStatus(request);
        System.out.println("更新报名状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更新
        Result<UserCourseEnrollmentDTO> getResult = userCourseEnrollmentService.getEnrollmentById(enrollmentId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals("IN_PROGRESS", getResult.getData().getEnrollmentStatus());
    }

    @Test
    public void testUpdateEnrollmentProgress() {
        // 先创建一个报名
        UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
        newEnrollment.setUserId(4L);
        newEnrollment.setCourseId(4L);
        newEnrollment.setEnrollmentStatus("IN_PROGRESS");

        Result<UserCourseEnrollmentDTO> createResult = userCourseEnrollmentService.createEnrollment(newEnrollment);
        Assert.assertTrue(createResult.isSuccess());
        Long enrollmentId = createResult.getData().getId();

        // 更新进度
        UpdateEnrollmentProgressRequest request = new UpdateEnrollmentProgressRequest();
        request.setId(enrollmentId);
        request.setProgressPercentage(new BigDecimal("50.00"));
        request.setCompletedStages(5);
        request.setTotalStages(10);
        request.setStudyHours(new BigDecimal("25.5"));

        Result<Void> result = userCourseEnrollmentService.updateEnrollmentProgress(request);
        System.out.println("更新学习进度结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证进度已更新
        Result<UserCourseEnrollmentDTO> getResult = userCourseEnrollmentService.getEnrollmentById(enrollmentId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(new BigDecimal("50.00"), getResult.getData().getProgressPercentage());
        Assert.assertEquals(Integer.valueOf(5), getResult.getData().getCompletedStages());
    }

    @Test
    public void testIsUserEnrolledInCourse() {
        // 先创建一个报名
        UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
        newEnrollment.setUserId(5L);
        newEnrollment.setCourseId(5L);
        newEnrollment.setEnrollmentStatus("ENROLLED");

        Result<UserCourseEnrollmentDTO> createResult = userCourseEnrollmentService.createEnrollment(newEnrollment);
        Assert.assertTrue(createResult.isSuccess());

        // 检查是否已报名
        Result<Boolean> result = userCourseEnrollmentService.isUserEnrolledInCourse(5L, 5L);
        System.out.println("检查用户是否已报名结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData());

        // 检查未报名的课程
        Result<Boolean> result2 = userCourseEnrollmentService.isUserEnrolledInCourse(5L, 999L);
        Assert.assertTrue(result2.isSuccess());
        Assert.assertFalse(result2.getData());
    }

    @Test
    public void testGetUserCourseEnrollment() {
        // 先创建一个报名
        UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
        newEnrollment.setUserId(6L);
        newEnrollment.setCourseId(6L);
        newEnrollment.setEnrollmentStatus("ENROLLED");

        Result<UserCourseEnrollmentDTO> createResult = userCourseEnrollmentService.createEnrollment(newEnrollment);
        Assert.assertTrue(createResult.isSuccess());

        // 获取用户课程报名信息
        Result<UserCourseEnrollmentDTO> result = userCourseEnrollmentService
                .getUserCourseEnrollment(6L, 6L);
        System.out.println("获取用户课程报名信息结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(Long.valueOf(6L), result.getData().getUserId());
        Assert.assertEquals(Long.valueOf(6L), result.getData().getCourseId());
    }

    @Test
    public void testGetUserEnrollments() {
        // 先创建多个报名
        for (int i = 1; i <= 3; i++) {
            UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
            newEnrollment.setUserId(7L);
            newEnrollment.setCourseId((long) (10 + i));
            newEnrollment.setEnrollmentStatus("ENROLLED");

            userCourseEnrollmentService.createEnrollment(newEnrollment);
        }

        // 获取用户的所有报名
        Result<List<UserCourseEnrollmentDTO>> result = userCourseEnrollmentService.getUserEnrollments(7L);
        System.out.println("获取用户所有报名结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 3);
    }

    @Test
    public void testGetUserInProgressCourses() {
        // 先创建一个进行中的报名
        UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
        newEnrollment.setUserId(8L);
        newEnrollment.setCourseId(20L);
        newEnrollment.setEnrollmentStatus("IN_PROGRESS");

        Result<UserCourseEnrollmentDTO> createResult = userCourseEnrollmentService.createEnrollment(newEnrollment);
        Assert.assertTrue(createResult.isSuccess());

        // 获取用户正在学习的课程
        Result<List<UserCourseEnrollmentDTO>> result = userCourseEnrollmentService
                .getUserInProgressCourses(8L);
        System.out.println("获取用户正在学习的课程结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 1);
    }

    @Test
    public void testBatchUpdateStudyHours() {
        // 先创建多个报名
        List<Long> enrollmentIds = Arrays.asList();
        for (int i = 1; i <= 2; i++) {
            UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
            newEnrollment.setUserId(9L);
            newEnrollment.setCourseId((long) (30 + i));
            newEnrollment.setEnrollmentStatus("IN_PROGRESS");

            Result<UserCourseEnrollmentDTO> createResult = userCourseEnrollmentService
                    .createEnrollment(newEnrollment);
            Assert.assertTrue(createResult.isSuccess());
            enrollmentIds = Arrays.asList(createResult.getData().getId());
        }

        // 批量更新学习时长
        Result<Void> result = userCourseEnrollmentService.batchUpdateStudyHours(enrollmentIds, 2.5);
        System.out.println("批量更新学习时长结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testDeleteEnrollment() {
        // 先创建一个报名
        UserCourseEnrollmentDTO newEnrollment = new UserCourseEnrollmentDTO();
        newEnrollment.setUserId(10L);
        newEnrollment.setCourseId(40L);
        newEnrollment.setEnrollmentStatus("ENROLLED");

        Result<UserCourseEnrollmentDTO> createResult = userCourseEnrollmentService.createEnrollment(newEnrollment);
        Assert.assertTrue(createResult.isSuccess());
        Long enrollmentId = createResult.getData().getId();

        // 删除报名
        Result<Void> result = userCourseEnrollmentService.deleteEnrollment(enrollmentId);
        System.out.println("删除报名结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证报名已删除
        Result<UserCourseEnrollmentDTO> getResult = userCourseEnrollmentService.getEnrollmentById(enrollmentId);
        Assert.assertFalse(getResult.isSuccess());
    }
}
