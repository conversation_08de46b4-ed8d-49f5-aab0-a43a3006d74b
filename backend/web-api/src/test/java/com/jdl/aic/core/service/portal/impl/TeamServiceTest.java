package com.jdl.aic.core.service.portal.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.team.TeamDTO;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamListRequest;
import com.jdl.aic.core.service.client.dto.request.team.GetTeamTreeRequest;
import com.jdl.aic.core.service.client.dto.request.team.ToggleTeamStatusRequest;
import com.jdl.aic.core.service.client.dto.request.team.MoveTeamToParentRequest;
import com.jdl.aic.core.service.portal.client.TeamDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 团队服务测试类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
public class TeamServiceTest {

    @Resource
    private TeamDataService teamService;

    @Test
    public void testCreateTeam() {
        // 创建测试团队
        TeamDTO newTeam = new TeamDTO();
        newTeam.setName("测试团队" + new Random().nextInt(1000));
        newTeam.setDescription("这是一个测试团队");
        newTeam.setIsActive(true);

        // 添加标签
        List<String> tags = Arrays.asList("开发", "测试", "Java");
        newTeam.setTags(tags);

        // 添加新字段测试
        newTeam.setAvatarUrl("https://example.com/avatar.jpg");
        newTeam.setPrivacy(0); // 公开
        newTeam.setInviteSetting(1); // 仅管理员可邀请

        Result<TeamDTO> result = teamService.createTeam(newTeam);
        System.out.println("创建团队结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertNotNull(result.getData().getId());
        assertEquals(tags, result.getData().getTags());
        assertEquals("https://example.com/avatar.jpg", result.getData().getAvatarUrl());
        assertEquals(Integer.valueOf(0), result.getData().getPrivacy());
        assertEquals(Integer.valueOf(1), result.getData().getInviteSetting());
    }

    @Test
    public void testGetTeamById() {
        // 先创建一个团队
        TeamDTO newTeam = new TeamDTO();
        newTeam.setName("查询测试团队" + new Random().nextInt(1000));
        newTeam.setDescription("用于查询测试的团队");
        newTeam.setIsActive(true);
        // 添加标签
        List<String> tags = Arrays.asList("开发1", "测试", "Java");
        newTeam.setTags(tags);
        // 添加新字段
        newTeam.setAvatarUrl("https://example.com/query-avatar.jpg");
        newTeam.setPrivacy(1); // 私有
        newTeam.setInviteSetting(0); // 任何人可邀请

        Result<TeamDTO> createResult = teamService.createTeam(newTeam);
        assertTrue(createResult.isSuccess());
        Long teamId = createResult.getData().getId();

        // 根据ID查询团队
        Result<TeamDTO> result = teamService.getTeamById(teamId);
        System.out.println("根据ID查询团队结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals(teamId, result.getData().getId());
        assertEquals("https://example.com/query-avatar.jpg", result.getData().getAvatarUrl());
        assertEquals(Integer.valueOf(1), result.getData().getPrivacy());
        assertEquals(Integer.valueOf(0), result.getData().getInviteSetting());
    }

    @Test
    public void testTeamNewFields() {
        // 测试新字段：avatarUrl, privacy, inviteSetting
        TeamDTO newTeam = new TeamDTO();
        newTeam.setName("新字段测试团队" + new Random().nextInt(1000));
        newTeam.setDescription("用于测试新字段的团队");
        newTeam.setIsActive(true);
        newTeam.setAvatarUrl("https://example.com/test-avatar.png");
        newTeam.setPrivacy(1); // 私有团队
        newTeam.setInviteSetting(2); // 仅创建者可邀请

        // 创建团队
        Result<TeamDTO> createResult = teamService.createTeam(newTeam);
        System.out.println("创建带新字段的团队结果：" + JSON.toJSONString(createResult));
        assertTrue(createResult.isSuccess());
        assertNotNull(createResult.getData());

        TeamDTO createdTeam = createResult.getData();
        assertEquals("https://example.com/test-avatar.png", createdTeam.getAvatarUrl());
        assertEquals(Integer.valueOf(1), createdTeam.getPrivacy());
        assertEquals(Integer.valueOf(2), createdTeam.getInviteSetting());

        // 更新团队新字段
        Long teamId = createdTeam.getId();
        TeamDTO updateTeam = new TeamDTO();
        updateTeam.setName("更新后的团队名称");
        updateTeam.setAvatarUrl("https://example.com/updated-avatar.jpg");
        updateTeam.setPrivacy(0); // 改为公开
        updateTeam.setInviteSetting(1); // 改为仅管理员可邀请

        Result<TeamDTO> updateResult = teamService.updateTeam(teamId, updateTeam);
        System.out.println("更新团队新字段结果：" + JSON.toJSONString(updateResult));
        assertTrue(updateResult.isSuccess());

        TeamDTO updatedTeam = updateResult.getData();
        assertEquals("更新后的团队名称", updatedTeam.getName());
        assertEquals("https://example.com/updated-avatar.jpg", updatedTeam.getAvatarUrl());
        assertEquals(Integer.valueOf(0), updatedTeam.getPrivacy());
        assertEquals(Integer.valueOf(1), updatedTeam.getInviteSetting());
    }

    @Test
    public void testUpdateTeam() {
        // 先创建一个团队
        TeamDTO newTeam = new TeamDTO();
        newTeam.setName("更新测试团队");
        newTeam.setDescription("用于更新测试的团队");
        newTeam.setIsActive(true);

        Result<TeamDTO> createResult = teamService.createTeam(newTeam);
        assertTrue(createResult.isSuccess());
        Long teamId = createResult.getData().getId();

        // 更新团队信息
        TeamDTO updateTeam = new TeamDTO();
        updateTeam.setName("更新后的团队名称");
        updateTeam.setDescription("更新后的团队描述");
        updateTeam.setIsActive(false);

        Result<TeamDTO> result = teamService.updateTeam(teamId, updateTeam);
        System.out.println("更新团队结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertEquals("更新后的团队名称", result.getData().getName());
        assertEquals("更新后的团队描述", result.getData().getDescription());
        assertEquals(false, result.getData().getIsActive());
    }

    @Test
    public void testGetTeamList() {
        // 创建几个测试团队
        for (int i = 1; i <= 3; i++) {
            TeamDTO team = new TeamDTO();
            team.setName("列表测试团队" + i);
            team.setDescription("用于列表测试的团队" + i);
            team.setIsActive(true);
            teamService.createTeam(team);
        }

        // 测试分页查询
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetTeamListRequest request = new GetTeamListRequest();
        request.setIsActive(true);

        Result<PageResult<TeamDTO>> result = teamService.getTeamList(pageRequest, request);
        System.out.println("获取团队列表结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().getPagination().getTotalElements() >= 3);
    }

    @Test
    public void testGetTeamTree() {
        // 创建父团队
        TeamDTO parentTeam = new TeamDTO();
        parentTeam.setName("父团队");
        parentTeam.setDescription("这是一个父团队");
        parentTeam.setIsActive(true);

        Result<TeamDTO> parentResult = teamService.createTeam(parentTeam);
        assertTrue(parentResult.isSuccess());
        Long parentId = parentResult.getData().getId();

        // 创建子团队
        TeamDTO childTeam = new TeamDTO();
        childTeam.setName("子团队");
        childTeam.setDescription("这是一个子团队");
        childTeam.setParentId(parentId);
        childTeam.setIsActive(true);

        Result<TeamDTO> childResult = teamService.createTeam(childTeam);
        assertTrue(childResult.isSuccess());

        // 获取团队树
        GetTeamTreeRequest request = new GetTeamTreeRequest();
        request.setIsActive(true);

        Result<List<TeamDTO>> result = teamService.getTeamTree(request);
        System.out.println("获取团队树结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().size() > 0);
    }

    @Test
    public void testToggleTeamStatus() {
        // 先创建一个团队
        TeamDTO newTeam = new TeamDTO();
        newTeam.setName("状态切换测试团队");
        newTeam.setDescription("用于状态切换测试的团队");
        newTeam.setIsActive(true);

        Result<TeamDTO> createResult = teamService.createTeam(newTeam);
        assertTrue(createResult.isSuccess());
        Long teamId = createResult.getData().getId();

        // 切换团队状态
        ToggleTeamStatusRequest request = new ToggleTeamStatusRequest();
        request.setTeamId(teamId);
        request.setIsActive(false);

        Result<Void> result = teamService.toggleTeamStatus(request);
        System.out.println("切换团队状态结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证状态是否已切换
        Result<TeamDTO> queryResult = teamService.getTeamById(teamId);
        assertTrue(queryResult.isSuccess());
        assertEquals(false, queryResult.getData().getIsActive());
    }

    @Test
    public void testMoveTeamToParent() {
        // 创建两个父团队
        TeamDTO parentTeam1 = new TeamDTO();
        parentTeam1.setName("父团队1");
        parentTeam1.setDescription("第一个父团队");
        parentTeam1.setIsActive(true);

        Result<TeamDTO> parent1Result = teamService.createTeam(parentTeam1);
        assertTrue(parent1Result.isSuccess());
        Long parent1Id = parent1Result.getData().getId();

        TeamDTO parentTeam2 = new TeamDTO();
        parentTeam2.setName("父团队2");
        parentTeam2.setDescription("第二个父团队");
        parentTeam2.setIsActive(true);

        Result<TeamDTO> parent2Result = teamService.createTeam(parentTeam2);
        assertTrue(parent2Result.isSuccess());
        Long parent2Id = parent2Result.getData().getId();

        // 创建子团队，初始属于父团队1
        TeamDTO childTeam = new TeamDTO();
        childTeam.setName("移动测试子团队");
        childTeam.setDescription("用于移动测试的子团队");
        childTeam.setParentId(parent1Id);
        childTeam.setIsActive(true);

        Result<TeamDTO> childResult = teamService.createTeam(childTeam);
        assertTrue(childResult.isSuccess());
        Long childId = childResult.getData().getId();

        // 将子团队移动到父团队2下
        MoveTeamToParentRequest request = new MoveTeamToParentRequest();
        request.setTeamId(childId);
        request.setNewParentId(parent2Id);

        Result<Void> result = teamService.moveTeamToParent(request);
        System.out.println("移动团队结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证团队是否已移动
        Result<TeamDTO> queryResult = teamService.getTeamById(childId);
        assertTrue(queryResult.isSuccess());
        assertEquals(parent2Id, queryResult.getData().getParentId());
    }

    @Test
    public void testGetActiveTeams() {
        // 创建一些活跃和非活跃的团队
        TeamDTO activeTeam = new TeamDTO();
        activeTeam.setName("活跃团队");
        activeTeam.setDescription("这是一个活跃团队");
        activeTeam.setIsActive(true);
        teamService.createTeam(activeTeam);

        TeamDTO inactiveTeam = new TeamDTO();
        inactiveTeam.setName("非活跃团队");
        inactiveTeam.setDescription("这是一个非活跃团队");
        inactiveTeam.setIsActive(false);
        teamService.createTeam(inactiveTeam);

        // 获取活跃团队列表
        Result<List<TeamDTO>> result = teamService.getActiveTeams();
        System.out.println("获取活跃团队列表结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        // 验证返回的都是活跃团队
        for (TeamDTO team : result.getData()) {
            assertTrue(team.getIsActive());
        }
    }

    @Test
    public void testSearchTeamsByName() {
        // 创建一些测试团队
        TeamDTO team1 = new TeamDTO();
        team1.setName("搜索测试团队A");
        team1.setDescription("用于搜索测试的团队A");
        team1.setIsActive(true);
        teamService.createTeam(team1);

        TeamDTO team2 = new TeamDTO();
        team2.setName("搜索测试团队B");
        team2.setDescription("用于搜索测试的团队B");
        team2.setIsActive(true);
        teamService.createTeam(team2);

        // 搜索团队
        Result<List<TeamDTO>> result = teamService.searchTeamsByName("搜索测试");
        System.out.println("搜索团队结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        assertTrue(result.getData().size() >= 2);
    }

    @Test
    public void testDeleteTeam() {
        // 先创建一个团队
        TeamDTO newTeam = new TeamDTO();
        newTeam.setName("删除测试团队");
        newTeam.setDescription("用于删除测试的团队");
        newTeam.setIsActive(true);

        Result<TeamDTO> createResult = teamService.createTeam(newTeam);
        assertTrue(createResult.isSuccess());
        Long teamId = createResult.getData().getId();

        // 删除团队
        Result<Void> result = teamService.deleteTeam(teamId);
        System.out.println("删除团队结果：" + JSON.toJSONString(result));
        assertNotNull(result);
        assertTrue(result.isSuccess());

        // 验证团队是否已删除
        Result<TeamDTO> queryResult = teamService.getTeamById(teamId);
        assertFalse(queryResult.isSuccess());
        assertEquals("TEAM_NOT_FOUND", queryResult.getCode());
    }

    @Test
    public void testTeamTags() {
        // 创建带标签的团队
        TeamDTO team1 = new TeamDTO();
        team1.setName("前端团队");
        team1.setDescription("负责前端开发的团队");
        team1.setIsActive(true);
        team1.setTags(Arrays.asList("前端", "React", "Vue"));

        Result<TeamDTO> result1 = teamService.createTeam(team1);
        assertTrue(result1.isSuccess());
        Long team1Id = result1.getData().getId();

        TeamDTO team2 = new TeamDTO();
        team2.setName("后端团队");
        team2.setDescription("负责后端开发的团队");
        team2.setIsActive(true);
        team2.setTags(Arrays.asList("后端", "Java", "Spring"));

        Result<TeamDTO> result2 = teamService.createTeam(team2);
        assertTrue(result2.isSuccess());
        Long team2Id = result2.getData().getId();

        // 测试根据标签过滤团队
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetTeamListRequest request = new GetTeamListRequest();
        request.setIsActive(true);
        request.setTags(Arrays.asList("Java")); // 只查找包含Java标签的团队

        Result<PageResult<TeamDTO>> listResult = teamService.getTeamList(pageRequest, request);
        System.out.println("根据标签过滤团队结果：" + JSON.toJSONString(listResult));
        assertTrue(listResult.isSuccess());
        assertNotNull(listResult.getData());

        // 验证返回的团队包含Java标签
        List<TeamDTO> teams = listResult.getData().getRecords();
        boolean foundJavaTeam = teams.stream()
                .anyMatch(team -> team.getTags() != null && team.getTags().contains("Java"));
        assertTrue(foundJavaTeam);

        // 测试更新团队标签
        TeamDTO updateTeam = new TeamDTO();
        updateTeam.setTags(Arrays.asList("前端", "React", "TypeScript", "Node.js"));

        Result<TeamDTO> updateResult = teamService.updateTeam(team1Id, updateTeam);
        System.out.println("更新团队标签结果：" + JSON.toJSONString(updateResult));
        assertTrue(updateResult.isSuccess());
        assertEquals(4, updateResult.getData().getTags().size());
        assertTrue(updateResult.getData().getTags().contains("TypeScript"));
        assertTrue(updateResult.getData().getTags().contains("Node.js"));
    }
}
