package com.jdl.aic.core.service.portal.impl;

import com.alibaba.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.community.FavoriteDTO;
import com.jdl.aic.core.service.client.dto.community.request.*;
import com.jdl.aic.core.service.portal.client.FavoriteDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Random;

/**
 * FavoriteDataService 集成测试 - 参考CategoryServiceTest结构
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class FavoriteDataServiceImplTest {

    @Resource
    private FavoriteDataService favoriteDataService;

    @Test
    public void testAddFavorite() {
        // 创建测试收藏
        AddFavoriteRequest request = new AddFavoriteRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0); // 知识类型
        request.setContentId(100L + new Random().nextInt(100));
        request.setRelatedKnowledgeTypeId(1L);

        Result<FavoriteDTO> result = favoriteDataService.addFavorite(request);
        System.out.println("添加收藏结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals(request.getUserId(), result.getData().getUserId());
        Assert.assertEquals(request.getContentType(), result.getData().getContentType());
        Assert.assertEquals(request.getContentId(), result.getData().getContentId());
    }

    @Test
    public void testRemoveFavorite() {
        // 先添加一个收藏
        AddFavoriteRequest addRequest = new AddFavoriteRequest();
        addRequest.setUserId(1000L + new Random().nextInt(1000));
        addRequest.setContentType(0);
        addRequest.setContentId(100L + new Random().nextInt(100));
        addRequest.setRelatedKnowledgeTypeId(1L);

        Result<FavoriteDTO> addResult = favoriteDataService.addFavorite(addRequest);
        Assert.assertTrue(addResult.isSuccess());

        // 取消收藏
        RemoveFavoriteRequest removeRequest = new RemoveFavoriteRequest();
        removeRequest.setUserId(addRequest.getUserId());
        removeRequest.setContentType(addRequest.getContentType());
        removeRequest.setContentId(addRequest.getContentId());

        Result<Void> result = favoriteDataService.removeFavorite(removeRequest);
        System.out.println("取消收藏结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testToggleFavorite() {
        // 测试切换收藏状态
        ToggleFavoriteRequest request = new ToggleFavoriteRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0);
        request.setContentId(100L + new Random().nextInt(100));
        request.setRelatedKnowledgeTypeId(1L);

        // 第一次切换（添加收藏）
        Result<Boolean> result1 = favoriteDataService.toggleFavorite(request);
        System.out.println("第一次切换收藏结果：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertTrue(result1.isSuccess());
        Assert.assertTrue(result1.getData()); // 应该返回true表示已收藏

        // 第二次切换（取消收藏）
        Result<Boolean> result2 = favoriteDataService.toggleFavorite(request);
        System.out.println("第二次切换收藏结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertTrue(result2.isSuccess());
        Assert.assertFalse(result2.getData()); // 应该返回false表示已取消收藏
    }

    @Test
    public void testIsFavorited() {
        // 先添加一个收藏
        AddFavoriteRequest addRequest = new AddFavoriteRequest();
        addRequest.setUserId(1000L + new Random().nextInt(1000));
        addRequest.setContentType(0);
        addRequest.setContentId(100L + new Random().nextInt(100));
        addRequest.setRelatedKnowledgeTypeId(1L);

        Result<FavoriteDTO> addResult = favoriteDataService.addFavorite(addRequest);
        Assert.assertTrue(addResult.isSuccess());

        // 检查收藏状态
        CheckFavoriteRequest checkRequest = new CheckFavoriteRequest();
        checkRequest.setUserId(addRequest.getUserId());
        checkRequest.setContentType(addRequest.getContentType());
        checkRequest.setContentId(addRequest.getContentId());

        Result<Boolean> result = favoriteDataService.isFavorited(checkRequest);
        System.out.println("检查收藏状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData()); // 应该返回true表示已收藏
    }

    @Test
    public void testGetUserFavorites() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个收藏
        for (int i = 1; i <= 3; i++) {
            AddFavoriteRequest request = new AddFavoriteRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(100L + i);
            request.setRelatedKnowledgeTypeId(1L);
            favoriteDataService.addFavorite(request);
        }

        // 测试分页查询用户收藏列表
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetUserFavoritesRequest request = new GetUserFavoritesRequest();
        request.setUserId(userId);
        request.setPageRequest(pageRequest);
        request.setContentType(0); // 只查询知识类型的收藏

        Result<PageResult<FavoriteDTO>> result = favoriteDataService.getUserFavorites(request);
        System.out.println("获取用户收藏列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().getPagination().getTotalElements() >= 3);
    }

    @Test
    public void testGetRecentFavorites() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个收藏
        for (int i = 1; i <= 2; i++) {
            AddFavoriteRequest request = new AddFavoriteRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(200L + i);
            request.setRelatedKnowledgeTypeId(1L);
            favoriteDataService.addFavorite(request);
        }

        // 获取最近收藏
        Result<List<FavoriteDTO>> result = favoriteDataService.getRecentFavorites(userId, 5);
        System.out.println("获取最近收藏结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 2);
    }

    @Test
    public void testGetContentFavoriteCount() {
        Integer contentType = 0;
        Long contentId = 300L + new Random().nextInt(100);
        
        // 让多个用户收藏同一个内容
        for (int i = 1; i <= 2; i++) {
            AddFavoriteRequest request = new AddFavoriteRequest();
            request.setUserId(1000L + i);
            request.setContentType(contentType);
            request.setContentId(contentId);
            request.setRelatedKnowledgeTypeId(1L);
            favoriteDataService.addFavorite(request);
        }

        GetContentFavoriteCountRequest request = new GetContentFavoriteCountRequest();
        request.setContentType(contentType);
        request.setContentId(contentId);
        // 统计内容收藏数
        Result<Integer> result = favoriteDataService.getContentFavoriteCount(request);
        System.out.println("获取内容收藏数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData() >= 2);
    }

    @Test
    public void testGetUserFavoriteCount() {
        Long userId = 1000L + new Random().nextInt(1000);
        
        // 为用户添加几个收藏
        for (int i = 1; i <= 2; i++) {
            AddFavoriteRequest request = new AddFavoriteRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(400L + i);
            request.setRelatedKnowledgeTypeId(1L);
            favoriteDataService.addFavorite(request);
        }

        // 统计用户收藏数
        Result<Integer> result = favoriteDataService.getUserFavoriteCount(userId);
        System.out.println("获取用户收藏数结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData() >= 2);
    }

    @Test
    public void testInvalidParameters() {
        // 测试空用户ID
        AddFavoriteRequest request1 = new AddFavoriteRequest();
        request1.setUserId(null);
        request1.setContentType(0);
        request1.setContentId(100L);
        
        Result<FavoriteDTO> result1 = favoriteDataService.addFavorite(request1);
        System.out.println("空用户ID测试结果：" + JSON.toJSONString(result1));
        Assert.assertNotNull(result1);
        Assert.assertFalse(result1.isSuccess());
        
        // 测试空内容类型
        AddFavoriteRequest request2 = new AddFavoriteRequest();
        request2.setUserId(1000L);
        request2.setContentType(null);
        request2.setContentId(100L);
        
        Result<FavoriteDTO> result2 = favoriteDataService.addFavorite(request2);
        System.out.println("空内容类型测试结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        Assert.assertFalse(result2.isSuccess());
        
        // 测试空内容ID
        AddFavoriteRequest request3 = new AddFavoriteRequest();
        request3.setUserId(1000L);
        request3.setContentType(0);
        request3.setContentId(null);
        
        Result<FavoriteDTO> result3 = favoriteDataService.addFavorite(request3);
        System.out.println("空内容ID测试结果：" + JSON.toJSONString(result3));
        Assert.assertNotNull(result3);
        Assert.assertFalse(result3.isSuccess());
    }

    @Test
    public void testDuplicateFavorite() {
        // 测试重复收藏
        AddFavoriteRequest request = new AddFavoriteRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0);
        request.setContentId(500L + new Random().nextInt(100));
        request.setRelatedKnowledgeTypeId(1L);

        // 第一次添加收藏
        Result<FavoriteDTO> result1 = favoriteDataService.addFavorite(request);
        Assert.assertTrue(result1.isSuccess());

        // 第二次添加相同收藏
        Result<FavoriteDTO> result2 = favoriteDataService.addFavorite(request);
        System.out.println("重复收藏测试结果：" + JSON.toJSONString(result2));
        Assert.assertNotNull(result2);
        // 根据业务逻辑，可能返回成功（幂等）或失败（重复收藏）
        if (!result2.isSuccess()) {
            Assert.assertTrue(result2.getCode().contains("ALREADY_FAVORITED") ||
                            result2.getCode().contains("DUPLICATE"));
        }
    }

    @Test
    public void testBatchOperations() {
        Long userId = 1000L + new Random().nextInt(1000);

        // 批量添加收藏
        for (int i = 1; i <= 5; i++) {
            AddFavoriteRequest request = new AddFavoriteRequest();
            request.setUserId(userId);
            request.setContentType(0);
            request.setContentId(600L + i);
            request.setRelatedKnowledgeTypeId(1L);

            Result<FavoriteDTO> result = favoriteDataService.addFavorite(request);
            Assert.assertTrue(result.isSuccess());
        }

        // 测试分页查询性能
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(3);

        GetUserFavoritesRequest request = new GetUserFavoritesRequest();
        request.setUserId(userId);
        request.setPageRequest(pageRequest);

        long startTime = System.currentTimeMillis();
        Result<PageResult<FavoriteDTO>> result = favoriteDataService.getUserFavorites(request);
        long endTime = System.currentTimeMillis();

        System.out.println("批量查询耗时：" + (endTime - startTime) + "ms");
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testRemoveNonExistentFavorite() {
        // 测试取消不存在的收藏
        RemoveFavoriteRequest request = new RemoveFavoriteRequest();
        request.setUserId(9999L);
        request.setContentType(0);
        request.setContentId(9999L);

        Result<Void> result = favoriteDataService.removeFavorite(request);
        System.out.println("取消不存在收藏结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        // 根据业务逻辑，可能返回成功（幂等）或失败（收藏不存在）
        if (!result.isSuccess()) {
            Assert.assertTrue(result.getCode().contains("NOT_FOUND") ||
                            result.getCode().contains("NOT_FAVORITED"));
        }
    }

    @Test
    public void testGetFavoritesByContent() {
        Integer contentType = 0;
        Long contentId = 700L + new Random().nextInt(100);

        // 让多个用户收藏同一个内容
        for (int i = 1; i <= 3; i++) {
            AddFavoriteRequest request = new AddFavoriteRequest();
            request.setUserId(2000L + i);
            request.setContentType(contentType);
            request.setContentId(contentId);
            request.setRelatedKnowledgeTypeId(1L);
            favoriteDataService.addFavorite(request);
        }

        // 查询收藏该内容的用户列表
        PageRequest pageRequest = new PageRequest();
        pageRequest.setPage(0);
        pageRequest.setSize(10);

        GetFavoritesByContentRequest request = new GetFavoritesByContentRequest();
        request.setContentType(contentType);
        request.setContentId(contentId);
        request.setPageRequest(pageRequest);

        Result<PageResult<FavoriteDTO>> result = favoriteDataService.getFavoritesByContent(request);
        System.out.println("根据内容获取收藏列表结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().getPagination().getTotalElements() >= 3);
    }

    @Test
    public void testDataIntegrity() {
        // 创建收藏并验证数据完整性
        AddFavoriteRequest request = new AddFavoriteRequest();
        request.setUserId(1000L + new Random().nextInt(1000));
        request.setContentType(0);
        request.setContentId(800L + new Random().nextInt(100));
        request.setRelatedKnowledgeTypeId(1L);

        Result<FavoriteDTO> result = favoriteDataService.addFavorite(request);
        Assert.assertTrue(result.isSuccess());

        FavoriteDTO favorite = result.getData();

        // 验证必要字段不为空
        Assert.assertNotNull(favorite.getId());
        Assert.assertNotNull(favorite.getUserId());
        Assert.assertNotNull(favorite.getContentType());
        Assert.assertNotNull(favorite.getContentId());
        Assert.assertNotNull(favorite.getCreatedAt());

        // 验证数据一致性
        Assert.assertEquals(request.getUserId(), favorite.getUserId());
        Assert.assertEquals(request.getContentType(), favorite.getContentType());
        Assert.assertEquals(request.getContentId(), favorite.getContentId());
        Assert.assertEquals(request.getRelatedKnowledgeTypeId(), favorite.getRelatedKnowledgeTypeId());
    }

    @Test
    public void testConcurrentOperations() {
        // 测试并发收藏操作
        Long userId = 1000L + new Random().nextInt(1000);
        Integer contentType = 0;
        Long contentId = 900L + new Random().nextInt(100);

        // 模拟并发添加收藏
        AddFavoriteRequest request1 = new AddFavoriteRequest();
        request1.setUserId(userId);
        request1.setContentType(contentType);
        request1.setContentId(contentId);
        request1.setRelatedKnowledgeTypeId(1L);

        AddFavoriteRequest request2 = new AddFavoriteRequest();
        request2.setUserId(userId);
        request2.setContentType(contentType);
        request2.setContentId(contentId);
        request2.setRelatedKnowledgeTypeId(1L);

        // 同时执行两个相同的收藏操作
        Result<FavoriteDTO> result1 = favoriteDataService.addFavorite(request1);
        Result<FavoriteDTO> result2 = favoriteDataService.addFavorite(request2);

        System.out.println("并发操作结果1：" + JSON.toJSONString(result1));
        System.out.println("并发操作结果2：" + JSON.toJSONString(result2));

        // 至少有一个操作成功
        Assert.assertTrue(result1.isSuccess() || result2.isSuccess());

        // 验证最终状态
        CheckFavoriteRequest checkRequest = new CheckFavoriteRequest();
        checkRequest.setUserId(userId);
        checkRequest.setContentType(contentType);
        checkRequest.setContentId(contentId);

        Result<Boolean> checkResult = favoriteDataService.isFavorited(checkRequest);
        Assert.assertTrue(checkResult.isSuccess());
        Assert.assertTrue(checkResult.getData());
    }
}
