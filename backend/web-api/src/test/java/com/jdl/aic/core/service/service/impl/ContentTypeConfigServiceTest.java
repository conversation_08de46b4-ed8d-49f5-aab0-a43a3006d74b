package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.config.ContentTypeConfigDTO;
import com.jdl.aic.core.service.client.dto.request.config.*;
import com.jdl.aic.core.service.client.service.ContentTypeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 内容类型配置服务测试类
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class ContentTypeConfigServiceTest {

    @Resource
    private ContentTypeConfigService contentTypeConfigService;

    @Test
    public void testCreateConfig() {
        log.info("开始测试创建内容类型配置");
        
        ContentTypeConfigDTO config = new ContentTypeConfigDTO();
        config.setCode("test_content");
        config.setName("测试内容类型");
        config.setDescription("这是一个测试内容类型");
        config.setTableName("test_content_table");
        config.setIsPortalModule(true);
        config.setIconUrl("https://example.com/icon.png");
        config.setSortOrder(1);
        config.setIsActive(true);

        Result<ContentTypeConfigDTO> result = contentTypeConfigService.createConfig(config);
        log.info("创建内容类型配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("test_content", result.getData().getCode());
        Assert.assertEquals("测试内容类型", result.getData().getName());
    }

    @Test
    public void testGetConfigById() {
        log.info("开始测试根据ID获取内容类型配置");
        
        // 先创建一个配置
        ContentTypeConfigDTO config = new ContentTypeConfigDTO();
        config.setCode("test_get_by_id");
        config.setName("测试获取配置");
        config.setTableName("test_get_table");
        
        Result<ContentTypeConfigDTO> createResult = contentTypeConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 根据ID获取配置
        Result<ContentTypeConfigDTO> result = contentTypeConfigService.getConfigById(configId);
        log.info("根据ID获取内容类型配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(configId, result.getData().getId());
        Assert.assertEquals("test_get_by_id", result.getData().getCode());
    }

    @Test
    public void testGetConfigByCode() {
        log.info("开始测试根据编码获取内容类型配置");
        
        // 先创建一个配置
        ContentTypeConfigDTO config = new ContentTypeConfigDTO();
        config.setCode("test_get_by_code");
        config.setName("测试根据编码获取");
        config.setTableName("test_code_table");
        
        Result<ContentTypeConfigDTO> createResult = contentTypeConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());

        // 根据编码获取配置
        Result<ContentTypeConfigDTO> result = contentTypeConfigService.getConfigByCode("test_get_by_code");
        log.info("根据编码获取内容类型配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("test_get_by_code", result.getData().getCode());
        Assert.assertEquals("测试根据编码获取", result.getData().getName());
    }

    @Test
    public void testUpdateConfig() {
        log.info("开始测试更新内容类型配置");
        
        // 先创建一个配置
        ContentTypeConfigDTO config = new ContentTypeConfigDTO();
        config.setCode("test_update");
        config.setName("测试更新前");
        config.setTableName("test_update_table");
        
        Result<ContentTypeConfigDTO> createResult = contentTypeConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 更新配置
        ContentTypeConfigDTO updateConfig = new ContentTypeConfigDTO();
        updateConfig.setCode("test_update");
        updateConfig.setName("测试更新后");
        updateConfig.setDescription("更新后的描述");
        updateConfig.setTableName("test_update_table");
        updateConfig.setIsPortalModule(false);
        updateConfig.setIsActive(true);

        Result<ContentTypeConfigDTO> result = contentTypeConfigService.updateConfig(configId, updateConfig);
        log.info("更新内容类型配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("测试更新后", result.getData().getName());
        Assert.assertEquals("更新后的描述", result.getData().getDescription());
        Assert.assertEquals(false, result.getData().getIsPortalModule());
    }

    @Test
    public void testToggleConfigStatus() {
        log.info("开始测试切换内容类型配置状态");
        
        // 先创建一个配置
        ContentTypeConfigDTO config = new ContentTypeConfigDTO();
        config.setCode("test_toggle");
        config.setName("测试状态切换");
        config.setTableName("test_toggle_table");
        config.setIsActive(true);
        
        Result<ContentTypeConfigDTO> createResult = contentTypeConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 切换状态为禁用
        ToggleContentTypeConfigStatusRequest request = new ToggleContentTypeConfigStatusRequest(configId, false);
        Result<Void> result = contentTypeConfigService.toggleConfigStatus(request);
        log.info("切换内容类型配置状态结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已切换
        Result<ContentTypeConfigDTO> getResult = contentTypeConfigService.getConfigById(configId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(false, getResult.getData().getIsActive());
    }

    @Test
    public void testGetConfigList() {
        log.info("开始测试获取内容类型配置列表");
        
        PageRequest pageRequest = PageRequest.of(1, 2);
        GetContentTypeConfigListRequest request = new GetContentTypeConfigListRequest();
        Result<PageResult<ContentTypeConfigDTO>> result = contentTypeConfigService.getConfigList(
                pageRequest, request);
        log.info("获取内容类型配置列表结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetAllActiveConfigs() {
        log.info("开始测试获取所有启用的内容类型配置");
        
        Result<List<ContentTypeConfigDTO>> result = contentTypeConfigService.getAllActiveConfigs();
        log.info("获取所有启用的内容类型配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testGetPortalModuleConfigs() {
        log.info("开始测试获取门户模块配置");
        
        Result<List<ContentTypeConfigDTO>> result = contentTypeConfigService.getPortalModuleConfigs(true);
        log.info("获取门户模块配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testSearchConfigs() {
        log.info("开始测试搜索内容类型配置");

        SearchContentTypeConfigsRequest request = new SearchContentTypeConfigsRequest("测试", true);
        Result<List<ContentTypeConfigDTO>> result = contentTypeConfigService.searchConfigs(request);
        log.info("搜索内容类型配置结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testCheckCodeExists() {
        log.info("开始测试检查编码是否存在");

        // 先创建一个配置
        ContentTypeConfigDTO config = new ContentTypeConfigDTO();
        config.setCode("test_check_code");
        config.setName("测试检查编码");
        config.setTableName("test_check_table");

        Result<ContentTypeConfigDTO> createResult = contentTypeConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());

        // 检查编码是否存在
        CheckContentTypeCodeExistsRequest request = new CheckContentTypeCodeExistsRequest("test_check_code", null);
        Result<Boolean> result = contentTypeConfigService.checkCodeExists(request);
        log.info("检查编码是否存在结果：{}", JSON.toJSONString(result));

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData());

        // 检查不存在的编码
        CheckContentTypeCodeExistsRequest notExistRequest = new CheckContentTypeCodeExistsRequest("not_exist_code", null);
        Result<Boolean> notExistResult = contentTypeConfigService.checkCodeExists(notExistRequest);
        Assert.assertTrue(notExistResult.isSuccess());
        Assert.assertFalse(notExistResult.getData());
    }

    @Test
    public void testBatchUpdateSortOrder() {
        log.info("开始测试批量更新排序");
        
        // 先创建两个配置
        ContentTypeConfigDTO config1 = new ContentTypeConfigDTO();
        config1.setCode("test_batch_1");
        config1.setName("测试批量1");
        config1.setTableName("test_batch_table_1");
        
        ContentTypeConfigDTO config2 = new ContentTypeConfigDTO();
        config2.setCode("test_batch_2");
        config2.setName("测试批量2");
        config2.setTableName("test_batch_table_2");
        
        Result<ContentTypeConfigDTO> createResult1 = contentTypeConfigService.createConfig(config1);
        Result<ContentTypeConfigDTO> createResult2 = contentTypeConfigService.createConfig(config2);
        
        Assert.assertTrue(createResult1.isSuccess());
        Assert.assertTrue(createResult2.isSuccess());

        // 批量更新排序
        List<Long> configIds = Arrays.asList(createResult1.getData().getId(), createResult2.getData().getId());
        List<Integer> sortOrders = Arrays.asList(10, 20);

        BatchUpdateContentTypeConfigSortOrderRequest request = new BatchUpdateContentTypeConfigSortOrderRequest(configIds, sortOrders);
        Result<Void> result = contentTypeConfigService.batchUpdateSortOrder(request);
        log.info("批量更新排序结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testDeleteConfig() {
        log.info("开始测试删除内容类型配置");
        
        // 先创建一个配置
        ContentTypeConfigDTO config = new ContentTypeConfigDTO();
        config.setCode("test_delete");
        config.setName("测试删除");
        config.setTableName("test_delete_table");
        
        Result<ContentTypeConfigDTO> createResult = contentTypeConfigService.createConfig(config);
        Assert.assertTrue(createResult.isSuccess());
        Long configId = createResult.getData().getId();

        // 删除配置
        Result<Void> result = contentTypeConfigService.deleteConfig(configId);
        log.info("删除内容类型配置结果：{}", JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证配置已删除
        Result<ContentTypeConfigDTO> getResult = contentTypeConfigService.getConfigById(configId);
        Assert.assertFalse(getResult.isSuccess());
    }
}
