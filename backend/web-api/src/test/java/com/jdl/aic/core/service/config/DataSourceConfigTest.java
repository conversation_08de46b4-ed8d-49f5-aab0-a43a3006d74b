package com.jdl.aic.core.service.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 多数据源配置测试
 *
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@SpringBootTest(classes = {DataSourceConfig.class, PrimaryDataSourceConfig.class, AdminDataSourceConfig.class, PortalDataSourceConfig.class})
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.primary.url=jdbc:h2:mem:primary_testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.datasource.primary.driver-class-name=org.h2.Driver",
    "spring.datasource.primary.username=sa",
    "spring.datasource.primary.password=",
    "spring.datasource.admin.url=jdbc:h2:mem:admin_testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.datasource.admin.driver-class-name=org.h2.Driver",
    "spring.datasource.admin.username=sa",
    "spring.datasource.admin.password=",
    "spring.datasource.portal.url=jdbc:h2:mem:portal_testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.datasource.portal.driver-class-name=org.h2.Driver",
    "spring.datasource.portal.username=sa",
    "spring.datasource.portal.password="
})
public class DataSourceConfigTest {

    @Autowired
    @Qualifier("primaryDataSource")
    private DataSource primaryDataSource;

    @Autowired
    @Qualifier("adminDataSource")
    private DataSource adminDataSource;

    @Autowired
    @Qualifier("portalDataSource")
    private DataSource portalDataSource;

    @Test
    public void testPrimaryDataSourceConnection() throws SQLException {
        assertNotNull(primaryDataSource, "主数据源不应为空");
        
        try (Connection connection = primaryDataSource.getConnection()) {
            assertNotNull(connection, "主数据源连接不应为空");
            assertFalse(connection.isClosed(), "主数据源连接应该是打开的");
            
            // 验证数据库名称
            String catalog = connection.getCatalog();
            assertTrue(catalog.contains("PRIMARY_TESTDB") || catalog.contains("testdb"),
                      "主数据源应连接到primary_testdb数据库或测试数据库");
            
            System.out.println("主数据源连接成功: " + catalog);
        }
    }

    @Test
    public void testAdminDataSourceConnection() throws SQLException {
        assertNotNull(adminDataSource, "管理端数据源不应为空");
        
        try (Connection connection = adminDataSource.getConnection()) {
            assertNotNull(connection, "管理端数据源连接不应为空");
            assertFalse(connection.isClosed(), "管理端数据源连接应该是打开的");
            
            // 验证数据库名称
            String catalog = connection.getCatalog();
            assertTrue(catalog.contains("ADMIN_TESTDB") || catalog.contains("testdb"),
                      "管理端数据源应连接到admin_testdb数据库或测试数据库");
            
            System.out.println("管理端数据源连接成功: " + catalog);
        }
    }

    @Test
    public void testPortalDataSourceConnection() throws SQLException {
        assertNotNull(portalDataSource, "门户端数据源不应为空");
        
        try (Connection connection = portalDataSource.getConnection()) {
            assertNotNull(connection, "门户端数据源连接不应为空");
            assertFalse(connection.isClosed(), "门户端数据源连接应该是打开的");
            
            // 验证数据库名称
            String catalog = connection.getCatalog();
            assertTrue(catalog.contains("PORTAL_TESTDB") || catalog.contains("testdb"),
                      "门户端数据源应连接到portal_testdb数据库或测试数据库");
            
            System.out.println("门户端数据源连接成功: " + catalog);
        }
    }

    @Test
    public void testAllDataSourcesAreDifferent() {
        // 验证三个数据源是不同的实例
        assertNotSame(primaryDataSource, adminDataSource, "主数据源和管理端数据源应该是不同的实例");
        assertNotSame(primaryDataSource, portalDataSource, "主数据源和门户端数据源应该是不同的实例");
        assertNotSame(adminDataSource, portalDataSource, "管理端数据源和门户端数据源应该是不同的实例");
        
        System.out.println("所有数据源都是独立的实例");
    }

    @Test
    public void testDataSourceTypes() {
        // 验证数据源类型
        assertTrue(primaryDataSource instanceof com.alibaba.druid.pool.DruidDataSource, 
                  "主数据源应该是DruidDataSource类型");
        assertTrue(adminDataSource instanceof com.alibaba.druid.pool.DruidDataSource, 
                  "管理端数据源应该是DruidDataSource类型");
        assertTrue(portalDataSource instanceof com.alibaba.druid.pool.DruidDataSource, 
                  "门户端数据源应该是DruidDataSource类型");
        
        System.out.println("所有数据源都是DruidDataSource类型");
    }
}
