package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.PageRequest;
import com.jdl.aic.core.service.client.common.PageResult;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.DictionaryDTO;
import com.jdl.aic.core.service.client.service.DictionaryService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * DictionaryService 集成测试
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class DictionaryServiceTest {

    @Resource
    private DictionaryService dictionaryService;

    @Test
    public void testCreateDictionary() {
        // 创建测试字典项
        DictionaryDTO newDictionary = new DictionaryDTO();
        newDictionary.setKey("test_key");
        newDictionary.setValue("测试值");
        newDictionary.setType("test_type");
        newDictionary.setDescription("这是一个测试字典项");
        newDictionary.setIsActive(true);
        newDictionary.setSortOrder(1);

        Result<DictionaryDTO> result = dictionaryService.createDictionary(newDictionary);
        System.out.println("创建字典项结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
        Assert.assertEquals("test_key", result.getData().getKey());
    }

    @Test
    public void testGetDictionaryById() {
        // 先创建一个字典项
        DictionaryDTO newDictionary = new DictionaryDTO();
        newDictionary.setKey("query_test_key");
        newDictionary.setValue("查询测试值");
        newDictionary.setType("query_test_type");
        newDictionary.setDescription("用于查询测试的字典项");
        newDictionary.setIsActive(true);

        Result<DictionaryDTO> createResult = dictionaryService.createDictionary(newDictionary);
        Assert.assertTrue(createResult.isSuccess());
        Long dictionaryId = createResult.getData().getId();

        // 查询字典项
        Result<DictionaryDTO> result = dictionaryService.getDictionaryById(dictionaryId);
        System.out.println("查询字典项结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("query_test_key", result.getData().getKey());
    }

    @Test
    public void testGetDictionaryByKeyAndType() {
        // 先创建一个字典项
        DictionaryDTO newDictionary = new DictionaryDTO();
        newDictionary.setKey("unique_key");
        newDictionary.setValue("唯一键值");
        newDictionary.setType("unique_type");
        newDictionary.setDescription("用于键类型查询测试");
        newDictionary.setIsActive(true);

        Result<DictionaryDTO> createResult = dictionaryService.createDictionary(newDictionary);
        Assert.assertTrue(createResult.isSuccess());

        // 根据键和类型查询
        Result<DictionaryDTO> result = dictionaryService.getDictionaryByKeyAndType("unique_key", "unique_type");
        System.out.println("根据键和类型查询结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("unique_key", result.getData().getKey());
        Assert.assertEquals("unique_type", result.getData().getType());
    }

    @Test
    public void testGetDictionariesByKey() {
        // 创建多个相同键的字典项
        String testKey = "common_key";
        
        DictionaryDTO dict1 = new DictionaryDTO();
        dict1.setKey(testKey);
        dict1.setValue("值1");
        dict1.setType("type1");
        dict1.setIsActive(true);

        DictionaryDTO dict2 = new DictionaryDTO();
        dict2.setKey(testKey);
        dict2.setValue("值2");
        dict2.setType("type2");
        dict2.setIsActive(true);

        dictionaryService.createDictionary(dict1);
        dictionaryService.createDictionary(dict2);

        // 根据键查询
        Result<List<DictionaryDTO>> result = dictionaryService.getDictionariesByKey(testKey, true);
        System.out.println("根据键查询结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 2);
    }

    @Test
    public void testGetDictionariesByType() {
        // 创建多个相同类型的字典项
        String testType = "common_type";
        
        DictionaryDTO dict1 = new DictionaryDTO();
        dict1.setKey("key1");
        dict1.setValue("值1");
        dict1.setType(testType);
        dict1.setIsActive(true);

        DictionaryDTO dict2 = new DictionaryDTO();
        dict2.setKey("key2");
        dict2.setValue("值2");
        dict2.setType(testType);
        dict2.setIsActive(true);

        dictionaryService.createDictionary(dict1);
        dictionaryService.createDictionary(dict2);

        // 根据类型查询
        Result<List<DictionaryDTO>> result = dictionaryService.getDictionariesByType(testType, true);
        System.out.println("根据类型查询结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 2);
    }

    @Test
    public void testUpdateDictionary() {
        // 先创建一个字典项
        DictionaryDTO newDictionary = new DictionaryDTO();
        newDictionary.setKey("update_test_key");
        newDictionary.setValue("原始值");
        newDictionary.setType("update_test_type");
        newDictionary.setDescription("用于更新测试的字典项");
        newDictionary.setIsActive(true);

        Result<DictionaryDTO> createResult = dictionaryService.createDictionary(newDictionary);
        Assert.assertTrue(createResult.isSuccess());
        Long dictionaryId = createResult.getData().getId();

        // 更新字典项
        DictionaryDTO updateDictionary = new DictionaryDTO();
        updateDictionary.setValue("更新后的值");
        updateDictionary.setDescription("更新后的描述");
        updateDictionary.setSortOrder(10);

        Result<DictionaryDTO> result = dictionaryService.updateDictionary(dictionaryId, updateDictionary);
        System.out.println("更新字典项结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("更新后的值", result.getData().getValue());
        Assert.assertEquals("更新后的描述", result.getData().getDescription());
    }

    @Test
    public void testUpdateDictionaryStatus() {
        // 先创建一个字典项
        DictionaryDTO newDictionary = new DictionaryDTO();
        newDictionary.setKey("status_test_key");
        newDictionary.setValue("状态测试值");
        newDictionary.setType("status_test_type");
        newDictionary.setIsActive(true);

        Result<DictionaryDTO> createResult = dictionaryService.createDictionary(newDictionary);
        Assert.assertTrue(createResult.isSuccess());
        Long dictionaryId = createResult.getData().getId();

        // 更新状态
        Result<Void> result = dictionaryService.updateDictionaryStatus(dictionaryId, false);
        System.out.println("更新字典项状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更改
        Result<DictionaryDTO> getResult = dictionaryService.getDictionaryById(dictionaryId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertFalse(getResult.getData().getIsActive());
    }

    @Test
    public void testUpdateDictionarySortOrder() {
        // 先创建一个字典项
        DictionaryDTO newDictionary = new DictionaryDTO();
        newDictionary.setKey("sort_test_key");
        newDictionary.setValue("排序测试值");
        newDictionary.setType("sort_test_type");
        newDictionary.setIsActive(true);
        newDictionary.setSortOrder(1);

        Result<DictionaryDTO> createResult = dictionaryService.createDictionary(newDictionary);
        Assert.assertTrue(createResult.isSuccess());
        Long dictionaryId = createResult.getData().getId();

        // 更新排序
        Result<Void> result = dictionaryService.updateDictionarySortOrder(dictionaryId, 100);
        System.out.println("更新字典项排序结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证排序已更改
        Result<DictionaryDTO> getResult = dictionaryService.getDictionaryById(dictionaryId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(100), getResult.getData().getSortOrder());
    }

    @Test
    public void testGetAllDictionaryTypes() {
        Result<List<String>> result = dictionaryService.getAllDictionaryTypes();
        System.out.println("获取所有字典类型结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testCountDictionariesByType() {
        // 先创建几个相同类型的字典项
        String testType = "count_test_type";
        
        for (int i = 1; i <= 3; i++) {
            DictionaryDTO dict = new DictionaryDTO();
            dict.setKey("count_key_" + i);
            dict.setValue("计数值" + i);
            dict.setType(testType);
            dict.setIsActive(true);
            dictionaryService.createDictionary(dict);
        }

        Result<Integer> result = dictionaryService.countDictionariesByType(testType, true);
        System.out.println("统计字典项数量结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertTrue(result.getData() >= 3);
    }

    @Test
    public void testBatchCreateDictionaries() {
        // 批量创建字典项
        DictionaryDTO dict1 = new DictionaryDTO();
        dict1.setKey("batch_key_1");
        dict1.setValue("批量值1");
        dict1.setType("batch_type");
        dict1.setIsActive(true);

        DictionaryDTO dict2 = new DictionaryDTO();
        dict2.setKey("batch_key_2");
        dict2.setValue("批量值2");
        dict2.setType("batch_type");
        dict2.setIsActive(true);

        List<DictionaryDTO> dictionaries = Arrays.asList(dict1, dict2);

        Result<List<DictionaryDTO>> result = dictionaryService.batchCreateDictionaries(dictionaries);
        System.out.println("批量创建字典项结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals(2, result.getData().size());
    }

    @Test
    public void testSearchDictionaries() {
        // 先创建一些测试数据
        DictionaryDTO dict1 = new DictionaryDTO();
        dict1.setKey("search_key_1");
        dict1.setValue("搜索测试值1");
        dict1.setType("search_type");
        dict1.setDescription("包含搜索关键词的描述");
        dict1.setIsActive(true);

        dictionaryService.createDictionary(dict1);

        // 搜索字典项
        Result<List<DictionaryDTO>> result = dictionaryService.searchDictionaries("搜索", "search_type", true);
        System.out.println("搜索字典项结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertTrue(result.getData().size() >= 1);
    }

    @Test
    public void testDeleteDictionary() {
        // 创建测试字典项
        DictionaryDTO newDictionary = new DictionaryDTO();
        newDictionary.setKey("delete_test_key");
        newDictionary.setValue("删除测试值");
        newDictionary.setType("delete_test_type");
        newDictionary.setDescription("用于删除测试的字典项");
        newDictionary.setIsActive(true);

        Result<DictionaryDTO> createResult = dictionaryService.createDictionary(newDictionary);
        Assert.assertTrue(createResult.isSuccess());
        Long dictionaryId = createResult.getData().getId();

        // 删除字典项
        Result<Void> result = dictionaryService.deleteDictionary(dictionaryId);
        System.out.println("删除字典项结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证字典项已删除
        Result<DictionaryDTO> getResult = dictionaryService.getDictionaryById(dictionaryId);
        Assert.assertFalse(getResult.isSuccess());
    }
}
