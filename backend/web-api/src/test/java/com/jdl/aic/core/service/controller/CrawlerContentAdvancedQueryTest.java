package com.jdl.aic.core.service.controller;

import com.alibaba.fastjson.JSON;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.system.CrawlerContentDTO;
import com.jdl.aic.core.service.client.service.CrawlerContentService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 爬虫内容高级查询测试类
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CrawlerContentAdvancedQueryTest {

    @Resource
    private CrawlerContentService crawlerContentService;

    @Test
    public void testSelectByNewFields() {
        System.out.println("=== 测试新增字段查询 ===");
        
        Result<List<CrawlerContentDTO>> result = crawlerContentService.selectByNewFields(
                "video", "task_001", 0, 5);
        System.out.println("新增字段查询结果：" + JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }

    @Test
    public void testSelectByMultipleConditions() {
        System.out.println("=== 测试多条件组合查询 ===");
        
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        List<String> contentTypes = Arrays.asList("article", "video");
        List<String> languages = Arrays.asList("zh-CN", "en-US");
        List<Integer> statuses = Arrays.asList(0, 1);
        List<String> types = Arrays.asList("video", "audio");
        List<String> taskIds = Arrays.asList("task_001", "task_002");
        
        Result<List<CrawlerContentDTO>> result = crawlerContentService.selectByMultipleConditions(
                ids, contentTypes, languages, statuses, types, taskIds, 10);
        System.out.println("多条件组合查询结果：" + JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
    }
}
