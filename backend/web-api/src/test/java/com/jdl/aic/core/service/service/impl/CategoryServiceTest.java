package com.jdl.aic.core.service.service.impl;

import com.jd.fastjson.JSON;
import com.jdl.aic.core.service.client.common.Result;
import com.jdl.aic.core.service.client.dto.category.CategoryDTO;
import com.jdl.aic.core.service.client.dto.request.category.GetCategoryTreeRequest;
import com.jdl.aic.core.service.client.dto.request.category.MoveCategoryToParentRequest;
import com.jdl.aic.core.service.client.dto.request.category.ToggleCategoryStatusRequest;
import com.jdl.aic.core.service.client.dto.request.category.UpdateCategorySortOrderRequest;
import com.jdl.aic.core.service.client.service.CategoryService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Random;

/**
 * CategoryService 集成测试
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@TestPropertySource(locations = "classpath:application-test.yml")
@Slf4j
public class CategoryServiceTest {

    @Resource
    private CategoryService categoryService;

    @Test
    public void testCreateCategory() {
        // 创建测试分类
        CategoryDTO newCategory = new CategoryDTO();
        newCategory.setName("测试分类" + new Random().nextInt(1000));
        newCategory.setDescription("这是一个测试分类");
        newCategory.setContentCategory("knowledge");
        newCategory.setIsActive(true);
        newCategory.setSortOrder(1);
        newCategory.setContentCategory("knowledge");
        newCategory.setSubTypeId(1L);
        newCategory.setIconUrl("/icons/opensource-news.svg");
        newCategory.setCreatedBy("aaaa");
        newCategory.setUpdatedBy("qqqq");

        Result<CategoryDTO> result = categoryService.createCategory(newCategory);
        System.out.println("创建分类结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertNotNull(result.getData().getId());
    }

    @Test
    public void testGetCategoryById() {
        // 先创建一个分类
        CategoryDTO newCategory = new CategoryDTO();
        newCategory.setName("查询测试分类");
        newCategory.setDescription("用于查询测试的分类");
        newCategory.setContentCategory("knowledge");
        newCategory.setIsActive(true);

        Result<CategoryDTO> createResult = categoryService.createCategory(newCategory);
        Assert.assertTrue(createResult.isSuccess());
        Long categoryId = createResult.getData().getId();

        // 查询分类
        Result<CategoryDTO> result = categoryService.getCategoryById(categoryId);
        System.out.println("查询分类结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
        Assert.assertEquals("查询测试分类", result.getData().getName());
    }

    @Test
    public void testGetCategoryTree() {
        GetCategoryTreeRequest request = new GetCategoryTreeRequest();
        request.setSubTypeId(7L);
        Result<List<CategoryDTO>> result = categoryService.getCategoryTree(request);
        System.out.println("分类树结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testUpdateCategory() {
        // 先创建一个分类
        CategoryDTO newCategory = new CategoryDTO();
        newCategory.setName("更新测试分类");
        newCategory.setDescription("用于更新测试的分类");
        newCategory.setContentCategory("knowledge");
        newCategory.setIsActive(true);

        Result<CategoryDTO> createResult = categoryService.createCategory(newCategory);
        Assert.assertTrue(createResult.isSuccess());
        Long categoryId = createResult.getData().getId();

        // 更新分类
        CategoryDTO updateCategory = new CategoryDTO();
        updateCategory.setName("更新后的分类名称");
        updateCategory.setDescription("更新后的描述");
        updateCategory.setSortOrder(10);

        Result<CategoryDTO> result = categoryService.updateCategory(categoryId, updateCategory);
        System.out.println("更新分类结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertEquals("更新后的分类名称", result.getData().getName());
    }

    @Test
    public void testToggleCategoryStatus() {
        // 先创建一个分类
        CategoryDTO newCategory = new CategoryDTO();
        newCategory.setName("状态测试分类");
        newCategory.setDescription("用于状态测试的分类");
        newCategory.setContentCategory("knowledge");
        newCategory.setIsActive(true);

        Result<CategoryDTO> createResult = categoryService.createCategory(newCategory);
        Assert.assertTrue(createResult.isSuccess());
        Long categoryId = createResult.getData().getId();

        // 禁用分类
        ToggleCategoryStatusRequest request = new ToggleCategoryStatusRequest();
        request.setId(categoryId);
        request.setIsActive(false);
        Result<Void> result = categoryService.toggleCategoryStatus(request);
        System.out.println("切换分类状态结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证状态已更改
        Result<CategoryDTO> getResult = categoryService.getCategoryById(categoryId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertFalse(getResult.getData().getIsActive());
    }

    @Test
    public void testUpdateCategorySortOrder() {
        // 先创建一个分类
        CategoryDTO newCategory = new CategoryDTO();
        newCategory.setName("排序测试分类");
        newCategory.setDescription("用于排序测试的分类");
        newCategory.setContentCategory("knowledge");
        newCategory.setIsActive(true);
        newCategory.setSortOrder(1);

        Result<CategoryDTO> createResult = categoryService.createCategory(newCategory);
        Assert.assertTrue(createResult.isSuccess());
        Long categoryId = createResult.getData().getId();

        UpdateCategorySortOrderRequest request = new UpdateCategorySortOrderRequest();
        request.setId(categoryId);
        request.setSortOrder(100);
        // 更新排序
        Result<Void> result = categoryService.updateCategorySortOrder(request);
        System.out.println("更新分类排序结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证排序已更改
        Result<CategoryDTO> getResult = categoryService.getCategoryById(categoryId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(Integer.valueOf(100), getResult.getData().getSortOrder());
    }

    @Test
    public void testCreateHierarchicalCategories() {
        // 创建父分类
        CategoryDTO parentCategory = new CategoryDTO();
        parentCategory.setName("父分类");
        parentCategory.setDescription("这是一个父分类");
        parentCategory.setContentCategory("knowledge");
        parentCategory.setIsActive(true);

        Result<CategoryDTO> parentResult = categoryService.createCategory(parentCategory);
        Assert.assertTrue(parentResult.isSuccess());
        Long parentId = parentResult.getData().getId();

        // 创建子分类
        CategoryDTO childCategory = new CategoryDTO();
        childCategory.setName("子分类");
        childCategory.setDescription("这是一个子分类");
        childCategory.setContentCategory("knowledge");
        childCategory.setParentId(parentId);
        childCategory.setIsActive(true);

        Result<CategoryDTO> childResult = categoryService.createCategory(childCategory);
        System.out.println("创建层级分类结果：" + JSON.toJSONString(childResult));
        Assert.assertTrue(childResult.isSuccess());
        Assert.assertEquals(parentId, childResult.getData().getParentId());
    }

    @Test
    public void testMoveCategoryToParent() {
        // 创建两个父分类
        CategoryDTO parent1 = new CategoryDTO();
        parent1.setName("父分类1");
        parent1.setContentCategory("knowledge");
        parent1.setIsActive(true);

        CategoryDTO parent2 = new CategoryDTO();
        parent2.setName("父分类2");
        parent2.setContentCategory("knowledge");
        parent2.setIsActive(true);

        Result<CategoryDTO> parent1Result = categoryService.createCategory(parent1);
        Result<CategoryDTO> parent2Result = categoryService.createCategory(parent2);
        Assert.assertTrue(parent1Result.isSuccess());
        Assert.assertTrue(parent2Result.isSuccess());

        Long parent1Id = parent1Result.getData().getId();
        Long parent2Id = parent2Result.getData().getId();

        // 创建子分类
        CategoryDTO child = new CategoryDTO();
        child.setName("移动测试子分类");
        child.setContentCategory("knowledge");
        child.setParentId(parent1Id);
        child.setIsActive(true);

        Result<CategoryDTO> childResult = categoryService.createCategory(child);
        Assert.assertTrue(childResult.isSuccess());
        Long childId = childResult.getData().getId();

        MoveCategoryToParentRequest request = new MoveCategoryToParentRequest();
        request.setId(childId);
        request.setNewParentId(parent2Id);
        // 移动子分类到另一个父分类下
        Result<Void> moveResult = categoryService.moveCategoryToParent(request);
        System.out.println("移动分类结果：" + JSON.toJSONString(moveResult));
        Assert.assertTrue(moveResult.isSuccess());

        // 验证移动成功
        Result<CategoryDTO> getResult = categoryService.getCategoryById(childId);
        Assert.assertTrue(getResult.isSuccess());
        Assert.assertEquals(parent2Id, getResult.getData().getParentId());
    }

    @Test
    public void testGetAvailableCategoriesForKnowledgeType() {
        Long knowledgeTypeId = 1L;
        Result<List<CategoryDTO>> result = categoryService.getAvailableCategoriesForKnowledgeType(knowledgeTypeId);
        System.out.println("知识类型可用分类结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());
        Assert.assertNotNull(result.getData());
    }

    @Test
    public void testDeleteCategory() {
        // 创建测试分类
        CategoryDTO newCategory = new CategoryDTO();
        newCategory.setName("删除测试分类");
        newCategory.setDescription("用于删除测试的分类");
        newCategory.setContentCategory("knowledge");
        newCategory.setIsActive(true);

        Result<CategoryDTO> createResult = categoryService.createCategory(newCategory);
        Assert.assertTrue(createResult.isSuccess());
        Long categoryId = createResult.getData().getId();

        // 删除分类
        Result<Void> result = categoryService.deleteCategory(categoryId);
        System.out.println("删除分类结果：" + JSON.toJSONString(result));
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isSuccess());

        // 验证分类已删除
        Result<CategoryDTO> getResult = categoryService.getCategoryById(categoryId);
        Assert.assertFalse(getResult.isSuccess());
    }
}
