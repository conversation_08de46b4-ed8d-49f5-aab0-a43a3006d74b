package com.jdl.aic.core.service.data;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jdl.aic.core.service.client.dto.knowledge.KnowledgeTypeDTO;
import com.jdl.aic.core.service.client.service.KnowledgeService;
import com.jdl.aic.core.service.client.common.Result;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 知识类型数据导入测试类
 * 解析enhanced-knowledge-types.json文件并将数据插入knowledge_type表
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class KnowledgeTypeDataImportTest {

    @Autowired
    private KnowledgeService knowledgeService;

    /**
     * 测试从enhanced-knowledge-types.json文件导入知识类型数据
     */
    @Test
    public void testImportKnowledgeTypesFromJson() {
        try {
            // 读取JSON文件 - 使用多种路径尝试策略
            String jsonContent = readJsonFileWithFallback();
            
            // 解析JSON数组
            JSONArray knowledgeTypesArray = JSON.parseArray(jsonContent);
            
            System.out.println("开始导入知识类型数据，共 " + knowledgeTypesArray.size() + " 条记录");
            
            int successCount = 0;
            int failCount = 0;
            
            // 遍历每个知识类型
            for (int i = 0; i < knowledgeTypesArray.size(); i++) {
                JSONObject jsonKnowledgeType = knowledgeTypesArray.getJSONObject(i);
                
                try {
                    // 转换为KnowledgeTypeDTO
                    KnowledgeTypeDTO knowledgeTypeDTO = convertJsonToKnowledgeTypeDTO(jsonKnowledgeType);
                    
                    // 检查是否已存在相同编码的知识类型
                    Result<KnowledgeTypeDTO> existingResult = knowledgeService.getKnowledgeTypeByCode(knowledgeTypeDTO.getCode());
                    if (existingResult.isSuccess() && existingResult.getData() != null) {
                        System.out.println("知识类型已存在，跳过: " + knowledgeTypeDTO.getCode() + " - " + knowledgeTypeDTO.getName());
                        continue;
                    }
                    
                    // 创建知识类型
                    Result<KnowledgeTypeDTO> result = knowledgeService.createKnowledgeType(knowledgeTypeDTO);
                    
                    if (result.isSuccess()) {
                        successCount++;
                        System.out.println("成功导入知识类型: " + knowledgeTypeDTO.getCode() + " - " + knowledgeTypeDTO.getName());
                    } else {
                        failCount++;
                        System.err.println("导入知识类型失败: " + knowledgeTypeDTO.getCode() + " - " + result.getMessage());
                    }
                    
                } catch (Exception e) {
                    failCount++;
                    System.err.println("处理知识类型数据时出错: " + e.getMessage());
                    e.printStackTrace();
                }
            }
            
            System.out.println("数据导入完成！成功: " + successCount + " 条，失败: " + failCount + " 条");
            
            // 验证导入结果
            Assert.assertTrue("至少应该成功导入一条数据", successCount > 0);
            
        } catch (Exception e) {
            System.err.println("导入过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            Assert.fail("导入过程中发生错误: " + e.getMessage());
        }
    }

    /**
     * 将JSON对象转换为KnowledgeTypeDTO
     */
    private KnowledgeTypeDTO convertJsonToKnowledgeTypeDTO(JSONObject jsonObject) {
        KnowledgeTypeDTO dto = new KnowledgeTypeDTO();

        dto.setId(jsonObject.getLong("id"));
        // 基本字段映射
        String code = jsonObject.getString("code");
        if (code == null || code.trim().isEmpty()) {
            // 如果没有code，根据name生成一个
            String name = jsonObject.getString("name");
            code = generateCodeFromName(name);
        }
        dto.setCode(code); // 确保编码是大写

        dto.setName(jsonObject.getString("name"));
        dto.setDescription(jsonObject.getString("description"));
        dto.setIconUrl(jsonObject.getString("iconUrl"));

        // 处理isActive字段，默认为true
        Boolean isActive = jsonObject.getBoolean("isActive");
        dto.setIsActive(isActive != null ? isActive : true);

        // 设置创建者信息
        dto.setCreatedBy("system_import");
        dto.setUpdatedBy("system_import");

        // 处理metadataSchema - 从JSON中提取metadataJsonSchema字段
        Object metadataSchemaObj = jsonObject.get("metadataJsonSchema");
        if (metadataSchemaObj != null && metadataSchemaObj instanceof JSONObject) {
            dto.setMetadataSchema(((JSONObject) metadataSchemaObj).getInnerMap());
        } else {
            // 创建默认的metadataSchema
            Map<String, Object> defaultMetadataSchema = createDefaultMetadataSchema();
            dto.setMetadataSchema(defaultMetadataSchema);
        }

        // 处理renderConfigJson
        Object renderConfigObj = jsonObject.get("renderConfigJson");
        if (renderConfigObj != null && renderConfigObj instanceof JSONObject) {
            dto.setRenderConfigJson(((JSONObject) renderConfigObj).getInnerMap());
        } else {
            // 创建默认的renderConfig
            Map<String, Object> defaultRenderConfig = createDefaultRenderConfig();
            dto.setRenderConfigJson(defaultRenderConfig);
        }

        // 处理communityConfigJson
        Object communityConfigObj = jsonObject.get("communityConfigJson");
        if (communityConfigObj != null && communityConfigObj instanceof JSONObject) {
            dto.setCommunityConfigJson(((JSONObject) communityConfigObj).getInnerMap());
        } else {
            // 创建默认的communityConfig
            Map<String, Object> defaultCommunityConfig = createDefaultCommunityConfig();
            dto.setCommunityConfigJson(defaultCommunityConfig);
        }

        return dto;
    }

    /**
     * 根据名称生成编码
     */
    private String generateCodeFromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "UNKNOWN_TYPE";
        }

        // 简单的编码生成逻辑：去除特殊字符，转换为大写，用下划线连接
        return name.replaceAll("[^\\u4e00-\\u9fa5a-zA-Z0-9]", "_")
                  .replaceAll("_+", "_")
                  .replaceAll("^_|_$", "")
                  .toUpperCase();
    }

    /**
     * 创建默认的元数据Schema
     */
    private Map<String, Object> createDefaultMetadataSchema() {
        Map<String, Object> schema = new HashMap<>();
        schema.put("type", "object");
        schema.put("properties", new HashMap<>());
        schema.put("additionalProperties", true);
        return schema;
    }

    /**
     * 创建默认的渲染配置
     */
    private Map<String, Object> createDefaultRenderConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("template", "default");
        config.put("layout", "card");
        config.put("display_mode", "standard");
        return config;
    }

    /**
     * 创建默认的社区配置
     */
    private Map<String, Object> createDefaultCommunityConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("can_comment", true);
        config.put("can_like", true);
        config.put("can_favorite", true);
        config.put("can_share", true);
        return config;
    }

    /**
     * 读取JSON文件内容 - 使用多种路径尝试策略
     */
    private String readJsonFileWithFallback() throws IOException {
        String[] possiblePaths = {
            // 相对于项目根目录
            "backend/docs/data/enhanced-knowledge-types.json",
            // 相对于当前工作目录
            "docs/data/enhanced-knowledge-types.json",
            // 绝对路径（基于用户工作目录）
            System.getProperty("user.dir") + "/backend/docs/data/enhanced-knowledge-types.json",
            // 尝试从父目录开始
            "../backend/docs/data/enhanced-knowledge-types.json",
            // 尝试从更上层目录
            "../../backend/docs/data/enhanced-knowledge-types.json",
            // 尝试从web-api模块向上查找
            "../../../backend/docs/data/enhanced-knowledge-types.json"
        };
        
        for (String path : possiblePaths) {
            try {
                Path filePath = Paths.get(path);
                if (Files.exists(filePath)) {
                    System.out.println("成功找到JSON文件: " + filePath.toAbsolutePath());
                    return new String(Files.readAllBytes(filePath));
                }
            } catch (Exception e) {
                // 继续尝试下一个路径
                System.out.println("尝试路径失败: " + path + " - " + e.getMessage());
            }
        }
        
        // 如果所有路径都失败，打印调试信息
        System.err.println("无法找到JSON文件，当前工作目录: " + System.getProperty("user.dir"));
        System.err.println("尝试过的路径:");
        for (String path : possiblePaths) {
            System.err.println("  - " + path);
        }
        
        throw new IOException("无法找到enhanced-knowledge-types.json文件，请检查文件路径");
    }

    /**
     * 读取JSON文件内容（保留原方法作为备用）
     */
    private String readJsonFile(String filePath) throws IOException {
        return new String(Files.readAllBytes(Paths.get(filePath)));
    }

    /**
     * 测试单个知识类型的创建（用于调试）
     */
    @Test
    public void testCreateSingleKnowledgeType() {
        // 创建一个测试用的知识类型
        KnowledgeTypeDTO testType = new KnowledgeTypeDTO();
        testType.setCode("TEST_IMPORT");
        testType.setName("测试导入类型");
        testType.setDescription("这是一个用于测试导入功能的知识类型");
        testType.setIconUrl("/icons/test.svg");
        testType.setIsActive(true);
        testType.setCreatedBy("test_user");
        testType.setUpdatedBy("test_user");
        
        // 设置必需的JSON配置
        Map<String, Object> metadataSchema = createDefaultMetadataSchema();
        testType.setMetadataSchema(metadataSchema);
        
        Map<String, Object> renderConfig = createDefaultRenderConfig();
        testType.setRenderConfigJson(renderConfig);
        
        Map<String, Object> communityConfig = createDefaultCommunityConfig();
        testType.setCommunityConfigJson(communityConfig);
        
        // 创建知识类型
        Result<KnowledgeTypeDTO> result = knowledgeService.createKnowledgeType(testType);
        
        System.out.println("测试创建结果: " + JSON.toJSONString(result));
        
        Assert.assertNotNull(result);
        Assert.assertTrue("创建应该成功", result.isSuccess());
        Assert.assertNotNull("返回的数据不应为空", result.getData());
        Assert.assertNotNull("ID应该被设置", result.getData().getId());
        
        System.out.println("成功创建测试知识类型，ID: " + result.getData().getId());
    }

    /**
     * 测试查询所有知识类型（验证导入结果）
     */
    @Test
    public void testQueryAllKnowledgeTypes() {
        // 这里可以添加查询所有知识类型的逻辑来验证导入结果
        // 由于当前接口可能不支持查询所有，这里先留空
        System.out.println("查询所有知识类型的测试方法");
    }
}
