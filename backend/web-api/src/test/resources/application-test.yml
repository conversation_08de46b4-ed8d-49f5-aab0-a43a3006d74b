# 测试环境配置
spring:
  application:
    name: aic-core-service-test
  
  # 允许Bean定义覆盖，解决事务管理器冲突
  main:
    allow-bean-definition-overriding: true

    # 多数据源配置
    datasource:
      # 主数据源 - ai_community_shared
      primary:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ****************************************************************************************************************************************************************************************************
        username: adminwl
        password: 2yfTkySnW6EcD

        # Druid连接池配置
        druid:
          # 初始连接数
          initial-size: 5
          # 最小连接池数量
          min-idle: 10
          # 最大连接池数量
          max-active: 20
          # 配置获取连接等待超时的时间
          max-wait: 60000
          # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          time-between-eviction-runs-millis: 60000
          # 配置一个连接在池中最小生存的时间，单位是毫秒
          min-evictable-idle-time-millis: 300000
          # 配置一个连接在池中最大生存的时间，单位是毫秒
          max-evictable-idle-time-millis: 900000
          # 配置检测连接是否有效
          validation-query: SELECT 1 FROM DUAL
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
          # 打开PSCache，并且指定每个连接上PSCache的大小
          pool-prepared-statements: true
          max-pool-prepared-statement-per-connection-size: 20
          # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
          filters: stat,wall,slf4j
          # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
          connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000

      # 管理端数据源 - ai_community_admin
      admin:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ***************************************************************************************************************************************************************************************************
        username: adminwl
        password: 2yfTkySnW6EcD

        # Druid连接池配置
        druid:
          # 初始连接数
          initial-size: 3
          # 最小连接池数量
          min-idle: 5
          # 最大连接池数量
          max-active: 15
          # 配置获取连接等待超时的时间
          max-wait: 60000
          # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          time-between-eviction-runs-millis: 60000
          # 配置一个连接在池中最小生存的时间，单位是毫秒
          min-evictable-idle-time-millis: 300000
          # 配置一个连接在池中最大生存的时间，单位是毫秒
          max-evictable-idle-time-millis: 900000
          # 配置检测连接是否有效
          validation-query: SELECT 1 FROM DUAL
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
          # 打开PSCache，并且指定每个连接上PSCache的大小
          pool-prepared-statements: true
          max-pool-prepared-statement-per-connection-size: 20
          # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
          filters: stat,wall,slf4j
          # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
          connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000

      # 门户端数据源 - ai_community_portal
      portal:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ****************************************************************************************************************************************************************************************************
        username: adminwl
        password: 2yfTkySnW6EcD

        # Druid连接池配置
        druid:
          # 初始连接数
          initial-size: 3
          # 最小连接池数量
          min-idle: 5
          # 最大连接池数量
          max-active: 15
          # 配置获取连接等待超时的时间
          max-wait: 60000
          # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
          time-between-eviction-runs-millis: 60000
          # 配置一个连接在池中最小生存的时间，单位是毫秒
          min-evictable-idle-time-millis: 300000
          # 配置一个连接在池中最大生存的时间，单位是毫秒
          max-evictable-idle-time-millis: 900000
          # 配置检测连接是否有效
          validation-query: SELECT 1 FROM DUAL
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
          # 打开PSCache，并且指定每个连接上PSCache的大小
          pool-prepared-statements: true
          max-pool-prepared-statement-per-connection-size: 20
          # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
          filters: stat,wall,slf4j
          # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
          connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
        # 配置DruidStatFilter
        web-stat-filter:
          enabled: true
          url-pattern: "/*"
          exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
        # 配置DruidStatViewServlet
        stat-view-servlet:
          enabled: true
          url-pattern: "/druid/*"
          # IP白名单(没有配置或者为空，则允许所有访问)
          allow: 127.0.0.1,*************
          # IP黑名单 (存在共同时，deny优先于allow)
          deny: ************
          # 禁用HTML页面上的"Reset All"功能
          reset-enable: false
          # 登录名
          login-username: admin
          # 登录密码
          login-password: 123456

  # MyBatis配置 - 多数据源配置
  mybatis:
    # 指定mapper文件位置 - 主数据源
    mapper-locations:
      - classpath:mapper/*.xml
      - classpath:mapper/primary/*.xml
      - classpath:mapper/admin/*.xml
      - classpath:mapper/portal/*.xml
    # 指定实体类包路径
    type-aliases-package: com.jdl.aic.core.service.dao.entity.primary,com.jdl.aic.core.service.dao.entity.admin,com.jdl.aic.core.service.dao.entity.portal
    configuration:
      # 开启驼峰命名转换
      map-underscore-to-camel-case: true
      # 开启二级缓存
      cache-enabled: true
      # 打印SQL语句
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

  # 日志配置
  logging:
    level:
      com.jdl.aic.core.service: debug
      org.springframework: info
      root: info
    pattern:
      console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
      file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file:
      name: logs/backend.log

  # JSF配置
  jsf:
    caller-monitor-filter:
      enable: true
