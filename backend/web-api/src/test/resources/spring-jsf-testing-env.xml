<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf
       http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!-- 单元测试，注册中心不使用 -->
    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="test.i.jsf.jd.local"/>


    <!-- JSF直联模式，用于unit test -->
    <jsf:consumer id="solutionServiceConsumer" interface="com.jdl.aic.core.service.client.service.SolutionService" retries="3"
                  alias="aic-test:1.0.0" url="jsf://127.0.0.1:22000?alias=aic-test:1.0.0">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:consumer>



</beans>
