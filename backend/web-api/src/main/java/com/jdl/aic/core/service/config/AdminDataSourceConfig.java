package com.jdl.aic.core.service.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 管理端数据源Mapper扫描配置
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Configuration
@MapperScan(
    basePackages = "com.jdl.aic.core.service.dao.mapper.admin",
    sqlSessionFactoryRef = "adminSqlSessionFactory"
)
public class AdminDataSourceConfig {

    /**
     * 管理端数据源SqlSessionTemplate
     */
    @Bean(name = "adminSqlSessionTemplate")
    public SqlSessionTemplate adminSqlSessionTemplate(@Qualifier("adminSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
