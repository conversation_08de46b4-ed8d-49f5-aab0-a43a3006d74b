package com.jdl.aic.core.service.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * 多数据源使用示例
 * 
 * 此类展示了如何在服务中使用不同的数据源
 * 
 * <AUTHOR> Community Development Team
 * @version 1.0.0
 */
@Component
public class DataSourceUsageExample {

    private final DataSource primaryDataSource;
    private final DataSource adminDataSource;
    private final DataSource portalDataSource;

    public DataSourceUsageExample(
            @Qualifier("primaryDataSource") DataSource primaryDataSource,
            @Qualifier("adminDataSource") DataSource adminDataSource,
            @Qualifier("portalDataSource") DataSource portalDataSource) {
        this.primaryDataSource = primaryDataSource;
        this.adminDataSource = adminDataSource;
        this.portalDataSource = portalDataSource;
    }

    /**
     * 使用主数据源的事务示例
     */
    @Transactional(transactionManager = "primaryTransactionManager")
    public void primaryDataSourceExample() throws SQLException {
        try (Connection connection = primaryDataSource.getConnection()) {
            // 执行主数据源相关的数据库操作
            // 例如：用户管理、内容管理等核心业务
            System.out.println("使用主数据源: " + connection.getCatalog());
        }
    }

    /**
     * 使用管理端数据源的事务示例
     */
    @Transactional(transactionManager = "adminTransactionManager")
    public void adminDataSourceExample() throws SQLException {
        try (Connection connection = adminDataSource.getConnection()) {
            // 执行管理端相关的数据库操作
            // 例如：系统配置、管理员操作日志、权限管理等
            System.out.println("使用管理端数据源: " + connection.getCatalog());
        }
    }

    /**
     * 使用门户端数据源的事务示例
     */
    @Transactional(transactionManager = "portalTransactionManager")
    public void portalDataSourceExample() throws SQLException {
        try (Connection connection = portalDataSource.getConnection()) {
            // 执行门户端相关的数据库操作
            // 例如：门户展示数据、用户行为统计、缓存数据等
            System.out.println("使用门户端数据源: " + connection.getCatalog());
        }
    }

    /**
     * 测试所有数据源连接
     */
    public void testAllDataSources() {
        try {
            primaryDataSourceExample();
            adminDataSourceExample();
            portalDataSourceExample();
            System.out.println("所有数据源连接测试成功！");
        } catch (SQLException e) {
            System.err.println("数据源连接测试失败: " + e.getMessage());
        }
    }
}

/*
使用说明：

1. 在Service类中注入特定的数据源：
   @Autowired
   @Qualifier("adminDataSource")
   private DataSource adminDataSource;

2. 在Service方法上使用对应的事务管理器：
   @Transactional(transactionManager = "adminTransactionManager")
   public void adminOperation() {
       // 管理端业务逻辑
   }

3. 在Mapper接口上无需特殊配置，MyBatis会根据包路径自动选择对应的数据源：
   - com.jdl.aic.core.service.dao.mapper.primary.* -> primaryDataSource
   - com.jdl.aic.core.service.dao.mapper.admin.*   -> adminDataSource
   - com.jdl.aic.core.service.dao.mapper.portal.*  -> portalDataSource

4. 数据库表建议：
   - ai_community_shared: 核心业务表（用户、内容、评论等）
   - ai_community_admin:  管理相关表（系统配置、操作日志、权限等）
   - ai_community_portal: 门户相关表（展示数据、统计数据、缓存等）
*/
