package com.jdl.aic.core.service.controller;

import com.jdl.aic.core.service.common.result.Result;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class HealthController {

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", System.currentTimeMillis());
        data.put("application", "Backend API");
        data.put("version", "1.0.0");
        return Result.success("系统运行正常", data);
    }

    /**
     * 欢迎接口
     */
    @GetMapping("/welcome")
    public Result<String> welcome() {
        return Result.success("欢迎使用Backend API系统！");
    }
}
