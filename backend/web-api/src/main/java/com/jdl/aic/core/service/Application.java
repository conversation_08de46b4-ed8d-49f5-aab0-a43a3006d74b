package com.jdl.aic.core.service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;

import java.util.Arrays;

/**
 * Spring Boot 启动类
 */
@SpringBootApplication
@ImportResource({"classpath:applicationContext.xml"})
@PropertySource("classpath:prop/important.properties")
@EnableAspectJAutoProxy
public class Application {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(Application.class, args);
        Environment env = context.getEnvironment();
        String[] activeProfiles = env.getActiveProfiles();
        String jdbcUrl = env.getProperty("jdbc.mysql.url");
        
        System.out.println("Backend application started successfully!");
        System.out.println("Active profiles: " + Arrays.toString(activeProfiles));

        // 打印其他重要的配置信息
        System.out.println("Application Name: " + env.getProperty("spring.application.name"));
        System.out.println("Server Port: " + env.getProperty("server.port"));
    }
}
