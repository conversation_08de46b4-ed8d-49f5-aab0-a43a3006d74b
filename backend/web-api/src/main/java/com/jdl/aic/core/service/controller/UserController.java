//package com.jdl.aic.core.service.controller;
//
//import com.jdl.aic.core.service.common.result.Result;
//import com.jdl.aic.core.service.dao.entity.portal.User;
//import com.jdl.aic.core.service.portal.client.UserService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * 用户控制器
// */
//@RestController
//@RequestMapping("/api/user")
//@CrossOrigin(origins = "*")
//public class UserController {
//
//    @Autowired
//    private UserService userService;
//
//    /**
//     * 获取所有用户
//     */
//    @GetMapping("/list")
//    public Result<List<User>> getAllUsers() {
//        try {
//            List<User> users = userService.getAllUsers();
//            return Result.success(users);
//        } catch (Exception e) {
//            return Result.failed("获取用户列表失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 根据ID获取用户
//     */
//    @GetMapping("/{id}")
//    public Result<User> getUserById(@PathVariable Long id) {
//        try {
//            User user = userService.getUserById(id);
//            if (user != null) {
//                return Result.success(user);
//            } else {
//                return Result.failed("用户不存在");
//            }
//        } catch (Exception e) {
//            return Result.failed("获取用户信息失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 创建用户
//     */
//    @PostMapping("/create")
//    public Result<String> createUser(@RequestBody User user) {
//        try {
//            boolean success = userService.createUser(user);
//            if (success) {
//                return Result.success("用户创建成功");
//            } else {
//                return Result.failed("用户创建失败，用户名可能已存在");
//            }
//        } catch (Exception e) {
//            return Result.failed("用户创建失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 更新用户
//     */
//    @PutMapping("/update")
//    public Result<String> updateUser(@RequestBody User user) {
//        try {
//            boolean success = userService.updateUser(user);
//            if (success) {
//                return Result.success("用户更新成功");
//            } else {
//                return Result.failed("用户更新失败");
//            }
//        } catch (Exception e) {
//            return Result.failed("用户更新失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 删除用户
//     */
//    @DeleteMapping("/{id}")
//    public Result<String> deleteUser(@PathVariable Long id) {
//        try {
//            boolean success = userService.deleteUser(id);
//            if (success) {
//                return Result.success("用户删除成功");
//            } else {
//                return Result.failed("用户删除失败");
//            }
//        } catch (Exception e) {
//            return Result.failed("用户删除失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 用户登录
//     */
//    @PostMapping("/login")
//    public Result<User> login(@RequestBody LoginRequest loginRequest) {
//        try {
//            User user = userService.login(loginRequest.getUsername(), loginRequest.getPassword());
//            if (user != null) {
//                // 清除密码信息
//                user.setPassword(null);
//                return Result.success("登录成功", user);
//            } else {
//                return Result.failed("用户名或密码错误");
//            }
//        } catch (Exception e) {
//            return Result.failed("登录失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取用户数量
//     */
//    @GetMapping("/count")
//    public Result<Integer> getUserCount() {
//        try {
//            int count = userService.getUserCount();
//            return Result.success(count);
//        } catch (Exception e) {
//            return Result.failed("获取用户数量失败：" + e.getMessage());
//        }
//    }
//
//    /**
//     * 登录请求对象
//     */
//    public static class LoginRequest {
//        private String username;
//        private String password;
//
//        public String getUsername() {
//            return username;
//        }
//
//        public void setUsername(String username) {
//            this.username = username;
//        }
//
//        public String getPassword() {
//            return password;
//        }
//
//        public void setPassword(String password) {
//            this.password = password;
//        }
//    }
//}
