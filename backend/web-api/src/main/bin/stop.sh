#!/bin/sh

PIDPROC=`ps -ef | grep 'Application' | grep -v 'grep'| awk '{print $2}'`

if [ -z "$PIDPROC" ];then
 echo "Application is not running"
 exit 0
fi

echo "PIDPROC: "$PIDPROC

#TOKEN="1ow2g121ser709Ab"

for PID in $PIDPROC
do

#if [ ! -z "$PID" ] ; then
#  status=`curl -XPOST http://g.jsf.jd.local/com.jd.jsf.deploy.app.InstOperateForDeployService/jsf-deploy/doInsOffline/${def_app_id} \
#  -d "[{\"appId\":${def_app_id},\"appInsId\":\"${def_instance_id}\",\"pid\":${PID},\"token\":\"${TOKEN}\"}]" -H 'token:jsf_deploy_call_real'`
#  echo "$status"
#  sleep 60s
#  if [ "$status" != "true" ] ; then
#    echo "appid:${def_app_id},instanceid=${def_instance_id}"
#    echo "doInsOffline $status :fail"
#    exit 1
#  else
#    echo "doInsOffline ok"
#  fi
#else
#    echo "Instance is not started"
#fi

if kill -9 $PID
   then echo "process Application(Pid:$PID) was force stopped at " `date`
fi
done
echo stop finished.