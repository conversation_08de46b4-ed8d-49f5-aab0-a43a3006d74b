#!/bin/bash

set +u

: "${PFINDER_PUBLISH_SERVICE:="http://pfinder-master.jd.com"}"
: "${PFINDER_STORAGE_SERVER:="http://storage.jd.local"}"
: "${PFINDER_STORAGE_BUCKET:="pfinder-agent"}"
: "${PFINDER_HOME:="/export/pfinder"}"
: "${PFINDER_SUGGEST:=""}"
: "${PFINDER_FORCE_UPDATE:="true"}"
: "${PFINDER_ACCESS_LOG:="true"}"
: "${PFINDER_APP_COORD:=""}"
: "${PFINDER_GROUP_NAME:=""}"
: "${PFINDER_SYSTEM_NAME:=""}"
: "${PFINDER_DATA_CENTER:=""}"
: "${PFINDER_CFG_CENTER:="http://pfinder-master.jd.com"}"; export PFINDER_CFG_CENTER
: "${PFINDER_SPECIAL_EVENT_REPORT_URL:="http://pfinder-proxy.jd.local:20561/report/special-event"}"; export PFINDER_SPECIAL_EVENT_REPORT_URL
: "${PFINDER_ON_ENVIRONMENT_UNSUPPORTED:="skip"}"; export PFINDER_ON_ENVIRONMENT_UNSUPPORTED
: "${PFINDER_LOGGING_LOGGER_SLF4J_DISABLED:="true"}"; export PFINDER_LOGGING_LOGGER_SLF4J_DISABLED
: "${PFINDER_LOGGING_LOGGER_BASIC_OUTPUT_MODE:="DEFAULT_FORMAT_ASYNC_ROLLING_FILE"}"; export PFINDER_LOGGING_LOGGER_BASIC_OUTPUT_MODE

_PFINDER_APP_COORD=$PFINDER_APP_COORD
_PFINDER_GROUP_NAME=$PFINDER_GROUP_NAME
_PFINDER_SYSTEM_NAME=$PFINDER_SYSTEM_NAME
_PFINDER_DATA_CENTER=$PFINDER_DATA_CENTER
_PFINDER_UNIT_ID=${DEPLOY_UNIT_ID:=""}

if [[ -z $_PFINDER_APP_COORD && -n $MLAAS_APPNAME ]]; then
    _PFINDER_APP_COORD="$MLAAS_APPNAME($MLAAS_PLATFORM)"
    _PFINDER_GROUP_NAME=$MLAAS_APP_GROUP
    _PFINDER_SYSTEM_NAME=$MLAAS_SYSTEM
    _PFINDER_DATA_CENTER=$MLAAS_DATA_CENTER
elif [[ -z $_PFINDER_APP_COORD && -n $deploy_app_name ]]; then
    _PFINDER_APP_COORD="$deploy_app_name(JDOS)"
    _PFINDER_GROUP_NAME=$APP_GROUP
    _PFINDER_SYSTEM_NAME=$_PFINDER_SYSTEM_NAME
    _PFINDER_DATA_CENTER=$JDOS_DATACENTER
elif [[ -z $_PFINDER_APP_COORD && -n $def_app_name ]]; then
    _PFINDER_APP_COORD="$def_app_name(JONE)"
    _PFINDER_GROUP_NAME=$def_jone_group_name
    _PFINDER_SYSTEM_NAME=$JONE_SYSTEM_NAME
elif [[ -z $_PFINDER_APP_COORD && -n $FX_APP_EN_NAME ]]; then
    _PFINDER_APP_COORD="$FX_APP_EN_NAME(FOCUS)"
    _PFINDER_GROUP_NAME=$FX_GROUP_NAME
elif [[ -z $_PFINDER_APP_COORD && -n $PFINDER_APPNAME && -n $PFINDER_PLATFORM ]]; then
    _PFINDER_APP_COORD="$PFINDER_APPNAME($PFINDER_PLATFORM)"
fi

PFINDER_APP_NAME=$(echo ${_PFINDER_APP_COORD} | awk '{ gsub(/\(.*\)/,""); print $0 }'); export PFINDER_APP_NAME
PFINDER_PLATFORM=$(echo ${_PFINDER_APP_COORD} | awk '{ gsub(/.*\(/,""); gsub(/\)/,""); print $0 }'); export PFINDER_PLATFORM
PFINDER_APP_HOME=${PFINDER_HOME}/$(echo ${_PFINDER_APP_COORD} | awk '{ gsub(/\(.*\)/,""); print $0 }'); export PFINDER_APP_HOME

PFINDER_LIB_PATH=${PFINDER_APP_HOME}/lib
PFINDER_LOG_FILE=${PFINDER_APP_HOME}/access.log
PFINDER_MODULE_PATH=${PFINDER_APP_HOME}/module
PFINDER_VERSION_FILE=${PFINDER_LIB_PATH}/version

mkdir -p ${PFINDER_APP_HOME} && chmod 777 ${PFINDER_APP_HOME} && touch $PFINDER_LOG_FILE && > $PFINDER_LOG_FILE

print_log() {
    [[ ${PFINDER_ACCESS_LOG} == 'true' ]] && echo $@ >> $PFINDER_LOG_FILE || :
}

print_error_log() {
    [[ ${PFINDER_ACCESS_LOG} == 'true' ]] && echo "[PFINDER_ACCESS_ERROR]" $@ >> $PFINDER_LOG_FILE || :
}

get_ip_address() {
    ip addr show | grep -v '127.0.0.1' | grep -oP '(?<=inet\s)\d+(\.\d+){3}' | head -n 1
}

report_special_event() {
    if [[ -z ${PFINDER_SPECIAL_EVENT_REPORT_URL} ]]; then
        return
    fi
    local special_event_key=$1
    local ip=$(get_ip_address)
    local timestamp=$(date +%s%3N)
    if [[ -n ${_PFINDER_APP_COORD} ]]; then
        local special_event_json="{\"appName\":\"${PFINDER_APP_NAME}\",\"platform\":\"${PFINDER_PLATFORM}\",\"ip\":\"${ip}\",\"timestamp\":${timestamp},\"key\":\"${special_event_key}\"}"
    else
        local special_event_json="{\"ip\":\"${ip}\",\"timestamp\":${timestamp},\"key\":\"${special_event_key}\"}"
    fi
    curl -XPOST -H "Content-Type: application/json" -d "${special_event_json}" ${PFINDER_SPECIAL_EVENT_REPORT_URL}
}

retry() {
    local retries=$1
    local interval=$2
    shift 2
    local cmd=$@

    local count=0
    until result=$($cmd); do
        exit_status=$?
        count=$(($count + 1))
        if [ $count -lt $retries ]; then
            sleep $interval
        else
            return $exit_status
        fi
    done

    echo $result
    return 0
}

fetch_agent_version() {
    local ip=$(hostname -I | head -n 1)
    echo `retry 3 1 curl -XGET -GsfL \
        --data-urlencode "ip=$ip" \
        --data-urlencode "unit_id=$_PFINDER_UNIT_ID" \
        --data-urlencode "app_coord=$_PFINDER_APP_COORD" \
        --data-urlencode "group_name=$_PFINDER_GROUP_NAME" \
        --data-urlencode "system_name=$_PFINDER_SYSTEM_NAME" \
        --data-urlencode "data_center=$_PFINDER_DATA_CENTER" \
        "${PFINDER_PUBLISH_SERVICE}/access/suggest/${PFINDER_SUGGEST}"`
}

fetch_modules() {
    echo `retry 3 1 curl -XGET -GsfL \
        --data-urlencode "app_coord=$_PFINDER_APP_COORD" \
        --data-urlencode "group_name=$_PFINDER_GROUP_NAME" \
        "${PFINDER_PUBLISH_SERVICE}/access/modules"`
}

download_and_extract_jar() {
    local download_url=$1
    local extract_to=$2
    local temp_folder="${PFINDER_APP_HOME}/temp"
    local temp_package_save_path="${temp_folder}/package.tar.gz"
    mkdir -p ${temp_folder}
    if [[ -f ${temp_package_save_path} ]]; then
        \rm ${temp_package_save_path}
    fi
    print_log "Download pfinder package from '$download_url'"
    if `curl -s -f -L "${download_url}" --output "${temp_package_save_path}"`; then
        tar --no-same-owner -zxf "${temp_package_save_path}" -C "$extract_to" \
        && \rm ${temp_package_save_path} \
        ; UNPACK_SUCCESS=$?
        if [[ ${UNPACK_SUCCESS} != 0 ]]; then
            print_error_log "unpack pfinder package fault."
            report_special_event "AGENT_JAR_DOWNLOAD_FAILED"
            return 1
        fi
    else
        print_error_log "download pfinder package fault."
        report_special_event "AGENT_JAR_DOWNLOAD_FAILED"
        return 1
    fi
    return 0
}

: ${PFINDER_VERSION:=`fetch_agent_version`}

CURR_PFINDER_VERSION=`[[ -f "$PFINDER_VERSION_FILE" ]] && cat "$PFINDER_VERSION_FILE"`

if [[ -z "${PFINDER_VERSION}" ]]; then
    if [[ -z "${CURR_PFINDER_VERSION}" ]]; then
        print_error_log "can't found any usable pfinder agent"
        report_special_event "AGENT_JAR_VERSION_FETCH_FAILED"
        return 1
    else
        print_error_log "can't get suggest pfinder agent version. will use cached agent version. (${CURR_PFINDER_VERSION})"
        PFINDER_VERSION=${CURR_PFINDER_VERSION}
    fi
fi

if [[ -d "${PFINDER_LIB_PATH}" ]]; then
    if [[ -z "${CURR_PFINDER_VERSION}" || "${CURR_PFINDER_VERSION}" != "${PFINDER_VERSION}" || ${PFINDER_FORCE_UPDATE} == 'true' ]]; then
        \rm -rf ${PFINDER_LIB_PATH}
        unset CURR_PFINDER_VERSION
    fi
fi
mkdir -p ${PFINDER_LIB_PATH}
mkdir -p ${PFINDER_MODULE_PATH}
if [[ ${PFINDER_FORCE_UPDATE} == 'true' ]]; then
    \rm -rf ${PFINDER_MODULE_PATH}/*
fi

PFINDER_AGENT_PACKAGE_URL_PREFIX="${PFINDER_STORAGE_SERVER}"
if [[ ! -z "$PFINDER_STORAGE_BUCKET" ]]; then
    PFINDER_AGENT_PACKAGE_URL_PREFIX="${PFINDER_AGENT_PACKAGE_URL_PREFIX}/${PFINDER_STORAGE_BUCKET}"
fi

if [[ "${CURR_PFINDER_VERSION:-}" != "${PFINDER_VERSION}" ]]; then
    retry 3 1 download_and_extract_jar "${PFINDER_AGENT_PACKAGE_URL_PREFIX}/pfinder-profiler-agent-${PFINDER_VERSION}.tar.gz" $PFINDER_LIB_PATH
    if [[ $? == 0 ]]; then
        echo ${PFINDER_VERSION} > ${PFINDER_VERSION_FILE};
    fi
fi

PFINDER_AGENT_PATH="$PFINDER_LIB_PATH/pfinder-profiler-agent-${PFINDER_VERSION}.jar"
if [[ -f ${PFINDER_AGENT_PATH} ]]; then
    print_log "pfinder-agent -> \"${PFINDER_AGENT_PATH}\""
    export PFINDER_AGENT="-javaagent:$PFINDER_AGENT_PATH"
    export JAVA_AGENT_OPTS="${JAVA_AGENT_OPTS:-} ${PFINDER_AGENT}"
else
    print_error_log "\"${PFINDER_AGENT_PATH}\" not exist"
    return 1
fi

: ${PFINDER_MODULES:=`fetch_modules`}
PFINDER_MODULE_JARS=()

if [[ -n "${PFINDER_MODULES}" ]]; then
    PFINDER_MODULE_ARRAY=(${PFINDER_MODULES//&/ })
    for PFINDER_MODULE in "${PFINDER_MODULE_ARRAY[@]}"
    do
        PFINDER_MODULE_NAME=`echo $PFINDER_MODULE | awk -F "=" '{print $1}'`
        PFINDER_MODULE_VERSION=`echo $PFINDER_MODULE | awk -F "=" '{print $2}'`
        PFINDER_MODULE_JAR="$PFINDER_MODULE_PATH/${PFINDER_MODULE_NAME}-${PFINDER_MODULE_VERSION}.jar"
        if [[ ! -f ${PFINDER_MODULE_JAR} ]]; then
            retry 3 1 download_and_extract_jar "${PFINDER_AGENT_PACKAGE_URL_PREFIX}/module/${PFINDER_MODULE_NAME}/${PFINDER_MODULE_NAME}-${PFINDER_MODULE_VERSION}.tar.gz" $PFINDER_MODULE_PATH
        fi
        if [[ -f ${PFINDER_MODULE_JAR} ]]; then
            print_log "pfinder-module-jar-> \"${PFINDER_MODULE_JAR}\""
            PFINDER_MODULE_JARS+=("$PFINDER_MODULE_JAR")
        else
            print_error_log "\"${PFINDER_MODULE_JAR}\" not exist"
        fi
    done
fi

export PFINDER_MODULE_CLASSPATH=$(IFS=\;; echo "${PFINDER_MODULE_JARS[*]}")