#!/bin/sh
curl -s "http://pfinder-master.jd.com/access/script" -o pfinder.sh ; source pfinder.sh || :

BASEDIR=`dirname $0`/..
BASEDIR=`(cd "$BASEDIR"; pwd)`
source /home/<USER>/default_vm.sh
# If a specific java binary isn't specified search for the standard 'java' binary
if [ -z "$JAVACMD" ] ; then
  if [ -n "$JAVA_HOME"  ] ; then
    if [ -x "$JAVA_HOME/jre/sh/java" ] ; then
      # IBM's JDK on AIX uses strange locations for the executables
      JAVACMD="$JAVA_HOME/jre/sh/java"
    else
      JAVACMD="$JAVA_HOME/bin/java"
    fi
  else
    JAVACMD=`which java`
  fi
fi


CLASSPATH="$BASEDIR"/conf:"$BASEDIR"/lib/*
LOGDIR="$BASEDIR/log/"

echo "$CLASSPATH"

if [ ! -x "$JAVACMD" ] ; then
  echo "Error: JAVA_HOME is not defined correctly."
  echo "  We cannot execute $JAVACMD"
  exit 1
fi


if [ -z "$OPTS_MEMORY" ] ; then
    OPTS_MEMORY="-Xss512k
    -XX:MetaspaceSize=256m
    -XX:MaxMetaspaceSize=512m
    -XX:+ParallelRefProcEnabled
    -XX:+UseParNewGC
    -XX:+UseConcMarkSweepGC
    -XX:+CMSScavengeBeforeRemark
    -XX:TargetSurvivorRatio=70
    -XX:SurvivorRatio=8
    -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./java_pid<pid>.hprof"
fi

LogPath="/export/Logs/aic/"
/bin/mkdir -p $LogPath

nohup "$JAVACMD" ${PFINDER_AGENT:-} $JEX_OPT $JAVA_OPTS \
  $OPTS_MEMORY \
  -classpath "$CLASSPATH" \
  -Dbasedir="$BASEDIR" \
  -Dfile.encoding="UTF-8" \
  -Dlog4j2.formatMsgNoLookups=true \
  -Dump.key.prefix="uat_" \
  com.jdl.aic.core.service.Application \
  "$@" >${LogPath}aic-main.log 2>&1 &
