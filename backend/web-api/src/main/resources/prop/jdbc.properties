##  jdbc configuration - 多数据源配置
#
## 主数据源 - ai_community_shared
#jdbc.mysql.primary.driver=com.mysql.cj.jdbc.Driver
#jdbc.mysql.primary.url=***************************************************************************************
##jdbc.mysql.primary.username=adminwl
##jdbc.mysql.primary.password=2yfTkySnW6EcD
#jdbc.mysql.primary.connectionProperties=allowMultiQueries=true;
#
## 管理端数据源 - ai_community_admin
#jdbc.mysql.admin.driver=com.mysql.cj.jdbc.Driver
#jdbc.mysql.admin.url=**************************************************************************************
##jdbc.mysql.admin.username=adminwl
##jdbc.mysql.admin.password=2yfTkySnW6EcD
#jdbc.mysql.admin.connectionProperties=allowMultiQueries=true;
#
## 门户端数据源 - ai_community_portal
#jdbc.mysql.portal.driver=com.mysql.cj.jdbc.Driver
#jdbc.mysql.portal.url=***************************************************************************************
##jdbc.mysql.portal.username=adminwl
##jdbc.mysql.portal.password=2yfTkySnW6EcD
#jdbc.mysql.portal.connectionProperties=allowMultiQueries=true;
#
##  dbcp configuration
#dbcp.initialSize=5
#dbcp.maxActive=30
#dbcp.maxIdle=5
#dbcp.minIdle=2
#dbcp.maxWait=1000
#dbcp.defaultAutoCommit=false
#dbcp.timeBetweenEvictionRunsMillis=600000
#dbcp.numTestsPerEvictionRun=3
#dbcp.minEvictableIdleTimeMillis=1800000
#dbcp.testWhileIdle=true
#dbcp.testOnBorrow=false
#dbcp.testOnReturn=false
#dbcp.validationQuery=select 1
#dbcp.removeAbandoned=true
#dbcp.removeAbandonedTimeout=180
#dbcp.logAbandoned=true
