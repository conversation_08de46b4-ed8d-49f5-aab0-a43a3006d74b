<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
    	http://www.springframework.org/schema/context
    	http://www.springframework.org/schema/context/spring-context.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd"
       default-lazy-init="false" default-autowire="byName">
<!--    <context:component-scan base-package="com.jd.eclp.master">-->
<!--        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>-->
<!--    </context:component-scan>-->
<!--    <aop:aspectj-autoproxy/>-->

    <!--<bean class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE"/>
        <property name="ignoreResourceNotFound" value="true"/>
    <property name="locations">
        <list>
            <value>classpath:prop/*.properties</value>
            <value>classpath:init-config/*.properties</value>
        </list>
    </property>
    </bean>

    &lt;!&ndash; jsf &ndash;&gt;
    <import resource="classpath:spring-jsf.xml"/>
    &lt;!&ndash; dao &ndash;&gt;
&lt;!&ndash;    <import resource="classpath:spring-dao.xml"/>&ndash;&gt;-->
</beans>
