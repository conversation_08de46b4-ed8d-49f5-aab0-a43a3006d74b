<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<beans xmlns="http://www.springframework.org/schema/beans"-->
<!--       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"-->
<!--       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd"-->
<!--       default-lazy-init="false" default-autowire="byName">-->

<!--    <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource">-->
<!--        <property name="driverClassName" value="${jdbc.mysql.driver}"/>-->
<!--        <property name="url" value="${jdbc.mysql.url}"/>-->
<!--        <property name="username" value="${jdbc.mysql.username}"/>-->
<!--        <property name="password" value="${jdbc.mysql.password}"/>-->
<!--        <property name="connectionProperties" value="${jdbc.mysql.connectionProperties}"/>-->

<!--        <property name="initialSize" value="5"/>-->
<!--        <property name="maxTotal" value="30"/>-->
<!--        <property name="maxIdle" value="5"/>-->
<!--        <property name="minIdle" value="2"/>-->
<!--        <property name="maxWaitMillis" value="1000"/>-->
<!--        <property name="defaultAutoCommit" value="true"/>-->
<!--        <property name="timeBetweenEvictionRunsMillis" value="600000"/>-->
<!--        <property name="numTestsPerEvictionRun" value="3"/>-->
<!--        <property name="minEvictableIdleTimeMillis" value="1800000"/>-->
<!--        <property name="testWhileIdle" value="true"/>-->
<!--        <property name="testOnBorrow" value="false"/>-->
<!--        <property name="testOnReturn" value="false"/>-->
<!--        <property name="validationQuery" value="select 1"/>-->
<!--        <property name="removeAbandonedOnBorrow" value="true"/>-->
<!--        <property name="removeAbandonedTimeout" value="180"/>-->
<!--        <property name="logAbandoned" value="true"/>-->
<!--    </bean>-->

<!--    <bean id="sqlSessionFactoryBeanName" class="org.mybatis.spring.SqlSessionFactoryBean">-->
<!--        <property name="dataSource" ref="dataSource"/>-->
<!--        <property name="configLocation" value="classpath:mybatis-config.xml"/>-->
<!--        <property name="mapperLocations">-->
<!--            <list>-->
<!--                <value>classpath:/mapper/*.xml</value>-->
<!--                <value>classpath*:com/jdl/aic/core/service/dao/mapper/*.xml</value>-->
<!--            </list>-->
<!--        </property>-->
<!--    </bean>-->

<!--    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">-->
<!--        <property name="basePackage" value="com.jdl.aic.core.service.dao.mapper"/>-->
<!--        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactoryBeanName"/>-->
<!--    </bean>-->

<!--    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">-->
<!--        <property name="dataSource" ref="dataSource"/>-->
<!--    </bean>-->

<!--    <tx:annotation-driven transaction-manager="transactionManager"/>-->

<!--</beans>-->
