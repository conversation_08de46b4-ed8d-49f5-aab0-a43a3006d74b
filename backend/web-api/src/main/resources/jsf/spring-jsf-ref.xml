<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <bean id="consumerUmpFilter" class="com.jd.eclp.core.jsf.filter.ConsumerUmpFilter" scope="prototype">
        <property name="appName" value="aic-core-service"/>
    </bean>
    <bean id="consumerChainFilter" class="com.jd.eclp.core.jsf.filter.ChainFilter" scope="prototype">
        <constructor-arg name="filters">
            <list>
                <!-- 顺序很重要，requestContextFilter一定要放在第一位 -->
                <ref bean="requestContextFilter"/>
                <ref bean="consumerUmpFilter"/>
            </list>
        </constructor-arg>
    </bean>
    <import resource="ref/jsf-ref.xml"/>
    <import resource="ref/jsf-ref-external.xml"/>
</beans>