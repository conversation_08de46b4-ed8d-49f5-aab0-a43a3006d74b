<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf
       http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <jsf:provider id="knowledgeServiceJsf" interface="com.jdl.aic.core.service.client.service.KnowledgeService"
                  ref="knowledgeService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="categoryServiceJsf" interface="com.jdl.aic.core.service.client.service.CategoryService"
                  ref="categoryService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="crawlerContentServiceJsf" interface="com.jdl.aic.core.service.client.service.CrawlerContentService"
                  ref="crawlerContentService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="dictionaryServiceJsf" interface="com.jdl.aic.core.service.client.service.DictionaryService"
                  ref="dictionaryService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="shareOptionConfigServiceJsf" interface="com.jdl.aic.core.service.client.service.ShareOptionConfigService"
                  ref="shareOptionConfigService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="contentTypeConfigServiceJsf" interface="com.jdl.aic.core.service.client.service.ContentTypeConfigService"
                  ref="contentTypeConfigService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="socialFeatureConfigServiceJsf" interface="com.jdl.aic.core.service.client.service.SocialFeatureConfigService"
                  ref="socialFeatureConfigService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="learningResourceServiceJsf" interface="com.jdl.aic.core.service.client.service.LearningResourceService"
                  ref="learningResourceService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="solutionServiceJsf" interface="com.jdl.aic.core.service.client.service.SolutionService"
                  ref="solutionService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="newsFeedServiceJsf" interface="com.jdl.aic.core.service.client.service.NewsFeedService"
                  ref="newsFeedService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="rssSourceServiceJsf" interface="com.jdl.aic.core.service.client.service.RssSourceService"
                  ref="rssSourceService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="learningCourseServiceJsf" interface="com.jdl.aic.core.service.client.service.LearningCourseService"
                  ref="learningCourseService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>


</beans>
