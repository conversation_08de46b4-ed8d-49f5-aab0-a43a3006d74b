<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://jsf.jd.com/schema/jsf
       http://jsf.jd.com/schema/jsf/jsf.xsd"
       default-lazy-init="false" default-autowire="byName">

    <jsf:provider id="teamServiceJsf" interface="com.jdl.aic.core.service.portal.client.TeamDataService"
                  ref="teamService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="userServiceJsf" interface="com.jdl.aic.core.service.portal.client.UserDataService"
                  ref="userService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="favoriteDataServiceJsf" interface="com.jdl.aic.core.service.portal.client.FavoriteDataService"
                  ref="favoriteDataService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="commentDataServiceJsf" interface="com.jdl.aic.core.service.portal.client.CommentDataService"
                  ref="commentDataService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="likeDataServiceJsf" interface="com.jdl.aic.core.service.portal.client.LikeDataService"
                  ref="likeDataService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="userCourseEnrollmentServiceJsf" interface="com.jdl.aic.core.service.portal.client.UserCourseEnrollmentService"
                  ref="userCourseEnrollmentService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="teamRecommendationServiceJsf" interface="com.jdl.aic.core.service.client.service.TeamRecommendationService"
                  ref="teamRecommendationService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="teamFavoriteServiceJsf" interface="com.jdl.aic.core.service.portal.client.TeamFavoriteService"
                  ref="teamFavoriteService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

    <jsf:provider id="userFollowServiceJsf" interface="com.jdl.aic.core.service.portal.client.UserFollowService"
                  ref="userFollowService" server="jsf" register="true" alias="${jsf.alias}" delay="-1"
                  serialization="hessian">
        <jsf:parameter key="token" value="cccc8f72149c48439c84f6bca5881696" hide="true"/>
    </jsf:provider>

</beans>
