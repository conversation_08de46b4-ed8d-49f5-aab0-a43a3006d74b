<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jsf="http://jsf.jd.com/schema/jsf"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
    http://jsf.jd.com/schema/jsf http://jsf.jd.com/schema/jsf/jsf.xsd">
    <jsf:registry id="jsfRegistry" protocol="jsfRegistry" index="i.jsf.jd.com"/>

    <jsf:server id="jsf" port="22000" protocol="jsf"/>

    <import resource="pub/jsf-pub-admin.xml"/>
    <import resource="pub/jsf-pub-portal.xml"/>
    <import resource="pub/jsf-pub-shared.xml"/>

</beans>