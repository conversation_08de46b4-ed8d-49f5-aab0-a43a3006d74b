<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<!DOCTYPE configuration-->
<!--        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"-->
<!--        "http://mybatis.org/dtd/mybatis-3-config.dtd">-->

<!--<configuration>-->

<!--    <settings>-->
<!--        &lt;!&ndash;<setting name="cacheEnabled" value="true" />&ndash;&gt;-->
<!--        <setting name="lazyLoadingEnabled" value="false"/>-->
<!--        <setting name="multipleResultSetsEnabled" value="true"/>-->
<!--        <setting name="useColumnLabel" value="true"/>-->
<!--        <setting name="useGeneratedKeys" value="true"/>-->
<!--        <setting name="autoMappingBehavior" value="PARTIAL"/>-->
<!--        <setting name="defaultExecutorType" value="SIMPLE"/>-->
<!--        <setting name="defaultStatementTimeout" value="20"/>-->
<!--        <setting name="cacheEnabled" value="false"/>-->
<!--        <setting name="defaultExecutorType" value="REUSE"/>-->
<!--        <setting name="logImpl" value="LOG4J"/>-->
<!--    </settings>-->

<!--    <plugins>-->
<!--        <plugin interceptor="com.jd.eclp.core.dao.interceptor.ExplainSelectInterceptor">-->
<!--            <property name="sampleRate" value="1"/>-->
<!--            <property name="rowsThreshold" value="50"/>-->
<!--        </plugin>-->
<!--        <plugin interceptor="com.jd.eclp.core.dao.interceptor.AutoFillInterceptor"/>-->
<!--        <plugin interceptor="com.jd.eclp.core.dao.interceptor.TpsInterceptor">-->
<!--            <property name="appName" value="flexdb"/>-->
<!--            <property name="dbName" value="flexdb"/>-->
<!--        </plugin>-->
<!--        <plugin interceptor="com.jd.eclp.core.dao.interceptor.SlowQueryInterceptor">-->
<!--            <property name="thresholdMs" value="50"/>-->
<!--        </plugin>-->
<!--        <plugin interceptor="com.jd.eclp.core.dao.interceptor.PaginationInterceptor"/>-->
<!--    </plugins>-->

<!--    <mappers>-->
<!--    </mappers>-->
<!--</configuration>-->
