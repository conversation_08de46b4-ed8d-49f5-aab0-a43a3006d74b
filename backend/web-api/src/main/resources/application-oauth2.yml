# OAuth2 配置文件
# 使用方法：将此文件重命名为 application-oauth2.yml 并配置真实的客户端ID和密钥
# 然后在启动时添加 --spring.profiles.active=oauth2

spring:
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: "281557269825-i3grb2fmi4uqhamier0j4dbv4ru32fm6.apps.googleusercontent.com"
            client-secret: "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"
            scope:
              - openid
              - profile
              - email
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
          github:
            client-id: "Ov23liLlToBzcmqzdQft"
            client-secret: "0bf3662929acf6a7f5bbb12c30298807d14184fd"
            scope:
              - user:email
              - read:user
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
        provider:
          google:
            authorization-uri: https://accounts.google.com/o/oauth2/v2/auth
            token-uri: https://www.googleapis.com/oauth2/v4/token
            user-info-uri: https://www.googleapis.com/oauth2/v3/userinfo
            user-name-attribute: sub
          github:
            authorization-uri: https://github.com/login/oauth/authorize
            token-uri: https://github.com/login/oauth/access_token
            user-info-uri: https://api.github.com/user
            user-name-attribute: id

# 配置步骤：
# 1. Google OAuth2:
#    - 访问 https://console.cloud.google.com/
#    - 创建项目并启用 Google+ API
#    - 创建 OAuth 2.0 客户端ID
#    - 设置重定向URI: http://localhost:8080/login/oauth2/code/google
#    - 将客户端ID和密钥填入上面的配置
#
# 2. GitHub OAuth2:
#    - 访问 https://github.com/settings/developers
#    - 创建新的 OAuth App
#    - 设置回调URL: http://localhost:8080/login/oauth2/code/github
#    - 将客户端ID和密钥填入上面的配置
#
# 3. 启动应用:
#    mvn spring-boot:run -Dspring-boot.run.profiles=oauth2
