<assembly xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
		http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/assembly-1.0.0.xsd
	">
	<id>package</id>
	<formats>
        <format>dir</format>
        <format>zip</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<directory>src/main/bin</directory>
			<outputDirectory>bin</outputDirectory>
		</fileSet>
		<fileSet>
			<directory>src/main/resources</directory>
			<outputDirectory>/conf</outputDirectory>
			<filtered>true</filtered>
			<excludes>
				<exclude>**/*.xls</exclude>
				<exclude>**/*.xlsx</exclude>
				<exclude>**/*.vm</exclude>
			</excludes>
		</fileSet>
		<fileSet>
			<directory>src/main/resources</directory>
			<outputDirectory>/conf</outputDirectory>
			<filtered>false</filtered>
			<includes>
				<include>**/*.xls</include>
				<include>**/*.xlsx</include>
				<include>**/*.vm</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>lib</directory>
			<outputDirectory>lib</outputDirectory>
		</fileSet>
	</fileSets>
	<dependencySets>
		<dependencySet>
			<outputDirectory>lib</outputDirectory>
			<scope>runtime</scope>
		</dependencySet>
	</dependencySets>
</assembly>