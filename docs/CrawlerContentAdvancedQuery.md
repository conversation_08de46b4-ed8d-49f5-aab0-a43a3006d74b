# CrawlerContent 高级条件查询接口文档

## 概述

为 `crawler_content` 表新增了强大的条件查询功能，支持根据主要字段进行灵活的查询操作。新增的查询方法支持：

- 基础字段过滤
- 新增字段查询
- 范围查询（数值、时间）
- 模糊搜索
- 多条件组合查询
- JSON 字段查询
- 排序和分页

## 新增的查询方法

### 1. selectByAdvancedCondition - 高级条件查询

**方法签名：**
```java
Result<List<CrawlerContentDTO>> selectByAdvancedCondition(Map<String, Object> params)
```

**支持的查询参数：**

#### 基础字段
- `id` (Long): 内容ID
- `ids` (List<Long>): 内容ID列表
- `title` (String): 标题（模糊查询）
- `link` (String): 链接（精确匹配）
- `linkLike` (String): 链接（模糊查询）
- `language` (String): 语言
- `languages` (List<String>): 语言列表
- `author` (String): 作者（模糊查询）
- `description` (String): 描述（模糊查询）
- `contentType` (String): 内容类型
- `contentTypes` (List<String>): 内容类型列表
- `status` (Integer): 状态
- `statuses` (List<Integer>): 状态列表
- `isFeatured` (Boolean): 是否精品
- `contentMd5` (String): 内容MD5

#### 新增字段
- `type` (String): 新内容类型
- `types` (List<String>): 新内容类型列表
- `taskId` (String): 任务ID
- `taskIds` (List<String>): 任务ID列表
- `taskName` (String): 任务名称（模糊查询）

#### 范围查询
- `qualityScoreMin` (BigDecimal): 质量评分最小值
- `qualityScoreMax` (BigDecimal): 质量评分最大值
- `wordCountMin` (Integer): 字数最小值
- `wordCountMax` (Integer): 字数最大值
- `pubDateStart` (LocalDateTime): 发布时间开始
- `pubDateEnd` (LocalDateTime): 发布时间结束
- `createdAtStart` (LocalDateTime): 创建时间开始
- `createdAtEnd` (LocalDateTime): 创建时间结束
- `updatedAtStart` (LocalDateTime): 更新时间开始
- `updatedAtEnd` (LocalDateTime): 更新时间结束

#### 搜索条件
- `keyword` (String): 关键词搜索（在标题、描述、内容中搜索）
- `tag` (String): 标签
- `tags` (List<String>): 标签列表

#### JSON 字段查询
- `hasAttachments` (Boolean): 是否包含附件
- `hasMedia` (Boolean): 是否包含媒体

#### 排序和分页
- `sortField` (String): 排序字段
- `sortDirection` (String): 排序方向（ASC/DESC）
- `limit` (Integer): 限制数量
- `offset` (Integer): 偏移量

#### 其他条件
- `includeDeleted` (Boolean): 是否包含已删除记录
- `createdBy` (String): 创建者
- `updatedBy` (String): 更新者

### 2. countByAdvancedCondition - 高级条件统计

**方法签名：**
```java
Result<Long> countByAdvancedCondition(Map<String, Object> params)
```

支持的参数与 `selectByAdvancedCondition` 相同，返回符合条件的记录总数。

### 3. selectByNewFields - 新增字段查询

**方法签名：**
```java
Result<List<CrawlerContentDTO>> selectByNewFields(String type, String taskId, Integer status, Integer limit)
```

**参数说明：**
- `type`: 内容类型（新字段）
- `taskId`: 任务ID
- `status`: 状态过滤
- `limit`: 限制数量

### 4. selectByMultipleConditions - 多条件组合查询

**方法签名：**
```java
Result<List<CrawlerContentDTO>> selectByMultipleConditions(
    List<Long> ids,
    List<String> contentTypes,
    List<String> languages,
    List<Integer> statuses,
    List<String> types,
    List<String> taskIds,
    Integer limit)
```

支持多个列表条件的 IN 查询。

## 使用示例

### 1. 基础查询示例

```java
Map<String, Object> params = new HashMap<>();
params.put("title", "AI");
params.put("language", "zh-CN");
params.put("status", 0);
params.put("limit", 10);

Result<List<CrawlerContentDTO>> result = crawlerContentService.selectByAdvancedCondition(params);
```

### 2. 范围查询示例

```java
Map<String, Object> params = new HashMap<>();
params.put("qualityScoreMin", new BigDecimal("7.0"));
params.put("qualityScoreMax", new BigDecimal("10.0"));
params.put("wordCountMin", 500);
params.put("wordCountMax", 2000);
params.put("createdAtStart", LocalDateTime.now().minusDays(7));
params.put("createdAtEnd", LocalDateTime.now());

Result<List<CrawlerContentDTO>> result = crawlerContentService.selectByAdvancedCondition(params);
```

### 3. 多条件查询示例

```java
Map<String, Object> params = new HashMap<>();
params.put("languages", Arrays.asList("zh-CN", "en-US"));
params.put("statuses", Arrays.asList(0, 1));
params.put("contentTypes", Arrays.asList("article", "video"));
params.put("types", Arrays.asList("video", "audio"));
params.put("keyword", "人工智能");
params.put("hasAttachments", true);

Result<List<CrawlerContentDTO>> result = crawlerContentService.selectByAdvancedCondition(params);
```

### 4. 分页查询示例

```java
Map<String, Object> params = new HashMap<>();
params.put("language", "zh-CN");
params.put("status", 0);
params.put("sortField", "created_at");
params.put("sortDirection", "DESC");
params.put("limit", 20);
params.put("offset", 0);

// 查询数据
Result<List<CrawlerContentDTO>> result = crawlerContentService.selectByAdvancedCondition(params);

// 查询总数
Result<Long> countResult = crawlerContentService.countByAdvancedCondition(params);
```

### 5. 新增字段查询示例

```java
// 查询特定类型和任务的内容
Result<List<CrawlerContentDTO>> result = crawlerContentService.selectByNewFields(
    "video", "task_001", 0, 10);
```

### 6. 多条件组合查询示例

```java
List<Long> ids = Arrays.asList(1L, 2L, 3L);
List<String> contentTypes = Arrays.asList("article", "video");
List<String> languages = Arrays.asList("zh-CN", "en-US");
List<Integer> statuses = Arrays.asList(0, 1);
List<String> types = Arrays.asList("video", "audio");
List<String> taskIds = Arrays.asList("task_001", "task_002");

Result<List<CrawlerContentDTO>> result = crawlerContentService.selectByMultipleConditions(
    ids, contentTypes, languages, statuses, types, taskIds, 10);
```

## 性能优化建议

1. **索引优化**: 确保常用查询字段有适当的索引
2. **分页查询**: 大数据量查询时使用 limit 和 offset 进行分页
3. **精确查询**: 尽量使用精确匹配而非模糊查询
4. **时间范围**: 时间范围查询时建议限制在合理范围内
5. **JSON 字段**: JSON 字段查询性能相对较低，谨慎使用

## 注意事项

1. 所有查询默认排除已删除的记录，除非设置 `includeDeleted = true`
2. 模糊查询字段会使用 `LIKE '%keyword%'` 进行匹配
3. 时间范围查询支持 LocalDateTime 类型
4. 排序字段需要是数据库表中的实际字段名
5. JSON 字段查询检查字段是否为 null、空数组或空字符串

## 测试

项目中提供了完整的测试用例：
- `CrawlerContentAdvancedQueryTest.java` - 包含各种查询场景的测试
- 可以参考测试用例了解具体的使用方法
